<?php

namespace Faveo;

/**
 * Created by PhpStorm.
 * User=> joseph
 * Date=> 14/10/19
 * Time=> 10=>12 ص
 */
use Models\User;
use snso;

class Faveo implements FaveoAdapter
{

    /**
     * get Auth User and return faveo user props
     * @return array
     */
    public static function getAuthUser()
    {
        $faveoUser = [
            "snso_id" => user('faveo_id') ,
            'name' => user('full_name'),
            'snso_email' => user('email'),
            "email" => uniqid(user('id').organization('id'))."@snso.support.net",
            "user_name" => user('email'),
            "last_name" => user('fm_name') ?: user('secd_name'),
            "first_name" => user('fr_name'),
            "org_name" => organization('name'),
            "org_domain" => snso::_config('Location'),
            "password" => 'faveo@123',
            'org_id' => organization('id'),
            "code" => organization('id') . user('id'),
        ];

        return $faveoUser;
    }
}