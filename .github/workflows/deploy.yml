name: Deploy to Test Server

on:
  push:
    branches:
      - dev

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Deploy to Test Server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: *************
          username: root
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          script: |
            echo "Start Deployment..."
            cd /var/www/html/dev-ehsan.snso.net/public_html
            git checkout -- .
            git pull origin dev
            cd framework
            cp config.ini.bak config.ini
            cp init.php.bak init.php
            cd /var/www/html/dev-ehsan.snso.net/
            composer install --no-dev -n --working-dir=/var/www/html/dev-ehsan.snso.net/public_html/framework/
            chown -R www-data:www-data public_html/
            chmod +x public_html/framework/SchemaAndDataRefactor
            ./public_html/framework/SchemaAndDataRefactor
            echo "Deployment Finished."
