# SNSO ERP Docker Management Makefile

.PHONY: help setup build up down restart logs clean status shell db-shell redis-shell backup restore

# Default target
help: ## Show this help message
	@echo "SNSO ERP Docker Management Commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "Examples:"
	@echo "  make setup    # Initial setup and start"
	@echo "  make logs     # View all container logs"
	@echo "  make shell    # Access application container"

setup: ## Initial setup and start all services
	@echo "🚀 Setting up SNSO ERP Docker environment..."
	@chmod +x docker-setup.sh
	@./docker-setup.sh

build: ## Build all Docker containers
	@echo "🔨 Building Docker containers..."
	@docker-compose build --no-cache

up: ## Start all services
	@echo "▶️  Starting all services..."
	@docker-compose up -d

down: ## Stop all services
	@echo "⏹️  Stopping all services..."
	@docker-compose down

restart: ## Restart all services
	@echo "🔄 Restarting all services..."
	@docker-compose restart

logs: ## View logs from all services
	@echo "📋 Viewing logs from all services..."
	@docker-compose logs -f

logs-app: ## View application logs only
	@echo "📋 Viewing application logs..."
	@docker-compose logs -f snso-app

logs-db: ## View database logs only
	@echo "📋 Viewing database logs..."
	@docker-compose logs -f snso-db

logs-redis: ## View Redis logs only
	@echo "📋 Viewing Redis logs..."
	@docker-compose logs -f snso-redis

status: ## Show status of all containers
	@echo "📊 Container status:"
	@docker-compose ps

shell: ## Access application container shell
	@echo "🐚 Accessing application container..."
	@docker-compose exec snso-app bash

db-shell: ## Access database shell
	@echo "🗄️  Accessing database shell..."
	@docker-compose exec snso-db mysql -u snso_user -p snso_erp

db-root: ## Access database as root
	@echo "🗄️  Accessing database as root..."
	@docker-compose exec snso-db mysql -u root -p

redis-shell: ## Access Redis shell
	@echo "🔴 Accessing Redis shell..."
	@docker-compose exec snso-redis redis-cli -a snso_redis_password

backup: ## Backup database
	@echo "💾 Creating database backup..."
	@mkdir -p backups
	@docker-compose exec snso-db mysqldump -u root -psnso_root_password snso_erp > backups/snso_backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Backup created in backups/ directory"

restore: ## Restore database from backup (usage: make restore FILE=backup.sql)
	@if [ -z "$(FILE)" ]; then \
		echo "❌ Please specify backup file: make restore FILE=backup.sql"; \
		exit 1; \
	fi
	@echo "🔄 Restoring database from $(FILE)..."
	@docker-compose exec -T snso-db mysql -u root -psnso_root_password snso_erp < $(FILE)
	@echo "✅ Database restored successfully"

clean: ## Clean up containers and volumes
	@echo "🧹 Cleaning up containers and volumes..."
	@docker-compose down -v
	@docker system prune -f
	@echo "✅ Cleanup completed"

clean-all: ## Clean up everything including images
	@echo "🧹 Cleaning up everything..."
	@docker-compose down -v --rmi all
	@docker system prune -a -f
	@echo "✅ Complete cleanup finished"

install-deps: ## Install PHP and Node.js dependencies
	@echo "📦 Installing dependencies..."
	@docker-compose exec snso-app composer install --working-dir=/var/www/html/framework
	@docker-compose exec snso-node npm install
	@echo "✅ Dependencies installed"

build-assets: ## Build frontend assets
	@echo "🎨 Building frontend assets..."
	@docker-compose exec snso-node npm run production
	@echo "✅ Assets built successfully"

watch-assets: ## Watch and rebuild assets on changes
	@echo "👀 Watching assets for changes..."
	@docker-compose exec snso-node npm run watch

permissions: ## Fix file permissions
	@echo "🔧 Fixing file permissions..."
	@sudo chown -R $(USER):$(USER) .
	@chmod -R 755 docker/
	@chmod -R 777 cache/ logs/ uploads/
	@echo "✅ Permissions fixed"

health: ## Check health of all services
	@echo "🏥 Checking service health..."
	@echo "Application: $$(curl -s -o /dev/null -w '%{http_code}' http://localhost:8080 || echo 'DOWN')"
	@echo "PhpMyAdmin: $$(curl -s -o /dev/null -w '%{http_code}' http://localhost:8081 || echo 'DOWN')"
	@echo "Database: $$(docker-compose exec snso-db mysqladmin ping -h localhost -u root -psnso_root_password 2>/dev/null && echo 'UP' || echo 'DOWN')"
	@echo "Redis: $$(docker-compose exec snso-redis redis-cli -a snso_redis_password ping 2>/dev/null || echo 'DOWN')"

update: ## Update and rebuild containers
	@echo "🔄 Updating containers..."
	@git pull
	@docker-compose build --no-cache
	@docker-compose up -d
	@echo "✅ Update completed"

# Development helpers
dev-setup: setup install-deps build-assets ## Complete development setup

prod-build: ## Build for production
	@echo "🏭 Building for production..."
	@docker-compose -f docker-compose.yml -f docker-compose.prod.yml build
	@echo "✅ Production build completed"
