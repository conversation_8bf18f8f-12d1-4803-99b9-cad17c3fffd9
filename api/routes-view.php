<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link rel="stylesheet" href="/templates/assets/css/dataTables.bootstrap.css">
    <title>Routes</title>
</head>
<body>
    <div class="container">
        <h3 class="text-center">Routes</h3>
        <table class="table table-bordered">
            <thead>
            <tr>
                <th class="text-center">URI</th>
                <th class="text-center">Name</th>
                <th class="text-center">Controller</th>
                <th class="text-center">Methods</th>
            </tr>
            </thead>
            <tbody>
            <?php $colors = [
                'HEAD' => 'secondary',
                'GET' => 'primary',
                'POST' => 'success',
                'PUT' => 'info',
                'PATCH' => 'info',
                'DELETE' => 'danger'
            ];?>
            <?php foreach ($routes as $route):?>
                <tr>
                    <td><?=$route->uri?></td>
                    <td class="text-center"><?=$route->action['as'] ?? '-'?></td>
                    <td><?=$route->action['controller'] ?? 'Closure'?></td>
                    <td>
                        <?php foreach ($route->methods as $method):?>
                            <span class="badge badge-<?=$colors[$method]?>"><?=$method?></span>
                        <?php endforeach;?>
                    </td>
                </tr>
            <?php endforeach;?>
            </tbody>
        </table>
    </div>
</body>
</html>