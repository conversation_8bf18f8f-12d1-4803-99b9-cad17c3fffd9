<?php

namespace Api\Middleware;
use Illuminate\Http\JsonResponse;

/**
 * Class Authenticate
 * Check if user if authenticated
 */

class AuthenticateToken
{
    public function handle($request, $next, $guard = null)
    {
        if ( ! true) {
            return new JsonResponse([
                'message' => 'Something wrong',
                'status' => '500'
            ]);
        }

        return $next($request);
    }
}