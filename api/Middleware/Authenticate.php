<?php

namespace Api\Middleware;
use Illuminate\Http\JsonResponse;

/**
 * Class Authenticate
 * Check if user if authenticated
 */

class Authenticate
{
    public function handle($request, $next, $guard = null)
    {
        if ( ! $_SESSION['user']) {
            return new JsonResponse([
                'message' => 'You are not authenticated!',
                'status' => '401'
            ]);
        }

        return $next($request);
    }
}