<?php

namespace Api\Responses;

use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\JsonResponse;

class ResponseWithCode implements Responsable
{
    /** @var int */
    private $statusCode;

    function __construct(int $statusCode)
    {

        $this->statusCode = $statusCode;
    }

    public function toResponse($request)
    {
        $messages = [
            200 => 'Success',
            400 => 'Bad request',
            401 => 'Not authenticated',
            403 => 'Access denied',
            404 => 'Resource not found',
            500 => 'Internal server error',
        ];

        return new JsonResponse(['msg' => $messages[$this->statusCode]], $this->statusCode);
    }
}