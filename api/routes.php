<?php

use Illuminate\Routing\Router;

/**
 * Check if api type is stateless, then you have to pass token
 *
 * @var $config array
 */
if ($config['app']['stateless']) {
    if (!$server->verifyResourceRequest(OAuth2\Request::createFromGlobals())) {
        $server->getResponse()->send();
        die;
    }
}

/**
 * @var $router Router
 */
$router->group(['namespace' => 'Api\Controllers', 'middleware' => []], function (Router $router) {
    $router->get('/beneficiary/{user_id}/activities', 'ActivityPaymentController@getActivities');
    $router->get('/beneficiaries', 'ClubSubscriptionController@getBeneficiaries');
    $router->get('/category/{category_id}/activities', 'ClubSubscriptionController@getActivitiesByCategoryId');
    $router->get('/activity/{id}/groups', 'ClubSubscriptionController@getGroupsByActivityId');
    $router->get('/beneficiary/{user_id}/data', 'ClubSubscriptionController@getUserData');

    $router->get('/activity/{activity_id}/tasks', 'ClubActivityController@getTasks');

    $router->post('/activity/{id}/approve', 'ClubSubscriptionController@approve');

    $router->get('/', 'HomeController@index');

    $router->post('/updateOrganizationOperationalStatus', 'HomeController@updateOrganizationOperationalStatus');

    $router->resource('/wisdoms', 'WisdomsController');

    $router->post('/register', 'UsersController@register');
    $router->get('/notificationNum', 'NotificationsController@getNotificationNum');

    $router->get('/beneficiary', 'UsersController@create');
    $router->post('/beneficiary', 'UsersController@store');
    $router->get('/beneficiary/check/{type}/{data}', 'UsersController@checkUserData');

    //get [countries | regions | cities]
    $router->get('regions/{countryId}', 'MenusController@regions');
    $router->get('cities/{regionId}', 'MenusController@cities');

    $router->get('number-to-words/{number}', 'ConversionController@convertToArabicWords');
});

// Finance program's routes
$router->group(['namespace' => 'Api\Controllers\Finance', 'prefix' => 'finance', 'middleware' => []], function (Router $router) {
    $router->get('entries/isRevOrExp/{account}', 'EntriesController@isRevenuesOrExpenses');
    $router->put('entries/{id}/lock', 'EntriesController@lock');
    $router->put('entries/{id}/post', 'EntriesController@post');
    $router->get('createVoucherClearingFilter', 'FinReportsController@voucherClearingFilter');
    $router->post('getVoucherWithFilter', 'FinReportsController@getVoucherWithFilter');
    $router->post('getVoucherWithFilterForClearance', 'FinReportsController@getVoucherWithFilterForClearance');
    $router->get('getReportData/{reportId}', 'FinReportsController@getReportData');
    $router->get('years/{year}/accounts', 'AccountsController@getRevenueAndExpensesAccounts');
    $router->get('years/{year}/accountsTree', 'AccountsController@getAccountsTree');
    $router->get('years/{year}/main-accounts', 'AccountsController@getMainAccountsTree');
    $router->get('years/{year}/permanentAccounts', 'AccountsController@getPermanentAccounts');
    $router->get('years/accounts/{year}', 'YearAccountsController@index');

    $router->resource('years', 'YearsController')->only(['show']);
});

// Assistances program's routes
$router->group(['namespace' => 'Api\Controllers\Assistances', 'prefix' => 'assist', 'middleware' => []], function (Router $router) {
    $router->resource('rules', 'RulesController')->only(['store', 'update']);
    $router->get('assistances/{assistance}/beneficiaries', 'BeneficiariesController@index');
    $router->get('assistances/{assistance}/beneficiaries/{beneficiary}', 'BeneficiariesController@show');
    $router->get('assistances/family/getRelationData', 'FamilyController@getRelationData');
    $router->get('assistances/family/getSocialStatus', 'FamilyController@getSocialStatus');
    $router->get('assistances/family/getWorkStatus', 'FamilyController@getWorkStatus');
    $router->get('assistances/family/getHealthStatus', 'FamilyController@getHealthStatus');
    $router->post('assistances/family/{id}/basicData', 'FamilyController@basicData');
    $router->get('assistances/family/{id}', 'FamilyController@getUserFamily');
    $router->post('assistances/family/{id}/relationData', 'FamilyController@relationData');
    $router->post('assistances/family/{id}/incomeData', 'FamilyController@incomeData');
    $router->post('assistances/family/{id}/upload/{index}', 'FamilyController@upload');
    $router->post('assistances/family/{id}/saveData', 'FamilyController@saveData');
    $router->any('assistances/user/{id}/{i}', 'FamilyController@getUserIncomeData');
    $router->any('getFileNumber/{id}', 'FamilyController@getFileNumber');
    $router->any('beneficiaries/search', 'BeneficiariesController@search');
    $router->any('beneficiaries/getReceived', 'BeneficiariesController@getBeneficiaryReceived');
    $router->any('assistances/getDetails', 'BeneficiariesController@getAssitancesDetails');

});

$router->group(['namespace' => 'Api\Controllers\HumanResource', 'prefix' => 'hr', 'middleware' => []], function (Router $router) {
    $router->get('/vacancies/{user_id}', 'ApiInHomePage@getUserVacancies');
    $router->get('/hint/', 'ApiInHomePage@getHint');
    $router->get('/news', 'ApiInHomePage@getNews');
    $router->get('/ads', 'ApiInHomePage@getAds');
    $router->get('/projects/{user_id}', 'ApiInHomePage@getUserProjects');
    $router->get('/leaves/{user_id}', 'ApiInHomePage@getLeaves');
    $router->get('/payslips/{user_id}', 'ApiInHomePage@getPayslip');
    $router->get('/attendances/{user_id}', 'ApiInHomePage@getAttendance');
    $router->get('/latencies/{user_id}', 'ApiInHomePage@getLatency');
    $router->get('/basic_jobs/{user_id}', 'ApiInHomePage@getBasicJob');
    $router->get('/leave_sheduleds/', 'ApiInHomePage@getLeaveScheduled');
    $router->get('/committees/{user_id}', 'ApiInHomePage@getCommittees');
    $router->get('/deduct_addition/{user_id}', 'ApiInHomePage@getDeductAddition');
    $router->get('/permissions/{user_id}', 'EmployeeRequestApi@permissions');
    $router->get('/leaves_request/{user_id}', 'EmployeeRequestApi@leaves');
    $router->get('/assignments/{user_id}', 'EmployeeRequestApi@assignments');
    $router->get('/retreats_leave/{user_id}', 'EmployeeRequestApi@retreats_leave');
    $router->get('/leave_credits/{user_id}', 'EmployeeRequestApi@leave_credits');
    $router->get('/advance_request/{user_id}', 'EmployeeRequestApi@advance_request');
    $router->get('/outworks/{user_id}', 'EmployeeRequestApi@outwork');
    $router->get('/outwork_fees/{user_id}', 'EmployeeRequestApi@outwork_fees');
    $router->get('/mandate/{user_id}', 'EmployeeRequestApi@mandate');
    $router->get('/mandate_fees/{user_id}', 'EmployeeRequestApi@mandate_fees');
    $router->get('/exchange/{user_id}', 'EmployeeRequestApi@exchange');

});

$router->group(['namespace' => 'Api\Controllers\Sanabel' , 'prefix' => 'sanabel' , 'middleware' => []] , function (Router $router) {
    $router->get('getDonorByCategory/{id}' , 'Donors@getDonorByCategory');
    $router->get('categories' , 'Categories@index');
    $router->get('getGatesByCategory/{id}' , 'Gates@getGates');
    $router->any('donors/search' , 'Voucher@searchDonars');
    $router->any('donors/getPayments' , 'Voucher@getAllDonarInstallment');
    $router->any('donors/getPurposePayments' , 'Voucher@getDonarPurposeInstallment');
    $router->any('donors/getCasePayments' , 'Voucher@getDonarCaseInstallment');
    $router->any('ledger/getGatesAndCases' , 'Voucher@getAllGatesAndCasesInsideLedger');
    $router->any('gate/type' , 'Voucher@gateType');
    $router->any('donation/limitedNotRepeated' , 'Voucher@limitedNotRepeated');
    $router->any('donation/notLimitedRepeated' , 'Voucher@notLimitedRepeated');
    $router->any('donation/limitedRepeated' , 'Voucher@limitedRepeated');
});

//Assignment Employee Requests
$router->get('EmployeeRequests/Assignment/{id}/getWrdias', 'Api\Controllers\EmployeeRequests@getWrdias');

$router->group(['namespace' => 'Api\Controllers\Faveo'], function (Router $router) {
    $router->get('/faveo/getAuthUser', 'FaveoController@getAuthUser');
    $router->post('/faveo/reply-ticket' , 'FaveoController@replay');
    $router->get('/faveo/read' , 'FaveoController@readAll');
    $router->get('/ticketsNum', 'FaveoController@getTicketsNum');

});

$router->get('gantt/{id}' , 'Api\Controllers\Planning\GanttChartController@tasks');

$router->get('type/{id}' , 'Api\Controllers\WarehouseController@index');
$router->get('getUnits/{id}' , 'Api\Controllers\WarehouseController@getUnits');
$router->get('getFilteredUnits/{id}/{unit_id}' , 'Api\Controllers\WarehouseController@getFilteredUnits');
$router->get('getQty','Api\Controllers\WarehouseController@getQty');
$router->get('getQtyUnit','Api\Controllers\WarehouseController@convertToLessUnit');
$router->get('getProducts/{id}' , 'Api\Controllers\WarehouseController@getProducts');

$router->get('getUnits_numbers','Api\Controllers\WarehouseController@getUnitsNumberDivion');
$router->get('getProductType/{id}', 'Api\Controllers\WarehouseController@getProductType');
$router->get('getTargetUnitNumber','Api\Controllers\WarehouseController@getTargetUnitNumber');
$router->get('regWithinSystemConfig', function () {

    $orgConfig = [];

    try {
        $fields = Setting::getList(277);
    } catch (SettingException $e) {
        $fields = null;
    }

    try {
        $orgConfig = ClientConfiguration::read([ClientConfiguration::OPR_ID => 277])[0];
    } catch (ClientConfigurationException $e) {
    }

    $orgConfig = json_decode($orgConfig->ids, true);

    $configs = [];

    foreach ($fields as $key => $row) {

        $configs[$key]["label"] = $row->translatedName;
        $configs[$key]["code"] = $row->code;
        $configs[$key]["visible"] = (bool)$orgConfig[$row->id][0];
        $configs[$key]["require"] = (bool)$orgConfig[$row->id][1];

    }

    return $configs;

});

// $routes variable comes from api.php file (See lines 60'sth)
$router->get('routes', function () use ($routes) {
    require 'routes-view.php';
});

$router->get('/translate/{id}', function ($id) {
    return ($_SESSION['translation'][$id] ? $_SESSION['translation'][$id] : $_SESSION['translation']['gnr_not_exist']);
});


// Faveo Helpdesk Routes

