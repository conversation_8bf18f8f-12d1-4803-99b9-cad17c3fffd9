<?php

namespace Api\Controllers\Finance;

use Api\Controllers\Controller;
use Api\Responses\ResponseWithCode;
use fin_entery;
use FinAccount;
use FinEntry;
use Illuminate\Http\JsonResponse;
use Models\Finance\Entry;

class EntriesController extends Controller
{

    public function isRevenuesOrExpenses(int $account)
    {
        $isRevOrExp = in_array(FinAccount::readID($account)->type, [
            FinAccount::SETTING_EXPENSES,
            FinAccount::SETTING_REVENUE,
        ]);

        return [
            'success' => $isRevOrExp,
        ];
    }

    /**
     * @param $id
     *
     * @return ResponseWithCode|JsonResponse
     */
    public function lock($id)
    {
        /** @var Entry $entry */
        $entry = Entry::findOrFail($id);
        if ($entry->isSaved() && $entry->isValid()) {

            $entry->lockEntry();
            FinEntry::registerEntry(fin_entery::readByID($entry->id));

            $entry = Entry::findOrFail($id);
            return new JsonResponse([
                'msg' => translate('p_entry_locked_msg', session('program')),
                'entry' => $entry
            ], 200);
        }

        return new ResponseWithCode(400);
    }

    /**
     * @param $id
     *
     * @return ResponseWithCode|JsonResponse
     */
    public function post($id)
    {
        /** @var Entry $entry */
        $entry = Entry::findOrFail($id);
        if ($entry->isLocked() && $entry->isValid()) {
            $entry->postEntry();

            return new JsonResponse([
                'msg' => translate('p_entry_posted_msg', session('program')),
                'entry' => $entry
            ], 200);
        }

        return new ResponseWithCode(400);
    }
}
