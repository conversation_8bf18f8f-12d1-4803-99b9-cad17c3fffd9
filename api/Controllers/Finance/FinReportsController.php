<?php

namespace Api\Controllers\Finance;

use Api\Controllers\Controller;
use ConfigurationParserException;
use fin_cleared_vouchers;
use ConfigurationParser;
use FinClearedVoucher;
use FinPCLedger;
use FinPCLedgerException;
use FinVoucher;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Infrastructure\Finance\Criteria\VoucherFilter;
use Models\Finance\Ledger;
use Models\Finance\Voucher;
use Setting;

class FinReportsController extends Controller
{
    /**
     * @var VoucherFilter
     */
    private $voucherFilter;

    function __construct(VoucherFilter $voucherFilter)
    {
        $this->voucherFilter = $voucherFilter;
    }

    public function voucherClearingFilter()
    {

        try {
            $translations = (new ConfigurationParser())->read(APP_DIR . DS . 'bsc/P054' . DS . TRANSLATIONS_DIR_NAME . DS . $_SESSION['lang'] . '.conf');
        } catch (ConfigurationParserException $e) {
            $translations = [];
        }

        $types = Setting::getList(70);

        return [
            'types' => $types,
            'translation' => $translations->getVariables(),
        ];
    }



    public function getVoucherWithFilter()
    {
        try {
            $PCLedger = FinPCLedger::readID((int)session('s_ledger_id'));
        } catch (FinPCLedgerException $e) {
            $PCLedger = null;
        }

        if ($PCLedger) {
            $vouchers = $this->filterVouchers($PCLedger);
            return $vouchers;
        }
        return [];
    }

    /**
     * @param $PCLedger
     * @return mixed
     */
    private function filterVouchers($PCLedger)
    {
        return Voucher::with(["relatedPurpose" , "relatedBox"])
            ->filterBy($this->voucherFilter)
            ->when(!$this->isAccountable($PCLedger), function (Builder $query) {
                $query->where(FinVoucher::ISSUED_BY, $_SESSION['user']->id);
            })->where(FinVoucher::PCLEDGER_ID, $PCLedger->id)
            ->where(function (Builder $query) {
                $query->whereNull(FinVoucher::REVIEW_REPORT_ID)
                    ->orWhere(FinVoucher::REVIEW_REPORT_ID, 0);
            })->where(FinVoucher::STATUS, FinClearedVoucher::REPORT_SAVED)
            ->orderByDesc(FinVoucher::ISSUE_DATE)
            ->get();
    }

    /**
     * Checks if the authenticated user is accountable of this PCLedger
     *
     * @param $PCLedger
     *
     * @return bool
     */
    private function isAccountable($PCLedger): bool
    {
        return $_SESSION['user']->id == $PCLedger->accountable;
    }

    public function getVoucherWithFilterForClearance(Request $request)
    {
        $PCLedger = Ledger::find((int)$_SESSION['s_ledger_id']);

        if ($PCLedger) {
            $hasNoClearReport = function (Builder $query) {
                $query->whereNull(FinVoucher::CLEAR_REPORT_ID)
                    ->orWhere(FinVoucher::CLEAR_REPORT_ID, 0);
            };

            return Voucher::with(["relatedPurpose" , "relatedBox"])
                ->filterBy($this->voucherFilter)
                ->where(FinVoucher::PCLEDGER_ID, $PCLedger->id)
                ->where(FinVoucher::STATUS, FinClearedVoucher::REPORT_ACCEPTED)
                ->where($hasNoClearReport)
                ->orderByDesc(FinVoucher::ISSUE_DATE)
                ->get();
        }

        return [];
    }

    public function getReportData($reportId)
    {

        try {
            return (array)fin_cleared_vouchers::readByID((int)$reportId);
        } catch (\ModelException $e) {
            return [];
        }

    }

}