<?php

namespace Api\Controllers\Finance;

use Api\Controllers\Controller;
use Illuminate\Http\Response;
use fin_year;
use DB;
use FinYear;
use Models\Finance\Year;
use snso;

class YearsController extends Controller
{

    /**
     * Get the fiscal year
     *
     * @param int $id Year Id
     *
     * @return mixed
     */
    public function show(int $id)
    {
        $fiscalYear = Year::find($id);
        return new Response([
            'fin_year_start_date' => snsoDate($fiscalYear->start_date, organization('def_datetype')),
            'fin_year_end_date' => snsoDate($fiscalYear->end_date, organization('def_datetype')),
        ]);
    }
}