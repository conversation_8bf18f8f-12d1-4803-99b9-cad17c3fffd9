<?php

namespace Api\Controllers\Finance;

use Api\Controllers\Controller;
use DB;
use fin_acc;
use FinAccount;
use FinYearException;
use Illuminate\Database\Eloquent\Builder;
use FinYear;
use Models\Finance\Account;

class AccountsController extends Controller
{

    public function getRevenueAndExpensesAccounts(int $year)
    {
        $accounts = Account::year($year)->sub()
            ->temporary()->get();

        return $accounts;
    }

    public function getAccountsTree(int $year)
    {
        $mainAccounts = Account::year($year)->top()->get();
        $subAccounts = Account::year($year)->lowLevel()
            ->orderBy(FinAccount::CODE)
            ->get()->groupBy(FinAccount::TOPACC_ID)->all();

        $accounts = [];
        foreach ($mainAccounts as $key => $mainAccount) {
            $mainAccount->type = 'main';
            $accounts[] = $mainAccount;
            $this->buildSubTree($mainAccount->fin_acc_id, $subAccounts, $accounts);
        }

        return $accounts;
    }

    public function getMainAccountsTree(int $year)
    {
        $mainAccounts = Account::year($year)->top()->get();
        $subAccounts = Account::year($year)->lowLevel()->sub()
            ->orderBy(FinAccount::CODE)
            ->get()
            ->groupBy(FinAccount::TOPACC_ID);

        $accounts = [];
        foreach ($mainAccounts as $key => $mainAccount) {
            $mainAccount->type = 'main';
            $accounts[] = $mainAccount;
            $this->buildSubTree($mainAccount->fin_acc_id, $subAccounts, $accounts);
        }

        return $accounts;
    }

    /**
     * Get permanent accounts in fiscal year
     *
     * @param int $year Fiscal year id
     * @return mixed
     */
    public function getPermanentAccounts(int $year)
    {
        try {
            $fiscalYear = FinYear::readID($year);
            return $fiscalYear->permanentAccounts();
        } catch (FinYearException $e) {
            return [];
        }
    }

    private function buildSubTree(int $accountId, $subAccounts, &$accounts)
    {
        foreach ($subAccounts[$accountId] as $key => $account) {
            $accounts[] = $account;
            if (! empty($subAccounts[$account->fin_acc_id])) {
                $this->buildSubTree($account->fin_acc_id, $subAccounts, $accounts);
            }
        }
    }
}