<?php

namespace Api\Controllers\Finance;

use Api\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Models\Finance\Year;

class YearAccountsController extends Controller
{
    private $request;

    function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * @param Year $year
     *
     * @return \Illuminate\Http\Response
     */
    public function index($year)
    {
        $year = Year::find($year);
        $accounts = $year->accounts();

        if ($this->request->has('type')) {
            $accounts->type($this->request->type);
        }

        $accounts = $accounts->get();

        return Response::create($accounts, 200);
    }

}
