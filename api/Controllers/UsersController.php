<?php

namespace Api\Controllers;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Setting;
use Subscription;
use Unifonic\API\Exception;
use User;
use UserClass;
use Validation;

class UsersController extends Controller
{
    public function __construct()
    {
//        $this->middleware('auth');
    }

    public function register(Request $request)
    {

        $result = Subscription::userSubscription(Subscription::REGISTER_USER_FROM_OUTSIDE, $request->register_by, $request->all());

        if ($result['status']) {
            $response = [
                'message' => 'success',
                'lang' => $_SESSION['lang'],
                'register_by' => $request->register_by  == 1 ? 'email' : 'phone',
                'user_id' => $result['userId'],
            ];
            $_SESSION['uid'] = $result['userId'];
        } else {
            $response = [
                'message' => array_shift($_SESSION['alertMessages']),
            ];
        }

        return new Response($response);

    }

    public function create($response)
    {

        try {
            $classifications = UserClass::read([UserClass::ORG_ID => $_SESSION['organization']->id]);
        } catch (\UserClassException $e) {
            $classifications = [];
        }

        return new Response([
            'users' => User::getBeneficiariesForFinance(),
            'sex_list' => Setting::getList(27),
            'classifications' => $classifications
        ], 200);

    }

    public function store(Request $request)
    {
        $data = User::createBeneficiaryByIdentityNumber($request->all());
        return new Response($data, $data['status'] ? 200 : 406);
    }

    public function checkUserData(string $type, string $data)
    {

        switch ($type) {

            case 'email':

                if (Subscription::checkEmail($data)) {
                    return [
                        'status' => true,
                        'message' => \Translation::translate(null, 'EmailIsCorrectAndNotUsedBefore')
                    ];
                } else {
                    return [
                        'status' => false,
                        'message' => \Translation::translate(null, 'EmailIsInCorrectOrUsedBefore')
                    ];
                }

                break;

            case 'identity':

                if (Subscription::checkIdentityNumber($data)) {
                    return [
                        'status' => true,
                        'message' => \Translation::translate(null, 'IdentityNumberIsCorrectAndNotUsedBefore')
                    ];
                } else {
                    return [
                        'status' => false,
                        'message' => \Translation::translate(null, 'IdentityNumberIsInCorrectOrUsedBefore')
                    ];
                }

                break;

            case 'tell':

                if (Subscription::checkTell($data)) {
                    return [
                        'status' => true,
                        'message' => \Translation::translate(null, 'TellIsCorrectAndNotUsedBefore')
                    ];
                } else {
                    return [
                        'status' => false,
                        'message' => \Translation::translate(null, 'TellIsInCorrectOrUsedBefore')
                    ];
                }

                break;

            case 'sponsorship':

                if (Subscription::checkSponsorshipNumber($data)) {
                    return [
                        'status' => true,
                        'message' => \Translation::translate(null, 'SponsorshipNumberIsCorrectAndNotUsedBefore')
                    ];
                } else {
                    return [
                        'status' => false,
                        'message' => \Translation::translate(null, 'SponsorshipNumberIsInCorrectOrUsedBefore')
                    ];
                }

                break;

            default:

                return false;

                break;
        }
    }
}