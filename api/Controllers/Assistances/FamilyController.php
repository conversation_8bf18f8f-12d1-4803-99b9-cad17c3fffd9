<?php
/**
 * Created by PhpStorm.
 * User: ab<PERSON>la
 * Date: 2/27/22
 * Time: 4:49 PM
 */

namespace Api\Controllers\Assistances;


use Api\Controllers\Controller;
use AssistancesSettings;
use Carbon\Carbon;
use Document;
use DocumentException;
use Domain\Assistances\FamilyCreator;
use Domain\Assistances\FamilyFormatter;
use Illuminate\Filesystem\Cache;
use Illuminate\Http\Request;
use Models\Assistances\Family;
use Models\Assistances\Income;
use Models\Assistances\Member;
use Models\User;
use Setting;
use sh_user;

class FamilyController extends Controller
{
    function basicData($id ,Request $request)
    {
//        SnsoRedis::put('family_' . $id ,$request->all() , now()->addYear());
        $data = $request->all();
        $step = 1;
        return cache('family_' . $id , [
            "step" => $step,
            "data" => $data

        ]);
//        return SnsoRedis::get('family_' . $id);
//        return cache('family_' . $id);
    }
    function relationData($id ,Request $request)
    {
        $data = $request->all();
        $step = 2;
        return cache('family_' . $id , [
            "step" => $step,
            "data" => $data

        ]);
    }
    function incomeData($id ,Request $request)
    {
        $data = $request->all();
        $step = 3;
        return cache('family_' . $id , [
            "step" => $step,
            "data" => $data

        ]);
    }
    function attachment()
    {

    }

    function upload($id , $index , Request $request){
        $family = collect(cache('family_' . $id ))["data"];

//        return $request->file('test')->getClientOriginalName();

        if ($request->hasfile('test')) {
            $file = [
                'file' => $request->file('test')->getRealPath(),
                'tmp_name' => $request->file('test')->getRealPath(),
                'size' => $request->file('test')->getSize(),
                'name' => $request->file('test')->getClientOriginalName(),
                'type' => $request->file('test')->getType(),
                'options' => [
                    'mime' => $request->file('test')->getClientMimeType(),
                    'as'    => $request->file('test')->getClientOriginalName()
                ],
            ];
            $newFile = $this->saveImage($id , $file);
            $member = $family[$index];
            $member["files"][] = $newFile;
            $family[$index] = $member;
        }
//        return $family;
//        $data;
        cache('family_' . $id , [
            "step" => 4,
            "data" => $family

        ]);

        return $family;

    }
    function getUserFamily($id)
    {
        $test =  cache('family_' . $id)["data"];
        if($test){
            return $test;
        } else {
            $user = User::find($id)->load('documents');
            return [
                [
                    'name'       => $user->fr_name.' '.$user->secd_name.' '.$user->thrd_name.' '.$user->fm_name,
                    'first_name'       => $user->fr_name,
                    'second_name'       => $user->secd_name,
                    'third_name'       => $user->thrd_name,
                    'fourth_name'       => $user->fm_name,
                    'classification'       => $user->classification,
                    'birth_date' => $user->birth_date,
                    'age' => Carbon::parse($user->birth_date)->age,
                    'gender' => $user->gender,
                    'health_status' => $user->health_status,
                    'work_status' => $user->work_status,
                    'social_status' => $user->social_status,
                    'identity_number' => $user->identity_number,
//                    'phone' => $user->tell !==null ? $user->tell : $user->phone,
                    'phone_number' => $user->tell !==null ? $user->tell : $user->phone,
                ]
            ];
        }

    }
    function getRelationData()
    {
        $fromConfig =  \AssistancesSettings::read([\AssistancesSettings::ORG_ID => $_SESSION['organization']->id])[0]->relatives_types_supported;
//        return $formConfig;

        $fromConfig = explode(',' , $fromConfig);

//        $fromConfig = collect($fromConfig);
//
//        $fromConfig->map(function ($config) {
//            $config->id  = intval($config->id);
//        });
//
//        return $fromConfig;

        $all = collect(Setting::getList(3));

        return $all->whereIn('id' , $fromConfig)->flatten();

//        $filterdData = [];
//        foreach ($all as $item){
////            return $item->id;
//            if (in_array($item->id , $fromConfig) ){
//                return $item;
//                $filterdData []= $item;
//            }
//        }
//        return $all;
//        return $all;
        return $filterdData;
    }
    function getSocialStatus()
    {
        return Setting::getList(5);
    }
    function getWorkStatus()
    {
        return Setting::getList(280);
    }
    function getHealthStatus()
    {
        return Setting::getList(269);
    }

    public function saveImage($family , $files){


        try {

            $document = new Document();
            $post['permission'] = null;
            $document->bindProperties($post);
            $document->fileArray = $files;
            $document->client_id = $_SESSION['organization']->id;
            $document->user_id = $family;
            $document->operation_code = 'userdata';
            $document->table_name = sh_user::class;
            $document->row_id = $family;
            $document->created_by = $_SESSION['user']->id;
            $document->created_date = date('Y-m-d');
            $documentName = explode('.', $files['name']);
            $document->name = $documentName[0];
            if($document->save()){
                return $document->id;
            }
            return "cannot be saved";
        }  catch (DocumentException $e) {
            /**
             * @TODO handling other errors and display some feed back for end user
             */
            return $e->getMessage();
        }

    }

    function saveData($id, Request $request){
        $file_number = $request->file_number;
        $cache = cache('family_' . $id);
        $cache = cache('family_' . $id , [
            "step" => 4,
            "data" => $cache['data']

        ]);
        $users =  (new FamilyCreator($cache['data'] , $id ,$file_number))->users;

        if ($users){
            cache_forget('family_' . $id);
            session('s_active_cv_tab' , 'FamilyData');
            return [
                "error" => false,
                "message" => $users
            ];
        }


        return [
            "error" => true,
            "message" => "problem in saving data"
        ];
//        return $cache['data'];
//        User::insert();
    }

    function getFileNumber($id,Request $request){
        $family =Member::with('family')->where(\AssistanceFamilyMembers::USER_ID , $id)->first()->family;
        if($family){
            return [
                'file_number' => $family->file_number,
                'edit' => false
            ];
        } else {
            $auto_number = AssistancesSettings::read([AssistancesSettings::ORG_ID => $_SESSION['organization']->id])[0]->auto_number;
            if($auto_number == 1){
                $family = Family::latest(\AssistanceFamilies::CREATED_DATE)->first();
                if($family) {
                    return [
                        'file_number' => $family->file_number + 1,
                        'edit' => false
                    ];
                } else {
                    return [
                        'file_number' => 1,
                        'edit' => false
                    ];
                }
            }
            else {
                return [
                    'file_number' => 0,
                    'edit' => true
                ];
            }
        }
    }


    function getUserIncomeData($id,$i){
        $income = Income::where("db_inc_user_id" , $id)
            ->where("db_inc_monthly_income_type" , \DBIncome::STATIC_SALARY)
             ->sum('db_inc_monthly_incole_value');

        return [
                'icnome' => $income ?? 0,
                'error' => $income ? true : false,
                'i' => intval($i)
            ];
//        ] , $incomeData);
    }



}