<?php

namespace Api\Controllers\Assistances;

use Api\Controllers\Controller;
use assist_exch_rule_classes;
use AssistanceExchRuleClass;
use DB;
use AssistanceExchRule;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Http\Response;

class RulesController extends Controller
{

    /**
     * Create new exchange rules
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $rule = new AssistanceExchRule();
        $rule->bindProperties($request->only(['name', 'points_sum']));
        $rule->org_id = $_SESSION['organization']->id;
        $rule->created_by = $_SESSION['user']->id;
        $rule->created_date = Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT);
        $rule->save();

        $classes = json_decode($request->get('classes'), true);
        $data = [];
        $orgId = $_SESSION['organization']->id;
        foreach ($classes as $class) {
            $newClass = [];
            $newClass[AssistanceExchRuleClass::ORG_ID] = $orgId;
            $newClass[AssistanceExchRuleClass::RULE_ID] = $rule->id;
            $newClass[AssistanceExchRuleClass::CODE] = $class['code'];
            $newClass[AssistanceExchRuleClass::NAME] = $class['name'];
            $newClass[AssistanceExchRuleClass::ACTION] = $class['action'];
            $newClass[AssistanceExchRuleClass::POINTS_MIN] = $class['points_min'];
            $newClass[AssistanceExchRuleClass::POINTS_MAX] = $class['points_max'];
            $newClass[AssistanceExchRuleClass::CREATED_BY] = $_SESSION['user']->id;
            $newClass[AssistanceExchRuleClass::CREATED_DATE] = Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT);

            $data[] = $newClass;
        }
        // Insert rule's classes into database
        DB::table(assist_exch_rule_classes::class)->insert($data);

        return new Response([
            'success' => true
        ]);
    }

    /**
     * Update exchange rule
     *
     * @param Request $request
     * @param int $rule
     * @return array
     * @internal param $id
     */
    public function update(Request $request, int $rule)
    {
        $ruleObject = AssistanceExchRule::readID($rule);
        $ruleObject->bindProperties($request->only(['name', 'points_sum']));
        try {
            $ruleObject->save();
        } catch (\AssistanceExchRuleException $e) {
        }

        // Delete old classes
        DB::table(assist_exch_rule_classes::class)
            ->where(AssistanceExchRuleClass::ORG_ID, $_SESSION['organization']->id)
            ->where(AssistanceExchRuleClass::RULE_ID, $ruleObject->id)
            ->delete();

        $classes = json_decode($request->get('classes'), true);
        $data = [];
        $orgId = $_SESSION['organization']->id;
        foreach ($classes as $class) {
            $newClass = [];
            $newClass[AssistanceExchRuleClass::ORG_ID] = $orgId;
            $newClass[AssistanceExchRuleClass::RULE_ID] = $ruleObject->id;
            $newClass[AssistanceExchRuleClass::CODE] = $class['code'];
            $newClass[AssistanceExchRuleClass::NAME] = $class['name'];
            $newClass[AssistanceExchRuleClass::ACTION] = $class['action'];
            $newClass[AssistanceExchRuleClass::POINTS_MIN] = $class['points_min'];
            $newClass[AssistanceExchRuleClass::POINTS_MAX] = $class['points_max'];
            $newClass[AssistanceExchRuleClass::CREATED_BY] = $_SESSION['user']->id;
            $newClass[AssistanceExchRuleClass::CREATED_DATE] = Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT);

            $data[] = $newClass;
        }
        // Insert rule's classes into database
        DB::table(assist_exch_rule_classes::class)->insert($data);

        return [
            'success' => true
        ];
    }
}