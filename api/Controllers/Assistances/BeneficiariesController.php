<?php

namespace Api\Controllers\Assistances;

use Api\Controllers\Controller;
use AssistanceBeneficiary;
use Carbon\Carbon;
use DB;
use assist_beneficiaries;
use assist_loan_installments;
use AssistanceInstallment;
use AssistanceRequest;
use assist_requests;
use Illuminate\Http\Request;
use sh_user;
use User;

class BeneficiariesController extends Controller
{
    public function index(int $assistance)
    {
        $beneficiaries = DB::table(assist_beneficiaries::class)
            ->where(AssistanceBeneficiary::ORG_ID, $_SESSION['organization']->id)
            ->where(AssistanceBeneficiary::SERVICE_ID, $assistance)
            ->where(AssistanceBeneficiary::RECORD_STATUS, AssistanceBeneficiary::ENABLED_RECORD_STATUS)
            ->get();
        $usersIds = $beneficiaries->pluck(AssistanceBeneficiary::USER_ID)->all();
        $users = DB::table(sh_user::class)
            ->whereIn(User::ID, $usersIds)
            ->pluck(User::FULL_NAME, User::ID)
            ->all();

        return [
            'beneficiaries' => $beneficiaries->all(),
            'users' => $users
        ];
    }

    public function show(int $assistance, int $beneficiary)
    {
        $beneficiaryObject = AssistanceBeneficiary::readID($beneficiary);
        /**
         * @var $request assist_requests
         */
        $request = DB::table(assist_requests::class)
            ->where(AssistanceRequest::ORG_ID, $_SESSION['organization']->id)
            ->where(AssistanceRequest::ASSISTANCE_ID, $assistance)
            ->where(AssistanceRequest::BENEFICIARY_ID, $beneficiaryObject->user_id)
            ->first();
        /**
         * @var $installment assist_loan_installments
         */
        $installment = DB::table(assist_loan_installments::class)
            ->where(AssistanceInstallment::ORG_ID, $_SESSION['organization']->id)
            ->where(AssistanceInstallment::BENEFICIARY_ID, $beneficiary)
            ->where(AssistanceInstallment::REQUEST_ID, $request->assist_requests_id)
            ->first();
        return $installment->assist_loan_installments_amount;
    }

    public function search(Request $request){

        $phone = $request->phone;
        $name = $request->name;

        $assistance = \AssistancesSettings::read([\AssistancesSettings::ORG_ID => $_SESSION['organization']->id])[0];
        $user_class = $assistance->client_id;
//        return $user_class;

//        return $users_id->pluck(\SanabelRecord::DONATOR_ID) ;

        /** @var Collection  $users */
        $users = \DB::table('sh_user')
            ->when($phone , function($query) use($phone){
                $query->where(\User::PHONE , $phone)
                    ->orWhere(\User::TELL, $phone);
            })
            ->when($name , function($query) use($name){
                $query->where(\User::FULL_NAME , 'LIKE' , '%'. $name .'%');

            })
//            ->whereRaw($user_class->id . ' IN (sh_user_classification)')
            ->whereRaw('FIND_IN_SET( '. $user_class .', sh_user_classification)')
            //            ->whereIn(\User::ID , $users_id)
            ->select([
                \User::ID,
                \User::FULL_NAME,
                \User::PHONE,
                \User::TELL,
                \User::EXTRA_DATA
            ])
            ->get()
        ;
        if($users){

            $users->map(function ($user)  use($categories){
                $user->id = $user->{\User::ID};
                $user->full_name = $user->{\User::FULL_NAME};
                $user->phone = $user->{\User::PHONE} != null ? $user->{\User::PHONE} : $user->{\User::TELL};

                unset($user->{\User::ID});
                unset($user->{\User::FULL_NAME});
                unset($user->{\User::PHONE});
                unset($user->{\User::TELL});
                unset($user->{\User::EXTRA_DATA});


            });

            return [
                "error" => false,
                "data" => $users
            ];
        }
        return [
            "error" => true,
            "data" => []
        ];
    }

    public function getBeneficiaryReceived(Request $request) {
        $beneficiary_id = $request->user_id;


        $records = \DB::table(\snbl_records::class . ' as sanabel_record')
            ->join(\snbl_cases::class . ' as sanabel_case' , 'sanabel_record.' . \SanabelRecord::CASE_ID , 'sanabel_case.' .  \SanabelCase::ID)
            ->join(\sh_user::class . ' as donar' , 'sanabel_record.' . \SanabelRecord::DONATOR_ID , 'donar.' .  \User::ID)
            ->join(\sh_user::class . ' as beneficary' , 'sanabel_record.' . \SanabelRecord::BENEFICIARY_ID , 'beneficary.' .  \User::ID)
            ->join(\fin_receipt_purposes::class . ' as purpose' , 'sanabel_case.' .  \SanabelCase::PURPOSE_ID , 'purpose.' .  \FinReceiptPurposes::ID)
            ->join(\fin_petty_cash_ledger::class . ' as ledger' , \DB::raw("FIND_IN_SET(purpose." . \FinReceiptPurposes::ID .",ledger." . \FinPCLedger::PURPOSES . ')'),">",\DB::raw("'0'"))
            ->where('sanabel_record.' . \SanabelRecord::DATE , '<=' , Carbon::today())
            ->where('sanabel_record.' . \SanabelRecord::BENEFICIARY_ID , $beneficiary_id)
            ->whereRaw('sanabel_record.' . \SanabelRecord::RECEIVED . '<>' . 'sanabel_record.' . \SanabelRecord::PAID)
//            ->whereRaw('sanabel_record.' . \SanabelRecord::RECEIVED . '> 0')
            ->select([
                'sanabel_record.' . \SanabelRecord::ID . ' as record_id',
                'sanabel_record.' . \SanabelRecord::RECEIVED . ' as record_received',
                'sanabel_record.' . \SanabelRecord::PAID . ' as record_paid',
                'sanabel_record.' . \SanabelRecord::DATE . ' as record_date',
                'sanabel_case.' . \SanabelCase::NAME . ' as case_name',
                'donar.' . \User::FULL_NAME . ' as donar_full_name',
                'beneficary.' . \User::FULL_NAME . ' as beneficary_full_name',
                'ledger.' . \FinPCLedger::ID . ' as ledger_id',
                'purpose.' .  \FinReceiptPurposes::ID . ' as purpose_id',

            ])
            ->get();

        return [
            "error" => $records->count() <= 0,
            "data" => $records
        ];


    }

    /*
     * get all details about assistance
     * first get the assistance then get AssistanceRequest
     * second get beneficiaries in the AssistanceRequest
     * third get Sanable case which belongs to AssistanceRequest
     * fourth get the Sanabel records before today from the Sanabel case and not paid
     * fifth return all the data group by beneficiaries
     * */
    public function getAssitancesDetails(Request $request){
        $assistance_id = $request->assist_id;


        $records = \DB::table(\snbl_records::class . ' as sanabel_record')
            ->join(\snbl_cases::class . ' as sanabel_case' , 'sanabel_record.' . \SanabelRecord::CASE_ID , 'sanabel_case.' .  \SanabelCase::ID)
            ->join(\assist_request::class . ' as assistance_request' , 'sanabel_case.' . \SanabelCase::PROGRAM_MAIN_KEY , 'assistance_request.' .  \AssistanceRequest::ID)
            ->join(\assist_assistance::class . ' as assistance' , 'assistance_request.' . \AssistanceRequest::ASSISTANCE_ID , 'assistance.' .  \Assistance::ID)
            ->join(\sh_user::class . ' as donar' , 'sanabel_record.' . \SanabelRecord::DONATOR_ID , 'donar.' .  \User::ID)
            ->join(\sh_user::class . ' as beneficary' , 'sanabel_record.' . \SanabelRecord::BENEFICIARY_ID , 'beneficary.' .  \User::ID)
            ->join(\fin_receipt_purposes::class . ' as purpose' , 'sanabel_case.' .  \SanabelCase::PURPOSE_ID , 'purpose.' .  \FinReceiptPurposes::ID)
            ->join(\fin_petty_cash_ledger::class . ' as ledger' , \DB::raw("FIND_IN_SET(purpose." . \FinReceiptPurposes::ID .",ledger." . \FinPCLedger::PURPOSES . ')'),">",\DB::raw("'0'"))

//            ->where('sanabel_record.' . \SanabelRecord::DATE , '<=' , Carbon::today())
            ->where('assistance.' . \Assistance::ID , $assistance_id)
            ->where('sanabel_case.' . \SanabelCase::PROGRAM_ID , \Program::PROGRAM_ASSISTANCES_P269)
            ->whereRaw('sanabel_record.' . \SanabelRecord::RECEIVED . '<>' . 'sanabel_record.' . \SanabelRecord::PAID)

//            ->whereRaw('sanabel_record.' . \SanabelRecord::RECEIVED . '> 0')
            ->whereRaw('sanabel_record.' . \SanabelRecord::PAID . '> 0')
            ->select([
                'sanabel_record.' . \SanabelRecord::ID . ' as record_id',
                'sanabel_record.' . \SanabelRecord::RECEIVED . ' as record_received',
                'sanabel_record.' . \SanabelRecord::PAID . ' as record_paid',
                'sanabel_record.' . \SanabelRecord::DATE . ' as record_date',
                'sanabel_case.' . \SanabelCase::NAME . ' as case_name',
                'donar.' . \User::FULL_NAME . ' as donar_full_name',
                'beneficary.' . \User::FULL_NAME . ' as beneficary_full_name',
                'sanabel_record.' . \SanabelRecord::BENEFICIARY_ID . ' as beneficary_id',
                'ledger.' . \FinPCLedger::ID . ' as ledger_id',
                'purpose.' .  \FinReceiptPurposes::ID . ' as purpose_id',

            ])
            ->orderBy('beneficary_id')

            ->get()
            ->groupBy('beneficary_full_name')
        ;

        return [
            "error" => $records->count() <= 0,
            "data" => $records
        ];
    }


}