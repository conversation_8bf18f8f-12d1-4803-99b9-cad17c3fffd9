<?php
namespace Api\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;


class HomeController extends Controller
{
    public function index()
    {

    }

    public function updateOrganizationOperationalStatus(Request $request)
    {

        if($request){

            try{
                $organization = \Organization::readID((int)$_SESSION['organization']->id);
                $organization->operational_status = \GuzzleHttp\json_encode($request->post('data'));
                $organization->update();

                return new Response([
                    'success' => true,
                    'status' => 200
                ]);

            }catch (\OrganizationException $e){

                return new Response([
                    'success' => false,
                    'message' => $e->getMessage(),
                    'status' => 500
                ]);

            }

        }

    }


}