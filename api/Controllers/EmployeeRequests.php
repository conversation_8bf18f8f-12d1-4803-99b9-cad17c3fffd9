<?php
namespace Api\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;

class EmployeeRequests extends Controller
{
    public function getWrdias($id){
        $doam_id = \Vacant::getEmployeeJobs($_SESSION['organization']->id, $id, \Vacant::EMPLOYEE_BASIC_VACANT)->att_doam_id;

        return \DB::table(\hr_wrdiah::class)
            ->where(\Wrdiah::ORG_ID,$_SESSION['organization']->id)
            ->where(\Wrdiah::DOAM_ID,$doam_id)->get();
    }
}