<?php
/**
 * Created by PhpStorm.
 * User: jodeveloper
 * Date: 28/04/21
 * Time: 10:07 ص
 */

namespace Api\Controllers;


use Exception;
use Illuminate\Http\Request;
use Models\Warehouse\Product;
use Models\Warehouse\ProductUnit;
use Models\Warehouse\Stock;
use Models\Warehouse\StockTransaction;
use Setting;

class WarehouseController extends Controller
{
    public function index($id){

        try{
            $type = Stock::findOrFail($id)->type;
            return [
                'error' => false,
                'data' => Setting::readID($type)->translatedName
            ];
        }catch (Exception $e){
            return ['error' => true, 'data' => ''
            ];
        }
    }

    /**
     * @param $id
     * @return mixed
     */
    public function  getProductType($id){
        return Product::find($id)->type;
    }
    public function getTargetUnitNumber(Request $request){
        try{
//            $units =ProductUnit::where('product_id',$request->query('product_id'))->with('unit')->get();
            $units = $this->getFilteredUnits($request->query('product_id'),(int)$request->query('unit_id'))['data'];
//            return $units;
            $data=[];
            foreach ($units as $unit){
                $data[]=[ $unit['id'] => $unit['number']];
            }
            $current=1;
            $test=[];
            if ((int)$request->query('unit_id')===(int)$request->query('target')) {
                return [
                    'error' => false,
                    'data' => 1 * (int)$request->query('qty')
                ];
            }
                for ($i = count($data)-1; $i >=0 ; $i--) {

                    if ((array_keys($data[$i])[0] === (int)$request->query('unit_id'))) {
                        for ($j = $i; $j < count($data); $j++) {
                            $current *= $data[$j][array_keys($data[$j])[0]];

                               if (array_keys($data[$j])[0] === (int)$request->query('target'))
                                {

                                    break;
                                }

                        }
                        break;
                    }

                }


            $data = $current;

            return [
                'error' => false,
                'data' => $data * (int)$request->query('qty')
            ];
        }catch (Exception $e){
            return [
                'error'=>true,
                'data'=>'No data exist'
            ];
        }
    }
    public function getUnitsNumberDivion(Request $request){
        try{
            $units =ProductUnit::where('product_id',$request->query('product_id'))->with('unit')->get();
            $data=[];
            foreach ($units as $unit){
                 $data[]=[ $unit["id"]=>$unit["number"]];
            }
            $current=1;

            for($i=0;$i<count($data);$i++){

                if((array_keys($data[$i])[0] === (int)$request->query('unit_id'))){
                    for($j=$i+1;$j<count($data);$j++){
                        $current *=$data[$j][array_keys($data[$j])[0]];
                    }
                    break;
                }
            }

            $data = $current;

            return [
                'error' => false,
                'data' => $data
            ];
        }catch (Exception $e){
            return [
                'error'=>true,
                'data'=>'No data exist'
            ];
        }
    }

    public function getUnits($id){
       try{
           $units =ProductUnit::where('product_id',$id)->with('unit')->get();
           return [
               'error' => false,
               'data' => $units
           ];
       }catch (Exception $e){
           return [
               'error'=>true,
               'data'=>'No data exist'
           ];
       }
    }

    public function getFilteredUnits($id,$unit_id){
        try{
            $units =ProductUnit::where('product_id',$id)->with('unit')->get();
            $list_unit=[];
            $i=0;
            foreach ($units as $unit){
                if($unit->id==$unit_id||$i>=1)
                {
                    $list_unit[]=$unit;
                    $i++;
                }
            }

             return [
                'error' => false,
                'data' => $list_unit
            ];
//            return [
//                'error' => false,
//                'data' => $units
//            ];
        }catch (Exception $e){
            return [
                'error'=>true,
                'data'=>'No data exist'
            ];
        }
    }

    public function getQty(Request $request)
    {
        try{
            $totalQty=StockTransaction::where('product_id',$request->query('product_id'))->where('stock_id',$request->query('stock_id'))->get()->sum('qty');
            return [
                'error' => false,
                'data' => $totalQty
            ];
        }catch (Exception $e){
            return [
                'error'=>true,
                'data'=>'No data exist'
            ];
        }
    }

    public function getProducts($id){
        try{
            $products =Product::where('group_id',$id)->with('group')->get();
            return [
                'error' => false,
                'data' => $products
            ];
        }catch (Exception $e){
            return [
                'error'=>true,
                'data'=>'No data exist'
            ];
        }
    }
    public function convertToLessUnit(Request $request)
    {
        try{
            $units = ProductUnit::where('product_id' ,$request->query('product_id'))->orderBy('sale_price')->get();
            $price = $units->where('id' ,$request->query('unit_id'))->first()->sale_price;
            $first_sale = $units->first()->sale_price;
            $first_unit_id = $units->first()->stock_unit_id;
            $quantity_from_first = $price / $first_sale;
            $qty = $request->query('qty') * $quantity_from_first;
            return [
                'error' => false,
                'data' => $qty
            ];
        }catch (Exception $e){
    return [
    'error'=>true,
    'data'=>'No data exist'
    ];
    }

    }
}