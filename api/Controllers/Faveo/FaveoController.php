<?php
/**
 * Created by PhpStorm.
 * User: joseph
 * Date: 22/10/19
 * Time: 02:05 م
 */

namespace Api\Controllers\Faveo;

use Api\Controllers\Controller;

use Application\WebSocket\Client\SnsoWebSocket;
use Faveo\Faveo;
use Illuminate\Http\Request;
use Models\SupportTicket;
use Ticket;


class FaveoController extends Controller
{
    public function getAuthUser()
    {
        return Faveo::getAuthUser();
    }

    public function replay(Request $request){

        try {
            $ticket_from_request = $request->json()->all()["ticket"];//["ticket_number"]

            $ticket = new SupportTicket();

            $ticket->ticket_id = $request->json()->all()['ticket_id'];
            $ticket->user_id = $request->json()->all()['user_id'];

            $ticket->ticket_number = $ticket_from_request["ticket_number"];

            $ticket->body = $request->json()->all()["body"];

            $ticket->url = "";
            $ticket->read = 0;


            $ticket->save();

            SnsoWebSocket::sendTicketFeedBack($ticket->user_id);

            return [
              "error" => false,
              "message" => "Feedback Send Successfully"
            ];

        } catch (\TicketException $e){
            return [
                "error" => true,
                "message" => $e->getMessage(),
                "trace" => $e->getTrace()
            ];
        }

    }

    public function readAll(){

        SupportTicket::getUnreadTicket(user())->update([
            'read' => 1
        ]);

        return [
            "error" => false,
            "message" => "done"
        ];
    }
    public function getTicketsNum()
    {
        if($_SESSION['user']){
            return SupportTicket::getUnreadTicket(user())->count();
        }
    }

}