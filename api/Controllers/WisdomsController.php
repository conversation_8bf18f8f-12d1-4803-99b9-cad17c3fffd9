<?php

namespace Api\Controllers;

use DB;
use Illuminate\Http\Request;
use Wisdom;

class WisdomsController extends Controller
{
    public function index()
    {

        $wisdoms = DB::table('st_hnts')->select('st_hnts_said as body', 'st_hnts_name as author', 'st_hnts_id as id')->get();

        return $wisdoms;

    }

    public function store(Request $request){

        DB::table('st_hnts')->insert([
            'st_hnts_name' => $request->author,
            'st_hnts_said' => $request->body
        ]);
        $wisdoms = DB::table('st_hnts')
            ->select('st_hnts_said as body','st_hnts_name as author','st_hnts_id as id')->get();
        return [
            'message' => 'success',
            'data' =>$wisdoms
        ];

    }

    public function destroy($wisdom)
    {

        try {
            DB::table('st_hnts')->where('st_hnts_id', '=', $wisdom)->delete();

            return [
                'message' => 'success',
                'deleted' => true
            ];

        } catch (\Exception $exception) {


        }

        return [
            'message' => 'failed',
            'deleted' => false
        ];

    }

    public function update(Request $request, $id)
    {

        try{

            $wisdom = \Wisdom::readID((int)$id);
            $wisdom->name = $request->author;
            $wisdom->said = $request->body;
            $wisdom->save();

        }catch (\WisdomException $e){

        }

    }

}