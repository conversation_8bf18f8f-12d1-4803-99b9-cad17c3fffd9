<?php

namespace Api\Controllers\Sanabel;
/**
 * Created by PhpStorm.
 * User: albraa
 * Date: 07/09/22
 * Time: 02:48 م
 */
use \Api\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;


class Voucher extends Controller
{

    public function searchDonars(Request $request){

        $phone = $request->phone;
        $name = $request->name;

        $user_class = \UserClass::readID((int) \SanabelSettings::read([\SanabelSettings::ORG_ID => $_SESSION['organization']->id])[0]->donors_classifications);
        $users_id = \DB::table(\snbl_records::class)
            ->whereRaw( \SanabelRecord::AMOUNT . '<>'  . \SanabelRecord::PAID )
            ->distinct()
            ->get([
                \SanabelRecord::DONATOR_ID,
            ])->pluck(\SanabelRecord::DONATOR_ID)
        ;
//        return $users_id->pluck(\SanabelRecord::DONATOR_ID) ;

        /** @var Collection  $users */
        $users = \DB::table('sh_user')
            ->when($phone , function($query) use($phone){
               $query->where(\User::PHONE , $phone)
                   ->orWhere(\User::TELL, $phone);
            })
            ->when($name , function($query) use($name){
                $query->where(\User::FULL_NAME , 'LIKE' , '%'. $name .'%');

            })
//            ->whereRaw($user_class->id . ' IN (sh_user_classification)')
            ->whereRaw('FIND_IN_SET( '. $user_class->id .', sh_user_classification)')
            //            ->whereIn(\User::ID , $users_id)
            ->select([
                \User::ID,
                \User::FULL_NAME,
                \User::PHONE,
                \User::TELL,
                \User::EXTRA_DATA
            ])
            ->get()
        ;
        if($users){
            $categories = \SanabelDonorsCategory::read([
                \SanabelDonorsCategory::ORG_ID => organization('id')
            ]);

            $categories = collect($categories);

            $users->map(function ($user)  use($categories){
                $user->id = $user->{\User::ID};
                $user->full_name = $user->{\User::FULL_NAME};
                $category_id = json_decode($user->{\User::EXTRA_DATA})->category_id;
                $user->category_name =   $categories->where('id' , $category_id)->first()->name;
                $user->phone = $user->{\User::PHONE} != null ? $user->{\User::PHONE} : $user->{\User::TELL};

                unset($user->{\User::ID});
                unset($user->{\User::FULL_NAME});
                unset($user->{\User::PHONE});
                unset($user->{\User::TELL});
                unset($user->{\User::EXTRA_DATA});


            });

            return [
                "error" => false,
                "data" => $users
            ];
        }
        return [
            "error" => true,
            "data" => []
        ];
    }

    public function getAllDonarInstallment(Request $request){
        $ledger_id = $request->ledger_id;
        $donar_id = $request->user_id;

        $records = \DB::table(\snbl_records::class . ' as sanabel_record')
            ->join(\snbl_cases::class . ' as sanabel_case' , 'sanabel_record.' . \SanabelRecord::CASE_ID , 'sanabel_case.' .  \SanabelCase::ID)
            ->join(\sh_user::class . ' as donar' , 'sanabel_record.' . \SanabelRecord::DONATOR_ID , 'donar.' .  \User::ID)
            ->join(\sh_user::class . ' as beneficary' , 'sanabel_record.' . \SanabelRecord::BENEFICIARY_ID , 'beneficary.' .  \User::ID)
            ->join(\fin_receipt_purposes::class . ' as purpose' , 'sanabel_case.' .  \SanabelCase::PURPOSE_ID , 'purpose.' .  \FinReceiptPurposes::ID)
            ->join(\fin_petty_cash_ledger::class . ' as ledger' , \DB::raw("FIND_IN_SET(purpose." . \FinReceiptPurposes::ID .",ledger." . \FinPCLedger::PURPOSES . ')'),">",\DB::raw("'0'"))
//            ->where('sanabel_record.' . \SanabelRecord::DATE , '<=' , Carbon::today())
            ->where('ledger.' . \FinPCLedger::ID , $ledger_id)
            ->where('sanabel_record.' . \SanabelRecord::DONATOR_ID , $donar_id)
            ->whereRaw('sanabel_record.' . \SanabelRecord::AMOUNT . '<>' . 'sanabel_record.' . \SanabelRecord::PAID )
            ->select([
                'sanabel_record.' . \SanabelRecord::ID . ' as record_id',
                'sanabel_record.' . \SanabelRecord::AMOUNT . ' as record_amount',
                'sanabel_record.' . \SanabelRecord::PAID . ' as record_paid',
                'sanabel_record.' . \SanabelRecord::DATE . ' as record_date',
                'sanabel_case.' . \SanabelCase::NAME . ' as case_name',
                'donar.' . \User::FULL_NAME . ' as donar_full_name',
                'beneficary.' . \User::FULL_NAME . ' as beneficary_full_name',
            ])
            ->get();

        return [
            "error" => $records->count() <= 0,
            "data" => $records
        ];
    }

    public function getDonarPurposeInstallment(Request $request) {
        $ledger_id = $request->ledger_id;
        $donar_id = $request->user_id;
        $purpose_id = $request->purpose_id;

        $records = \DB::table(\snbl_records::class . ' as sanabel_record')
            ->join(\snbl_cases::class . ' as sanabel_case' , 'sanabel_record.' . \SanabelRecord::CASE_ID , 'sanabel_case.' .  \SanabelCase::ID)
            ->join(\sh_user::class . ' as donar' , 'sanabel_record.' . \SanabelRecord::DONATOR_ID , 'donar.' .  \User::ID)
            ->join(\sh_user::class . ' as beneficary' , 'sanabel_record.' . \SanabelRecord::BENEFICIARY_ID , 'beneficary.' .  \User::ID)
            ->join(\fin_receipt_purposes::class . ' as purpose' , 'sanabel_case.' .  \SanabelCase::PURPOSE_ID , 'purpose.' .  \FinReceiptPurposes::ID)
            ->join(\fin_petty_cash_ledger::class . ' as ledger' , \DB::raw("FIND_IN_SET(purpose." . \FinReceiptPurposes::ID .",ledger." . \FinPCLedger::PURPOSES . ')'),">",\DB::raw("'0'"))
//            ->where('sanabel_record.' . \SanabelRecord::DATE , '<=' , Carbon::today())
            ->where('ledger.' . \FinPCLedger::ID , $ledger_id)
            ->where('sanabel_record.' . \SanabelRecord::DONATOR_ID , $donar_id)
            ->whereRaw('sanabel_record.' . \SanabelRecord::AMOUNT . '<>' . 'sanabel_record.' . \SanabelRecord::PAID )
            ->where('sanabel_case.' . \SanabelCase::PURPOSE_ID , $purpose_id)
            ->select([
                'sanabel_record.' . \SanabelRecord::ID . ' as record_id',
                'sanabel_record.' . \SanabelRecord::AMOUNT . ' as record_amount',
                'sanabel_record.' . \SanabelRecord::PAID . ' as record_paid',
                'sanabel_record.' . \SanabelRecord::DATE . ' as record_date',
                'sanabel_case.' . \SanabelCase::NAME . ' as case_name',
                'donar.' . \User::FULL_NAME . ' as donar_full_name',
                'beneficary.' . \User::FULL_NAME . ' as beneficary_full_name',
            ])
            ->get();

        return [
            "error" => $records->count() <= 0,
            "data" => $records
        ];


    }

    public function getDonarCaseInstallment(Request $request) {
        $ledger_id = $request->ledger_id;
        $donar_id = $request->user_id;
        $case_id = $request->case_id;

        $records = \DB::table(\snbl_records::class . ' as sanabel_record')
            ->join(\snbl_cases::class . ' as sanabel_case' , 'sanabel_record.' . \SanabelRecord::CASE_ID , 'sanabel_case.' .  \SanabelCase::ID)
            ->join(\sh_user::class . ' as donar' , 'sanabel_record.' . \SanabelRecord::DONATOR_ID , 'donar.' .  \User::ID)
            ->join(\sh_user::class . ' as beneficary' , 'sanabel_record.' . \SanabelRecord::BENEFICIARY_ID , 'beneficary.' .  \User::ID)
            ->join(\fin_receipt_purposes::class . ' as purpose' , 'sanabel_case.' .  \SanabelCase::PURPOSE_ID , 'purpose.' .  \FinReceiptPurposes::ID)
            ->join(\fin_petty_cash_ledger::class . ' as ledger' , \DB::raw("FIND_IN_SET(purpose." . \FinReceiptPurposes::ID .",ledger." . \FinPCLedger::PURPOSES . ')'),">",\DB::raw("'0'"))
//            ->where('sanabel_record.' . \SanabelRecord::DATE , '<=' , Carbon::today())
//            ->where('ledger.' . \FinPCLedger::ID , $ledger_id)
            ->where('sanabel_record.' . \SanabelRecord::DONATOR_ID , $donar_id)
            ->whereRaw('sanabel_record.' . \SanabelRecord::AMOUNT . '<>' . 'sanabel_record.' . \SanabelRecord::PAID )
            ->where('sanabel_case.' . \SanabelCase::ID , $case_id)
            ->select([
                'sanabel_record.' . \SanabelRecord::ID . ' as record_id',
                'sanabel_record.' . \SanabelRecord::AMOUNT . ' as record_amount',
                'sanabel_record.' . \SanabelRecord::PAID . ' as record_paid',
                'sanabel_record.' . \SanabelRecord::DATE . ' as record_date',
                'sanabel_case.' . \SanabelCase::NAME . ' as case_name',
                'donar.' . \User::FULL_NAME . ' as donar_full_name',
                'beneficary.' . \User::FULL_NAME . ' as beneficary_full_name',
            ])
            ->get();

        return [
            "error" => $records->count() <= 0,
            "data" => $records
        ];


    }

    public function getAllGatesAndCasesInsideLedger(Request $request){
        $ledger_id = $request->ledger_id;
        $user_id = $request->user_id;


        $gates = \DB::table(\snbl_gates::class . ' as sanabel_gate')
            ->join(\fin_receipt_purposes::class . ' as purpose' , 'sanabel_gate.' .  \SanabelGate::PURPOSE_ID , 'purpose.' .  \FinReceiptPurposes::ID)
            ->join(\fin_petty_cash_ledger::class . ' as ledger' , \DB::raw("FIND_IN_SET(purpose." . \FinReceiptPurposes::ID .",ledger." . \FinPCLedger::PURPOSES . ')'),">",\DB::raw("'0'"))
            ->where('ledger.' . \FinPCLedger::ID , $ledger_id)
            ->where(\SanabelGate::IS_ACTIVE , \SanabelGate::ACTIVATED_SETTING)
            ->select([
                'sanabel_gate.' .\SanabelGate::ID . ' as gate_id',
                'sanabel_gate.' .\SanabelGate::ID . ' as case_id',
                'sanabel_gate.'.\SanabelGate::NAME . ' as name',
                'sanabel_gate.'.\SanabelGate::PURPOSE_ID . ' as purpose_id',
            ])
            ->get()
        ;




        $gates->map(function ($gate)  {
            $gate->public = 'مشاع';
            $gate->is_public = true;
            $gate->name =   $gate->public . ' >> ' . $gate->name ;
            $gate->case_id = 0;
        });

        $cases = \DB::table(\snbl_records::class . ' as sanabel_record')
            ->join(\snbl_cases::class . ' as sanabel_case' , 'sanabel_record.' . \SanabelRecord::CASE_ID , 'sanabel_case.' .  \SanabelCase::ID)
//            ->join(\fin_receipt_purposes::class . ' as purpose' , 'sanabel_case.' .  \SanabelCase::PURPOSE_ID , 'purpose.' .  \FinReceiptPurposes::ID)
//            ->join(\fin_petty_cash_ledger::class . ' as ledger' , \DB::raw("FIND_IN_SET(purpose." . \FinReceiptPurposes::ID .",ledger." . \FinPCLedger::PURPOSES . ')'),">",\DB::raw("'0'"))
//            ->where('ledger.' . \FinPCLedger::ID , $ledger_id)
//            ->where('sanabel_case.' .\SanabelCase::IS_ACTIVE , \SanabelGate::ACTIVATED_SETTING)
            ->where('sanabel_record.' .\SanabelRecord::DONATOR_ID , $user_id)
            ->select([
                'sanabel_case.' .\SanabelCase::ID . ' as case_id',
                'sanabel_case.' .\SanabelCase::GATE_ID . ' as gate_id',
                'sanabel_case.'.\SanabelCase::NAME . ' as name',
                'sanabel_case.'.\SanabelCase::PURPOSE_ID . ' as purpose_id',
            ])
            ->get()
        ;

//        return $cases;

        $cases->map(function ($case)  {
            $case->public = 'مخصص';
            $case->is_public = false;
            $case->name =   $case->public . ' >> ' . $case->name ;

        });


        $allData = $gates->merge($cases)->sortByDesc('gate_id')->flatten();

        return $allData;

    }

    public function gateType(Request $request){
        $gate = \DB::table(\snbl_gates::class)
            ->where(\SanabelGate::ID , $request->gate_id)
            ->select([
                \SanabelGate::REPEAT_TYPE .' as repeat_type',
                \SanabelGate::IS_LIMITED .' as is_limited'
            ])
            ->get();
        return $gate;
    }

    /*
     * get total amount and get donated amount
     * */
    public function limitedNotRepeated(Request $request){



        $records = \DB::table(\snbl_gates::class . ' as sanabel_gate')
            ->leftJoin(\snbl_records::class . ' as sanabel_record' , 'sanabel_record.' . \SanabelRecord::GATE_ID , 'sanabel_gate.' .  \SanabelGate::ID)
            ->selectRaw("SUM(sanabel_record.". \SanabelRecord::PAID .") as paid , sanabel_gate.". \SanabelGate::AMOUNT . " as total")
            ->where("sanabel_gate." . \SanabelGate::ID ,  $request->gate_id)
            ->get();

        foreach ($records as $record){
            if(!$record->paid){
                $record->paid = 0;
            }
            $record->can_donate =  $record->total > $record->paid;
        }

        return $records;
    }

    /*
     * get installment amount
     * */
    public function notLimitedRepeated(Request $request){



        $records = \DB::table(\snbl_gates::class . ' as sanabel_gate')
            ->selectRaw("sanabel_gate.". \SanabelGate::AMOUNT . " as total")
            ->where("sanabel_gate." . \SanabelGate::ID ,  $request->gate_id)
            ->get();

//        foreach ($records as $record){
//            if(!$record->paid){
//                $record->paid = 0;
//            }
//        }

        return $records;
    }

    /*
     * get installment amount and all donated installment count and targeted installment
     * */
    public function limitedRepeated(Request $request){



        $records = \DB::table(\snbl_gates::class . ' as sanabel_gate')
            ->leftJoin(\snbl_records::class . ' as sanabel_record' , 'sanabel_record.' . \SanabelRecord::GATE_ID , 'sanabel_gate.' .  \SanabelGate::ID)
            ->selectRaw("COUNT(sanabel_record.". \SanabelRecord::ID .") as paid_installments , sanabel_gate.". \SanabelGate::AMOUNT . " as installment_amount , sanabel_gate." . \SanabelGate::SHARES_COUNT . " as targeted_installments")
            ->where("sanabel_gate." . \SanabelGate::ID ,  $request->gate_id)
            ->whereRaw('sanabel_record.' . \SanabelRecord::AMOUNT . '=' . 'sanabel_record.' . \SanabelRecord::PAID )
            ->get();

        foreach ($records as $record){
            $record->can_donate =  $record->targeted_installments > $record->paid_installments;
        }

        return $records;
    }

}