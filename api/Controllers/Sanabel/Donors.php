<?php

namespace Api\Controllers\Sanabel;
/**
 * Created by PhpStorm.
 * User: albraa
 * Date: 07/09/22
 * Time: 02:48 م
 */
use \Api\Controllers\Controller;

class Donors extends Controller
{
    public function getDonorByCategory($id){
        $user_class = \UserClass::readID((int) \SanabelSettings::read([\SanabelSettings::ORG_ID => $_SESSION['organization']->id])[0]->donors_classifications);
        $users = \User::getUsersWithSpecificClassification($_SESSION['organization'] , $user_class);
        $users = collect($users)->filter(function ($user) use ($id){
            return json_decode($user->extra_data)->category_id === $id;
        });

        return $users;
    }
}