<?php

namespace Api\Controllers\Sanabel;
/**
 * Created by PhpStorm.
 * User: albraa
 * Date: 07/09/22
 * Time: 02:48 م
 */
use \Api\Controllers\Controller;

class Gates extends Controller
{
    public function getGates($id){

        $gates = \DB::table('snbl_gates')
            ->where(\SanabelGate::CATEGORY_ID , $id)
            ->where(\SanabelGate::IS_ACTIVE , \SanabelGate::ACTIVATED_SETTING)
            ->select([
                \SanabelGate::ID,
                \SanabelGate::NAME,
                \SanabelGate::IS_PUBLIC
            ])
            ->get()
        ;


        $gates->map(function ($gate)  {
            $gate->id = $gate->{\SanabelGate::ID};
            $gate->public = $gate->{\SanabelGate::IS_PUBLIC} == \SanabelGate::PUBLIC_GATE ? 'مشاع' : 'مخصص';
            $gate->name =   $gate->public . ' >> ' . $gate->{\SanabelGate::NAME} ;

            unset($gate ->{\SanabelGate::ID});
            unset($gate ->{\SanabelGate::NAME});
            unset($gate ->{\SanabelGate::IS_PUBLIC});
            unset($gate ->public);
        });



        return $gates;
    }
}