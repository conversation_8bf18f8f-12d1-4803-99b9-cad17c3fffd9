<?php

namespace Api\Controllers\Sanabel;
/**
 * Created by PhpStorm.
 * User: albraa
 * Date: 07/09/22
 * Time: 02:48 م
 */
use \Api\Controllers\Controller;

class Categories extends Controller
{
    public function index(){
        try{
        $categories = \SanabelDonorsCategory::read([\SanabelDonorsCategory::ORG_ID => organization('id')]);
        } catch (\SanabelDonorsCategoryException $exception){
            $categories = [];
        }

        return $categories;
    }
}