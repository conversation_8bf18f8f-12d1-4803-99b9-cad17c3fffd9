<?php
/**
 * Created by PhpStorm.
 * User: developer
 * Date: 19/11/20
 * Time: 02:59 م
 */

namespace Api\Controllers\Planning;


use Api\Controllers\Controller;
use Domain\Planning\Reports\GanttChart;

class GanttChartController extends Controller
{
    public function tasks($id){



        $chart = new GanttChart($id);

        return [
            "data" => $chart->tasks,
            "links" => $chart->links
//              "test" => "test"
        ];

    }
}