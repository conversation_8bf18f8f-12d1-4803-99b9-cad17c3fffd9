<?php

namespace Api\Controllers;

use Illuminate\Database\Eloquent\Builder;
use Models\ClubActivity;
use Models\ClubBeneficiary;

class ActivityPaymentController extends Controller
{
    public function __construct()
    {

    }

    public function getActivities($beneficiary_id)
    {

        $activities = ClubActivity::with(['account', 'fee'])->whereHas('subscriptions.beneficiary', function (Builder $query) use ($beneficiary_id){
            $query->where('beneficiary_id', $beneficiary_id);
        })->get()->map(function ($activity) use ($beneficiary_id){

            $activity->payment = \DB::table('activity_subscription_payments')->where(
                [
                    ['beneficiary_id', $beneficiary_id],
                    ['activity_id', $activity->id]
                ]
            )->first();

            return $activity;
        });

        return $activities;

    }
}
