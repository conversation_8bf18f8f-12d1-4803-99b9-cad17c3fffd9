<?php

namespace Api\Controllers;

use Illuminate\Http\Request;
use Models\ActivitySubscription;
use Models\ClubActivity;

class ClubActivityController extends Controller
{
    public function __construct()
    {

    }

    public function getTasks($activity_id)
    {
        $activities = ClubActivity::with(['groups' => function ($query) {
            $query->withCount('subscriptions');
        }, 'tasks'])->find($activity_id);

        return response($activities);
    }
}
