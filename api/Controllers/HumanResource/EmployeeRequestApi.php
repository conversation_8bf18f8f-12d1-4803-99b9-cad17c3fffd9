<?php
/**
 * Created by PhpStorm.
 * User: albraa
 * Date: 25/04/21
 * Time: 10:48 ص
 */

namespace Api\Controllers\HumanResource;


use Api\Controllers\Controller;
use DB;
use Illuminate\Http\Response;
use Program;
use Request;
use Translation;

class EmployeeRequestApi extends Controller
{

    public function permissions($user_id){

        $statuses = [Request::REQUEST_IS_NOT_SEND, Request::REQUEST_IS_UNDER_PROCESS, Request::REQUEST_REACH_END];

        $requests = DB::table('wf_request as request')
            ->select('request.wf_request_id as id' ,
                    'request.wf_request_send_status as send_status' ,
                    'request.wf_request_success as success' ,
                    'wrdiah.hr_wrdiah_name as wrdiah_name',
                    'perm.hr_perm_type as perm_type',
                    'perm.hr_perm_date as perm_date',
                    'perm.hr_perm_id as perm_id',
                    'step.wf_step_name as step_name'
            )
            ->leftjoin('hr_perm as perm' , 'request.wf_request_row_id' , '=' , 'perm.hr_perm_id' )
            ->leftjoin('hr_wrdiah as wrdiah' , 'perm.hr_perm_workinterval_id' , '=' , 'wrdiah.hr_wrdiah_id' )
            ->leftjoin('wf_step as step' , 'request.wf_request_step_id' , '=' , 'step.wf_step_id' )
            ->where('request.wf_request_user_id' , $user_id)
            ->where('request.wf_request_table_name' , 'hr_perm')
            ->where('request.wf_request_created_by' , $user_id)
            ->whereIn('request.wf_request_send_status' , $statuses)
            ->orderByDesc('request.wf_request_id')
            ->get()
            ->map(function ($request) {
                $request->workflow = $this->getWorkflow($request);
                $request->perm_type = $this->getTranslate($request->perm_type);
                return $request;
            })
        ;

        return new Response([
            "error" => $requests->count() > 0 ? false : true,
            "data" => $requests
        ]);


    }

    public function leaves($user_id){
        $statuses = [Request::REQUEST_IS_NOT_SEND, Request::REQUEST_IS_UNDER_PROCESS, Request::REQUEST_REACH_END];

        $requests = DB::table('wf_request as request')
            ->select('request.wf_request_id as id' ,
                'request.wf_request_send_status as send_status' ,
                'request.wf_request_success as success' ,
                'lev.hr_lev_id as leave_id',
                'levst.hr_levst_name as leave_name',
                'lev.hr_lev_start_date as start_date',
                'lev.hr_lev_end_date as end_date',
                'lev.hr_lev_allowedleave_id as allowedleave_id',
                'lev.hr_lev_intrvl_lngth as length',
                'step.wf_step_name as step_name'
            )
            ->leftjoin('hr_lev as lev' , 'request.wf_request_row_id' , '=' , 'lev.hr_lev_id' )
            ->leftjoin('hr_levst as levst' , 'lev.hr_lev_levst_id' , '=' , 'levst.hr_levst_id' )
            ->leftjoin('wf_step as step' , 'request.wf_request_step_id' , '=' , 'step.wf_step_id' )
            ->where('request.wf_request_user_id' , $user_id)
            ->where('request.wf_request_table_name' , 'hr_lev')
            ->where('request.wf_request_created_by' , $user_id)
            ->whereIn('request.wf_request_send_status' , $statuses)
            ->orderByDesc('request.wf_request_id')
            ->get()
            ->map(function ($request) {
                $request->workflow = $this->getWorkflow($request);
                return $request;
            });

        return new Response([
            "error" => $requests->count() > 0 ? false : true,
            "data" => $requests
        ]);
    }

    public function assignments($user_id){
        $statuses = [Request::REQUEST_IS_NOT_SEND, Request::REQUEST_IS_UNDER_PROCESS, Request::REQUEST_REACH_END];

        $requests = DB::table('wf_request as request')
            ->select('request.wf_request_id as id' ,
                'request.wf_request_send_status as send_status' ,
                'request.wf_request_success as success' ,
                'wrdiah.hr_wrdiah_name as wrdiah_name',
                'assignment.hr_assignment_type as type',
                'assignment.hr_assignment_date as date',
                'assignment.hr_assignment_id as assignment_id',
                'user.sh_user_full_name as user_name',
                'step.wf_step_name as step_name'
            )
            ->leftjoin('hr_assignment as assignment' , 'request.wf_request_row_id' , '=' , 'assignment.hr_assignment_id' )
            ->leftjoin('hr_wrdiah as wrdiah' , 'assignment.hr_assignment_workinterval_id' , '=' , 'wrdiah.hr_wrdiah_id' )
            ->leftjoin('sh_user as user' , 'assignment.hr_assignment_user_id' , '=' , 'user.sh_user_id' )
            ->leftjoin('wf_step as step' , 'request.wf_request_step_id' , '=' , 'step.wf_step_id' )
            ->where('request.wf_request_table_name' , 'hr_assignment')
            ->where('request.wf_request_created_by' , $user_id)
            ->whereIn('request.wf_request_send_status' , $statuses)
            ->orderByDesc('request.wf_request_id')
            ->get()
            ->map(function ($request) {
                $request->workflow = $this->getWorkflow($request);
                $request->type = $this->getTranslate($request->type);
                return $request;
            });

        return new Response([
            "error" => $requests->count() > 0 ? false : true,
            "data" => $requests
        ]);
    }

    public function retreats_leave($user_id){
        $statuses = [Request::REQUEST_IS_NOT_SEND, Request::REQUEST_IS_UNDER_PROCESS, Request::REQUEST_REACH_END];

        $requests = DB::table('wf_request as request')
            ->select('request.wf_request_id as id' ,
                'request.wf_request_send_status as send_status' ,
                'request.wf_request_success as success' ,
                'levst.hr_levst_name as levst_name',
                'lev.hr_lev_start_date as start_date',
                'lev.hr_lev_end_date as end_date',
                'retreat.hr_retreat_leave_retreated_days as days',
                'retreat.hr_retreat_leave_created_date as created_date',
                'retreat.hr_retreat_leave_id as leave_id',
                'step.wf_step_name as step_name'
            )
            ->leftjoin('hr_retreat_leave as retreat' , 'request.wf_request_row_id' , '=' , 'retreat.hr_retreat_leave_id' )
            ->leftjoin('hr_lev as lev' , 'retreat.hr_retreat_leave_leave_request_id' , '=' , 'lev.hr_lev_id' )
            ->leftjoin('hr_levst as levst' , 'lev.hr_lev_levst_id' , '=' , 'levst.hr_levst_id' )
            ->leftjoin('wf_step as step' , 'request.wf_request_step_id' , '=' , 'step.wf_step_id' )
            ->where('request.wf_request_prg_id' , Program::PROGRAM_EMPLOYEE_DASHBOARD_P002)
            ->where('request.wf_request_table_name' , 'hr_retreat_leave')
            ->where('request.wf_request_created_by' , $user_id)
            ->whereIn('request.wf_request_send_status' , $statuses)
            ->orderByDesc('request.wf_request_id')
            ->get()
            ->map(function ($request) {
                $request->workflow = $this->getWorkflow($request);
                $request->days = count(json_decode($request->days));
                $time = strtotime($request->created_date);
                $request->created_date = date('Y-m-d',$time);
                return $request;
            })
        ;

        return new Response([
            "error" => $requests->count() > 0 ? false : true,
            "data" => $requests
        ]);
    }

    public function leave_credits($user_id){
        $statuses = [Request::REQUEST_IS_NOT_SEND, Request::REQUEST_IS_UNDER_PROCESS, Request::REQUEST_REACH_END];

        $requests = DB::table('wf_request as request')
            ->select('request.wf_request_id as id' ,
                'request.wf_request_send_status as send_status' ,
                'request.wf_request_success as success' ,
                'levst.hr_levst_name as levst_name',
                'credit.hr_creditedit_credit as credit',
                'credit.hr_creditedit_id as credit_id',
                'credit.hr_creditedit_user_id as user_id',
                'credit.hr_creditedit_take_effect_date as take_effect_date',
                'step.wf_step_name as step_name'
            )
            ->leftjoin('hr_creditedit as credit' , 'request.wf_request_row_id' , '=' , 'credit.hr_creditedit_id' )
            ->leftjoin('hr_levst as levst' , 'credit.hr_creditedit_leave_id' , '=' , 'levst.hr_levst_id' )
            ->leftjoin('wf_step as step' , 'request.wf_request_step_id' , '=' , 'step.wf_step_id' )
            ->where('request.wf_request_prg_id' , Program::PROGRAM_EMPLOYEE_DASHBOARD_P002)
            ->where('request.wf_request_table_name' , 'hr_creditedit')
            ->where('request.wf_request_created_by' , $user_id)
            ->whereIn('request.wf_request_send_status' , $statuses)
            ->orderByDesc('request.wf_request_id')
            ->get()
            ->map(function ($request) {
                $request->workflow = $this->getWorkflow($request);
                $time = strtotime($request->created_date);
                $request->created_date = date('Y-m-d',$time);
                return $request;
            })
        ;

        return new Response([
            "error" => $requests->count() > 0 ? false : true,
            "data" => $requests
        ]);
    }

    public function advance_request($user_id) {
        $statuses = [Request::REQUEST_IS_NOT_SEND, Request::REQUEST_IS_UNDER_PROCESS, Request::REQUEST_REACH_END];

        $requests = DB::table('wf_request as request')
            ->select('request.wf_request_id as id' ,
                'request.wf_request_send_status as send_status' ,
                'request.wf_request_success as success' ,
                'advance.hr_advancerequest_amount as amount',
                'advance.hr_advancerequest_id as advance_id',
                'advance.hr_advancerequest_months_num as months',
                'advance.hr_advancerequest_starting_date as starting_date',
                'step.wf_step_name as step_name'
            )
            ->leftjoin('hr_advancerequest as advance' , 'request.wf_request_row_id' , '=' , 'advance.hr_advancerequest_id' )
//            ->leftjoin('hr_levst as levst' , 'credit.hr_creditedit_leave_id' , '=' , 'levst.hr_levst_id' )
            ->leftjoin('wf_step as step' , 'request.wf_request_step_id' , '=' , 'step.wf_step_id' )
            ->where('request.wf_request_prg_id' , Program::PROGRAM_EMPLOYEE_DASHBOARD_P002)
            ->where('request.wf_request_table_name' , 'hr_advancerequest')
            ->where('request.wf_request_created_by' , $user_id)
            ->whereIn('request.wf_request_send_status' , $statuses)
            ->orderByDesc('request.wf_request_id')
            ->get()
            ->map(function ($request) {
                $request->workflow = $this->getWorkflow($request);
                $time = strtotime($request->starting_date);
                $request->starting_date = date('Y-m-d',$time);
                return $request;
            })
        ;

        return new Response([
            "error" => $requests->count() > 0 ? false : true,
            "data" => $requests
        ]);
    }

    public function outwork($user_id) {
        $statuses = [Request::REQUEST_IS_NOT_SEND, Request::REQUEST_IS_UNDER_PROCESS, Request::REQUEST_REACH_END];

        $requests = DB::table('wf_request as request')
            ->select('request.wf_request_id as id' ,
                'request.wf_request_send_status as send_status' ,
                'request.wf_request_success as success' ,
                'outwork.hr_otwrk_id as outwork_id',
                'outwork.hr_otwrk_date as date',
                'outwork.hr_otwrk_end_date as end_date',
                'outwork.hr_otwrk_rate as rate',
                'user.sh_user_full_name as full_name',
                'step.wf_step_name as step_name'
            )
            ->leftjoin('hr_otwrk as outwork' , 'request.wf_request_row_id' , '=' , 'outwork.hr_otwrk_id' )
            ->leftjoin('sh_user as user' , 'user.sh_user_id' , '=' , 'outwork.hr_otwrk_user_id' )
            ->leftjoin('wf_step as step' , 'request.wf_request_step_id' , '=' , 'step.wf_step_id' )
            ->where('request.wf_request_table_name' , 'hr_otwrk')
            ->where('request.wf_request_created_by' , $user_id)
            ->whereIn('request.wf_request_send_status' , $statuses)
            ->orderByDesc('request.wf_request_id')
            ->get()
            ->map(function ($request) {
                $request->workflow = $this->getWorkflow($request);
                $time = strtotime($request->starting_date);
                $request->starting_date = date('Y-m-d',$time);
                return $request;
            })
        ;

        return new Response([
            "error" => $requests->count() > 0 ? false : true,
            "data" => $requests
        ]);
    }

    public function outwork_fees($user_id) {
        $statuses = [Request::REQUEST_IS_NOT_SEND, Request::REQUEST_IS_UNDER_PROCESS, Request::REQUEST_REACH_END];

        $requests = DB::table('wf_request as request')
            ->select('request.wf_request_id as id' ,
                'request.wf_request_send_status as send_status' ,
                'request.wf_request_success as success' ,
                'outwork.hr_otwrk_id as outwork_id',
                'outwork.hr_otwrk_date as date',
                'outwork.hr_otwrk_end_date as end_date',
                'outwork.hr_otwrk_rate as rate',
                'outwork_fees.hr_outworkfees_id as outworkfees_id',
                'user.sh_user_full_name as full_name',
                'step.wf_step_name as step_name'
            )
            ->leftjoin('hr_outworkfees as outwork_fees' , 'request.wf_request_row_id' , '=' , 'outwork_fees.hr_outworkfees_id')
            ->leftjoin('hr_otwrk as outwork' , 'outwork_fees.hr_outworkfees_outwork_id' , '=' , 'outwork.hr_otwrk_id')
            ->leftjoin('sh_user as user' , 'user.sh_user_id' , '=' , 'outwork_fees.hr_outworkfees_user_id' )
            ->leftjoin('wf_step as step' , 'request.wf_request_step_id' , '=' , 'step.wf_step_id' )
            ->where('request.wf_request_table_name' , 'hr_outworkfees')
            ->where('request.wf_request_created_by' , $user_id)
            ->whereIn('request.wf_request_send_status' , $statuses)
            ->orderByDesc('request.wf_request_id')
            ->get()
            ->map(function ($request) {
                $request->workflow = $this->getWorkflow($request);
                $time = strtotime($request->starting_date);
                $request->starting_date = date('Y-m-d',$time);
                return $request;
            })
        ;

        return new Response([
            "error" => $requests->count() > 0 ? false : true,
            "data" => $requests
        ]);
    }

    public function mandate($user_id) {
        $statuses = [Request::REQUEST_IS_NOT_SEND, Request::REQUEST_IS_UNDER_PROCESS, Request::REQUEST_REACH_END];

        $requests = DB::table('wf_request as request')
            ->select('request.wf_request_id as id' ,
                'request.wf_request_send_status as send_status' ,
                'request.wf_request_success as success' ,
                'mandate.hr_mndt_id as mandate_id',
                'mandate.hr_mndt_city as city',
                'mandate.hr_mndt_days_number as days',
                'mandate.hr_mndt_start_date as start_date',
                'mandate.hr_mndt_end_date as end_date',
                'mandate.hr_mndt_reason as reason',
                'user.sh_user_full_name as full_name',
                'step.wf_step_name as step_name'
            )
            ->leftjoin('hr_mndt as mandate' , 'request.wf_request_row_id' , '=' , 'mandate.hr_mndt_id')
            ->leftjoin('sh_user as user' , 'user.sh_user_id' , '=' , 'mandate.hr_mndt_user_id' )
            ->leftjoin('wf_step as step' , 'request.wf_request_step_id' , '=' , 'step.wf_step_id' )
            ->where('request.wf_request_table_name' , 'hr_mndt')
            ->where('request.wf_request_created_by' , $user_id)
            ->whereIn('request.wf_request_send_status' , $statuses)
            ->orderByDesc('request.wf_request_id')
            ->get()
            ->map(function ($request) {
                $request->workflow = $this->getWorkflow($request);
                $time = strtotime($request->starting_date);
                $request->starting_date = date('Y-m-d',$time);
                return $request;
            })
        ;

        return new Response([
            "error" => $requests->count() > 0 ? false : true,
            "data" => $requests
        ]);
    }

    public function mandate_fees($user_id) {
        $statuses = [Request::REQUEST_IS_NOT_SEND, Request::REQUEST_IS_UNDER_PROCESS, Request::REQUEST_REACH_END];

        $requests = DB::table('wf_request as request')
            ->select('request.wf_request_id as id' ,
                'request.wf_request_send_status as send_status' ,
                'request.wf_request_success as success' ,
                'mandate_fees.hr_mndtfees_id as fees_id',
                'mandate.hr_mndt_id as mandate_id',
                'mandate.hr_mndt_city as city',
                'mandate.hr_mndt_days_number as days',
                'mandate.hr_mndt_start_date as start_date',
                'mandate.hr_mndt_end_date as end_date',
                'mandate.hr_mndt_reason as reason',
                'user.sh_user_full_name as full_name',
                'step.wf_step_name as step_name'
            )
            ->leftjoin('hr_mndtfees as mandate_fees' , 'request.wf_request_row_id' , '=' , 'mandate_fees.hr_mndtfees_id')
            ->leftjoin('hr_mndt as mandate' , 'mandate_fees.hr_mndtfees_mndt_id' , '=' , 'mandate.hr_mndt_id')
            ->leftjoin('sh_user as user' , 'user.sh_user_id' , '=' , 'mandate.hr_mndt_user_id' )
            ->leftjoin('wf_step as step' , 'request.wf_request_step_id' , '=' , 'step.wf_step_id' )
            ->where('request.wf_request_table_name' , 'hr_mndtfees')
            ->where('request.wf_request_created_by' , $user_id)
            ->whereIn('request.wf_request_send_status' , $statuses)
            ->orderByDesc('request.wf_request_id')
            ->get()
            ->map(function ($request) {
                $request->workflow = $this->getWorkflow($request);
                $time = strtotime($request->starting_date);
                $request->starting_date = date('Y-m-d',$time);
                return $request;
            })
        ;

        return new Response([
            "error" => $requests->count() > 0 ? false : true,
            "data" => $requests
        ]);
    }

    public function exchange($user_id) {
        $statuses = [Request::REQUEST_IS_NOT_SEND, Request::REQUEST_IS_UNDER_PROCESS, Request::REQUEST_REACH_END];

        $requests = DB::table('wf_request as request')
            ->select('request.wf_request_id as id' ,
                'request.wf_request_send_status as send_status' ,
                'request.wf_request_success as success' ,
                'request.wf_request_created_date as created_date' ,
                'exchange.fin_exch_id as exchange_id',
                'exchange.fin_exch_beneficiary as beneficiary',
                'exchange.fin_exch_amount_number as amount_number',
                'step.wf_step_name as step_name'
            )
            ->leftjoin('fin_exch as exchange' , 'request.wf_request_row_id' , '=' , 'exchange.fin_exch_id')
            ->leftjoin('wf_step as step' , 'request.wf_request_step_id' , '=' , 'step.wf_step_id' )
            ->where('request.wf_request_table_name' , 'fin_exch')
            ->where('request.wf_request_prg_id' , Program::PROGRAM_EMPLOYEE_DASHBOARD_P002)
            ->where('request.wf_request_created_by' , $user_id)
            ->whereIn('request.wf_request_send_status' , $statuses)
            ->orderByDesc('request.wf_request_id')
            ->get()
            ->map(function ($request) {
                $request->workflow = $this->getWorkflow($request);
                $time = strtotime($request->created_date);
                $request->created_date = date('Y-m-d',$time);
                return $request;
            })
        ;


        return new Response([
            "error" => $requests->count() > 0 ? false : true,
            "data" => $requests,
            "permission" => [
                'view' => can('view','finexchreq'),
            ]
        ]);
    }

    public function getWorkflow($request){
        global $snso;
        $workFlowString = '';
        $style = 'class="btn btn-info shiny"';
        $hideStatus = false;

        switch ($request->send_status) {

            case Request::REQUEST_IS_NOT_SEND:

                if ($hideStatus == false) {
                    $workFlowString .= Translation::translate(null, 'NotSent');
                }

                $workFlowString .= "&nbsp;<a href='" . $snso->URL->getURL("gnr/X000/wfrequest/send/0/ar/save_session/{$request->id}/usr/P002/employeeRequestsDashboard/show/0/ar") . "' {$style}>" . Translation::translate(null, 'send') . "</a>";
                break;

            case Request::REQUEST_IS_UNDER_PROCESS:


                if ($hideStatus == false && $request->step_id) {
                    $workFlowString .= $request->step_name;
                }

                $workFlowString .= "&nbsp;<a href='" . $snso->URL->getURL("gnr/X000/wfrequest/browse/0/ar/{$request->id}") . "' {$style} data-toggle='modal' data-target='#modal'> " . Translation::translate(null, 'browse') . "</a>";
                break;

            case Request::REQUEST_REACH_END:
            case Request::REQUEST_IS_ARCHIVED:



                if ($hideStatus == false && $request->step_id) {
                    $workFlowString .= $request->step_name;
                }
                $request_success =  Translation::translate(null, 'gnr_request_rejected');
                if ($request->success == 1) {
                    $request_success =  Translation::translate(null, 'gnr_request_accepted');
                }

                $workFlowString .= " &nbsp; ". $request_success . " &nbsp;&nbsp;<a href='" . $snso->URL->getURL("gnr/X000/wfrequest/browse/0/ar/{$request->id}") . "' {$style} data-toggle='modal' data-target='#modal'> " . Translation::translate(null, 'browse') . "</a>";
                break;


            default:
                break;

        }

        return $workFlowString;
    }

    public function getTranslate($id){
        return ($_SESSION['translation'][$id] ? $_SESSION['translation'][$id] : $_SESSION['translation']['gnr_not_exist']);
    }

}