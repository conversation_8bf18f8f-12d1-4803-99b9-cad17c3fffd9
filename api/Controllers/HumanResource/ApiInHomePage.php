<?php

namespace Api\Controllers\HumanResource;

use Api\Controllers\Controller;
use ClientList;
use Illuminate\Container\Container;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Pagination\Paginator;
use LeaveAllowed;
use Models\Hint;
use Models\HumanResource\AllowedLeave;
use Models\HumanResource\Attendance;
use Models\HumanResource\CommitteeMember;
use Models\HumanResource\DeductAdditionRequest;
use Models\HumanResource\Latency;
use Models\HumanResource\LeaveScheduled;
use Models\HumanResource\PayrollTransaction;
use Models\HumanResource\Vacant;
use Models\MediaCenter;
use Models\Planning\Project;
use Models\UserLog;
use PayrollTrans;
use Vacant as VacantResource;

class ApiInHomePage extends Controller
{

    public function getUserVacancies($user_id){


        $vacancies = Vacant::with([
            'job' => function($query){
                $query->select('sh_job_name' , 'sh_job_id');
            }
        ])
            ->where(VacantResource::USER_ID , $user_id)
            ->where(VacantResource::B_TYPE , 2)
            ->where(VacantResource::B_ACCEPTANCE , 1)
            ->where(VacantResource::QUIT , 0)
            ->where(VacantResource::DELETED , 0)
            ->orderBy(VacantResource::BASIC, 'DESC')

            ->get();
        ;

        $vacancies = $vacancies->map(function($vacancy) {
            return [
                'job_id' => $vacancy->job->sh_job_id,
                'job_name' => $vacancy->job->sh_job_name
            ];
        });



        return new Response([
            "error" => $vacancies->count() > 0 ? false : true,
            "data" => $vacancies
        ]);
    }

    public function getHint()
    {
        $hint=Hint::inRandomOrder()->limit(1)->first();
        return new Response([
            "error" => $hint->count() >0 ? false : true,
            "data" => $hint
        ]);
    }

    public function getNews(){
        $news = MediaCenter::published()->news()
            ->select(['sh_mediacenter_id' , 'sh_mediacenter_name' , 'sh_mediacenter_text' , 'sh_mediacenter_created_date'])
            ->limit($this->maxOnDashboard())
            ->get();

        return new Response([
            "error" => $news->count() >0 ? false : true,
            "data" => $news
        ]);
    }

    public function getAds(){
        $news = MediaCenter::published()->ads()
            ->select(['sh_mediacenter_id' , 'sh_mediacenter_name' , 'sh_mediacenter_text' , 'sh_mediacenter_created_date'])
            ->limit($this->maxOnDashboard())
            ->get();

        return new Response([
            "error" => $news->count() >0 ? false : true,
            "data" => $news
        ]);
    }

    public function getUserProjects(Request $request , $user_id)
    {

        $page = $request->page ?? 1;
        $limit = 5;
        ////////// Get Project in Logger
        $logs = UserLog::where('log_type', Project::class)
            ->where('causer_id', $user_id)
            ->orderBy('id', 'desc')
            ->get(['description']);

        $logs = $logs->groupBy('description.project_id')->keys();

        ////////// If There Was Project Logger
//        if ($logs->count() > 0) {
//
//            $projects_all = Project::select([
//                \Project::ID, \Project::NAME
//            ])->whereIn(\Project::ID, $logs)
//                ->paginate($limit, ['*'], 'page', $page);
//
//            $projects = collect($projects_all->items());
//            $count = $logs->count();
//
//            $projects = $projects->map(function ($project) {
//                return [
//                    'id' => $project->id,
//                    'name' => $project->name
//                ];
//            });
//
//            $data = Container::getInstance()->makeWith(\Illuminate\Pagination\LengthAwarePaginator::class, [
//                "items" => $projects,
//                "total" => $count,
//                "perPage" => $limit,
//                "currentPage" => $page,
//                "options" => [
//                    'path' => $request->url(),
//                    'pageName' => 'page'
//                ]
//            ]);
//
//            return new Response([
//                "error" => $projects->count() > 0 ? false : true,
//                "data" => $data,
//            ]);
//        }
//        ////////// If Doesn't Have Project Logger Get All Projects Where User is Member In
//        else{
//        return $logs;
            $projects_all = Project::getProjectsByUser($user_id , $limit , $page);

            $projects = collect($projects_all["projects"]);
//            return $projects;
        $lodgedProjects = $projects->whereIn('pm_project_id' , $logs)->flatten(1);
//        return $lodgedProjects;
        $sorted = collect($logs)->map(function($log) use($lodgedProjects) {
            return $lodgedProjects->where('pm_project_id', $log)->first();
        })->where('pm_project_id' , '<>' , null)->flatten(1);
//        return $sorted;

        $projects = $sorted->merge($projects)->unique()->flatten();
//        return $sorted;
//            $test = $projects->whereNotIn('pm_project_id' , $sorted->get('pm_project_id'));
//            return $test;
//            $projects
            $count = $projects_all["count"];

            $projects = $projects->map(function($project) {
                return [
                    'id' => $project->id,
                    'name' => $project->name
                ];
            });

            $data =  Container::getInstance()->makeWith(\Illuminate\Pagination\LengthAwarePaginator::class , [
                "items" => $projects,
                "total" => $count,
                "perPage" => $limit,
                "currentPage" => $page,
                "options" => [
                    'path' => $request->url(),
                    'pageName' => 'page'
                ]
            ]);

            return new Response([
                "error" => $projects->count() >0 ? false : true,
                "data" => $data,
            ]);
//        }

    }

    public function getLeaves($user_id){
        $leaves = AllowedLeave::with('leave')
            ->where(LeaveAllowed::USER_ID , $user_id)
            ->get();

        $leaves = $leaves->map(function($leave) {
            return [
                'id' => $leave->id,
                'name' => $leave->leave->name
            ];
        });

        return new Response([
            "error" => $leaves->count() >0 ? false : true,
            "data" => $leaves
        ]);
    }

    public function getPayslip($user_id){
        $transactions = PayrollTransaction::with('batch')
            ->where(PayrollTrans::USER_ID , $user_id)
            ->where(PayrollTrans::CONFIRM_STATUS , PayrollTrans::PAYSLIP_CONFIRMED)
            ->select(['prl_trans_id' ,
                'prl_trans_batch_id' ,
                'prl_trans_basic_salary' ,
                'prl_trans_allowances' ,
                'prl_trans_deductions' ,
                'prl_trans_net',
                'prl_trans_uao_id',
                'prl_trans_user_id'
            ])
            ->get();

        $transactions = $transactions->map(function($transaction) {
            return [
                'id' => $transaction->prl_trans_id,
                'name' => $transaction->batch->prl_batches_name,
                'basic_salary' => $transaction->prl_trans_basic_salary,
                'allowances' => $transaction->prl_trans_allowances,
                'deductions' => $transaction->prl_trans_deductions,
                'net' => $transaction->prl_trans_net,
                'uao_id' => $transaction->prl_trans_uao_id,
                'user_id' => $transaction->prl_trans_user_id,
            ];
        });

        return new Response([
            "error" => $transactions->count() >0 ? false : true,
            "data" => $transactions
        ]);
    }

    public function getAttendance($user_id){
        $list = Attendance::select(
            'hr_attendance_start_date' ,
            'hr_attendance_end_date' ,
            'hr_attendance_duration' ,
            'hr_attendance_id' ,
            'hr_attendance_manipulation_type' ,
            'hr_attendance_manipulation_trans_id',
            'hr_attendance_manipulation_leave_id'
        )->where('hr_attendance_user_id' , $user_id)
            ->get();

        $list = $list->map(function($attendance) {
            return [
                'id' => $attendance->hr_attendance_id,
                'start_date' => $attendance->hr_attendance_start_date,
                'end_date' => $attendance->hr_attendance_end_date,
                'duration' => $attendance->hr_attendance_duration,
                'manipulation_type' => $attendance->hr_attendance_manipulation_type,
                'trans_id' => $attendance->hr_attendance_manipulation_trans_id,
                'leave_id' => $attendance->hr_attendance_manipulation_leave_id,

            ];
        });

        return new Response([
            "error" => $list->count() >0 ? false : true,
            "data" => $list
        ]);
    }

    public function getLatency($user_id){
        $list = Latency::select(
            'hr_latency_start_date' ,
            'hr_latency_end_date' ,
            'hr_latency_duration' ,
            'hr_latency_id' ,
            'hr_latency_manipulation_type' ,
            'hr_latency_manipulation_trans_id' ,
            'hr_latency_manipulation_leave_id'
        )
            ->where('hr_latency_user_id' , $user_id)
            ->get();




        $list = $list->map(function($attendance) {
            return [
                'id' => $attendance->hr_latency_id,
                'start_date' => $attendance->hr_latency_start_date,
                'end_date' => $attendance->hr_latency_end_date,
                'duration' => $attendance->hr_latency_duration,
                'manipulation_type' => $attendance->hr_latency_manipulation_type,
                'trans_id' => $attendance->hr_latency_manipulation_trans_id,
                'leave_id' => $attendance->hr_latency_manipulation_leave_id,

            ];
        });

        return new Response([
            "error" => $list->count() >0 ? false : true,
            "data" => $list
        ]);
    }

    public function getBasicJob($user_id){
        $vacents = Vacant::with(
            [
                'job' => function($query) {
                    $query->select(\Job::ID ,\Job::NAME);
                }
            ],
            [
                'doam' => function($query) {
                    $query->select('hr_doam_id', 'hr_doam_name');
                }
           ]
        )
            ->select('sh_uao_id' , 'sh_uao_job_id' , 'sh_uao_att_doam_id')
            ->basicJobs()
            ->where('sh_uao_user_id' , $user_id)
            ->get();

        $vacents = $vacents->map(function($vacent) {
            return [
                'id' => $vacent->id,
                'job_id' => $vacent->job_id,
                'job_name' => $vacent->job->name,
                'shift_id' => $vacent->doam->id,
                'shift_name' => $vacent->doam->name,
            ];
        });

        return new Response([
            "error" => $vacents->count() >0 ? false : true,
            "data" => $vacents
        ]);
    }

    public function getLeaveScheduled(){
        $sheduleds = LeaveScheduled::with([
            'type'=> function($query) {
                $query->select('hr_levst_id' , 'hr_levst_name');
            }])->circulateStatus(1)
            ->select('hr_schedholid_leav_id' , 'hr_schedholid_start_date' , 'hr_schedholid_end_date')
            ->get();

        $sheduleds = $sheduleds->map(function($sheduled) {
            return [
                'start_date' => $sheduled->hr_schedholid_start_date,
                'end_date' => $sheduled->hr_schedholid_end_date,
                'name' => $sheduled->type->hr_levst_name,
            ];
        });

        return new Response([
            "error" => $sheduleds->count() >0 ? false : true,
            "data" => $sheduleds
        ]);
    }

    public function getCommittees($user_id){
        $committees =CommitteeMember::with([
            'committee' => function($query){
                $query->select('es_committee_name' , 'es_committee_id' , 'es_committee_purpose');
            } ])
            ->select('es_committeemember_committee_id')
            ->userId($user_id)
            ->get()
        ;

        $committees = $committees->map(function($committee) {
            return [
                'id' => $committee->committee->es_committee_id,
                'name' => $committee->committee->es_committee_name,
                'purpose' => $committee->committee->es_committee_purpose,
            ];
        });


        return new Response([
            "error" => $committees->count() >0 ? false : true,
            "data" => $committees
        ]);
    }

    public function getDeductAddition($user_id){
        $requests =DeductAdditionRequest::with([
            'setting' => function($query){
                $query->select('st_setting_id' );
                },
            'batch' => function($query){
                $query->select('prl_batches_name' );
            },
            ])
            ->select('hr_deductaddition_id', 'hr_deductaddition_type' , 'hr_deductaddition_amount' , 'hr_deductaddition_manipulation_batche_id' , 'hr_deductaddition_request_success')
            ->userId($user_id)
            ->get()
        ;

        $requests = $requests->map(function($request) {
            return [
                'id' => $request->hr_deductaddition_id,
                'type_id' => $request->hr_deductaddition_type,
                'amount' => $request->hr_deductaddition_amount,
                'success' => $request->hr_deductaddition_request_success,
                'batch_name' => $request->batch->prl_batches_name,
                'type_name' => $request->setting->name,

            ];
        });


        return new Response([
            "error" => $requests->count() >0 ? false : true,
            "data" => $requests
        ]);
    }

    public function maxOnDashboard(){

            $maxAdsOnDashboard = \Models\ClientList::getMax()
                ->select('sh_clientlist_name')
                ->take(1)
                ->get()
                ->first();
        return $maxAdsOnDashboard->sh_clientlist_name;
    }
}