<?php

namespace Api\Controllers;

use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Models\ActivitySubscription;
use Models\ClubActivity;
use Models\ClubActivityGroup;

class ClubSubscriptionController extends Controller
{
    public function __construct()
    {

    }

    public function approve(ActivitySubscription $activitySubscription)
    {
        $status = $activitySubscription->update([
            'approved' => 1
        ]);

        return response($status);
    }

    public function getBeneficiaries()
    {
        $beneficiaries = DB::table('sh_user')
            ->join('club_beneficiaries as beneficiaries', 'sh_user.sh_user_id', '=', 'beneficiaries.user_id')
            ->join('club_categories as categories', 'beneficiaries.club_category_id', '=', 'categories.id')
            ->select('beneficiaries.id as id', 'sh_user_full_name as name', 'sh_user.sh_user_birth_date as birth_date', 'categories.name as category', 'categories.id as category_id')
            ->get();

        $beneficiaries->map(function ($el) {
            $el->age = Carbon::parse($el->birth_date)->age;
        });

        return response($beneficiaries);
    }

    public function getUserData($user_id)
    {
        $beneficiary = DB::table('sh_user')
            ->join('club_beneficiaries as beneficiaries', 'sh_user.sh_user_id', '=', 'beneficiaries.user_id')
            ->join('club_categories as categories', 'beneficiaries.club_category_id', '=', 'categories.id')
            ->select('sh_user_id as id', 'sh_user_full_name as name', 'sh_user.sh_user_birth_date as birth_date', 'categories.name as category', 'categories.id as category_id')
            ->where('id', $user_id)
            ->first();

        $beneficiary->map(function ($el) {
            $el->age = Carbon::parse($el->birth_date)->age;
        });

        return response($beneficiary);
    }

    public function getActivitiesByCategoryId(Request $request, $category_id)
    {

        $activities = ClubActivity::with(['categories', 'fee', 'subscriptions'])->whereHas('categories', function ($query) use ($category_id) {
            $query->where('category_id', $category_id);
        })->whereHas('groups')->get();


        $activities->map(function ($activity) use ($request){
            $subscribers = collect($activity->subscriptions->pluck('beneficiary_id'));
            $activity->subscribed = $subscribers->contains($request->beneficiary_id);
        });

        $activities->map(function ($activity) {
            $activity->offsetUnset('subscriptions');
        });

        return response($activities);
    }

    public function getGroupsByActivityId($activity_id)
    {
        $groups = ClubActivityGroup::withCount('subscriptions')->where('activity_id', $activity_id)->whereDate('end_date', '>', Carbon::now())->get();

        return response($groups);
    }

}
