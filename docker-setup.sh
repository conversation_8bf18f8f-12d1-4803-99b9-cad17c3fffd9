#!/bin/bash

# SNSO ERP Docker Setup Script
# This script sets up the Docker environment for the legacy SNSO ERP system

set -e

echo "🚀 Setting up SNSO ERP Docker Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_status "Docker and Docker Compose are installed ✓"

# Create necessary directories
print_status "Creating necessary directories..."

directories=(
    "docker/logs/apache"
    "docker/logs/php"
    "docker/logs/mysql"
    "cache/smarty"
    "logs"
    "uploads"
    "public/templates/assets/resources/dist"
)

for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        print_status "Created directory: $dir"
    fi
done

# Set proper permissions
print_status "Setting proper permissions..."
chmod -R 755 docker/
chmod -R 777 cache/
chmod -R 777 logs/
chmod -R 777 uploads/

# Create environment file if it doesn't exist
if [ ! -f ".env" ]; then
    print_status "Creating .env file..."
    cat > .env << EOF
# SNSO ERP Environment Configuration

# Application
APP_NAME="SNSO ERP"
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:8080

# Database
DB_CONNECTION=mysql
DB_HOST=snso-db
DB_PORT=3306
DB_DATABASE=snso_erp
DB_USERNAME=snso_user
DB_PASSWORD=snso_password
DB_ROOT_PASSWORD=snso_root_password

# Redis
REDIS_HOST=snso-redis
REDIS_PASSWORD=snso_redis_password
REDIS_PORT=6379

# Mail
MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

# Session
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Cache
CACHE_DRIVER=redis

# Timezone
APP_TIMEZONE=Asia/Riyadh

# Locale
APP_LOCALE=ar
APP_FALLBACK_LOCALE=en
EOF
    print_success "Created .env file"
else
    print_warning ".env file already exists, skipping creation"
fi

# Check if package.json exists and install dependencies
if [ -f "package.json" ]; then
    print_status "Installing Node.js dependencies..."
    if command -v npm &> /dev/null; then
        npm install
        print_success "Node.js dependencies installed"
    else
        print_warning "npm not found locally, dependencies will be installed in container"
    fi
else
    print_warning "package.json not found, skipping Node.js dependencies"
fi

# Check if composer.json exists in framework directory
if [ -f "framework/composer.json" ]; then
    print_status "PHP dependencies found in framework directory"
    print_warning "PHP dependencies will be installed in container during build"
else
    print_warning "No composer.json found in framework directory"
fi

# Build and start containers
print_status "Building Docker containers..."
docker-compose build --no-cache

print_status "Starting Docker containers..."
docker-compose up -d

# Wait for database to be ready
print_status "Waiting for database to be ready..."
sleep 30

# Check if containers are running
print_status "Checking container status..."
if docker-compose ps | grep -q "Up"; then
    print_success "Containers are running!"
else
    print_error "Some containers failed to start. Check logs with: docker-compose logs"
    exit 1
fi

# Display access information
echo ""
echo "🎉 SNSO ERP Docker Environment Setup Complete!"
echo ""
echo "📋 Access Information:"
echo "   🌐 Application: http://localhost:8080"
echo "   🗄️  PhpMyAdmin: http://localhost:8081"
echo "   📊 Database: localhost:3306"
echo "   🔴 Redis: localhost:6379"
echo ""
echo "🔑 Database Credentials:"
echo "   Database: snso_erp"
echo "   Username: snso_user"
echo "   Password: snso_password"
echo "   Root Password: snso_root_password"
echo ""
echo "🔧 Useful Commands:"
echo "   View logs: docker-compose logs -f"
echo "   Stop containers: docker-compose down"
echo "   Restart containers: docker-compose restart"
echo "   Access app container: docker-compose exec snso-app bash"
echo "   Access database: docker-compose exec snso-db mysql -u snso_user -p snso_erp"
echo ""
echo "📝 Notes:"
echo "   - The application files are mounted as volumes for development"
echo "   - Database data is persisted in Docker volumes"
echo "   - Check docker-compose logs if you encounter any issues"
echo ""

# Final health check
print_status "Performing health check..."
sleep 5

if curl -f http://localhost:8080 >/dev/null 2>&1; then
    print_success "Application is responding on http://localhost:8080"
else
    print_warning "Application might still be starting up. Please wait a moment and try accessing http://localhost:8080"
fi

print_success "Setup completed successfully! 🚀"
