# SNSO ERP Docker Setup

This Docker configuration provides a complete development environment for the legacy SNSO ERP system, maintaining compatibility with the original technology stack.

## 🏗️ Architecture

The Docker setup includes the following services:

- **snso-app**: PHP 7.0 + Apache + Node.js 10 application container
- **snso-db**: MySQL 5.5 database with Arabic/UTF-8 support
- **snso-redis**: Redis 5.0 for caching and sessions
- **snso-phpmyadmin**: PhpMyAdmin 4.9 for database management
- **snso-node**: Node.js 10 container for frontend asset compilation

## 🚀 Quick Start

### Prerequisites

- Docker Engine 20.10+
- Docker Compose 1.29+
- At least 4GB RAM available for containers
- At least 10GB disk space

### Setup

1. **<PERSON><PERSON> and navigate to the project:**
   ```bash
   cd /path/to/snso-erp
   ```

2. **Run the setup script:**
   ```bash
   chmod +x docker-setup.sh
   ./docker-setup.sh
   ```

3. **Access the application:**
   - Application: http://localhost:8080
   - PhpMyAdmin: http://localhost:8081
   - Database: localhost:3306

### Manual Setup (Alternative)

If you prefer manual setup:

```bash
# Create necessary directories
mkdir -p docker/logs/{apache,php,mysql}
mkdir -p cache/smarty logs uploads

# Set permissions
chmod -R 777 cache/ logs/ uploads/

# Build and start containers
docker-compose build
docker-compose up -d

# Check status
docker-compose ps
```

## 📋 Container Details

### Application Container (snso-app)
- **Base Image**: php:7.0-apache
- **PHP Version**: 7.0 (legacy compatibility)
- **Node.js Version**: 10.x (for Vue.js 2 and webpack)
- **Extensions**: mysqli, pdo_mysql, mbstring, xml, zip, gd, curl, json, intl, mcrypt, opcache
- **Composer**: 1.10.26 (compatible with PHP 7.0)

### Database Container (snso-db)
- **Base Image**: mysql:5.5
- **Character Set**: utf8mb4 (Arabic support)
- **Collation**: utf8mb4_unicode_ci
- **Configuration**: Optimized for ERP workloads

### Redis Container (snso-redis)
- **Base Image**: redis:5.0-alpine
- **Configuration**: Optimized for session storage and caching
- **Memory Limit**: 256MB
- **Persistence**: AOF enabled

## 🔧 Configuration

### Environment Variables

The setup creates a `.env` file with the following key variables:

```env
# Database
DB_HOST=snso-db
DB_DATABASE=snso_erp
DB_USERNAME=snso_user
DB_PASSWORD=snso_password

# Redis
REDIS_HOST=snso-redis
REDIS_PASSWORD=snso_redis_password

# Application
APP_URL=http://localhost:8080
APP_TIMEZONE=Asia/Riyadh
APP_LOCALE=ar
```

### Volume Mounts

- **Application Code**: `.:/var/www/html` (live reload for development)
- **Database Data**: `snso_db_data:/var/lib/mysql` (persistent)
- **Redis Data**: `snso_redis_data:/data` (persistent)
- **Node Modules**: `snso_node_modules:/app/node_modules` (cached)

## 🛠️ Development Workflow

### Frontend Development

The Node.js container automatically runs `npm run watch` for asset compilation:

```bash
# View frontend build logs
docker-compose logs -f snso-node

# Manually run frontend commands
docker-compose exec snso-node npm run dev
docker-compose exec snso-node npm run production
```

### Backend Development

PHP files are mounted as volumes, so changes are reflected immediately:

```bash
# Access application container
docker-compose exec snso-app bash

# View application logs
docker-compose logs -f snso-app

# Restart PHP/Apache
docker-compose restart snso-app
```

### Database Management

```bash
# Access MySQL CLI
docker-compose exec snso-db mysql -u snso_user -p snso_erp

# Access as root
docker-compose exec snso-db mysql -u root -p

# Backup database
docker-compose exec snso-db mysqldump -u root -p snso_erp > backup.sql

# Restore database
docker-compose exec -T snso-db mysql -u root -p snso_erp < backup.sql
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check if ports are in use
   netstat -tulpn | grep :8080
   netstat -tulpn | grep :3306
   
   # Modify ports in docker-compose.yml if needed
   ```

2. **Permission Issues**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   chmod -R 755 docker/
   chmod -R 777 cache/ logs/ uploads/
   ```

3. **Database Connection Issues**
   ```bash
   # Check database container logs
   docker-compose logs snso-db
   
   # Verify database is ready
   docker-compose exec snso-db mysql -u root -p -e "SHOW DATABASES;"
   ```

4. **Frontend Build Issues**
   ```bash
   # Clear node modules and reinstall
   docker-compose down
   docker volume rm snso-erp_snso_node_modules
   docker-compose up -d
   ```

### Performance Optimization

1. **Increase Memory Limits**
   ```yaml
   # In docker-compose.yml
   services:
     snso-app:
       deploy:
         resources:
           limits:
             memory: 1G
   ```

2. **Enable OPcache**
   ```ini
   # Already enabled in docker/php/php.ini
   opcache.enable=1
   opcache.memory_consumption=128
   ```

## 📊 Monitoring

### Container Health

```bash
# Check container status
docker-compose ps

# View resource usage
docker stats

# Check container health
docker-compose exec snso-app curl -f http://localhost/
```

### Logs

```bash
# View all logs
docker-compose logs

# Follow specific service logs
docker-compose logs -f snso-app
docker-compose logs -f snso-db
docker-compose logs -f snso-redis

# View last 100 lines
docker-compose logs --tail=100 snso-app
```

## 🔒 Security Considerations

### Development Environment

This setup is optimized for **development only**. For production:

1. Change all default passwords
2. Disable debug mode
3. Use environment-specific configurations
4. Implement proper SSL/TLS
5. Restrict database access
6. Use secrets management

### Default Credentials

- **Database Root**: `snso_root_password`
- **Database User**: `snso_user` / `snso_password`
- **Redis**: `snso_redis_password`
- **Default Admin**: `admin` / `admin123`

## 🚦 Commands Reference

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart services
docker-compose restart

# Rebuild containers
docker-compose build --no-cache

# View logs
docker-compose logs -f

# Execute commands in containers
docker-compose exec snso-app bash
docker-compose exec snso-db mysql -u root -p
docker-compose exec snso-redis redis-cli

# Clean up
docker-compose down -v  # Removes volumes
docker system prune     # Clean up unused resources
```

## 📞 Support

For issues related to:
- **Docker Setup**: Check this README and troubleshooting section
- **SNSO ERP Application**: Refer to the main application documentation
- **Legacy Compatibility**: Ensure you're using the specified versions (PHP 7.0, MySQL 5.5, Node.js 10)

---

**Note**: This Docker setup maintains compatibility with the legacy technology stack. For production deployments, consider upgrading to modern versions with appropriate migration strategies.
