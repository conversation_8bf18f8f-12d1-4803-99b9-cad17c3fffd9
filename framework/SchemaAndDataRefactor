#!/usr/bin/env php
<?php
/**
 * Project: snso-framework-7
 *
 * Main CMD File.
 *
 * Main Application Loader Which loads the Debugger and Benchmark.
 *
 * PHP version 7
 *
 * @category   Core
 * @package    Core
 * <AUTHOR> <<EMAIL>>
 * @version    0.0.0
 * @link       http://snso.net
 * @since      File available since Release 0.0.0
 */
if (!defined('ENVIRONMENT'))
    /**
     * Current Environments
     */
    define('ENVIRONMENT', (isset($_SERVER['ENVIRONMENT'])) ? $_SERVER['ENVIRONMENT'] : 'DEVELOPMENT');

// Grab the starter.
require_once 'init.php';
require 'helpers.php';
require_once CORE_DIR . DS . 'autoload.php';

// Load Debugger
$dbg = new Debugger();

// Initialize SNSO
global $snso;
$snso = snso::startup();

// Schema Refactor
echo "\n SchemaAndDataRefactor Start .............................................................................\n";
echo "\n Entities Up Start ====================================================================================\n";
Database::entitiesUp();

echo "\n Core Entities Seed Start ====================================================================================\n";
Database::coreEntitiesSeed();

echo "\n Migration Start ====================================================================================\n";
DataRefactor::migrations();


echo "\n Deleting Un Using Tables ====================================================================================\n";
DataRefactor::deleteUnUsingTables();

echo "\n Organization Setup ====================================================================================\n";
Organization::OrganizationBasicSetup();
$_SESSION['organization'] = Organization::readID(CLIENT_ID);

echo "\n Update Manager Privilege ====================================================================================\n";
Privilege::updateManagersPrivileges($_SESSION['organization']);

echo "\n Update Transaction List ====================================================================================\n";
Translation::updateTranslationList();

echo "\n Calculate users age and store the value in age column in sh_user ====================================================================================\n";
DataRefactor::calculateUsersAge();

echo "\n update user live status, just for not updated users live status ====================================================================================\n";
DataRefactor::updateUsersLiveStatus();

// echo "\nUpdate accounts tree and fix accounts branching";
// DataRefactor::refactorFiscalYearAccounts('update');

echo "\nUpdate Requests archive status";
DataRefactor::updateArchivedRequestsIdStatus();

//echo "\nEdit arch serial numbers with prefix";
//DataRefactor::addPrefixForOldTransactions();

echo "\nFix org metadata [extra setting]";
DataRefactor::org_meta_data_extra_fallback();

echo "\nSet missing bank name that has a code";
DataRefactor::setMissingBankName();

echo "\nAdding the new Privileg to ArchReferral";
DataRefactor::addingAppendAndCommentPrivileagesToReferral();

//echo "\nRefactor old print templates to new ones depending on base print templates";
//DataRefactor::refactorToNewBaseTemplateStructure();

//DataRefactor::notifyWithUpdate();

DataRefactor::adjustSubCostCentersActivity();

echo "\n migrate old data in transactions which come from sales model";
DataRefactor::migrateOldAccountToNew();
echo "\n migrate transactions without entry date come from sales";
DataRefactor::transactionsWithoutEntryDate();

echo "\n migrate sanabel records received from null to zero";
DataRefactor::nonNullSanabelRecords();

echo "\nFlush all cached data in the redis cache memory";
$snso->run();
cache_flush();



// Shutdown framework gracefully..
$dbg->shutdown();
