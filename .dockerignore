# Docker ignore file for SNSO ERP

# Version control
.git
.gitignore
.gitattributes

# Docker files
Dockerfile
docker-compose.yml
docker-compose.*.yml
.dockerignore

# Documentation
README.md
*.md
docs/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vagrant
.vagrant/
Vagrantfile

# Cache directories
cache/
.cache/
tmp/
temp/

# Build directories
build/
dist/
public/templates/assets/resources/dist/

# Backup files
*.bak
*.backup
*.old

# Database files
*.sql
*.db
*.sqlite
*.sqlite3

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Test files
tests/
test/
spec/
*.test.js
*.spec.js

# PHP specific
vendor/
composer.lock
.phpunit.result.cache

# Framework specific cache
framework/cache/
smarty/cache/
smarty/templates_c/

# Upload directories (may contain sensitive data)
uploads/
files/
documents/

# Configuration files that might contain secrets
config.php
database.php
*.conf.local
*.ini.local
