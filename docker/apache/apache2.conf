# Apache2 configuration for SNSO ERP
# Optimized for legacy PHP 7.0 application

# Global configuration
ServerRoot /etc/apache2
PidFile ${APACHE_PID_FILE}
Timeout 300
KeepAlive On
MaxKeepAliveRequests 100
KeepAliveTimeout 5

# User and Group
User ${APACHE_RUN_USER}
Group ${APACHE_RUN_GROUP}

# Server name
ServerName localhost

# Directory permissions
<Directory />
    Options FollowSymLinks
    AllowOverride None
    Require all denied
</Directory>

<Directory /usr/share>
    AllowOverride None
    Require all granted
</Directory>

<Directory /var/www/>
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
</Directory>

# AccessFileName
AccessFileName .htaccess

# Deny access to .htaccess files
<FilesMatch "^\.ht">
    Require all denied
</FilesMatch>

# Log format
LogFormat "%v:%p %h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\"" vhost_combined
LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\"" combined
LogFormat "%h %l %u %t \"%r\" %>s %O" common
LogFormat "%{Referer}i -> %U" referer
LogFormat "%{User-agent}i" agent

# Include module configuration
IncludeOptional mods-enabled/*.load
IncludeOptional mods-enabled/*.conf

# Include ports configuration
Include ports.conf

# Include sites
IncludeOptional sites-enabled/*.conf

# Security settings for SNSO ERP
ServerTokens Prod
ServerSignature Off

# PHP settings for legacy compatibility
<IfModule mod_php7.c>
    php_value memory_limit 512M
    php_value upload_max_filesize 100M
    php_value post_max_size 100M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value session.gc_maxlifetime 3600
    php_value date.timezone "Asia/Riyadh"
</IfModule>

# MIME types for SNSO assets
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
</IfModule>
