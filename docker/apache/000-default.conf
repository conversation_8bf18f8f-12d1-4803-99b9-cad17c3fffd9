<VirtualHost *:80>
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/html
    
    # Enable rewrite module for clean URLs
    RewriteEngine On
    
    # SNSO ERP specific rewrites
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ /index.php [QSA,L]
    
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    # Enable compression
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
    
    # Cache static assets
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
    </FilesMatch>
    
    # Directory permissions
    <Directory /var/www/html>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Protect sensitive files
        <FilesMatch "\.(ini|log|conf)$">
            Require all denied
        </FilesMatch>
    </Directory>
    
    # Protect framework directory from direct access
    <Directory /var/www/html/framework>
        Options -Indexes
        AllowOverride None
        Require all denied
    </Directory>
    
    # Allow access to framework assets
    <Directory /var/www/html/framework/assets>
        Options -Indexes
        AllowOverride None
        Require all granted
    </Directory>
    
    # Protect sensitive directories
    <DirectoryMatch "/var/www/html/(logs|cache|model|src)">
        Options -Indexes
        AllowOverride None
        Require all denied
    </DirectoryMatch>
    
    # Error and access logs
    ErrorLog ${APACHE_LOG_DIR}/snso_error.log
    CustomLog ${APACHE_LOG_DIR}/snso_access.log combined
    
    # Log level for debugging (change to warn in production)
    LogLevel info
</VirtualHost>
