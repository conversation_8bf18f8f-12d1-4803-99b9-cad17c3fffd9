-- SNSO ERP Basic Database Setup
-- MySQL 5.5 Compatible
-- NOTE: This only creates the database and user.
-- The actual schema initialization should be done via framework/SchemaAndDataRefactor.php

-- Set character set for Arabic support
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS `snso_erp`
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- Use the database
USE `snso_erp`;

-- Grant privileges to snso_user
GRANT ALL PRIVILEGES ON `snso_erp`.* TO 'snso_user'@'%';
GRANT ALL PRIVILEGES ON `snso_erp`.* TO 'snso_user'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;

-- Show completion message
SELECT 'SNSO ERP Database Created - Run SchemaAndDataRefactor.php to initialize schema' as Status;
