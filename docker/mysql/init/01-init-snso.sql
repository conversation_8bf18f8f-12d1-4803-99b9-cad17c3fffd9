-- SNSO ERP Database Initialization Script
-- MySQL 5.5 Compatible

-- Set character set for Arabic support
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS `snso_erp` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use the database
USE `snso_erp`;

-- Grant privileges to snso_user
GRANT ALL PRIVILEGES ON `snso_erp`.* TO 'snso_user'@'%';
GRANT ALL PRIVILEGES ON `snso_erp`.* TO 'snso_user'@'localhost';

-- Create a basic configuration table for initial setup
CREATE TABLE IF NOT EXISTS `sh_setting` (
  `sh_setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `sh_setting_key` varchar(255) NOT NULL,
  `sh_setting_value` text,
  `sh_setting_description` text,
  `sh_setting_created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `sh_setting_updated_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`sh_setting_id`),
  UNIQUE KEY `sh_setting_key` (`sh_setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert basic configuration
INSERT INTO `sh_setting` (`sh_setting_key`, `sh_setting_value`, `sh_setting_description`) VALUES
('system_name', 'SNSO ERP System', 'System Name'),
('system_version', '17.16.3', 'Current System Version'),
('database_version', '1.0.0', 'Database Schema Version'),
('timezone', 'Asia/Riyadh', 'System Timezone'),
('locale', 'ar_SA', 'System Locale'),
('currency', 'SAR', 'Default Currency'),
('date_format', 'Y-m-d', 'Default Date Format'),
('time_format', 'H:i:s', 'Default Time Format'),
('session_timeout', '3600', 'Session Timeout in Seconds'),
('max_upload_size', '100M', 'Maximum Upload File Size'),
('debug_mode', '1', 'Debug Mode (1=On, 0=Off)'),
('maintenance_mode', '0', 'Maintenance Mode (1=On, 0=Off)')
ON DUPLICATE KEY UPDATE 
`sh_setting_value` = VALUES(`sh_setting_value`),
`sh_setting_updated_date` = CURRENT_TIMESTAMP;

-- Create users table for basic authentication
CREATE TABLE IF NOT EXISTS `sh_user` (
  `sh_user_id` int(11) NOT NULL AUTO_INCREMENT,
  `sh_user_username` varchar(100) NOT NULL,
  `sh_user_email` varchar(255) NOT NULL,
  `sh_user_password` varchar(255) NOT NULL,
  `sh_user_first_name` varchar(100) DEFAULT NULL,
  `sh_user_last_name` varchar(100) DEFAULT NULL,
  `sh_user_status` tinyint(1) DEFAULT '1',
  `sh_user_created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `sh_user_updated_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `sh_user_last_login` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`sh_user_id`),
  UNIQUE KEY `sh_user_username` (`sh_user_username`),
  UNIQUE KEY `sh_user_email` (`sh_user_email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default admin user (password: admin123)
INSERT INTO `sh_user` (`sh_user_username`, `sh_user_email`, `sh_user_password`, `sh_user_first_name`, `sh_user_last_name`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator')
ON DUPLICATE KEY UPDATE 
`sh_user_updated_date` = CURRENT_TIMESTAMP;

-- Create sessions table for session management
CREATE TABLE IF NOT EXISTS `sh_session` (
  `sh_session_id` varchar(128) NOT NULL,
  `sh_session_user_id` int(11) DEFAULT NULL,
  `sh_session_data` text,
  `sh_session_created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `sh_session_updated_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `sh_session_expires` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`sh_session_id`),
  KEY `sh_session_user_id` (`sh_session_user_id`),
  KEY `sh_session_expires` (`sh_session_expires`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create logs table for system logging
CREATE TABLE IF NOT EXISTS `sh_log` (
  `sh_log_id` int(11) NOT NULL AUTO_INCREMENT,
  `sh_log_level` varchar(20) NOT NULL,
  `sh_log_message` text NOT NULL,
  `sh_log_context` text,
  `sh_log_user_id` int(11) DEFAULT NULL,
  `sh_log_ip_address` varchar(45) DEFAULT NULL,
  `sh_log_user_agent` text,
  `sh_log_created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`sh_log_id`),
  KEY `sh_log_level` (`sh_log_level`),
  KEY `sh_log_user_id` (`sh_log_user_id`),
  KEY `sh_log_created_date` (`sh_log_created_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Flush privileges
FLUSH PRIVILEGES;

-- Log initialization
INSERT INTO `sh_log` (`sh_log_level`, `sh_log_message`, `sh_log_context`) VALUES
('INFO', 'Database initialized successfully', '{"script": "01-init-snso.sql", "version": "1.0.0"}');

-- Show completion message
SELECT 'SNSO ERP Database Initialized Successfully' as Status;
