#!/bin/bash

# SNSO ERP Schema Initialization Script
# This script initializes the SNSO ERP database schema using the framework's built-in system

set -e

echo "🗄️ SNSO ERP Schema Initialization Starting..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Wait for database to be ready
print_status "Waiting for database to be ready..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker-compose exec -T snso-db mysql -u snso_user -psnso_password -e "SELECT 1;" >/dev/null 2>&1; then
        print_success "Database is ready!"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "Database failed to become ready after $max_attempts attempts"
        exit 1
    fi
    
    print_status "Attempt $attempt/$max_attempts - Database not ready yet, waiting..."
    sleep 2
    ((attempt++))
done

# Check if SchemaAndDataRefactor file exists
SCHEMA_FILE="framework/SchemaAndDataRefactor"
SCHEMA_PHP_FILE="framework/SchemaAndDataRefactor.php"

if [ ! -f "$SCHEMA_FILE" ]; then
    print_error "SchemaAndDataRefactor file not found at $SCHEMA_FILE"
    exit 1
fi

print_status "Found SchemaAndDataRefactor file"

# Backup original file if .php version doesn't exist
if [ ! -f "$SCHEMA_PHP_FILE" ]; then
    print_status "Creating PHP version of SchemaAndDataRefactor..."
    cp "$SCHEMA_FILE" "$SCHEMA_PHP_FILE"
    print_success "Created $SCHEMA_PHP_FILE"
else
    print_warning "$SCHEMA_PHP_FILE already exists, using existing file"
fi

# Wait for web server to be ready
print_status "Waiting for web server to be ready..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -f http://localhost:8080 >/dev/null 2>&1; then
        print_success "Web server is ready!"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "Web server failed to become ready after $max_attempts attempts"
        exit 1
    fi
    
    print_status "Attempt $attempt/$max_attempts - Web server not ready yet, waiting..."
    sleep 2
    ((attempt++))
done

# Make GET request to initialize schema
print_status "Initializing SNSO ERP database schema..."
print_warning "This may take several minutes depending on the database size..."

# Create a temporary log file for the initialization output
INIT_LOG="docker/logs/schema-init-$(date +%Y%m%d_%H%M%S).log"
mkdir -p "$(dirname "$INIT_LOG")"

print_status "Making request to SchemaAndDataRefactor.php..."
print_status "Logs will be saved to: $INIT_LOG"

# Make the request and capture output
if curl -v -m 600 "http://localhost:8080/framework/SchemaAndDataRefactor.php" > "$INIT_LOG" 2>&1; then
    print_success "Schema initialization completed successfully!"
    print_status "Check the log file for details: $INIT_LOG"
    
    # Show last few lines of the log
    echo ""
    echo "📋 Last 10 lines of initialization log:"
    echo "----------------------------------------"
    tail -n 10 "$INIT_LOG"
    echo "----------------------------------------"
    
else
    print_error "Schema initialization failed!"
    print_error "Check the log file for details: $INIT_LOG"
    
    # Show last few lines of the error log
    echo ""
    echo "❌ Last 10 lines of error log:"
    echo "----------------------------------------"
    tail -n 10 "$INIT_LOG"
    echo "----------------------------------------"
    
    exit 1
fi

# Verify some basic tables were created
print_status "Verifying database initialization..."

# Check if some expected tables exist
EXPECTED_TABLES=("sh_user" "sh_setting" "sh_program" "sh_section")
MISSING_TABLES=()

for table in "${EXPECTED_TABLES[@]}"; do
    if ! docker-compose exec -T snso-db mysql -u snso_user -psnso_password snso_erp -e "DESCRIBE $table;" >/dev/null 2>&1; then
        MISSING_TABLES+=("$table")
    fi
done

if [ ${#MISSING_TABLES[@]} -eq 0 ]; then
    print_success "Database verification passed - all expected tables found"
else
    print_warning "Some expected tables are missing: ${MISSING_TABLES[*]}"
    print_warning "This might be normal depending on your SNSO ERP configuration"
fi

# Clean up - remove the .php file if we created it
if [ -f "$SCHEMA_PHP_FILE" ] && [ ! -f "${SCHEMA_FILE}.original" ]; then
    print_status "Cleaning up temporary PHP file..."
    rm "$SCHEMA_PHP_FILE"
    print_success "Cleanup completed"
fi

echo ""
print_success "🎉 SNSO ERP Schema Initialization Completed!"
echo ""
echo "📋 Next Steps:"
echo "   1. Access the application: http://localhost:8080"
echo "   2. Check the initialization log: $INIT_LOG"
echo "   3. Verify the application is working correctly"
echo ""
echo "🔧 If you encounter issues:"
echo "   - Check application logs: docker-compose logs snso-app"
echo "   - Check database logs: docker-compose logs snso-db"
echo "   - Review the initialization log: $INIT_LOG"
echo ""
