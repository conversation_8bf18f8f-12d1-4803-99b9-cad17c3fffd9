/**
 * selectize.bootstrap3.css (v0.12.6) - Bootstrap 3 Theme
 * Copyright (c) 2013–2015 <PERSON> & contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the License at:
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF
 * ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 *
 * <AUTHOR> <<EMAIL>>
 */
.selectize-control.plugin-drag_drop.multi > .selectize-input > div.ui-sortable-placeholder {
  visibility: visible !important;
  background: #f2f2f2 !important;
  background: rgba(0, 0, 0, 0.06) !important;
  border: 0 none !important;
  -webkit-box-shadow: inset 0 0 12px 4px #fff;
  box-shadow: inset 0 0 12px 4px #fff;
}
.selectize-control.plugin-drag_drop .ui-sortable-placeholder::after {
  content: '!';
  visibility: hidden;
}
.selectize-control.plugin-drag_drop .ui-sortable-helper {
  -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.selectize-dropdown-header {
  position: relative;
  padding: 3px 12px;
  border-bottom: 1px solid #d0d0d0;
  background: #f8f8f8;
  border-radius: 4px 4px 0 0;
}
.selectize-dropdown-header-close {
  position: absolute;
  right: 12px;
  top: 50%;
  color: #333333;
  opacity: 0.4;
  margin-top: -12px;
  line-height: 20px;
  font-size: 20px !important;
}
.selectize-dropdown-header-close:hover {
  color: #000000;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup {
  border-right: 1px solid #f2f2f2;
  border-top: 0 none;
  float: left;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup:last-child {
  border-right: 0 none;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup:before {
  display: none;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup-header {
  border-top: 0 none;
}
.selectize-control.plugin-remove_button [data-value] {
  position: relative;
  padding-right: 24px !important;
}
.selectize-control.plugin-remove_button [data-value] .remove {
  z-index: 1;
  /* fixes ie bug (see #392) */
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 17px;
  text-align: center;
  font-weight: bold;
  font-size: 12px;
  color: inherit;
  text-decoration: none;
  vertical-align: middle;
  display: inline-block;
  padding: 1px 0 0 0;
  border-left: 1px solid rgba(0, 0, 0, 0);
  border-radius: 0 2px 2px 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.selectize-control.plugin-remove_button [data-value] .remove:hover {
  background: rgba(0, 0, 0, 0.05);
}
.selectize-control.plugin-remove_button [data-value].active .remove {
  border-left-color: rgba(0, 0, 0, 0);
}
.selectize-control.plugin-remove_button .disabled [data-value] .remove:hover {
  background: none;
}
.selectize-control.plugin-remove_button .disabled [data-value] .remove {
  border-left-color: rgba(77, 77, 77, 0);
}
.selectize-control.plugin-remove_button .remove-single {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 23px;
}
.selectize-control {
  position: relative;
}
.selectize-dropdown,
.selectize-input,
.selectize-input input {
  color: #333333;
  font-family: inherit;
  font-size: inherit;
  line-height: 20px;
  -webkit-font-smoothing: inherit;
}
.selectize-input,
.selectize-control.single .selectize-input.input-active {
  background: #fff;
  cursor: text;
  display: inline-block;
}
.selectize-input {
  border: 1px solid #ccc;
  padding: 6px 12px;
  display: inline-block;
  width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 1;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 4px;
}
.selectize-control.multi .selectize-input.has-items {
  padding: 5px 12px 2px;
}
.selectize-input.full {
  background-color: #fff;
}
.selectize-input.disabled,
.selectize-input.disabled * {
  cursor: default !important;
}
.selectize-input.focus {
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
}
.selectize-input.dropdown-active {
  border-radius: 4px 4px 0 0;
}
.selectize-input > * {
  vertical-align: baseline;
  display: -moz-inline-stack;
  display: inline-block;
  zoom: 1;
  *display: inline;
}
.selectize-control.multi .selectize-input > div {
  cursor: pointer;
  margin: 0 3px 3px 0;
  padding: 1px 3px;
  background: #efefef;
  color: #333333;
  border: 0 solid rgba(0, 0, 0, 0);
}
.selectize-control.multi .selectize-input > div.active {
  background: #428bca;
  color: #fff;
  border: 0 solid rgba(0, 0, 0, 0);
}
.selectize-control.multi .selectize-input.disabled > div,
.selectize-control.multi .selectize-input.disabled > div.active {
  color: #808080;
  background: #ffffff;
  border: 0 solid rgba(77, 77, 77, 0);
}
.selectize-input > input {
  display: inline-block !important;
  padding: 0 !important;
  min-height: 0 !important;
  max-height: none !important;
  max-width: 100% !important;
  margin: 0 !important;
  text-indent: 0 !important;
  border: 0 none !important;
  background: none !important;
  line-height: inherit !important;
  -webkit-user-select: auto !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}
.selectize-input > input::-ms-clear {
  display: none;
}
.selectize-input > input:focus {
  outline: none !important;
}
.selectize-input::after {
  content: ' ';
  display: block;
  clear: left;
}
.selectize-input.dropdown-active::before {
  content: ' ';
  display: block;
  position: absolute;
  background: #ffffff;
  height: 1px;
  bottom: 0;
  left: 0;
  right: 0;
}
.selectize-dropdown {
  position: absolute;
  z-index: 10;
  border: 1px solid #d0d0d0;
  background: #fff;
  margin: -1px 0 0 0;
  border-top: 0 none;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 4px 4px;
}
.selectize-dropdown [data-selectable] {
  cursor: pointer;
  overflow: hidden;
}
.selectize-dropdown [data-selectable] .highlight {
  background: rgba(255, 237, 40, 0.4);
  border-radius: 1px;
}
.selectize-dropdown .option,
.selectize-dropdown .optgroup-header {
  padding: 3px 12px;
}
.selectize-dropdown .option,
.selectize-dropdown [data-disabled],
.selectize-dropdown [data-disabled] [data-selectable].option {
  cursor: inherit;
  opacity: 0.5;
}
.selectize-dropdown [data-selectable].option {
  opacity: 1;
}
.selectize-dropdown .optgroup:first-child .optgroup-header {
  border-top: 0 none;
}
.selectize-dropdown .optgroup-header {
  color: #777777;
  background: #fff;
  cursor: default;
}
.selectize-dropdown .active {
  background-color: #f5f5f5;
  color: #262626;
}
.selectize-dropdown .active.create {
  color: #262626;
}
.selectize-dropdown .create {
  color: rgba(51, 51, 51, 0.5);
}
.selectize-dropdown-content {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 200px;
  -webkit-overflow-scrolling: touch;
}
.selectize-control.single .selectize-input,
.selectize-control.single .selectize-input input {
  cursor: pointer;
}
.selectize-control.single .selectize-input.input-active,
.selectize-control.single .selectize-input.input-active input {
  cursor: text;
}
.selectize-control.single .selectize-input:after {
  content: ' ';
  display: block;
  position: absolute;
  top: 50%;
  right: 17px;
  margin-top: -3px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 5px 0 5px;
  border-color: #333333 transparent transparent transparent;
}
.selectize-control.single .selectize-input.dropdown-active:after {
  margin-top: -4px;
  border-width: 0 5px 5px 5px;
  border-color: transparent transparent #333333 transparent;
}
.selectize-control.rtl.single .selectize-input:after {
  left: 17px;
  right: auto;
}
.selectize-control.rtl .selectize-input > input {
  margin: 0 4px 0 -2px !important;
}
.selectize-control .selectize-input.disabled {
  opacity: 0.5;
  background-color: #fff;
}
.selectize-dropdown,
.selectize-dropdown.form-control {
  height: auto;
  padding: 0;
  margin: 2px 0 0 0;
  z-index: 1000;
  background: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}
.selectize-dropdown .optgroup-header {
  font-size: 12px;
  line-height: 1.42857143;
}
.selectize-dropdown .optgroup:first-child:before {
  display: none;
}
.selectize-dropdown .optgroup:before {
  content: ' ';
  display: block;
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
  margin-left: -12px;
  margin-right: -12px;
}
.selectize-dropdown-content {
  padding: 5px 0;
}
.selectize-dropdown-header {
  padding: 6px 12px;
}
.selectize-input {
  min-height: 34px;
}
.selectize-input.dropdown-active {
  border-radius: 4px;
}
.selectize-input.dropdown-active::before {
  display: none;
}
.selectize-input.focus {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.has-error .selectize-input {
  border-color: #a94442;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-error .selectize-input:focus {
  border-color: #843534;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}
.selectize-control.multi .selectize-input.has-items {
  padding-left: 9px;
  padding-right: 9px;
}
.selectize-control.multi .selectize-input > div {
  border-radius: 3px;
}
.form-control.selectize-control {
  padding: 0;
  height: auto;
  border: none;
  background: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 0;
}
@media only screen and (max-width: 320px) {
  .sm\:m-0 {
    margin: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-0 {
    margin: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-0 {
    margin: 0rem !important;
  }
}

.m-0 {
  margin: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-0 {
    margin-top: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-0 {
    margin-top: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-0 {
    margin-top: 0rem !important;
  }
}

.mt-0 {
  margin-top: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-0-px {
    margin-top: 0px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-0-px {
    margin-top: 0px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-0-px {
    margin-top: 0px !important;
  }
}

.mt-0-px {
  margin-top: 0px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--0 {
    margin-top: -0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--0 {
    margin-top: -0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--0 {
    margin-top: -0rem !important;
  }
}

.mt--0 {
  margin-top: -0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-0 {
    margin-left: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-0 {
    margin-left: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-0 {
    margin-left: 0rem !important;
  }
}

.ml-0 {
  margin-left: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--0 {
    margin-left: -0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--0 {
    margin-left: -0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--0 {
    margin-left: -0rem !important;
  }
}

.ml--0 {
  margin-left: -0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--0 {
    margin-right: -0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--0 {
    margin-right: -0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--0 {
    margin-right: -0rem !important;
  }
}

.mr--0 {
  margin-right: -0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-0 {
    margin-right: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-0 {
    margin-right: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-0 {
    margin-right: 0rem !important;
  }
}

.mr-0 {
  margin-right: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-0-px {
    margin-right: 0px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-0-px {
    margin-right: 0px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-0-px {
    margin-right: 0px !important;
  }
}

.mr-0-px {
  margin-right: 0px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-0 {
    margin-bottom: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-0 {
    margin-bottom: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-0 {
    margin-bottom: 0rem !important;
  }
}

.mb-0 {
  margin-bottom: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--0 {
    margin-bottom: -0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--0 {
    margin-bottom: -0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--0 {
    margin-bottom: -0rem !important;
  }
}

.mb--0 {
  margin-bottom: -0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-0 {
    margin-left: 0rem !important;
    margin-right: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-0 {
    margin-left: 0rem !important;
    margin-right: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-0 {
    margin-left: 0rem !important;
    margin-right: 0rem !important;
  }
}

.mx-0 {
  margin-left: 0rem !important;
  margin-right: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-00 -px {
    margin-left: 00px !important;
    margin-right: 00px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-00 -px {
    margin-left: 00px !important;
    margin-right: 00px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-00 -px {
    margin-left: 00px !important;
    margin-right: 00px !important;
  }
}

.mx-00 -px {
  margin-left: 00px !important;
  margin-right: 00px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--0 {
    margin-left: -0rem !important;
    margin-right: -0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--0 {
    margin-left: -0rem !important;
    margin-right: -0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--0 {
    margin-left: -0rem !important;
    margin-right: -0rem !important;
  }
}

.mx--0 {
  margin-left: -0rem !important;
  margin-right: -0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-0 {
    margin-top: 0rem !important;
    margin-bottom: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-0 {
    margin-top: 0rem !important;
    margin-bottom: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-0 {
    margin-top: 0rem !important;
    margin-bottom: 0rem !important;
  }
}

.my-0 {
  margin-top: 0rem !important;
  margin-bottom: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-00 -px {
    margin-top: 00px !important;
    margin-bottom: 00px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-00 -px {
    margin-top: 00px !important;
    margin-bottom: 00px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-00 -px {
    margin-top: 00px !important;
    margin-bottom: 00px !important;
  }
}

.my-00 -px {
  margin-top: 00px !important;
  margin-bottom: 00px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--0 {
    margin-top: -0rem !important;
    margin-bottom: -0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--0 {
    margin-top: -0rem !important;
    margin-bottom: -0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--0 {
    margin-top: -0rem !important;
    margin-bottom: -0rem !important;
  }
}

.my--0 {
  margin-top: -0rem !important;
  margin-bottom: -0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:m-1 {
    margin: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-1 {
    margin: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-1 {
    margin: 1rem !important;
  }
}

.m-1 {
  margin: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-1 {
    margin-top: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-1 {
    margin-top: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-1 {
    margin-top: 1rem !important;
  }
}

.mt-1 {
  margin-top: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-1-px {
    margin-top: 1px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-1-px {
    margin-top: 1px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-1-px {
    margin-top: 1px !important;
  }
}

.mt-1-px {
  margin-top: 1px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--1 {
    margin-top: -1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--1 {
    margin-top: -1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--1 {
    margin-top: -1rem !important;
  }
}

.mt--1 {
  margin-top: -1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-1 {
    margin-left: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-1 {
    margin-left: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-1 {
    margin-left: 1rem !important;
  }
}

.ml-1 {
  margin-left: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--1 {
    margin-left: -1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--1 {
    margin-left: -1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--1 {
    margin-left: -1rem !important;
  }
}

.ml--1 {
  margin-left: -1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--1 {
    margin-right: -1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--1 {
    margin-right: -1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--1 {
    margin-right: -1rem !important;
  }
}

.mr--1 {
  margin-right: -1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-1 {
    margin-right: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-1 {
    margin-right: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-1 {
    margin-right: 1rem !important;
  }
}

.mr-1 {
  margin-right: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-1-px {
    margin-right: 1px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-1-px {
    margin-right: 1px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-1-px {
    margin-right: 1px !important;
  }
}

.mr-1-px {
  margin-right: 1px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-1 {
    margin-bottom: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-1 {
    margin-bottom: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-1 {
    margin-bottom: 1rem !important;
  }
}

.mb-1 {
  margin-bottom: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--1 {
    margin-bottom: -1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--1 {
    margin-bottom: -1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--1 {
    margin-bottom: -1rem !important;
  }
}

.mb--1 {
  margin-bottom: -1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-1 {
    margin-left: 1rem !important;
    margin-right: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-1 {
    margin-left: 1rem !important;
    margin-right: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-1 {
    margin-left: 1rem !important;
    margin-right: 1rem !important;
  }
}

.mx-1 {
  margin-left: 1rem !important;
  margin-right: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-10 -px {
    margin-left: 10px !important;
    margin-right: 10px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-10 -px {
    margin-left: 10px !important;
    margin-right: 10px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-10 -px {
    margin-left: 10px !important;
    margin-right: 10px !important;
  }
}

.mx-10 -px {
  margin-left: 10px !important;
  margin-right: 10px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--1 {
    margin-left: -1rem !important;
    margin-right: -1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--1 {
    margin-left: -1rem !important;
    margin-right: -1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--1 {
    margin-left: -1rem !important;
    margin-right: -1rem !important;
  }
}

.mx--1 {
  margin-left: -1rem !important;
  margin-right: -1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-1 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-1 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-1 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
}

.my-1 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-10 -px {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-10 -px {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-10 -px {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
  }
}

.my-10 -px {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--1 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--1 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--1 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }
}

.my--1 {
  margin-top: -1rem !important;
  margin-bottom: -1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:m-2 {
    margin: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-2 {
    margin: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-2 {
    margin: 2rem !important;
  }
}

.m-2 {
  margin: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-2 {
    margin-top: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-2 {
    margin-top: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-2 {
    margin-top: 2rem !important;
  }
}

.mt-2 {
  margin-top: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-2-px {
    margin-top: 2px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-2-px {
    margin-top: 2px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-2-px {
    margin-top: 2px !important;
  }
}

.mt-2-px {
  margin-top: 2px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--2 {
    margin-top: -2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--2 {
    margin-top: -2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--2 {
    margin-top: -2rem !important;
  }
}

.mt--2 {
  margin-top: -2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-2 {
    margin-left: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-2 {
    margin-left: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-2 {
    margin-left: 2rem !important;
  }
}

.ml-2 {
  margin-left: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--2 {
    margin-left: -2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--2 {
    margin-left: -2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--2 {
    margin-left: -2rem !important;
  }
}

.ml--2 {
  margin-left: -2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--2 {
    margin-right: -2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--2 {
    margin-right: -2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--2 {
    margin-right: -2rem !important;
  }
}

.mr--2 {
  margin-right: -2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-2 {
    margin-right: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-2 {
    margin-right: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-2 {
    margin-right: 2rem !important;
  }
}

.mr-2 {
  margin-right: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-2-px {
    margin-right: 2px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-2-px {
    margin-right: 2px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-2-px {
    margin-right: 2px !important;
  }
}

.mr-2-px {
  margin-right: 2px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-2 {
    margin-bottom: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-2 {
    margin-bottom: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-2 {
    margin-bottom: 2rem !important;
  }
}

.mb-2 {
  margin-bottom: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--2 {
    margin-bottom: -2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--2 {
    margin-bottom: -2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--2 {
    margin-bottom: -2rem !important;
  }
}

.mb--2 {
  margin-bottom: -2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-2 {
    margin-left: 2rem !important;
    margin-right: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-2 {
    margin-left: 2rem !important;
    margin-right: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-2 {
    margin-left: 2rem !important;
    margin-right: 2rem !important;
  }
}

.mx-2 {
  margin-left: 2rem !important;
  margin-right: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-20 -px {
    margin-left: 20px !important;
    margin-right: 20px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-20 -px {
    margin-left: 20px !important;
    margin-right: 20px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-20 -px {
    margin-left: 20px !important;
    margin-right: 20px !important;
  }
}

.mx-20 -px {
  margin-left: 20px !important;
  margin-right: 20px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--2 {
    margin-left: -2rem !important;
    margin-right: -2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--2 {
    margin-left: -2rem !important;
    margin-right: -2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--2 {
    margin-left: -2rem !important;
    margin-right: -2rem !important;
  }
}

.mx--2 {
  margin-left: -2rem !important;
  margin-right: -2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-2 {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-2 {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-2 {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important;
  }
}

.my-2 {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-20 -px {
    margin-top: 20px !important;
    margin-bottom: 20px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-20 -px {
    margin-top: 20px !important;
    margin-bottom: 20px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-20 -px {
    margin-top: 20px !important;
    margin-bottom: 20px !important;
  }
}

.my-20 -px {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--2 {
    margin-top: -2rem !important;
    margin-bottom: -2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--2 {
    margin-top: -2rem !important;
    margin-bottom: -2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--2 {
    margin-top: -2rem !important;
    margin-bottom: -2rem !important;
  }
}

.my--2 {
  margin-top: -2rem !important;
  margin-bottom: -2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:m-3 {
    margin: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-3 {
    margin: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-3 {
    margin: 3rem !important;
  }
}

.m-3 {
  margin: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-3 {
    margin-top: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-3 {
    margin-top: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-3 {
    margin-top: 3rem !important;
  }
}

.mt-3 {
  margin-top: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-3-px {
    margin-top: 3px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-3-px {
    margin-top: 3px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-3-px {
    margin-top: 3px !important;
  }
}

.mt-3-px {
  margin-top: 3px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--3 {
    margin-top: -3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--3 {
    margin-top: -3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--3 {
    margin-top: -3rem !important;
  }
}

.mt--3 {
  margin-top: -3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-3 {
    margin-left: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-3 {
    margin-left: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-3 {
    margin-left: 3rem !important;
  }
}

.ml-3 {
  margin-left: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--3 {
    margin-left: -3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--3 {
    margin-left: -3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--3 {
    margin-left: -3rem !important;
  }
}

.ml--3 {
  margin-left: -3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--3 {
    margin-right: -3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--3 {
    margin-right: -3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--3 {
    margin-right: -3rem !important;
  }
}

.mr--3 {
  margin-right: -3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-3 {
    margin-right: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-3 {
    margin-right: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-3 {
    margin-right: 3rem !important;
  }
}

.mr-3 {
  margin-right: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-3-px {
    margin-right: 3px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-3-px {
    margin-right: 3px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-3-px {
    margin-right: 3px !important;
  }
}

.mr-3-px {
  margin-right: 3px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-3 {
    margin-bottom: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-3 {
    margin-bottom: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-3 {
    margin-bottom: 3rem !important;
  }
}

.mb-3 {
  margin-bottom: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--3 {
    margin-bottom: -3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--3 {
    margin-bottom: -3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--3 {
    margin-bottom: -3rem !important;
  }
}

.mb--3 {
  margin-bottom: -3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-3 {
    margin-left: 3rem !important;
    margin-right: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-3 {
    margin-left: 3rem !important;
    margin-right: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-3 {
    margin-left: 3rem !important;
    margin-right: 3rem !important;
  }
}

.mx-3 {
  margin-left: 3rem !important;
  margin-right: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-30 -px {
    margin-left: 30px !important;
    margin-right: 30px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-30 -px {
    margin-left: 30px !important;
    margin-right: 30px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-30 -px {
    margin-left: 30px !important;
    margin-right: 30px !important;
  }
}

.mx-30 -px {
  margin-left: 30px !important;
  margin-right: 30px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--3 {
    margin-left: -3rem !important;
    margin-right: -3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--3 {
    margin-left: -3rem !important;
    margin-right: -3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--3 {
    margin-left: -3rem !important;
    margin-right: -3rem !important;
  }
}

.mx--3 {
  margin-left: -3rem !important;
  margin-right: -3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-3 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-3 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-3 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
}

.my-3 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-30 -px {
    margin-top: 30px !important;
    margin-bottom: 30px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-30 -px {
    margin-top: 30px !important;
    margin-bottom: 30px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-30 -px {
    margin-top: 30px !important;
    margin-bottom: 30px !important;
  }
}

.my-30 -px {
  margin-top: 30px !important;
  margin-bottom: 30px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--3 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--3 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--3 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }
}

.my--3 {
  margin-top: -3rem !important;
  margin-bottom: -3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:m-4 {
    margin: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-4 {
    margin: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-4 {
    margin: 4rem !important;
  }
}

.m-4 {
  margin: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-4 {
    margin-top: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-4 {
    margin-top: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-4 {
    margin-top: 4rem !important;
  }
}

.mt-4 {
  margin-top: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-4-px {
    margin-top: 4px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-4-px {
    margin-top: 4px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-4-px {
    margin-top: 4px !important;
  }
}

.mt-4-px {
  margin-top: 4px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--4 {
    margin-top: -4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--4 {
    margin-top: -4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--4 {
    margin-top: -4rem !important;
  }
}

.mt--4 {
  margin-top: -4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-4 {
    margin-left: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-4 {
    margin-left: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-4 {
    margin-left: 4rem !important;
  }
}

.ml-4 {
  margin-left: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--4 {
    margin-left: -4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--4 {
    margin-left: -4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--4 {
    margin-left: -4rem !important;
  }
}

.ml--4 {
  margin-left: -4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--4 {
    margin-right: -4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--4 {
    margin-right: -4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--4 {
    margin-right: -4rem !important;
  }
}

.mr--4 {
  margin-right: -4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-4 {
    margin-right: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-4 {
    margin-right: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-4 {
    margin-right: 4rem !important;
  }
}

.mr-4 {
  margin-right: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-4-px {
    margin-right: 4px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-4-px {
    margin-right: 4px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-4-px {
    margin-right: 4px !important;
  }
}

.mr-4-px {
  margin-right: 4px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-4 {
    margin-bottom: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-4 {
    margin-bottom: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-4 {
    margin-bottom: 4rem !important;
  }
}

.mb-4 {
  margin-bottom: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--4 {
    margin-bottom: -4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--4 {
    margin-bottom: -4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--4 {
    margin-bottom: -4rem !important;
  }
}

.mb--4 {
  margin-bottom: -4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-4 {
    margin-left: 4rem !important;
    margin-right: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-4 {
    margin-left: 4rem !important;
    margin-right: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-4 {
    margin-left: 4rem !important;
    margin-right: 4rem !important;
  }
}

.mx-4 {
  margin-left: 4rem !important;
  margin-right: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-40 -px {
    margin-left: 40px !important;
    margin-right: 40px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-40 -px {
    margin-left: 40px !important;
    margin-right: 40px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-40 -px {
    margin-left: 40px !important;
    margin-right: 40px !important;
  }
}

.mx-40 -px {
  margin-left: 40px !important;
  margin-right: 40px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--4 {
    margin-left: -4rem !important;
    margin-right: -4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--4 {
    margin-left: -4rem !important;
    margin-right: -4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--4 {
    margin-left: -4rem !important;
    margin-right: -4rem !important;
  }
}

.mx--4 {
  margin-left: -4rem !important;
  margin-right: -4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-4 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-4 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-4 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important;
  }
}

.my-4 {
  margin-top: 4rem !important;
  margin-bottom: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-40 -px {
    margin-top: 40px !important;
    margin-bottom: 40px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-40 -px {
    margin-top: 40px !important;
    margin-bottom: 40px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-40 -px {
    margin-top: 40px !important;
    margin-bottom: 40px !important;
  }
}

.my-40 -px {
  margin-top: 40px !important;
  margin-bottom: 40px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--4 {
    margin-top: -4rem !important;
    margin-bottom: -4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--4 {
    margin-top: -4rem !important;
    margin-bottom: -4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--4 {
    margin-top: -4rem !important;
    margin-bottom: -4rem !important;
  }
}

.my--4 {
  margin-top: -4rem !important;
  margin-bottom: -4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:m-5 {
    margin: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-5 {
    margin: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-5 {
    margin: 5rem !important;
  }
}

.m-5 {
  margin: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-5 {
    margin-top: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-5 {
    margin-top: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-5 {
    margin-top: 5rem !important;
  }
}

.mt-5 {
  margin-top: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-5-px {
    margin-top: 5px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-5-px {
    margin-top: 5px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-5-px {
    margin-top: 5px !important;
  }
}

.mt-5-px {
  margin-top: 5px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--5 {
    margin-top: -5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--5 {
    margin-top: -5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--5 {
    margin-top: -5rem !important;
  }
}

.mt--5 {
  margin-top: -5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-5 {
    margin-left: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-5 {
    margin-left: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-5 {
    margin-left: 5rem !important;
  }
}

.ml-5 {
  margin-left: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--5 {
    margin-left: -5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--5 {
    margin-left: -5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--5 {
    margin-left: -5rem !important;
  }
}

.ml--5 {
  margin-left: -5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--5 {
    margin-right: -5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--5 {
    margin-right: -5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--5 {
    margin-right: -5rem !important;
  }
}

.mr--5 {
  margin-right: -5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-5 {
    margin-right: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-5 {
    margin-right: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-5 {
    margin-right: 5rem !important;
  }
}

.mr-5 {
  margin-right: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-5-px {
    margin-right: 5px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-5-px {
    margin-right: 5px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-5-px {
    margin-right: 5px !important;
  }
}

.mr-5-px {
  margin-right: 5px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-5 {
    margin-bottom: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-5 {
    margin-bottom: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-5 {
    margin-bottom: 5rem !important;
  }
}

.mb-5 {
  margin-bottom: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--5 {
    margin-bottom: -5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--5 {
    margin-bottom: -5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--5 {
    margin-bottom: -5rem !important;
  }
}

.mb--5 {
  margin-bottom: -5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-5 {
    margin-left: 5rem !important;
    margin-right: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-5 {
    margin-left: 5rem !important;
    margin-right: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-5 {
    margin-left: 5rem !important;
    margin-right: 5rem !important;
  }
}

.mx-5 {
  margin-left: 5rem !important;
  margin-right: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-50 -px {
    margin-left: 50px !important;
    margin-right: 50px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-50 -px {
    margin-left: 50px !important;
    margin-right: 50px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-50 -px {
    margin-left: 50px !important;
    margin-right: 50px !important;
  }
}

.mx-50 -px {
  margin-left: 50px !important;
  margin-right: 50px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--5 {
    margin-left: -5rem !important;
    margin-right: -5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--5 {
    margin-left: -5rem !important;
    margin-right: -5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--5 {
    margin-left: -5rem !important;
    margin-right: -5rem !important;
  }
}

.mx--5 {
  margin-left: -5rem !important;
  margin-right: -5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-5 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-5 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-5 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }
}

.my-5 {
  margin-top: 5rem !important;
  margin-bottom: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-50 -px {
    margin-top: 50px !important;
    margin-bottom: 50px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-50 -px {
    margin-top: 50px !important;
    margin-bottom: 50px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-50 -px {
    margin-top: 50px !important;
    margin-bottom: 50px !important;
  }
}

.my-50 -px {
  margin-top: 50px !important;
  margin-bottom: 50px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--5 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--5 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--5 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }
}

.my--5 {
  margin-top: -5rem !important;
  margin-bottom: -5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:m-6 {
    margin: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-6 {
    margin: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-6 {
    margin: 6rem !important;
  }
}

.m-6 {
  margin: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-6 {
    margin-top: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-6 {
    margin-top: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-6 {
    margin-top: 6rem !important;
  }
}

.mt-6 {
  margin-top: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-6-px {
    margin-top: 6px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-6-px {
    margin-top: 6px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-6-px {
    margin-top: 6px !important;
  }
}

.mt-6-px {
  margin-top: 6px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--6 {
    margin-top: -6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--6 {
    margin-top: -6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--6 {
    margin-top: -6rem !important;
  }
}

.mt--6 {
  margin-top: -6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-6 {
    margin-left: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-6 {
    margin-left: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-6 {
    margin-left: 6rem !important;
  }
}

.ml-6 {
  margin-left: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--6 {
    margin-left: -6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--6 {
    margin-left: -6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--6 {
    margin-left: -6rem !important;
  }
}

.ml--6 {
  margin-left: -6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--6 {
    margin-right: -6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--6 {
    margin-right: -6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--6 {
    margin-right: -6rem !important;
  }
}

.mr--6 {
  margin-right: -6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-6 {
    margin-right: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-6 {
    margin-right: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-6 {
    margin-right: 6rem !important;
  }
}

.mr-6 {
  margin-right: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-6-px {
    margin-right: 6px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-6-px {
    margin-right: 6px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-6-px {
    margin-right: 6px !important;
  }
}

.mr-6-px {
  margin-right: 6px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-6 {
    margin-bottom: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-6 {
    margin-bottom: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-6 {
    margin-bottom: 6rem !important;
  }
}

.mb-6 {
  margin-bottom: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--6 {
    margin-bottom: -6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--6 {
    margin-bottom: -6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--6 {
    margin-bottom: -6rem !important;
  }
}

.mb--6 {
  margin-bottom: -6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-6 {
    margin-left: 6rem !important;
    margin-right: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-6 {
    margin-left: 6rem !important;
    margin-right: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-6 {
    margin-left: 6rem !important;
    margin-right: 6rem !important;
  }
}

.mx-6 {
  margin-left: 6rem !important;
  margin-right: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-60 -px {
    margin-left: 60px !important;
    margin-right: 60px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-60 -px {
    margin-left: 60px !important;
    margin-right: 60px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-60 -px {
    margin-left: 60px !important;
    margin-right: 60px !important;
  }
}

.mx-60 -px {
  margin-left: 60px !important;
  margin-right: 60px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--6 {
    margin-left: -6rem !important;
    margin-right: -6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--6 {
    margin-left: -6rem !important;
    margin-right: -6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--6 {
    margin-left: -6rem !important;
    margin-right: -6rem !important;
  }
}

.mx--6 {
  margin-left: -6rem !important;
  margin-right: -6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-6 {
    margin-top: 6rem !important;
    margin-bottom: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-6 {
    margin-top: 6rem !important;
    margin-bottom: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-6 {
    margin-top: 6rem !important;
    margin-bottom: 6rem !important;
  }
}

.my-6 {
  margin-top: 6rem !important;
  margin-bottom: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-60 -px {
    margin-top: 60px !important;
    margin-bottom: 60px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-60 -px {
    margin-top: 60px !important;
    margin-bottom: 60px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-60 -px {
    margin-top: 60px !important;
    margin-bottom: 60px !important;
  }
}

.my-60 -px {
  margin-top: 60px !important;
  margin-bottom: 60px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--6 {
    margin-top: -6rem !important;
    margin-bottom: -6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--6 {
    margin-top: -6rem !important;
    margin-bottom: -6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--6 {
    margin-top: -6rem !important;
    margin-bottom: -6rem !important;
  }
}

.my--6 {
  margin-top: -6rem !important;
  margin-bottom: -6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:m-7 {
    margin: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-7 {
    margin: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-7 {
    margin: 7rem !important;
  }
}

.m-7 {
  margin: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-7 {
    margin-top: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-7 {
    margin-top: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-7 {
    margin-top: 7rem !important;
  }
}

.mt-7 {
  margin-top: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-7-px {
    margin-top: 7px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-7-px {
    margin-top: 7px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-7-px {
    margin-top: 7px !important;
  }
}

.mt-7-px {
  margin-top: 7px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--7 {
    margin-top: -7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--7 {
    margin-top: -7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--7 {
    margin-top: -7rem !important;
  }
}

.mt--7 {
  margin-top: -7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-7 {
    margin-left: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-7 {
    margin-left: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-7 {
    margin-left: 7rem !important;
  }
}

.ml-7 {
  margin-left: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--7 {
    margin-left: -7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--7 {
    margin-left: -7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--7 {
    margin-left: -7rem !important;
  }
}

.ml--7 {
  margin-left: -7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--7 {
    margin-right: -7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--7 {
    margin-right: -7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--7 {
    margin-right: -7rem !important;
  }
}

.mr--7 {
  margin-right: -7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-7 {
    margin-right: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-7 {
    margin-right: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-7 {
    margin-right: 7rem !important;
  }
}

.mr-7 {
  margin-right: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-7-px {
    margin-right: 7px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-7-px {
    margin-right: 7px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-7-px {
    margin-right: 7px !important;
  }
}

.mr-7-px {
  margin-right: 7px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-7 {
    margin-bottom: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-7 {
    margin-bottom: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-7 {
    margin-bottom: 7rem !important;
  }
}

.mb-7 {
  margin-bottom: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--7 {
    margin-bottom: -7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--7 {
    margin-bottom: -7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--7 {
    margin-bottom: -7rem !important;
  }
}

.mb--7 {
  margin-bottom: -7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-7 {
    margin-left: 7rem !important;
    margin-right: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-7 {
    margin-left: 7rem !important;
    margin-right: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-7 {
    margin-left: 7rem !important;
    margin-right: 7rem !important;
  }
}

.mx-7 {
  margin-left: 7rem !important;
  margin-right: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-70 -px {
    margin-left: 70px !important;
    margin-right: 70px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-70 -px {
    margin-left: 70px !important;
    margin-right: 70px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-70 -px {
    margin-left: 70px !important;
    margin-right: 70px !important;
  }
}

.mx-70 -px {
  margin-left: 70px !important;
  margin-right: 70px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--7 {
    margin-left: -7rem !important;
    margin-right: -7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--7 {
    margin-left: -7rem !important;
    margin-right: -7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--7 {
    margin-left: -7rem !important;
    margin-right: -7rem !important;
  }
}

.mx--7 {
  margin-left: -7rem !important;
  margin-right: -7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-7 {
    margin-top: 7rem !important;
    margin-bottom: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-7 {
    margin-top: 7rem !important;
    margin-bottom: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-7 {
    margin-top: 7rem !important;
    margin-bottom: 7rem !important;
  }
}

.my-7 {
  margin-top: 7rem !important;
  margin-bottom: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-70 -px {
    margin-top: 70px !important;
    margin-bottom: 70px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-70 -px {
    margin-top: 70px !important;
    margin-bottom: 70px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-70 -px {
    margin-top: 70px !important;
    margin-bottom: 70px !important;
  }
}

.my-70 -px {
  margin-top: 70px !important;
  margin-bottom: 70px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--7 {
    margin-top: -7rem !important;
    margin-bottom: -7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--7 {
    margin-top: -7rem !important;
    margin-bottom: -7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--7 {
    margin-top: -7rem !important;
    margin-bottom: -7rem !important;
  }
}

.my--7 {
  margin-top: -7rem !important;
  margin-bottom: -7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:m-8 {
    margin: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-8 {
    margin: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-8 {
    margin: 8rem !important;
  }
}

.m-8 {
  margin: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-8 {
    margin-top: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-8 {
    margin-top: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-8 {
    margin-top: 8rem !important;
  }
}

.mt-8 {
  margin-top: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-8-px {
    margin-top: 8px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-8-px {
    margin-top: 8px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-8-px {
    margin-top: 8px !important;
  }
}

.mt-8-px {
  margin-top: 8px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--8 {
    margin-top: -8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--8 {
    margin-top: -8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--8 {
    margin-top: -8rem !important;
  }
}

.mt--8 {
  margin-top: -8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-8 {
    margin-left: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-8 {
    margin-left: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-8 {
    margin-left: 8rem !important;
  }
}

.ml-8 {
  margin-left: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--8 {
    margin-left: -8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--8 {
    margin-left: -8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--8 {
    margin-left: -8rem !important;
  }
}

.ml--8 {
  margin-left: -8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--8 {
    margin-right: -8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--8 {
    margin-right: -8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--8 {
    margin-right: -8rem !important;
  }
}

.mr--8 {
  margin-right: -8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-8 {
    margin-right: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-8 {
    margin-right: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-8 {
    margin-right: 8rem !important;
  }
}

.mr-8 {
  margin-right: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-8-px {
    margin-right: 8px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-8-px {
    margin-right: 8px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-8-px {
    margin-right: 8px !important;
  }
}

.mr-8-px {
  margin-right: 8px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-8 {
    margin-bottom: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-8 {
    margin-bottom: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-8 {
    margin-bottom: 8rem !important;
  }
}

.mb-8 {
  margin-bottom: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--8 {
    margin-bottom: -8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--8 {
    margin-bottom: -8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--8 {
    margin-bottom: -8rem !important;
  }
}

.mb--8 {
  margin-bottom: -8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-8 {
    margin-left: 8rem !important;
    margin-right: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-8 {
    margin-left: 8rem !important;
    margin-right: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-8 {
    margin-left: 8rem !important;
    margin-right: 8rem !important;
  }
}

.mx-8 {
  margin-left: 8rem !important;
  margin-right: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-80 -px {
    margin-left: 80px !important;
    margin-right: 80px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-80 -px {
    margin-left: 80px !important;
    margin-right: 80px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-80 -px {
    margin-left: 80px !important;
    margin-right: 80px !important;
  }
}

.mx-80 -px {
  margin-left: 80px !important;
  margin-right: 80px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--8 {
    margin-left: -8rem !important;
    margin-right: -8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--8 {
    margin-left: -8rem !important;
    margin-right: -8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--8 {
    margin-left: -8rem !important;
    margin-right: -8rem !important;
  }
}

.mx--8 {
  margin-left: -8rem !important;
  margin-right: -8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-8 {
    margin-top: 8rem !important;
    margin-bottom: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-8 {
    margin-top: 8rem !important;
    margin-bottom: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-8 {
    margin-top: 8rem !important;
    margin-bottom: 8rem !important;
  }
}

.my-8 {
  margin-top: 8rem !important;
  margin-bottom: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-80 -px {
    margin-top: 80px !important;
    margin-bottom: 80px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-80 -px {
    margin-top: 80px !important;
    margin-bottom: 80px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-80 -px {
    margin-top: 80px !important;
    margin-bottom: 80px !important;
  }
}

.my-80 -px {
  margin-top: 80px !important;
  margin-bottom: 80px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--8 {
    margin-top: -8rem !important;
    margin-bottom: -8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--8 {
    margin-top: -8rem !important;
    margin-bottom: -8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--8 {
    margin-top: -8rem !important;
    margin-bottom: -8rem !important;
  }
}

.my--8 {
  margin-top: -8rem !important;
  margin-bottom: -8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:m-9 {
    margin: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-9 {
    margin: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-9 {
    margin: 9rem !important;
  }
}

.m-9 {
  margin: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-9 {
    margin-top: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-9 {
    margin-top: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-9 {
    margin-top: 9rem !important;
  }
}

.mt-9 {
  margin-top: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-9-px {
    margin-top: 9px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-9-px {
    margin-top: 9px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-9-px {
    margin-top: 9px !important;
  }
}

.mt-9-px {
  margin-top: 9px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--9 {
    margin-top: -9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--9 {
    margin-top: -9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--9 {
    margin-top: -9rem !important;
  }
}

.mt--9 {
  margin-top: -9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-9 {
    margin-left: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-9 {
    margin-left: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-9 {
    margin-left: 9rem !important;
  }
}

.ml-9 {
  margin-left: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--9 {
    margin-left: -9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--9 {
    margin-left: -9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--9 {
    margin-left: -9rem !important;
  }
}

.ml--9 {
  margin-left: -9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--9 {
    margin-right: -9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--9 {
    margin-right: -9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--9 {
    margin-right: -9rem !important;
  }
}

.mr--9 {
  margin-right: -9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-9 {
    margin-right: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-9 {
    margin-right: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-9 {
    margin-right: 9rem !important;
  }
}

.mr-9 {
  margin-right: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-9-px {
    margin-right: 9px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-9-px {
    margin-right: 9px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-9-px {
    margin-right: 9px !important;
  }
}

.mr-9-px {
  margin-right: 9px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-9 {
    margin-bottom: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-9 {
    margin-bottom: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-9 {
    margin-bottom: 9rem !important;
  }
}

.mb-9 {
  margin-bottom: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--9 {
    margin-bottom: -9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--9 {
    margin-bottom: -9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--9 {
    margin-bottom: -9rem !important;
  }
}

.mb--9 {
  margin-bottom: -9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-9 {
    margin-left: 9rem !important;
    margin-right: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-9 {
    margin-left: 9rem !important;
    margin-right: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-9 {
    margin-left: 9rem !important;
    margin-right: 9rem !important;
  }
}

.mx-9 {
  margin-left: 9rem !important;
  margin-right: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-90 -px {
    margin-left: 90px !important;
    margin-right: 90px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-90 -px {
    margin-left: 90px !important;
    margin-right: 90px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-90 -px {
    margin-left: 90px !important;
    margin-right: 90px !important;
  }
}

.mx-90 -px {
  margin-left: 90px !important;
  margin-right: 90px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--9 {
    margin-left: -9rem !important;
    margin-right: -9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--9 {
    margin-left: -9rem !important;
    margin-right: -9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--9 {
    margin-left: -9rem !important;
    margin-right: -9rem !important;
  }
}

.mx--9 {
  margin-left: -9rem !important;
  margin-right: -9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-9 {
    margin-top: 9rem !important;
    margin-bottom: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-9 {
    margin-top: 9rem !important;
    margin-bottom: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-9 {
    margin-top: 9rem !important;
    margin-bottom: 9rem !important;
  }
}

.my-9 {
  margin-top: 9rem !important;
  margin-bottom: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-90 -px {
    margin-top: 90px !important;
    margin-bottom: 90px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-90 -px {
    margin-top: 90px !important;
    margin-bottom: 90px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-90 -px {
    margin-top: 90px !important;
    margin-bottom: 90px !important;
  }
}

.my-90 -px {
  margin-top: 90px !important;
  margin-bottom: 90px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--9 {
    margin-top: -9rem !important;
    margin-bottom: -9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--9 {
    margin-top: -9rem !important;
    margin-bottom: -9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--9 {
    margin-top: -9rem !important;
    margin-bottom: -9rem !important;
  }
}

.my--9 {
  margin-top: -9rem !important;
  margin-bottom: -9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:m-10 {
    margin: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-10 {
    margin: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-10 {
    margin: 10rem !important;
  }
}

.m-10 {
  margin: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-10 {
    margin-top: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-10 {
    margin-top: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-10 {
    margin-top: 10rem !important;
  }
}

.mt-10 {
  margin-top: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-10-px {
    margin-top: 10px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-10-px {
    margin-top: 10px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-10-px {
    margin-top: 10px !important;
  }
}

.mt-10-px {
  margin-top: 10px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--10 {
    margin-top: -10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--10 {
    margin-top: -10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--10 {
    margin-top: -10rem !important;
  }
}

.mt--10 {
  margin-top: -10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-10 {
    margin-left: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-10 {
    margin-left: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-10 {
    margin-left: 10rem !important;
  }
}

.ml-10 {
  margin-left: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--10 {
    margin-left: -10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--10 {
    margin-left: -10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--10 {
    margin-left: -10rem !important;
  }
}

.ml--10 {
  margin-left: -10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--10 {
    margin-right: -10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--10 {
    margin-right: -10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--10 {
    margin-right: -10rem !important;
  }
}

.mr--10 {
  margin-right: -10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-10 {
    margin-right: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-10 {
    margin-right: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-10 {
    margin-right: 10rem !important;
  }
}

.mr-10 {
  margin-right: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-10-px {
    margin-right: 10px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-10-px {
    margin-right: 10px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-10-px {
    margin-right: 10px !important;
  }
}

.mr-10-px {
  margin-right: 10px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-10 {
    margin-bottom: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-10 {
    margin-bottom: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-10 {
    margin-bottom: 10rem !important;
  }
}

.mb-10 {
  margin-bottom: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--10 {
    margin-bottom: -10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--10 {
    margin-bottom: -10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--10 {
    margin-bottom: -10rem !important;
  }
}

.mb--10 {
  margin-bottom: -10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-10 {
    margin-left: 10rem !important;
    margin-right: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-10 {
    margin-left: 10rem !important;
    margin-right: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-10 {
    margin-left: 10rem !important;
    margin-right: 10rem !important;
  }
}

.mx-10 {
  margin-left: 10rem !important;
  margin-right: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-100 -px {
    margin-left: 100px !important;
    margin-right: 100px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-100 -px {
    margin-left: 100px !important;
    margin-right: 100px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-100 -px {
    margin-left: 100px !important;
    margin-right: 100px !important;
  }
}

.mx-100 -px {
  margin-left: 100px !important;
  margin-right: 100px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--10 {
    margin-left: -10rem !important;
    margin-right: -10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--10 {
    margin-left: -10rem !important;
    margin-right: -10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--10 {
    margin-left: -10rem !important;
    margin-right: -10rem !important;
  }
}

.mx--10 {
  margin-left: -10rem !important;
  margin-right: -10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-10 {
    margin-top: 10rem !important;
    margin-bottom: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-10 {
    margin-top: 10rem !important;
    margin-bottom: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-10 {
    margin-top: 10rem !important;
    margin-bottom: 10rem !important;
  }
}

.my-10 {
  margin-top: 10rem !important;
  margin-bottom: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-100 -px {
    margin-top: 100px !important;
    margin-bottom: 100px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-100 -px {
    margin-top: 100px !important;
    margin-bottom: 100px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-100 -px {
    margin-top: 100px !important;
    margin-bottom: 100px !important;
  }
}

.my-100 -px {
  margin-top: 100px !important;
  margin-bottom: 100px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--10 {
    margin-top: -10rem !important;
    margin-bottom: -10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--10 {
    margin-top: -10rem !important;
    margin-bottom: -10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--10 {
    margin-top: -10rem !important;
    margin-bottom: -10rem !important;
  }
}

.my--10 {
  margin-top: -10rem !important;
  margin-bottom: -10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:m-11 {
    margin: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-11 {
    margin: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-11 {
    margin: 11rem !important;
  }
}

.m-11 {
  margin: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-11 {
    margin-top: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-11 {
    margin-top: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-11 {
    margin-top: 11rem !important;
  }
}

.mt-11 {
  margin-top: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-11-px {
    margin-top: 11px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-11-px {
    margin-top: 11px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-11-px {
    margin-top: 11px !important;
  }
}

.mt-11-px {
  margin-top: 11px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--11 {
    margin-top: -11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--11 {
    margin-top: -11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--11 {
    margin-top: -11rem !important;
  }
}

.mt--11 {
  margin-top: -11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-11 {
    margin-left: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-11 {
    margin-left: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-11 {
    margin-left: 11rem !important;
  }
}

.ml-11 {
  margin-left: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--11 {
    margin-left: -11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--11 {
    margin-left: -11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--11 {
    margin-left: -11rem !important;
  }
}

.ml--11 {
  margin-left: -11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--11 {
    margin-right: -11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--11 {
    margin-right: -11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--11 {
    margin-right: -11rem !important;
  }
}

.mr--11 {
  margin-right: -11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-11 {
    margin-right: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-11 {
    margin-right: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-11 {
    margin-right: 11rem !important;
  }
}

.mr-11 {
  margin-right: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-11-px {
    margin-right: 11px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-11-px {
    margin-right: 11px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-11-px {
    margin-right: 11px !important;
  }
}

.mr-11-px {
  margin-right: 11px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-11 {
    margin-bottom: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-11 {
    margin-bottom: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-11 {
    margin-bottom: 11rem !important;
  }
}

.mb-11 {
  margin-bottom: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--11 {
    margin-bottom: -11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--11 {
    margin-bottom: -11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--11 {
    margin-bottom: -11rem !important;
  }
}

.mb--11 {
  margin-bottom: -11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-11 {
    margin-left: 11rem !important;
    margin-right: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-11 {
    margin-left: 11rem !important;
    margin-right: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-11 {
    margin-left: 11rem !important;
    margin-right: 11rem !important;
  }
}

.mx-11 {
  margin-left: 11rem !important;
  margin-right: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-110 -px {
    margin-left: 110px !important;
    margin-right: 110px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-110 -px {
    margin-left: 110px !important;
    margin-right: 110px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-110 -px {
    margin-left: 110px !important;
    margin-right: 110px !important;
  }
}

.mx-110 -px {
  margin-left: 110px !important;
  margin-right: 110px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--11 {
    margin-left: -11rem !important;
    margin-right: -11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--11 {
    margin-left: -11rem !important;
    margin-right: -11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--11 {
    margin-left: -11rem !important;
    margin-right: -11rem !important;
  }
}

.mx--11 {
  margin-left: -11rem !important;
  margin-right: -11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-11 {
    margin-top: 11rem !important;
    margin-bottom: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-11 {
    margin-top: 11rem !important;
    margin-bottom: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-11 {
    margin-top: 11rem !important;
    margin-bottom: 11rem !important;
  }
}

.my-11 {
  margin-top: 11rem !important;
  margin-bottom: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-110 -px {
    margin-top: 110px !important;
    margin-bottom: 110px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-110 -px {
    margin-top: 110px !important;
    margin-bottom: 110px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-110 -px {
    margin-top: 110px !important;
    margin-bottom: 110px !important;
  }
}

.my-110 -px {
  margin-top: 110px !important;
  margin-bottom: 110px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--11 {
    margin-top: -11rem !important;
    margin-bottom: -11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--11 {
    margin-top: -11rem !important;
    margin-bottom: -11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--11 {
    margin-top: -11rem !important;
    margin-bottom: -11rem !important;
  }
}

.my--11 {
  margin-top: -11rem !important;
  margin-bottom: -11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:m-12 {
    margin: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:m-12 {
    margin: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:m-12 {
    margin: 12rem !important;
  }
}

.m-12 {
  margin: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-12 {
    margin-top: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-12 {
    margin-top: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-12 {
    margin-top: 12rem !important;
  }
}

.mt-12 {
  margin-top: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt-12-px {
    margin-top: 12px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt-12-px {
    margin-top: 12px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt-12-px {
    margin-top: 12px !important;
  }
}

.mt-12-px {
  margin-top: 12px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mt--12 {
    margin-top: -12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mt--12 {
    margin-top: -12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mt--12 {
    margin-top: -12rem !important;
  }
}

.mt--12 {
  margin-top: -12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml-12 {
    margin-left: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml-12 {
    margin-left: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml-12 {
    margin-left: 12rem !important;
  }
}

.ml-12 {
  margin-left: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:ml--12 {
    margin-left: -12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:ml--12 {
    margin-left: -12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:ml--12 {
    margin-left: -12rem !important;
  }
}

.ml--12 {
  margin-left: -12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr--12 {
    margin-right: -12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr--12 {
    margin-right: -12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr--12 {
    margin-right: -12rem !important;
  }
}

.mr--12 {
  margin-right: -12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-12 {
    margin-right: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-12 {
    margin-right: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-12 {
    margin-right: 12rem !important;
  }
}

.mr-12 {
  margin-right: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mr-12-px {
    margin-right: 12px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mr-12-px {
    margin-right: 12px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mr-12-px {
    margin-right: 12px !important;
  }
}

.mr-12-px {
  margin-right: 12px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb-12 {
    margin-bottom: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb-12 {
    margin-bottom: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb-12 {
    margin-bottom: 12rem !important;
  }
}

.mb-12 {
  margin-bottom: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mb--12 {
    margin-bottom: -12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mb--12 {
    margin-bottom: -12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mb--12 {
    margin-bottom: -12rem !important;
  }
}

.mb--12 {
  margin-bottom: -12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-12 {
    margin-left: 12rem !important;
    margin-right: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-12 {
    margin-left: 12rem !important;
    margin-right: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-12 {
    margin-left: 12rem !important;
    margin-right: 12rem !important;
  }
}

.mx-12 {
  margin-left: 12rem !important;
  margin-right: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-120 -px {
    margin-left: 120px !important;
    margin-right: 120px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-120 -px {
    margin-left: 120px !important;
    margin-right: 120px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-120 -px {
    margin-left: 120px !important;
    margin-right: 120px !important;
  }
}

.mx-120 -px {
  margin-left: 120px !important;
  margin-right: 120px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx--12 {
    margin-left: -12rem !important;
    margin-right: -12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx--12 {
    margin-left: -12rem !important;
    margin-right: -12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx--12 {
    margin-left: -12rem !important;
    margin-right: -12rem !important;
  }
}

.mx--12 {
  margin-left: -12rem !important;
  margin-right: -12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-12 {
    margin-top: 12rem !important;
    margin-bottom: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-12 {
    margin-top: 12rem !important;
    margin-bottom: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-12 {
    margin-top: 12rem !important;
    margin-bottom: 12rem !important;
  }
}

.my-12 {
  margin-top: 12rem !important;
  margin-bottom: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my-120 -px {
    margin-top: 120px !important;
    margin-bottom: 120px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my-120 -px {
    margin-top: 120px !important;
    margin-bottom: 120px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my-120 -px {
    margin-top: 120px !important;
    margin-bottom: 120px !important;
  }
}

.my-120 -px {
  margin-top: 120px !important;
  margin-bottom: 120px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:my--12 {
    margin-top: -12rem !important;
    margin-bottom: -12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:my--12 {
    margin-top: -12rem !important;
    margin-bottom: -12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:my--12 {
    margin-top: -12rem !important;
    margin-bottom: -12rem !important;
  }
}

.my--12 {
  margin-top: -12rem !important;
  margin-bottom: -12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-0 {
    padding: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-0 {
    padding: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-0 {
    padding: 0rem !important;
  }
}

.p-0 {
  padding: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-0 {
    padding-top: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-0 {
    padding-top: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-0 {
    padding-top: 0rem !important;
  }
}

.pt-0 {
  padding-top: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-0 {
    padding-left: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-0 {
    padding-left: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-0 {
    padding-left: 0rem !important;
  }
}

.pl-0 {
  padding-left: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-0-px {
    padding-left: 0px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-0-px {
    padding-left: 0px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-0-px {
    padding-left: 0px !important;
  }
}

.pl-0-px {
  padding-left: 0px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-0-px {
    padding-right: 0px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-0-px {
    padding-right: 0px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-0-px {
    padding-right: 0px !important;
  }
}

.pr-0-px {
  padding-right: 0px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-0 {
    padding-right: 0 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-0 {
    padding-right: 0 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-0 {
    padding-right: 0 !important;
  }
}

.pr-0 {
  padding-right: 0 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-0 {
    padding-bottom: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-0 {
    padding-bottom: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-0 {
    padding-bottom: 0rem !important;
  }
}

.pb-0 {
  padding-bottom: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-0 {
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-0 {
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-0 {
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }
}

.px-0 {
  padding-left: 0rem !important;
  padding-right: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-0-px {
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-0-px {
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-0-px {
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
}

.px-0-px {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-0 {
    padding-top: 0rem !important;
    padding-bottom: 0rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-0 {
    padding-top: 0rem !important;
    padding-bottom: 0rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-0 {
    padding-top: 0rem !important;
    padding-bottom: 0rem !important;
  }
}

.py-0 {
  padding-top: 0rem !important;
  padding-bottom: 0rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-0-px {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-0-px {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-0-px {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
  }
}

.py-0-px {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-1 {
    padding: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-1 {
    padding: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-1 {
    padding: 1rem !important;
  }
}

.p-1 {
  padding: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-1 {
    padding-top: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-1 {
    padding-top: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-1 {
    padding-top: 1rem !important;
  }
}

.pt-1 {
  padding-top: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-1 {
    padding-left: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-1 {
    padding-left: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-1 {
    padding-left: 1rem !important;
  }
}

.pl-1 {
  padding-left: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-1-px {
    padding-left: 1px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-1-px {
    padding-left: 1px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-1-px {
    padding-left: 1px !important;
  }
}

.pl-1-px {
  padding-left: 1px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-1-px {
    padding-right: 1px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-1-px {
    padding-right: 1px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-1-px {
    padding-right: 1px !important;
  }
}

.pr-1-px {
  padding-right: 1px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-1 {
    padding-right: 1 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-1 {
    padding-right: 1 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-1 {
    padding-right: 1 !important;
  }
}

.pr-1 {
  padding-right: 1 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-1 {
    padding-bottom: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-1 {
    padding-bottom: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-1 {
    padding-bottom: 1rem !important;
  }
}

.pb-1 {
  padding-bottom: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-1 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-1 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-1 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

.px-1 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-1-px {
    padding-left: 1px !important;
    padding-right: 1px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-1-px {
    padding-left: 1px !important;
    padding-right: 1px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-1-px {
    padding-left: 1px !important;
    padding-right: 1px !important;
  }
}

.px-1-px {
  padding-left: 1px !important;
  padding-right: 1px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-1 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-1 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-1 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
}

.py-1 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-1-px {
    padding-top: 1px !important;
    padding-bottom: 1px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-1-px {
    padding-top: 1px !important;
    padding-bottom: 1px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-1-px {
    padding-top: 1px !important;
    padding-bottom: 1px !important;
  }
}

.py-1-px {
  padding-top: 1px !important;
  padding-bottom: 1px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-2 {
    padding: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-2 {
    padding: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-2 {
    padding: 2rem !important;
  }
}

.p-2 {
  padding: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-2 {
    padding-top: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-2 {
    padding-top: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-2 {
    padding-top: 2rem !important;
  }
}

.pt-2 {
  padding-top: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-2 {
    padding-left: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-2 {
    padding-left: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-2 {
    padding-left: 2rem !important;
  }
}

.pl-2 {
  padding-left: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-2-px {
    padding-left: 2px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-2-px {
    padding-left: 2px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-2-px {
    padding-left: 2px !important;
  }
}

.pl-2-px {
  padding-left: 2px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-2-px {
    padding-right: 2px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-2-px {
    padding-right: 2px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-2-px {
    padding-right: 2px !important;
  }
}

.pr-2-px {
  padding-right: 2px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-2 {
    padding-right: 2 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-2 {
    padding-right: 2 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-2 {
    padding-right: 2 !important;
  }
}

.pr-2 {
  padding-right: 2 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-2 {
    padding-bottom: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-2 {
    padding-bottom: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-2 {
    padding-bottom: 2rem !important;
  }
}

.pb-2 {
  padding-bottom: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-2 {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-2 {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-2 {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }
}

.px-2 {
  padding-left: 2rem !important;
  padding-right: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-2-px {
    padding-left: 2px !important;
    padding-right: 2px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-2-px {
    padding-left: 2px !important;
    padding-right: 2px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-2-px {
    padding-left: 2px !important;
    padding-right: 2px !important;
  }
}

.px-2-px {
  padding-left: 2px !important;
  padding-right: 2px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-2 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-2 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-2 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
}

.py-2 {
  padding-top: 2rem !important;
  padding-bottom: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-2-px {
    padding-top: 2px !important;
    padding-bottom: 2px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-2-px {
    padding-top: 2px !important;
    padding-bottom: 2px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-2-px {
    padding-top: 2px !important;
    padding-bottom: 2px !important;
  }
}

.py-2-px {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-3 {
    padding: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-3 {
    padding: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-3 {
    padding: 3rem !important;
  }
}

.p-3 {
  padding: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-3 {
    padding-top: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-3 {
    padding-top: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-3 {
    padding-top: 3rem !important;
  }
}

.pt-3 {
  padding-top: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-3 {
    padding-left: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-3 {
    padding-left: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-3 {
    padding-left: 3rem !important;
  }
}

.pl-3 {
  padding-left: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-3-px {
    padding-left: 3px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-3-px {
    padding-left: 3px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-3-px {
    padding-left: 3px !important;
  }
}

.pl-3-px {
  padding-left: 3px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-3-px {
    padding-right: 3px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-3-px {
    padding-right: 3px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-3-px {
    padding-right: 3px !important;
  }
}

.pr-3-px {
  padding-right: 3px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-3 {
    padding-right: 3 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-3 {
    padding-right: 3 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-3 {
    padding-right: 3 !important;
  }
}

.pr-3 {
  padding-right: 3 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-3 {
    padding-bottom: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-3 {
    padding-bottom: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-3 {
    padding-bottom: 3rem !important;
  }
}

.pb-3 {
  padding-bottom: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-3 {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-3 {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-3 {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }
}

.px-3 {
  padding-left: 3rem !important;
  padding-right: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-3-px {
    padding-left: 3px !important;
    padding-right: 3px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-3-px {
    padding-left: 3px !important;
    padding-right: 3px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-3-px {
    padding-left: 3px !important;
    padding-right: 3px !important;
  }
}

.px-3-px {
  padding-left: 3px !important;
  padding-right: 3px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-3 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-3 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-3 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
}

.py-3 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-3-px {
    padding-top: 3px !important;
    padding-bottom: 3px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-3-px {
    padding-top: 3px !important;
    padding-bottom: 3px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-3-px {
    padding-top: 3px !important;
    padding-bottom: 3px !important;
  }
}

.py-3-px {
  padding-top: 3px !important;
  padding-bottom: 3px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-4 {
    padding: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-4 {
    padding: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-4 {
    padding: 4rem !important;
  }
}

.p-4 {
  padding: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-4 {
    padding-top: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-4 {
    padding-top: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-4 {
    padding-top: 4rem !important;
  }
}

.pt-4 {
  padding-top: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-4 {
    padding-left: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-4 {
    padding-left: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-4 {
    padding-left: 4rem !important;
  }
}

.pl-4 {
  padding-left: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-4-px {
    padding-left: 4px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-4-px {
    padding-left: 4px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-4-px {
    padding-left: 4px !important;
  }
}

.pl-4-px {
  padding-left: 4px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-4-px {
    padding-right: 4px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-4-px {
    padding-right: 4px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-4-px {
    padding-right: 4px !important;
  }
}

.pr-4-px {
  padding-right: 4px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-4 {
    padding-right: 4 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-4 {
    padding-right: 4 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-4 {
    padding-right: 4 !important;
  }
}

.pr-4 {
  padding-right: 4 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-4 {
    padding-bottom: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-4 {
    padding-bottom: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-4 {
    padding-bottom: 4rem !important;
  }
}

.pb-4 {
  padding-bottom: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-4 {
    padding-left: 4rem !important;
    padding-right: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-4 {
    padding-left: 4rem !important;
    padding-right: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-4 {
    padding-left: 4rem !important;
    padding-right: 4rem !important;
  }
}

.px-4 {
  padding-left: 4rem !important;
  padding-right: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-4-px {
    padding-left: 4px !important;
    padding-right: 4px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-4-px {
    padding-left: 4px !important;
    padding-right: 4px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-4-px {
    padding-left: 4px !important;
    padding-right: 4px !important;
  }
}

.px-4-px {
  padding-left: 4px !important;
  padding-right: 4px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-4 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-4 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-4 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }
}

.py-4 {
  padding-top: 4rem !important;
  padding-bottom: 4rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-4-px {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-4-px {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-4-px {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
  }
}

.py-4-px {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-5 {
    padding: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-5 {
    padding: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-5 {
    padding: 5rem !important;
  }
}

.p-5 {
  padding: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-5 {
    padding-top: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-5 {
    padding-top: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-5 {
    padding-top: 5rem !important;
  }
}

.pt-5 {
  padding-top: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-5 {
    padding-left: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-5 {
    padding-left: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-5 {
    padding-left: 5rem !important;
  }
}

.pl-5 {
  padding-left: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-5-px {
    padding-left: 5px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-5-px {
    padding-left: 5px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-5-px {
    padding-left: 5px !important;
  }
}

.pl-5-px {
  padding-left: 5px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-5-px {
    padding-right: 5px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-5-px {
    padding-right: 5px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-5-px {
    padding-right: 5px !important;
  }
}

.pr-5-px {
  padding-right: 5px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-5 {
    padding-right: 5 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-5 {
    padding-right: 5 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-5 {
    padding-right: 5 !important;
  }
}

.pr-5 {
  padding-right: 5 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-5 {
    padding-bottom: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-5 {
    padding-bottom: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-5 {
    padding-bottom: 5rem !important;
  }
}

.pb-5 {
  padding-bottom: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-5 {
    padding-left: 5rem !important;
    padding-right: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-5 {
    padding-left: 5rem !important;
    padding-right: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-5 {
    padding-left: 5rem !important;
    padding-right: 5rem !important;
  }
}

.px-5 {
  padding-left: 5rem !important;
  padding-right: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-5-px {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-5-px {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-5-px {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }
}

.px-5-px {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-5 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-5 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-5 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }
}

.py-5 {
  padding-top: 5rem !important;
  padding-bottom: 5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-5-px {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-5-px {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-5-px {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
  }
}

.py-5-px {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-6 {
    padding: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-6 {
    padding: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-6 {
    padding: 6rem !important;
  }
}

.p-6 {
  padding: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-6 {
    padding-top: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-6 {
    padding-top: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-6 {
    padding-top: 6rem !important;
  }
}

.pt-6 {
  padding-top: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-6 {
    padding-left: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-6 {
    padding-left: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-6 {
    padding-left: 6rem !important;
  }
}

.pl-6 {
  padding-left: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-6-px {
    padding-left: 6px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-6-px {
    padding-left: 6px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-6-px {
    padding-left: 6px !important;
  }
}

.pl-6-px {
  padding-left: 6px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-6-px {
    padding-right: 6px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-6-px {
    padding-right: 6px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-6-px {
    padding-right: 6px !important;
  }
}

.pr-6-px {
  padding-right: 6px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-6 {
    padding-right: 6 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-6 {
    padding-right: 6 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-6 {
    padding-right: 6 !important;
  }
}

.pr-6 {
  padding-right: 6 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-6 {
    padding-bottom: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-6 {
    padding-bottom: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-6 {
    padding-bottom: 6rem !important;
  }
}

.pb-6 {
  padding-bottom: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-6 {
    padding-left: 6rem !important;
    padding-right: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-6 {
    padding-left: 6rem !important;
    padding-right: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-6 {
    padding-left: 6rem !important;
    padding-right: 6rem !important;
  }
}

.px-6 {
  padding-left: 6rem !important;
  padding-right: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-6-px {
    padding-left: 6px !important;
    padding-right: 6px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-6-px {
    padding-left: 6px !important;
    padding-right: 6px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-6-px {
    padding-left: 6px !important;
    padding-right: 6px !important;
  }
}

.px-6-px {
  padding-left: 6px !important;
  padding-right: 6px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-6 {
    padding-top: 6rem !important;
    padding-bottom: 6rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-6 {
    padding-top: 6rem !important;
    padding-bottom: 6rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-6 {
    padding-top: 6rem !important;
    padding-bottom: 6rem !important;
  }
}

.py-6 {
  padding-top: 6rem !important;
  padding-bottom: 6rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-6-px {
    padding-top: 6px !important;
    padding-bottom: 6px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-6-px {
    padding-top: 6px !important;
    padding-bottom: 6px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-6-px {
    padding-top: 6px !important;
    padding-bottom: 6px !important;
  }
}

.py-6-px {
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-7 {
    padding: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-7 {
    padding: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-7 {
    padding: 7rem !important;
  }
}

.p-7 {
  padding: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-7 {
    padding-top: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-7 {
    padding-top: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-7 {
    padding-top: 7rem !important;
  }
}

.pt-7 {
  padding-top: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-7 {
    padding-left: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-7 {
    padding-left: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-7 {
    padding-left: 7rem !important;
  }
}

.pl-7 {
  padding-left: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-7-px {
    padding-left: 7px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-7-px {
    padding-left: 7px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-7-px {
    padding-left: 7px !important;
  }
}

.pl-7-px {
  padding-left: 7px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-7-px {
    padding-right: 7px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-7-px {
    padding-right: 7px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-7-px {
    padding-right: 7px !important;
  }
}

.pr-7-px {
  padding-right: 7px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-7 {
    padding-right: 7 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-7 {
    padding-right: 7 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-7 {
    padding-right: 7 !important;
  }
}

.pr-7 {
  padding-right: 7 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-7 {
    padding-bottom: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-7 {
    padding-bottom: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-7 {
    padding-bottom: 7rem !important;
  }
}

.pb-7 {
  padding-bottom: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-7 {
    padding-left: 7rem !important;
    padding-right: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-7 {
    padding-left: 7rem !important;
    padding-right: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-7 {
    padding-left: 7rem !important;
    padding-right: 7rem !important;
  }
}

.px-7 {
  padding-left: 7rem !important;
  padding-right: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-7-px {
    padding-left: 7px !important;
    padding-right: 7px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-7-px {
    padding-left: 7px !important;
    padding-right: 7px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-7-px {
    padding-left: 7px !important;
    padding-right: 7px !important;
  }
}

.px-7-px {
  padding-left: 7px !important;
  padding-right: 7px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-7 {
    padding-top: 7rem !important;
    padding-bottom: 7rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-7 {
    padding-top: 7rem !important;
    padding-bottom: 7rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-7 {
    padding-top: 7rem !important;
    padding-bottom: 7rem !important;
  }
}

.py-7 {
  padding-top: 7rem !important;
  padding-bottom: 7rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-7-px {
    padding-top: 7px !important;
    padding-bottom: 7px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-7-px {
    padding-top: 7px !important;
    padding-bottom: 7px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-7-px {
    padding-top: 7px !important;
    padding-bottom: 7px !important;
  }
}

.py-7-px {
  padding-top: 7px !important;
  padding-bottom: 7px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-8 {
    padding: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-8 {
    padding: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-8 {
    padding: 8rem !important;
  }
}

.p-8 {
  padding: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-8 {
    padding-top: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-8 {
    padding-top: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-8 {
    padding-top: 8rem !important;
  }
}

.pt-8 {
  padding-top: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-8 {
    padding-left: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-8 {
    padding-left: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-8 {
    padding-left: 8rem !important;
  }
}

.pl-8 {
  padding-left: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-8-px {
    padding-left: 8px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-8-px {
    padding-left: 8px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-8-px {
    padding-left: 8px !important;
  }
}

.pl-8-px {
  padding-left: 8px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-8-px {
    padding-right: 8px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-8-px {
    padding-right: 8px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-8-px {
    padding-right: 8px !important;
  }
}

.pr-8-px {
  padding-right: 8px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-8 {
    padding-right: 8 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-8 {
    padding-right: 8 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-8 {
    padding-right: 8 !important;
  }
}

.pr-8 {
  padding-right: 8 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-8 {
    padding-bottom: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-8 {
    padding-bottom: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-8 {
    padding-bottom: 8rem !important;
  }
}

.pb-8 {
  padding-bottom: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-8 {
    padding-left: 8rem !important;
    padding-right: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-8 {
    padding-left: 8rem !important;
    padding-right: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-8 {
    padding-left: 8rem !important;
    padding-right: 8rem !important;
  }
}

.px-8 {
  padding-left: 8rem !important;
  padding-right: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-8-px {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-8-px {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-8-px {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}

.px-8-px {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-8 {
    padding-top: 8rem !important;
    padding-bottom: 8rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-8 {
    padding-top: 8rem !important;
    padding-bottom: 8rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-8 {
    padding-top: 8rem !important;
    padding-bottom: 8rem !important;
  }
}

.py-8 {
  padding-top: 8rem !important;
  padding-bottom: 8rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-8-px {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-8-px {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-8-px {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
  }
}

.py-8-px {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-9 {
    padding: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-9 {
    padding: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-9 {
    padding: 9rem !important;
  }
}

.p-9 {
  padding: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-9 {
    padding-top: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-9 {
    padding-top: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-9 {
    padding-top: 9rem !important;
  }
}

.pt-9 {
  padding-top: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-9 {
    padding-left: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-9 {
    padding-left: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-9 {
    padding-left: 9rem !important;
  }
}

.pl-9 {
  padding-left: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-9-px {
    padding-left: 9px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-9-px {
    padding-left: 9px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-9-px {
    padding-left: 9px !important;
  }
}

.pl-9-px {
  padding-left: 9px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-9-px {
    padding-right: 9px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-9-px {
    padding-right: 9px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-9-px {
    padding-right: 9px !important;
  }
}

.pr-9-px {
  padding-right: 9px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-9 {
    padding-right: 9 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-9 {
    padding-right: 9 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-9 {
    padding-right: 9 !important;
  }
}

.pr-9 {
  padding-right: 9 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-9 {
    padding-bottom: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-9 {
    padding-bottom: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-9 {
    padding-bottom: 9rem !important;
  }
}

.pb-9 {
  padding-bottom: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-9 {
    padding-left: 9rem !important;
    padding-right: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-9 {
    padding-left: 9rem !important;
    padding-right: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-9 {
    padding-left: 9rem !important;
    padding-right: 9rem !important;
  }
}

.px-9 {
  padding-left: 9rem !important;
  padding-right: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-9-px {
    padding-left: 9px !important;
    padding-right: 9px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-9-px {
    padding-left: 9px !important;
    padding-right: 9px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-9-px {
    padding-left: 9px !important;
    padding-right: 9px !important;
  }
}

.px-9-px {
  padding-left: 9px !important;
  padding-right: 9px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-9 {
    padding-top: 9rem !important;
    padding-bottom: 9rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-9 {
    padding-top: 9rem !important;
    padding-bottom: 9rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-9 {
    padding-top: 9rem !important;
    padding-bottom: 9rem !important;
  }
}

.py-9 {
  padding-top: 9rem !important;
  padding-bottom: 9rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-9-px {
    padding-top: 9px !important;
    padding-bottom: 9px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-9-px {
    padding-top: 9px !important;
    padding-bottom: 9px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-9-px {
    padding-top: 9px !important;
    padding-bottom: 9px !important;
  }
}

.py-9-px {
  padding-top: 9px !important;
  padding-bottom: 9px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-10 {
    padding: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-10 {
    padding: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-10 {
    padding: 10rem !important;
  }
}

.p-10 {
  padding: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-10 {
    padding-top: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-10 {
    padding-top: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-10 {
    padding-top: 10rem !important;
  }
}

.pt-10 {
  padding-top: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-10 {
    padding-left: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-10 {
    padding-left: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-10 {
    padding-left: 10rem !important;
  }
}

.pl-10 {
  padding-left: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-10-px {
    padding-left: 10px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-10-px {
    padding-left: 10px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-10-px {
    padding-left: 10px !important;
  }
}

.pl-10-px {
  padding-left: 10px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-10-px {
    padding-right: 10px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-10-px {
    padding-right: 10px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-10-px {
    padding-right: 10px !important;
  }
}

.pr-10-px {
  padding-right: 10px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-10 {
    padding-right: 10 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-10 {
    padding-right: 10 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-10 {
    padding-right: 10 !important;
  }
}

.pr-10 {
  padding-right: 10 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-10 {
    padding-bottom: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-10 {
    padding-bottom: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-10 {
    padding-bottom: 10rem !important;
  }
}

.pb-10 {
  padding-bottom: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-10 {
    padding-left: 10rem !important;
    padding-right: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-10 {
    padding-left: 10rem !important;
    padding-right: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-10 {
    padding-left: 10rem !important;
    padding-right: 10rem !important;
  }
}

.px-10 {
  padding-left: 10rem !important;
  padding-right: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-10-px {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-10-px {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-10-px {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
}

.px-10-px {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-10 {
    padding-top: 10rem !important;
    padding-bottom: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-10 {
    padding-top: 10rem !important;
    padding-bottom: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-10 {
    padding-top: 10rem !important;
    padding-bottom: 10rem !important;
  }
}

.py-10 {
  padding-top: 10rem !important;
  padding-bottom: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-10-px {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-10-px {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-10-px {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }
}

.py-10-px {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-11 {
    padding: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-11 {
    padding: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-11 {
    padding: 11rem !important;
  }
}

.p-11 {
  padding: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-11 {
    padding-top: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-11 {
    padding-top: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-11 {
    padding-top: 11rem !important;
  }
}

.pt-11 {
  padding-top: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-11 {
    padding-left: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-11 {
    padding-left: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-11 {
    padding-left: 11rem !important;
  }
}

.pl-11 {
  padding-left: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-11-px {
    padding-left: 11px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-11-px {
    padding-left: 11px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-11-px {
    padding-left: 11px !important;
  }
}

.pl-11-px {
  padding-left: 11px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-11-px {
    padding-right: 11px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-11-px {
    padding-right: 11px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-11-px {
    padding-right: 11px !important;
  }
}

.pr-11-px {
  padding-right: 11px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-11 {
    padding-right: 11 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-11 {
    padding-right: 11 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-11 {
    padding-right: 11 !important;
  }
}

.pr-11 {
  padding-right: 11 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-11 {
    padding-bottom: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-11 {
    padding-bottom: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-11 {
    padding-bottom: 11rem !important;
  }
}

.pb-11 {
  padding-bottom: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-11 {
    padding-left: 11rem !important;
    padding-right: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-11 {
    padding-left: 11rem !important;
    padding-right: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-11 {
    padding-left: 11rem !important;
    padding-right: 11rem !important;
  }
}

.px-11 {
  padding-left: 11rem !important;
  padding-right: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-11-px {
    padding-left: 11px !important;
    padding-right: 11px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-11-px {
    padding-left: 11px !important;
    padding-right: 11px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-11-px {
    padding-left: 11px !important;
    padding-right: 11px !important;
  }
}

.px-11-px {
  padding-left: 11px !important;
  padding-right: 11px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-11 {
    padding-top: 11rem !important;
    padding-bottom: 11rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-11 {
    padding-top: 11rem !important;
    padding-bottom: 11rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-11 {
    padding-top: 11rem !important;
    padding-bottom: 11rem !important;
  }
}

.py-11 {
  padding-top: 11rem !important;
  padding-bottom: 11rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-11-px {
    padding-top: 11px !important;
    padding-bottom: 11px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-11-px {
    padding-top: 11px !important;
    padding-bottom: 11px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-11-px {
    padding-top: 11px !important;
    padding-bottom: 11px !important;
  }
}

.py-11-px {
  padding-top: 11px !important;
  padding-bottom: 11px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:p-12 {
    padding: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:p-12 {
    padding: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:p-12 {
    padding: 12rem !important;
  }
}

.p-12 {
  padding: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pt-12 {
    padding-top: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pt-12 {
    padding-top: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pt-12 {
    padding-top: 12rem !important;
  }
}

.pt-12 {
  padding-top: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-12 {
    padding-left: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-12 {
    padding-left: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-12 {
    padding-left: 12rem !important;
  }
}

.pl-12 {
  padding-left: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pl-12-px {
    padding-left: 12px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pl-12-px {
    padding-left: 12px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pl-12-px {
    padding-left: 12px !important;
  }
}

.pl-12-px {
  padding-left: 12px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-12-px {
    padding-right: 12px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-12-px {
    padding-right: 12px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-12-px {
    padding-right: 12px !important;
  }
}

.pr-12-px {
  padding-right: 12px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pr-12 {
    padding-right: 12 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pr-12 {
    padding-right: 12 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pr-12 {
    padding-right: 12 !important;
  }
}

.pr-12 {
  padding-right: 12 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:pb-12 {
    padding-bottom: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pb-12 {
    padding-bottom: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pb-12 {
    padding-bottom: 12rem !important;
  }
}

.pb-12 {
  padding-bottom: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-12 {
    padding-left: 12rem !important;
    padding-right: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-12 {
    padding-left: 12rem !important;
    padding-right: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-12 {
    padding-left: 12rem !important;
    padding-right: 12rem !important;
  }
}

.px-12 {
  padding-left: 12rem !important;
  padding-right: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:px-12-px {
    padding-left: 12px !important;
    padding-right: 12px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:px-12-px {
    padding-left: 12px !important;
    padding-right: 12px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:px-12-px {
    padding-left: 12px !important;
    padding-right: 12px !important;
  }
}

.px-12-px {
  padding-left: 12px !important;
  padding-right: 12px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-12 {
    padding-top: 12rem !important;
    padding-bottom: 12rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-12 {
    padding-top: 12rem !important;
    padding-bottom: 12rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-12 {
    padding-top: 12rem !important;
    padding-bottom: 12rem !important;
  }
}

.py-12 {
  padding-top: 12rem !important;
  padding-bottom: 12rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:py-12-px {
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:py-12-px {
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:py-12-px {
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
}

.py-12-px {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }
}

.flex {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
}

@media only screen and (max-width: 320px) {
  .sm\:block {
    display: block !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:block {
    display: block !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:block {
    display: block !important;
  }
}

.block {
  display: block !important;
}

@media only screen and (max-width: 320px) {
  .sm\:inline-block {
    display: inline-block !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:inline-block {
    display: inline-block !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:inline-block {
    display: inline-block !important;
  }
}

.inline-block {
  display: inline-block !important;
}

@media only screen and (max-width: 320px) {
  .sm\:inline {
    display: inline !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:inline {
    display: inline !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:inline {
    display: inline !important;
  }
}

.inline {
  display: inline !important;
}

@media only screen and (max-width: 320px) {
  .sm\:invisible {
    visibility: hidden !important;
    display: none !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:invisible {
    visibility: hidden !important;
    display: none !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:invisible {
    visibility: hidden !important;
    display: none !important;
  }
}

.invisible {
  visibility: hidden !important;
  display: none !important;
}

@media only screen and (max-width: 320px) {
  .sm\:font-weight-300 {
    font-weight: 300 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:font-weight-300 {
    font-weight: 300 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:font-weight-300 {
    font-weight: 300 !important;
  }
}

.font-weight-300 {
  font-weight: 300 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:font-weight-400 {
    font-weight: 400 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:font-weight-400 {
    font-weight: 400 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:font-weight-400 {
    font-weight: 400 !important;
  }
}

.font-weight-400 {
  font-weight: 400 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:font-weight-500 {
    font-weight: 500 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:font-weight-500 {
    font-weight: 500 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:font-weight-500 {
    font-weight: 500 !important;
  }
}

.font-weight-500 {
  font-weight: 500 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:font-weight-600 {
    font-weight: 600 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:font-weight-600 {
    font-weight: 600 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:font-weight-600 {
    font-weight: 600 !important;
  }
}

.font-weight-600 {
  font-weight: 600 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:font-weight-700 {
    font-weight: 700 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:font-weight-700 {
    font-weight: 700 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:font-weight-700 {
    font-weight: 700 !important;
  }
}

.font-weight-700 {
  font-weight: 700 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:font-weight-800 {
    font-weight: 800 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:font-weight-800 {
    font-weight: 800 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:font-weight-800 {
    font-weight: 800 !important;
  }
}

.font-weight-800 {
  font-weight: 800 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:font-weight-900 {
    font-weight: 900 !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:font-weight-900 {
    font-weight: 900 !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:font-weight-900 {
    font-weight: 900 !important;
  }
}

.font-weight-900 {
  font-weight: 900 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:bold {
    font-weight: bold;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:bold {
    font-weight: bold;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:bold {
    font-weight: bold;
  }
}

.bold {
  font-weight: bold;
}

@media only screen and (max-width: 320px) {
  .sm\:text-underline {
    text-decoration: underline !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:text-underline {
    text-decoration: underline !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:text-underline {
    text-decoration: underline !important;
  }
}

.text-underline {
  text-decoration: underline !important;
}

@media only screen and (max-width: 320px) {
  .sm\:no-underline {
    text-decoration: none !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:no-underline {
    text-decoration: none !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:no-underline {
    text-decoration: none !important;
  }
}

.no-underline {
  text-decoration: none !important;
}

@media only screen and (max-width: 320px) {
  .sm\:text-through {
    text-decoration: line-through !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:text-through {
    text-decoration: line-through !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:text-through {
    text-decoration: line-through !important;
  }
}

.text-through {
  text-decoration: line-through !important;
}

@media only screen and (max-width: 320px) {
  .sm\:text-center {
    text-align: center !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:text-center {
    text-align: center !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:text-center {
    text-align: center !important;
  }
}

.text-center {
  text-align: center !important;
}

@media only screen and (max-width: 320px) {
  .sm\:text-left {
    text-align: left !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:text-left {
    text-align: left !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:text-left {
    text-align: left !important;
  }
}

.text-left {
  text-align: left !important;
}

@media only screen and (max-width: 320px) {
  .sm\:text-right {
    text-align: right !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:text-right {
    text-align: right !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:text-right {
    text-align: right !important;
  }
}

.text-right {
  text-align: right !important;
}

@media only screen and (max-width: 320px) {
  .sm\:text-xs {
    font-size: 0.75rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:text-xs {
    font-size: 0.75rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:text-xs {
    font-size: 0.75rem !important;
  }
}

.text-xs {
  font-size: 0.75rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:text-sm {
    font-size: 0.875rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:text-sm {
    font-size: 0.875rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:text-sm {
    font-size: 0.875rem !important;
  }
}

.text-sm {
  font-size: 0.875rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:text-base {
    font-size: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:text-base {
    font-size: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:text-base {
    font-size: 1rem !important;
  }
}

.text-base {
  font-size: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:text-md {
    font-size: 1rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:text-md {
    font-size: 1rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:text-md {
    font-size: 1rem !important;
  }
}

.text-md {
  font-size: 1rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:text-lg {
    font-size: 1.25rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:text-lg {
    font-size: 1.25rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:text-lg {
    font-size: 1.25rem !important;
  }
}

.text-lg {
  font-size: 1.25rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:text-xl {
    font-size: 1.5rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:text-xl {
    font-size: 1.5rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:text-xl {
    font-size: 1.5rem !important;
  }
}

.text-xl {
  font-size: 1.5rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:text-xxl {
    font-size: 2rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:text-xxl {
    font-size: 2rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:text-xxl {
    font-size: 2rem !important;
  }
}

.text-xxl {
  font-size: 2rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:line-height-100 {
    line-height: 1;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:line-height-100 {
    line-height: 1;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:line-height-100 {
    line-height: 1;
  }
}

.line-height-100 {
  line-height: 1;
}

@media only screen and (max-width: 320px) {
  .sm\:line-height-110 {
    line-height: 1.1;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:line-height-110 {
    line-height: 1.1;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:line-height-110 {
    line-height: 1.1;
  }
}

.line-height-110 {
  line-height: 1.1;
}

.line-height-120 {
  line-height: 1.2;
}

.line-height-130 {
  line-height: 1.3;
}

.line-height-140 {
  line-height: 1.4;
}

.line-height-150 {
  line-height: 1.5;
}

.line-height-160 {
  line-height: 1.6;
}

.line-height-170 {
  line-height: 1.7;
}

.line-height-180 {
  line-height: 1.8;
}

.letter-spacing-1 {
  letter-spacing: .0625rem;
}

.letter-spacing-15 {
  letter-spacing: .09375rem;
}

.letter-spacing-2 {
  letter-spacing: 0.125rem;
}

.font-bold {
  font-weight: bold;
}

.font-thin {
  font-weight: lighter;
}

.text-blue {
  color: #5e72e4 !important;
}

.text-indigo {
  color: #5603ad !important;
}

.text-purple {
  color: #8965e0 !important;
}

.text-pink {
  color: #f3a4b5 !important;
}

.text-red {
  color: #ff0033 !important;
}

.text-orange {
  color: orange !important;
}

.text-orange-dark {
  color: #fb641e !important;
}

.text-yellow {
  color: #ffd600 !important;
}

.text-green {
  color: green !important;
}

.text-teal {
  color: #11cdef !important;
}

.text-cyan {
  color: #2bffc6 !important;
}

.text-white {
  color: #fff !important;
}

.text-gray {
  color: gray !important;
}

.text-light {
  color: #ced4da !important;
}

.text-lighter {
  color: #e9ecef !important;
}

.text-gray-dark {
  color: #8c8c8c !important;
}

.text-dark {
  color: #222222 !important;
}

.text-muted {
  color: gray !important;
}

.text-primary {
  color: #8cc474 !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-full {
    min-height: 100%;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-full {
    min-height: 100%;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-full {
    min-height: 100%;
  }
}

.min-h-full {
  min-height: 100%;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-full {
    min-width: 100% !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-full {
    min-width: 100% !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-full {
    min-width: 100% !important;
  }
}

.min-h-full {
  min-width: 100% !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-full {
    height: 100% !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-full {
    height: 100% !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-full {
    height: 100% !important;
  }
}

.h-full {
  height: 100% !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-screen {
    height: 100vh !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-screen {
    height: 100vh !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-screen {
    height: 100vh !important;
  }
}

.h-screen {
  height: 100vh !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-full {
    width: 100% !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-full {
    width: 100% !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-full {
    width: 100% !important;
  }
}

.w-full {
  width: 100% !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-1\/2 {
    width: 50% !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-1\/2 {
    width: 50% !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-1\/2 {
    width: 50% !important;
  }
}

.w-1\/2 {
  width: 50% !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-1\/3 {
    width: 33.3% !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-1\/3 {
    width: 33.3% !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-1\/3 {
    width: 33.3% !important;
  }
}

.w-1\/3 {
  width: 33.3% !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-1\/4 {
    width: 25% !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-1\/4 {
    width: 25% !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-1\/4 {
    width: 25% !important;
  }
}

.w-1\/4 {
  width: 25% !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-3\/4 {
    width: 75% !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-3\/4 {
    width: 75% !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-3\/4 {
    width: 75% !important;
  }
}

.w-3\/4 {
  width: 75% !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-full {
    width: 100% !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-full {
    width: 100% !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-full {
    width: 100% !important;
  }
}

.w-full {
  width: 100% !important;
}

@media only screen and (max-width: 320px) {
  .sm\:justify-between {
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:justify-between {
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:justify-between {
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
}

.justify-between {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media only screen and (max-width: 320px) {
  .sm\:justify-around {
    -ms-flex-pack: distribute;
        justify-content: space-around;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:justify-around {
    -ms-flex-pack: distribute;
        justify-content: space-around;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:justify-around {
    -ms-flex-pack: distribute;
        justify-content: space-around;
  }
}

.justify-around {
  -ms-flex-pack: distribute;
      justify-content: space-around;
}

@media only screen and (max-width: 320px) {
  .sm\:justify-center {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:justify-center {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:justify-center {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.justify-center {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

@media only screen and (max-width: 320px) {
  .sm\:justify-evenly {
    -webkit-box-pack: space-evenly;
        -ms-flex-pack: space-evenly;
            justify-content: space-evenly;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:justify-evenly {
    -webkit-box-pack: space-evenly;
        -ms-flex-pack: space-evenly;
            justify-content: space-evenly;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:justify-evenly {
    -webkit-box-pack: space-evenly;
        -ms-flex-pack: space-evenly;
            justify-content: space-evenly;
  }
}

.justify-evenly {
  -webkit-box-pack: space-evenly;
      -ms-flex-pack: space-evenly;
          justify-content: space-evenly;
}

@media only screen and (max-width: 320px) {
  .sm\:items-center {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:items-center {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:items-center {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

.items-center {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media only screen and (max-width: 320px) {
  .sm\:items-start {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: start;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:items-start {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: start;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:items-start {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: start;
  }
}

.items-start {
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: start;
}

@media only screen and (max-width: 320px) {
  .sm\:items-baseline {
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:items-baseline {
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:items-baseline {
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
  }
}

.items-baseline {
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
}

@media only screen and (max-width: 320px) {
  .sm\:items-end {
    -webkit-box-align: end;
        -ms-flex-align: end;
            align-items: end;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:items-end {
    -webkit-box-align: end;
        -ms-flex-align: end;
            align-items: end;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:items-end {
    -webkit-box-align: end;
        -ms-flex-align: end;
            align-items: end;
  }
}

.items-end {
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: end;
}

@media only screen and (max-width: 320px) {
  .sm\:w-10 {
    width: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-10 {
    width: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-10 {
    width: 10rem !important;
  }
}

.w-10 {
  width: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-10 -px {
    width: 10px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-10 -px {
    width: 10px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-10 -px {
    width: 10px !important;
  }
}

.w-10 -px {
  width: 10px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-w-10 {
    min-width: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-w-10 {
    min-width: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-w-10 {
    min-width: 10rem !important;
  }
}

.min-w-10 {
  min-width: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-10 {
    height: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-10 {
    height: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-10 {
    height: 10rem !important;
  }
}

.h-10 {
  height: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-10 {
    min-height: 10rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-10 {
    min-height: 10rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-10 {
    min-height: 10rem !important;
  }
}

.min-h-10 {
  min-height: 10rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-15 {
    min-height: 15rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-15 {
    min-height: 15rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-15 {
    min-height: 15rem !important;
  }
}

.min-h-15 {
  min-height: 15rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-20 {
    width: 20rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-20 {
    width: 20rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-20 {
    width: 20rem !important;
  }
}

.w-20 {
  width: 20rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-20 -px {
    width: 20px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-20 -px {
    width: 20px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-20 -px {
    width: 20px !important;
  }
}

.w-20 -px {
  width: 20px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-w-20 {
    min-width: 20rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-w-20 {
    min-width: 20rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-w-20 {
    min-width: 20rem !important;
  }
}

.min-w-20 {
  min-width: 20rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-20 {
    height: 20rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-20 {
    height: 20rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-20 {
    height: 20rem !important;
  }
}

.h-20 {
  height: 20rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-20 {
    min-height: 20rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-20 {
    min-height: 20rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-20 {
    min-height: 20rem !important;
  }
}

.min-h-20 {
  min-height: 20rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-25 {
    min-height: 25rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-25 {
    min-height: 25rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-25 {
    min-height: 25rem !important;
  }
}

.min-h-25 {
  min-height: 25rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-30 {
    width: 30rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-30 {
    width: 30rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-30 {
    width: 30rem !important;
  }
}

.w-30 {
  width: 30rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-30 -px {
    width: 30px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-30 -px {
    width: 30px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-30 -px {
    width: 30px !important;
  }
}

.w-30 -px {
  width: 30px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-w-30 {
    min-width: 30rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-w-30 {
    min-width: 30rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-w-30 {
    min-width: 30rem !important;
  }
}

.min-w-30 {
  min-width: 30rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-30 {
    height: 30rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-30 {
    height: 30rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-30 {
    height: 30rem !important;
  }
}

.h-30 {
  height: 30rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-30 {
    min-height: 30rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-30 {
    min-height: 30rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-30 {
    min-height: 30rem !important;
  }
}

.min-h-30 {
  min-height: 30rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-35 {
    min-height: 35rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-35 {
    min-height: 35rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-35 {
    min-height: 35rem !important;
  }
}

.min-h-35 {
  min-height: 35rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-40 {
    width: 40rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-40 {
    width: 40rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-40 {
    width: 40rem !important;
  }
}

.w-40 {
  width: 40rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-40 -px {
    width: 40px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-40 -px {
    width: 40px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-40 -px {
    width: 40px !important;
  }
}

.w-40 -px {
  width: 40px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-w-40 {
    min-width: 40rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-w-40 {
    min-width: 40rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-w-40 {
    min-width: 40rem !important;
  }
}

.min-w-40 {
  min-width: 40rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-40 {
    height: 40rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-40 {
    height: 40rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-40 {
    height: 40rem !important;
  }
}

.h-40 {
  height: 40rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-40 {
    min-height: 40rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-40 {
    min-height: 40rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-40 {
    min-height: 40rem !important;
  }
}

.min-h-40 {
  min-height: 40rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-45 {
    min-height: 45rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-45 {
    min-height: 45rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-45 {
    min-height: 45rem !important;
  }
}

.min-h-45 {
  min-height: 45rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-50 {
    width: 50rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-50 {
    width: 50rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-50 {
    width: 50rem !important;
  }
}

.w-50 {
  width: 50rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-50 -px {
    width: 50px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-50 -px {
    width: 50px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-50 -px {
    width: 50px !important;
  }
}

.w-50 -px {
  width: 50px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-w-50 {
    min-width: 50rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-w-50 {
    min-width: 50rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-w-50 {
    min-width: 50rem !important;
  }
}

.min-w-50 {
  min-width: 50rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-50 {
    height: 50rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-50 {
    height: 50rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-50 {
    height: 50rem !important;
  }
}

.h-50 {
  height: 50rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-50 {
    min-height: 50rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-50 {
    min-height: 50rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-50 {
    min-height: 50rem !important;
  }
}

.min-h-50 {
  min-height: 50rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-55 {
    min-height: 55rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-55 {
    min-height: 55rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-55 {
    min-height: 55rem !important;
  }
}

.min-h-55 {
  min-height: 55rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-60 {
    width: 60rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-60 {
    width: 60rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-60 {
    width: 60rem !important;
  }
}

.w-60 {
  width: 60rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-60 -px {
    width: 60px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-60 -px {
    width: 60px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-60 -px {
    width: 60px !important;
  }
}

.w-60 -px {
  width: 60px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-w-60 {
    min-width: 60rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-w-60 {
    min-width: 60rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-w-60 {
    min-width: 60rem !important;
  }
}

.min-w-60 {
  min-width: 60rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-60 {
    height: 60rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-60 {
    height: 60rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-60 {
    height: 60rem !important;
  }
}

.h-60 {
  height: 60rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-60 {
    min-height: 60rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-60 {
    min-height: 60rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-60 {
    min-height: 60rem !important;
  }
}

.min-h-60 {
  min-height: 60rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-65 {
    min-height: 65rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-65 {
    min-height: 65rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-65 {
    min-height: 65rem !important;
  }
}

.min-h-65 {
  min-height: 65rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-70 {
    width: 70rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-70 {
    width: 70rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-70 {
    width: 70rem !important;
  }
}

.w-70 {
  width: 70rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-70 -px {
    width: 70px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-70 -px {
    width: 70px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-70 -px {
    width: 70px !important;
  }
}

.w-70 -px {
  width: 70px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-w-70 {
    min-width: 70rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-w-70 {
    min-width: 70rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-w-70 {
    min-width: 70rem !important;
  }
}

.min-w-70 {
  min-width: 70rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-70 {
    height: 70rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-70 {
    height: 70rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-70 {
    height: 70rem !important;
  }
}

.h-70 {
  height: 70rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-70 {
    min-height: 70rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-70 {
    min-height: 70rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-70 {
    min-height: 70rem !important;
  }
}

.min-h-70 {
  min-height: 70rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-75 {
    min-height: 75rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-75 {
    min-height: 75rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-75 {
    min-height: 75rem !important;
  }
}

.min-h-75 {
  min-height: 75rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-80 {
    width: 80rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-80 {
    width: 80rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-80 {
    width: 80rem !important;
  }
}

.w-80 {
  width: 80rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-80 -px {
    width: 80px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-80 -px {
    width: 80px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-80 -px {
    width: 80px !important;
  }
}

.w-80 -px {
  width: 80px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-w-80 {
    min-width: 80rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-w-80 {
    min-width: 80rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-w-80 {
    min-width: 80rem !important;
  }
}

.min-w-80 {
  min-width: 80rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-80 {
    height: 80rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-80 {
    height: 80rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-80 {
    height: 80rem !important;
  }
}

.h-80 {
  height: 80rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-80 {
    min-height: 80rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-80 {
    min-height: 80rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-80 {
    min-height: 80rem !important;
  }
}

.min-h-80 {
  min-height: 80rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-85 {
    min-height: 85rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-85 {
    min-height: 85rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-85 {
    min-height: 85rem !important;
  }
}

.min-h-85 {
  min-height: 85rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-90 {
    width: 90rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-90 {
    width: 90rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-90 {
    width: 90rem !important;
  }
}

.w-90 {
  width: 90rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-90 -px {
    width: 90px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-90 -px {
    width: 90px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-90 -px {
    width: 90px !important;
  }
}

.w-90 -px {
  width: 90px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-w-90 {
    min-width: 90rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-w-90 {
    min-width: 90rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-w-90 {
    min-width: 90rem !important;
  }
}

.min-w-90 {
  min-width: 90rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-90 {
    height: 90rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-90 {
    height: 90rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-90 {
    height: 90rem !important;
  }
}

.h-90 {
  height: 90rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-90 {
    min-height: 90rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-90 {
    min-height: 90rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-90 {
    min-height: 90rem !important;
  }
}

.min-h-90 {
  min-height: 90rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-95 {
    min-height: 95rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-95 {
    min-height: 95rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-95 {
    min-height: 95rem !important;
  }
}

.min-h-95 {
  min-height: 95rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-100 {
    width: 100rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-100 {
    width: 100rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-100 {
    width: 100rem !important;
  }
}

.w-100 {
  width: 100rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-100 -px {
    width: 100px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-100 -px {
    width: 100px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-100 -px {
    width: 100px !important;
  }
}

.w-100 -px {
  width: 100px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-w-100 {
    min-width: 100rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-w-100 {
    min-width: 100rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-w-100 {
    min-width: 100rem !important;
  }
}

.min-w-100 {
  min-width: 100rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-100 {
    height: 100rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-100 {
    height: 100rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-100 {
    height: 100rem !important;
  }
}

.h-100 {
  height: 100rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-100 {
    min-height: 100rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-100 {
    min-height: 100rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-100 {
    min-height: 100rem !important;
  }
}

.min-h-100 {
  min-height: 100rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-105 {
    min-height: 105rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-105 {
    min-height: 105rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-105 {
    min-height: 105rem !important;
  }
}

.min-h-105 {
  min-height: 105rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-110 {
    width: 110rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-110 {
    width: 110rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-110 {
    width: 110rem !important;
  }
}

.w-110 {
  width: 110rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-110 -px {
    width: 110px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-110 -px {
    width: 110px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-110 -px {
    width: 110px !important;
  }
}

.w-110 -px {
  width: 110px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-w-110 {
    min-width: 110rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-w-110 {
    min-width: 110rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-w-110 {
    min-width: 110rem !important;
  }
}

.min-w-110 {
  min-width: 110rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-110 {
    height: 110rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-110 {
    height: 110rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-110 {
    height: 110rem !important;
  }
}

.h-110 {
  height: 110rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-110 {
    min-height: 110rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-110 {
    min-height: 110rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-110 {
    min-height: 110rem !important;
  }
}

.min-h-110 {
  min-height: 110rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-115 {
    min-height: 115rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-115 {
    min-height: 115rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-115 {
    min-height: 115rem !important;
  }
}

.min-h-115 {
  min-height: 115rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-120 {
    width: 120rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-120 {
    width: 120rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-120 {
    width: 120rem !important;
  }
}

.w-120 {
  width: 120rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:w-120 -px {
    width: 120px !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:w-120 -px {
    width: 120px !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:w-120 -px {
    width: 120px !important;
  }
}

.w-120 -px {
  width: 120px !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-w-120 {
    min-width: 120rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-w-120 {
    min-width: 120rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-w-120 {
    min-width: 120rem !important;
  }
}

.min-w-120 {
  min-width: 120rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:h-120 {
    height: 120rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:h-120 {
    height: 120rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:h-120 {
    height: 120rem !important;
  }
}

.h-120 {
  height: 120rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-120 {
    min-height: 120rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-120 {
    min-height: 120rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-120 {
    min-height: 120rem !important;
  }
}

.min-h-120 {
  min-height: 120rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:min-h-125 {
    min-height: 125rem !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:min-h-125 {
    min-height: 125rem !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:min-h-125 {
    min-height: 125rem !important;
  }
}

.min-h-125 {
  min-height: 125rem !important;
}

@media only screen and (max-width: 320px) {
  .sm\:no-shadow {
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:no-shadow {
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:no-shadow {
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
  }
}

.no-shadow {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

@media only screen and (max-width: 320px) {
  .sm\:shadow-sm {
    -webkit-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:shadow-sm {
    -webkit-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:shadow-sm {
    -webkit-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
  }
}

.shadow-sm {
  -webkit-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
}

@media only screen and (max-width: 320px) {
  .sm\:border-bottom {
    border-bottom: 1px solid #e9ecef !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:border-bottom {
    border-bottom: 1px solid #e9ecef !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:border-bottom {
    border-bottom: 1px solid #e9ecef !important;
  }
}

.border-bottom {
  border-bottom: 1px solid #e9ecef !important;
}

@media only screen and (max-width: 320px) {
  .sm\:border-right {
    border-right: 1px solid #e9ecef !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:border-right {
    border-right: 1px solid #e9ecef !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:border-right {
    border-right: 1px solid #e9ecef !important;
  }
}

.border-right {
  border-right: 1px solid #e9ecef !important;
}

@media only screen and (max-width: 320px) {
  .sm\:border-left {
    border-left: 1px solid #e9ecef !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:border-left {
    border-left: 1px solid #e9ecef !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:border-left {
    border-left: 1px solid #e9ecef !important;
  }
}

.border-left {
  border-left: 1px solid #e9ecef !important;
}

@media only screen and (max-width: 320px) {
  .sm\:border-top {
    border-top: 1px solid #e9ecef !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:border-top {
    border-top: 1px solid #e9ecef !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:border-top {
    border-top: 1px solid #e9ecef !important;
  }
}

.border-top {
  border-top: 1px solid #e9ecef !important;
}

@media only screen and (max-width: 320px) {
  .sm\:border-none {
    border: none !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:border-none {
    border: none !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:border-none {
    border: none !important;
  }
}

.border-none {
  border: none !important;
}

@media only screen and (max-width: 320px) {
  .sm\:border-left-none {
    border-left: none !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:border-left-none {
    border-left: none !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:border-left-none {
    border-left: none !important;
  }
}

.border-left-none {
  border-left: none !important;
}

@media only screen and (max-width: 320px) {
  .sm\:border-right-none {
    border-right: none !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:border-right-none {
    border-right: none !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:border-right-none {
    border-right: none !important;
  }
}

.border-right-none {
  border-right: none !important;
}

@media only screen and (max-width: 320px) {
  .sm\:border-top-none {
    border-top: none !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:border-top-none {
    border-top: none !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:border-top-none {
    border-top: none !important;
  }
}

.border-top-none {
  border-top: none !important;
}

@media only screen and (max-width: 320px) {
  .sm\:border-bottom-none {
    border-bottom: none !important;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:border-bottom-none {
    border-bottom: none !important;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:border-bottom-none {
    border-bottom: none !important;
  }
}

.border-bottom-none {
  border-bottom: none !important;
}

@media only screen and (max-width: 320px) {
  .sm\:sticky {
    position: -webkit-sticky;
    position: sticky;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:sticky {
    position: -webkit-sticky;
    position: sticky;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:sticky {
    position: -webkit-sticky;
    position: sticky;
  }
}

.sticky {
  position: -webkit-sticky;
  position: sticky;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

@media only screen and (max-width: 320px) {
  .sm\:pin-top {
    top: 0;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pin-top {
    top: 0;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pin-top {
    top: 0;
  }
}

.pin-top {
  top: 0;
}

@media only screen and (max-width: 320px) {
  .sm\:pin-right {
    right: 0;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pin-right {
    right: 0;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pin-right {
    right: 0;
  }
}

.pin-right {
  right: 0;
}

@media only screen and (max-width: 320px) {
  .sm\:pin-bottom {
    bottom: 0;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pin-bottom {
    bottom: 0;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pin-bottom {
    bottom: 0;
  }
}

.pin-bottom {
  bottom: 0;
}

@media only screen and (max-width: 320px) {
  .sm\:pin-left {
    left: 0;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:pin-left {
    left: 0;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:pin-left {
    left: 0;
  }
}

.pin-left {
  left: 0;
}

@media only screen and (max-width: 320px) {
  .sm\:z-5 {
    z-index: 5;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:z-5 {
    z-index: 5;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:z-5 {
    z-index: 5;
  }
}

.z-5 {
  z-index: 5;
}

@media only screen and (max-width: 320px) {
  .sm\:z-10 {
    z-index: 10;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:z-10 {
    z-index: 10;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:z-10 {
    z-index: 10;
  }
}

.z-10 {
  z-index: 10;
}

@media only screen and (max-width: 320px) {
  .sm\:z-20 {
    z-index: 20;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:z-20 {
    z-index: 20;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:z-20 {
    z-index: 20;
  }
}

.z-20 {
  z-index: 20;
}

@media only screen and (max-width: 320px) {
  .sm\:z-1000 {
    z-index: 1000;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:z-1000 {
    z-index: 1000;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:z-1000 {
    z-index: 1000;
  }
}

.z-1000 {
  z-index: 1000;
}

@media only screen and (max-width: 320px) {
  .sm\:flex-col {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:flex-col {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:flex-col {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}

.flex-col {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

@media only screen and (max-width: 320px) {
  .sm\:grow-1 {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:grow-1 {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:grow-1 {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
  }
}

.grow-1 {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

@media only screen and (max-width: 320px) {
  .sm\:shrink-1 {
    -ms-flex-negative: 1;
        flex-shrink: 1;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:shrink-1 {
    -ms-flex-negative: 1;
        flex-shrink: 1;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:shrink-1 {
    -ms-flex-negative: 1;
        flex-shrink: 1;
  }
}

.shrink-1 {
  -ms-flex-negative: 1;
      flex-shrink: 1;
}

@media only screen and (max-width: 320px) {
  .sm\:grow-2 {
    -webkit-box-flex: 2;
        -ms-flex-positive: 2;
            flex-grow: 2;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:grow-2 {
    -webkit-box-flex: 2;
        -ms-flex-positive: 2;
            flex-grow: 2;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:grow-2 {
    -webkit-box-flex: 2;
        -ms-flex-positive: 2;
            flex-grow: 2;
  }
}

.grow-2 {
  -webkit-box-flex: 2;
      -ms-flex-positive: 2;
          flex-grow: 2;
}

@media only screen and (max-width: 320px) {
  .sm\:shrink-2 {
    -ms-flex-negative: 2;
        flex-shrink: 2;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:shrink-2 {
    -ms-flex-negative: 2;
        flex-shrink: 2;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:shrink-2 {
    -ms-flex-negative: 2;
        flex-shrink: 2;
  }
}

.shrink-2 {
  -ms-flex-negative: 2;
      flex-shrink: 2;
}

@media only screen and (max-width: 320px) {
  .sm\:grow-3 {
    -webkit-box-flex: 3;
        -ms-flex-positive: 3;
            flex-grow: 3;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:grow-3 {
    -webkit-box-flex: 3;
        -ms-flex-positive: 3;
            flex-grow: 3;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:grow-3 {
    -webkit-box-flex: 3;
        -ms-flex-positive: 3;
            flex-grow: 3;
  }
}

.grow-3 {
  -webkit-box-flex: 3;
      -ms-flex-positive: 3;
          flex-grow: 3;
}

@media only screen and (max-width: 320px) {
  .sm\:shrink-3 {
    -ms-flex-negative: 3;
        flex-shrink: 3;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:shrink-3 {
    -ms-flex-negative: 3;
        flex-shrink: 3;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:shrink-3 {
    -ms-flex-negative: 3;
        flex-shrink: 3;
  }
}

.shrink-3 {
  -ms-flex-negative: 3;
      flex-shrink: 3;
}

@media only screen and (max-width: 320px) {
  .sm\:grow-4 {
    -webkit-box-flex: 4;
        -ms-flex-positive: 4;
            flex-grow: 4;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:grow-4 {
    -webkit-box-flex: 4;
        -ms-flex-positive: 4;
            flex-grow: 4;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:grow-4 {
    -webkit-box-flex: 4;
        -ms-flex-positive: 4;
            flex-grow: 4;
  }
}

.grow-4 {
  -webkit-box-flex: 4;
      -ms-flex-positive: 4;
          flex-grow: 4;
}

@media only screen and (max-width: 320px) {
  .sm\:shrink-4 {
    -ms-flex-negative: 4;
        flex-shrink: 4;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:shrink-4 {
    -ms-flex-negative: 4;
        flex-shrink: 4;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:shrink-4 {
    -ms-flex-negative: 4;
        flex-shrink: 4;
  }
}

.shrink-4 {
  -ms-flex-negative: 4;
      flex-shrink: 4;
}

@media only screen and (max-width: 320px) {
  .sm\:grow-5 {
    -webkit-box-flex: 5;
        -ms-flex-positive: 5;
            flex-grow: 5;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:grow-5 {
    -webkit-box-flex: 5;
        -ms-flex-positive: 5;
            flex-grow: 5;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:grow-5 {
    -webkit-box-flex: 5;
        -ms-flex-positive: 5;
            flex-grow: 5;
  }
}

.grow-5 {
  -webkit-box-flex: 5;
      -ms-flex-positive: 5;
          flex-grow: 5;
}

@media only screen and (max-width: 320px) {
  .sm\:shrink-5 {
    -ms-flex-negative: 5;
        flex-shrink: 5;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:shrink-5 {
    -ms-flex-negative: 5;
        flex-shrink: 5;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:shrink-5 {
    -ms-flex-negative: 5;
        flex-shrink: 5;
  }
}

.shrink-5 {
  -ms-flex-negative: 5;
      flex-shrink: 5;
}

@media only screen and (max-width: 320px) {
  .sm\:grow-6 {
    -webkit-box-flex: 6;
        -ms-flex-positive: 6;
            flex-grow: 6;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:grow-6 {
    -webkit-box-flex: 6;
        -ms-flex-positive: 6;
            flex-grow: 6;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:grow-6 {
    -webkit-box-flex: 6;
        -ms-flex-positive: 6;
            flex-grow: 6;
  }
}

.grow-6 {
  -webkit-box-flex: 6;
      -ms-flex-positive: 6;
          flex-grow: 6;
}

@media only screen and (max-width: 320px) {
  .sm\:shrink-6 {
    -ms-flex-negative: 6;
        flex-shrink: 6;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:shrink-6 {
    -ms-flex-negative: 6;
        flex-shrink: 6;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:shrink-6 {
    -ms-flex-negative: 6;
        flex-shrink: 6;
  }
}

.shrink-6 {
  -ms-flex-negative: 6;
      flex-shrink: 6;
}

@media only screen and (max-width: 320px) {
  .sm\:grow-7 {
    -webkit-box-flex: 7;
        -ms-flex-positive: 7;
            flex-grow: 7;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:grow-7 {
    -webkit-box-flex: 7;
        -ms-flex-positive: 7;
            flex-grow: 7;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:grow-7 {
    -webkit-box-flex: 7;
        -ms-flex-positive: 7;
            flex-grow: 7;
  }
}

.grow-7 {
  -webkit-box-flex: 7;
      -ms-flex-positive: 7;
          flex-grow: 7;
}

@media only screen and (max-width: 320px) {
  .sm\:shrink-7 {
    -ms-flex-negative: 7;
        flex-shrink: 7;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:shrink-7 {
    -ms-flex-negative: 7;
        flex-shrink: 7;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:shrink-7 {
    -ms-flex-negative: 7;
        flex-shrink: 7;
  }
}

.shrink-7 {
  -ms-flex-negative: 7;
      flex-shrink: 7;
}

@media only screen and (max-width: 320px) {
  .sm\:grow-8 {
    -webkit-box-flex: 8;
        -ms-flex-positive: 8;
            flex-grow: 8;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:grow-8 {
    -webkit-box-flex: 8;
        -ms-flex-positive: 8;
            flex-grow: 8;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:grow-8 {
    -webkit-box-flex: 8;
        -ms-flex-positive: 8;
            flex-grow: 8;
  }
}

.grow-8 {
  -webkit-box-flex: 8;
      -ms-flex-positive: 8;
          flex-grow: 8;
}

@media only screen and (max-width: 320px) {
  .sm\:shrink-8 {
    -ms-flex-negative: 8;
        flex-shrink: 8;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:shrink-8 {
    -ms-flex-negative: 8;
        flex-shrink: 8;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:shrink-8 {
    -ms-flex-negative: 8;
        flex-shrink: 8;
  }
}

.shrink-8 {
  -ms-flex-negative: 8;
      flex-shrink: 8;
}

@media only screen and (max-width: 320px) {
  .sm\:grow-9 {
    -webkit-box-flex: 9;
        -ms-flex-positive: 9;
            flex-grow: 9;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:grow-9 {
    -webkit-box-flex: 9;
        -ms-flex-positive: 9;
            flex-grow: 9;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:grow-9 {
    -webkit-box-flex: 9;
        -ms-flex-positive: 9;
            flex-grow: 9;
  }
}

.grow-9 {
  -webkit-box-flex: 9;
      -ms-flex-positive: 9;
          flex-grow: 9;
}

@media only screen and (max-width: 320px) {
  .sm\:shrink-9 {
    -ms-flex-negative: 9;
        flex-shrink: 9;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:shrink-9 {
    -ms-flex-negative: 9;
        flex-shrink: 9;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:shrink-9 {
    -ms-flex-negative: 9;
        flex-shrink: 9;
  }
}

.shrink-9 {
  -ms-flex-negative: 9;
      flex-shrink: 9;
}

@media only screen and (max-width: 320px) {
  .sm\:grow-10 {
    -webkit-box-flex: 10;
        -ms-flex-positive: 10;
            flex-grow: 10;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:grow-10 {
    -webkit-box-flex: 10;
        -ms-flex-positive: 10;
            flex-grow: 10;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:grow-10 {
    -webkit-box-flex: 10;
        -ms-flex-positive: 10;
            flex-grow: 10;
  }
}

.grow-10 {
  -webkit-box-flex: 10;
      -ms-flex-positive: 10;
          flex-grow: 10;
}

@media only screen and (max-width: 320px) {
  .sm\:shrink-10 {
    -ms-flex-negative: 10;
        flex-shrink: 10;
  }
}

@media only screen and (min-width: 321px) and (max-width: 1200px) {
  .md\:shrink-10 {
    -ms-flex-negative: 10;
        flex-shrink: 10;
  }
}

@media only screen and (min-width: 1200px) {
  .lg\:shrink-10 {
    -ms-flex-negative: 10;
        flex-shrink: 10;
  }
}

.shrink-10 {
  -ms-flex-negative: 10;
      flex-shrink: 10;
}

.has-error {
  outline-color: #ff0033;
}

.is-loading {
  position: fixed;
  height: 100vh;
  width: 100vw;
  left: 0;
  top: 0;
  background-color: rgba(50, 50, 50, 0.8);
  z-index: 1000;
}

.is-loading__spinner {
  position: absolute;
  color: #fff;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.direction-rtl {
  direction: rtl;
}

.direction-ltr {
  direction: ltr;
}

table.table thead {
  background-color: #8cc474;
}

table.table thead .sorting {
  background-color: #8cc474;
}

table.table thead .sorting_desc {
  background-color: #8cc474;
}

table.table thead .sorting_asc {
  background-color: #8cc474;
}

table.table.collapse-separate {
  border-collapse: separate;
}

table.table-fixed {
  table-layout: fixed;
  min-width: 100%;
}

table.table.table-print tr > td {
  padding: 5px;
}

table.table.table-print tr > th {
  padding: 5px;
}

table.table > thead th {
  background-color: #8cc474;
  color: white;
}

table.table > tfoot th {
  background-color: #8cc474;
  color: white;
}

table.table td.align-middle {
  vertical-align: middle;
}

table.table td.align-bottom {
  vertical-align: bottom;
}

table.table td.align-top {
  vertical-align: top;
}

table.table td.align-baseline {
  vertical-align: baseline;
}

.table-responsive {
  overflow-x: scroll;
}

.card {
  background-color: #fff;
  -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  margin-bottom: 30px;
}

.card .card-header {
  padding: 1rem;
}

.card .card-header p,
.card .card-header h1,
.card .card-header h2,
.card .card-header h3,
.card .card-header h4,
.card .card-header h5,
.card .card-header h6 {
  background-color: transparent;
}

.card .card-body {
  background-color: #fff;
  padding: 2rem;
}

.card-blue .card-header {
  background-color: #5e72e4;
}

.card-blue .card-footer {
  background-color: #5e72e4;
}

.card-indigo .card-header {
  background-color: #5603ad;
}

.card-indigo .card-footer {
  background-color: #5603ad;
}

.card-purple .card-header {
  background-color: #8965e0;
}

.card-purple .card-footer {
  background-color: #8965e0;
}

.card-pink .card-header {
  background-color: #f3a4b5;
}

.card-pink .card-footer {
  background-color: #f3a4b5;
}

.card-red .card-header {
  background-color: #ff0033;
}

.card-red .card-footer {
  background-color: #ff0033;
}

.card-orange .card-header {
  background-color: orange;
}

.card-orange .card-footer {
  background-color: orange;
}

.card-orange-dark .card-header {
  background-color: #fb641e;
}

.card-orange-dark .card-footer {
  background-color: #fb641e;
}

.card-yellow .card-header {
  background-color: #ffd600;
}

.card-yellow .card-footer {
  background-color: #ffd600;
}

.card-green .card-header {
  background-color: green;
}

.card-green .card-footer {
  background-color: green;
}

.card-teal .card-header {
  background-color: #11cdef;
}

.card-teal .card-footer {
  background-color: #11cdef;
}

.card-cyan .card-header {
  background-color: #2bffc6;
}

.card-cyan .card-footer {
  background-color: #2bffc6;
}

.card-white .card-header {
  background-color: #fff;
}

.card-white .card-footer {
  background-color: #fff;
}

.card-gray .card-header {
  background-color: gray;
}

.card-gray .card-footer {
  background-color: gray;
}

.card-light .card-header {
  background-color: #ced4da;
}

.card-light .card-footer {
  background-color: #ced4da;
}

.card-lighter .card-header {
  background-color: #e9ecef;
}

.card-lighter .card-footer {
  background-color: #e9ecef;
}

.card-gray-dark .card-header {
  background-color: #8c8c8c;
}

.card-gray-dark .card-footer {
  background-color: #8c8c8c;
}

.card-dark .card-header {
  background-color: #222222;
}

.card-dark .card-footer {
  background-color: #222222;
}

.card-muted .card-header {
  background-color: gray;
}

.card-muted .card-footer {
  background-color: gray;
}

.card-primary .card-header {
  background-color: #8cc474;
}

.card-primary .card-footer {
  background-color: #8cc474;
}

.widget-body.shadow {
  -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.35);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.35);
}

.comment .comment-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.comment .comment-text {
  border: none;
  border-bottom: 1px solid #dee2e6;
}

.blockquote {
  padding: 40px 10px 10px;
  position: relative;
  margin-bottom: 0;
  border-right: none;
  border-left: none;
}

.blockquote p:first-child {
  font-size: 16px;
  line-height: 1.4em;
  font-weight: 600px;
  text-align: center;
  margin-bottom: 10px;
}

.blockquote:before {
  position: absolute;
  font-family: 'FontAwesome';
  top: 10%;
  left: 10%;
  -webkit-transform: translateX(-10%);
          transform: translateX(-10%);
  content: "\F10D";
  font-size: 60px;
  color: rgba(30, 30, 30, 0.1);
}

.blockquote::after {
  content: "";
  top: 10px;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  position: absolute;
  border-bottom: 3px solid green;
  height: 3px;
  min-width: 100px;
}

.btna,
.btn.shiny {
  border-radius: 5px !important;
}

.nav {
  margin-bottom: 2%;
}

.logo {
  position: absolute;
  top: 15%;
  z-index: 99999999;
  left: 0;
}

.wizard-card .wizard-header h3 {
  font-weight: 200;
  text-align: center;
  margin-bottom: 10%;
  padding-left: 21%;
}

.avatar-thumbnail {
  width: 100px;
  height: 100px;
  display: inline-block;
  padding: 4px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.alert.alert-gray {
  color: #fff;
  background-color: #adb5bd;
  border-color: gray;
}

.calendars-month {
  width: auto;
}

.calendars-month .calendars-month-header select {
  height: auto;
  padding: 2px;
}

textarea,
input[type],
.form-control[type] {
  border: 1px solid #ccc;
  border-radius: 5px !important;
}

input + img {
  margin: 0 2px;
}

.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.vs__clear {
  margin: 0 8px;
}

input[type="search"].vs__search {
  padding-top: 1rem;
  padding-bottom: 1rem;
  border: 0;
}

input[type="search"].vs__search:focus {
  padding-top: 1rem;
  padding-bottom: 1rem;
  border: 1px solid #ccc;
}

.v-select svg {
  fill: current;
  color: #dcdde1;
}

