﻿/* http://keith-wood.name/calendars.html
   French localisation for Gregorian/Julian calendars for jQuery.
   <PERSON><PERSON><PERSON><PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['fr'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','Septem<PERSON>','Octobre','Novembre','Décembre'],
		monthNamesShort: ['Jan','Fév','<PERSON>','Avr','<PERSON>','<PERSON>',
		'Jul','A<PERSON><PERSON>','Sep','Oct','Nov','<PERSON>é<PERSON>'],
		dayNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON>','Mar<PERSON>','Mercredi','<PERSON><PERSON>','Vendredi','<PERSON><PERSON>'],
		dayNamesShort: ['<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>'],
		dayNamesMin: ['<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','V<PERSON>','Sa'],
		dateFormat: 'dd/mm/yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['fr'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['fr'];
	}
})(jQuery);
