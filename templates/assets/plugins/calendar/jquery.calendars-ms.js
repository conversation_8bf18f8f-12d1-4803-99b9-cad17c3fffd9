﻿/* http://keith-wood.name/calendars.html
   Malaysian localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['ms'] = {
		name: '<PERSON><PERSON>',
		epochs: ['BCE', 'CE'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','April','<PERSON>','<PERSON>',
		'<PERSON><PERSON>','<PERSON><PERSON>','September','Okto<PERSON>','November','Disember'],
		monthNamesShort: ['Jan','Feb','<PERSON>','Apr','<PERSON>','<PERSON>',
		'<PERSON>','<PERSON><PERSON>','Sep','Okt','Nov','Di<PERSON>'],
		dayNames: ['Ahad','Isnin','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>ma<PERSON>','Sabtu'],
		dayNamesShort: ['Aha','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>b'],
		dayNamesMin: ['Ah','<PERSON>','<PERSON>','<PERSON>','<PERSON>h','<PERSON>','Sa'],
		dateFormat: 'dd/mm/yyyy',
		firstDay: 0,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['ms'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['ms'];
	}
})(jQuery);
