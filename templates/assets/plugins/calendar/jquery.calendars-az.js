﻿/* http://keith-wood.name/calendars.html
   Azerbaijani localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['az'] = {
		name: 'Gregor<PERSON>',
		epochs: ['BCE', 'CE'],
		monthNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','May','<PERSON>yun',
		'<PERSON><PERSON><PERSON>','<PERSON>vq<PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON>yabr','<PERSON><PERSON><PERSON><PERSON>'],
		monthNamesShort: ['<PERSON>','Fev','<PERSON>','Apr','May','<PERSON>yun',
		'<PERSON>yu<PERSON>','Avq','<PERSON>','Okt','Noy','Dek'],
		dayNames: ['<PERSON>zar','<PERSON>zar ertəsi','Çərşənbə axşamı','<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON> axşamı','<PERSON><PERSON><PERSON><PERSON>','<PERSON>ənb<PERSON>'],
		dayNamesShort: ['B','Be','Ça','<PERSON>','Ca','C','Ş'],
		dayNamesMin: ['B','B','Ç','С','Ç','C','Ş'],
		dateFormat: 'dd.mm.yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['az'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['az'];
	}
})(jQuery);
