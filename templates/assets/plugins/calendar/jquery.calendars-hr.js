﻿/* http://keith-wood.name/calendars.html
   Croatian localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON><PERSON><PERSON>. */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['hr'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>sinac'],
		monthNamesShort: ['<PERSON>j','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','Lip',
		'Srp','Kol','Ruj','<PERSON><PERSON>','Stu','Pro'],
		dayNames: ['<PERSON><PERSON><PERSON><PERSON>','Ponedjeljak','U<PERSON><PERSON>','<PERSON><PERSON><PERSON>','Četvrtak','<PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON>'],
		dayNamesMin: ['<PERSON><PERSON>','<PERSON>','U<PERSON>','<PERSON>','<PERSON><PERSON>','Pe','Su'],
		dateFormat: 'dd.mm.yyyy.',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['hr'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['hr'];
	}
})(jQuery);
