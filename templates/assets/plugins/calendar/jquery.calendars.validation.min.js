﻿/* http://keith-wood.name/calendars.html
   Calendars Validation extension for jQuery 2.0.0.
   Requires <PERSON><PERSON><PERSON>'s Validation plugin (http://plugins.jquery.com/project/validate).
   Written by <PERSON> (kbwood{at}iinet.com.au).
   Available under the MIT (https://github.com/jquery/jquery/blob/master/MIT-LICENSE.txt) license. 
   Please attribute the author if you use it. */
(function($){if($.fn.validate){$.calendarsPicker.selectDateOrig=$.calendarsPicker.selectDate;$.extend($.calendarsPicker.regionalOptions[''],{validateDate:'Please enter a valid date',validateDateMin:'Please enter a date on or after {0}',validateDateMax:'Please enter a date on or before {0}',validateDateMinMax:'Please enter a date between {0} and {1}',validateDateCompare:'Please enter a date {0} {1}',validateDateToday:'today',validateDateOther:'the other date',validateDateEQ:'equal to',validateDateNE:'not equal to',validateDateLT:'before',validateDateGT:'after',validateDateLE:'not after',validateDateGE:'not before'});$.extend($.calendarsPicker.defaultOptions,$.calendarsPicker.regionalOptions['']);$.extend($.calendarsPicker,{selectDate:function(a,b){this.selectDateOrig(a,b);var c=$.calendarsPicker._getInst(a);if(!c.inline&&$.fn.validate){var d=$(a).parents('form').validate();if(d){d.element('#'+a.id)}}},errorPlacement:function(a,b){var c=$.calendarsPicker._getInst(b);if(c){a[c.options.isRTL?'insertBefore':'insertAfter'](c.trigger.length>0?c.trigger:b)}else{a.insertAfter(b)}},errorFormat:function(c,d){var e=($.calendarsPicker.curInst?$.calendarsPicker.curInst.get('dateFormat'):$.calendarsPicker.defaultOptions.dateFormat);$.each(d,function(a,b){c=c.replace(new RegExp('\\{'+a+'\\}','g'),b.formatDate(e)||'nothing')});return c}});var l=null;$.validator.addMethod('cpDate',function(a,b){l=b;return this.optional(b)||validateEach(a,b)},function(a){var b=$.calendarsPicker._getInst(l);var c=b.get('minDate');var d=b.get('maxDate');var e=$.calendarsPicker.defaultOptions;return(c&&d?$.calendarsPicker.errorFormat(e.validateDateMinMax,[c,d]):(c?$.calendarsPicker.errorFormat(e.validateDateMin,[c]):(d?$.calendarsPicker.errorFormat(e.validateDateMax,[d]):e.validateDate)))});function validateEach(a,b){var c=$.calendarsPicker._getInst(b);var d=(c.options.multiSelect?a.split(c.options.multiSeparator):(c.options.rangeSelect?a.split(c.options.rangeSeparator):[a]));var f=(c.options.multiSelect&&d.length<=c.options.multiSelect)||(!c.options.multiSelect&&c.options.rangeSelect&&d.length===2)||(!c.options.multiSelect&&!c.options.rangeSelect&&d.length===1);if(f){try{var g=c.get('dateFormat');var h=c.get('minDate');var j=c.get('maxDate');var k=$(b);$.each(d,function(i,v){d[i]=c.options.calendar.parseDate(g,v);f=f&&(!d[i]||(k.calendarsPicker('isSelectable',d[i])&&(!h||d[i].compareTo(h)!==-1)&&(!j||d[i].compareTo(j)!==+1)))})}catch(e){f=false}}if(f&&c.options.rangeSelect){f=(d[0].compareTo(d[1])!==+1)}return f}$.validator.addClassRules('cpDate',{cpDate:true});var m={equal:'eq',same:'eq',notEqual:'ne',notSame:'ne',lessThan:'lt',before:'lt',greaterThan:'gt',after:'gt',notLessThan:'ge',notBefore:'ge',notGreaterThan:'le',notAfter:'le'};$.validator.addMethod('cpCompareDate',function(a,b,c){if(this.optional(b)){return true}c=normaliseParams(c);var d=$(b).calendarsPicker('getDate');var e=extractOtherDate(b,c[1]);if(d.length===0||e.length===0){return true}l=b;var f=true;for(var i=0;i<d.length;i++){var g=d[i].compareTo(e[0]);switch(m[c[0]]||c[0]){case'eq':f=(g===0);break;case'ne':f=(g!==0);break;case'lt':f=(g<0);break;case'gt':f=(g>0);break;case'le':f=(g<=0);break;case'ge':f=(g>=0);break;default:f=true}if(!f){break}}return f},function(a){var b=$.calendarsPicker.defaultOptions;a=normaliseParams(a);var c=extractOtherDate(l,a[1],true);c=(a[1]==='today'?b.validateDateToday:(c.length?c[0].formatDate():b.validateDateOther));return b.validateDateCompare.replace(/\{0\}/,b['validateDate'+(m[a[0]]||a[0]).toUpperCase()]).replace(/\{1\}/,c)});function normaliseParams(a){if(typeof a==='string'){a=a.split(' ')}else if(!$.isArray(a)){var b=[];for(var c in a){b[0]=c;b[1]=a[c]}a=b}return a}function extractOtherDate(a,b,c){if(b.newDate&&b.extraInfo){return[b]}var d=$.calendarsPicker._getInst(a);var f=null;try{if(typeof b==='string'&&b!=='today'){f=d.options.calendar.parseDate(d.get('dateFormat'),b)}}catch(e){}f=(f?[f]:(b==='today'?[d.options.calendar.today()]:(c?[]:$(b).calendarsPicker('getDate'))));return f}}})(jQuery);