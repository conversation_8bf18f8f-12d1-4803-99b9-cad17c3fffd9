﻿/* http://keith-wood.name/calendars.html
   Esperanto localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['eo'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>',
		'<PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','Nov<PERSON><PERSON>','Dec<PERSON><PERSON>'],
		monthNamesShort: ['Jan','Feb','Mar','Apr','Maj','Jun',
		'Jul','Aŭg','Sep','Okt','Nov','Dec'],
		dayNames: ['Dimanĉo','<PERSON><PERSON>','<PERSON><PERSON>','Merk<PERSON>','Ĵaŭdo','Vendredo','Sabato'],
		dayNamesShort: ['Di<PERSON>','Lu<PERSON>','<PERSON>','<PERSON><PERSON>','Ĵaŭ','V<PERSON>','Sab'],
		dayNamesMin: ['<PERSON>','Lu','Ma','<PERSON>','Ĵa','Ve','Sa'],
		dateFormat: 'dd/mm/yyyy',
		firstDay: 0,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['eo'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['eo'];
	}
})(jQuery);
