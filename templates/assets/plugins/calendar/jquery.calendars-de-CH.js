﻿/* http://keith-wood.name/calendars.html
   Swiss-German localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON> & <PERSON>. */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['de-CH'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','April','<PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','August','September','<PERSON><PERSON><PERSON>','November','Dezember'],
		monthNamesShort: ['Jan','Feb','<PERSON><PERSON><PERSON>','Apr','<PERSON>','Jun',
		'Jul','Aug','Sep','Okt','Nov','Dez'],
		dayNames: ['Sonntag','<PERSON><PERSON>','Dienst<PERSON>','Mittwo<PERSON>','Donnerstag','Freitag','Samstag'],
		dayNamesShort: ['So','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>'],
		dayNamesMin: ['So','<PERSON>','<PERSON>','Mi','<PERSON>','<PERSON>','Sa'],
		dateFormat: 'dd.mm.yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['de-CH'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['de-CH'];
	}
})(jQuery);
