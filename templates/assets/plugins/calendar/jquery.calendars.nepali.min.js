﻿/* http://keith-wood.name/calendars.html
   Nepali calendar for jQuery v2.0.0.
   Written by <PERSON><PERSON> (ict.projects{at}nepal.inf.org) April 2013.
   Available under the MIT (https://github.com/jquery/jquery/blob/master/MIT-LICENSE.txt) license. 
   Please attribute the author if you use it. */
(function($){function NepaliCalendar(a){this.local=this.regionalOptions[a||'']||this.regionalOptions['']}NepaliCalendar.prototype=new $.calendars.baseCalendar;$.extend(NepaliCalendar.prototype,{name:'Nepali',jdEpoch:1700709.5,daysPerMonth:[31,31,32,32,31,30,30,29,30,29,30,30],hasYearZero:false,minMonth:1,firstMonth:1,minDay:1,daysPerYear:365,regionalOptions:{'':{name:'Nepali',epochs:['BBS','ABS'],monthNames:['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>'],monthNamesShort:['Bai','Je','As','Shra','Bha','Ash','Kar','Mang','Pau','Ma','Fal','Chai'],dayNames:['Aaitabaar','Sombaar','Manglbaar','Budhabaar','Bihibaar','Shukrabaar','Shanibaar'],dayNamesShort:['Aaita','Som','Mangl','Budha','Bihi','Shukra','Shani'],dayNamesMin:['Aai','So','Man','Bu','Bi','Shu','Sha'],dateFormat:'dd/mm/yyyy',firstDay:1,isRTL:false}},leapYear:function(a){return this.daysInYear(a)!==this.daysPerYear},weekOfYear:function(a,b,c){var d=this.newDate(a,b,c);d.add(-d.dayOfWeek(),'d');return Math.floor((d.dayOfYear()-1)/7)+1},daysInYear:function(a){var b=this._validate(a,this.minMonth,this.minDay,$.calendars.local.invalidYear);a=b.year();if(typeof this.NEPALI_CALENDAR_DATA[a]==='undefined'){return this.daysPerYear}var c=0;for(var d=this.minMonth;d<=12;d++){c+=this.NEPALI_CALENDAR_DATA[a][d]}return c},daysInMonth:function(a,b){if(a.year){b=a.month();a=a.year()}this._validate(a,b,this.minDay,$.calendars.local.invalidMonth);return(typeof this.NEPALI_CALENDAR_DATA[a]==='undefined'?this.daysPerMonth[b-1]:this.NEPALI_CALENDAR_DATA[a][b])},weekDay:function(a,b,c){return this.dayOfWeek(a,b,c)!==6},toJD:function(a,b,c){var d=this._validate(a,b,c,$.calendars.local.invalidDate);a=d.year();b=d.month();c=d.day();var e=$.calendars.instance();var f=0;var g=b;var h=a;this._createMissingCalendarData(a);var i=a-(g>9||(g===9&&c>=this.NEPALI_CALENDAR_DATA[h][0])?56:57);if(b!==9){f=c;g--}while(g!==9){if(g<=0){g=12;h--}f+=this.NEPALI_CALENDAR_DATA[h][g];g--}if(b===9){f+=c-this.NEPALI_CALENDAR_DATA[h][0];if(f<=0){f+=(e.leapYear(i)?366:365)}}else{f+=this.NEPALI_CALENDAR_DATA[h][9]-this.NEPALI_CALENDAR_DATA[h][0]}return e.newDate(i,1,1).add(f,'d').toJD()},fromJD:function(a){var b=$.calendars.instance();var c=b.fromJD(a);var d=c.year();var e=c.dayOfYear();var f=d+56;this._createMissingCalendarData(f);var g=9;var h=this.NEPALI_CALENDAR_DATA[f][0];var i=this.NEPALI_CALENDAR_DATA[f][g]-h+1;while(e>i){g++;if(g>12){g=1;f++}i+=this.NEPALI_CALENDAR_DATA[f][g]}var j=this.NEPALI_CALENDAR_DATA[f][g]-(i-e);return this.newDate(f,g,j)},_createMissingCalendarData:function(a){var b=this.daysPerMonth.slice(0);b.unshift(17);for(var c=(a-1);c<(a+2);c++){if(typeof this.NEPALI_CALENDAR_DATA[c]==='undefined'){this.NEPALI_CALENDAR_DATA[c]=b}}},NEPALI_CALENDAR_DATA:{1970:[18,31,31,32,31,31,31,30,29,30,29,30,30],1971:[18,31,31,32,31,32,30,30,29,30,29,30,30],1972:[17,31,32,31,32,31,30,30,30,29,29,30,30],1973:[19,30,32,31,32,31,30,30,30,29,30,29,31],1974:[19,31,31,32,30,31,31,30,29,30,29,30,30],1975:[18,31,31,32,32,30,31,30,29,30,29,30,30],1976:[17,31,32,31,32,31,30,30,30,29,29,30,31],1977:[18,31,32,31,32,31,31,29,30,29,30,29,31],1978:[18,31,31,32,31,31,31,30,29,30,29,30,30],1979:[18,31,31,32,32,31,30,30,29,30,29,30,30],1980:[17,31,32,31,32,31,30,30,30,29,29,30,31],1981:[18,31,31,31,32,31,31,29,30,30,29,30,30],1982:[18,31,31,32,31,31,31,30,29,30,29,30,30],1983:[18,31,31,32,32,31,30,30,29,30,29,30,30],1984:[17,31,32,31,32,31,30,30,30,29,29,30,31],1985:[18,31,31,31,32,31,31,29,30,30,29,30,30],1986:[18,31,31,32,31,31,31,30,29,30,29,30,30],1987:[18,31,32,31,32,31,30,30,29,30,29,30,30],1988:[17,31,32,31,32,31,30,30,30,29,29,30,31],1989:[18,31,31,31,32,31,31,30,29,30,29,30,30],1990:[18,31,31,32,31,31,31,30,29,30,29,30,30],1991:[18,31,32,31,32,31,30,30,29,30,29,30,30],1992:[17,31,32,31,32,31,30,30,30,29,30,29,31],1993:[18,31,31,31,32,31,31,30,29,30,29,30,30],1994:[18,31,31,32,31,31,31,30,29,30,29,30,30],1995:[17,31,32,31,32,31,30,30,30,29,29,30,30],1996:[17,31,32,31,32,31,30,30,30,29,30,29,31],1997:[18,31,31,32,31,31,31,30,29,30,29,30,30],1998:[18,31,31,32,31,31,31,30,29,30,29,30,30],1999:[17,31,32,31,32,31,30,30,30,29,29,30,31],2000:[17,30,32,31,32,31,30,30,30,29,30,29,31],2001:[18,31,31,32,31,31,31,30,29,30,29,30,30],2002:[18,31,31,32,32,31,30,30,29,30,29,30,30],2003:[17,31,32,31,32,31,30,30,30,29,29,30,31],2004:[17,30,32,31,32,31,30,30,30,29,30,29,31],2005:[18,31,31,32,31,31,31,30,29,30,29,30,30],2006:[18,31,31,32,32,31,30,30,29,30,29,30,30],2007:[17,31,32,31,32,31,30,30,30,29,29,30,31],2008:[17,31,31,31,32,31,31,29,30,30,29,29,31],2009:[18,31,31,32,31,31,31,30,29,30,29,30,30],2010:[18,31,31,32,32,31,30,30,29,30,29,30,30],2011:[17,31,32,31,32,31,30,30,30,29,29,30,31],2012:[17,31,31,31,32,31,31,29,30,30,29,30,30],2013:[18,31,31,32,31,31,31,30,29,30,29,30,30],2014:[18,31,31,32,32,31,30,30,29,30,29,30,30],2015:[17,31,32,31,32,31,30,30,30,29,29,30,31],2016:[17,31,31,31,32,31,31,29,30,30,29,30,30],2017:[18,31,31,32,31,31,31,30,29,30,29,30,30],2018:[18,31,32,31,32,31,30,30,29,30,29,30,30],2019:[17,31,32,31,32,31,30,30,30,29,30,29,31],2020:[17,31,31,31,32,31,31,30,29,30,29,30,30],2021:[18,31,31,32,31,31,31,30,29,30,29,30,30],2022:[17,31,32,31,32,31,30,30,30,29,29,30,30],2023:[17,31,32,31,32,31,30,30,30,29,30,29,31],2024:[17,31,31,31,32,31,31,30,29,30,29,30,30],2025:[18,31,31,32,31,31,31,30,29,30,29,30,30],2026:[17,31,32,31,32,31,30,30,30,29,29,30,31],2027:[17,30,32,31,32,31,30,30,30,29,30,29,31],2028:[17,31,31,32,31,31,31,30,29,30,29,30,30],2029:[18,31,31,32,31,32,30,30,29,30,29,30,30],2030:[17,31,32,31,32,31,30,30,30,30,30,30,31],2031:[17,31,32,31,32,31,31,31,31,31,31,31,31],2032:[17,32,32,32,32,32,32,32,32,32,32,32,32],2033:[18,31,31,32,32,31,30,30,29,30,29,30,30],2034:[17,31,32,31,32,31,30,30,30,29,29,30,31],2035:[17,30,32,31,32,31,31,29,30,30,29,29,31],2036:[17,31,31,32,31,31,31,30,29,30,29,30,30],2037:[18,31,31,32,32,31,30,30,29,30,29,30,30],2038:[17,31,32,31,32,31,30,30,30,29,29,30,31],2039:[17,31,31,31,32,31,31,29,30,30,29,30,30],2040:[17,31,31,32,31,31,31,30,29,30,29,30,30],2041:[18,31,31,32,32,31,30,30,29,30,29,30,30],2042:[17,31,32,31,32,31,30,30,30,29,29,30,31],2043:[17,31,31,31,32,31,31,29,30,30,29,30,30],2044:[17,31,31,32,31,31,31,30,29,30,29,30,30],2045:[18,31,32,31,32,31,30,30,29,30,29,30,30],2046:[17,31,32,31,32,31,30,30,30,29,29,30,31],2047:[17,31,31,31,32,31,31,30,29,30,29,30,30],2048:[17,31,31,32,31,31,31,30,29,30,29,30,30],2049:[17,31,32,31,32,31,30,30,30,29,29,30,30],2050:[17,31,32,31,32,31,30,30,30,29,30,29,31],2051:[17,31,31,31,32,31,31,30,29,30,29,30,30],2052:[17,31,31,32,31,31,31,30,29,30,29,30,30],2053:[17,31,32,31,32,31,30,30,30,29,29,30,30],2054:[17,31,32,31,32,31,30,30,30,29,30,29,31],2055:[17,31,31,32,31,31,31,30,29,30,30,29,30],2056:[17,31,31,32,31,32,30,30,29,30,29,30,30],2057:[17,31,32,31,32,31,30,30,30,29,29,30,31],2058:[17,30,32,31,32,31,30,30,30,29,30,29,31],2059:[17,31,31,32,31,31,31,30,29,30,29,30,30],2060:[17,31,31,32,32,31,30,30,29,30,29,30,30],2061:[17,31,32,31,32,31,30,30,30,29,29,30,31],2062:[17,30,32,31,32,31,31,29,30,29,30,29,31],2063:[17,31,31,32,31,31,31,30,29,30,29,30,30],2064:[17,31,31,32,32,31,30,30,29,30,29,30,30],2065:[17,31,32,31,32,31,30,30,30,29,29,30,31],2066:[17,31,31,31,32,31,31,29,30,30,29,29,31],2067:[17,31,31,32,31,31,31,30,29,30,29,30,30],2068:[17,31,31,32,32,31,30,30,29,30,29,30,30],2069:[17,31,32,31,32,31,30,30,30,29,29,30,31],2070:[17,31,31,31,32,31,31,29,30,30,29,30,30],2071:[17,31,31,32,31,31,31,30,29,30,29,30,30],2072:[17,31,32,31,32,31,30,30,29,30,29,30,30],2073:[17,31,32,31,32,31,30,30,30,29,29,30,31],2074:[17,31,31,31,32,31,31,30,29,30,29,30,30],2075:[17,31,31,32,31,31,31,30,29,30,29,30,30],2076:[16,31,32,31,32,31,30,30,30,29,29,30,30],2077:[17,31,32,31,32,31,30,30,30,29,30,29,31],2078:[17,31,31,31,32,31,31,30,29,30,29,30,30],2079:[17,31,31,32,31,31,31,30,29,30,29,30,30],2080:[16,31,32,31,32,31,30,30,30,29,29,30,30],2081:[17,31,31,32,32,31,30,30,30,29,30,30,30],2082:[17,31,32,31,32,31,30,30,30,29,30,30,30],2083:[17,31,31,32,31,31,30,30,30,29,30,30,30],2084:[17,31,31,32,31,31,30,30,30,29,30,30,30],2085:[17,31,32,31,32,31,31,30,30,29,30,30,30],2086:[17,31,32,31,32,31,30,30,30,29,30,30,30],2087:[16,31,31,32,31,31,31,30,30,29,30,30,30],2088:[16,30,31,32,32,30,31,30,30,29,30,30,30],2089:[17,31,32,31,32,31,30,30,30,29,30,30,30],2090:[17,31,32,31,32,31,30,30,30,29,30,30,30],2091:[16,31,31,32,31,31,31,30,30,29,30,30,30],2092:[16,31,31,32,32,31,30,30,30,29,30,30,30],2093:[17,31,32,31,32,31,30,30,30,29,30,30,30],2094:[17,31,31,32,31,31,30,30,30,29,30,30,30],2095:[17,31,31,32,31,31,31,30,29,30,30,30,30],2096:[17,30,31,32,32,31,30,30,29,30,29,30,30],2097:[17,31,32,31,32,31,30,30,30,29,30,30,30],2098:[17,31,31,32,31,31,31,29,30,29,30,30,31],2099:[17,31,31,32,31,31,31,30,29,29,30,30,30],2100:[17,31,32,31,32,30,31,30,29,30,29,30,30]}});$.calendars.calendars.nepali=NepaliCalendar})(jQuery);