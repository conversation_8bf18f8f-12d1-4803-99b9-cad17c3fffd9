﻿/* http://keith-wood.name/calendars.html
   Serbian localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON>. */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['sr-<PERSON>'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','April','<PERSON>','<PERSON>',
		'<PERSON>','Av<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','Decembar'],
		monthNamesShort: ['Jan','Feb','Mar','Apr','Maj','Jun','Jul','Avg','Sep','Okt','Nov','Dec'],
		dayNames: ['<PERSON><PERSON><PERSON>','Ponedeljak','Utorak','Sr<PERSON>','Četvrta<PERSON>','<PERSON><PERSON>','Subota'],
		dayNamesShort: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON>'],
		dayNamesMin: ['<PERSON><PERSON>','<PERSON>','Ut','Sr','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>'],
		dateFormat: 'dd/mm/yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['sr-SR'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['sr-SR'];
	}
})(jQuery);
