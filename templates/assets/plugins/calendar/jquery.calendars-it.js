﻿/* http://keith-wood.name/calendars.html
   Italian localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['it'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','Novembre','Dice<PERSON>'],
		monthNamesShort: ['Gen','Feb','Mar','Apr','Mag','G<PERSON>',
		'Lug','Ago','<PERSON>','Ott','Nov','Dic'],
		dayNames: ['Dome<PERSON>','Lunedì','Martedì','Mercoled<PERSON>','Giovedì','Venerdì','Sabato'],
		dayNamesShort: ['<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>b'],
		dayNamesMin: ['<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON><PERSON>','V<PERSON>','Sa'],
		dateFormat: 'dd/mm/yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['it'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['it'];
	}
})(jQuery);
