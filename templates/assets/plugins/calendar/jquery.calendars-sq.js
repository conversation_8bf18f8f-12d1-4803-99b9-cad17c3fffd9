﻿/* http://keith-wood.name/calendars.html
   Albanian localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['sq'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
		monthNamesShort: ['<PERSON>','<PERSON>hk','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>ë<PERSON>','Dhj'],
		dayNames: ['<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>'],
		dayNamesShort: ['<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>h'],
		dayNamesMin: ['<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON>r','Sh'],
		dateFormat: 'dd.mm.yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['sq'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['sq'];
	}
})(jQuery);
