﻿/* http://keith-wood.name/calendars.html
   Faroese localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON><PERSON>, <EMAIL> */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['fo'] = {
		name: '<PERSON><PERSON><PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','August','September','Okto<PERSON>','November','Desember'],
		monthNamesShort: ['Jan','Feb','Mar','Apr','<PERSON>','<PERSON>',
		'Jul','Aug','Sep','Okt','Nov','<PERSON>'],
		dayNames: ['<PERSON><PERSON><PERSON>gu<PERSON>','M<PERSON><PERSON>gur','<PERSON><PERSON><PERSON>dagur','<PERSON><PERSON>dagur','H<PERSON>dagur','Fríggjadagur','Leyardagur'],
		dayNamesShort: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON>'],
		dateFormat: 'dd-mm-yyyy',
		firstDay: 0,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['fo'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['fo'];
	}
})(jQuery);
