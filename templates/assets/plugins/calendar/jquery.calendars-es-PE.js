﻿/* http://keith-wood.name/calendars.html
   Spanish/Perú localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON> (<EMAIL>) of ASIX (http://www.asixonline.com). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['es-PE'] = {
		name: '<PERSON><PERSON>',
		epochs: ['BCE', 'CE'],
		monthNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>',
		'<PERSON>','A<PERSON><PERSON>','Septiembre','Octubre','Noviembre','Diciembre'],
		monthNamesShort: ['Ene','Feb','Mar','Abr','May','Jun',
		'Jul','Ago','Sep','Oct','Nov','Dic'],
		dayNames: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','Vier<PERSON>','S<PERSON><PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','Vi','Sa'],
		dateFormat: 'dd/mm/yyyy',
		firstDay: 0,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['es-PE'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['es-PE'];
	}
})(jQuery);
