﻿/* http://keith-wood.name/calendars.html
   Indonesian localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['id'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','April','<PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','<PERSON><PERSON><PERSON>','September','Okto<PERSON>','Nopember','Desember'],
		monthNamesShort: ['Jan','Feb','Mar','Apr','<PERSON>','<PERSON>',
		'<PERSON>','<PERSON><PERSON>','<PERSON>','Okt','Nop','<PERSON>'],
		dayNames: ['<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','Sabtu'],
		dayNamesShort: ['<PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','kam','<PERSON><PERSON>','Sab'],
		dayNamesMin: ['Mg','Sn','Sl','Rb','Km','jm','Sb'],
		dateFormat: 'dd/mm/yyyy',
		firstDay: 0,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['id'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['id'];
	}
})(jQuery);
