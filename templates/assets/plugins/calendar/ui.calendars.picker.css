/* Theme<PERSON>oller override style sheet for jQuery Calendars Picker v2.0.0. */
.ui-datepicker {
	display: block;
}
#ui-datepicker-div,
.ui-datepicker-inline {
	width: 17em;
	font-size: 75%;
}
#ui-datepicker-div {
	z-index: 100;
}
.ui-datepicker-inline {
	float: left;
}
.ui-datepicker-rtl {
	direction: rtl;
}
#ui-datepicker-div a,
.ui-datepicker-inline a {
	text-decoration: none;
}
.ui-datepicker-prompt {
	height: 1.5em;
	padding-top: 0.25em;
	text-align: center;
}
button.ui-datepicker-cmd {
	height: 2em;
}
.ui-datepicker-cmd-clear {
	float: left;
	margin-left: 0.25em;
}
.ui-datepicker-cmd-close {
	float: right;
	margin-right: 0.25em;
}
.ui-datepicker-cmd-prev {
	position: static;
	float: left;
	width: 30%;
	height: auto;
	margin-left: 1%;
}
.ui-datepicker-cmd-next {
	position: static;
	float: right;
	width: 30%;
	height: auto;
	margin-right: 1%;
	text-align: right;
}
.ui-datepicker-cmd-current,
.ui-datepicker-cmd-today {
	float: left;
	width: 37%;
	text-align: center;
}
.ui-datepicker-month-nav {
	float: left;
	text-align: center;
}
.ui-datepicker-month-nav div {
	float: left;
	width: 12.5%;
	margin: 1%;
	padding: 1%;
}
.ui-datepicker-month-nav span {
	color: #888;
}
.ui-datepicker-row-break {
	width: 100%;
	font-size: 100%;
}
.ui-datepicker-group {
	float: left;
	width: 17em;
}
.ui-datepicker-group .ui-datepicker-header {
	height: 1.5em;
	text-align: center;
}
.ui-datepicker select,
.ui-datepicker-inline select {
	width: auto;
	height: 1.66em;
	border: none;
	font-weight: bold;
}
.ui-datepicker th {
	padding: 0.5em 0.3em;
}
.ui-datepicker td,
.ui-datepicker td a,
.ui-datepicker td span {
	border: 1px solid transparent;
	text-align: center;
}
.ui-datepicker-status {
	padding: 0.25em 0em;
	text-align: center;
}
.ui-datepicker .ui-helper-clearfix {
	clear: both;
}
