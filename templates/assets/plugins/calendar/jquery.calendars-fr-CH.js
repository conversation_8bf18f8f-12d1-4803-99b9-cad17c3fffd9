﻿/* http://keith-wood.name/calendars.html
   Swiss French localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['fr-CH'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', 'CE'],
		monthNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','Septembre','Octobre','Novembre','Décembre'],
		monthNamesShort: ['Jan','Fév','<PERSON>','Avr','<PERSON>','<PERSON>',
		'Jul','<PERSON><PERSON><PERSON>','Sep','Oct','Nov','Déc'],
		dayNames: ['<PERSON><PERSON>che','<PERSON><PERSON>','<PERSON><PERSON>','Merc<PERSON>i','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesShort: ['<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>'],
		dayNamesMin: ['<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','V<PERSON>','Sa'],
		dateFormat: 'dd.mm.yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['fr-CH'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['fr-CH'];
	}
})(jQuery);
