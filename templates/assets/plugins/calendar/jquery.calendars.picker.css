/* Default styling for jQuery Calendars Picker v2.0.0. */
.calendars {
	background-color: #000;
	color: #000;
	border: 1px solid #444;
    -moz-border-radius: 0.25em;
    -webkit-border-radius: 0.25em;
    border-radius: 0.25em;
	font-family: Arial,Helvetica,Sans-serif;
	font-size: 15px;
}
.calendars-rtl {
	direction: ltr;
}
.calendars-popup {
	z-index: 1000;
}
.calendars-disable {
	position: absolute;
	z-index: 100;
	background-color: white;
	opacity: 0.5;
	filter: alpha(opacity=50);
}
.calendars a {
	color: #fff;
	text-decoration: none;
}
.calendars a.calendars-disabled {
	color: #888;
	cursor: auto;
}
.calendars button {
    margin: 0.25em;
    padding: 0.125em 0em;
    background-color: #fcc;
    border: none;
    -moz-border-radius: 0.25em;
    -webkit-border-radius: 0.25em;
    border-radius: 0.25em;
    font-weight: bold;
}
.calendars-nav, .calendars-ctrl {
	float: left;
	width: 100%;
	background-color: #000;
	color: #fff;
	font-size: 90%;
	font-weight: bold;
}
.calendars-ctrl {
	background-color: #600;
}
.calendars-cmd {
	width: 30%;
}
.calendars-cmd:hover {
	background-color: #777;
}
.calendars-ctrl .calendars-cmd:hover {
	background-color: #f08080;
}
.calendars-cmd-prevJump, .calendars-cmd-nextJump {
	width: 50%;
}
a.calendars-cmd {
	height: 1.5em;
}
button.calendars-cmd {
	text-align: center;
}
.calendars-cmd-prev, .calendars-cmd-prevJump{
	float: left;
	text-align: left;
	width: 50%;
}
.calendars-cmd-current, .calendars-cmd-today {
	float: left;
	text-align: center;
	width: 34%;
}
.calendars-cmd-next, .calendars-cmd-nextJump {
	float: left;
	text-align: right;
	width: 50%;
}

.calendars-rtl .calendars-cmd-clear {
	float: center;
	text-align: center;
	width: 50%;
}

.calendars-rtl .calendars-cmd-current, .calendars-rtl .calendars-cmd-today {
	float: center;
}

.calendars-rtl .calendars-cmd-close {
	float: left;
	text-align: center;
	width: 50%
}
.calendars-month-nav {
	float: left;
	background-color: #ffffff;
	text-align: center;
}
.calendars-month-nav div {
	float: center;
	width: 5%;
	margin: 1%;
	padding: 1%;
}
.calendars-month-nav span {
	color: #888;
}
.calendars-month-row {
	clear: left;
}
.calendars-month {
	float: center;
	width: 18em;
	border: 1px solid #444;
	text-align: center;
}
.calendars-month-header, .calendars-month-header select, .calendars-month-header input {
	height: 2em;
	background-color: #444;
	color: #fff;
	font-weight: bold;
}
.calendars-month-header select, .calendars-month-header input {
	height: 2em;
	width: 9em;
	border: none;
}
.calendars-month-header input {
	position: absolute;
	display: none;
}
.calendars-month table {
	width: 100%;
	border-collapse: collapse;
}
.calendars-month thead {
	border-bottom: 1px solid #aaa;
}
.calendars-month th, .calendars-month td {
	margin: 0em;
	padding: 0em;
	font-weight: normal;
	text-align: center;
}
.calendars-month th {
	border: 1px solid #777;
}
.calendars-month th, .calendars-month th a {
	background-color: #777;
	color: #fff;
}
.calendars-month td {
	background-color: #eee;
	border: 1px solid #aaa;
}
.calendars-month td.calendars-week {
	border: 1px solid #777;
}
.calendars-month td.calendars-week * {
	background-color: #777;
	color: #fff;
	border: none;
}
.calendars-month a {
	display: block;
	width: 100%;
	padding: 0.125em 0em;
	background-color: #eee;
	color: #000;
	text-decoration: none;
}
.calendars-month span {
	display: block;
	width: 100%;
	padding: 0.125em 0em;
}
.calendars-month td span {
	color: #888;
}
.calendars-month td .calendars-other-month {
	background-color: #fff;
}
.calendars-month td .calendars-weekend {
	background-color: #ddd;
}
.calendars-month td .calendars-today {
	background-color: #ffffff;
}
.calendars-month td .calendars-highlight {
	background-color: #f08080;
}
.calendars-month td .calendars-selected {
	background-color: #777;
	color: #fff;
}
.calendars-month th.calendars-week {
	background-color: #777;
	color: #fff;
}
.calendars-status {
	clear: both;
	background-color: #ddd;
	text-align: center;
}
.calendars-clear-fix {
	clear: both;
}
