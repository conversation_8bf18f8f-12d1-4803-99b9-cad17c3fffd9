﻿/* http://keith-wood.name/calendars.html
   Romansh localisation for Gregorian/Julian calendars for jQuery.
   <PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['rm'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON>vust','<PERSON><PERSON><PERSON>','October','November','December'],
		monthNamesShort: ['<PERSON>ha','Fev','Mar','Avr','Matg','<PERSON>er',
		'<PERSON>','Avu','Sett','Oct','Nov','Dec'],
		dayNames: ['Du<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','Mesemna','<PERSON>ie<PERSON><PERSON>','Vender<PERSON>','Son<PERSON>'],
		dayNamesShort: ['<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','Som'],
		dayNamesMin: ['<PERSON>','G<PERSON>','Ma','Me','G<PERSON>','Ve','So'],
		dateFormat: 'dd/mm/yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['rm'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['rm'];
	}
})(jQuery);
