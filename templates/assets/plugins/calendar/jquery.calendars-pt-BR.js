﻿/* http://keith-wood.name/calendars.html
   Brazilian Portuguese localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['pt-BR'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['Janeiro','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
		monthNamesShort: ['Jan','<PERSON>v','<PERSON>','<PERSON>b<PERSON>','<PERSON>','<PERSON>',
		'<PERSON>','<PERSON><PERSON>','<PERSON>','Out','Nov','De<PERSON>'],
		dayNames: ['<PERSON>','Segunda-feira','Ter<PERSON>-feira','Quarta-feira','<PERSON>uinta-feira','<PERSON><PERSON>-feira','Sábad<PERSON>'],
		dayNamesShort: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','Seg','<PERSON>r','<PERSON><PERSON>','<PERSON>ui','Sex','Sáb'],
		dateFormat: 'dd/mm/yyyy',
		firstDay: 0,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['pt-BR'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['pt-BR'];
	}
})(jQuery);
