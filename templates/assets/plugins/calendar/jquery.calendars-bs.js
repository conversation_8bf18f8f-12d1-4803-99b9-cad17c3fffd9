﻿/* http://keith-wood.name/calendars.html
   Bosnian localisation for Gregorian/Julian calendars for jQuery.
   <PERSON><PERSON>. */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['bs'] = {
		name: 'Gregor<PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','April','<PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','August','Septembar','<PERSON><PERSON><PERSON>','<PERSON>embar','Decembar'],
		monthNamesShort: ['Jan','Feb','<PERSON>','Apr','<PERSON>','Jun',
		'Jul','Aug','<PERSON>','Okt','Nov','Dec'],
		dayNames: ['<PERSON><PERSON><PERSON>','Po<PERSON>el<PERSON>','U<PERSON><PERSON>','Sri<PERSON><PERSON>','Č<PERSON>vrta<PERSON>','<PERSON><PERSON>','Sub<PERSON>'],
		dayNamesShort: ['<PERSON>','<PERSON>n','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON>'],
		dayNamesMin: ['<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','Sr','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>'],
		dateFormat: 'dd.mm.yy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['bs'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['bs'];
	}
})(jQuery);
