﻿/* http://keith-wood.name/calendars.html
   Slovenian localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON> (<EMAIL>). */
/* c = &#x10D;, s = &#x161; z = &#x17E; C = &#x10C; S = &#x160; Z = &#x17D; */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['sl'] = {
		name: '<PERSON><PERSON>',
		epochs: ['BCE', 'CE'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','April','Maj','Junij',
		'Julij','Avgust','September','Oktober','November','December'],
		monthNamesShort: ['Jan','Feb','Mar','Apr','Maj','Jun',
		'Jul','Avg','Sep','Okt','Nov','Dec'],
		dayNames: ['<PERSON><PERSON><PERSON>','Ponedelje<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','&#x10C;etrtek','<PERSON><PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','Po<PERSON>','<PERSON>','Sre','&#x10C;et','<PERSON>','Sob'],
		dayNamesMin: ['Ne','Po','To','Sr','&#x10C;e','Pe','So'],
		dateFormat: 'dd.mm.yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['sl'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['sl'];
	}
})(jQuery);
