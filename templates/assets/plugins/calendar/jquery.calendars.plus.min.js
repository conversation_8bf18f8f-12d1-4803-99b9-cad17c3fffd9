﻿/* http://keith-wood.name/calendars.html
   Calendars extras for jQuery v2.0.0.
   Written by <PERSON> (kbwood{at}iinet.com.au) August 2009.
   Available under the MIT (https://github.com/jquery/jquery/blob/master/MIT-LICENSE.txt) license. 
   Please attribute the author if you use it. */
(function($){$.extend($.calendars.regionalOptions[''],{invalidArguments:'Invalid arguments',invalidFormat:'Cannot format a date from another calendar',missingNumberAt:'Missing number at position {0}',unknownNameAt:'Unknown name at position {0}',unexpectedLiteralAt:'Unexpected literal at position {0}',unexpectedText:'Additional text found at end'});$.calendars.local=$.calendars.regionalOptions[''];$.extend($.calendars.cdate.prototype,{formatDate:function(a){return this._calendar.formatDate(a||'',this)}});$.extend($.calendars.baseCalendar.prototype,{UNIX_EPOCH:$.calendars.instance().newDate(1970,1,1).toJD(),SECS_PER_DAY:24*60*60,TICKS_EPOCH:$.calendars.instance().jdEpoch,TICKS_PER_DAY:24*60*60*10000000,ATOM:'yyyy-mm-dd',COOKIE:'D, dd M yyyy',FULL:'DD, MM d, yyyy',ISO_8601:'yyyy-mm-dd',JULIAN:'J',RFC_822:'D, d M yy',RFC_850:'DD, dd-M-yy',RFC_1036:'D, d M yy',RFC_1123:'D, d M yyyy',RFC_2822:'D, d M yyyy',RSS:'D, d M yy',TICKS:'!',TIMESTAMP:'@',W3C:'yyyy-mm-dd',formatDate:function(f,g,h){if(typeof f!=='string'){h=g;g=f;f=''}if(!g){return''}if(g.calendar()!==this){throw $.calendars.local.invalidFormat||$.calendars.regionalOptions[''].invalidFormat;}f=f||this.local.dateFormat;h=h||{};var i=h.dayNamesShort||this.local.dayNamesShort;var j=h.dayNames||this.local.dayNames;var k=h.monthNamesShort||this.local.monthNamesShort;var l=h.monthNames||this.local.monthNames;var m=h.calculateWeek||this.local.calculateWeek;var n=function(a,b){var c=1;while(s+c<f.length&&f.charAt(s+c)===a){c++}s+=c-1;return Math.floor(c/(b||1))>1};var o=function(a,b,c,d){var e=''+b;if(n(a,d)){while(e.length<c){e='0'+e}}return e};var p=function(a,b,c,d){return(n(a)?d[b]:c[b])};var q='';var r=false;for(var s=0;s<f.length;s++){if(r){if(f.charAt(s)==="'"&&!n("'")){r=false}else{q+=f.charAt(s)}}else{switch(f.charAt(s)){case'd':q+=o('d',g.day(),2);break;case'D':q+=p('D',g.dayOfWeek(),i,j);break;case'o':q+=o('o',g.dayOfYear(),3);break;case'w':q+=o('w',g.weekOfYear(),2);break;case'm':q+=o('m',g.month(),2);break;case'M':q+=p('M',g.month()-this.minMonth,k,l);break;case'y':q+=(n('y',2)?g.year():(g.year()%100<10?'0':'')+g.year()%100);break;case'Y':n('Y',2);q+=g.formatYear();break;case'J':q+=g.toJD();break;case'@':q+=(g.toJD()-this.UNIX_EPOCH)*this.SECS_PER_DAY;break;case'!':q+=(g.toJD()-this.TICKS_EPOCH)*this.TICKS_PER_DAY;break;case"'":if(n("'")){q+="'"}else{r=true}break;default:q+=f.charAt(s)}}}return q},parseDate:function(g,h,j){if(h==null){throw $.calendars.local.invalidArguments||$.calendars.regionalOptions[''].invalidArguments;}h=(typeof h==='object'?h.toString():h+'');if(h===''){return null}g=g||this.local.dateFormat;j=j||{};var k=j.shortYearCutoff||this.shortYearCutoff;k=(typeof k!=='string'?k:this.today().year()%100+parseInt(k,10));var l=j.dayNamesShort||this.local.dayNamesShort;var m=j.dayNames||this.local.dayNames;var n=j.monthNamesShort||this.local.monthNamesShort;var o=j.monthNames||this.local.monthNames;var p=-1;var q=-1;var r=-1;var s=-1;var t=-1;var u=false;var v=false;var w=function(a,b){var c=1;while(C+c<g.length&&g.charAt(C+c)===a){c++}C+=c-1;return Math.floor(c/(b||1))>1};var x=function(a,b){var c=w(a,b);var d=[2,3,c?4:2,c?4:2,10,11,20]['oyYJ@!'.indexOf(a)+1];var e=new RegExp('^-?\\d{1,'+d+'}');var f=h.substring(B).match(e);if(!f){throw($.calendars.local.missingNumberAt||$.calendars.regionalOptions[''].missingNumberAt).replace(/\{0\}/,B)}B+=f[0].length;return parseInt(f[0],10)};var y=this;var z=function(a,b,c,d){var e=(w(a,d)?c:b);for(var i=0;i<e.length;i++){if(h.substr(B,e[i].length).toLowerCase()===e[i].toLowerCase()){B+=e[i].length;return i+y.minMonth}}throw($.calendars.local.unknownNameAt||$.calendars.regionalOptions[''].unknownNameAt).replace(/\{0\}/,B)};var A=function(){if(h.charAt(B)!==g.charAt(C)){throw($.calendars.local.unexpectedLiteralAt||$.calendars.regionalOptions[''].unexpectedLiteralAt).replace(/\{0\}/,B)}B++};var B=0;for(var C=0;C<g.length;C++){if(v){if(g.charAt(C)==="'"&&!w("'")){v=false}else{A()}}else{switch(g.charAt(C)){case'd':s=x('d');break;case'D':z('D',l,m);break;case'o':t=x('o');break;case'w':x('w');break;case'm':r=x('m');break;case'M':r=z('M',n,o);break;case'y':var D=C;u=!w('y',2);C=D;q=x('y',2);break;case'Y':q=x('Y',2);break;case'J':p=x('J')+0.5;if(h.charAt(B)==='.'){B++;x('J')}break;case'@':p=x('@')/this.SECS_PER_DAY+this.UNIX_EPOCH;break;case'!':p=x('!')/this.TICKS_PER_DAY+this.TICKS_EPOCH;break;case'*':B=h.length;break;case"'":if(w("'")){A()}else{v=true}break;default:A()}}}if(B<h.length){throw $.calendars.local.unexpectedText||$.calendars.regionalOptions[''].unexpectedText;}if(q===-1){q=this.today().year()}else if(q<100&&u){q+=(k===-1?1900:this.today().year()-this.today().year()%100-(q<=k?0:100))}if(t>-1){r=1;s=t;for(var E=this.daysInMonth(q,r);s>E;E=this.daysInMonth(q,r)){r++;s-=E}}return(p>-1?this.fromJD(p):this.newDate(q,r,s))},determineDate:function(f,g,h,i,j){if(h&&typeof h!=='object'){j=i;i=h;h=null}if(typeof i!=='string'){j=i;i=''}var k=this;var l=function(a){try{return k.parseDate(i,a,j)}catch(e){}a=a.toLowerCase();var b=(a.match(/^c/)&&h?h.newDate():null)||k.today();var c=/([+-]?[0-9]+)\s*(d|w|m|y)?/g;var d=c.exec(a);while(d){b.add(parseInt(d[1],10),d[2]||'d');d=c.exec(a)}return b};g=(g?g.newDate():null);f=(f==null?g:(typeof f==='string'?l(f):(typeof f==='number'?(isNaN(f)||f===Infinity||f===-Infinity?g:k.today().add(f,'d')):k.newDate(f))));return f}})})(jQuery);