﻿/* http://keith-wood.name/calendars.html
   Spanish localisation for Gregorian/Julian calendars for jQuery.
   Traducido por Vester (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['es'] = {
		name: '<PERSON><PERSON>',
		epochs: ['BCE', 'CE'],
		monthNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>',
		'<PERSON>','Agos<PERSON>','Septiembre','Octubre','Novi<PERSON><PERSON>','Diciembre'],
		monthNamesShort: ['Ene','Feb','Mar','Abr','May','Jun',
		'Jul','Ago','Sep','Oct','Nov','Dic'],
		dayNames: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','Viernes','Sábado'],
		dayNamesShort: ['<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','V<PERSON>','<PERSON><PERSON>'],
		dateFormat: 'dd/mm/yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['es'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['es'];
	}
})(jQuery);
