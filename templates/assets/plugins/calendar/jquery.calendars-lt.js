﻿/* http://keith-wood.name/calendars.html
   Lithuanian localisation for Gregorian/Julian calendars for jQuery.
   Arturas <PERSON> <<EMAIL>>. */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['lt'] = {
		name: '<PERSON><PERSON>',
		epochs: ['BCE', 'CE'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>',
		'<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
		monthNamesShort: ['Sau','Vas','Kov','Bal','Geg','Bir',
		'Lie','Rugp','Rugs','Spa','Lap','Gru'],
		dayNames: ['sekmadienis','pirmadienis','antradienis','tre<PERSON>ia<PERSON><PERSON>','ketvirtadien<PERSON>','penktadi<PERSON>s','še<PERSON><PERSON>ien<PERSON>'],
		dayNamesShort: ['sek','pir','ant','tre','ket','pen','še<PERSON>'],
		dayNamesMin: ['Se','Pr','An','Tr','Ke','Pe','Še'],
		dateFormat: 'yyyy-mm-dd',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['lt'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['lt'];
	}
})(jQuery);
