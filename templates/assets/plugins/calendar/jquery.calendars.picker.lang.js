﻿/* http://keith-wood.name/calendars.html
   Calendars date picker localisations for jQuery v2.0.0.
   Written by <PERSON> (kbwood{at}iinet.com.au) August 2009.
   Available under the MIT (https://github.com/jquery/jquery/blob/master/MIT-LICENSE.txt) license. 
   Please attribute the author if you use it. */

/* http://keith-wood.name/calendars.html
   Afrikaans localisation for calendars datepicker for jQuery.
   Written by <PERSON><PERSON> and <PERSON><PERSON><PERSON>. */
(function($) {
	$.calendarsPicker.regionalOptions['af'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'Vorige', prevStatus: 'Vertoon vorige maand',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Vertoon vorige jaar',
		nextText: 'Volgende', nextStatus: 'Vertoon volgende maand',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Vertoon volgende jaar',
		currentText: 'Vandag', currentStatus: 'Vertoon huidige maand',
		todayText: 'Vandag', todayStatus: 'Vertoon huidige maand',
		clearText: 'Vee uit', clearStatus: 'Verwyder die huidige datum',
		closeText: 'Klaar', closeStatus: 'Sluit sonder verandering',
		yearStatus: 'Vertoon \'n ander jaar', monthStatus: 'Vertoon \'n ander maand',
		weekText: 'Wk', weekStatus: 'Week van die jaar',
		dayStatus: 'Kies DD, M d', defaultStatus: 'Kies \'n datum',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['af']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Amharic (አማርኛ) localisation for calendars datepicker for jQuery.
   Leyu Sisay. */
(function($) {
	$.calendarsPicker.regionalOptions['am'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'ያለፈ', prevStatus: 'ያለፈውን ወር አሳይ',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'ያለፈውን ዓመት አሳይ',
		nextText: 'ቀጣይ', nextStatus: 'ቀጣዩን ወር አሳይ',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'ቀጣዩን ዓመት አሳይ',
		currentText: 'አሁን', currentStatus: 'የአሁኑን ወር አሳይ',
		todayText: 'ዛሬ', todayStatus: 'የዛሬን ወር አሳይ',
		clearText: 'አጥፋ', clearStatus: 'የተመረጠውን ቀን አጥፋ',
		closeText: 'ዝጋ', closeStatus: 'የቀን መምረጫውን ዝጋ',
		yearStatus: 'ዓመቱን ቀይር', monthStatus: 'ወሩን ቀይር',
		weekText: 'ሳም', weekStatus: 'የዓመቱ ሳምንት ',
		dayStatus: 'DD, M d, yyyy ምረጥ', defaultStatus: 'ቀን ምረጥ',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['am']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Algerian (and Tunisian) Arabic localisation for calendars datepicker for jQuery.
   Mohamed Cherif BOUCHELAGHEM -- <EMAIL> */
(function($) {
	$.calendarsPicker.regionalOptions['ar-DZ'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;السابق', prevStatus: 'عرض الشهر السابق',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'التالي&#x3e;', nextStatus: 'عرض الشهر القادم',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'اليوم', currentStatus: 'عرض الشهر الحالي',
		todayText: 'اليوم', todayStatus: 'عرض الشهر الحالي',
		clearText: 'مسح', clearStatus: 'امسح التاريخ الحالي',
		closeText: 'إغلاق', closeStatus: 'إغلاق بدون حفظ',
		yearStatus: 'عرض سنة آخرى', monthStatus: 'عرض شهر آخر',
		weekText: 'أسبوع', weekStatus: 'أسبوع السنة',
		dayStatus: 'اختر D, M d', defaultStatus: 'اختر يوم',
		isRTL: true
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ar-DZ']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Arabic localisation for calendars datepicker for jQuery.
   Mahmoud Khaled -- <EMAIL>
   NOTE: monthNames are the new months names */
(function($) {
	$.calendarsPicker.regionalOptions['ar-EG'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;السابق', prevStatus: 'عرض الشهر السابق',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'التالي&#x3e;', nextStatus: 'عرض الشهر القادم',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'اليوم', currentStatus: 'عرض الشهر الحالي',
		todayText: 'اليوم', todayStatus: 'عرض الشهر الحالي',
		clearText: 'مسح', clearStatus: 'امسح التاريخ الحالي',
		closeText: 'إغلاق', closeStatus: 'إغلاق بدون حفظ',
		yearStatus: 'عرض سنة آخرى', monthStatus: 'عرض شهر آخر',
		weekText: 'أسبوع', weekStatus: 'أسبوع السنة',
		dayStatus: 'اختر D, M d', defaultStatus: 'اختر يوم',
		isRTL: true
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ar-EG']);
})(jQuery);
/* http://keith-wood.name/calendars.html
   Arabic localisation for calendars datepicker for jQuery.
   Khaled Al Horani -- خالد الحوراني -- <EMAIL> */
(function($) {
	$.calendarsPicker.regionalOptions['ar'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;السابق', prevStatus: 'عرض الشهر السابق',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'التالي&#x3e;', nextStatus: 'عرض الشهر القادم',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'اليوم', currentStatus: 'عرض الشهر الحالي',
		todayText: 'اليوم', todayStatus: 'عرض الشهر الحالي',
		clearText: 'مسح', clearStatus: 'امسح التاريخ الحالي',
		closeText: 'إغلاق', closeStatus: 'إغلاق بدون حفظ',
		yearStatus: 'عرض سنة آخرى', monthStatus: 'عرض شهر آخر',
		weekText: 'أسبوع', weekStatus: 'أسبوع السنة',
		dayStatus: 'اختر D, M d', defaultStatus: 'اختر يوم',
		isRTL: true
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ar']);
})(jQuery);
/* http://keith-wood.name/calendars.html
   Azerbaijani localisation for calendars datepicker for jQuery.
   Written by Jamil Najafov (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['az'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Geri',  prevStatus: 'Əvvəlki ay',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Əvvəlki il',
		nextText: 'İrəli&#x3e;', nextStatus: 'Sonrakı ay',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Sonrakı il',
		currentText: 'Bugün', currentStatus: 'İndiki ay',
		todayText: 'Bugün', todayStatus: 'İndiki ay',
		clearText: 'Təmizlə', clearStatus: 'Tarixi sil',
		closeText: 'Bağla', closeStatus: 'Təqvimi bağla',
		yearStatus: 'Başqa il', monthStatus: 'Başqa ay',
		weekText: 'Hf', weekStatus: 'Həftələr',
		dayStatus: 'D, M d seçin', defaultStatus: 'Bir tarix seçin',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['az']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Bulgarian localisation for calendars datepicker for jQuery.
   Written by Stoyan Kyosev (http://svest.org). */
(function($) {
	$.calendarsPicker.regionalOptions['bg'] = {
		renderer: $.calendarsPicker.defaultRenderer,
        prevText: '&#x3c;назад', prevStatus: 'покажи последния месец',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
        nextText: 'напред&#x3e;', nextStatus: 'покажи следващия месец',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
        currentText: 'днес', currentStatus: '',
		todayText: 'днес', todayStatus: '',
		clearText: 'изчисти', clearStatus: 'изчисти актуалната дата',
        closeText: 'затвори', closeStatus: 'затвори без промени',
		yearStatus: 'покажи друга година', monthStatus: 'покажи друг месец',
		weekText: 'Wk', weekStatus: 'седмица от месеца',
		dayStatus: 'Избери D, M d', defaultStatus: 'Избери дата',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['bg']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Bosnian localisation for calendars datepicker for jQuery.
   Kenan Konjo. */
(function($) {
	$.calendarsPicker.regionalOptions['bs'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: '&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Danas', currentStatus: '',
		todayText: 'Danas', todayStatus: '',
		clearText: 'X', clearStatus: '',
		closeText: 'Zatvori', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Wk', weekStatus: '',
		dayStatus: '', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['bs']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Catalan localisation for calendars datepicker for jQuery.
   Writers: (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['ca'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Ant', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Seg&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Avui', currentStatus: '',
		todayText: 'Avui', todayStatus: '',
		clearText: 'Netejar', clearStatus: '',
		closeText: 'Tancar', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Wk', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ca']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Czech localisation for calendars datepicker for jQuery.
   Written by Tomas Muller (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['cs'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Dříve', prevStatus: 'Přejít na předchozí měsí',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Později&#x3e;', nextStatus: 'Přejít na další měsíc',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Nyní', currentStatus: 'Přejde na aktuální měsíc',
		todayText: 'Nyní', todayStatus: 'Přejde na aktuální měsíc',
		clearText: 'Vymazat', clearStatus: 'Vymaže zadané datum',
		closeText: 'Zavřít',  closeStatus: 'Zavře kalendář beze změny',
		yearStatus: 'Přejít na jiný rok', monthStatus: 'Přejít na jiný měsíc',
		weekText: 'Týd', weekStatus: 'Týden v roce',
		dayStatus: '\'Vyber\' DD, M d', defaultStatus: 'Vyberte datum',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['cs']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Danish localisation for calendars datepicker for jQuery.
   Written by Jan Christensen ( <EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['da'] = {
		renderer: $.calendarsPicker.defaultRenderer,
        prevText: '&#x3c;Forrige', prevStatus: 'Vis forrige måned',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Næste&#x3e;', nextStatus: 'Vis næste måned',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Idag', currentStatus: 'Vis aktuel måned',
		todayText: 'Idag', todayStatus: 'Vis aktuel måned',
		clearText: 'Nulstil', clearStatus: 'Nulstil den aktuelle dato',
		closeText: 'Luk', closeStatus: 'Luk uden ændringer',
		yearStatus: 'Vis et andet år', monthStatus: 'Vis en anden måned',
		weekText: 'Uge', weekStatus: 'Årets uge',
		dayStatus: 'Vælg D, M d', defaultStatus: 'Vælg en dato',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['da']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Swiss-German localisation for calendars datepicker for jQuery.
   Written by Douglas Jose & Juerg Meier. */
(function($) {
	$.calendarsPicker.regionalOptions['de-CH'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;zurück', prevStatus: 'letzten Monat zeigen',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'nächster&#x3e;', nextStatus: 'nächsten Monat zeigen',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'heute', currentStatus: '',
		todayText: 'heute', todayStatus: '',
		clearText: 'löschen', clearStatus: 'aktuelles Datum löschen',
		closeText: 'schliessen', closeStatus: 'ohne Änderungen schliessen',
		yearStatus: 'anderes Jahr anzeigen', monthStatus: 'anderen Monat anzeige',
		weekText: 'Wo', weekStatus: 'Woche des Monats',
		dayStatus: 'Wähle D, M d', defaultStatus: 'Wähle ein Datum',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['de-CH']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   German localisation for calendars datepicker for jQuery.
   Written by Milian Wolff (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['de'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;zurück', prevStatus: 'letzten Monat zeigen',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Vor&#x3e;', nextStatus: 'nächsten Monat zeigen',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'heute', currentStatus: '',
		todayText: 'heute', todayStatus: '',
		clearText: 'löschen', clearStatus: 'aktuelles Datum löschen',
		closeText: 'schließen', closeStatus: 'ohne Änderungen schließen',
		yearStatus: 'anderes Jahr anzeigen', monthStatus: 'anderen Monat anzeige',
		weekText: 'Wo', weekStatus: 'Woche des Monats',
		dayStatus: 'Wähle D, M d', defaultStatus: 'Wähle ein Datum',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['de']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Greek localisation for calendars datepicker for jQuery.
   Written by Alex Cicovic (http://www.alexcicovic.com). */
(function($) {
	$.calendarsPicker.regionalOptions['el'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'Προηγούμενος', prevStatus: 'Επισκόπηση προηγούμενου μήνα',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Επόμενος', nextStatus: 'Επισκόπηση επόμενου μήνα',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Τρέχων Μήνας', currentStatus: 'Επισκόπηση τρέχοντος μήνα',
		todayText: 'Τρέχων Μήνας', todayStatus: 'Επισκόπηση τρέχοντος μήνα',
		clearText: 'Σβήσιμο', clearStatus: 'Σβήσιμο της επιλεγμένης ημερομηνίας',
		closeText: 'Κλείσιμο', closeStatus: 'Κλείσιμο χωρίς αλλαγή',
		yearStatus: 'Επισκόπηση άλλου έτους', monthStatus: 'Επισκόπηση άλλου μήνα',
		weekText: 'Εβδ', weekStatus: '',
		dayStatus: 'Επιλογή DD d MM', defaultStatus: 'Επιλέξτε μια ημερομηνία',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['el']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   English/Australia localisation for calendars datepicker for jQuery.
   Based on en-GB. */
(function($) {
	$.calendarsPicker.regionalOptions['en-AU'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'Prev', prevStatus: 'Show the previous month',
		prevJumpText: '&lt;&lt;', prevJumpStatus: 'Show the previous year',
		nextText: 'Next', nextStatus: 'Show the next month',
		nextJumpText: '&gt;&gt;', nextJumpStatus: 'Show the next year',
		currentText: 'Current', currentStatus: 'Show the current month',
		todayText: 'Today', todayStatus: 'Show today\'s month',
		clearText: 'Clear', clearStatus: 'Clear all the dates',
		closeText: 'Done', closeStatus: 'Close the datepicker',
		yearStatus: 'Change the year', monthStatus: 'Change the month',
		weekText: 'Wk', weekStatus: 'Week of the year',
		dayStatus: 'Select DD, M d, yyyy', defaultStatus: 'Select a date',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['en-AU']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   English/UK localisation for calendars datepicker for jQuery.
   Stuart. */
(function($) {
	$.calendarsPicker.regionalOptions['en-GB'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'Prev', prevStatus: 'Show the previous month',
		prevJumpText: '&lt;&lt;', prevJumpStatus: 'Show the previous year',
		nextText: 'Next', nextStatus: 'Show the next month',
		nextJumpText: '&gt;&gt;', nextJumpStatus: 'Show the next year',
		currentText: 'Current', currentStatus: 'Show the current month',
		todayText: 'Today', todayStatus: 'Show today\'s month',
		clearText: 'Clear', clearStatus: 'Clear all the dates',
		closeText: 'Done', closeStatus: 'Close the datepicker',
		yearStatus: 'Change the year', monthStatus: 'Change the month',
		weekText: 'Wk', weekStatus: 'Week of the year',
		dayStatus: 'Select DD, M d, yyyy', defaultStatus: 'Select a date',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['en-GB']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   English/New Zealand localisation for calendars datepicker for jQuery.
   Based on en-GB. */
(function($) {
	$.calendarsPicker.regionalOptions['en-NZ'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'Prev', prevStatus: 'Show the previous month',
		prevJumpText: '&lt;&lt;', prevJumpStatus: 'Show the previous year',
		nextText: 'Next', nextStatus: 'Show the next month',
		nextJumpText: '&gt;&gt;', nextJumpStatus: 'Show the next year',
		currentText: 'Current', currentStatus: 'Show the current month',
		todayText: 'Today', todayStatus: 'Show today\'s month',
		clearText: 'Clear', clearStatus: 'Clear all the dates',
		closeText: 'Done', closeStatus: 'Close the datepicker',
		yearStatus: 'Change the year', monthStatus: 'Change the month',
		weekText: 'Wk', weekStatus: 'Week of the year',
		dayStatus: 'Select DD, M d, yyyy', defaultStatus: 'Select a date',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['en-NZ']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Esperanto localisation for calendars datepicker for jQuery.
   Written by Olivier M. (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['eo'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&lt;Anta', prevStatus: 'Vidi la antaŭan monaton',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Sekv&gt;', nextStatus: 'Vidi la sekvan monaton',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Nuna', currentStatus: 'Vidi la nunan monaton',
		todayText: 'Nuna', todayStatus: 'Vidi la nunan monaton',
		clearText: 'Vakigi', clearStatus: '',
		closeText: 'Fermi', closeStatus: 'Fermi sen modifi',
		yearStatus: 'Vidi alian jaron', monthStatus: 'Vidi alian monaton',
		weekText: 'Sb', weekStatus: '',
		dayStatus: 'Elekti DD, MM d', defaultStatus: 'Elekti la daton',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['eo']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Spanish/Argentina localisation for calendars datepicker for jQuery.
   Written by Esteban Acosta Villafane (<EMAIL>) of Globant (http://www.globant.com). */
(function($) {
	$.calendarsPicker.regionalOptions['es-AR'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Ant', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Sig&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Hoy', currentStatus: '',
		todayText: 'Hoy', todayStatus: '',
		clearText: 'Limpiar', clearStatus: '',
		closeText: 'Cerrar', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Sm', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['es-AR']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Spanish/Perú localisation for calendars datepicker for jQuery.
   Written by Fischer Tirado (<EMAIL>) of ASIX (http://www.asixonline.com). */
(function($) {
	$.calendarsPicker.regionalOptions['es-PE'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Ant', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Sig&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Hoy', currentStatus: '',
		todayText: 'Hoy', todayStatus: '',
		clearText: 'Limpiar', clearStatus: '',
		closeText: 'Cerrar', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Sm', weekStatus: '',
		dayStatus: 'DD d, MM yyyy', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['es-PE']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Spanish localisation for calendars datepicker for jQuery.
   Traducido por Vester (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['es'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Ant', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Sig&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Hoy', currentStatus: '',
		todayText: 'Hoy', todayStatus: '',
		clearText: 'Limpiar', clearStatus: '',
		closeText: 'Cerrar', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Sm', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['es']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Estonian localisation for calendars datepicker for jQuery.
   Written by Mart Sõmermaa (mrts.pydev at gmail com). */
(function($) {
	$.calendarsPicker.regionalOptions['et'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'Eelnev', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Järgnev', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Täna', currentStatus: '',
		todayText: 'Täna', todayStatus: '',
		clearText: 'X', clearStatus: '',
		closeText: 'Sulge', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Wk', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['et']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Basque localisation for calendars datepicker for jQuery.
   Karrikas-ek itzulia (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['eu'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Aur', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Hur&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Gaur', currentStatus: '',
		todayText: 'Gaur', todayStatus: '',
		clearText: 'X', clearStatus: '',
		closeText: 'Egina', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Wk', weekStatus: '',
		dayStatus: 'DD d MM', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['eu']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Farsi/Persian localisation for calendars datepicker for jQuery.
   Javad Mowlanezhad -- <EMAIL>. */
(function($) {
	$.calendarsPicker.regionalOptions['fa'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;قبلي', prevStatus: 'نمايش ماه قبل',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'بعدي&#x3e;', nextStatus: 'نمايش ماه بعد',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'امروز', currentStatus: 'نمايش ماه جاري',
		todayText: 'امروز', todayStatus: 'نمايش ماه جاري',
		clearText: 'حذف تاريخ', clearStatus: 'پاک کردن تاريخ جاري',
		closeText: 'بستن', closeStatus: 'بستن بدون اعمال تغييرات',
		yearStatus: 'نمايش سال متفاوت', monthStatus: 'نمايش ماه متفاوت',
		weekText: 'هف', weekStatus: 'هفتهِ سال',
		dayStatus: 'انتخاب D, M d', defaultStatus: 'انتخاب تاريخ',
		isRTL: true
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['fa']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Finnish localisation for calendars datepicker for jQuery.
   Written by Harri Kilpiö (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['fi'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&laquo;Edellinen', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Seuraava&raquo;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'T&auml;n&auml;&auml;n', currentStatus: '',
		todayText: 'T&auml;n&auml;&auml;n', todayStatus: '',
		clearText: 'Tyhjenn&auml;', clearStatus: '',
		closeText: 'Sulje', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Vk', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['fi']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Faroese localisation for calendars datepicker for jQuery.
   Written by Sverri Mohr Olsen, <EMAIL> */
(function($) {
	$.calendarsPicker.regionalOptions['fo'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Sísta', prevStatus: 'Vís sísta mánaðan',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Vís sísta árið',
		nextText: 'Næsta&#x3e;', nextStatus: 'Vís næsta mánaðan',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Vís næsta árið',
		currentText: 'Hesin', currentStatus: 'Vís hendan mánaðan',
		todayText: 'Í dag', todayStatus: 'Vís mánaðan fyri í dag',
		clearText: 'Strika', clearStatus: 'Strika allir mánaðarnar',
		closeText: 'Goym', closeStatus: 'Goym hetta vindeyðga',
		yearStatus: 'Broyt árið', monthStatus: 'Broyt mánaðans',
		weekText: 'Vk', weekStatus: 'Vika av árinum',
		dayStatus: 'Vel DD, M d, yyyy', defaultStatus: 'Vel ein dato',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['fo']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Swiss French localisation for calendars datepicker for jQuery.
   Written by Martin Voelkle (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['fr-CH'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Préc', prevStatus: 'Voir le mois précédent',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Voir l\'année précédent',
		nextText: 'Suiv&#x3e;', nextStatus: 'Voir le mois suivant',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Voir l\'année suivant',
		currentText: 'Courant', currentStatus: 'Voir le mois courant',
		todayText: 'Aujourd\'hui', todayStatus: 'Voir aujourd\'hui',
		clearText: 'Effacer', clearStatus: 'Effacer la date sélectionnée',
		closeText: 'Fermer', closeStatus: 'Fermer sans modifier',
		yearStatus: 'Voir une autre année', monthStatus: 'Voir un autre mois',
		weekText: 'Sm', weekStatus: 'Semaine de l\'année',
		dayStatus: '\'Choisir\' le DD d MM', defaultStatus: 'Choisir la date',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['fr-CH']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   French localisation for calendars datepicker for jQuery.
   Stéphane Nahmani (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['fr'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Préc', prevStatus: 'Voir le mois précédent',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Voir l\'année précédent',
		nextText: 'Suiv&#x3e;', nextStatus: 'Voir le mois suivant',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Voir l\'année suivant',
		currentText: 'Courant', currentStatus: 'Voir le mois courant',
		todayText: 'Aujourd\'hui', todayStatus: 'Voir aujourd\'hui',
		clearText: 'Effacer', clearStatus: 'Effacer la date sélectionnée',
		closeText: 'Fermer', closeStatus: 'Fermer sans modifier',
		yearStatus: 'Voir une autre année', monthStatus: 'Voir un autre mois',
		weekText: 'Sm', weekStatus: 'Semaine de l\'année',
		dayStatus: '\'Choisir\' le DD d MM', defaultStatus: 'Choisir la date',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['fr']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Iniciacion en galego para a extensión 'UI date picker' para jQuery.
   Traducido por Manuel (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['gl'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Ant', prevStatus: 'Amosar mes anterior',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Amosar ano anterior',
		nextText: 'Seg&#x3e;', nextStatus: 'Amosar mes seguinte',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Amosar ano seguinte',
		currentText: 'Hoxe', currentStatus: 'Amosar mes actual',
		todayText: 'Hoxe', todayStatus: 'Amosar mes actual',
		clearText: 'Limpar', clearStatus: 'Borrar data actual',
		closeText: 'Pechar', closeStatus: 'Pechar sen gardar',
		yearStatus: 'Amosar outro ano', monthStatus: 'Amosar outro mes',
		weekHeader: 'Sm', weekStatus: 'Semana do ano',
		dayStatus: 'D, M d', defaultStatus: 'Selecciona Data',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['gl']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Gujarati (ગુજરાતી) localisation for calendars datepicker for jQuery.
   Naymesh Mistry (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['gu'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;પાછળ', prevStatus: 'પાછલો મહિનો બતાવો',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'પાછળ',
		nextText: 'આગળ&#x3e;', nextStatus: 'આગલો મહિનો બતાવો',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'આગળ',
		currentText: 'આજે', currentStatus: 'આજનો દિવસ બતાવો',
		todayText: 'આજે', todayStatus: 'આજનો દિવસ',
		clearText: 'ભૂંસો', clearStatus: 'હાલ પસંદ કરેલી તારીખ ભૂંસો',
		closeText: 'બંધ કરો', closeStatus: 'તારીખ પસંદ કર્યા વગર બંધ કરો',
		yearStatus: 'જુદુ વર્ષ બતાવો', monthStatus: 'જુદો મહિનો બતાવો',
		weekText: 'અઠવાડિયું', weekStatus: 'અઠવાડિયું',
		dayStatus: 'અઠવાડિયાનો પહેલો દિવસ પસંદ કરો', defaultStatus: 'તારીખ પસંદ કરો',		
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['gu']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Hebrew localisation for calendars datepicker for jQuery.
   Written by Amir Hardon (ahardon at gmail dot com). */
(function($) {
	$.calendarsPicker.regionalOptions['he'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;הקודם', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'הבא&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'היום', currentStatus: '',
		todayText: 'היום', todayStatus: '',
		clearText: 'נקה', clearStatus: '',
		closeText: 'סגור', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Wk', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: true
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['he']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Hindi INDIA localisation for calendars datepicker for jQuery.
   Written by Pawan Kumar Singh. */
(function($) {
	$.calendarsPicker.regionalOptions['hi-IN'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'पिछला', prevStatus: 'पिछला महीना देखें',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'पिछला वर्ष देखें',
		nextText: 'अगला', nextStatus: 'अगला महीना देखें',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'अगला वर्ष देखें',
		currentText: 'वर्तमान', currentStatus: 'वर्तमान महीना देखें',
		todayText: 'आज', todayStatus: 'वर्तमान दिन देखें',
		clearText: 'साफ', clearStatus: 'वर्तमान दिनांक मिटाए',
		closeText: 'समाप्त', closeStatus: 'बदलाव के बिना बंद',
		yearStatus: 'एक अलग वर्ष का चयन करें', monthStatus: 'एक अलग महीने का चयन करें',
		weekText: 'Wk', weekStatus: 'वर्ष का सप्ताह',
		dayStatus: 'चुने DD, M d', defaultStatus: 'एक तिथि का चयन करें',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['hi-IN']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Croatian localisation for calendars datepicker for jQuery.
   Written by Vjekoslav Nesek. */
(function($) {
	$.calendarsPicker.regionalOptions['hr'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;', prevStatus: 'Prikaži prethodni mjesec',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: '&#x3e;', nextStatus: 'Prikaži slijedeći mjesec',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Danas', currentStatus: 'Današnji datum',
		todayText: 'Danas', todayStatus: 'Današnji datum',
		clearText: 'izbriši', clearStatus: 'Izbriši trenutni datum',
		closeText: 'Zatvori', closeStatus: 'Zatvori kalendar',
		yearStatus: 'Prikaži godine', monthStatus: 'Prikaži mjesece',
		weekText: 'Tje', weekStatus: 'Tjedanr',
		dayStatus: '\'Datum\' DD, M d', defaultStatus: 'Odaberi datum',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['hr']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Hungarian localisation for calendars datepicker for jQuery.
   Written by Istvan Karaszi (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['hu'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&laquo;&nbsp;vissza', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'előre&nbsp;&raquo;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'ma', currentStatus: '',
		todayText: 'ma', todayStatus: '',
		clearText: 'törlés', clearStatus: '',
		closeText: 'bezárás', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Hé', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['hu']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Armenian localisation for calendars datepicker for jQuery.
   Written by Levon Zakaryan (<EMAIL>) */
(function($) {
	$.calendarsPicker.regionalOptions['hy'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Նախ.',  prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Հաջ.&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Այսօր', currentStatus: '',
		todayText: 'Այսօր', todayStatus: '',
		clearText: 'Մաքրել', clearStatus: '',
		closeText: 'Փակել', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'ՇԲՏ', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['hy']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Indonesian localisation for calendars datepicker for jQuery.
   Written by Deden Fathurahman (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['id'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;mundur', prevStatus: 'Tampilkan bulan sebelumnya',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'maju&#x3e;', nextStatus: 'Tampilkan bulan berikutnya',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'hari ini', currentStatus: 'Tampilkan bulan sekarang',
		todayText: 'hari ini', todayStatus: 'Tampilkan bulan sekarang',
		clearText: 'kosongkan', clearStatus: 'bersihkan tanggal yang sekarang',
		closeText: 'Tutup', closeStatus: 'Tutup tanpa mengubah',
		yearStatus: 'Tampilkan tahun yang berbeda', monthStatus: 'Tampilkan bulan yang berbeda',
		weekText: 'Mg', weekStatus: 'Minggu dalam tahu',
		dayStatus: 'pilih le DD, MM d', defaultStatus: 'Pilih Tanggal',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['id']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Icelandic localisation for calendars datepicker for jQuery.
   Written by Haukur H. Thorsson (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['is'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c; Fyrri', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'N&aelig;sti &#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: '&Iacute; dag', currentStatus: '',
		todayText: '&Iacute; dag', todayStatus: '',
		clearText: 'Hreinsa', clearStatus: '',
		closeText: 'Loka', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Vika', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['is']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Italian localisation for calendars datepicker for jQuery.
   Written by Apaella (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['it'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Prec', prevStatus: 'Mese precedente',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Mostra l\'anno precedente',
		nextText: 'Succ&#x3e;', nextStatus: 'Mese successivo',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Mostra l\'anno successivo',
		currentText: 'Oggi', currentStatus: 'Mese corrente',
		todayText: 'Oggi', todayStatus: 'Mese corrente',
		clearText: 'Svuota', clearStatus: 'Annulla',
		closeText: 'Chiudi', closeStatus: 'Chiudere senza modificare',
		yearStatus: 'Seleziona un altro anno', monthStatus: 'Seleziona un altro mese',
		weekText: 'Sm', weekStatus: 'Settimana dell\'anno',
		dayStatus: '\'Seleziona\' DD, M d', defaultStatus: 'Scegliere una data',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['it']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Japanese localisation for calendars datepicker for jQuery.
   Written by Kentaro SATO (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['ja'] = {
		renderer: $.extend({}, $.calendarsPicker.defaultRenderer,
			{month: $.calendarsPicker.defaultRenderer.month.
				replace(/monthHeader/, 'monthHeader:yyyy年 MM')}),
		prevText: '&#x3c;前', prevStatus: '前月を表示します',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '前年を表示します',
		nextText: '次&#x3e;', nextStatus: '翌月を表示します',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '翌年を表示します',
		currentText: '今日', currentStatus: '今月を表示します',
		todayText: '今日', todayStatus: '今月を表示します',
		clearText: 'クリア', clearStatus: '日付をクリアします',
		closeText: '閉じる', closeStatus: '変更せずに閉じます',
		yearStatus: '表示する年を変更します', monthStatus: '表示する月を変更します',
		weekText: '週', weekStatus: '暦週で第何週目かを表します',
		dayStatus: 'yyyy/mm/dd', defaultStatus: '日付を選択します',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ja']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Georgian localisation for calendars datepicker for jQuery.
   Andrei Gorbushkin. */
(function($) {
	$.calendarsPicker.regionalOptions['ka'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '<უკან', prevStatus: 'წინა თვე',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'წინა წელი',
		nextText: 'წინ>', nextStatus: 'შემდეგი თვე',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'შემდეგი წელი',
		currentText: 'მიმდინარე', currentStatus: 'მიმდინარე თვე',
		todayText: 'დღეს', todayStatus: 'მიმდინარე დღე',
		clearText: 'გასუფთავება', clearStatus: 'მიმდინარე თარიღის წაშლა',
		closeText: 'არის', closeStatus: 'დახურვა უცვლილებოდ',
		yearStatus: 'სხვა წელი', monthStatus: 'სხვა თვე',
		weekText: 'კვ', weekStatus: 'წლის კვირა',
		dayStatus: 'აირჩიეთ DD, M d', defaultStatus: 'აიღჩიეთ თარიღი',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ka']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Khmer initialisation for calendars datepicker for jQuery.
   Written by Sovichet Tep (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['km'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'ថយ​ក្រោយ', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'ទៅ​មុខ', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'ថ្ងៃ​នេះ', currentStatus: '',
		todayText: 'ថ្ងៃ​នេះ', todayStatus: '',
		clearText: 'X', clearStatus: '',
		closeText: 'រួច​រាល់', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Wk', weekStatus: '',
		dayStatus: 'DD d MM', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['km']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Korean localisation for calendars datepicker for jQuery.
   Written by DaeKwon Kang (<EMAIL>), Edited by Genie. */
(function($) {
	$.calendarsPicker.regionalOptions['ko'] = {
		renderer: $.extend({}, $.calendarsPicker.defaultRenderer,
			{month: $.calendarsPicker.defaultRenderer.month.
				replace(/monthHeader/, 'monthHeader:yyyy년 MM')}),
		prevText: '이전달', prevStatus: '이전달을 표시합니다',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '이전 연도를 표시합니다',
		nextText: '다음달', nextStatus: '다음달을 표시합니다',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '다음 연도를 표시합니다',
		currentText: '현재', currentStatus: '입력한 달을 표시합니다',
		todayText: '오늘', todayStatus: '이번달을 표시합니다',
		clearText: '지우기', clearStatus: '입력한 날짜를 지웁니다',
		closeText: '닫기', closeStatus: '',
		yearStatus: '표시할 연도를 변경합니다', monthStatus: '표시할 월을 변경합니다',
		weekText: 'Wk', weekStatus: '해당 연도의 주차',
		dayStatus: 'M d일 (D)', defaultStatus: '날짜를 선택하세요',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ko']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Lithuanian localisation for calendars datepicker for jQuery.
   Arturas Paleicikas <<EMAIL>>. */
(function($) {
	$.calendarsPicker.regionalOptions['lt'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Atgal',  prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Pirmyn&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Šiandien', currentStatus: '',
		todayText: 'Šiandien', todayStatus: '',
		clearText: 'Išvalyti', clearStatus: '',
		closeText: 'Uždaryti', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Wk', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['lt']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Latvian localisation for calendars datepicker for jQuery.
   Arturas Paleicikas <<EMAIL>>. */
(function($) {
	$.calendarsPicker.regionalOptions['lv'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'Iepr',  prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Nāka', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Šodien', currentStatus: '',
		todayText: 'Šodien', todayStatus: '',
		clearText: 'Notīrīt', clearStatus: '',
		closeText: 'Aizvērt', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Nav', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['lv']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Montenegrin localisation for calendars datepicker for jQuery.
   Written by Miloš Milošević - fleka d.o.o. */
(function($) {
	$.calendarsPicker.regionalOptions['me-ME'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;', prevStatus: 'Prikaži prethodni mjesec',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Prikaži prethodnu godinu',
		nextText: '&#x3e;', nextStatus: 'Prikaži sljedeći mjesec',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Prikaži sljedeću godinu',
		currentText: 'Danas', currentStatus: 'Tekući mjesec',
		todayText: 'Danas', todayStatus: 'Tekući mjesec',
		clearText: 'Obriši', clearStatus: 'Obriši trenutni datum',
		closeText: 'Zatvori', closeStatus: 'Zatvori kalendar',
		yearStatus: 'Prikaži godine', monthStatus: 'Prikaži mjesece',
		weekText: 'Sed', weekStatus: 'Sedmica',
		dayStatus: '\'Datum\' DD, M d', defaultStatus: 'Odaberi datum',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['me-ME']);
})(jQuery);
/* http://keith-wood.name/calendars.html
   Montenegrin localisation for calendars datepicker for jQuery.
   Written by Miloš Milošević - fleka d.o.o. */
(function($) {
	$.calendarsPicker.regionalOptions['me'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;', prevStatus: 'Прикажи претходни мјесец',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Прикажи претходну годину',
		nextText: '&#x3e;', nextStatus: 'Прикажи сљедећи мјесец',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Прикажи сљедећу годину',
		currentText: 'Данас', currentStatus: 'Текући мјесец',
		todayText: 'Данас', todayStatus: 'Текући мјесец',
		clearText: 'Обриши', clearStatus: 'Обриши тренутни датум',
		closeText: 'Затвори', closeStatus: 'Затвори календар',
		yearStatus: 'Прикажи године', monthStatus: 'Прикажи мјесеце',
		weekText: 'Сед', weekStatus: 'Седмица',
		dayStatus: '\'Датум\' DD d MM', defaultStatus: 'Одабери датум',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['me']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Македонски MK localisation for calendars datepicker for jQuery.
   Hajan Selmani (hajan [at] live [dot] com). */
(function($) {
	$.calendarsPicker.regionalOptions['mk'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'Претх.', prevStatus: 'Прикажи го претходниот месец',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Прикажи ја претходната година',
		nextText: 'Следен', nextStatus: 'Прикажи го следниот месец',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Прикажи ја следната година',
		currentText: 'Тековен', currentStatus: 'Прикажи го тековниот месец',
		todayText: 'Денес', todayStatus: 'Прикажи го денешниот месец',
		clearText: 'Бриши', clearStatus: 'Избриши го тековниот датум',
		closeText: 'Затвори', closeStatus: 'Затвори без промени',
		yearStatus: 'Избери друга година', monthStatus: 'Избери друг месец',
		weekText: 'Нед', weekStatus: 'Недела во годината',
		dayStatus: 'Избери DD, M d', defaultStatus: 'Избери датум',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['mk']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Malayalam localisation for calendars datepicker for jQuery.
   Saji Nediyanchath (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['ml'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'മുന്നത്തെ', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'അടുത്തത് ', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'ഇന്ന്', currentStatus: '',
		todayText: 'ഇന്ന്', todayStatus: '',
		clearText: 'X', clearStatus: '',
		closeText: 'ശരി', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'ആ', weekStatus: '',
		dayStatus: 'DD d MM', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ml']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Malaysian localisation for calendars datepicker for jQuery.
   Written by Mohd Nawawi Mohamad Jamili (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['ms'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Sebelum', prevStatus: 'Tunjukkan bulan lepas',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Tunjukkan tahun lepas',
		nextText: 'Selepas&#x3e;', nextStatus: 'Tunjukkan bulan depan',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Tunjukkan tahun depan',
		currentText: 'hari ini', currentStatus: 'Tunjukkan bulan terkini',
		todayText: 'hari ini', todayStatus: 'Tunjukkan bulan terkini',
		clearText: 'Padam', clearStatus: 'Padamkan tarikh terkini',
		closeText: 'Tutup', closeStatus: 'Tutup tanpa perubahan',
		yearStatus: 'Tunjukkan tahun yang lain', monthStatus: 'Tunjukkan bulan yang lain',
		weekText: 'Mg', weekStatus: 'Minggu bagi tahun ini',
		dayStatus: 'DD, d MM', defaultStatus: 'Sila pilih tarikh',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ms']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Maltese localisation for calendars datepicker for jQuery.
   Written by Chritian Sciberras (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['mt'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'Ta Qabel', prevStatus: 'Ix-xahar ta qabel',
 		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Is-sena ta qabel',
 		nextText: 'Li Jmiss', nextStatus: 'Ix-xahar li jmiss',
 		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Is-sena li jmiss',
 		currentText: 'Illum', currentStatus: 'Ix-xahar ta llum',
 		todayText: 'Illum', todayStatus: 'Uri ix-xahar ta llum',
 		clearText: 'Ħassar', clearStatus: 'Ħassar id-data',
 		closeText: 'Lest', closeStatus: 'Għalaq mingħajr tibdiliet',
 		yearStatus: 'Uri sena differenti', monthStatus: 'Uri xahar differenti',
		weekText: 'Ġm', weekStatus: 'Il-Ġimgħa fis-sena',
		dayStatus: 'Għazel DD, M d', defaultStatus: 'Għazel data',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['mt']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Dutch/Belgian localisation for calendars datepicker for jQuery.
   Written by Mathias Bynens <http://mathiasbynens.be/>. */
(function($) {
	$.calendarsPicker.regionalOptions['nl-BE'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '←', prevStatus: 'Bekijk de vorige maand',
		prevJumpText: '«', nextJumpStatus: 'Bekijk het vorige jaar',
		nextText: '→', nextStatus: 'Bekijk de volgende maand',
		nextJumpText: '»', nextJumpStatus: 'Bekijk het volgende jaar',
		currentText: 'Vandaag', currentStatus: 'Bekijk de huidige maand',
		todayText: 'Vandaag', todayStatus: 'Bekijk de huidige maand',
		clearText: 'Wissen', clearStatus: 'Wis de huidige datum',
		closeText: 'Sluiten', closeStatus: 'Sluit zonder verandering',
		yearStatus: 'Bekijk een ander jaar', monthStatus: 'Bekijk een andere maand',
		weekText: 'Wk', weekStatus: 'Week van het jaar',
		dayStatus: 'dd/mm/yyyy', defaultStatus: 'Kies een datum',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['nl-BE']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Dutch localisation for calendars datepicker for jQuery.
   Written by Mathias Bynens <http://mathiasbynens.be/>. */
(function($) {
	$.calendarsPicker.regionalOptions['nl'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '←', prevStatus: 'Bekijk de vorige maand',
		prevJumpText: '«', nextJumpStatus: 'Bekijk het vorige jaar',
		nextText: '→', nextStatus: 'Bekijk de volgende maand',
		nextJumpText: '»', nextJumpStatus: 'Bekijk het volgende jaar',
		currentText: 'Vandaag', currentStatus: 'Bekijk de huidige maand',
		todayText: 'Vandaag', todayStatus: 'Bekijk de huidige maand',
		clearText: 'Wissen', clearStatus: 'Wis de huidige datum',
		closeText: 'Sluiten', closeStatus: 'Sluit zonder verandering',
		yearStatus: 'Bekijk een ander jaar', monthStatus: 'Bekijk een andere maand',
		weekText: 'Wk', weekStatus: 'Week van het jaar',
		dayStatus: 'dd-mm-yyyy', defaultStatus: 'Kies een datum',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['nl']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Norwegian localisation for calendars datepicker for jQuery.
   Written by Naimdjon Takhirov (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['no'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&laquo;Forrige',  prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Neste&raquo;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'I dag', currentStatus: '',
		todayText: 'I dag', todayStatus: '',
		clearText: 'Tøm', clearStatus: '',
		closeText: 'Lukk', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Uke', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['no']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Polish localisation for calendars datepicker for jQuery.
   Written by Jacek Wysocki (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['pl'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Poprzedni', prevStatus: 'Pokaż poprzedni miesiąc',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Następny&#x3e;', nextStatus: 'Pokaż następny miesiąc',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Dziś', currentStatus: 'Pokaż aktualny miesiąc',
		todayText: 'Dziś', todayStatus: 'Pokaż aktualny miesiąc',
		clearText: 'Wyczyść', clearStatus: 'Wyczyść obecną datę',
		closeText: 'Zamknij', closeStatus: 'Zamknij bez zapisywania',
		yearStatus: 'Pokaż inny rok', monthStatus: 'Pokaż inny miesiąc',
		weekText: 'Tydz', weekStatus: 'Tydzień roku',
		dayStatus: '\'Wybierz\' DD, M d', defaultStatus: 'Wybierz datę',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['pl']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Brazilian Portuguese localisation for calendars datepicker for jQuery.
   Written by Leonildo Costa Silva (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['pt-BR'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&lt;Anterior', prevStatus: 'Mostra o mês anterior', 
		prevJumpText: '&lt;&lt;', prevJumpStatus: 'Mostra o ano anterior', 
		nextText: 'Próximo&gt;', nextStatus: 'Mostra o próximo mês', 
		nextJumpText: '&gt;&gt;', nextJumpStatus: 'Mostra o próximo ano',
		currentText: 'Atual', currentStatus: 'Mostra o mês atual',
		todayText: 'Hoje', todayStatus: 'Vai para hoje', 
		clearText: 'Limpar', clearStatus: 'Limpar data',
		closeText: 'Fechar', closeStatus: 'Fechar o calendário',
		yearStatus: 'Selecionar ano', monthStatus: 'Selecionar mês',
		weekText: 's', weekStatus: 'Semana do ano', 
		dayStatus: 'DD, d \'de\' M \'de\' yyyy', defaultStatus: 'Selecione um dia',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['pt-BR']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Romansh localisation for calendars datepicker for jQuery.
   Yvonne Gienal (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['rm'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Suandant', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Precedent&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Actual', currentStatus: '',
		todayText: 'Actual', todayStatus: '',
		clearText: 'X', clearStatus: '',
		closeText: 'Serrar', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'emna', weekStatus: '',
		dayStatus: 'DD d MM', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['rm']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Romanian localisation for calendars datepicker for jQuery.
   Written by Edmond L. (<EMAIL>) and Ionut G. Stan (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['ro'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&laquo;Precedenta', prevStatus: 'Arata luna precedenta',
		prevJumpText: '&laquo;&laquo;', prevJumpStatus: '',
		nextText: 'Urmatoare&raquo;', nextStatus: 'Arata luna urmatoare',
		nextJumpText: '&raquo;&raquo;', nextJumpStatus: '',
		currentText: 'Azi', currentStatus: 'Arata luna curenta',
		todayText: 'Azi', todayStatus: 'Arata luna curenta',
		clearText: 'Curat', clearStatus: 'Sterge data curenta',
		closeText: 'Închide', closeStatus: 'Închide fara schimbare',
		yearStatus: 'Arat un an diferit', monthStatus: 'Arata o luna diferita',
		weekText: 'Săpt', weekStatus: 'Săptamana anului',
		dayStatus: 'Selecteaza DD, M d', defaultStatus: 'Selecteaza o data',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ro']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Russian localisation for calendars datepicker for jQuery.
   Written by Andrew Stromnov (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['ru'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Пред',  prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'След&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Сегодня', currentStatus: '',
		todayText: 'Сегодня', todayStatus: '',
		clearText: 'Очистить', clearStatus: '',
		closeText: 'Закрыть', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Не', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ru']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Slovak localisation for calendars datepicker for jQuery.
   Written by Vojtech Rinik (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['sk'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Predchádzajúci',  prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Nasledujúci&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Dnes', currentStatus: '',
		todayText: 'Dnes', todayStatus: '',
		clearText: 'Zmazať', clearStatus: '',
		closeText: 'Zavrieť', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Ty', weekStatus: '',
		dayStatus: 'DD. M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['sk']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Slovenian localisation for calendars datepicker for jQuery.
   Written by Jaka Jancar (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['sl'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&lt;Prej&#x161;nji', prevStatus: 'Prika&#x17E;i prej&#x161;nji mesec',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Naslednji&gt;', nextStatus: 'Prika&#x17E;i naslednji mesec',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Trenutni', currentStatus: 'Prika&#x17E;i trenutni mesec',
		todayText: 'Trenutni', todayStatus: 'Prika&#x17E;i trenutni mesec',
		clearText: 'Izbri&#x161;i', clearStatus: 'Izbri&#x161;i trenutni datum',
		closeText: 'Zapri', closeStatus: 'Zapri brez spreminjanja',
		yearStatus: 'Prika&#x17E;i drugo leto', monthStatus: 'Prika&#x17E;i drug mesec',
		weekText: 'Teden', weekStatus: 'Teden v letu',
		dayStatus: 'Izberi DD, d MM yy', defaultStatus: 'Izbira datuma',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['sl']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Albanian localisation for calendars datepicker for jQuery.
   Written by Flakron Bytyqi (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['sq'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;mbrapa', prevStatus: 'trego muajin e fundit',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Përpara&#x3e;', nextStatus: 'trego muajin tjetër',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'sot', currentStatus: '',
		todayText: 'sot', todayStatus: '',
		clearText: 'fshije', clearStatus: 'fshije datën aktuale',
		closeText: 'mbylle', closeStatus: 'mbylle pa ndryshime',
		yearStatus: 'trego tjetër vit', monthStatus: 'trego muajin tjetër',
		weekText: 'Ja', weekStatus: 'Java e muajit',
		dayStatus: '\'Zgjedh\' D, M d', defaultStatus: 'Zgjedhe një datë',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['sq']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Serbian localisation for calendars datepicker for jQuery.
   Written by Dejan Dimić. */
(function($) {
	$.calendarsPicker.regionalOptions['sr-SR'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;', prevStatus: 'Prikaži predhodni mesec',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Prikaži predhodnu godinu',
		nextText: '&#x3e;', nextStatus: 'Prikaži sledeći mesec',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Prikaži sledeću godinu',
		currentText: 'Danas', currentStatus: 'Tekući mesec',
		todayText: 'Danas', todayStatus: 'Tekući mesec',
		clearText: 'Obriši', clearStatus: 'Obriši trenutni datum',
		closeText: 'Zatvori', closeStatus: 'Zatvori kalendar',
		yearStatus: 'Prikaži godine', monthStatus: 'Prikaži mesece',
		weekText: 'Sed', weekStatus: 'Sedmica',
		dayStatus: '\'Datum\' DD, M d', defaultStatus: 'Odaberi datum',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['sr-SR']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Serbian localisation for calendars datepicker for jQuery.
   Written by Dejan Dimić. */
(function($) {
	$.calendarsPicker.regionalOptions['sr'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;', prevStatus: 'Прикажи предходни месец',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Прикажи предходну годину',
		nextText: '&#x3e;', nextStatus: 'Прикажи слецећи месец',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Прикажи следећу годину',
		currentText: 'Данас', currentStatus: 'Текући месец',
		todayText: 'Данас', todayStatus: 'Текући месец',
		clearText: 'Обриши', clearStatus: 'Обриши тренутни датум',
		closeText: 'Затвори', closeStatus: 'Затвори календар',
		yearStatus: 'Прикажи године', monthStatus: 'Прикажи месеце',
		weekText: 'Сед', weekStatus: 'Седмица',
		dayStatus: '\'Датум\' DD d MM', defaultStatus: 'Одабери датум',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['sr']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Swedish localisation for calendars datepicker for jQuery.
   Written by Anders Ekdahl ( <EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['sv'] = {
		renderer: $.calendarsPicker.defaultRenderer,
        prevText: '&laquo;Förra',  prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'Nästa&raquo;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Idag', currentStatus: '',
		todayText: 'Idag', todayStatus: '',
		clearText: 'Rensa', clearStatus: '',
		closeText: 'Stäng', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Ve', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['sv']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Tamil (UTF-8) localisation for calendars datepicker for jQuery.
   Written by S A Sureshkumar (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['ta'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'முன்னையது',  prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'அடுத்தது', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'இன்று', currentStatus: '',
		todayText: 'இன்று', todayStatus: '',
		clearText: 'அழி', clearStatus: '',
		closeText: 'மூடு', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Wk', weekStatus: '',
		dayStatus: 'D, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ta']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Thai localisation for calendars datepicker for jQuery.
   Written by pipo (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['th'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&laquo;&nbsp;ย้อน', prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'ถัดไป&nbsp;&raquo;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'วันนี้', currentStatus: '',
		todayText: 'วันนี้', todayStatus: '',
		clearText: 'ลบ', clearStatus: '',
		closeText: 'ปิด', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Wk', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['th']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Turkish localisation for calendars datepicker for jQuery.
   Written by Izzet Emre Erkan (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['tr'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;geri', prevStatus: 'önceki ayı göster',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: 'ileri&#x3e', nextStatus: 'sonraki ayı göster',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'bugün', currentStatus: '',
		todayText: 'bugün', todayStatus: '',
		clearText: 'temizle', clearStatus: 'geçerli tarihi temizler',
		closeText: 'kapat', closeStatus: 'sadece göstergeyi kapat',
		yearStatus: 'başka yıl', monthStatus: 'başka ay',
		weekText: 'Hf', weekStatus: 'Ayın haftaları',
		dayStatus: 'D, M d seçiniz', defaultStatus: 'Bir tarih seçiniz',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['tr']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Tatar localisation for calendars datepicker for jQuery.
   Written by Irek Khaziev (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['tt'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: 'Алдагы',  prevStatus: 'Алдагы айны күрсәтү',
		prevJumpText: '&lt;&lt;', prevJumpStatus: 'Алдагы елны күрсәтү',
		nextText: 'Киләсе', nextStatus: 'Киләсе айны күрсәтү',
		nextJumpText: '&gt;&gt;', nextJumpStatus: 'Киләсе елны күрсәтү',
		currentText: 'Хәзер', currentStatus: 'Хәзерге айны күрсәтү',
		todayText: 'Бүген', todayStatus: 'Бүгенге айны күрсәтү',
		clearText: 'Чистарту', clearStatus: 'Барлык көннәрне чистарту',
		closeText: 'Ябарга', closeStatus: 'Көн сайлауны ябарга',
		yearStatus: 'Елны кертегез', monthStatus: 'Айны кертегез',
		weekText: 'Атна', weekStatus: 'Елда атна саны',
		dayStatus: 'DD, M d', defaultStatus: 'Көнне сайлагыз',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['tt']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Ukrainian localisation for calendars datepicker for jQuery.
   Written by Maxim Drogobitskiy (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['uk'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;',  prevStatus: '',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
		nextText: '&#x3e;', nextStatus: '',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
		currentText: 'Сьогодні', currentStatus: '',
		todayText: 'Сьогодні', todayStatus: '',
		clearText: 'Очистити', clearStatus: '',
		closeText: 'Закрити', closeStatus: '',
		yearStatus: '', monthStatus: '',
		weekText: 'Не', weekStatus: '',
		dayStatus: 'DD, M d', defaultStatus: '',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['uk']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Urdu localisation for calendars datepicker for jQuery.
   Mansoor Munib -- <EMAIL> <http://www.mansoor.co.nr/mansoor.html>
   Thanks to Habib Ahmed, ObaidUllah Anwar. */
(function($) {
	$.calendarsPicker.regionalOptions['ur'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;گذشتہ', prevStatus: 'ماه گذشتہ',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'برس گذشتہ',
		nextText: 'آئندہ&#x3e;', nextStatus: 'ماه آئندہ',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'برس آئندہ',
		currentText: 'رواں', currentStatus: 'ماه رواں',
		todayText: 'آج', todayStatus: 'آج',
		clearText: 'حذف تاريخ', clearStatus: 'کریں حذف تاریخ',
		closeText: 'کریں بند', closeStatus: 'کیلئے کرنے بند',
		yearStatus: 'برس تبدیلی', monthStatus: 'ماه تبدیلی',
		weekText: 'ہفتہ', weekStatus: 'ہفتہ',
		dayStatus: 'انتخاب D, M d', defaultStatus: 'کریں منتخب تاريخ',
		isRTL: true
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['ur']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Vietnamese localisation for calendars datepicker for jQuery.
   Translated by Le Thanh Huy (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['vi'] = {
		renderer: $.calendarsPicker.defaultRenderer,
		prevText: '&#x3c;Trước', prevStatus: 'Tháng trước',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: 'Năm trước',
		nextText: 'Tiếp&#x3e;', nextStatus: 'Tháng sau',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: 'Năm sau',
		currentText: 'Hôm nay', currentStatus: 'Tháng hiện tại',
		todayText: 'Hôm nay', todayStatus: 'Tháng hiện tại',
		clearText: 'Xóa', clearStatus: 'Xóa ngày hiện tại',
		closeText: 'Đóng', closeStatus: 'Đóng và không lưu lại thay đổi',
		yearStatus: 'Năm khác', monthStatus: 'Tháng khác',
		weekText: 'Tu', weekStatus: 'Tuần trong năm',
		dayStatus: 'Đang chọn DD, \'ngày\' d M', defaultStatus: 'Chọn ngày',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['vi']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Simplified Chinese localisation for calendars datepicker for jQuery.
   Written by Cloudream (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['zh-CN'] = {
		renderer: $.extend({}, $.calendarsPicker.defaultRenderer,
			{month: $.calendarsPicker.defaultRenderer.month.
				replace(/monthHeader/, 'monthHeader:MM yyyy年')}),
		prevText: '&#x3c;上月', prevStatus: '显示上月',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '显示上一年',
		nextText: '下月&#x3e;', nextStatus: '显示下月',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '显示下一年',
		currentText: '今天', currentStatus: '显示本月',
		todayText: '今天', todayStatus: '显示本月',
		clearText: '清除', clearStatus: '清除已选日期',
		closeText: '关闭', closeStatus: '不改变当前选择',
		yearStatus: '选择年份', monthStatus: '选择月份',
		weekText: '周', weekStatus: '年内周次',
		dayStatus: '选择 m月 d日, DD', defaultStatus: '请选择日期',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['zh-CN']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Hong Kong Chinese localisation for calendars datepicker for jQuery.
   Written by SCCY (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['zh-HK'] = {
		renderer: $.extend({}, $.calendarsPicker.defaultRenderer,
			{month: $.calendarsPicker.defaultRenderer.month.
				replace(/monthHeader/, 'monthHeader:yyyy年 MM')}),
		prevText: '&#x3c;上月', prevStatus: '顯示上月',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '顯示上一年',
		nextText: '下月&#x3e;', nextStatus: '顯示下月',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '顯示下一年',
		currentText: '今天', currentStatus: '顯示本月',
		todayText: '今天', todayStatus: '顯示本月',
		clearText: '清除', clearStatus: '清除已選日期',
		closeText: '關閉', closeStatus: '不改變目前的選擇',
		yearStatus: '選擇年份', monthStatus: '選擇月份',
		weekText: '周', weekStatus: '年內周次',
		dayStatus: '選擇 m月 d日, DD', defaultStatus: '請選擇日期',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['zh-HK']);
})(jQuery);
﻿/* http://keith-wood.name/calendars.html
   Traditional Chinese localisation for calendars datepicker for jQuery.
   Written by Ressol (<EMAIL>). */
(function($) {
	$.calendarsPicker.regionalOptions['zh-TW'] = {
		renderer: $.extend({}, $.calendarsPicker.defaultRenderer,
			{month: $.calendarsPicker.defaultRenderer.month.
				replace(/monthHeader/, 'monthHeader:MM yyyy年')}),
		prevText: '&#x3c;上月', prevStatus: '顯示上月',
		prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '顯示上一年',
		nextText: '下月&#x3e;', nextStatus: '顯示下月',
		nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '顯示下一年',
		currentText: '今天', currentStatus: '顯示本月',
		todayText: '今天', todayStatus: '顯示本月',
		clearText: '清除', clearStatus: '清除已選日期',
		closeText: '關閉', closeStatus: '不改變目前的選擇',
		yearStatus: '選擇年份', monthStatus: '選擇月份',
		weekText: '周', weekStatus: '年內周次',
		dayStatus: '選擇 m月 d日, DD', defaultStatus: '請選擇日期',
		isRTL: false
	};
	$.calendarsPicker.setDefaults($.calendarsPicker.regionalOptions['zh-TW']);
})(jQuery);
