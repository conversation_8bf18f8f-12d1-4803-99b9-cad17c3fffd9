﻿/* http://keith-wood.name/calendars.html
   Calendars date picker extensions for jQuery v2.0.0.
   Written by <PERSON> (kbwood{at}iinet.com.au) August 2009.
   Available under the MIT (https://github.com/jquery/jquery/blob/master/MIT-LICENSE.txt) license. 
   Please attribute the author if you use it. */
(function($){var n={picker:'<div{popup:start} id="ui-datepicker-div"{popup:end} class="ui-datepicker ui-widget '+'ui-widget-content ui-helper-clearfix ui-corner-all{inline:start} ui-datepicker-inline{inline:end}">'+'<div class="ui-datepicker-header ui-widget-header ui-helper-clearfix ui-corner-all">'+'{link:prev}{link:today}{link:next}</div>{months}'+'{popup:start}<div class="ui-datepicker-header ui-widget-header ui-helper-clearfix '+'ui-corner-all">{button:clear}{button:close}</div>{popup:end}'+'<div class="ui-helper-clearfix"></div></div>',monthRow:'<div class="ui-datepicker-row-break">{months}</div>',month:'<div class="ui-datepicker-group">'+'<div class="ui-datepicker-header ui-widget-header ui-helper-clearfix ui-corner-all">{monthHeader:MM yyyy}</div>'+'<table class="ui-datepicker-calendar"><thead>{weekHeader}</thead><tbody>{weeks}</tbody></table></div>',weekHeader:'<tr>{days}</tr>',dayHeader:'<th>{day}</th>',week:'<tr>{days}</tr>',day:'<td>{day}</td>',monthSelector:'.ui-datepicker-group',daySelector:'td',rtlClass:'ui-datepicker-rtl',multiClass:'ui-datepicker-multi',defaultClass:'ui-state-default',selectedClass:'ui-state-active',highlightedClass:'ui-state-hover',todayClass:'ui-state-highlight',otherMonthClass:'ui-datepicker-other-month',weekendClass:'ui-datepicker-week-end',commandClass:'ui-datepicker-cmd',commandButtonClass:'ui-state-default ui-corner-all',commandLinkClass:'',disabledClass:'ui-datepicker-disabled'};$.extend($.calendarsPicker,{weekOfYearRenderer:$.extend({},$.calendarsPicker.defaultRenderer,{weekHeader:'<tr><th class="calendars-week">'+'<span title="{l10n:weekStatus}">{l10n:weekText}</span></th>{days}</tr>',week:'<tr><td class="calendars-week">{weekOfYear}</td>{days}</tr>'}),themeRollerRenderer:n,themeRollerWeekOfYearRenderer:$.extend({},n,{weekHeader:'<tr><th class="ui-state-hover"><span>{l10n:weekText}</span></th>{days}</tr>',week:'<tr><td class="ui-state-hover">{weekOfYear}</td>{days}</tr>'}),noWeekends:function(a){return{selectable:a.weekDay()}},changeFirstDay:function(b,c,d){var e=$(this);b.find('th span').each(function(){if(this.parentNode.className.match(/.*calendars-week.*/)){return}$('<a href="javascript:void(0)" class="'+this.className+'" title="Change first day of the week">'+$(this).text()+'</a>').click(function(){var a=parseInt(this.className.replace(/^.*calendars-dow-(\d+).*$/,'$1'),10);e.calendarsPicker('option',{firstDay:a})}).replaceAll(this)})},hoverCallback:function(f){return function(a,b,c){if($.isFunction(f)){var d=this;var e=c.options.renderer;a.find(e.daySelector+' a, '+e.daySelector+' span').hover(function(){f.apply(d,[$(d).calendarsPicker('retrieveDate',this),this.nodeName.toLowerCase()==='a'])},function(){f.apply(d,[])})}}},highlightWeek:function(a,b,c){var d=this;var e=c.options.renderer;a.find(e.daySelector+' a, '+e.daySelector+' span').hover(function(){$(this).parents('tr').find(e.daySelector+' *').addClass(e.highlightedClass)},function(){$(this).parents('tr').find(e.daySelector+' *').removeClass(e.highlightedClass)})},showStatus:function(b,c,d){var e=(d.options.renderer.selectedClass==='ui-state-active');var f=d.options.defaultStatus||'&nbsp;';var g=$('<div class="'+(!e?'calendars-status':'ui-datepicker-status ui-widget-header ui-helper-clearfix ui-corner-all')+'">'+f+'</div>').insertAfter(b.find('.calendars-month-row:last,.ui-datepicker-row-break:last'));b.find('*[title]').each(function(){var a=$(this).attr('title');$(this).removeAttr('title').hover(function(){g.text(a||f)},function(){g.text(f)})})},monthNavigation:function(b,c,d){var e=$(this);var f=(d.options.renderer.selectedClass==='ui-state-active');var g=d.curMinDate();var h=d.get('maxDate');var j=d.drawDate.year();var k='<div class="'+(!f?'calendars-month-nav':'ui-datepicker-month-nav')+'">';for(var i=0;i<c.monthsInYear(j);i++){var l=c.fromMonthOfYear(j,i+c.minMonth)-c.minMonth;var m=((!g||c.newDate(j,i+c.minMonth,c.daysInMonth(j,i+c.minMonth)).compareTo(g)>-1)&&(!h||c.newDate(j,i+c.minMonth,c.minDay).compareTo(h)<+1));k+='<div>'+(m?'<a href="#" class="jd'+c.newDate(j,i+c.minMonth,c.minDay).toJD()+'"':'<span')+' title="'+c.local.monthNames[l]+'">'+c.local.monthNamesShort[l]+(m?'</a>':'</span>')+'</div>'}k+='</div>';$(k).insertAfter(b.find('div.calendars-nav,div.ui-datepicker-header:first')).find('a').click(function(){var a=e.calendarsPicker('retrieveDate',this);e.calendarsPicker('showMonth',a.year(),a.month());return false})},selectWeek:function(c,d,e){var f=$(this);c.find('td.calendars-week span').each(function(){$('<a href="javascript:void(0)" class="'+this.className+'" title="Select the entire week">'+$(this).text()+'</a>').click(function(){var a=f.calendarsPicker('retrieveDate',this);var b=[a];for(var i=1;i<d.daysInWeek();i++){b.push(a=a.newDate().add(1,'d'))}if(e.options.rangeSelect){b.splice(1,b.length-2)}f.calendarsPicker('setDate',b).calendarsPicker('hide')}).replaceAll(this)})},selectMonth:function(d,e,f){var g=$(this);d.find('th.calendars-week').each(function(){$('<a href="javascript:void(0)" title="Select the entire month">'+$(this).text()+'</a>').click(function(){var a=g.calendarsPicker('retrieveDate',$(this).parents('table').find('td:not(.calendars-week) *:not(.calendars-other-month)')[0]);var b=[a.day(1)];var c=e.daysInMonth(a);for(var i=1;i<c;i++){b.push(a=a.newDate().add(1,'d'))}if(f.options.rangeSelect){b.splice(1,b.length-2)}g.calendarsPicker('setDate',b).calendarsPicker('hide')}).appendTo(this)})},monthOnly:function(b,c,d){var e=$(this);var f=$('<div style="text-align: center;"><button type="button">Select</button></div>').insertAfter(b.find('.calendars-month-row:last,.ui-datepicker-row-break:last')).children().click(function(){var a=b.find('.calendars-month-year:first').val().split('/');e.calendarsPicker('setDate',c.newDate(parseInt(a[1],10),parseInt(a[0],10),c.minDay)).calendarsPicker('hide')});b.find('.calendars-month-row table,.ui-datepicker-row-break table').remove()}})})(jQuery);