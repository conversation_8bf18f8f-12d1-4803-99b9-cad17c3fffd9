﻿/* http://keith-wood.name/calendars.html
   Swedish localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['sv'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
        monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','April','<PERSON>','<PERSON><PERSON>',
        '<PERSON><PERSON>','Augusti','September','<PERSON><PERSON><PERSON>','November','December'],
        monthNamesShort: ['Jan','Feb','Mar','Apr','Maj','Jun',
        'Jul','Aug','Sep','Okt','Nov','Dec'],
		dayNames: ['Söndag','Måndag','Tisdag','Onsdag','Torsdag','Fredag','<PERSON><PERSON>rdag'],
		dayNamesShort: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesMin: ['<PERSON><PERSON>','<PERSON><PERSON>','T<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON><PERSON>'],
        dateFormat: 'yyyy-mm-dd',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['sv'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['sv'];
	}
})(jQuery);
