﻿/* http://keith-wood.name/calendars.html
   Danish localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON> ( <EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['da'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
        monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','April','<PERSON>','<PERSON><PERSON>',
        '<PERSON><PERSON>','August','September','<PERSON>to<PERSON>','November','December'],
        monthNamesShort: ['Jan','Feb','Mar','Apr','Maj','Jun',
        'Jul','Aug','Sep','Okt','Nov','Dec'],
		dayNames: ['Søndag','Mandag','Tirsdag','Onsdag','Torsdag','Fredag','Lørdag'],
		dayNamesShort: ['<PERSON>ø<PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesMin: ['<PERSON><PERSON>','<PERSON>','Ti','On','<PERSON>','<PERSON>','<PERSON><PERSON>'],
        dateFormat: 'dd-mm-yyyy',
		firstDay: 0,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['da'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['da'];
	}
})(jQuery);
