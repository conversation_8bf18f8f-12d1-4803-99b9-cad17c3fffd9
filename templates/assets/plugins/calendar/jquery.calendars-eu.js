﻿/* http://keith-wood.name/calendars.html
   Basque localisation for Gregorian/Julian calendars for jQuery.
   Karrikas-ek itzulia (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['eu'] = {
		name: 'Gregorian',
		epochs: ['BCE', 'CE'],
		monthNames: ['U<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON>z<PERSON><PERSON>','<PERSON>z<PERSON><PERSON>','<PERSON><PERSON>','Urri<PERSON>','Azaroa','Abend<PERSON>'],
		monthNamesShort: ['Urt','Ots','<PERSON>','Api','<PERSON>','<PERSON><PERSON>',
		'Uzt','<PERSON>','<PERSON>','Urr','A<PERSON>','<PERSON>'],
		dayNames: ['Igandea','Astelehena','As<PERSON><PERSON>a','As<PERSON>azkena','Ostegun<PERSON>','Ost<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
		dayNamesShort: ['Iga','<PERSON>t','<PERSON>t','Ast','Ost','Ost','Lar'],
		dayNamesMin: ['Ig','As','As','As','Os','Os','La'],
		dateFormat: 'yyyy/mm/dd',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['eu'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['eu'];
	}
})(jQuery);
