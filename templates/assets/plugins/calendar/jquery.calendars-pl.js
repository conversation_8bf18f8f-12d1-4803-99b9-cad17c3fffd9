﻿/* http://keith-wood.name/calendars.html
   Polish localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['pl'] = {
		name: '<PERSON><PERSON>',
		epochs: ['BCE', 'CE'],
		monthNames: ['Styczeń','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON>ździer<PERSON>','Listopad','<PERSON><PERSON>zie<PERSON>'],
		monthNamesShort: ['<PERSON>y','Lu','<PERSON>','Kw','<PERSON>','<PERSON><PERSON>',
		'Lip','Sie','Wrz','Pa','Lis','Gru'],
		dayNames: ['Niedziela','Poniedzialek','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesShort: ['Nie','Pn','Wt','Śr','<PERSON>zw','Pt','So'],
		dayNamesMin: ['N','Pn','Wt','Śr','Cz','Pt','So'],
		dateFormat: 'yyyy-mm-dd',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['pl'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['pl'];
	}
})(jQuery);
