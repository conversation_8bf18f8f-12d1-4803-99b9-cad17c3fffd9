/* ThemeRoller Blitzer override style sheet for jQuery Calendars Picker v2.0.0. */
@import "ui.calendars.picker.css";

.ui-widget-header a,
.ui-widget-header select {
	color: #ffffff; /* Set (.ui-widget-header a) colour from theme here */
}
.ui-widget-header a:hover {
	background-color: #f6f6f6; /* Set (.ui-state-hover) colours from theme here */
	color: #111111;
}
.ui-widget-header select,
.ui-widget-header option {
	background-color: #cc0000; /* Set (.ui-widget-header) background colour from theme here */
}
.ui-state-highlight a {
	color: #555555; /* Set (.ui-state-highlight) colour from theme here */
}
