﻿/* http://keith-wood.name/calendars.html
   Turkish localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['tr'] = {
		name: 'Gregorian',
		epochs: ['BCE', 'CE'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
		monthNamesShort: ['<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','May','<PERSON><PERSON>',
		'<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>'],
		dayNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','Çarşam<PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
		dayNamesShort: ['<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','Ct'],
		dayNamesMin: ['P<PERSON>','Pt','Sa','<PERSON><PERSON>','<PERSON>e','Cu','Ct'],
		dateFormat: 'dd.mm.yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['tr'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['tr'];
	}
})(jQuery);
