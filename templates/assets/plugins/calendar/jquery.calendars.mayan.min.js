﻿/* http://keith-wood.name/calendars.html
   Mayan calendar for jQuery v2.0.0.
   Written by <PERSON> (kbwood{at}iinet.com.au) August 2009.
   Available under the MIT (https://github.com/jquery/jquery/blob/master/MIT-LICENSE.txt) license. 
   Please attribute the author if you use it. */
(function($){function MayanCalendar(a){this.local=this.regionalOptions[a||'']||this.regionalOptions['']}MayanCalendar.prototype=new $.calendars.baseCalendar;$.extend(MayanCalendar.prototype,{name:'Mayan',jdEpoch:584282.5,hasYearZero:true,minMonth:0,firstMonth:0,minDay:0,regionalOptions:{'':{name:'Mayan',epochs:['',''],monthNames:['0','1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17'],monthNamesShort:['0','1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17'],dayNames:['0','1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19'],dayNamesShort:['0','1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19'],dayNamesMin:['0','1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19'],dateFormat:'YYYY.m.d',firstDay:0,isRTL:false,haabMonths:['Pop','Uo','Zip','Zotz','Tzec','Xul','Yaxkin','Mol','Chen','Yax','Zac','Ceh','Mac','Kankin','Muan','Pax','Kayab','Cumku','Uayeb'],tzolkinMonths:['Imix','Ik','Akbal','Kan','Chicchan','Cimi','Manik','Lamat','Muluc','Oc','Chuen','Eb','Ben','Ix','Men','Cib','Caban','Etznab','Cauac','Ahau']}},leapYear:function(a){this._validate(a,this.minMonth,this.minDay,$.calendars.local.invalidYear);return false},formatYear:function(a){var b=this._validate(a,this.minMonth,this.minDay,$.calendars.local.invalidYear);a=b.year();var c=Math.floor(a/400);a=a%400;a+=(a<0?400:0);var d=Math.floor(a/20);return c+'.'+d+'.'+(a%20)},forYear:function(a){a=a.split('.');if(a.length<3){throw'Invalid Mayan year';}var b=0;for(var i=0;i<a.length;i++){var y=parseInt(a[i],10);if(Math.abs(y)>19||(i>0&&y<0)){throw'Invalid Mayan year';}b=b*20+y}return b},monthsInYear:function(a){this._validate(a,this.minMonth,this.minDay,$.calendars.local.invalidYear);return 18},weekOfYear:function(a,b,c){this._validate(a,b,c,$.calendars.local.invalidDate);return 0},daysInYear:function(a){this._validate(a,this.minMonth,this.minDay,$.calendars.local.invalidYear);return 360},daysInMonth:function(a,b){this._validate(a,b,this.minDay,$.calendars.local.invalidMonth);return 20},daysInWeek:function(){return 5},dayOfWeek:function(a,b,c){var d=this._validate(a,b,c,$.calendars.local.invalidDate);return d.day()},weekDay:function(a,b,c){this._validate(a,b,c,$.calendars.local.invalidDate);return true},extraInfo:function(a,b,c){var d=this._validate(a,b,c,$.calendars.local.invalidDate);var e=d.toJD();var f=this._toHaab(e);var g=this._toTzolkin(e);return{haabMonthName:this.local.haabMonths[f[0]-1],haabMonth:f[0],haabDay:f[1],tzolkinDayName:this.local.tzolkinMonths[g[0]-1],tzolkinDay:g[0],tzolkinTrecena:g[1]}},_toHaab:function(a){a-=this.jdEpoch;var b=mod(a+8+((18-1)*20),365);return[Math.floor(b/20)+1,mod(b,20)]},_toTzolkin:function(a){a-=this.jdEpoch;return[amod(a+20,20),amod(a+4,13)]},toJD:function(a,b,c){var d=this._validate(a,b,c,$.calendars.local.invalidDate);return d.day()+(d.month()*20)+(d.year()*360)+this.jdEpoch},fromJD:function(a){a=Math.floor(a)+0.5-this.jdEpoch;var b=Math.floor(a/360);a=a%360;a+=(a<0?360:0);var c=Math.floor(a/20);var d=a%20;return this.newDate(b,c,d)}});function mod(a,b){return a-(b*Math.floor(a/b))}function amod(a,b){return mod(a-1,b)+1}$.calendars.calendars.mayan=MayanCalendar})(jQuery);