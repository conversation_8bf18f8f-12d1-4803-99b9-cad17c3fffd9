﻿/* http://keith-wood.name/calendars.html
   Slovak localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON><PERSON><PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['sk'] = {
		name: '<PERSON><PERSON>',
		epochs: ['BCE', '<PERSON>'],
		monthNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','August','September','Október','November','December'],
		monthNamesShort: ['Jan','Feb','<PERSON>','Apr','<PERSON><PERSON><PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','Aug','Sep','Okt','Nov','Dec'],
		dayNames: ['<PERSON><PERSON>\'a','Pondelok','Utorok','Streda','Štvrtok','Piatok','<PERSON><PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>tv','<PERSON><PERSON>','Sob'],
		dayNamesMin: ['N<PERSON>','<PERSON>','Ut','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','So'],
		dateFormat: 'dd.mm.yyyy',
		firstDay: 0,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['sk'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['sk'];
	}
})(jQuery);
