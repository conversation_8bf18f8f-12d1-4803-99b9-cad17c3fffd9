﻿/* ThemeRoller Overcast override style sheet for jQuery Calendars Picker v2.0.0. */
@import "ui.calendars.picker.css";

.ui-widget-header a,
.ui-widget-header select {
	color: #444444; /* Set (.ui-widget-header a) colour from theme here */
}
.ui-widget-header a:hover {
	background-color: #f8f8f8; /* Set (.ui-state-hover) colours from theme here */
	color: #599fcf;
}
.ui-widget-header select,
.ui-widget-header option {
	background-color: #dddddd; /* Set (.ui-widget-header) background colour from theme here */
}
.ui-state-highlight a {
	color: #444444; /* Set (.ui-state-highlight) colour from theme here */
}
