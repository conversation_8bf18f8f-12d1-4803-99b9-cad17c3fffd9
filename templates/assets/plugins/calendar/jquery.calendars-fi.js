﻿/* http://keith-wood.name/calendars.html
   Finnish localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON><PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['fi'] = {
		name: '<PERSON><PERSON>',
		epochs: ['BCE', 'CE'],
        monthNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>&auml;kuu',
        'Hein&auml;kuu','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>ku<PERSON>','<PERSON>aku<PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
        monthNamesShort: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','Ke<PERSON>&auml;',
        'He<PERSON>&auml;','<PERSON><PERSON>','<PERSON>y<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>'],
		dayNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>antai'],
		dayNamesMin: ['Su','Ma','Ti','Ke','To','Pe','La'],
        dateFormat: 'dd.mm.yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['fi'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['fi'];
	}
})(jQuery);
