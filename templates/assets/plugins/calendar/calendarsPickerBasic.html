﻿<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html;charset=utf-8">
<title>jQuery Calendars Datepicker</title>
<link rel="stylesheet" href="jquery.calendars.picker.css">
<!-- OR for ThemeRoller styling
<link rel="stylesheet" href="http://ajax.googleapis.com/ajax/libs/jqueryui/1.10.3/themes/south-street/jquery-ui.css">
<link rel="stylesheet" href="ui-southstreet.datepick.css">
-->
<script src="http://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
<script src="jquery.plugin.js"></script>
<!--<script src="jquery.calendars.all.js"></script><!-- Use instead of calendars, plus, and picker below -->
<script src="jquery.calendars.js"></script>
<script src="jquery.calendars.plus.js"></script>
<script src="jquery.calendars.picker.js"></script>
<!--<script src="jquery.calendars.picker.ext.js"></script><!-- Include for ThemeRoller styling -->
<script src="jquery.calendars.persian.js"></script>
<script>
$(function() {
//	$.calendars.picker.setDefaults({renderer: $.calendars.picker.themeRollerRenderer}); // Requires jquery.calendars.picker.ext.js
	var calendar = $.calendars.instance('persian');
	$('#popupDatepicker').calendarsPicker({calendar: calendar});
	$('#inlineDatepicker').calendarsPicker({calendar: calendar, onSelect: showDate});
});

function showDate(date) {
	alert('The date chosen is ' + date);
}
</script>
</head>
<body>
<h1>jQuery Calendars Datepicker</h1>
<p>This page demonstrates the very basics of the
	<a href="http://keith-wood.name/calendarsPicker.html">jQuery Calendars Datepicker plugin</a>.
	It contains the minimum requirements for using the plugin and
	can be used as the basis for your own experimentation.</p>
<p>For more detail see the <a href="http://keith-wood.name/calendarsPickerRef.html">documentation reference</a> page.</p>
<p>A popup datepicker <input type="text" id="popupDatepicker"></p>
<p>Or inline</p>
<div id="inlineDatepicker"></div>
</body>
</html>
