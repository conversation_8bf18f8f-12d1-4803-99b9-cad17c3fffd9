﻿/* http://keith-wood.name/calendars.html
   Estonian localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON> (mrts.pydev at gmail com). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['et'] = {
		name: '<PERSON><PERSON>',
		epochs: ['<PERSON>', '<PERSON>'],
		monthNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>', 
			'<PERSON><PERSON>','August','September','Oktoober','November','Detsember'],
		monthNamesShort: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>',
			'<PERSON><PERSON>', '<PERSON>', 'Sept', 'Ok<PERSON>', 'Nov', 'Det<PERSON>'],
		dayNames: ['<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
		dayNamesShort: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','<PERSON>','<PERSON>','<PERSON>','N','R','L'],
		dateFormat: 'dd.mm.yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['et'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['et'];
	}
})(jQuery);
