﻿/* http://keith-wood.name/calendars.html
   Hebrew calendar for jQuery v2.0.0.
   Written by <PERSON> (kbwood{at}iinet.com.au) August 2009.
   Available under the MIT (https://github.com/jquery/jquery/blob/master/MIT-LICENSE.txt) license. 
   Please attribute the author if you use it. */
(function($){function HebrewCalendar(a){this.local=this.regionalOptions[a||'']||this.regionalOptions['']}HebrewCalendar.prototype=new $.calendars.baseCalendar;$.extend(HebrewCalendar.prototype,{name:'Hebrew',jdEpoch:347995.5,daysPerMonth:[30,29,30,29,30,29,30,29,30,29,30,29,29],hasYearZero:false,minMonth:1,firstMonth:7,minDay:1,regionalOptions:{'':{name:'Hebrew',epochs:['BAM','AM'],monthNames:['<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON> II'],monthNamesShort:['Nis','Iya','<PERSON>v','<PERSON>','Av','Elu','Tis','Che','Kis','Tev','She','Ada','Ad2'],dayNames:['Yom Rishon','Yom Sheni','Yom Shlishi','Yom Revi\'i','Yom Chamishi','Yom Shishi','Yom Shabbat'],dayNamesShort:['Ris','She','Shl','Rev','Cha','Shi','Sha'],dayNamesMin:['Ri','She','Shl','Re','Ch','Shi','Sha'],dateFormat:'dd/mm/yyyy',firstDay:0,isRTL:false}},leapYear:function(a){var b=this._validate(a,this.minMonth,this.minDay,$.calendars.local.invalidYear);return this._leapYear(b.year())},_leapYear:function(a){a=(a<0?a+1:a);return mod(a*7+1,19)<7},monthsInYear:function(a){this._validate(a,this.minMonth,this.minDay,$.calendars.local.invalidYear);return this._leapYear(a.year?a.year():a)?13:12},weekOfYear:function(a,b,c){var d=this.newDate(a,b,c);d.add(-d.dayOfWeek(),'d');return Math.floor((d.dayOfYear()-1)/7)+1},daysInYear:function(a){var b=this._validate(a,this.minMonth,this.minDay,$.calendars.local.invalidYear);a=b.year();return this.toJD((a===-1?+1:a+1),7,1)-this.toJD(a,7,1)},daysInMonth:function(a,b){if(a.year){b=a.month();a=a.year()}this._validate(a,b,this.minDay,$.calendars.local.invalidMonth);return(b===12&&this.leapYear(a)?30:(b===8&&mod(this.daysInYear(a),10)===5?30:(b===9&&mod(this.daysInYear(a),10)===3?29:this.daysPerMonth[b-1])))},weekDay:function(a,b,c){return this.dayOfWeek(a,b,c)!==6},extraInfo:function(a,b,c){var d=this._validate(a,b,c,$.calendars.local.invalidDate);return{yearType:(this.leapYear(d)?'embolismic':'common')+' '+['deficient','regular','complete'][this.daysInYear(d)%10-3]}},toJD:function(a,b,c){var d=this._validate(a,b,c,$.calendars.local.invalidDate);a=d.year();b=d.month();c=d.day();var e=(a<=0?a+1:a);var f=this.jdEpoch+this._delay1(e)+this._delay2(e)+c+1;if(b<7){for(var m=7;m<=this.monthsInYear(a);m++){f+=this.daysInMonth(a,m)}for(var m=1;m<b;m++){f+=this.daysInMonth(a,m)}}else{for(var m=7;m<b;m++){f+=this.daysInMonth(a,m)}}return f},_delay1:function(a){var b=Math.floor((235*a-234)/19);var c=12084+13753*b;var d=b*29+Math.floor(c/25920);if(mod(3*(d+1),7)<3){d++}return d},_delay2:function(a){var b=this._delay1(a-1);var c=this._delay1(a);var d=this._delay1(a+1);return((d-c)===356?2:((c-b)===382?1:0))},fromJD:function(a){a=Math.floor(a)+0.5;var b=Math.floor(((a-this.jdEpoch)*98496.0)/35975351.0)-1;while(a>=this.toJD((b===-1?+1:b+1),7,1)){b++}var c=(a<this.toJD(b,1,1))?7:1;while(a>this.toJD(b,c,this.daysInMonth(b,c))){c++}var d=a-this.toJD(b,c,1)+1;return this.newDate(b,c,d)}});function mod(a,b){return a-(b*Math.floor(a/b))}$.calendars.calendars.hebrew=HebrewCalendar})(jQuery);