﻿/* http://keith-wood.name/calendars.html
   Romanian localisation for Gregorian/Julian calendars for jQuery.
   Written by <PERSON> (<EMAIL>) and <PERSON><PERSON> (<EMAIL>). */
(function($) {
	$.calendars.calendars.gregorian.prototype.regionalOptions['ro'] = {
		name: '<PERSON><PERSON>',
		epochs: ['BCE', 'CE'],
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','August','Septembrie','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','Decembrie'],
		monthNamesShort: ['<PERSON>', 'Feb', 'Mar', 'Apr', '<PERSON>', 'Iun',
		'I<PERSON>', 'Aug', 'Sep', 'Oct', 'Noi', 'Dec'],
		dayNames: ['Duminic<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'],
		dayNamesShort: ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','Vi','Sâ'],
		dateFormat: 'dd.mm.yyyy',
		firstDay: 1,
		isRTL: false
	};
	if ($.calendars.calendars.julian) {
		$.calendars.calendars.julian.prototype.regionalOptions['ro'] =
			$.calendars.calendars.gregorian.prototype.regionalOptions['ro'];
	}
})(jQuery);
