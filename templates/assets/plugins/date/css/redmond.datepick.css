/* Redmond style sheet for j<PERSON><PERSON><PERSON> v5.0.0. */
.datepick {
	background-color: #fff;
	color: #222;
	border: 1px solid #4297d7;
    border-radius: 0.25em;
    -moz-border-radius: 0.25em;
    -webkit-border-radius: 0.25em;
	font-family: Arial,Helvetica,Sans-serif;
	font-size: 90%;
}
.datepick-rtl {
	direction: rtl;
}
.datepick-popup {
	z-index: 1000;
}
.datepick-disable {
	position: absolute;
	z-index: 100;
	background-color: white;
	opacity: 0.5;
	filter: alpha(opacity=50);
}
.datepick a {
	color: #222;
	text-decoration: none;
}
.datepick a.datepick-disabled {
	color: #888;
	cursor: auto;
}
.datepick button {
    margin: 0.25em;
    padding: 0.125em 0em;
	background-color: #5c9ccc;
	color: #fff;
    border: none;
    border-radius: 0.25em;
    -moz-border-radius: 0.25em;
    -webkit-border-radius: 0.25em;
    font-weight: bold;
}
.datepick-nav, .datepick-ctrl {
	float: left;
	width: 100%;
	background-color: #fff;
	font-size: 90%;
	font-weight: bold;
}
.datepick-ctrl {
	background-color: #d0e5f5;
}
.datepick-cmd {
	width: 30%;
}
.datepick-cmd:hover {
	background-color: #dfeffc;
}
button.datepick-cmd:hover {
	background-color: #79b7e7;
}
.datepick-cmd-prevJump, .datepick-cmd-nextJump {
	width: 8%;
}
a.datepick-cmd {
	height: 1.5em;
}
button.datepick-cmd {
	text-align: center;
}
.datepick-cmd-prev, .datepick-cmd-prevJump, .datepick-cmd-clear {
	float: left;
	padding-left: 2%;
}
.datepick-cmd-current, .datepick-cmd-today {
	float: left;
	width: 35%;
	text-align: center;
}
.datepick-cmd-next, .datepick-cmd-nextJump, .datepick-cmd-close {
	float: right;
	padding-right: 2%;
	text-align: right;
}
.datepick-rtl .datepick-cmd-prev, .datepick-rtl .datepick-cmd-prevJump,
.datepick-rtl .datepick-cmd-clear {
	float: right;
	padding-left: 0%;
	padding-right: 2%;
	text-align: right;
}
.datepick-rtl .datepick-cmd-current, .datepick-rtl .datepick-cmd-today {
	float: right;
}
.datepick-rtl .datepick-cmd-next, .datepick-rtl .datepick-cmd-nextJump,
.datepick-rtl .datepick-cmd-close {
	float: left;
	padding-left: 2%;
	padding-right: 0%;
	text-align: left;
}
.datepick-month-nav {
	float: left;
	text-align: center;
}
.datepick-month-nav div {
	float: left;
	width: 12.5%;
	margin: 1%;
	padding: 1%;
}
.datepick-month-nav span {
	color: #888;
}
.datepick-month-row {
	clear: left;
}
.datepick-month {
	float: left;
	width: 15em;
	border: 1px solid #5c9ccc;
	text-align: center;
}
.datepick-month-header, .datepick-month-header select, .datepick-month-header input {
	height: 1.5em;
	background-color: #5c9ccc;
	color: #fff;
	font-weight: bold;
}
.datepick-month-header select, .datepick-month-header input {
	height: 1.4em;
	border: none;
}
.datepick-month-header input {
	position: absolute;
	display: none;
}
.datepick-month table {
	width: 100%;
	border-collapse: collapse;
}
.datepick-month thead {
	border-bottom: 1px solid #aaa;
}
.datepick-month th, .datepick-month td {
	margin: 0em;
	padding: 0em;
	font-weight: normal;
	text-align: center;
}
.datepick-month th {
	border: 1px solid #fff;
	border-bottom: 1px solid #c5dbec;
}
.datepick-month td {
	border: 1px solid #c5dbec;
}
.datepick-month td.datepick-week * {
	background-color: #d0e5f5;
	color: #222;
	border: none;
}
.datepick-month a {
	display: block;
	width: 100%;
	padding: 0.125em 0em;
	background-color: #dfeffc;
	color: #000;
	text-decoration: none;
}
.datepick-month span {
	display: block;
	width: 100%;
	padding: 0.125em 0em;
}
.datepick-month td span {
	color: #888;
}
.datepick-month td .datepick-other-month {
	background-color: #fff;
}
.datepick-month td .datepick-today {
	background-color: #fad42e;
}
.datepick-month td .datepick-highlight {
	background-color: #79b7e7;
}
.datepick-month td .datepick-selected {
	background-color: #4297d7;
	color: #fff;
}
.datepick-status {
	clear: both;
	text-align: center;
}
.datepick-clear-fix {
	clear: both;
}
