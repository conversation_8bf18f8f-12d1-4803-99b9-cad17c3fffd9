<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>

	<title>ColReorder examples - ColReorder examples</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>ColReorder example <span>ColReorder examples</span></h1>

			<div class="info">
				<p>ColReorder adds the ability for the end user to click and drag column headers to reorder a table as
				they see fit, to DataTables. Key features include:</p>

				<ul class="markdown">
					<li>Very easy integration with DataTables</li>
					<li>Tight integration with all other DataTables plug-ins</li>
					<li>The ability to exclude the first (or more) column from being movable</li>
					<li>Predefine a column order</li>
					<li>Save staving integration with DataTables</li>
				</ul>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Examples</a></h3>
						<ul class="toc">
							<li><a href="./simple.html">Basic initialisation</a></li>
							<li><a href="./new_init.html">Initialisation using `new`</a></li>
							<li><a href="./alt_insert.html">Alternative insert styling</a></li>
							<li><a href="./realtime.html">Realtime updating</a></li>
							<li><a href="./state_save.html">State saving</a></li>
							<li><a href="./scrolling.html">Scrolling table</a></li>
							<li><a href="./predefined.html">Predefined column ordering</a></li>
							<li><a href="./reset.html">Reset ordering API</a></li>
							<li><a href="./colvis.html">ColVis integration</a></li>
							<li><a href="./fixedcolumns.html">FixedColumns integration</a></li>
							<li><a href="./fixedheader.html">FixedHeader integration</a></li>
							<li><a href="./jqueryui.html">jQuery UI styling</a></li>
							<li><a href="./col_filter.html">Individual column filtering</a></li>
							<li><a href="./server_side.html">Server-side processing</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full
					information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and
					<a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of
					DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href=
					"http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2014<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>