/* http://keith-wood.name/countdown.html
   Estonian initialisation for the jQuery countdown extension
   Written by <PERSON><PERSON> <helmer{at}city.ee> */
(function($) {
    $.countdown.regionalOptions['et'] = {
        labels: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
        labels1: ['<PERSON><PERSON><PERSON>', 'Ku<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Tund', 'Minut', 'Sekund'],
        compactLabels: ['a', 'k', 'n', 'p'],
        whichLabels: null,
		digits: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
        timeSeparator: ':', isRTL: false};
    $.countdown.setDefaults($.countdown.regionalOptions['et']);
})(jQuery);
