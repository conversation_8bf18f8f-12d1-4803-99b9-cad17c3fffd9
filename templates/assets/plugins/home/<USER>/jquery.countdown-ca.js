﻿/* http://keith-wood.name/countdown.html
   Catalan initialisation for the jQuery countdown extension
   Written by Amanida Media www.amanidamedia.com (2010) */
(function($) {
	$.countdown.regionalOptions['ca'] = {
		labels: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Minuts', 'Se<PERSON><PERSON>'],
		labels1: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>res', 'Minuts', 'Segons'],
		compactLabels: ['a', 'm', 's', 'g'],
		whichLabels: null,
		digits: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
		timeSeparator: ':', isRTL: false};
	$.countdown.setDefaults($.countdown.regionalOptions['ca']);
})(jQuery);
