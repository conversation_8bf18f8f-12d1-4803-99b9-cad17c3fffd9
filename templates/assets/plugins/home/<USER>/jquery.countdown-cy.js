﻿/* http://keith-wood.name/countdown.html
   Welsh initialisation for the jQuery countdown extension
   Written by <PERSON> | http://garethvjones.com | October 2011. */
(function($) {
	$.countdown.regionalOptions['cy'] = {
		labels: ['Blynyddo<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>nu<PERSON><PERSON>', '<PERSON><PERSON><PERSON>u'],
		labels1: ['<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Mi<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>wr', '<PERSON>nud', '<PERSON><PERSON><PERSON>'],
		compactLabels: ['b', 'm', 'w', 'd'],
		whichLabels: null,
		digits: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
		timeSeparator: ':', isRTL: false};
	$.countdown.setDefaults($.countdown.regionalOptions['cy']);
})(jQuery);
