/**
* http://keith-wood.name/countdown.html
* Croatian l10n for the jQuery countdown plugin
* Written by <PERSON><PERSON> <EMAIL> (2011)
* Improved by <PERSON><PERSON><PERSON><PERSON><PERSON> (2014)
*/
(function($) {
	$.countdown.regionalOptions['hr'] = {
		// plurals
		labels: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
		// singles
		labels1: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>t', 'Minutu', '<PERSON>kundu'],
		// paucals
		labels2: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', 'Minute', '<PERSON>ku<PERSON>'],
		compactLabels: ['g', 'm', 't', 'd'],
		whichLabels: function(amount){
			amount = parseInt(amount, 10);
			if (amount % 10 === 1 && amount % 100 !== 11) {
				return 1; // singles (/.*1$/ && ! /.*11$/)
			}
			if (amount % 10 >= 2 && amount % 10 <= 4 && (amount % 100 < 10 || amount % 100 >= 20)) {
				return 2; // paucals (/.*[234]$/ && ! /.*1[234]$/
			}
			return 0; // default plural (most common case)
		},
		digits: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
		timeSeparator: ':', isRTL: false};
	$.countdown.setDefaults($.countdown.regionalOptions['hr']);
})(jQuery);
