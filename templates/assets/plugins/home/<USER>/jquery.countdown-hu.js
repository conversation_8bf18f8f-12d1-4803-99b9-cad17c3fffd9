﻿/* http://keith-wood.name/countdown.html
 * Hungarian initialisation for the jQuery countdown extension
 * Written by <PERSON> (<EMAIL>). */
(function($) {
	$.countdown.regionalOptions['hu'] = {
		labels: ['<PERSON>v', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>'],
		labels1: ['<PERSON>v', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Na<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>odper<PERSON>'],
		compactLabels: ['É', 'H', 'Hé', 'N'],
		whichLabels: null,
		digits: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
		timeSeparator: ':', isRTL: false};
	$.countdown.setDefaults($.countdown.regionalOptions['hu']);
})(jQuery);
