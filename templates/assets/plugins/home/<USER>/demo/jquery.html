<!doctype html>
<html lang="en">
<head>
	<meta charset="UTF-8"/>
	<title>jQuery</title>
	<meta name="viewport" content="width=device-width"/>
	<link rel="stylesheet" href="style.css"/>
</head>
<body>

	<ul>
		<li><a href="index.html">Vanilla JS</a></li>
		<li><a href="jquery.html" class="active">jQuery plugin</a></li>
		<li><a href="angular.html">Angular module</a></li>
		<li><a href="requirejs.html">RequireJS module</a></li>
	</ul>

	<span class="chart" data-percent="86">
		<span class="percent"></span>
	</span>

	<span class="btn js_update">Update chart</span>

	<script src="https://code.jquery.com/jquery-2.0.3.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-easing/1.3/jquery.easing.min.js"></script>
	<script src="../dist/jquery.easypiechart.min.js"></script>
	<script>
	$(function() {
		$('.chart').easyPieChart({
			easing: 'easeOutBounce',
			onStep: function(from, to, percent) {
				$(this.el).find('.percent').text(Math.round(percent));
			}
		});
		var chart = window.chart = $('.chart').data('easyPieChart');
		$('.js_update').on('click', function() {
			chart.update(Math.random()*200-100);
		});
	});
	</script>
</body>
</html>
