/* http://keith-wood.name/countdown.html
 * Bosnian Latin initialisation for the jQuery countdown extension
 * Written by <PERSON><PERSON> Me<PERSON> <EMAIL> (2011) */
(function($) {
	$.countdown.regionalOptions['bs'] = {
		labels: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
		labels1: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
		labels2: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>d<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', 'Minute', '<PERSON>ku<PERSON>'],
		compactLabels: ['g', 'm', 't', 'd'],
		whichLabels: function(amount) {
			return (amount == 1 ? 1 : (amount >= 2 && amount <= 4 ? 2 : 0));
		},
		digits: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
		timeSeparator: ':', isRTL: false};
	$.countdown.setDefaults($.countdown.regionalOptions['bs']);
})(jQuery);
