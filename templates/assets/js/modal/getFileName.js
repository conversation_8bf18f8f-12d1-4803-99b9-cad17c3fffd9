let files = document.getElementById('files');
let fileNameHolder = document.getElementById('filenameHolder');
let docTitle = document.getElementById('docTitle');
debugger
function getFileExtension(filename) {
    return filename.split('.').pop();
}
var fileExtention
files.addEventListener('change',function(){
    let filename = files.files[0].name;
    fileNameHolder.innerText = filename;
    docTitle.setAttribute("placeholder", filename)
    fileExtention = getFileExtension(filename)
})