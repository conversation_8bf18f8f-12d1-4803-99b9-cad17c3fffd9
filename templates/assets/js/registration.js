/**
 * Created by <PERSON><PERSON><PERSON> on 31/03/16.
 */

function registration_init() {

    var regTypeEmail = $('#regTypeEmail');
    var regTypeMobile = $('#regTypeMobile');

    // Put the right selections on load
    if (regTypeEmail.is(':checked')) {
        $('#regMethod').html(emailChoice).hide(0);
        $('#regMethod').show(500);

    } else if (regTypeMobile.is(':checked')) {
        $('#regMethod').html(mobileChoice).hide(0);
        $('#regMethod').show(500);
    }

    regTypeEmail.click(function () {
        $('#regMethod').html(emailChoice).hide(0);
        $('#regMethod').show(500);
    });

    regTypeMobile.click(function () {
        $('#regMethod').html(mobileChoice).hide(0);
        $('#regMethod').show(500);
    });
}
