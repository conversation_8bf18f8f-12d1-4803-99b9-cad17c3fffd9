﻿function getInternetExplorerVersion(){var n=-1,t,i;return navigator.appName=="Microsoft Internet Explorer"&&(t=navigator.userAgent,i=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})"),i.exec(t)!=null&&(n=parseFloat(RegExp.$1))),n}function checkVersion(){var n="You're not using Windows Internet Explorer.",t=getInternetExplorerVersion();t>-1&&(n=t>=8?"You're using a recent copy of Windows Internet Explorer.":"You should upgrade your copy of Windows Internet Explorer.");alert(n)}function isIE8orlower(){var n="0",t=getInternetExplorerVersion();return t>-1&&(n=t>=9?0:1),n}(function(n){n(["jquery"],function(n){return function(){function l(n,t,f){return u({type:r.custom,iconClass:i().iconClass,message:n,optionsOverride:f,title:t})}function a(n,t,f){return u({type:r.error,iconClass:i().iconClasses.error,message:n,optionsOverride:f,title:t})}function v(n,t,f){return u({type:r.info,iconClass:i().iconClasses.info,message:n,optionsOverride:f,title:t})}function y(n){e=n}function p(n,t,f){return u({type:r.success,iconClass:i().iconClasses.success,message:n,optionsOverride:f,title:t})}function w(n,t,f){return u({type:r.warning,iconClass:i().iconClasses.warning,message:n,optionsOverride:f,title:t})}function b(r){var u=i();if(t||f(u),r&&n(":focus",r).length===0){r[u.hideMethod]({duration:u.hideDuration,easing:u.hideEasing,complete:function(){c(r)}});return}t.children().length&&t[u.hideMethod]({duration:u.hideDuration,easing:u.hideEasing,complete:function(){t.remove()}})}function k(){return{tapToDismiss:!0,toastClass:"toast",containerId:"toast-container",debug:!1,showMethod:"fadeIn",showDuration:300,showEasing:"swing",onShown:undefined,hideMethod:"fadeOut",hideDuration:1e3,hideEasing:"swing",onHidden:undefined,extendedTimeOut:1e3,iconClasses:{error:"toast-error",info:"toast-info",success:"toast-success",warning:"toast-warning",custom:"toast-custom"},iconClass:"toast-info",positionClass:"toast-top-right",timeOut:5e3,titleClass:"toast-title",messageClass:"toast-message",target:"body",closeHtml:"<button>&times;<\/button>",newestOnTop:!0}}function h(n){e&&e(n)}function u(r){function a(t){if(!n(":focus",e).length||t)return e[u.hideMethod]({duration:u.hideDuration,easing:u.hideEasing,complete:function(){c(e);u.onHidden&&u.onHidden();l.state="hidden";l.endTime=new Date;h(l)}})}function d(){(u.timeOut>0||u.extendedTimeOut>0)&&(p=setTimeout(a,u.extendedTimeOut))}function g(){clearTimeout(p);e.stop(!0,!0)[u.showMethod]({duration:u.showDuration,easing:u.showEasing})}var u=i(),y=r.iconClass||u.iconClass,k,s;typeof r.optionsOverride!="undefined"&&(u=n.extend(u,r.optionsOverride),y=r.optionsOverride.iconClass||y);o++;t=f(u);var p=null,e=n("<div/>"),w=n("<div/>"),b=n("<div/>"),v=n(u.closeHtml),l={toastId:o,state:"visible",startTime:new Date,options:u,map:r};return r.iconClass&&e.addClass(u.toastClass).addClass(y),r.title&&(w.append(r.title).addClass(u.titleClass),e.append(w)),r.message&&(b.append(r.message).addClass(u.messageClass),e.append(b)),u.closeButton&&(v.addClass("toast-close-button"),e.prepend(v)),u.positionClass&&t.attr("class")!=u.positionClass&&(t.html(""),t.removeAttr("class"),t.addClass(u.positionClass)),e.hide(),u.newestOnTop?t.prepend(e):t.append(e),e[u.showMethod]({duration:u.showDuration,easing:u.showEasing,complete:u.onShown}),u.timeOut>0&&(p=setTimeout(a,u.timeOut)),e.hover(g,d),!u.onclick&&u.tapToDismiss&&e.click(a),u.closeButton&&v&&v.click(function(n){n.stopPropagation?n.stopPropagation():n.cancelBubble!==undefined&&n.cancelBubble!==!0&&(n.cancelBubble=!0);a(!0)}),u.onclick&&e.click(function(){u.onclick();a()}),h(l),k=0,k=1,isIE8orlower()==0&&(s=document.createElement("audio"),s.setAttribute("src","assets/sound/alert.mp3"),n.get(),s.addEventListener("load",function(){s.play()},!0),s.pause(),s.play()),u.debug&&console&&console.log(l),e}function f(r){return(r||(r=i()),t=n("#"+r.containerId),t.length)?t:(t=n("<div/>").attr("id",r.containerId).addClass(r.positionClass),t.appendTo(n(r.target)),t)}function i(){return n.extend({},k(),s.options)}function c(n){(t||(t=f()),n.is(":visible"))||(n.remove(),n=null,t.children().length===0&&t.remove())}var t,e,o=0,r={error:"error",info:"info",success:"success",warning:"warning",custom:"custom"},s={clear:b,error:a,getContainer:f,info:v,options:{},subscribe:y,success:p,version:"2.0.1",warning:w,custom:l};return s}()})})(typeof define=="function"&&define.amd?define:function(n,t){typeof module!="undefined"&&module.exports?module.exports=t(require("jquery")):window.toastr=t(window.jQuery)});
/*
//# sourceMappingURL=toastr.min.js.map
*/