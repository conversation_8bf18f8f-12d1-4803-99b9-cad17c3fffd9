﻿(function(n){var t=function(t){var r={x:0,y:0},i=t.getOptions(),e=function(n){r.x=n.x;r.y=n.y},o=function(n){var t={x:0,y:0};t.x=n.pageX;t.y=n.pageY;e(t)},s=function(t){var r=new Date(t);return n.plot.formatDate(r,i.tooltipOpts.dateFormat)},f,u;t.hooks.bindEvents.push(function(t,u){var h=i.tooltipOpts,c=t.getPlaceholder(),e;i.tooltip!==!1&&(n("#flotTip").length>0?e=n("#flotTip"):(e=n("<div />").attr("id","flotTip"),e.appendTo("body").hide().css({position:"absolute"}),h.defaultTheme&&e.css({background:"#fff","z-index":"100",padding:"0.4em 0.6em","border-radius":"0.5em","font-size":"0.8em",border:"1px solid #111"})),n(c).bind("plothover",function(n,t,u){if(u){var o;o=i.xaxis.mode==="time"||i.xaxes[0].mode==="time"?f(h.content,u,s):f(h.content,u);e.html(o).css({left:r.x+h.shifts.x,top:r.y+h.shifts.y}).show()}else e.hide().html("")}),u.mousemove(o))});f=function(n,t,i){var r=/%x\.{0,1}(\d{0,})/;return typeof t.series.percent!="undefined"&&(n=u(/%p\.{0,1}(\d{0,})/,n,t.series.percent)),typeof t.series.label!="undefined"&&(n=n.replace(/%s/,t.series.label)),typeof i=="function"?n=n.replace(r,i(t.series.data[t.dataIndex][0])):typeof t.series.data[t.dataIndex][0]=="number"&&(n=u(r,n,t.series.data[t.dataIndex][0])),typeof t.series.data[t.dataIndex][1]=="number"&&(n=u(/%y\.{0,1}(\d{0,})/,n,t.series.data[t.dataIndex][1])),n};u=function(n,t,i){var r;return t.match(n)!=="null"&&(RegExp.$1!==""&&(r=RegExp.$1,i=i.toFixed(r)),t=t.replace(n,i)),t}};n.plot.plugins.push({init:t,options:{tooltip:!1,tooltipOpts:{content:"%s | X: %x | Y: %y.2",dateFormat:"%y-%0m-%0d",shifts:{x:10,y:20},defaultTheme:!0}},name:"tooltip",version:"0.4.4"})})(jQuery);
/*
//# sourceMappingURL=jquery.flot.tooltip.min.js.map
*/