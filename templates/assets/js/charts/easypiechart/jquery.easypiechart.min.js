﻿(function(n){return n.easyPieChart=function(t,i){var o,s,u,h,f,e,c,l,r=this;return this.el=t,this.$el=n(t),this.$el.data("easyPieChart",this),this.init=function(){var u,t;return r.options=n.extend({},n.easyPieChart.defaultOptions,i),u=parseInt(r.$el.data("percent"),10),r.percentage=0,r.canvas=n("<canvas width='"+r.options.size+"' height='"+r.options.size+"'><\/canvas>").get(0),r.$el.append(r.canvas),typeof G_vmlCanvasManager!="undefined"&&G_vmlCanvasManager!==null&&G_vmlCanvasManager.initElement(r.canvas),r.ctx=r.canvas.getContext("2d"),window.devicePixelRatio>1&&(t=window.devicePixelRatio,n(r.canvas).css({width:r.options.size,height:r.options.size}),r.canvas.width*=t,r.canvas.height*=t,r.ctx.scale(t,t)),r.ctx.translate(r.options.size/2,r.options.size/2),r.ctx.rotate(r.options.rotate*Math.PI/180),r.$el.addClass("easyPieChart"),r.$el.css({width:r.options.size,height:r.options.size,lineHeight:""+r.options.size+"px"}),r.update(u),r},this.update=function(n){return n=parseFloat(n)||0,r.options.animate===!1?u(n):s(r.percentage,n),r},c=function(){var n,t,i;for(r.ctx.fillStyle=r.options.scaleColor,r.ctx.lineWidth=1,i=[],n=t=0;t<=24;n=++t)i.push(o(n));return i},o=function(n){var t;t=n%6==0?0:r.options.size*.017;r.ctx.save();r.ctx.rotate(n*Math.PI/12);r.ctx.fillRect(r.options.size/2-t,0,-r.options.size*.05+t,1);r.ctx.restore()},l=function(){var n;n=r.options.size/2-r.options.lineWidth/2;r.options.scaleColor!==!1&&(n-=r.options.size*.08);r.ctx.beginPath();r.ctx.arc(0,0,n,0,Math.PI*2,!0);r.ctx.closePath();r.ctx.strokeStyle=r.options.trackColor;r.ctx.lineWidth=r.options.lineWidth;r.ctx.stroke()},e=function(){r.options.scaleColor!==!1&&c();r.options.trackColor!==!1&&l()},u=function(t){var i;e();r.ctx.strokeStyle=n.isFunction(r.options.barColor)?r.options.barColor(t):r.options.barColor;r.ctx.lineCap=r.options.lineCap;r.ctx.lineWidth=r.options.lineWidth;i=r.options.size/2-r.options.lineWidth/2;r.options.scaleColor!==!1&&(i-=r.options.size*.08);r.ctx.save();r.ctx.rotate(-Math.PI/2);r.ctx.beginPath();r.ctx.arc(0,0,i,0,Math.PI*2*t/100,!1);r.ctx.stroke();r.ctx.restore()},f=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(n){return window.setTimeout(n,1e3/60)}}(),s=function(n,t){var i,o;r.options.onStart.call(r);r.percentage=t;o=Date.now();i=function(){var c,s;return s=Date.now()-o,s<r.options.animate&&f(i),r.ctx.clearRect(-r.options.size/2,-r.options.size/2,r.options.size,r.options.size),e.call(r),c=[h(s,n,t-n,r.options.animate)],r.options.onStep.call(r,c),u.call(r,c),s>=r.options.animate?r.options.onStop.call(r):void 0};f(i)},h=function(n,t,i,r){var u,f;return u=function(n){return Math.pow(n,2)},f=function(n){return n<1?u(n):2-u(n*-1+2)},n/=r/2,i/2*f(n)+t},this.init()},n.easyPieChart.defaultOptions={barColor:"#ef1e25",trackColor:"#f2f2f2",scaleColor:"#dfe0e0",lineCap:"round",rotate:0,size:110,lineWidth:3,animate:!1,onStart:n.noop,onStop:n.noop,onStep:n.noop},n.fn.easyPieChart=function(t){return n.each(this,function(i,r){var u,f;return u=n(r),u.data("easyPieChart")?void 0:(f=n.extend({},t,u.data()),u.data("easyPieChart",new n.easyPieChart(r,f)))})},void 0})(jQuery);
/*
//# sourceMappingURL=jquery.easypiechart.min.js.map
*/