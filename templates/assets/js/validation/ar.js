// Validation errors messages for <PERSON><PERSON><PERSON>
// Load this after Pars<PERSON>

Parsley.addMessages('ar', {
  defaultMessage: "تأكد من صحة القيمة المدخل</span>",
  type: {
    email:      " <span class='text-danger'>تأكد من إدخال بريد الكتروني صحيح</span>",
    url:        " <span class='text-danger'>تأكد من إدخال رابط صحيح</span>",
    number:     " <span class='text-danger'>تأكد من إدخال رقم</span>",
    integer:    " <span class='text-danger'>تأكد من إدخال عدد صحيح بدون كسور</span>",
    digits:     " <span class='text-danger'>تأكد من إدخال رقم</span>",
    alphanum:   " <span class='text-danger'>تأكد من إدخال حروف وأرقام فقط"
  },
  notblank:     " <span class='text-danger'>تأكد من تعبئة الحقل</span>",
  required:     " <span class='text-danger'>هذا الحقل مطلوب</span>",
  pattern:      " <span class='text-danger'>القيمة المدخلة غير صحيحة</span>",
  min:          " <span class='text-danger'>القيمة المدخلة يجب أن تكون أكبر من %s.</span>",
  max:          " <span class='text-danger'>القيمة المدخلة يجب أن تكون أصغر من %s.</span>",
  range:        " <span class='text-danger'>القيمة المدخلة يجب أن تكون بين %s و %s.</span>",
  minlength:    " <span class='text-danger'>القيمة المدخلة قصيرة جداً . تأكد من إدخال %s حرف أو أكثر</span>",
  maxlength:    " <span class='text-danger'>القيمة المدخلة طويلة . تأكد من إدخال %s حرف أو أقل</span>",
  length:       " <span class='text-danger'>القيمة المدخلة غير صحيحة. تأكد من إدخال بين  %s و %s خانة</span>",
  mincheck:     " <span class='text-danger'>يجب اختيار %s خيار على الأقل.</span>",
  maxcheck:     " <span class='text-danger'>يجب اختيار%s خيار أو أقل</span>",
  check:        " <span class='text-danger'>يجب اختيار بين %s و %s خيار.</span>",
  equalto:      " <span class='text-danger'>تأكد من تطابق القيمتين المدخلة.</span>"
});

Parsley.setLocale('ar');
