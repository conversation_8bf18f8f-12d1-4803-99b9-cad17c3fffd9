﻿/*!
 * BootstrapValidator (http://bootstrapvalidator.com)
 * The best jQuery plugin to validate form fields. Designed to use with Bootstrap 3
 *
 * @version     v0.5.3, built on 2014-11-05 9:14:18 PM
 * <AUTHOR>
 * @copyright   (c) 2013 - 2014 <PERSON><PERSON><PERSON>
 * @license     Commercial: http://bootstrapvalidator.com/license/
 *              Non-commercial: http://creativecommons.org/licenses/by-nc-nd/3.0/
 */
if(typeof jQuery=="undefined")throw new Error("BootstrapValidator requires jQuery");(function(n){var t=n.fn.jquery.split(" ")[0].split(".");if(+t[0]<2&&+t[1]<9||+t[0]==1&&+t[1]==9&&+t[2]<1)throw new Error("BootstrapValidator requires jQuery version 1.9.1 or higher");})(window.jQuery),function(n){var t=function(t,i){this.$form=n(t);this.options=n.extend({},n.fn.bootstrapValidator.DEFAULT_OPTIONS,i);this.$invalidFields=n([]);this.$submitButton=null;this.$hiddenButton=null;this.STATUS_NOT_VALIDATED="NOT_VALIDATED";this.STATUS_VALIDATING="VALIDATING";this.STATUS_INVALID="INVALID";this.STATUS_VALID="VALID";var r=function(){for(var n=3,t=document.createElement("div"),i=t.all||[];t.innerHTML="<!--[if gt IE "+ ++n+"]><br><![endif]-->",i[0];);return n>4?n:!n}(),u=document.createElement("div");this._changeEvent=r===9||!("oninput"in u)?"keyup":"input";this._submitIfValid=null;this._cacheFields={};this._init()};t.prototype={constructor:t,_init:function(){var t=this,i={autoFocus:this.$form.attr("data-bv-autofocus"),container:this.$form.attr("data-bv-container"),events:{formInit:this.$form.attr("data-bv-events-form-init"),formError:this.$form.attr("data-bv-events-form-error"),formSuccess:this.$form.attr("data-bv-events-form-success"),fieldAdded:this.$form.attr("data-bv-events-field-added"),fieldRemoved:this.$form.attr("data-bv-events-field-removed"),fieldInit:this.$form.attr("data-bv-events-field-init"),fieldError:this.$form.attr("data-bv-events-field-error"),fieldSuccess:this.$form.attr("data-bv-events-field-success"),fieldStatus:this.$form.attr("data-bv-events-field-status"),validatorError:this.$form.attr("data-bv-events-validator-error"),validatorSuccess:this.$form.attr("data-bv-events-validator-success")},excluded:this.$form.attr("data-bv-excluded"),feedbackIcons:{valid:this.$form.attr("data-bv-feedbackicons-valid"),invalid:this.$form.attr("data-bv-feedbackicons-invalid"),validating:this.$form.attr("data-bv-feedbackicons-validating")},group:this.$form.attr("data-bv-group"),live:this.$form.attr("data-bv-live"),message:this.$form.attr("data-bv-message"),onError:this.$form.attr("data-bv-onerror"),onSuccess:this.$form.attr("data-bv-onsuccess"),submitButtons:this.$form.attr("data-bv-submitbuttons"),threshold:this.$form.attr("data-bv-threshold"),trigger:this.$form.attr("data-bv-trigger"),verbose:this.$form.attr("data-bv-verbose"),fields:{}},r;this.$form.attr("novalidate","novalidate").addClass(this.options.elementClass).on("submit.bv",function(n){n.preventDefault();t.validate()}).on("click.bv",this.options.submitButtons,function(){t.$submitButton=n(this);t._submitIfValid=!0}).find("[name], [data-bv-field]").each(function(){var r=n(this),u=r.attr("name")||r.attr("data-bv-field"),f=t._parseOptions(r);f&&(r.attr("data-bv-field",u),i.fields[u]=n.extend({},f,i.fields[u]))});this.options=n.extend(!0,this.options,i);this.$hiddenButton=n("<button/>").attr("type","submit").prependTo(this.$form).addClass("bv-hidden-submit").css({display:"none",width:0,height:0});this.$form.on("click.bv",'[type="submit"]',function(i){if(!i.isDefaultPrevented()){var r=n(i.target),u=r.is('[type="submit"]')?r.eq(0):r.parent('[type="submit"]').eq(0);!t.options.submitButtons||u.is(t.options.submitButtons)||u.is(t.$hiddenButton)||t.$form.off("submit.bv").submit()}});for(r in this.options.fields)this._initField(r);if(this.$form.trigger(n.Event(this.options.events.formInit),{bv:this,options:this.options}),this.options.onSuccess)this.$form.on(this.options.events.formSuccess,function(i){n.fn.bootstrapValidator.helpers.call(t.options.onSuccess,[i])});if(this.options.onError)this.$form.on(this.options.events.formError,function(i){n.fn.bootstrapValidator.helpers.call(t.options.onError,[i])})},_parseOptions:function(t){var v=t.attr("name")||t.attr("data-bv-field"),f={},r,u,s,e,a,h,i,c,o;for(u in n.fn.bootstrapValidator.validators)if(r=n.fn.bootstrapValidator.validators[u],s="data-bv-"+u.toLowerCase(),e=t.attr(s)+"",o="function"==typeof r.enableByHtml5?r.enableByHtml5(t):null,o&&e!=="false"||o!==!0&&(""===e||"true"===e||s===e.toLowerCase())){r.html5Attributes=n.extend({},{message:"message",onerror:"onError",onsuccess:"onSuccess"},r.html5Attributes);f[u]=n.extend({},o===!0?{}:o,f[u]);for(c in r.html5Attributes)a=r.html5Attributes[c],h="data-bv-"+u.toLowerCase()+"-"+c,i=t.attr(h),i&&("true"===i||h===i.toLowerCase()?i=!0:"false"===i&&(i=!1),f[u][a]=i)}var l={autoFocus:t.attr("data-bv-autofocus"),container:t.attr("data-bv-container"),excluded:t.attr("data-bv-excluded"),feedbackIcons:t.attr("data-bv-feedbackicons"),group:t.attr("data-bv-group"),message:t.attr("data-bv-message"),onError:t.attr("data-bv-onerror"),onStatus:t.attr("data-bv-onstatus"),onSuccess:t.attr("data-bv-onsuccess"),selector:t.attr("data-bv-selector"),threshold:t.attr("data-bv-threshold"),trigger:t.attr("data-bv-trigger"),verbose:t.attr("data-bv-verbose"),validators:f},y=n.isEmptyObject(l),p=n.isEmptyObject(f);return!p||!y&&this.options.fields&&this.options.fields[v]?(l.validators=f,l):null},_initField:function(t){var i=n([]),u,s,h,v;switch(typeof t){case"object":i=t;t=t.attr("data-bv-field");break;case"string":i=this.getFieldElements(t);i.attr("data-bv-field",t)}if(i.length!==0&&this.options.fields[t]!==null&&this.options.fields[t].validators!==null){for(u in this.options.fields[t].validators)n.fn.bootstrapValidator.validators[u]||delete this.options.fields[t].validators[u];this.options.fields[t].enabled===null&&(this.options.fields[t].enabled=!0);var e=this,l=i.length,o=i.attr("type"),p=l===1||"radio"===o||"checkbox"===o,k="radio"===o||"checkbox"===o||"file"===o||"SELECT"===i.eq(0).get(0).tagName?"change":this._changeEvent,w=(this.options.fields[t].trigger||this.options.trigger||k).split(" "),c=n.map(w,function(n){return n+".update.bv"}).join(" ");for(s=0;s<l;s++){var r=i.eq(s),b=this.options.fields[t].group||this.options.group,a=r.parents(b),f="function"==typeof(this.options.fields[t].container||this.options.container)?(this.options.fields[t].container||this.options.container).call(this,r,this):this.options.fields[t].container||this.options.container,y=f&&f!=="tooltip"&&f!=="popover"?n(f):this._getMessageContainer(r,b);f&&f!=="tooltip"&&f!=="popover"&&y.addClass("has-error");y.find('.help-block[data-bv-validator][data-bv-for="'+t+'"]').remove();a.find('i[data-bv-icon-for="'+t+'"]').remove();r.off(c).on(c,function(){e.updateStatus(n(this),e.STATUS_NOT_VALIDATED)});r.data("bv.messages",y);for(u in this.options.fields[t].validators)r.data("bv.result."+u,this.STATUS_NOT_VALIDATED),p&&s!==l-1||n("<small/>").css("display","none").addClass("help-block").attr("data-bv-validator",u).attr("data-bv-for",t).attr("data-bv-result",this.STATUS_NOT_VALIDATED).html(this._getMessage(t,u)).appendTo(y),"function"==typeof n.fn.bootstrapValidator.validators[u].init&&n.fn.bootstrapValidator.validators[u].init(this,r,this.options.fields[t].validators[u]);if(this.options.fields[t].feedbackIcons!==!1&&this.options.fields[t].feedbackIcons!=="false"&&this.options.feedbackIcons&&this.options.feedbackIcons.validating&&this.options.feedbackIcons.invalid&&this.options.feedbackIcons.valid&&(!p||s===l-1)&&(a.addClass("has-feedback"),h=n("<i/>").css("display","none").addClass("form-control-feedback").attr("data-bv-icon-for",t).insertAfter(r),("checkbox"===o||"radio"===o)&&(v=r.parent(),v.hasClass(o)?h.insertAfter(v):v.parent().hasClass(o)&&h.insertAfter(v.parent())),a.find("label").length===0&&h.addClass("bv-no-label"),a.find(".input-group").length!==0&&h.addClass("bv-icon-input-group").insertAfter(a.find(".input-group").eq(0)),p?s===l-1&&i.data("bv.icon",h):r.data("bv.icon",h),f))r.off("focus.container.bv").on("focus.container.bv",function(){switch(f){case"tooltip":n(this).data("bv.icon").tooltip("show");break;case"popover":n(this).data("bv.icon").popover("show")}}).off("blur.container.bv").on("blur.container.bv",function(){switch(f){case"tooltip":n(this).data("bv.icon").tooltip("hide");break;case"popover":n(this).data("bv.icon").popover("hide")}})}i.on(this.options.events.fieldSuccess,function(t,i){var r=e.getOptions(i.field,null,"onSuccess");r&&n.fn.bootstrapValidator.helpers.call(r,[t,i])}).on(this.options.events.fieldError,function(t,i){var r=e.getOptions(i.field,null,"onError");r&&n.fn.bootstrapValidator.helpers.call(r,[t,i])}).on(this.options.events.fieldStatus,function(t,i){var r=e.getOptions(i.field,null,"onStatus");r&&n.fn.bootstrapValidator.helpers.call(r,[t,i])}).on(this.options.events.validatorError,function(t,i){var r=e.getOptions(i.field,i.validator,"onError");r&&n.fn.bootstrapValidator.helpers.call(r,[t,i])}).on(this.options.events.validatorSuccess,function(t,i){var r=e.getOptions(i.field,i.validator,"onSuccess");r&&n.fn.bootstrapValidator.helpers.call(r,[t,i])});c=n.map(w,function(n){return n+".live.bv"}).join(" ");switch(this.options.live){case"submitted":break;case"disabled":i.off(c);break;case"enabled":default:i.off(c).on(c,function(){e._exceedThreshold(n(this))&&e.validateField(n(this))})}i.trigger(n.Event(this.options.events.fieldInit),{bv:this,field:t,element:i})}},_getMessage:function(t,i){if(!this.options.fields[t]||!n.fn.bootstrapValidator.validators[i]||!this.options.fields[t].validators||!this.options.fields[t].validators[i])return"";var r=this.options.fields[t].validators[i];switch(!0){case!!r.message:return r.message;case!!this.options.fields[t].message:return this.options.fields[t].message;case!!n.fn.bootstrapValidator.i18n[i]:return n.fn.bootstrapValidator.i18n[i]["default"];default:return this.options.message}},_getMessageContainer:function(n,t){var r=n.parent(),i,f,u;if(r.is(t))return r;if(i=r.attr("class"),!i)return this._getMessageContainer(r,t);for(i=i.split(" "),f=i.length,u=0;u<f;u++)if(/^col-(xs|sm|md|lg)-\d+$/.test(i[u])||/^col-(xs|sm|md|lg)-offset-\d+$/.test(i[u]))return r;return this._getMessageContainer(r,t)},_submit:function(){var i=this.isValid(),r=i?this.options.events.formSuccess:this.options.events.formError,t=n.Event(r);this.$form.trigger(t);this.$submitButton&&(i?this._onSuccess(t):this._onError(t))},_isExcluded:function(t){var u=t.attr("data-bv-excluded"),i=t.attr("data-bv-field")||t.attr("name"),f,r;switch(!0){case!!i&&this.options.fields&&this.options.fields[i]&&(this.options.fields[i].excluded==="true"||this.options.fields[i].excluded===!0):case u==="true":case u==="":return!0;case!!i&&this.options.fields&&this.options.fields[i]&&(this.options.fields[i].excluded==="false"||this.options.fields[i].excluded===!1):case u==="false":return!1;default:if(this.options.excluded)for("string"==typeof this.options.excluded&&(this.options.excluded=n.map(this.options.excluded.split(","),function(t){return n.trim(t)})),f=this.options.excluded.length,r=0;r<f;r++)if("string"==typeof this.options.excluded[r]&&t.is(this.options.excluded[r])||"function"==typeof this.options.excluded[r]&&this.options.excluded[r].call(this,t,this)===!0)return!0;return!1}},_exceedThreshold:function(t){var u=t.attr("data-bv-field"),i=this.options.fields[u].threshold||this.options.threshold,r;return i?(r=n.inArray(t.attr("type"),["button","checkbox","file","hidden","image","radio","reset","submit"])!==-1,r||t.val().length>=i):!0},_onError:function(t){var i,f,r,u,o,e,s;if(!t.isDefaultPrevented()){if("submitted"===this.options.live){this.options.live="enabled";i=this;for(f in this.options.fields)(function(t){var r=i.getFieldElements(t);if(r.length){var u=n(r[0]).attr("type"),o="radio"===u||"checkbox"===u||"file"===u||"SELECT"===n(r[0]).get(0).tagName?"change":i._changeEvent,s=i.options.fields[f].trigger||i.options.trigger||o,e=n.map(s.split(" "),function(n){return n+".live.bv"}).join(" ");r.off(e).on(e,function(){i._exceedThreshold(n(this))&&i.validateField(n(this))})}})(f)}for(r=0;r<this.$invalidFields.length;r++)if(u=this.$invalidFields.eq(r),o=this._isOptionEnabled(u.attr("data-bv-field"),"autoFocus"),o){e=u.parents(".tab-pane");e&&(s=e.attr("id"))&&n('a[href="#'+s+'"][data-toggle="tab"]').tab("show");u.focus();break}}},_onSuccess:function(n){n.isDefaultPrevented()||this.disableSubmitButtons(!0).defaultSubmit()},_onFieldValidated:function(t,i){var f=t.attr("data-bv-field"),s=this.options.fields[f].validators,r={},h=0,u={bv:this,field:f,element:t,validator:i,result:t.data("bv.response."+i)},e,o;if(i)switch(t.data("bv.result."+i)){case this.STATUS_INVALID:t.trigger(n.Event(this.options.events.validatorError),u);break;case this.STATUS_VALID:t.trigger(n.Event(this.options.events.validatorSuccess),u)}r[this.STATUS_NOT_VALIDATED]=0;r[this.STATUS_VALIDATING]=0;r[this.STATUS_INVALID]=0;r[this.STATUS_VALID]=0;for(e in s)s[e].enabled!==!1&&(h++,o=t.data("bv.result."+e),o&&r[o]++);r[this.STATUS_VALID]===h?(this.$invalidFields=this.$invalidFields.not(t),t.trigger(n.Event(this.options.events.fieldSuccess),u)):(r[this.STATUS_NOT_VALIDATED]===0||!this._isOptionEnabled(f,"verbose"))&&r[this.STATUS_VALIDATING]===0&&r[this.STATUS_INVALID]>0&&(this.$invalidFields=this.$invalidFields.add(t),t.trigger(n.Event(this.options.events.fieldError),u))},_isOptionEnabled:function(n,t){return this.options.fields[n]&&(this.options.fields[n][t]==="true"||this.options.fields[n][t]===!0)?!0:this.options.fields[n]&&(this.options.fields[n][t]==="false"||this.options.fields[n][t]===!1)?!1:this.options[t]==="true"||this.options[t]===!0},getFieldElements:function(t){return this._cacheFields[t]||(this._cacheFields[t]=this.options.fields[t]&&this.options.fields[t].selector?n(this.options.fields[t].selector):this.$form.find('[name="'+t+'"]')),this._cacheFields[t]},getOptions:function(n,t,i){if(!n)return i?this.options[i]:this.options;if("object"==typeof n&&(n=n.attr("data-bv-field")),!this.options.fields[n])return null;var r=this.options.fields[n];return t?!r.validators||!r.validators[t]?null:i?r.validators[t][i]:r.validators[t]:i?r[i]:r},disableSubmitButtons:function(n){return n?this.options.live!=="disabled"&&this.$form.find(this.options.submitButtons).attr("disabled","disabled"):this.$form.find(this.options.submitButtons).removeAttr("disabled"),this},validate:function(){if(!this.options.fields)return this;this.disableSubmitButtons(!0);this._submitIfValid=!1;for(var n in this.options.fields)this.validateField(n);return this._submit(),this._submitIfValid=!0,this},validateField:function(t){var f=n([]),h,u,a,v;switch(typeof t){case"object":f=t;t=t.attr("data-bv-field");break;case"string":f=this.getFieldElements(t)}if(f.length===0||!this.options.fields[t]||this.options.fields[t].enabled===!1)return this;var e=this,s=f.attr("type"),y="radio"===s||"checkbox"===s?1:f.length,o="radio"===s||"checkbox"===s,c=this.options.fields[t].validators,l=this._isOptionEnabled(t,"verbose"),i,r;for(h=0;h<y;h++)if(u=f.eq(h),!this._isExcluded(u)){a=!1;for(i in c){if(u.data("bv.dfs."+i)&&u.data("bv.dfs."+i).reject(),a)break;if(v=u.data("bv.result."+i),v===this.STATUS_VALID||v===this.STATUS_INVALID){this._onFieldValidated(u,i);continue}else if(c[i].enabled===!1){this.updateStatus(o?t:u,this.STATUS_VALID,i);continue}if(u.data("bv.result."+i,this.STATUS_VALIDATING),r=n.fn.bootstrapValidator.validators[i].validate(this,u,c[i]),"object"==typeof r&&r.resolve)this.updateStatus(o?t:u,this.STATUS_VALIDATING,i),u.data("bv.dfs."+i,r),r.done(function(n,t,i){n.removeData("bv.dfs."+t).data("bv.response."+t,i);i.message&&e.updateMessage(n,t,i.message);e.updateStatus(o?n.attr("data-bv-field"):n,i.valid?e.STATUS_VALID:e.STATUS_INVALID,t);i.valid&&e._submitIfValid===!0?e._submit():i.valid||l||(a=!0)});else if("object"==typeof r&&r.valid!==undefined&&r.message!==undefined){if(u.data("bv.response."+i,r),this.updateMessage(o?t:u,i,r.message),this.updateStatus(o?t:u,r.valid?this.STATUS_VALID:this.STATUS_INVALID,i),!r.valid&&!l)break}else if("boolean"==typeof r&&(u.data("bv.response."+i,r),this.updateStatus(o?t:u,r?this.STATUS_VALID:this.STATUS_INVALID,i),!r&&!l))break}}return this},updateMessage:function(t,i,r){var u=n([]);switch(typeof t){case"object":u=t;t=t.attr("data-bv-field");break;case"string":u=this.getFieldElements(t)}u.each(function(){n(this).data("bv.messages").find('.help-block[data-bv-validator="'+i+'"][data-bv-for="'+t+'"]').html(r)})},updateStatus:function(t,i,r){var h=n([]),l,f,b,a,k,o;switch(typeof t){case"object":h=t;t=t.attr("data-bv-field");break;case"string":h=this.getFieldElements(t)}i===this.STATUS_NOT_VALIDATED&&(this._submitIfValid=!1);var y=this,p=h.attr("type"),d=this.options.fields[t].group||this.options.group,g="radio"===p||"checkbox"===p?1:h.length;for(l=0;l<g;l++)if(f=h.eq(l),!this._isExcluded(f)){var c=f.parents(d),nt=f.data("bv.messages"),s=nt.find('.help-block[data-bv-validator][data-bv-for="'+t+'"]'),v=r?s.filter('[data-bv-validator="'+r+'"]'):s,u=f.data("bv.icon"),w="function"==typeof(this.options.fields[t].container||this.options.container)?(this.options.fields[t].container||this.options.container).call(this,f,this):this.options.fields[t].container||this.options.container,e=null;if(r)f.data("bv.result."+r,i);else for(b in this.options.fields[t].validators)f.data("bv.result."+b,i);v.attr("data-bv-result",i);a=f.parents(".tab-pane");a&&(k=a.attr("id"))&&(o=n('a[href="#'+k+'"][data-toggle="tab"]').parent());switch(i){case this.STATUS_VALIDATING:e=null;this.disableSubmitButtons(!0);c.removeClass("has-success").removeClass("has-error");u&&u.removeClass(this.options.feedbackIcons.valid).removeClass(this.options.feedbackIcons.invalid).addClass(this.options.feedbackIcons.validating).show();o&&o.removeClass("bv-tab-success").removeClass("bv-tab-error");break;case this.STATUS_INVALID:e=!1;this.disableSubmitButtons(!0);c.removeClass("has-success").addClass("has-error");u&&u.removeClass(this.options.feedbackIcons.valid).removeClass(this.options.feedbackIcons.validating).addClass(this.options.feedbackIcons.invalid).show();o&&o.removeClass("bv-tab-success").addClass("bv-tab-error");break;case this.STATUS_VALID:e=s.filter('[data-bv-result="'+this.STATUS_NOT_VALIDATED+'"]').length===0?s.filter('[data-bv-result="'+this.STATUS_VALID+'"]').length===s.length:null;e!==null&&(this.disableSubmitButtons(this.$submitButton?!this.isValid():!e),u&&u.removeClass(this.options.feedbackIcons.invalid).removeClass(this.options.feedbackIcons.validating).removeClass(this.options.feedbackIcons.valid).addClass(e?this.options.feedbackIcons.valid:this.options.feedbackIcons.invalid).show());c.removeClass("has-error has-success").addClass(this.isValidContainer(c)?"has-success":"has-error");o&&o.removeClass("bv-tab-success").removeClass("bv-tab-error").addClass(this.isValidContainer(a)?"bv-tab-success":"bv-tab-error");break;case this.STATUS_NOT_VALIDATED:default:e=null;this.disableSubmitButtons(!1);c.removeClass("has-success").removeClass("has-error");u&&u.removeClass(this.options.feedbackIcons.valid).removeClass(this.options.feedbackIcons.invalid).removeClass(this.options.feedbackIcons.validating).hide();o&&o.removeClass("bv-tab-success").removeClass("bv-tab-error")}switch(!0){case u&&"tooltip"===w:e===!1?u.css("cursor","pointer").tooltip("destroy").tooltip({container:"body",html:!0,placement:"auto top",title:s.filter('[data-bv-result="'+y.STATUS_INVALID+'"]').eq(0).html()}):u.css("cursor","").tooltip("destroy");break;case u&&"popover"===w:e===!1?u.css("cursor","pointer").popover("destroy").popover({container:"body",content:s.filter('[data-bv-result="'+y.STATUS_INVALID+'"]').eq(0).html(),html:!0,placement:"auto top",trigger:"hover click"}):u.css("cursor","").popover("destroy");break;default:i===this.STATUS_INVALID?v.show():v.hide()}f.trigger(n.Event(this.options.events.fieldStatus),{bv:this,field:t,element:f,status:i});this._onFieldValidated(f,r)}return this},isValid:function(){for(var n in this.options.fields)if(!this.isValidField(n))return!1;return!0},isValidField:function(t){var i=n([]),u,o,f,e,s,r;switch(typeof t){case"object":i=t;t=t.attr("data-bv-field");break;case"string":i=this.getFieldElements(t)}if(i.length===0||!this.options.fields[t]||this.options.fields[t].enabled===!1)return!0;for(u=i.attr("type"),o="radio"===u||"checkbox"===u?1:i.length,r=0;r<o;r++)if(f=i.eq(r),!this._isExcluded(f))for(e in this.options.fields[t].validators)if(this.options.fields[t].validators[e].enabled!==!1&&(s=f.data("bv.result."+e),s!==this.STATUS_VALID))return!1;return!0},isValidContainer:function(t){var e=this,i={},u="string"==typeof t?n(t):t,r,f;if(u.length===0)return!0;u.find("[data-bv-field]").each(function(){var t=n(this),r=t.attr("data-bv-field");e._isExcluded(t)||i[r]||(i[r]=t)});for(r in i)if(f=i[r],f.data("bv.messages").find('.help-block[data-bv-validator][data-bv-for="'+r+'"]').filter('[data-bv-result="'+this.STATUS_INVALID+'"]').length>0)return!1;return!0},defaultSubmit:function(){this.$submitButton&&n("<input/>").attr("type","hidden").attr("data-bv-submit-hidden","").attr("name",this.$submitButton.attr("name")).val(this.$submitButton.val()).appendTo(this.$form);this.$form.off("submit.bv").submit()},getInvalidFields:function(){return this.$invalidFields},getSubmitButton:function(){return this.$submitButton},getMessages:function(t,i){var o=this,f=[],u=n([]),r,e,s;switch(!0){case t&&"object"==typeof t:u=t;break;case t&&"string"==typeof t:r=this.getFieldElements(t);r.length>0&&(e=r.attr("type"),u="radio"===e||"checkbox"===e?r.eq(0):r);break;default:u=this.$invalidFields}return s=i?'[data-bv-validator="'+i+'"]':"",u.each(function(){f=f.concat(n(this).data("bv.messages").find('.help-block[data-bv-for="'+n(this).attr("data-bv-field")+'"][data-bv-result="'+o.STATUS_INVALID+'"]'+s).map(function(){var t=n(this).attr("data-bv-validator"),i=n(this).attr("data-bv-for");return o.options.fields[i].validators[t].enabled===!1?"":n(this).html()}).get())}),f},updateOption:function(n,t,i,r){return"object"==typeof n&&(n=n.attr("data-bv-field")),this.options.fields[n]&&this.options.fields[n].validators[t]&&(this.options.fields[n].validators[t][i]=r,this.updateStatus(n,this.STATUS_NOT_VALIDATED,t)),this},addField:function(t,i){var r=n([]),u,s,o,f,e;switch(typeof t){case"object":r=t;t=t.attr("data-bv-field")||t.attr("name");break;case"string":delete this._cacheFields[t];r=this.getFieldElements(t)}for(r.attr("data-bv-field",t),u=r.attr("type"),s="radio"===u||"checkbox"===u?1:r.length,o=0;o<s;o++)f=r.eq(o),e=this._parseOptions(f),e=e===null?i:n.extend(!0,i,e),this.options.fields[t]=n.extend(!0,this.options.fields[t],e),this._cacheFields[t]=this._cacheFields[t]?this._cacheFields[t].add(f):f,this._initField("checkbox"===u||"radio"===u?t:f);return this.disableSubmitButtons(!1),this.$form.trigger(n.Event(this.options.events.fieldAdded),{field:t,element:r,options:this.options.fields[t]}),this},removeField:function(t){var i=n([]),r,e,u,f;switch(typeof t){case"object":i=t;t=t.attr("data-bv-field")||t.attr("name");i.attr("data-bv-field",t);break;case"string":i=this.getFieldElements(t)}if(i.length===0)return this;for(r=i.attr("type"),e="radio"===r||"checkbox"===r?1:i.length,u=0;u<e;u++)f=i.eq(u),this.$invalidFields=this.$invalidFields.not(f),this._cacheFields[t]=this._cacheFields[t].not(f);return this._cacheFields[t]&&this._cacheFields[t].length!==0||delete this.options.fields[t],("checkbox"===r||"radio"===r)&&this._initField(t),this.disableSubmitButtons(!1),this.$form.trigger(n.Event(this.options.events.fieldRemoved),{field:t,element:i}),this},resetField:function(t,i){var r=n([]),e,u,o,f;switch(typeof t){case"object":r=t;t=t.attr("data-bv-field");break;case"string":r=this.getFieldElements(t)}if(e=r.length,this.options.fields[t])for(u=0;u<e;u++)for(o in this.options.fields[t].validators)r.eq(u).removeData("bv.dfs."+o);return this.updateStatus(t,this.STATUS_NOT_VALIDATED),i&&(f=r.attr("type"),"radio"===f||"checkbox"===f?r.removeAttr("checked").removeAttr("selected"):r.val("")),this},resetForm:function(t){for(var i in this.options.fields)this.resetField(i,t);return this.$invalidFields=n([]),this.$submitButton=null,this.disableSubmitButtons(!1),this},revalidateField:function(n){return this.updateStatus(n,this.STATUS_NOT_VALIDATED).validateField(n),this},enableFieldValidators:function(n,t,i){var r=this.options.fields[n].validators,u;if(i&&r&&r[i]&&r[i].enabled!==t)this.options.fields[n].validators[i].enabled=t,this.updateStatus(n,this.STATUS_NOT_VALIDATED,i);else if(!i&&this.options.fields[n].enabled!==t){this.options.fields[n].enabled=t;for(u in r)this.enableFieldValidators(n,t,u)}return this},getDynamicOption:function(t,i){var r="string"==typeof t?this.getFieldElements(t):t,f=r.val(),u;return"function"==typeof i?n.fn.bootstrapValidator.helpers.call(i,[f,this,r]):"string"==typeof i?(u=this.getFieldElements(i),u.length?u.val():n.fn.bootstrapValidator.helpers.call(i,[f,this,r])||i):null},destroy:function(){var t,e,i,r,u,o,f,s;for(t in this.options.fields)for(e=this.getFieldElements(t),o=this.options.fields[t].group||this.options.group,f=0;f<e.length;f++){if(i=e.eq(f),i.data("bv.messages").find('.help-block[data-bv-validator][data-bv-for="'+t+'"]').remove().end().end().removeData("bv.messages").parents(o).removeClass("has-feedback has-error has-success").end().off(".bv").removeAttr("data-bv-field"),u=i.data("bv.icon"),u){s="function"==typeof(this.options.fields[t].container||this.options.container)?(this.options.fields[t].container||this.options.container).call(this,i,this):this.options.fields[t].container||this.options.container;switch(s){case"tooltip":u.tooltip("destroy").remove();break;case"popover":u.popover("destroy").remove();break;default:u.remove()}}i.removeData("bv.icon");for(r in this.options.fields[t].validators)i.data("bv.dfs."+r)&&i.data("bv.dfs."+r).reject(),i.removeData("bv.result."+r).removeData("bv.response."+r).removeData("bv.dfs."+r),"function"==typeof n.fn.bootstrapValidator.validators[r].destroy&&n.fn.bootstrapValidator.validators[r].destroy(this,i,this.options.fields[t].validators[r])}this.disableSubmitButtons(!1);this.$hiddenButton.remove();this.$form.removeClass(this.options.elementClass).off(".bv").removeData("bootstrapValidator").find("[data-bv-submit-hidden]").remove().end().find('[type="submit"]').off("click.bv")}};n.fn.bootstrapValidator=function(i){var r=arguments;return this.each(function(){var f=n(this),u=f.data("bootstrapValidator"),e="object"==typeof i&&i;u||(u=new t(this,e),f.data("bootstrapValidator",u));"string"==typeof i&&u[i].apply(u,Array.prototype.slice.call(r,1))})};n.fn.bootstrapValidator.DEFAULT_OPTIONS={autoFocus:!0,container:null,elementClass:"bv-form",events:{formInit:"init.form.bv",formError:"error.form.bv",formSuccess:"success.form.bv",fieldAdded:"added.field.bv",fieldRemoved:"removed.field.bv",fieldInit:"init.field.bv",fieldError:"error.field.bv",fieldSuccess:"success.field.bv",fieldStatus:"status.field.bv",validatorError:"error.validator.bv",validatorSuccess:"success.validator.bv"},excluded:[":disabled",":hidden",":not(:visible)"],feedbackIcons:{valid:null,invalid:null,validating:null},fields:null,group:".form-group",live:"enabled",message:"This value is not valid",submitButtons:'[type="submit"]',threshold:null,verbose:!0};n.fn.bootstrapValidator.validators={};n.fn.bootstrapValidator.i18n={};n.fn.bootstrapValidator.Constructor=t;n.fn.bootstrapValidator.helpers={call:function(n,t){var r;if("function"==typeof n)return n.apply(this,t);if("string"==typeof n){"()"===n.substring(n.length-2)&&(n=n.substring(0,n.length-2));var u=n.split("."),f=u.pop(),i=window;for(r=0;r<u.length;r++)i=i[u[r]];return typeof i[f]=="undefined"?null:i[f].apply(this,t)}},format:function(t,i){n.isArray(i)||(i=[i]);for(var r in i)t=t.replace("%s",i[r]);return t},date:function(n,t,i,r){var u;if(isNaN(n)||isNaN(t)||isNaN(i)||i.length>2||t.length>2||n.length>4||(i=parseInt(i,10),t=parseInt(t,10),n=parseInt(n,10),n<1e3||n>9999||t<=0||t>12)||(u=[31,28,31,30,31,30,31,31,30,31,30,31],(n%400==0||n%100!=0&&n%4==0)&&(u[1]=29),i<=0||i>u[t-1]))return!1;if(r===!0){var f=new Date,e=f.getFullYear(),o=f.getMonth(),s=f.getDate();return n<e||n===e&&t-1<o||n===e&&t-1===o&&i<s}return!0},luhn:function(n){for(var i=n.length,r=0,u=[[0,1,2,3,4,5,6,7,8,9],[0,2,4,6,8,1,3,5,7,9]],t=0;i--;)t+=u[r][parseInt(n.charAt(i),10)],r^=1;return t%10==0&&t>0},mod11And10:function(n){for(var t=5,r=n.length,i=0;i<r;i++)t=((t||10)*2%11+parseInt(n.charAt(i),10))%10;return t===1},mod37And36:function(n,t){var r;t=t||"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";var i=t.length,f=n.length,u=Math.floor(i/2);for(r=0;r<f;r++)u=((u||i)*2%(i+1)+t.indexOf(n.charAt(r)))%i;return u===1}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.base64=n.extend(n.fn.bootstrapValidator.i18n.base64||{},{"default":"Please enter a valid base 64 encoded"});n.fn.bootstrapValidator.validators.base64={validate:function(n,t){var i=t.val();return i===""?!0:/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{4})$/.test(i)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.between=n.extend(n.fn.bootstrapValidator.i18n.between||{},{"default":"Please enter a value between %s and %s",notInclusive:"Please enter a value between %s and %s strictly"});n.fn.bootstrapValidator.validators.between={html5Attributes:{message:"message",min:"min",max:"max",inclusive:"inclusive"},enableByHtml5:function(n){return"range"===n.attr("type")?{min:n.attr("min"),max:n.attr("max")}:!1},validate:function(t,i,r){var u=i.val();if(u==="")return!0;if(u=this._format(u),!n.isNumeric(u))return!1;var f=n.isNumeric(r.min)?r.min:t.getDynamicOption(i,r.min),e=n.isNumeric(r.max)?r.max:t.getDynamicOption(i,r.max),o=this._format(f),s=this._format(e);return u=parseFloat(u),r.inclusive===!0||r.inclusive===undefined?{valid:u>=o&&u<=s,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.between["default"],[f,e])}:{valid:u>o&&u<s,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.between.notInclusive,[f,e])}},_format:function(n){return(n+"").replace(",",".")}}}(window.jQuery),function(n){n.fn.bootstrapValidator.validators.blank={validate:function(){return!0}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.callback=n.extend(n.fn.bootstrapValidator.i18n.callback||{},{"default":"Please enter a valid value"});n.fn.bootstrapValidator.validators.callback={html5Attributes:{message:"message",callback:"callback"},validate:function(t,i,r){var o=i.val(),f=new n.Deferred,e={valid:!0},u;return r.callback&&(u=n.fn.bootstrapValidator.helpers.call(r.callback,[o,t,i]),e="boolean"==typeof u?{valid:u}:u),f.resolve(i,"callback",e),f}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.choice=n.extend(n.fn.bootstrapValidator.i18n.choice||{},{"default":"Please enter a valid value",less:"Please choose %s options at minimum",more:"Please choose %s options at maximum",between:"Please choose %s - %s options"});n.fn.bootstrapValidator.validators.choice={html5Attributes:{message:"message",min:"min",max:"max"},validate:function(t,i,r){var o=i.is("select")?t.getFieldElements(i.attr("data-bv-field")).find("option").filter(":selected").length:t.getFieldElements(i.attr("data-bv-field")).filter(":checked").length,u=r.min?n.isNumeric(r.min)?r.min:t.getDynamicOption(i,r.min):null,f=r.max?n.isNumeric(r.max)?r.max:t.getDynamicOption(i,r.max):null,s=!0,e=r.message||n.fn.bootstrapValidator.i18n.choice["default"];(u&&o<parseInt(u,10)||f&&o>parseInt(f,10))&&(s=!1);switch(!0){case!!u&&!!f:e=n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.choice.between,[parseInt(u,10),parseInt(f,10)]);break;case!!u:e=n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.choice.less,parseInt(u,10));break;case!!f:e=n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.choice.more,parseInt(f,10))}return{valid:s,message:e}}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.color=n.extend(n.fn.bootstrapValidator.i18n.color||{},{"default":"Please enter a valid color"});n.fn.bootstrapValidator.validators.color={SUPPORTED_TYPES:["hex","rgb","rgba","hsl","hsla","keyword"],KEYWORD_COLORS:["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","transparent","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],validate:function(t,i,r){var o=i.val(),u,s,h,f,e;if(o==="")return!0;for(u=r.type||this.SUPPORTED_TYPES,n.isArray(u)||(u=u.replace(/s/g,"").split(",")),f=!1,e=0;e<u.length;e++)if(h=u[e],s="_"+h.toLowerCase(),f=f||this[s](o),f)return!0;return!1},_hex:function(n){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(n)},_hsl:function(n){return/^hsl\((\s*(-?\d+)\s*,)(\s*(\b(0?\d{1,2}|100)\b%)\s*,)(\s*(\b(0?\d{1,2}|100)\b%)\s*)\)$/.test(n)},_hsla:function(n){return/^hsla\((\s*(-?\d+)\s*,)(\s*(\b(0?\d{1,2}|100)\b%)\s*,){2}(\s*(0?(\.\d+)?|1(\.0+)?)\s*)\)$/.test(n)},_keyword:function(t){return n.inArray(t,this.KEYWORD_COLORS)>=0},_rgb:function(n){return/^rgb\((\s*(\b([01]?\d{1,2}|2[0-4]\d|25[0-5])\b)\s*,){2}(\s*(\b([01]?\d{1,2}|2[0-4]\d|25[0-5])\b)\s*)\)$/.test(n)||/^rgb\((\s*(\b(0?\d{1,2}|100)\b%)\s*,){2}(\s*(\b(0?\d{1,2}|100)\b%)\s*)\)$/.test(n)},_rgba:function(n){return/^rgba\((\s*(\b([01]?\d{1,2}|2[0-4]\d|25[0-5])\b)\s*,){3}(\s*(0?(\.\d+)?|1(\.0+)?)\s*)\)$/.test(n)||/^rgba\((\s*(\b(0?\d{1,2}|100)\b%)\s*,){3}(\s*(0?(\.\d+)?|1(\.0+)?)\s*)\)$/.test(n)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.creditCard=n.extend(n.fn.bootstrapValidator.i18n.creditCard||{},{"default":"Please enter a valid credit card number"});n.fn.bootstrapValidator.validators.creditCard={validate:function(t,i){var r=i.val(),u,f,e;if(r==="")return!0;if(/[^0-9-\s]+/.test(r)||(r=r.replace(/\D/g,""),!n.fn.bootstrapValidator.helpers.luhn(r)))return!1;u={AMERICAN_EXPRESS:{length:[15],prefix:["34","37"]},DINERS_CLUB:{length:[14],prefix:["300","301","302","303","304","305","36"]},DINERS_CLUB_US:{length:[16],prefix:["54","55"]},DISCOVER:{length:[16],prefix:["6011","622126","622127","622128","622129","62213","62214","62215","62216","62217","62218","62219","6222","6223","6224","6225","6226","6227","6228","62290","62291","622920","622921","622922","622923","622924","622925","644","645","646","647","648","649","65"]},JCB:{length:[16],prefix:["3528","3529","353","354","355","356","357","358"]},LASER:{length:[16,17,18,19],prefix:["6304","6706","6771","6709"]},MAESTRO:{length:[12,13,14,15,16,17,18,19],prefix:["5018","5020","5038","6304","6759","6761","6762","6763","6764","6765","6766"]},MASTERCARD:{length:[16],prefix:["51","52","53","54","55"]},SOLO:{length:[16,18,19],prefix:["6334","6767"]},UNIONPAY:{length:[16,17,18,19],prefix:["622126","622127","622128","622129","62213","62214","62215","62216","62217","62218","62219","6222","6223","6224","6225","6226","6227","6228","62290","62291","622920","622921","622922","622923","622924","622925"]},VISA:{length:[16],prefix:["4"]}};for(f in u)for(e in u[f].prefix)if(r.substr(0,u[f].prefix[e].length)===u[f].prefix[e]&&n.inArray(r.length,u[f].length)!==-1)return!0;return!1}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.cusip=n.extend(n.fn.bootstrapValidator.i18n.cusip||{},{"default":"Please enter a valid CUSIP number"});n.fn.bootstrapValidator.validators.cusip={validate:function(t,i){var r=i.val(),u,f;if(r==="")return!0;if(r=r.toUpperCase(),!/^[0-9A-Z]{9}$/.test(r))return!1;var o=n.map(r.split(""),function(n){var t=n.charCodeAt(0);return t>="A".charCodeAt(0)&&t<="Z".charCodeAt(0)?t-"A".charCodeAt(0)+10:n}),s=o.length,e=0;for(u=0;u<s-1;u++)f=parseInt(o[u],10),u%2!=0&&(f*=2),f>9&&(f-=9),e+=f;return e=(10-e%10)%10,e===o[s-1]}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.cvv=n.extend(n.fn.bootstrapValidator.i18n.cvv||{},{"default":"Please enter a valid CVV number"});n.fn.bootstrapValidator.validators.cvv={html5Attributes:{message:"message",ccfield:"creditCardField"},validate:function(t,i,r){var o=i.val(),u,f,e,h,s;if(o==="")return!0;if(!/^[0-9]{3,4}$/.test(o))return!1;if(!r.creditCardField||(u=t.getFieldElements(r.creditCardField).val(),u===""))return!0;u=u.replace(/\D/g,"");f={AMERICAN_EXPRESS:{length:[15],prefix:["34","37"]},DINERS_CLUB:{length:[14],prefix:["300","301","302","303","304","305","36"]},DINERS_CLUB_US:{length:[16],prefix:["54","55"]},DISCOVER:{length:[16],prefix:["6011","622126","622127","622128","622129","62213","62214","62215","62216","62217","62218","62219","6222","6223","6224","6225","6226","6227","6228","62290","62291","622920","622921","622922","622923","622924","622925","644","645","646","647","648","649","65"]},JCB:{length:[16],prefix:["3528","3529","353","354","355","356","357","358"]},LASER:{length:[16,17,18,19],prefix:["6304","6706","6771","6709"]},MAESTRO:{length:[12,13,14,15,16,17,18,19],prefix:["5018","5020","5038","6304","6759","6761","6762","6763","6764","6765","6766"]},MASTERCARD:{length:[16],prefix:["51","52","53","54","55"]},SOLO:{length:[16,18,19],prefix:["6334","6767"]},UNIONPAY:{length:[16,17,18,19],prefix:["622126","622127","622128","622129","62213","62214","62215","62216","62217","62218","62219","6222","6223","6224","6225","6226","6227","6228","62290","62291","622920","622921","622922","622923","622924","622925"]},VISA:{length:[16],prefix:["4"]}};s=null;for(e in f)for(h in f[e].prefix)if(u.substr(0,f[e].prefix[h].length)===f[e].prefix[h]&&n.inArray(u.length,f[e].length)!==-1){s=e;break}return s===null?!1:"AMERICAN_EXPRESS"===s?o.length===4:o.length===3}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.date=n.extend(n.fn.bootstrapValidator.i18n.date||{},{"default":"Please enter a valid date",min:"Please enter a date after %s",max:"Please enter a date before %s",range:"Please enter a date in the range %s - %s"});n.fn.bootstrapValidator.validators.date={html5Attributes:{message:"message",format:"format",min:"min",max:"max",separator:"separator"},validate:function(t,i,r){var it=i.val(),h;if(it==="")return!0;r.format=r.format||"MM/DD/YYYY";i.attr("type")==="date"&&(r.format="YYYY-MM-DD");var y=r.format.split(" "),a=y[0],p=y.length>1?y[1]:null,rt=y.length>2?y[2]:null,w=it.split(" "),u=w[0],s=w.length>1?w[1]:null;if(y.length!==w.length)return{valid:!1,message:r.message||n.fn.bootstrapValidator.i18n.date["default"]};if(h=r.separator,h||(h=u.indexOf("/")!==-1?"/":u.indexOf("-")!==-1?"-":null),h===null||u.indexOf(h)===-1)return{valid:!1,message:r.message||n.fn.bootstrapValidator.i18n.date["default"]};if(u=u.split(h),a=a.split(h),u.length!==a.length)return{valid:!1,message:r.message||n.fn.bootstrapValidator.i18n.date["default"]};var b=u[n.inArray("YYYY",a)],d=u[n.inArray("MM",a)],g=u[n.inArray("DD",a)];if(!b||!d||!g||b.length!==4)return{valid:!1,message:r.message||n.fn.bootstrapValidator.i18n.date["default"]};var c=null,f=null,l=null;if(p){if(p=p.split(":"),s=s.split(":"),p.length!==s.length)return{valid:!1,message:r.message||n.fn.bootstrapValidator.i18n.date["default"]};if(f=s.length>0?s[0]:null,c=s.length>1?s[1]:null,l=s.length>2?s[2]:null,l){if(isNaN(l)||l.length>2)return{valid:!1,message:r.message||n.fn.bootstrapValidator.i18n.date["default"]};if(l=parseInt(l,10),l<0||l>60)return{valid:!1,message:r.message||n.fn.bootstrapValidator.i18n.date["default"]}}if(f){if(isNaN(f)||f.length>2)return{valid:!1,message:r.message||n.fn.bootstrapValidator.i18n.date["default"]};if(f=parseInt(f,10),f<0||f>=24||rt&&f>12)return{valid:!1,message:r.message||n.fn.bootstrapValidator.i18n.date["default"]}}if(c){if(isNaN(c)||c.length>2)return{valid:!1,message:r.message||n.fn.bootstrapValidator.i18n.date["default"]};if(c=parseInt(c,10),c<0||c>59)return{valid:!1,message:r.message||n.fn.bootstrapValidator.i18n.date["default"]}}}var v=n.fn.bootstrapValidator.helpers.date(b,d,g),k=r.message||n.fn.bootstrapValidator.i18n.date["default"],nt=null,tt=null,e=r.min,o=r.max;e&&(isNaN(Date.parse(e))&&(e=t.getDynamicOption(i,e)),nt=this._parseDate(e,a,h));o&&(isNaN(Date.parse(o))&&(o=t.getDynamicOption(i,o)),tt=this._parseDate(o,a,h));u=new Date(b,d,g,f,c,l);switch(!0){case e&&!o&&v:v=u.getTime()>=nt.getTime();k=r.message||n.fn.bootstrapValidator.helpers.format(n.fn.bootstrapValidator.i18n.date.min,e);break;case o&&!e&&v:v=u.getTime()<=tt.getTime();k=r.message||n.fn.bootstrapValidator.helpers.format(n.fn.bootstrapValidator.i18n.date.max,o);break;case o&&e&&v:v=u.getTime()<=tt.getTime()&&u.getTime()>=nt.getTime();k=r.message||n.fn.bootstrapValidator.helpers.format(n.fn.bootstrapValidator.i18n.date.range,[e,o])}return{valid:v,message:k}},_parseDate:function(t,i,r){var o=0,s=0,h=0,e=t.split(" "),f=e[0],u=e.length>1?e[1]:null;f=f.split(r);var c=f[n.inArray("YYYY",i)],l=f[n.inArray("MM",i)],a=f[n.inArray("DD",i)];return u&&(u=u.split(":"),s=u.length>0?u[0]:null,o=u.length>1?u[1]:null,h=u.length>2?u[2]:null),new Date(c,l,a,s,o,h)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.different=n.extend(n.fn.bootstrapValidator.i18n.different||{},{"default":"Please enter a different value"});n.fn.bootstrapValidator.validators.different={html5Attributes:{message:"message",field:"field"},validate:function(n,t,i){var s=t.val(),f,e,u,r,o;if(s==="")return!0;for(f=i.field.split(","),e=!0,u=0;u<f.length;u++)(r=n.getFieldElements(f[u]),r!=null&&r.length!==0)&&(o=r.val(),s===o?e=!1:o!==""&&n.updateStatus(r,n.STATUS_VALID,"different"));return e}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.digits=n.extend(n.fn.bootstrapValidator.i18n.digits||{},{"default":"Please enter only digits"});n.fn.bootstrapValidator.validators.digits={validate:function(n,t){var i=t.val();return i===""?!0:/^\d+$/.test(i)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.ean=n.extend(n.fn.bootstrapValidator.i18n.ean||{},{"default":"Please enter a valid EAN number"});n.fn.bootstrapValidator.validators.ean={validate:function(n,t){var i=t.val(),r;if(i==="")return!0;if(!/^(\d{8}|\d{12}|\d{13})$/.test(i))return!1;var f=i.length,u=0,e=f===8?[3,1]:[1,3];for(r=0;r<f-1;r++)u+=parseInt(i.charAt(r),10)*e[r%2];return u=(10-u%10)%10,u+""===i.charAt(f-1)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.emailAddress=n.extend(n.fn.bootstrapValidator.i18n.emailAddress||{},{"default":"Please enter a valid email address"});n.fn.bootstrapValidator.validators.emailAddress={html5Attributes:{message:"message",multiple:"multiple",separator:"separator"},enableByHtml5:function(n){return"email"===n.attr("type")},validate:function(n,t,i){var u=t.val(),f,o,s,e,r;if(u==="")return!0;if(f=/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,o=i.multiple===!0||i.multiple==="true",o){for(s=i.separator||/[,;]/,e=this._splitEmailAddresses(u,s),r=0;r<e.length;r++)if(!f.test(e[r]))return!1;return!0}return f.test(u)},_splitEmailAddresses:function(n,t){for(var u,e,o,s=n.split(/"/),h=s.length,f=[],i="",r=0;r<h;r++)if(r%2==0)if(u=s[r].split(t),e=u.length,e===1)i+=u[0];else{for(f.push(i+u[0]),o=1;o<e-1;o++)f.push(u[o]);i=u[e-1]}else i+='"'+s[r],r<h-1&&(i+='"');return f.push(i),f}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.file=n.extend(n.fn.bootstrapValidator.i18n.file||{},{"default":"Please choose a valid file"});n.fn.bootstrapValidator.validators.file={html5Attributes:{extension:"extension",maxfiles:"maxFiles",minfiles:"minFiles",maxsize:"maxSize",minsize:"minSize",maxtotalsize:"maxTotalSize",mintotalsize:"minTotalSize",message:"message",type:"type"},validate:function(t,i,r){var s=i.val(),u;if(s==="")return!0;var e,o=r.extension?r.extension.toLowerCase().split(","):null,l=r.type?r.type.toLowerCase().split(","):null,a=window.File&&window.FileList&&window.FileReader;if(a){var f=i.get(0).files,h=f.length,c=0;if(r.maxFiles&&h>parseInt(r.maxFiles,10)||r.minFiles&&h<parseInt(r.minFiles,10))return!1;for(u=0;u<h;u++)if(c+=f[u].size,e=f[u].name.substr(f[u].name.lastIndexOf(".")+1),r.minSize&&f[u].size<parseInt(r.minSize,10)||r.maxSize&&f[u].size>parseInt(r.maxSize,10)||o&&n.inArray(e.toLowerCase(),o)===-1||f[u].type&&l&&n.inArray(f[u].type.toLowerCase(),l)===-1)return!1;if(r.maxTotalSize&&c>parseInt(r.maxTotalSize,10)||r.minTotalSize&&c<parseInt(r.minTotalSize,10))return!1}else if(e=s.substr(s.lastIndexOf(".")+1),o&&n.inArray(e.toLowerCase(),o)===-1)return!1;return!0}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.greaterThan=n.extend(n.fn.bootstrapValidator.i18n.greaterThan||{},{"default":"Please enter a value greater than or equal to %s",notInclusive:"Please enter a value greater than %s"});n.fn.bootstrapValidator.validators.greaterThan={html5Attributes:{message:"message",value:"value",inclusive:"inclusive"},enableByHtml5:function(n){var i=n.attr("type"),t=n.attr("min");return t&&i!=="date"?{value:t}:!1},validate:function(t,i,r){var u=i.val(),f,e;return u===""?!0:(u=this._format(u),!n.isNumeric(u))?!1:(f=n.isNumeric(r.value)?r.value:t.getDynamicOption(i,r.value),e=this._format(f),u=parseFloat(u),r.inclusive===!0||r.inclusive===undefined?{valid:u>=e,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.greaterThan["default"],f)}:{valid:u>e,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.greaterThan.notInclusive,f)})},_format:function(n){return(n+"").replace(",",".")}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.grid=n.extend(n.fn.bootstrapValidator.i18n.grid||{},{"default":"Please enter a valid GRId number"});n.fn.bootstrapValidator.validators.grid={validate:function(t,i){var r=i.val();return r===""?!0:(r=r.toUpperCase(),!/^[GRID:]*([0-9A-Z]{2})[-\s]*([0-9A-Z]{5})[-\s]*([0-9A-Z]{10})[-\s]*([0-9A-Z]{1})$/g.test(r))?!1:(r=r.replace(/\s/g,"").replace(/-/g,""),"GRID:"===r.substr(0,5)&&(r=r.substr(5)),n.fn.bootstrapValidator.helpers.mod37And36(r))}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.hex=n.extend(n.fn.bootstrapValidator.i18n.hex||{},{"default":"Please enter a valid hexadecimal number"});n.fn.bootstrapValidator.validators.hex={validate:function(n,t){var i=t.val();return i===""?!0:/^[0-9a-fA-F]+$/.test(i)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.hexColor=n.extend(n.fn.bootstrapValidator.i18n.hexColor||{},{"default":"Please enter a valid hex color"});n.fn.bootstrapValidator.validators.hexColor={enableByHtml5:function(n){return"color"===n.attr("type")},validate:function(n,t){var i=t.val();return i===""?!0:"color"===t.attr("type")?/^#[0-9A-F]{6}$/i.test(i):/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(i)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.iban=n.extend(n.fn.bootstrapValidator.i18n.iban||{},{"default":"Please enter a valid IBAN number",countryNotSupported:"The country code %s is not supported",country:"Please enter a valid IBAN number in %s",countries:{AD:"Andorra",AE:"United Arab Emirates",AL:"Albania",AO:"Angola",AT:"Austria",AZ:"Azerbaijan",BA:"Bosnia and Herzegovina",BE:"Belgium",BF:"Burkina Faso",BG:"Bulgaria",BH:"Bahrain",BI:"Burundi",BJ:"Benin",BR:"Brazil",CH:"Switzerland",CI:"Ivory Coast",CM:"Cameroon",CR:"Costa Rica",CV:"Cape Verde",CY:"Cyprus",CZ:"Czech Republic",DE:"Germany",DK:"Denmark",DO:"Dominican Republic",DZ:"Algeria",EE:"Estonia",ES:"Spain",FI:"Finland",FO:"Faroe Islands",FR:"France",GB:"United Kingdom",GE:"Georgia",GI:"Gibraltar",GL:"Greenland",GR:"Greece",GT:"Guatemala",HR:"Croatia",HU:"Hungary",IE:"Ireland",IL:"Israel",IR:"Iran",IS:"Iceland",IT:"Italy",JO:"Jordan",KW:"Kuwait",KZ:"Kazakhstan",LB:"Lebanon",LI:"Liechtenstein",LT:"Lithuania",LU:"Luxembourg",LV:"Latvia",MC:"Monaco",MD:"Moldova",ME:"Montenegro",MG:"Madagascar",MK:"Macedonia",ML:"Mali",MR:"Mauritania",MT:"Malta",MU:"Mauritius",MZ:"Mozambique",NL:"Netherlands",NO:"Norway",PK:"Pakistan",PL:"Poland",PS:"Palestine",PT:"Portugal",QA:"Qatar",RO:"Romania",RS:"Serbia",SA:"Saudi Arabia",SE:"Sweden",SI:"Slovenia",SK:"Slovakia",SM:"San Marino",SN:"Senegal",TN:"Tunisia",TR:"Turkey",VG:"Virgin Islands, British"}});n.fn.bootstrapValidator.validators.iban={html5Attributes:{message:"message",country:"country"},REGEX:{AD:"AD[0-9]{2}[0-9]{4}[0-9]{4}[A-Z0-9]{12}",AE:"AE[0-9]{2}[0-9]{3}[0-9]{16}",AL:"AL[0-9]{2}[0-9]{8}[A-Z0-9]{16}",AO:"AO[0-9]{2}[0-9]{21}",AT:"AT[0-9]{2}[0-9]{5}[0-9]{11}",AZ:"AZ[0-9]{2}[A-Z]{4}[A-Z0-9]{20}",BA:"BA[0-9]{2}[0-9]{3}[0-9]{3}[0-9]{8}[0-9]{2}",BE:"BE[0-9]{2}[0-9]{3}[0-9]{7}[0-9]{2}",BF:"BF[0-9]{2}[0-9]{23}",BG:"BG[0-9]{2}[A-Z]{4}[0-9]{4}[0-9]{2}[A-Z0-9]{8}",BH:"BH[0-9]{2}[A-Z]{4}[A-Z0-9]{14}",BI:"BI[0-9]{2}[0-9]{12}",BJ:"BJ[0-9]{2}[A-Z]{1}[0-9]{23}",BR:"BR[0-9]{2}[0-9]{8}[0-9]{5}[0-9]{10}[A-Z][A-Z0-9]",CH:"CH[0-9]{2}[0-9]{5}[A-Z0-9]{12}",CI:"CI[0-9]{2}[A-Z]{1}[0-9]{23}",CM:"CM[0-9]{2}[0-9]{23}",CR:"CR[0-9]{2}[0-9]{3}[0-9]{14}",CV:"CV[0-9]{2}[0-9]{21}",CY:"CY[0-9]{2}[0-9]{3}[0-9]{5}[A-Z0-9]{16}",CZ:"CZ[0-9]{2}[0-9]{20}",DE:"DE[0-9]{2}[0-9]{8}[0-9]{10}",DK:"DK[0-9]{2}[0-9]{14}",DO:"DO[0-9]{2}[A-Z0-9]{4}[0-9]{20}",DZ:"DZ[0-9]{2}[0-9]{20}",EE:"EE[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{11}[0-9]{1}",ES:"ES[0-9]{2}[0-9]{4}[0-9]{4}[0-9]{1}[0-9]{1}[0-9]{10}",FI:"FI[0-9]{2}[0-9]{6}[0-9]{7}[0-9]{1}",FO:"FO[0-9]{2}[0-9]{4}[0-9]{9}[0-9]{1}",FR:"FR[0-9]{2}[0-9]{5}[0-9]{5}[A-Z0-9]{11}[0-9]{2}",GB:"GB[0-9]{2}[A-Z]{4}[0-9]{6}[0-9]{8}",GE:"GE[0-9]{2}[A-Z]{2}[0-9]{16}",GI:"GI[0-9]{2}[A-Z]{4}[A-Z0-9]{15}",GL:"GL[0-9]{2}[0-9]{4}[0-9]{9}[0-9]{1}",GR:"GR[0-9]{2}[0-9]{3}[0-9]{4}[A-Z0-9]{16}",GT:"GT[0-9]{2}[A-Z0-9]{4}[A-Z0-9]{20}",HR:"HR[0-9]{2}[0-9]{7}[0-9]{10}",HU:"HU[0-9]{2}[0-9]{3}[0-9]{4}[0-9]{1}[0-9]{15}[0-9]{1}",IE:"IE[0-9]{2}[A-Z]{4}[0-9]{6}[0-9]{8}",IL:"IL[0-9]{2}[0-9]{3}[0-9]{3}[0-9]{13}",IR:"IR[0-9]{2}[0-9]{22}",IS:"IS[0-9]{2}[0-9]{4}[0-9]{2}[0-9]{6}[0-9]{10}",IT:"IT[0-9]{2}[A-Z]{1}[0-9]{5}[0-9]{5}[A-Z0-9]{12}",JO:"JO[0-9]{2}[A-Z]{4}[0-9]{4}[0]{8}[A-Z0-9]{10}",KW:"KW[0-9]{2}[A-Z]{4}[0-9]{22}",KZ:"KZ[0-9]{2}[0-9]{3}[A-Z0-9]{13}",LB:"LB[0-9]{2}[0-9]{4}[A-Z0-9]{20}",LI:"LI[0-9]{2}[0-9]{5}[A-Z0-9]{12}",LT:"LT[0-9]{2}[0-9]{5}[0-9]{11}",LU:"LU[0-9]{2}[0-9]{3}[A-Z0-9]{13}",LV:"LV[0-9]{2}[A-Z]{4}[A-Z0-9]{13}",MC:"MC[0-9]{2}[0-9]{5}[0-9]{5}[A-Z0-9]{11}[0-9]{2}",MD:"MD[0-9]{2}[A-Z0-9]{20}",ME:"ME[0-9]{2}[0-9]{3}[0-9]{13}[0-9]{2}",MG:"MG[0-9]{2}[0-9]{23}",MK:"MK[0-9]{2}[0-9]{3}[A-Z0-9]{10}[0-9]{2}",ML:"ML[0-9]{2}[A-Z]{1}[0-9]{23}",MR:"MR13[0-9]{5}[0-9]{5}[0-9]{11}[0-9]{2}",MT:"MT[0-9]{2}[A-Z]{4}[0-9]{5}[A-Z0-9]{18}",MU:"MU[0-9]{2}[A-Z]{4}[0-9]{2}[0-9]{2}[0-9]{12}[0-9]{3}[A-Z]{3}",MZ:"MZ[0-9]{2}[0-9]{21}",NL:"NL[0-9]{2}[A-Z]{4}[0-9]{10}",NO:"NO[0-9]{2}[0-9]{4}[0-9]{6}[0-9]{1}",PK:"PK[0-9]{2}[A-Z]{4}[A-Z0-9]{16}",PL:"PL[0-9]{2}[0-9]{8}[0-9]{16}",PS:"PS[0-9]{2}[A-Z]{4}[A-Z0-9]{21}",PT:"PT[0-9]{2}[0-9]{4}[0-9]{4}[0-9]{11}[0-9]{2}",QA:"QA[0-9]{2}[A-Z]{4}[A-Z0-9]{21}",RO:"RO[0-9]{2}[A-Z]{4}[A-Z0-9]{16}",RS:"RS[0-9]{2}[0-9]{3}[0-9]{13}[0-9]{2}",SA:"SA[0-9]{2}[0-9]{2}[A-Z0-9]{18}",SE:"SE[0-9]{2}[0-9]{3}[0-9]{16}[0-9]{1}",SI:"SI[0-9]{2}[0-9]{5}[0-9]{8}[0-9]{2}",SK:"SK[0-9]{2}[0-9]{4}[0-9]{6}[0-9]{10}",SM:"SM[0-9]{2}[A-Z]{1}[0-9]{5}[0-9]{5}[A-Z0-9]{12}",SN:"SN[0-9]{2}[A-Z]{1}[0-9]{23}",TN:"TN59[0-9]{2}[0-9]{3}[0-9]{13}[0-9]{2}",TR:"TR[0-9]{2}[0-9]{5}[A-Z0-9]{1}[A-Z0-9]{16}",VG:"VG[0-9]{2}[A-Z]{4}[0-9]{16}"},validate:function(t,i,r){var u=i.val(),f,e,s,o;if(u==="")return!0;if(u=u.replace(/[^a-zA-Z0-9]/g,"").toUpperCase(),f=r.country,f?typeof f=="string"&&this.REGEX[f]||(f=t.getDynamicOption(i,f)):f=u.substr(0,2),!this.REGEX[f])return{valid:!1,message:n.fn.bootstrapValidator.helpers.format(n.fn.bootstrapValidator.i18n.iban.countryNotSupported,f)};if(!new RegExp("^"+this.REGEX[f]+"$").test(u))return{valid:!1,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.iban.country,n.fn.bootstrapValidator.i18n.iban.countries[f])};for(u=u.substr(4)+u.substr(0,4),u=n.map(u.split(""),function(n){var t=n.charCodeAt(0);return t>="A".charCodeAt(0)&&t<="Z".charCodeAt(0)?t-"A".charCodeAt(0)+10:n}),u=u.join(""),e=parseInt(u.substr(0,1),10),s=u.length,o=1;o<s;++o)e=(e*10+parseInt(u.substr(o,1),10))%97;return{valid:e===1,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.iban.country,n.fn.bootstrapValidator.i18n.iban.countries[f])}}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.id=n.extend(n.fn.bootstrapValidator.i18n.id||{},{"default":"Please enter a valid identification number",countryNotSupported:"The country code %s is not supported",country:"Please enter a valid identification number in %s",countries:{BA:"Bosnia and Herzegovina",BG:"Bulgaria",BR:"Brazil",CH:"Switzerland",CL:"Chile",CN:"China",CZ:"Czech Republic",DK:"Denmark",EE:"Estonia",ES:"Spain",FI:"Finland",HR:"Croatia",IE:"Ireland",IS:"Iceland",LT:"Lithuania",LV:"Latvia",ME:"Montenegro",MK:"Macedonia",NL:"Netherlands",RO:"Romania",RS:"Serbia",SE:"Sweden",SI:"Slovenia",SK:"Slovakia",SM:"San Marino",TH:"Thailand",ZA:"South Africa"}});n.fn.bootstrapValidator.validators.id={html5Attributes:{message:"message",country:"country"},COUNTRY_CODES:["BA","BG","BR","CH","CL","CN","CZ","DK","EE","ES","FI","HR","IE","IS","LT","LV","ME","MK","NL","RO","RS","SE","SI","SK","SM","TH","ZA"],validate:function(t,i,r){var f=i.val(),u,e;return f===""?!0:(u=r.country,u?(typeof u!="string"||n.inArray(u.toUpperCase(),this.COUNTRY_CODES)===-1)&&(u=t.getDynamicOption(i,u)):u=f.substr(0,2),n.inArray(u,this.COUNTRY_CODES)===-1)?{valid:!1,message:n.fn.bootstrapValidator.helpers.format(n.fn.bootstrapValidator.i18n.id.countryNotSupported,u)}:(e=["_",u.toLowerCase()].join(""),this[e](f)?!0:{valid:!1,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.id.country,n.fn.bootstrapValidator.i18n.id.countries[u.toUpperCase()])})},_validateJMBG:function(n,t){var r,u;if(!/^\d{13}$/.test(n))return!1;var f=parseInt(n.substr(0,2),10),e=parseInt(n.substr(2,2),10),s=parseInt(n.substr(4,3),10),i=parseInt(n.substr(7,2),10),o=parseInt(n.substr(12,1),10);if(f>31||e>12)return!1;for(r=0,u=0;u<6;u++)r+=(7-u)*(parseInt(n.charAt(u),10)+parseInt(n.charAt(u+6),10));if(r=11-r%11,(r===10||r===11)&&(r=0),r!==o)return!1;switch(t.toUpperCase()){case"BA":return 10<=i&&i<=19;case"MK":return 41<=i&&i<=49;case"ME":return 20<=i&&i<=29;case"RS":return 70<=i&&i<=99;case"SI":return 50<=i&&i<=59;default:return!0}},_ba:function(n){return this._validateJMBG(n,"BA")},_mk:function(n){return this._validateJMBG(n,"MK")},_me:function(n){return this._validateJMBG(n,"ME")},_rs:function(n){return this._validateJMBG(n,"RS")},_si:function(n){return this._validateJMBG(n,"SI")},_bg:function(t){var r,e,u;if(!/^\d{10}$/.test(t)&&!/^\d{6}\s\d{3}\s\d{1}$/.test(t))return!1;t=t.replace(/\s/g,"");var f=parseInt(t.substr(0,2),10)+1900,i=parseInt(t.substr(2,2),10),o=parseInt(t.substr(4,2),10);if(i>40?(f+=100,i-=40):i>20&&(f-=100,i-=20),!n.fn.bootstrapValidator.helpers.date(f,i,o))return!1;for(r=0,e=[2,4,8,5,10,9,7,3,6],u=0;u<9;u++)r+=parseInt(t.charAt(u),10)*e[u];return r=r%11%10,r+""===t.substr(9,1)},_br:function(n){var i,t,r;if(/^1{11}|2{11}|3{11}|4{11}|5{11}|6{11}|7{11}|8{11}|9{11}|0{11}$/.test(n)||!/^\d{11}$/.test(n)&&!/^\d{3}\.\d{3}\.\d{3}-\d{2}$/.test(n))return!1;for(n=n.replace(/\./g,"").replace(/-/g,""),i=0,t=0;t<9;t++)i+=(10-t)*parseInt(n.charAt(t),10);if(i=11-i%11,(i===10||i===11)&&(i=0),i+""!==n.charAt(9))return!1;for(r=0,t=0;t<10;t++)r+=(11-t)*parseInt(n.charAt(t),10);return r=11-r%11,(r===10||r===11)&&(r=0),r+""===n.charAt(10)},_ch:function(n){var t;if(!/^756[\.]{0,1}[0-9]{4}[\.]{0,1}[0-9]{4}[\.]{0,1}[0-9]{2}$/.test(n))return!1;n=n.replace(/\D/g,"").substr(3);var r=n.length,i=0,u=r===8?[3,1]:[1,3];for(t=0;t<r-1;t++)i+=parseInt(n.charAt(t),10)*u[t%2];return i=10-i%10,i+""===n.charAt(r-1)},_cl:function(n){var t,r,i;if(!/^\d{7,8}[-]{0,1}[0-9K]$/i.test(n))return!1;for(n=n.replace(/\-/g,"");n.length<9;)n="0"+n;for(t=0,r=[3,2,7,6,5,4,3,2],i=0;i<8;i++)t+=parseInt(n.charAt(i),10)*r[i];return t=11-t%11,t===11?t=0:t===10&&(t="K"),t+""===n.charAt(8).toUpperCase()},_cn:function(t){var h,r,i,f,u,l,a;if(t=t.trim(),!/^\d{15}$/.test(t)&&!/^\d{17}[\dXx]{1}$/.test(t))return!1;var e={11:{0:[0],1:[[0,9],[11,17]],2:[0,28,29]},12:{0:[0],1:[[0,16]],2:[0,21,23,25]},13:{0:[0],1:[[0,5],7,8,21,[23,33],[81,85]],2:[[0,5],[7,9],[23,25],27,29,30,81,83],3:[[0,4],[21,24]],4:[[0,4],6,21,[23,35],81],5:[[0,3],[21,35],81,82],6:[[0,4],[21,38],[81,84]],7:[[0,3],5,6,[21,33]],8:[[0,4],[21,28]],9:[[0,3],[21,30],[81,84]],10:[[0,3],[22,26],28,81,82],11:[[0,2],[21,28],81,82]},14:{0:[0],1:[0,1,[5,10],[21,23],81],2:[[0,3],11,12,[21,27]],3:[[0,3],11,21,22],4:[[0,2],11,21,[23,31],81],5:[[0,2],21,22,24,25,81],6:[[0,3],[21,24]],7:[[0,2],[21,29],81],8:[[0,2],[21,30],81,82],9:[[0,2],[21,32],81],10:[[0,2],[21,34],81,82],11:[[0,2],[21,30],81,82],23:[[0,3],22,23,[25,30],32,33]},15:{0:[0],1:[[0,5],[21,25]],2:[[0,7],[21,23]],3:[[0,4]],4:[[0,4],[21,26],[28,30]],5:[[0,2],[21,26],81],6:[[0,2],[21,27]],7:[[0,3],[21,27],[81,85]],8:[[0,2],[21,26]],9:[[0,2],[21,29],81],22:[[0,2],[21,24]],25:[[0,2],[22,31]],26:[[0,2],[24,27],[29,32],34],28:[0,1,[22,27]],29:[0,[21,23]]},21:{0:[0],1:[[0,6],[11,14],[22,24],81],2:[[0,4],[11,13],24,[81,83]],3:[[0,4],11,21,23,81],4:[[0,4],11,[21,23]],5:[[0,5],21,22],6:[[0,4],24,81,82],7:[[0,3],11,26,27,81,82],8:[[0,4],11,81,82],9:[[0,5],11,21,22],10:[[0,5],11,21,81],11:[[0,3],21,22],12:[[0,2],4,21,23,24,81,82],13:[[0,3],21,22,24,81,82],14:[[0,4],21,22,81]},22:{0:[0],1:[[0,6],12,22,[81,83]],2:[[0,4],11,21,[81,84]],3:[[0,3],22,23,81,82],4:[[0,3],21,22],5:[[0,3],21,23,24,81,82],6:[[0,2],4,5,[21,23],25,81],7:[[0,2],[21,24],81],8:[[0,2],21,22,81,82],24:[[0,6],24,26]},23:{0:[0],1:[[0,12],21,[23,29],[81,84]],2:[[0,8],21,[23,25],27,[29,31],81],3:[[0,7],21,81,82],4:[[0,7],21,22],5:[[0,3],5,6,[21,24]],6:[[0,6],[21,24]],7:[[0,16],22,81],8:[[0,5],11,22,26,28,33,81,82],9:[[0,4],21],10:[[0,5],24,25,81,[83,85]],11:[[0,2],21,23,24,81,82],12:[[0,2],[21,26],[81,83]],27:[[0,4],[21,23]]},31:{0:[0],1:[0,1,[3,10],[12,20]],2:[0,30]},32:{0:[0],1:[[0,7],11,[13,18],24,25],2:[[0,6],11,81,82],3:[[0,5],11,12,[21,24],81,82],4:[[0,2],4,5,11,12,81,82],5:[[0,9],[81,85]],6:[[0,2],11,12,21,23,[81,84]],7:[0,1,3,5,6,[21,24]],8:[[0,4],11,26,[29,31]],9:[[0,3],[21,25],28,81,82],10:[[0,3],11,12,23,81,84,88],11:[[0,2],11,12,[81,83]],12:[[0,4],[81,84]],13:[[0,2],11,[21,24]]},33:{0:[0],1:[[0,6],[8,10],22,27,82,83,85],2:[0,1,[3,6],11,12,25,26,[81,83]],3:[[0,4],22,24,[26,29],81,82],4:[[0,2],11,21,24,[81,83]],5:[[0,3],[21,23]],6:[[0,2],21,24,[81,83]],7:[[0,3],23,26,27,[81,84]],8:[[0,3],22,24,25,81],9:[[0,3],21,22],10:[[0,4],[21,24],81,82],11:[[0,2],[21,27],81]},34:{0:[0],1:[[0,4],11,[21,24],81],2:[[0,4],7,8,[21,23],25],3:[[0,4],11,[21,23]],4:[[0,6],21],5:[[0,4],6,[21,23]],6:[[0,4],21],7:[[0,3],11,21],8:[[0,3],11,[22,28],81],10:[[0,4],[21,24]],11:[[0,3],22,[24,26],81,82],12:[[0,4],21,22,25,26,82],13:[[0,2],[21,24]],14:[[0,2],[21,24]],15:[[0,3],[21,25]],16:[[0,2],[21,23]],17:[[0,2],[21,23]],18:[[0,2],[21,25],81]},35:{0:[0],1:[[0,5],11,[21,25],28,81,82],2:[[0,6],[11,13]],3:[[0,5],22],4:[[0,3],21,[23,30],81],5:[[0,5],21,[24,27],[81,83]],6:[[0,3],[22,29],81],7:[[0,2],[21,25],[81,84]],8:[[0,2],[21,25],81],9:[[0,2],[21,26],81,82]},36:{0:[0],1:[[0,5],11,[21,24]],2:[[0,3],22,81],3:[[0,2],13,[21,23]],4:[[0,3],21,[23,30],81,82],5:[[0,2],21],6:[[0,2],22,81],7:[[0,2],[21,35],81,82],8:[[0,3],[21,30],81],9:[[0,2],[21,26],[81,83]],10:[[0,2],[21,30]],11:[[0,2],[21,30],81]},37:{0:[0],1:[[0,5],12,13,[24,26],81],2:[[0,3],5,[11,14],[81,85]],3:[[0,6],[21,23]],4:[[0,6],81],5:[[0,3],[21,23]],6:[[0,2],[11,13],34,[81,87]],7:[[0,5],24,25,[81,86]],8:[[0,2],11,[26,32],[81,83]],9:[[0,3],11,21,23,82,83],10:[[0,2],[81,83]],11:[[0,3],21,22],12:[[0,3]],13:[[0,2],11,12,[21,29]],14:[[0,2],[21,28],81,82],15:[[0,2],[21,26],81],16:[[0,2],[21,26]],17:[[0,2],[21,28]]},41:{0:[0],1:[[0,6],8,22,[81,85]],2:[[0,5],11,[21,25]],3:[[0,7],11,[22,29],81],4:[[0,4],11,[21,23],25,81,82],5:[[0,3],5,6,22,23,26,27,81],6:[[0,3],11,21,22],7:[[0,4],11,21,[24,28],81,82],8:[[0,4],11,[21,23],25,[81,83]],9:[[0,2],22,23,[26,28]],10:[[0,2],[23,25],81,82],11:[[0,4],[21,23]],12:[[0,2],21,22,24,81,82],13:[[0,3],[21,30],81],14:[[0,3],[21,26],81],15:[[0,3],[21,28]],16:[[0,2],[21,28],81],17:[[0,2],[21,29]],90:[0,1]},42:{0:[0],1:[[0,7],[11,17]],2:[[0,5],22,81],3:[[0,3],[21,25],81],5:[[0,6],[25,29],[81,83]],6:[[0,2],6,7,[24,26],[82,84]],7:[[0,4]],8:[[0,2],4,21,22,81],9:[[0,2],[21,23],81,82,84],10:[[0,3],[22,24],81,83,87],11:[[0,2],[21,27],81,82],12:[[0,2],[21,24],81],13:[[0,3],21,81],28:[[0,2],22,23,[25,28]],90:[0,[4,6],21]},43:{0:[0],1:[[0,5],11,12,21,22,24,81],2:[[0,4],11,21,[23,25],81],3:[[0,2],4,21,81,82],4:[0,1,[5,8],12,[21,24],26,81,82],5:[[0,3],11,[21,25],[27,29],81],6:[[0,3],11,21,23,24,26,81,82],7:[[0,3],[21,26],81],8:[[0,2],11,21,22],9:[[0,3],[21,23],81],10:[[0,3],[21,28],81],11:[[0,3],[21,29]],12:[[0,2],[21,30],81],13:[[0,2],21,22,81,82],31:[0,1,[22,27],30]},44:{0:[0],1:[[0,7],[11,16],83,84],2:[[0,5],21,22,24,29,32,33,81,82],3:[0,1,[3,8]],4:[[0,4]],5:[0,1,[6,15],23,82,83],6:[0,1,[4,8]],7:[0,1,[3,5],81,[83,85]],8:[[0,4],11,23,25,[81,83]],9:[[0,3],23,[81,83]],12:[[0,3],[23,26],83,84],13:[[0,3],[22,24],81],14:[[0,2],[21,24],26,27,81],15:[[0,2],21,23,81],16:[[0,2],[21,25]],17:[[0,2],21,23,81],18:[[0,3],21,23,[25,27],81,82],19:[0],20:[0],51:[[0,3],21,22],52:[[0,3],21,22,24,81],53:[[0,2],[21,23],81]},45:{0:[0],1:[[0,9],[21,27]],2:[[0,5],[21,26]],3:[[0,5],11,12,[21,32]],4:[0,1,[3,6],11,[21,23],81],5:[[0,3],12,21],6:[[0,3],21,81],7:[[0,3],21,22],8:[[0,4],21,81],9:[[0,3],[21,24],81],10:[[0,2],[21,31]],11:[[0,2],[21,23]],12:[[0,2],[21,29],81],13:[[0,2],[21,24],81],14:[[0,2],[21,25],81]},46:{0:[0],1:[0,1,[5,8]],2:[0,1],3:[0,[21,23]],90:[[0,3],[5,7],[21,39]]},50:{0:[0],1:[[0,19]],2:[0,[22,38],[40,43]],3:[0,[81,84]]},51:{0:[0],1:[0,1,[4,8],[12,15],[21,24],29,31,32,[81,84]],3:[[0,4],11,21,22],4:[[0,3],11,21,22],5:[[0,4],21,22,24,25],6:[0,1,3,23,26,[81,83]],7:[0,1,3,4,[22,27],81],8:[[0,2],11,12,[21,24]],9:[[0,4],[21,23]],10:[[0,2],11,24,25,28],11:[[0,2],[11,13],23,24,26,29,32,33,81],13:[[0,4],[21,25],81],14:[[0,2],[21,25]],15:[[0,3],[21,29]],16:[[0,3],[21,23],81],17:[[0,3],[21,25],81],18:[[0,3],[21,27]],19:[[0,3],[21,23]],20:[[0,2],21,22,81],32:[0,[21,33]],33:[0,[21,38]],34:[0,1,[22,37]]},52:{0:[0],1:[[0,3],[11,15],[21,23],81],2:[0,1,3,21,22],3:[[0,3],[21,30],81,82],4:[[0,2],[21,25]],5:[[0,2],[21,27]],6:[[0,3],[21,28]],22:[0,1,[22,30]],23:[0,1,[22,28]],24:[0,1,[22,28]],26:[0,1,[22,36]],27:[[0,2],22,23,[25,32]]},53:{0:[0],1:[[0,3],[11,14],21,22,[24,29],81],3:[[0,2],[21,26],28,81],4:[[0,2],[21,28]],5:[[0,2],[21,24]],6:[[0,2],[21,30]],7:[[0,2],[21,24]],8:[[0,2],[21,29]],9:[[0,2],[21,27]],23:[0,1,[22,29],31],25:[[0,4],[22,32]],26:[0,1,[21,28]],27:[0,1,[22,30]],28:[0,1,22,23],29:[0,1,[22,32]],31:[0,2,3,[22,24]],34:[0,[21,23]],33:[0,21,[23,25]],35:[0,[21,28]]},54:{0:[0],1:[[0,2],[21,27]],21:[0,[21,29],32,33],22:[0,[21,29],[31,33]],23:[0,1,[22,38]],24:[0,[21,31]],25:[0,[21,27]],26:[0,[21,27]]},61:{0:[0],1:[[0,4],[11,16],22,[24,26]],2:[[0,4],22],3:[[0,4],[21,24],[26,31]],4:[[0,4],[22,31],81],5:[[0,2],[21,28],81,82],6:[[0,2],[21,32]],7:[[0,2],[21,30]],8:[[0,2],[21,31]],9:[[0,2],[21,29]],10:[[0,2],[21,26]]},62:{0:[0],1:[[0,5],11,[21,23]],2:[0,1],3:[[0,2],21],4:[[0,3],[21,23]],5:[[0,3],[21,25]],6:[[0,2],[21,23]],7:[[0,2],[21,25]],8:[[0,2],[21,26]],9:[[0,2],[21,24],81,82],10:[[0,2],[21,27]],11:[[0,2],[21,26]],12:[[0,2],[21,28]],24:[0,21,[24,29]],26:[0,21,[23,30]],29:[0,1,[21,27]],30:[0,1,[21,27]]},63:{0:[0],1:[[0,5],[21,23]],2:[0,2,[21,25]],21:[0,[21,23],[26,28]],22:[0,[21,24]],23:[0,[21,24]],25:[0,[21,25]],26:[0,[21,26]],27:[0,1,[21,26]],28:[[0,2],[21,23]]},64:{0:[0],1:[0,1,[4,6],21,22,81],2:[[0,3],5,[21,23]],3:[[0,3],[21,24],81],4:[[0,2],[21,25]],5:[[0,2],21,22]},65:{0:[0],1:[[0,9],21],2:[[0,5]],21:[0,1,22,23],22:[0,1,22,23],23:[[0,3],[23,25],27,28],28:[0,1,[22,29]],29:[0,1,[22,29]],30:[0,1,[22,24]],31:[0,1,[21,31]],32:[0,1,[21,27]],40:[0,2,3,[21,28]],42:[[0,2],21,[23,26]],43:[0,1,[21,26]],90:[[0,4]],27:[[0,2],22,23]},71:{0:[0]},81:{0:[0]},82:{0:[0]}},o=parseInt(t.substr(0,2),10),c=parseInt(t.substr(2,2),10),s=parseInt(t.substr(4,2),10);if(!e[o]||!e[o][c])return!1;for(h=!1,r=e[o][c],i=0;i<r.length;i++)if(n.isArray(r[i])&&r[i][0]<=s&&s<=r[i][1]||!n.isArray(r[i])&&s===r[i]){h=!0;break}if(!h)return!1;f=t.length===18?t.substr(6,8):"19"+t.substr(6,6);var v=parseInt(f.substr(0,4),10),y=parseInt(f.substr(4,2),10),p=parseInt(f.substr(6,2),10);if(!n.fn.bootstrapValidator.helpers.date(v,y,p))return!1;if(t.length===18){for(u=0,l=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2],i=0;i<17;i++)u+=parseInt(t.charAt(i),10)*l[i];return u=(12-u%11)%11,a=t.charAt(17).toUpperCase()!=="X"?parseInt(t.charAt(17),10):10,a===u}return!0},_cz:function(t){var r;if(!/^\d{9,10}$/.test(t))return!1;var i=1900+parseInt(t.substr(0,2),10),u=parseInt(t.substr(2,2),10)%50%20,f=parseInt(t.substr(4,2),10);if(t.length===9){if(i>=1980&&(i-=100),i>1953)return!1}else i<1954&&(i+=100);return n.fn.bootstrapValidator.helpers.date(i,u,f)?t.length===10?(r=parseInt(t.substr(0,9),10)%11,i<1985&&(r=r%10),r+""===t.substr(9,1)):!0:!1},_dk:function(t){if(!/^[0-9]{6}[-]{0,1}[0-9]{4}$/.test(t))return!1;t=t.replace(/-/g,"");var r=parseInt(t.substr(0,2),10),u=parseInt(t.substr(2,2),10),i=parseInt(t.substr(4,2),10);switch(!0){case"5678".indexOf(t.charAt(6))!==-1&&i>=58:i+=1800;break;case"0123".indexOf(t.charAt(6))!==-1:case"49".indexOf(t.charAt(6))!==-1&&i>=37:i+=1900;break;default:i+=2e3}return n.fn.bootstrapValidator.helpers.date(i,u,r)},_ee:function(n){return this._lt(n)},_es:function(n){var i,t;return!/^[0-9A-Z]{8}[-]{0,1}[0-9A-Z]$/.test(n)&&!/^[XYZ][-]{0,1}[0-9]{7}[-]{0,1}[0-9A-Z]$/.test(n)?!1:(n=n.replace(/-/g,""),i="XYZ".indexOf(n.charAt(0)),i!==-1&&(n=i+n.substr(1)+""),t=parseInt(n.substr(0,8),10),t="TRWAGMYFPDXBNJZSQVHLCKE"[t%23],t===n.substr(8,1))},_fi:function(t){var u,i;if(!/^[0-9]{6}[-+A][0-9]{3}[0-9ABCDEFHJKLMNPRSTUVWXY]$/.test(t))return!1;var f=parseInt(t.substr(0,2),10),e=parseInt(t.substr(2,2),10),r=parseInt(t.substr(4,2),10);return(r={"+":1800,"-":1900,A:2e3}[t.charAt(6)]+r,!n.fn.bootstrapValidator.helpers.date(r,e,f))?!1:(u=parseInt(t.substr(7,3),10),u<2)?!1:(i=t.substr(0,6)+t.substr(7,3)+"",i=parseInt(i,10),"0123456789ABCDEFHJKLMNPRSTUVWXY".charAt(i%31)===t.charAt(10))},_hr:function(t){return/^[0-9]{11}$/.test(t)?n.fn.bootstrapValidator.helpers.mod11And10(t):!1},_ie:function(n){if(!/^\d{7}[A-W][AHWTX]?$/.test(n))return!1;var t=function(n){for(var r,i,t;n.length<7;)n="0"+n;for(r="WABCDEFGHIJKLMNOPQRSTUV",i=0,t=0;t<7;t++)i+=parseInt(n.charAt(t),10)*(8-t);return i+=9*r.indexOf(n.substr(7)),r[i%23]};return n.length===9&&("A"===n.charAt(8)||"H"===n.charAt(8))?n.charAt(7)===t(n.substr(0,7)+n.substr(8)+""):n.charAt(7)===t(n.substr(0,7))},_is:function(t){var i,e,r;if(!/^[0-9]{6}[-]{0,1}[0-9]{4}$/.test(t))return!1;t=t.replace(/-/g,"");var o=parseInt(t.substr(0,2),10),s=parseInt(t.substr(2,2),10),u=parseInt(t.substr(4,2),10),f=parseInt(t.charAt(9),10);if(u=f===9?1900+u:(20+f)*100+u,!n.fn.bootstrapValidator.helpers.date(u,s,o,!0))return!1;for(i=0,e=[3,2,7,6,5,4,3,2],r=0;r<8;r++)i+=parseInt(t.charAt(r),10)*e[r];return i=11-i%11,i+""===t.charAt(8)},_lt:function(t){var i,u,r;if(!/^[0-9]{11}$/.test(t))return!1;var f=parseInt(t.charAt(0),10),e=parseInt(t.substr(1,2),10),o=parseInt(t.substr(3,2),10),s=parseInt(t.substr(5,2),10),h=f%2==0?17+f/2:17+(f+1)/2;if(e=h*100+e,!n.fn.bootstrapValidator.helpers.date(e,o,s,!0))return!1;for(i=0,u=[1,2,3,4,5,6,7,8,9,1],r=0;r<10;r++)i+=parseInt(t.charAt(r),10)*u[r];if(i=i%11,i!==10)return i+""===t.charAt(10);for(i=0,u=[3,4,5,6,7,8,9,1,2,3],r=0;r<10;r++)i+=parseInt(t.charAt(r),10)*u[r];return i=i%11,i===10&&(i=0),i+""===t.charAt(10)},_lv:function(t){var i,f,r;if(!/^[0-9]{6}[-]{0,1}[0-9]{5}$/.test(t))return!1;t=t.replace(/\D/g,"");var e=parseInt(t.substr(0,2),10),o=parseInt(t.substr(2,2),10),u=parseInt(t.substr(4,2),10);if(u=u+1800+parseInt(t.charAt(6),10)*100,!n.fn.bootstrapValidator.helpers.date(u,o,e,!0))return!1;for(i=0,f=[10,5,8,4,2,1,6,3,7,9],r=0;r<10;r++)i+=parseInt(t.charAt(r),10)*f[r];return i=(i+1)%11%10,i+""===t.charAt(10)},_nl:function(n){for(var t,r,i;n.length<9;)n="0"+n;if(!/^[0-9]{4}[.]{0,1}[0-9]{2}[.]{0,1}[0-9]{3}$/.test(n)||(n=n.replace(/\./g,""),parseInt(n,10)===0))return!1;for(t=0,r=n.length,i=0;i<r-1;i++)t+=(9-i)*parseInt(n.charAt(i),10);return t=t%11,t===10&&(t=0),t+""===n.charAt(r-1)},_ro:function(t){var i,u;if(!/^[0-9]{13}$/.test(t)||(i=parseInt(t.charAt(0),10),i===0||i===7||i===8))return!1;var f=parseInt(t.substr(1,2),10),e=parseInt(t.substr(3,2),10),o=parseInt(t.substr(5,2),10);if(o>31&&e>12||i!==9&&(f={"1":1900,"2":1900,"3":1800,"4":1800,"5":2e3,"6":2e3}[i+""]+f,!n.fn.bootstrapValidator.helpers.date(f,e,o)))return!1;var r=0,h=[2,7,9,1,4,6,3,5,8,2,7,9],s=t.length;for(u=0;u<s-1;u++)r+=parseInt(t.charAt(u),10)*h[u];return r=r%11,r===10&&(r=1),r+""===t.charAt(s-1)},_se:function(t){if(!/^[0-9]{10}$/.test(t)&&!/^[0-9]{6}[-|+][0-9]{4}$/.test(t))return!1;t=t.replace(/[^0-9]/g,"");var i=parseInt(t.substr(0,2),10)+1900,r=parseInt(t.substr(2,2),10),u=parseInt(t.substr(4,2),10);return n.fn.bootstrapValidator.helpers.date(i,r,u)?n.fn.bootstrapValidator.helpers.luhn(t):!1},_sk:function(n){return this._cz(n)},_sm:function(n){return/^\d{5}$/.test(n)},_th:function(n){var i,t;if(n.length!==13)return!1;for(i=0,t=0;t<12;t++)i+=parseInt(n.charAt(t),10)*(13-t);return(11-i%11)%10===parseInt(n.charAt(12),10)},_za:function(t){if(!/^[0-9]{10}[0|1][8|9][0-9]$/.test(t))return!1;var i=parseInt(t.substr(0,2),10),r=(new Date).getFullYear()%100,u=parseInt(t.substr(2,2),10),f=parseInt(t.substr(4,2),10);return(i=i>=r?i+1900:i+2e3,!n.fn.bootstrapValidator.helpers.date(i,u,f))?!1:n.fn.bootstrapValidator.helpers.luhn(t)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.identical=n.extend(n.fn.bootstrapValidator.i18n.identical||{},{"default":"Please enter the same value"});n.fn.bootstrapValidator.validators.identical={html5Attributes:{message:"message",field:"field"},validate:function(n,t,i){var u=t.val(),r;return u===""?!0:(r=n.getFieldElements(i.field),r===null||r.length===0)?!0:u===r.val()?(n.updateStatus(i.field,n.STATUS_VALID,"identical"),!0):!1}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.imei=n.extend(n.fn.bootstrapValidator.i18n.imei||{},{"default":"Please enter a valid IMEI number"});n.fn.bootstrapValidator.validators.imei={validate:function(t,i){var r=i.val();if(r==="")return!0;switch(!0){case/^\d{15}$/.test(r):case/^\d{2}-\d{6}-\d{6}-\d{1}$/.test(r):case/^\d{2}\s\d{6}\s\d{6}\s\d{1}$/.test(r):return r=r.replace(/[^0-9]/g,""),n.fn.bootstrapValidator.helpers.luhn(r);case/^\d{14}$/.test(r):case/^\d{16}$/.test(r):case/^\d{2}-\d{6}-\d{6}(|-\d{2})$/.test(r):case/^\d{2}\s\d{6}\s\d{6}(|\s\d{2})$/.test(r):return!0;default:return!1}}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.imo=n.extend(n.fn.bootstrapValidator.i18n.imo||{},{"default":"Please enter a valid IMO number"});n.fn.bootstrapValidator.validators.imo={validate:function(n,t){var r=t.val(),u,f,i;if(r==="")return!0;if(!/^IMO \d{7}$/i.test(r))return!1;for(u=0,f=r.replace(/^.*(\d{7})$/,"$1"),i=6;i>=1;i--)u+=f.slice(6-i,-i)*(i+1);return u%10===parseInt(f.charAt(6),10)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.integer=n.extend(n.fn.bootstrapValidator.i18n.integer||{},{"default":"Please enter a valid number"});n.fn.bootstrapValidator.validators.integer={enableByHtml5:function(n){return"number"===n.attr("type")&&(n.attr("step")===undefined||n.attr("step")%1==0)},validate:function(n,t){if(this.enableByHtml5(t)&&t.get(0).validity&&t.get(0).validity.badInput===!0)return!1;var i=t.val();return i===""?!0:/^(?:-?(?:0|[1-9][0-9]*))$/.test(i)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.ip=n.extend(n.fn.bootstrapValidator.i18n.ip||{},{"default":"Please enter a valid IP address",ipv4:"Please enter a valid IPv4 address",ipv6:"Please enter a valid IPv6 address"});n.fn.bootstrapValidator.validators.ip={html5Attributes:{message:"message",ipv4:"ipv4",ipv6:"ipv6"},validate:function(t,i,r){var u=i.val();if(u==="")return!0;r=n.extend({},{ipv4:!0,ipv6:!0},r);var o=/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,s=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,f=!1,e;switch(!0){case r.ipv4&&!r.ipv6:f=o.test(u);e=r.message||n.fn.bootstrapValidator.i18n.ip.ipv4;break;case!r.ipv4&&r.ipv6:f=s.test(u);e=r.message||n.fn.bootstrapValidator.i18n.ip.ipv6;break;case r.ipv4&&r.ipv6:default:f=o.test(u)||s.test(u);e=r.message||n.fn.bootstrapValidator.i18n.ip["default"]}return{valid:f,message:e}}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.isbn=n.extend(n.fn.bootstrapValidator.i18n.isbn||{},{"default":"Please enter a valid ISBN number"});n.fn.bootstrapValidator.validators.isbn={validate:function(n,t){var i=t.val(),s;if(i==="")return!0;switch(!0){case/^\d{9}[\dX]$/.test(i):case i.length===13&&/^(\d+)-(\d+)-(\d+)-([\dX])$/.test(i):case i.length===13&&/^(\d+)\s(\d+)\s(\d+)\s([\dX])$/.test(i):s="ISBN10";break;case/^(978|979)\d{9}[\dX]$/.test(i):case i.length===17&&/^(978|979)-(\d+)-(\d+)-(\d+)-([\dX])$/.test(i):case i.length===17&&/^(978|979)\s(\d+)\s(\d+)\s(\d+)\s([\dX])$/.test(i):s="ISBN13";break;default:return!1}i=i.replace(/[^0-9X]/gi,"");var f=i.split(""),o=f.length,e=0,r,u;switch(s){case"ISBN10":for(e=0,r=0;r<o-1;r++)e+=parseInt(f[r],10)*(10-r);return u=11-e%11,u===11?u=0:u===10&&(u="X"),u+""===f[o-1];case"ISBN13":for(e=0,r=0;r<o-1;r++)e+=r%2==0?parseInt(f[r],10):parseInt(f[r],10)*3;return u=10-e%10,u===10&&(u="0"),u+""===f[o-1];default:return!1}}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.isin=n.extend(n.fn.bootstrapValidator.i18n.isin||{},{"default":"Please enter a valid ISIN number"});n.fn.bootstrapValidator.validators.isin={COUNTRY_CODES:"AF|AX|AL|DZ|AS|AD|AO|AI|AQ|AG|AR|AM|AW|AU|AT|AZ|BS|BH|BD|BB|BY|BE|BZ|BJ|BM|BT|BO|BQ|BA|BW|BV|BR|IO|BN|BG|BF|BI|KH|CM|CA|CV|KY|CF|TD|CL|CN|CX|CC|CO|KM|CG|CD|CK|CR|CI|HR|CU|CW|CY|CZ|DK|DJ|DM|DO|EC|EG|SV|GQ|ER|EE|ET|FK|FO|FJ|FI|FR|GF|PF|TF|GA|GM|GE|DE|GH|GI|GR|GL|GD|GP|GU|GT|GG|GN|GW|GY|HT|HM|VA|HN|HK|HU|IS|IN|ID|IR|IQ|IE|IM|IL|IT|JM|JP|JE|JO|KZ|KE|KI|KP|KR|KW|KG|LA|LV|LB|LS|LR|LY|LI|LT|LU|MO|MK|MG|MW|MY|MV|ML|MT|MH|MQ|MR|MU|YT|MX|FM|MD|MC|MN|ME|MS|MA|MZ|MM|NA|NR|NP|NL|NC|NZ|NI|NE|NG|NU|NF|MP|NO|OM|PK|PW|PS|PA|PG|PY|PE|PH|PN|PL|PT|PR|QA|RE|RO|RU|RW|BL|SH|KN|LC|MF|PM|VC|WS|SM|ST|SA|SN|RS|SC|SL|SG|SX|SK|SI|SB|SO|ZA|GS|SS|ES|LK|SD|SR|SJ|SZ|SE|CH|SY|TW|TJ|TZ|TH|TL|TG|TK|TO|TT|TN|TR|TM|TC|TV|UG|UA|AE|GB|US|UM|UY|UZ|VU|VE|VN|VG|VI|WF|EH|YE|ZM|ZW",validate:function(n,t){var r=t.val(),h,f,e,i,o,u;if(r==="")return!0;if(r=r.toUpperCase(),h=new RegExp("^("+this.COUNTRY_CODES+")[0-9A-Z]{10}$"),!h.test(r))return!1;for(f="",e=r.length,i=0;i<e-1;i++)o=r.charCodeAt(i),f+=o>57?(o-55).toString():r.charAt(i);var s="",c=f.length,l=c%2!=0?0:1;for(i=0;i<c;i++)s+=parseInt(f[i],10)*(i%2===l?2:1)+"";for(u=0,i=0;i<s.length;i++)u+=parseInt(s.charAt(i),10);return u=(10-u%10)%10,u+""===r.charAt(e-1)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.ismn=n.extend(n.fn.bootstrapValidator.i18n.ismn||{},{"default":"Please enter a valid ISMN number"});n.fn.bootstrapValidator.validators.ismn={validate:function(n,t){var i=t.val(),f,r;if(i==="")return!0;switch(!0){case/^M\d{9}$/.test(i):case/^M-\d{4}-\d{4}-\d{1}$/.test(i):case/^M\s\d{4}\s\d{4}\s\d{1}$/.test(i):f="ISMN10";break;case/^9790\d{9}$/.test(i):case/^979-0-\d{4}-\d{4}-\d{1}$/.test(i):case/^979\s0\s\d{4}\s\d{4}\s\d{1}$/.test(i):f="ISMN13";break;default:return!1}"ISMN10"===f&&(i="9790"+i.substr(1));i=i.replace(/[^0-9]/gi,"");var e=i.length,u=0,o=[1,3];for(r=0;r<e-1;r++)u+=parseInt(i.charAt(r),10)*o[r%2];return u=10-u%10,u+""===i.charAt(e-1)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.issn=n.extend(n.fn.bootstrapValidator.i18n.issn||{},{"default":"Please enter a valid ISSN number"});n.fn.bootstrapValidator.validators.issn={validate:function(n,t){var i=t.val(),r;if(i==="")return!0;if(!/^\d{4}\-\d{3}[\dX]$/.test(i))return!1;i=i.replace(/[^0-9X]/gi,"");var u=i.split(""),e=u.length,f=0;for(u[7]==="X"&&(u[7]=10),r=0;r<e;r++)f+=parseInt(u[r],10)*(8-r);return f%11==0}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.lessThan=n.extend(n.fn.bootstrapValidator.i18n.lessThan||{},{"default":"Please enter a value less than or equal to %s",notInclusive:"Please enter a value less than %s"});n.fn.bootstrapValidator.validators.lessThan={html5Attributes:{message:"message",value:"value",inclusive:"inclusive"},enableByHtml5:function(n){var i=n.attr("type"),t=n.attr("max");return t&&i!=="date"?{value:t}:!1},validate:function(t,i,r){var u=i.val(),f,e;return u===""?!0:(u=this._format(u),!n.isNumeric(u))?!1:(f=n.isNumeric(r.value)?r.value:t.getDynamicOption(i,r.value),e=this._format(f),u=parseFloat(u),r.inclusive===!0||r.inclusive===undefined?{valid:u<=e,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.lessThan["default"],f)}:{valid:u<e,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.lessThan.notInclusive,f)})},_format:function(n){return(n+"").replace(",",".")}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.mac=n.extend(n.fn.bootstrapValidator.i18n.mac||{},{"default":"Please enter a valid MAC address"});n.fn.bootstrapValidator.validators.mac={validate:function(n,t){var i=t.val();return i===""?!0:/^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/.test(i)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.meid=n.extend(n.fn.bootstrapValidator.i18n.meid||{},{"default":"Please enter a valid MEID number"});n.fn.bootstrapValidator.validators.meid={validate:function(t,i){var r=i.val(),o,e,u,f;if(r==="")return!0;switch(!0){case/^[0-9A-F]{15}$/i.test(r):case/^[0-9A-F]{2}[- ][0-9A-F]{6}[- ][0-9A-F]{6}[- ][0-9A-F]$/i.test(r):case/^\d{19}$/.test(r):case/^\d{5}[- ]\d{5}[- ]\d{4}[- ]\d{4}[- ]\d$/.test(r):if(o=r.charAt(r.length-1),r=r.replace(/[- ]/g,""),r.match(/^\d*$/i))return n.fn.bootstrapValidator.helpers.luhn(r);for(r=r.slice(0,-1),e="",u=1;u<=13;u+=2)e+=(parseInt(r.charAt(u),16)*2).toString(16);for(f=0,u=0;u<e.length;u++)f+=parseInt(e.charAt(u),16);return f%10==0?o==="0":o===((Math.floor((f+10)/10)*10-f)*2).toString(16);case/^[0-9A-F]{14}$/i.test(r):case/^[0-9A-F]{2}[- ][0-9A-F]{6}[- ][0-9A-F]{6}$/i.test(r):case/^\d{18}$/.test(r):case/^\d{5}[- ]\d{5}[- ]\d{4}[- ]\d{4}$/.test(r):return!0;default:return!1}}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.notEmpty=n.extend(n.fn.bootstrapValidator.i18n.notEmpty||{},{"default":"Please enter a value"});n.fn.bootstrapValidator.validators.notEmpty={enableByHtml5:function(n){var t=n.attr("required")+"";return"required"===t||"true"===t},validate:function(t,i){var r=i.attr("type");return"radio"===r||"checkbox"===r?t.getFieldElements(i.attr("data-bv-field")).filter(":checked").length>0:"number"===r&&i.get(0).validity&&i.get(0).validity.badInput===!0?!0:n.trim(i.val())!==""}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.numeric=n.extend(n.fn.bootstrapValidator.i18n.numeric||{},{"default":"Please enter a valid float number"});n.fn.bootstrapValidator.validators.numeric={html5Attributes:{message:"message",separator:"separator"},enableByHtml5:function(n){return"number"===n.attr("type")&&n.attr("step")!==undefined&&n.attr("step")%1!=0},validate:function(n,t,i){var r,u;return this.enableByHtml5(t)&&t.get(0).validity&&t.get(0).validity.badInput===!0?!1:(r=t.val(),r==="")?!0:(u=i.separator||".",u!=="."&&(r=r.replace(u,".")),!isNaN(parseFloat(r))&&isFinite(r))}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.phone=n.extend(n.fn.bootstrapValidator.i18n.phone||{},{"default":"Please enter a valid phone number",countryNotSupported:"The country code %s is not supported",country:"Please enter a valid phone number in %s",countries:{BR:"Brazil",CN:"China",CZ:"Czech Republic",DE:"Germany",DK:"Denmark",ES:"Spain",FR:"France",GB:"United Kingdom",MA:"Morocco",PK:"Pakistan",RO:"Romania",RU:"Russia",SK:"Slovakia",TH:"Thailand",US:"USA",VE:"Venezuela"}});n.fn.bootstrapValidator.validators.phone={html5Attributes:{message:"message",country:"country"},COUNTRY_CODES:["BR","CN","CZ","DE","DK","ES","FR","GB","MA","PK","RO","RU","SK","TH","US","VE"],validate:function(t,i,r){var u=i.val(),e,f;if(u==="")return!0;if(e=r.country,(typeof e!="string"||n.inArray(e,this.COUNTRY_CODES)===-1)&&(e=t.getDynamicOption(i,e)),!e||n.inArray(e.toUpperCase(),this.COUNTRY_CODES)===-1)return{valid:!1,message:n.fn.bootstrapValidator.helpers.format(n.fn.bootstrapValidator.i18n.phone.countryNotSupported,e)};f=!0;switch(e.toUpperCase()){case"BR":u=n.trim(u);f=/^(([\d]{4}[-.\s]{1}[\d]{2,3}[-.\s]{1}[\d]{2}[-.\s]{1}[\d]{2})|([\d]{4}[-.\s]{1}[\d]{3}[-.\s]{1}[\d]{4})|((\(?\+?[0-9]{2}\)?\s?)?(\(?\d{2}\)?\s?)?\d{4,5}[-.\s]?\d{4}))$/.test(u);break;case"CN":u=n.trim(u);f=/^((00|\+)?(86(?:-| )))?((\d{11})|(\d{3}[- ]{1}\d{4}[- ]{1}\d{4})|((\d{2,4}[- ]){1}(\d{7,8}|(\d{3,4}[- ]{1}\d{4}))([- ]{1}\d{1,4})?))$/.test(u);break;case"CZ":f=/^(((00)([- ]?)|\+)(420)([- ]?))?((\d{3})([- ]?)){2}(\d{3})$/.test(u);break;case"DE":u=n.trim(u);f=/^(((((((00|\+)49[ \-/]?)|0)[1-9][0-9]{1,4})[ \-/]?)|((((00|\+)49\()|\(0)[1-9][0-9]{1,4}\)[ \-/]?))[0-9]{1,7}([ \-/]?[0-9]{1,5})?)$/.test(u);break;case"DK":u=n.trim(u);f=/^(\+45|0045|\(45\))?\s?[2-9](\s?\d){7}$/.test(u);break;case"ES":u=n.trim(u);f=/^(?:(?:(?:\+|00)34\D?))?(?:9|6)(?:\d\D?){8}$/.test(u);break;case"FR":u=n.trim(u);f=/^(?:(?:(?:\+|00)33[ ]?(?:\(0\)[ ]?)?)|0){1}[1-9]{1}([ .-]?)(?:\d{2}\1?){3}\d{2}$/.test(u);break;case"GB":u=n.trim(u);f=/^\(?(?:(?:0(?:0|11)\)?[\s-]?\(?|\+)44\)?[\s-]?\(?(?:0\)?[\s-]?\(?)?|0)(?:\d{2}\)?[\s-]?\d{4}[\s-]?\d{4}|\d{3}\)?[\s-]?\d{3}[\s-]?\d{3,4}|\d{4}\)?[\s-]?(?:\d{5}|\d{3}[\s-]?\d{3})|\d{5}\)?[\s-]?\d{4,5}|8(?:00[\s-]?11[\s-]?11|45[\s-]?46[\s-]?4\d))(?:(?:[\s-]?(?:x|ext\.?\s?|\#)\d+)?)$/.test(u);break;case"MA":u=n.trim(u);f=/^(?:(?:(?:\+|00)212[\s]?(?:[\s]?\(0\)[\s]?)?)|0){1}(?:5[\s.-]?[2-3]|6[\s.-]?[13-9]){1}[0-9]{1}(?:[\s.-]?\d{2}){3}$/.test(u);break;case"PK":u=n.trim(u);f=/^0?3[0-9]{2}[0-9]{7}$/.test(u);break;case"RO":f=/^(\+4|)?(07[0-8]{1}[0-9]{1}|02[0-9]{2}|03[0-9]{2}){1}?(\s|\.|\-)?([0-9]{3}(\s|\.|\-|)){2}$/g.test(u);break;case"RU":f=/^((8|\+7|007)[\-\.\/ ]?)?([\(\/\.]?\d{3}[\)\/\.]?[\-\.\/ ]?)?[\d\-\.\/ ]{7,10}$/g.test(u);break;case"SK":f=/^(((00)([- ]?)|\+)(420)([- ]?))?((\d{3})([- ]?)){2}(\d{3})$/.test(u);break;case"TH":f=/^0\(?([6|8-9]{2})*-([0-9]{3})*-([0-9]{4})$/.test(u);break;case"VE":u=n.trim(u);f=/^0(?:2(?:12|4[0-9]|5[1-9]|6[0-9]|7[0-8]|8[1-35-8]|9[1-5]|3[45789])|4(?:1[246]|2[46]))\d{7}$/.test(u);break;case"US":default:u=u.replace(/\D/g,"");f=/^(?:(1\-?)|(\+1 ?))?\(?(\d{3})[\)\-\.]?(\d{3})[\-\.]?(\d{4})$/.test(u)&&u.length===10}return{valid:f,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.phone.country,n.fn.bootstrapValidator.i18n.phone.countries[e])}}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.regexp=n.extend(n.fn.bootstrapValidator.i18n.regexp||{},{"default":"Please enter a value matching the pattern"});n.fn.bootstrapValidator.validators.regexp={html5Attributes:{message:"message",regexp:"regexp"},enableByHtml5:function(n){var t=n.attr("pattern");return t?{regexp:t}:!1},validate:function(n,t,i){var r=t.val(),u;return r===""?!0:(u="string"==typeof i.regexp?new RegExp(i.regexp):i.regexp,u.test(r))}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.remote=n.extend(n.fn.bootstrapValidator.i18n.remote||{},{"default":"Please enter a valid value"});n.fn.bootstrapValidator.validators.remote={html5Attributes:{message:"message",name:"name",type:"type",url:"url",data:"data",delay:"delay"},destroy:function(n,t){t.data("bv.remote.timer")&&(clearTimeout(t.data("bv.remote.timer")),t.removeData("bv.remote.timer"))},validate:function(t,i,r){function s(){var t=n.ajax({type:c,headers:l,url:e,dataType:"json",data:u});return t.then(function(n){n.valid=n.valid===!0||n.valid==="true";f.resolve(i,"remote",n)}),f.fail(function(){t.abort()}),f}var o=i.val(),f=new n.Deferred;if(o==="")return f.resolve(i,"remote",{valid:!0}),f;var h=i.attr("data-bv-field"),u=r.data||{},e=r.url,c=r.type||"GET",l=r.headers||{};return"function"==typeof u&&(u=u.call(this,t)),"string"==typeof u&&(u=JSON.parse(u)),"function"==typeof e&&(e=e.call(this,t)),u[r.name||h]=o,r.delay?(i.data("bv.remote.timer")&&clearTimeout(i.data("bv.remote.timer")),i.data("bv.remote.timer",setTimeout(s,r.delay)),f):s()}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.rtn=n.extend(n.fn.bootstrapValidator.i18n.rtn||{},{"default":"Please enter a valid RTN number"});n.fn.bootstrapValidator.validators.rtn={validate:function(n,t){var i=t.val(),u,r;if(i==="")return!0;if(!/^\d{9}$/.test(i))return!1;for(u=0,r=0;r<i.length;r+=3)u+=parseInt(i.charAt(r),10)*3+parseInt(i.charAt(r+1),10)*7+parseInt(i.charAt(r+2),10);return u!==0&&u%10==0}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.sedol=n.extend(n.fn.bootstrapValidator.i18n.sedol||{},{"default":"Please enter a valid SEDOL number"});n.fn.bootstrapValidator.validators.sedol={validate:function(n,t){var i=t.val(),r;if(i==="")return!0;if(i=i.toUpperCase(),!/^[0-9A-Z]{7}$/.test(i))return!1;var u=0,e=[1,3,1,7,3,9,1],f=i.length;for(r=0;r<f-1;r++)u+=e[r]*parseInt(i.charAt(r),36);return u=(10-u%10)%10,u+""===i.charAt(f-1)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.siren=n.extend(n.fn.bootstrapValidator.i18n.siren||{},{"default":"Please enter a valid SIREN number"});n.fn.bootstrapValidator.validators.siren={validate:function(t,i){var r=i.val();return r===""?!0:/^\d{9}$/.test(r)?n.fn.bootstrapValidator.helpers.luhn(r):!1}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.siret=n.extend(n.fn.bootstrapValidator.i18n.siret||{},{"default":"Please enter a valid SIRET number"});n.fn.bootstrapValidator.validators.siret={validate:function(n,t){var u=t.val(),f,e,i,r;if(u==="")return!0;for(f=0,e=u.length,r=0;r<e;r++)i=parseInt(u.charAt(r),10),r%2==0&&(i=i*2,i>9&&(i-=9)),f+=i;return f%10==0}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.step=n.extend(n.fn.bootstrapValidator.i18n.step||{},{"default":"Please enter a valid step of %s"});n.fn.bootstrapValidator.validators.step={html5Attributes:{message:"message",base:"baseValue",step:"step"},validate:function(t,i,r){var u=i.val();if(u==="")return!0;if(r=n.extend({},{baseValue:0,step:1},r),u=parseFloat(u),!n.isNumeric(u))return!1;var e=function(n,t){var i=Math.pow(10,t),r,u;return n=n*i,r=n>0|-(n<0),u=n%1==.5*r,u?(Math.floor(n)+(r>0))/i:Math.round(n)/i},o=function(n,t){if(t===0)return 1;var i=(n+"").split("."),r=(t+"").split("."),u=(i.length===1?0:i[1].length)+(r.length===1?0:r[1].length);return e(n-t*Math.floor(n/t),u)},f=o(u-r.baseValue,r.step);return{valid:f===0||f===r.step,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.step["default"],[r.step])}}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.stringCase=n.extend(n.fn.bootstrapValidator.i18n.stringCase||{},{"default":"Please enter only lowercase characters",upper:"Please enter only uppercase characters"});n.fn.bootstrapValidator.validators.stringCase={html5Attributes:{message:"message","case":"case"},validate:function(t,i,r){var u=i.val(),f;return u===""?!0:(f=(r["case"]||"lower").toLowerCase(),{valid:"upper"===f?u===u.toUpperCase():u===u.toLowerCase(),message:r.message||("upper"===f?n.fn.bootstrapValidator.i18n.stringCase.upper:n.fn.bootstrapValidator.i18n.stringCase["default"])})}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.stringLength=n.extend(n.fn.bootstrapValidator.i18n.stringLength||{},{"default":"Please enter a value with valid length",less:"Please enter less than %s characters",more:"Please enter more than %s characters",between:"Please enter value between %s and %s characters long"});n.fn.bootstrapValidator.validators.stringLength={html5Attributes:{message:"message",min:"min",max:"max",trim:"trim",utf8bytes:"utf8Bytes"},enableByHtml5:function(t){var i={},r=t.attr("maxlength"),u=t.attr("minlength");return r&&(i.max=parseInt(r,10)),u&&(i.min=parseInt(u,10)),n.isEmptyObject(i)?!1:i},validate:function(t,i,r){var e=i.val();if((r.trim===!0||r.trim==="true")&&(e=n.trim(e)),e==="")return!0;var u=n.isNumeric(r.min)?r.min:t.getDynamicOption(i,r.min),f=n.isNumeric(r.max)?r.max:t.getDynamicOption(i,r.max),c=function(n){for(var t,r=n.length,i=n.length-1;i>=0;i--)t=n.charCodeAt(i),t>127&&t<=2047?r++:t>2047&&t<=65535&&(r+=2),t>=56320&&t<=57343&&i--;return r},s=r.utf8Bytes?c(e):e.length,h=!0,o=r.message||n.fn.bootstrapValidator.i18n.stringLength["default"];(u&&s<parseInt(u,10)||f&&s>parseInt(f,10))&&(h=!1);switch(!0){case!!u&&!!f:o=n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.stringLength.between,[parseInt(u,10),parseInt(f,10)]);break;case!!u:o=n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.stringLength.more,parseInt(u,10));break;case!!f:o=n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.stringLength.less,parseInt(f,10))}return{valid:h,message:o}}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.uri=n.extend(n.fn.bootstrapValidator.i18n.uri||{},{"default":"Please enter a valid URI"});n.fn.bootstrapValidator.validators.uri={html5Attributes:{message:"message",allowlocal:"allowLocal",protocol:"protocol"},enableByHtml5:function(n){return"url"===n.attr("type")},validate:function(n,t,i){var r=t.val();if(r==="")return!0;var u=i.allowLocal===!0||i.allowLocal==="true",f=(i.protocol||"http, https, ftp").split(",").join("|").replace(/\s/g,""),e=new RegExp("^(?:(?:"+f+")://)(?:\\S+(?::\\S*)?@)?(?:"+(u?"":"(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})")+"(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))"+(u?"?":"")+")(?::\\d{2,5})?(?:/[^\\s]*)?$","i");return e.test(r)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.uuid=n.extend(n.fn.bootstrapValidator.i18n.uuid||{},{"default":"Please enter a valid UUID number",version:"Please enter a valid UUID version %s number"});n.fn.bootstrapValidator.validators.uuid={html5Attributes:{message:"message",version:"version"},validate:function(t,i,r){var e=i.val(),u,f;return e===""?!0:(u={"3":/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,"4":/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,"5":/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,all:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i},f=r.version?r.version+"":"all",{valid:null===u[f]?!0:u[f].test(e),message:r.version?n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.uuid.version,r.version):r.message||n.fn.bootstrapValidator.i18n.uuid["default"]})}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.vat=n.extend(n.fn.bootstrapValidator.i18n.vat||{},{"default":"Please enter a valid VAT number",countryNotSupported:"The country code %s is not supported",country:"Please enter a valid VAT number in %s",countries:{AT:"Austria",BE:"Belgium",BG:"Bulgaria",BR:"Brazil",CH:"Switzerland",CY:"Cyprus",CZ:"Czech Republic",DE:"Germany",DK:"Denmark",EE:"Estonia",ES:"Spain",FI:"Finland",FR:"France",GB:"United Kingdom",GR:"Greek",EL:"Greek",HU:"Hungary",HR:"Croatia",IE:"Ireland",IS:"Iceland",IT:"Italy",LT:"Lithuania",LU:"Luxembourg",LV:"Latvia",MT:"Malta",NL:"Netherlands",NO:"Norway",PL:"Poland",PT:"Portugal",RO:"Romania",RU:"Russia",RS:"Serbia",SE:"Sweden",SI:"Slovenia",SK:"Slovakia",VE:"Venezuela",ZA:"South Africa"}});n.fn.bootstrapValidator.validators.vat={html5Attributes:{message:"message",country:"country"},COUNTRY_CODES:["AT","BE","BG","BR","CH","CY","CZ","DE","DK","EE","EL","ES","FI","FR","GB","GR","HR","HU","IE","IS","IT","LT","LU","LV","MT","NL","NO","PL","PT","RO","RU","RS","SE","SK","SI","VE","ZA"],validate:function(t,i,r){var f=i.val(),u,e;return f===""?!0:(u=r.country,u?(typeof u!="string"||n.inArray(u.toUpperCase(),this.COUNTRY_CODES)===-1)&&(u=t.getDynamicOption(i,u)):u=f.substr(0,2),n.inArray(u,this.COUNTRY_CODES)===-1)?{valid:!1,message:n.fn.bootstrapValidator.helpers.format(n.fn.bootstrapValidator.i18n.vat.countryNotSupported,u)}:(e=["_",u.toLowerCase()].join(""),this[e](f)?!0:{valid:!1,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.vat.country,n.fn.bootstrapValidator.i18n.vat.countries[u.toUpperCase()])})},_at:function(n){var r;if(/^ATU[0-9]{8}$/.test(n)&&(n=n.substr(2)),!/^U[0-9]{8}$/.test(n))return!1;n=n.substr(1);var t=0,u=[1,2,1,2,1,2,1],i=0;for(r=0;r<7;r++)i=parseInt(n.charAt(r),10)*u[r],i>9&&(i=Math.floor(i/10)+i%10),t+=i;return t=10-(t+4)%10,t===10&&(t=0),t+""===n.substr(7,1)},_be:function(n){if((/^BE[0]{0,1}[0-9]{9}$/.test(n)&&(n=n.substr(2)),!/^[0]{0,1}[0-9]{9}$/.test(n))||(n.length===9&&(n="0"+n),n.substr(1,1)==="0"))return!1;var t=parseInt(n.substr(0,8),10)+parseInt(n.substr(8,2),10);return t%97==0},_bg:function(t){var r,i;if(/^BG[0-9]{9,10}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9,10}$/.test(t))return!1;if(r=0,i=0,t.length===9){for(i=0;i<8;i++)r+=parseInt(t.charAt(i),10)*(i+1);if(r=r%11,r===10)for(r=0,i=0;i<8;i++)r+=parseInt(t.charAt(i),10)*(i+3);return r=r%10,r+""===t.substr(8)}if(t.length===10){var u=function(t){var f=parseInt(t.substr(0,2),10)+1900,i=parseInt(t.substr(2,2),10),o=parseInt(t.substr(4,2),10),r,e,u;if(i>40?(f+=100,i-=40):i>20&&(f-=100,i-=20),!n.fn.bootstrapValidator.helpers.date(f,i,o))return!1;for(r=0,e=[2,4,8,5,10,9,7,3,6],u=0;u<9;u++)r+=parseInt(t.charAt(u),10)*e[u];return r=r%11%10,r+""===t.substr(9,1)},f=function(n){for(var t=0,r=[21,19,17,13,11,9,7,3,1],i=0;i<9;i++)t+=parseInt(n.charAt(i),10)*r[i];return t=t%10,t+""===n.substr(9,1)},e=function(n){for(var t=0,r=[4,3,2,7,6,5,4,3,2],i=0;i<9;i++)t+=parseInt(n.charAt(i),10)*r[i];return(t=11-t%11,t===10)?!1:(t===11&&(t=0),t+""===n.substr(9,1))};return u(t)||f(t)||e(t)}return!1},_br:function(n){var t,r,e;if(n==="")return!0;if((t=n.replace(/[^\d]+/g,""),t===""||t.length!==14)||t==="00000000000000"||t==="11111111111111"||t==="22222222222222"||t==="33333333333333"||t==="44444444444444"||t==="55555555555555"||t==="66666666666666"||t==="77777777777777"||t==="88888888888888"||t==="99999999999999")return!1;var i=t.length-2,o=t.substring(0,i),s=t.substring(i),u=0,f=i-7;for(r=i;r>=1;r--)u+=parseInt(o.charAt(i-r),10)*f--,f<2&&(f=9);if(e=u%11<2?0:11-u%11,e!==parseInt(s.charAt(0),10))return!1;for(i=i+1,o=t.substring(0,i),u=0,f=i-7,r=i;r>=1;r--)u+=parseInt(o.charAt(i-r),10)*f--,f<2&&(f=9);return e=u%11<2?0:11-u%11,e===parseInt(s.charAt(1),10)},_ch:function(n){var t,r,i;if(/^CHE[0-9]{9}(MWST)?$/.test(n)&&(n=n.substr(2)),!/^E[0-9]{9}(MWST)?$/.test(n))return!1;for(n=n.substr(1),t=0,r=[5,4,3,2,7,6,5,4],i=0;i<8;i++)t+=parseInt(n.charAt(i),10)*r[i];return(t=11-t%11,t===10)?!1:(t===11&&(t=0),t+""===n.substr(8,1))},_cy:function(n){var t,u,i,r;if((/^CY[0-5|9]{1}[0-9]{7}[A-Z]{1}$/.test(n)&&(n=n.substr(2)),!/^[0-5|9]{1}[0-9]{7}[A-Z]{1}$/.test(n))||n.substr(0,2)==="12")return!1;for(t=0,u={"0":1,"1":0,"2":5,"3":7,"4":9,"5":13,"6":15,"7":17,"8":19,"9":21},i=0;i<8;i++)r=parseInt(n.charAt(i),10),i%2==0&&(r=u[r+""]),t+=r;return t="ABCDEFGHIJKLMNOPQRSTUVWXYZ"[t%26],t+""===n.substr(8,1)},_cz:function(t){var i,r,f;if(/^CZ[0-9]{8,10}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{8,10}$/.test(t))return!1;if(i=0,r=0,t.length===8){if(t.charAt(0)+""=="9")return!1;for(i=0,r=0;r<7;r++)i+=parseInt(t.charAt(r),10)*(8-r);return i=11-i%11,i===10&&(i=0),i===11&&(i=1),i+""===t.substr(7,1)}if(t.length===9&&t.charAt(0)+""=="6"){for(i=0,r=0;r<7;r++)i+=parseInt(t.charAt(r+1),10)*(8-r);return i=11-i%11,i===10&&(i=0),i===11&&(i=1),i=[8,7,6,5,4,3,2,1,0,9,10][i-1],i+""===t.substr(8,1)}if(t.length===9||t.length===10){var u=1900+parseInt(t.substr(0,2),10),e=parseInt(t.substr(2,2),10)%50%20,o=parseInt(t.substr(4,2),10);if(t.length===9){if(u>=1980&&(u-=100),u>1953)return!1}else u<1954&&(u+=100);return n.fn.bootstrapValidator.helpers.date(u,e,o)?t.length===10?(f=parseInt(t.substr(0,9),10)%11,u<1985&&(f=f%10),f+""===t.substr(9,1)):!0:!1}return!1},_de:function(t){return(/^DE[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9}$/.test(t))?!1:n.fn.bootstrapValidator.helpers.mod11And10(t)},_dk:function(n){var i,r,t;if(/^DK[0-9]{8}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{8}$/.test(n))return!1;for(i=0,r=[2,7,6,5,4,3,2,1],t=0;t<8;t++)i+=parseInt(n.charAt(t),10)*r[t];return i%11==0},_ee:function(n){var i,r,t;if(/^EE[0-9]{9}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{9}$/.test(n))return!1;for(i=0,r=[3,7,1,3,7,1,3,7,1],t=0;t<9;t++)i+=parseInt(n.charAt(t),10)*r[t];return i%10==0},_es:function(n){if(/^ES[0-9A-Z][0-9]{7}[0-9A-Z]$/.test(n)&&(n=n.substr(2)),!/^[0-9A-Z][0-9]{7}[0-9A-Z]$/.test(n))return!1;var i=function(n){var t=parseInt(n.substr(0,8),10);return t="TRWAGMYFPDXBNJZSQVHLCKE"[t%23],t+""===n.substr(8,1)},r=function(n){var t=["XYZ".indexOf(n.charAt(0)),n.substr(1)].join("");return t=parseInt(t,10),t="TRWAGMYFPDXBNJZSQVHLCKE"[t%23],t+""===n.substr(8,1)},u=function(n){var f=n.charAt(0),u,r;if("KLM".indexOf(f)!==-1)return u=parseInt(n.substr(1,8),10),u="TRWAGMYFPDXBNJZSQVHLCKE"[u%23],u+""===n.substr(8,1);if("ABCDEFGHJNPQRSUVW".indexOf(f)!==-1){var i=0,e=[2,1,2,1,2,1,2],t=0;for(r=0;r<7;r++)t=parseInt(n.charAt(r+1),10)*e[r],t>9&&(t=Math.floor(t/10)+t%10),i+=t;return i=10-i%10,i+""===n.substr(8,1)||"JABCDEFGHI"[i]===n.substr(8,1)}return!1},t=n.charAt(0);return/^[0-9]$/.test(t)?i(n):/^[XYZ]$/.test(t)?r(n):u(n)},_fi:function(n){var i,r,t;if(/^FI[0-9]{8}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{8}$/.test(n))return!1;for(i=0,r=[7,9,10,5,8,4,2,1],t=0;t<8;t++)i+=parseInt(n.charAt(t),10)*r[t];return i%11==0},_fr:function(t){if((/^FR[0-9A-Z]{2}[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9A-Z]{2}[0-9]{9}$/.test(t))||!n.fn.bootstrapValidator.helpers.luhn(t.substr(2)))return!1;if(/^[0-9]{2}$/.test(t.substr(0,2)))return t.substr(0,2)===parseInt(t.substr(2)+"12",10)%97+"";var i="0123456789ABCDEFGHJKLMNPQRSTUVWXYZ",r;return r=/^[0-9]{1}$/.test(t.charAt(0))?i.indexOf(t.charAt(0))*24+i.indexOf(t.charAt(1))-10:i.indexOf(t.charAt(0))*34+i.indexOf(t.charAt(1))-100,(parseInt(t.substr(2),10)+1+Math.floor(r/11))%11==r%11},_gb:function(n){var i,u,f,t,e,r;if((/^GB[0-9]{9}$/.test(n)||/^GB[0-9]{12}$/.test(n)||/^GBGD[0-9]{3}$/.test(n)||/^GBHA[0-9]{3}$/.test(n)||/^GB(GD|HA)8888[0-9]{5}$/.test(n))&&(n=n.substr(2)),!/^[0-9]{9}$/.test(n)&&!/^[0-9]{12}$/.test(n)&&!/^GD[0-9]{3}$/.test(n)&&!/^HA[0-9]{3}$/.test(n)&&!/^(GD|HA)8888[0-9]{5}$/.test(n))return!1;if(i=n.length,i===5)return u=n.substr(0,2),f=parseInt(n.substr(2),10),"GD"===u&&f<500||"HA"===u&&f>=500;if(i===11&&("GD8888"===n.substr(0,6)||"HA8888"===n.substr(0,6)))return"GD"===n.substr(0,2)&&parseInt(n.substr(6,3),10)>=500||"HA"===n.substr(0,2)&&parseInt(n.substr(6,3),10)<500?!1:parseInt(n.substr(6,3),10)%97===parseInt(n.substr(9,2),10);if(i===9||i===12){for(t=0,e=[8,7,6,5,4,3,2,10,1],r=0;r<9;r++)t+=parseInt(n.charAt(r),10)*e[r];return t=t%97,parseInt(n.substr(0,3),10)>=100?t===0||t===42||t===55:t===0}return!0},_gr:function(n){var t,r,i;if(/^(GR|EL)[0-9]{9}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{9}$/.test(n))return!1;for(n.length===8&&(n="0"+n),t=0,r=[256,128,64,32,16,8,4,2],i=0;i<8;i++)t+=parseInt(n.charAt(i),10)*r[i];return t=t%11%10,t+""===n.substr(8,1)},_el:function(n){return this._gr(n)},_hu:function(n){var i,r,t;if(/^HU[0-9]{8}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{8}$/.test(n))return!1;for(i=0,r=[9,7,3,1,9,7,3,1],t=0;t<8;t++)i+=parseInt(n.charAt(t),10)*r[t];return i%10==0},_hr:function(t){return(/^HR[0-9]{11}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{11}$/.test(t))?!1:n.fn.bootstrapValidator.helpers.mod11And10(t)},_ie:function(n){if(/^IE[0-9]{1}[0-9A-Z\*\+]{1}[0-9]{5}[A-Z]{1,2}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{1}[0-9A-Z\*\+]{1}[0-9]{5}[A-Z]{1,2}$/.test(n))return!1;var t=function(n){for(var r,i,t;n.length<7;)n="0"+n;for(r="WABCDEFGHIJKLMNOPQRSTUV",i=0,t=0;t<7;t++)i+=parseInt(n.charAt(t),10)*(8-t);return i+=9*r.indexOf(n.substr(7)),r[i%23]};return/^[0-9]+$/.test(n.substr(0,7))?n.charAt(7)===t(n.substr(0,7)+n.substr(8)+""):"ABCDEFGHIJKLMNOPQRSTUVWXYZ+*".indexOf(n.charAt(1))!==-1?n.charAt(7)===t(n.substr(2,5)+n.substr(0,1)+""):!0},_is:function(n){return/^IS[0-9]{5,6}$/.test(n)&&(n=n.substr(2)),/^[0-9]{5,6}$/.test(n)},_it:function(t){if((/^IT[0-9]{11}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{11}$/.test(t))||parseInt(t.substr(0,7),10)===0)return!1;var i=parseInt(t.substr(7,3),10);return i<1||i>201&&i!==999&&i!==888?!1:n.fn.bootstrapValidator.helpers.luhn(t)},_lt:function(n){var u,i,t,r;if(/^LT([0-9]{7}1[0-9]{1}|[0-9]{10}1[0-9]{1})$/.test(n)&&(n=n.substr(2)),!/^([0-9]{7}1[0-9]{1}|[0-9]{10}1[0-9]{1})$/.test(n))return!1;for(u=n.length,i=0,t=0;t<u-1;t++)i+=parseInt(n.charAt(t),10)*(1+t%9);if(r=i%11,r===10)for(i=0,t=0;t<u-1;t++)i+=parseInt(n.charAt(t),10)*(1+(t+2)%9);return r=r%11%10,r+""===n.charAt(u-1)},_lu:function(n){return(/^LU[0-9]{8}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{8}$/.test(n))?!1:parseInt(n.substr(0,6),10)%89+""===n.substr(6,2)},_lv:function(t){if(/^LV[0-9]{11}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{11}$/.test(t))return!1;var o=parseInt(t.charAt(0),10),i=0,u=[],r,f=t.length;if(o>3){for(i=0,u=[9,1,4,8,3,10,2,5,7,6,1],r=0;r<f;r++)i+=parseInt(t.charAt(r),10)*u[r];return i=i%11,i===3}var s=parseInt(t.substr(0,2),10),h=parseInt(t.substr(2,2),10),e=parseInt(t.substr(4,2),10);if(e=e+1800+parseInt(t.charAt(6),10)*100,!n.fn.bootstrapValidator.helpers.date(e,h,s))return!1;for(i=0,u=[10,5,8,4,2,1,6,3,7,9],r=0;r<f-1;r++)i+=parseInt(t.charAt(r),10)*u[r];return i=(i+1)%11%10,i+""===t.charAt(f-1)},_mt:function(n){var i,r,t;if(/^MT[0-9]{8}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{8}$/.test(n))return!1;for(i=0,r=[3,4,6,7,8,9,10,1],t=0;t<8;t++)i+=parseInt(n.charAt(t),10)*r[t];return i%37==0},_nl:function(n){var t,r,i;if(/^NL[0-9]{9}B[0-9]{2}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{9}B[0-9]{2}$/.test(n))return!1;for(t=0,r=[9,8,7,6,5,4,3,2],i=0;i<8;i++)t+=parseInt(n.charAt(i),10)*r[i];return t=t%11,t>9&&(t=0),t+""===n.substr(8,1)},_no:function(n){var t,r,i;if(/^NO[0-9]{9}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{9}$/.test(n))return!1;for(t=0,r=[3,2,7,6,5,4,3,2],i=0;i<8;i++)t+=parseInt(n.charAt(i),10)*r[i];return t=11-t%11,t===11&&(t=0),t+""===n.substr(8,1)},_pl:function(n){var i,r,t;if(/^PL[0-9]{10}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{10}$/.test(n))return!1;for(i=0,r=[6,5,7,2,3,4,5,6,7,-1],t=0;t<10;t++)i+=parseInt(n.charAt(t),10)*r[t];return i%11==0},_pt:function(n){var t,r,i;if(/^PT[0-9]{9}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{9}$/.test(n))return!1;for(t=0,r=[9,8,7,6,5,4,3,2],i=0;i<8;i++)t+=parseInt(n.charAt(i),10)*r[i];return t=11-t%11,t>9&&(t=0),t+""===n.substr(8,1)},_ro:function(n){var t;if(/^RO[1-9][0-9]{1,9}$/.test(n)&&(n=n.substr(2)),!/^[1-9][0-9]{1,9}$/.test(n))return!1;var r=n.length,u=[7,5,3,2,1,7,5,3,2].slice(10-r),i=0;for(t=0;t<r-1;t++)i+=parseInt(n.charAt(t),10)*u[t];return i=10*i%11%10,i+""===n.substr(r-1,1)},_ru:function(n){var t,i,f;if(/^RU([0-9]{10}|[0-9]{12})$/.test(n)&&(n=n.substr(2)),!/^([0-9]{10}|[0-9]{12})$/.test(n))return!1;if(t=0,n.length===10){for(i=0,f=[2,4,10,3,5,9,4,6,8,0],t=0;t<10;t++)i+=parseInt(n.charAt(t),10)*f[t];return i=i%11,i>9&&(i=i%10),i+""===n.substr(9,1)}if(n.length===12){var r=0,e=[7,2,4,10,3,5,9,4,6,8,0],u=0,o=[3,7,2,4,10,3,5,9,4,6,8,0];for(t=0;t<11;t++)r+=parseInt(n.charAt(t),10)*e[t],u+=parseInt(n.charAt(t),10)*o[t];return r=r%11,r>9&&(r=r%10),u=u%11,u>9&&(u=u%10),r+""===n.substr(10,1)&&u+""===n.substr(11,1)}return!1},_rs:function(n){var i,t,r;if(/^RS[0-9]{9}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{9}$/.test(n))return!1;for(i=10,t=0,r=0;r<8;r++)t=(parseInt(n.charAt(r),10)+i)%10,t===0&&(t=10),i=2*t%11;return(i+parseInt(n.substr(8,1),10))%10==1},_se:function(t){return(/^SE[0-9]{10}01$/.test(t)&&(t=t.substr(2)),!/^[0-9]{10}01$/.test(t))?!1:(t=t.substr(0,10),n.fn.bootstrapValidator.helpers.luhn(t))},_si:function(n){var t,r,i;if(/^SI[0-9]{8}$/.test(n)&&(n=n.substr(2)),!/^[0-9]{8}$/.test(n))return!1;for(t=0,r=[8,7,6,5,4,3,2],i=0;i<7;i++)t+=parseInt(n.charAt(i),10)*r[i];return t=11-t%11,t===10&&(t=0),t+""===n.substr(7,1)},_sk:function(n){return(/^SK[1-9][0-9][(2-4)|(6-9)][0-9]{7}$/.test(n)&&(n=n.substr(2)),!/^[1-9][0-9][(2-4)|(6-9)][0-9]{7}$/.test(n))?!1:parseInt(n,10)%11==0},_ve:function(n){var i;if(/^VE[VEJPG][0-9]{9}$/.test(n)&&(n=n.substr(2)),!/^[VEJPG][0-9]{9}$/.test(n))return!1;var t={V:4,E:8,J:12,P:16,G:20}[n.charAt(0)],r=[3,2,7,6,5,4,3,2];for(i=0;i<8;i++)t+=parseInt(n.charAt(i+1),10)*r[i];return t=11-t%11,(t===11||t===10)&&(t=0),t+""===n.substr(9,1)},_za:function(n){return/^ZA4[0-9]{9}$/.test(n)&&(n=n.substr(2)),/^4[0-9]{9}$/.test(n)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.vin=n.extend(n.fn.bootstrapValidator.i18n.vin||{},{"default":"Please enter a valid VIN number"});n.fn.bootstrapValidator.validators.vin={validate:function(n,t){var i=t.val(),r,u;if(i==="")return!0;if(!/^[a-hj-npr-z0-9]{8}[0-9xX][a-hj-npr-z0-9]{8}$/i.test(i))return!1;i=i.toUpperCase();var e={A:1,B:2,C:3,D:4,E:5,F:6,G:7,H:8,J:1,K:2,L:3,M:4,N:5,P:7,R:9,S:2,T:3,U:4,V:5,W:6,X:7,Y:8,Z:9,"1":1,"2":2,"3":3,"4":4,"5":5,"6":6,"7":7,"8":8,"9":9,"0":0},o=[8,7,6,5,4,3,2,10,0,9,8,7,6,5,4,3,2],f=0,s=i.length;for(r=0;r<s;r++)f+=e[i.charAt(r)+""]*o[r];return u=f%11,u===10&&(u="X"),u+""===i.charAt(8)}}}(window.jQuery),function(n){n.fn.bootstrapValidator.i18n.zipCode=n.extend(n.fn.bootstrapValidator.i18n.zipCode||{},{"default":"Please enter a valid postal code",countryNotSupported:"The country code %s is not supported",country:"Please enter a valid postal code in %s",countries:{AT:"Austria",BR:"Brazil",CA:"Canada",CH:"Switzerland",CZ:"Czech Republic",DE:"Germany",DK:"Denmark",FR:"France",GB:"United Kingdom",IE:"Ireland",IT:"Italy",MA:"Morocco",NL:"Netherlands",PT:"Portugal",RO:"Romania",RU:"Russia",SE:"Sweden",SG:"Singapore",SK:"Slovakia",US:"USA"}});n.fn.bootstrapValidator.validators.zipCode={html5Attributes:{message:"message",country:"country"},COUNTRY_CODES:["AT","BR","CA","CH","CZ","DE","DK","FR","GB","IE","IT","MA","NL","PT","RO","RU","SE","SG","SK","US"],validate:function(t,i,r){var f=i.val(),e,u;if(f===""||!r.country)return!0;if(e=r.country,(typeof e!="string"||n.inArray(e,this.COUNTRY_CODES)===-1)&&(e=t.getDynamicOption(i,e)),!e||n.inArray(e.toUpperCase(),this.COUNTRY_CODES)===-1)return{valid:!1,message:n.fn.bootstrapValidator.helpers.format(n.fn.bootstrapValidator.i18n.zipCode.countryNotSupported,e)};u=!1;e=e.toUpperCase();switch(e){case"AT":u=/^([1-9]{1})(\d{3})$/.test(f);break;case"BR":u=/^(\d{2})([\.]?)(\d{3})([\-]?)(\d{3})$/.test(f);break;case"CA":u=/^(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|X|Y){1}[0-9]{1}(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|W|X|Y|Z){1}\s?[0-9]{1}(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|W|X|Y|Z){1}[0-9]{1}$/i.test(f);break;case"CH":u=/^([1-9]{1})(\d{3})$/.test(f);break;case"CZ":u=/^(\d{3})([ ]?)(\d{2})$/.test(f);break;case"DE":u=/^(?!01000|99999)(0[1-9]\d{3}|[1-9]\d{4})$/.test(f);break;case"DK":u=/^(DK(-|\s)?)?\d{4}$/i.test(f);break;case"FR":u=/^[0-9]{5}$/i.test(f);break;case"GB":u=this._gb(f);break;case"IE":u=/^(D6W|[ACDEFHKNPRTVWXY]\d{2})\s[0-9ACDEFHKNPRTVWXY]{4}$/.test(f);break;case"IT":u=/^(I-|IT-)?\d{5}$/i.test(f);break;case"MA":u=/^[1-9][0-9]{4}$/i.test(f);break;case"NL":u=/^[1-9][0-9]{3} ?(?!sa|sd|ss)[a-z]{2}$/i.test(f);break;case"PT":u=/^[1-9]\d{3}-\d{3}$/.test(f);break;case"RO":u=/^(0[1-8]{1}|[1-9]{1}[0-5]{1})?[0-9]{4}$/i.test(f);break;case"RU":u=/^[0-9]{6}$/i.test(f);break;case"SE":u=/^(S-)?\d{3}\s?\d{2}$/i.test(f);break;case"SG":u=/^([0][1-9]|[1-6][0-9]|[7]([0-3]|[5-9])|[8][0-2])(\d{4})$/i.test(f);break;case"SK":u=/^(\d{3})([ ]?)(\d{2})$/.test(f);break;case"US":default:u=/^\d{4,5}([\-]?\d{4})?$/.test(f)}return{valid:u,message:n.fn.bootstrapValidator.helpers.format(r.message||n.fn.bootstrapValidator.i18n.zipCode.country,n.fn.bootstrapValidator.i18n.zipCode.countries[e])}},_gb:function(n){for(var t="[ABCDEFGHIJKLMNOPRSTUWYZ]",u="[ABCDEFGHKLMNOPQRSTUVWXY]",i="[ABDEFGHJLNPQRSTUWXYZ]",f=[new RegExp("^("+t+"{1}"+u+"?[0-9]{1,2})(\\s*)([0-9]{1}"+i+"{2})$","i"),new RegExp("^("+t+"{1}[0-9]{1}[ABCDEFGHJKPMNRSTUVWXY]{1})(\\s*)([0-9]{1}"+i+"{2})$","i"),new RegExp("^("+t+"{1}"+u+"{1}?[0-9]{1}[ABEHMNPRVWXY]{1})(\\s*)([0-9]{1}"+i+"{2})$","i"),new RegExp("^(BF1)(\\s*)([0-6]{1}[ABDEFGHJLNPQRST]{1}[ABDEFGHJLNPQRSTUWZYZ]{1})$","i"),/^(GIR)(\s*)(0AA)$/i,/^(BFPO)(\s*)([0-9]{1,4})$/i,/^(BFPO)(\s*)(c\/o\s*[0-9]{1,3})$/i,/^([A-Z]{4})(\s*)(1ZZ)$/i,/^(AI-2640)$/i],r=0;r<f.length;r++)if(f[r].test(n))return!0;return!1}}}(window.jQuery);