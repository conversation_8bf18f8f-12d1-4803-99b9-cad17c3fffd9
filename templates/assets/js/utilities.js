/**
 * Created by <PERSON><PERSON><PERSON> on 31/03/16.
 */
function animateCounterClass(className) {
    $('.'+className).each(function () {
        var $this = $(this);
        jQuery({ Counter: 0 }).animate({ Counter: $this.text() }, {
            duration: 1000,
            easing: 'swing',
            step: function () {
                $this.text(Math.ceil(this.Counter));
            }
        });
    });
}