{"version": 3, "file": "dist/bootstrap-tagsinput.min.js", "sources": ["dist/bootstrap-tagsinput.js"], "names": ["$", "TagsInput", "element", "options", "this", "itemsArray", "$element", "hide", "isSelect", "tagName", "multiple", "hasAttribute", "objectItems", "itemValue", "placeholderText", "attr", "inputSize", "Math", "max", "length", "$container", "$input", "appendTo", "after", "build", "makeOptionItemFunction", "key", "propertyName", "item", "makeOptionFunction", "value", "htmlEncode", "htmlEncodeContainer", "text", "html", "doGetCaretPosition", "oField", "iCaretPos", "document", "selection", "focus", "oSel", "createRange", "moveStart", "selectionStart", "defaultOptions", "tagClass", "toString", "itemText", "freeInput", "maxTags", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "onTagExists", "$tag", "fadeIn", "prototype", "constructor", "add", "dontPush<PERSON>al", "self", "match", "remove", "items", "split", "i", "pushVal", "existing", "grep", "$existingTag", "filter", "data", "push", "findInputWrapper", "before", "escape", "$option", "append", "addClass", "trigger", "Event", "other", "splice", "inArray", "removeClass", "removeAll", "pop", "isEnabled", "enable", "refresh", "each", "contents", "nodeType", "nodeValue", "option", "val", "map", "extend", "typeahead", "source", "fn", "query", "process", "processItems", "texts", "isFunction", "success", "when", "then", "updater", "matcher", "toLowerCase", "indexOf", "trim", "sorter", "sort", "highlighter", "regex", "RegExp", "replace", "on", "proxy", "event", "target", "$inputWrapper", "which", "prev", "next", "$prevTag", "$nextTag", "preventDefault", "closest", "destroy", "off", "removeData", "show", "input", "elt", "container", "parentNode", "tagsinput", "arg1", "arg2", "results", "retVal", "<PERSON><PERSON><PERSON><PERSON>", "window", "j<PERSON><PERSON><PERSON>"], "mappings": "CAAA,SAAWA,GACT,YAuBA,SAASC,GAAUC,EAASC,GAC1BC,KAAKC,cAELD,KAAKE,SAAWN,EAAEE,GAClBE,KAAKE,SAASC,OAEdH,KAAKI,SAAgC,WAApBN,EAAQO,QACzBL,KAAKM,SAAYN,KAAKI,UAAYN,EAAQS,aAAa,YACvDP,KAAKQ,YAAcT,GAAWA,EAAQU,UACtCT,KAAKU,gBAAkBZ,EAAQS,aAAa,eAAiBP,KAAKE,SAASS,KAAK,eAAiB,GACjGX,KAAKY,UAAYC,KAAKC,IAAI,EAAGd,KAAKU,gBAAgBK,QAElDf,KAAKgB,WAAapB,EAAE,2CACpBI,KAAKiB,OAASrB,EAAE,gBAAkBI,KAAKY,UAAY,8BAAgCZ,KAAKU,gBAAkB,OAAOQ,SAASlB,KAAKgB,YAE/HhB,KAAKE,SAASiB,MAAMnB,KAAKgB,YAEzBhB,KAAKoB,MAAMrB,GA6Zb,QAASsB,GAAuBtB,EAASuB,GACvC,GAA4B,kBAAjBvB,GAAQuB,GAAqB,CACtC,GAAIC,GAAexB,EAAQuB,EAC3BvB,GAAQuB,GAAO,SAASE,GAAQ,MAAOA,GAAKD,KAGhD,QAASE,GAAmB1B,EAASuB,GACnC,GAA4B,kBAAjBvB,GAAQuB,GAAqB,CACtC,GAAII,GAAQ3B,EAAQuB,EACpBvB,GAAQuB,GAAO,WAAa,MAAOI,KAOvC,QAASC,GAAWD,GAClB,MAAIA,GACKE,EAAoBC,KAAKH,GAAOI,OAEhC,GAQX,QAASC,GAAmBC,GAC1B,GAAIC,GAAY,CAChB,IAAIC,SAASC,UAAW,CACtBH,EAAOI,OACP,IAAIC,GAAOH,SAASC,UAAUG,aAC9BD,GAAKE,UAAW,aAAcP,EAAON,MAAMX,QAC3CkB,EAAYI,EAAKR,KAAKd,YACbiB,EAAOQ,gBAA2C,KAAzBR,EAAOQ,kBACzCP,EAAYD,EAAOQ,eAErB,OAAO,GAzeT,GAAIC,IACFC,SAAU,WACR,MAAO,oBAETjC,UAAW,SAASe,GAClB,MAAOA,GAAOA,EAAKmB,WAAanB,GAElCoB,SAAU,SAASpB,GACjB,MAAOxB,MAAKS,UAAUe,IAExBqB,WAAW,EACXC,QAASC,OACTC,aAAc,IACdC,YAAa,SAASzB,EAAM0B,GAC1BA,EAAK/C,OAAOgD,UA2BhBtD,GAAUuD,WACRC,YAAaxD,EAMbyD,IAAK,SAAS9B,EAAM+B,GAClB,GAAIC,GAAOxD,IAEX,MAAIwD,EAAKzD,QAAQ+C,SAAWU,EAAKvD,WAAWc,QAAUyC,EAAKzD,QAAQ+C,SAI/DtB,KAAS,IAAUA,GAAvB,CAIA,GAAoB,gBAATA,KAAsBgC,EAAKhD,YACpC,KAAK,oDAGP,KAAIgB,EAAKmB,WAAWc,MAAM,SAA1B,CAOA,GAHID,EAAKpD,WAAaoD,EAAKlD,UAAYkD,EAAKvD,WAAWc,OAAS,GAC9DyC,EAAKE,OAAOF,EAAKvD,WAAW,IAEV,gBAATuB,IAAkD,UAA7BxB,KAAKE,SAAS,GAAGG,QAAqB,CACpE,GAAIsD,GAAQnC,EAAKoC,MAAM,IACvB,IAAID,EAAM5C,OAAS,EAAG,CACpB,IAAK,GAAI8C,GAAI,EAAGA,EAAIF,EAAM5C,OAAQ8C,IAChC7D,KAAKsD,IAAIK,EAAME,IAAI,EAKrB,OAFKN,IACHC,EAAKM,UACP,QAIJ,GAAIrD,GAAY+C,EAAKzD,QAAQU,UAAUe,GACnCoB,EAAWY,EAAKzD,QAAQ6C,SAASpB,GACjCkB,EAAWc,EAAKzD,QAAQ2C,SAASlB,GAGjCuC,EAAWnE,EAAEoE,KAAKR,EAAKvD,WAAY,SAASuB,GAAQ,MAAOgC,GAAKzD,QAAQU,UAAUe,KAAUf,IAAe,EAC/G,IAAIsD,GAEF,GAAIP,EAAKzD,QAAQkD,YAAa,CAC5B,GAAIgB,GAAerE,EAAE,OAAQ4D,EAAKxC,YAAYkD,OAAO,WAAa,MAAOtE,GAAEI,MAAMmE,KAAK,UAAYJ,GAClGP,GAAKzD,QAAQkD,YAAYzB,EAAMyC,QAJnC,CAUAT,EAAKvD,WAAWmE,KAAK5C,EAGrB,IAAI0B,GAAOtD,EAAE,oBAAsB+B,EAAWe,GAAY,KAAOf,EAAWiB,GAAY,0CAMxF,IALAM,EAAKiB,KAAK,OAAQ3C,GAClBgC,EAAKa,mBAAmBC,OAAOpB,GAC/BA,EAAK/B,MAAM,KAGPqC,EAAKpD,WAAaR,EAAE,iBAAmB2E,OAAO9D,GAAa,KAAK+C,EAAKtD,UAAU,GAAI,CACrF,GAAIsE,GAAU5E,EAAE,oBAAsB+B,EAAWiB,GAAY,YAC7D4B,GAAQL,KAAK,OAAQ3C,GACrBgD,EAAQ7D,KAAK,QAASF,GACtB+C,EAAKtD,SAASuE,OAAOD,GAGlBjB,GACHC,EAAKM,UAGHN,EAAKzD,QAAQ+C,UAAYU,EAAKvD,WAAWc,QAC3CyC,EAAKxC,WAAW0D,SAAS,2BAE3BlB,EAAKtD,SAASyE,QAAQ/E,EAAEgF,MAAM,aAAepD,KAAMA,SAOrDkC,OAAQ,SAASlC,EAAM+B,GACrB,GAAIC,GAAOxD,IAEPwD,GAAKhD,cAELgB,EADkB,gBAATA,GACF5B,EAAEoE,KAAKR,EAAKvD,WAAY,SAAS4E,GAAS,MAAOrB,GAAKzD,QAAQU,UAAUoE,IAAWrB,EAAKzD,QAAQU,UAAUe,KAAW,GAErH5B,EAAEoE,KAAKR,EAAKvD,WAAY,SAAS4E,GAAS,MAAOrB,GAAKzD,QAAQU,UAAUoE,IAAWrD,IAAU,IAGpGA,IACF5B,EAAE,OAAQ4D,EAAKxC,YAAYkD,OAAO,WAAa,MAAOtE,GAAEI,MAAMmE,KAAK,UAAY3C,IAASkC,SACxF9D,EAAE,SAAU4D,EAAKtD,UAAUgE,OAAO,WAAa,MAAOtE,GAAEI,MAAMmE,KAAK,UAAY3C,IAASkC,SACxFF,EAAKvD,WAAW6E,OAAOlF,EAAEmF,QAAQvD,EAAMgC,EAAKvD,YAAa,IAGtDsD,GACHC,EAAKM,UAGHN,EAAKzD,QAAQ+C,QAAUU,EAAKvD,WAAWc,QACzCyC,EAAKxC,WAAWgE,YAAY,2BAE9BxB,EAAKtD,SAASyE,QAAQ/E,EAAEgF,MAAM,eAAkBpD,KAAMA,MAMxDyD,UAAW,WACT,GAAIzB,GAAOxD,IAKX,KAHAJ,EAAE,OAAQ4D,EAAKxC,YAAY0C,SAC3B9D,EAAE,SAAU4D,EAAKtD,UAAUwD,SAErBF,EAAKvD,WAAWc,OAAS,GAC7ByC,EAAKvD,WAAWiF,KAElB1B,GAAKM,UAEDN,EAAKzD,QAAQ+C,UAAY9C,KAAKmF,aAChCnF,KAAKoF,UAOTC,QAAS,WACP,GAAI7B,GAAOxD,IACXJ,GAAE,OAAQ4D,EAAKxC,YAAYsE,KAAK,WAC9B,GAAIpC,GAAOtD,EAAEI,MACTwB,EAAO0B,EAAKiB,KAAK,QACjB1D,EAAY+C,EAAKzD,QAAQU,UAAUe,GACnCoB,EAAWY,EAAKzD,QAAQ6C,SAASpB,GACjCkB,EAAWc,EAAKzD,QAAQ2C,SAASlB,EASnC,IANA0B,EAAKvC,KAAK,QAAS,MACnBuC,EAAKwB,SAAS,OAAS/C,EAAWe,IAClCQ,EAAKqC,WAAWrB,OAAO,WACrB,MAAwB,IAAjBlE,KAAKwF,WACX,GAAGC,UAAY9D,EAAWiB,GAEzBY,EAAKpD,SAAU,CACjB,GAAIsF,GAAS9F,EAAE,SAAU4D,EAAKtD,UAAUgE,OAAO,WAAa,MAAOtE,GAAEI,MAAMmE,KAAK,UAAY3C,GAC5FkE,GAAO/E,KAAK,QAASF,OAQ7BkD,MAAO,WACL,MAAO3D,MAAKC,YAOd6D,QAAS,WACP,GAAIN,GAAOxD,KACP2F,EAAM/F,EAAEgG,IAAIpC,EAAKG,QAAS,SAASnC,GACjC,MAAOgC,GAAKzD,QAAQU,UAAUe,GAAMmB,YAG1Ca,GAAKtD,SAASyF,IAAIA,GAAK,GAAMhB,QAAQ,WAMvCvD,MAAO,SAASrB,GACd,GAAIyD,GAAOxD,IAEXwD,GAAKzD,QAAUH,EAAEiG,UAAWpD,EAAgB1C,EAC5C,IAAI+F,GAAYtC,EAAKzD,QAAQ+F,aAGzBtC,GAAKhD,cACPgD,EAAKzD,QAAQ8C,WAAY,GAE3BxB,EAAuBmC,EAAKzD,QAAS,aACrCsB,EAAuBmC,EAAKzD,QAAS,YACrCsB,EAAuBmC,EAAKzD,QAAS,YAGjCyD,EAAKzD,QAAQgG,SACfD,EAAUC,OAASvC,EAAKzD,QAAQgG,QAE9BD,EAAUC,QAAUnG,EAAEoG,GAAGF,YAC3BrE,EAAmBqE,EAAW,UAE9BtC,EAAKvC,OAAO6E,WACVC,OAAQ,SAAUE,EAAOC,GACvB,QAASC,GAAaxC,GAGpB,IAAK,GAFDyC,MAEKvC,EAAI,EAAGA,EAAIF,EAAM5C,OAAQ8C,IAAK,CACrC,GAAIhC,GAAO2B,EAAKzD,QAAQ6C,SAASe,EAAME,GACvC+B,GAAI/D,GAAQ8B,EAAME,GAClBuC,EAAMhC,KAAKvC,GAEbqE,EAAQE,GAGVpG,KAAK4F,MACL,IAAIA,GAAM5F,KAAK4F,IACXzB,EAAO2B,EAAUC,OAAOE,EAExBrG,GAAEyG,WAAWlC,EAAKmC,SAEpBnC,EAAKmC,QAAQH,GAGbvG,EAAE2G,KAAKpC,GACLqC,KAAKL,IAGXM,QAAS,SAAU5E,GACjB2B,EAAKF,IAAItD,KAAK4F,IAAI/D,KAEpB6E,QAAS,SAAU7E,GACjB,MAAwE,KAAhEA,EAAK8E,cAAcC,QAAQ5G,KAAKiG,MAAMY,OAAOF,gBAEvDG,OAAQ,SAAUV,GAChB,MAAOA,GAAMW,QAEfC,YAAa,SAAUnF,GACrB,GAAIoF,GAAQ,GAAIC,QAAQ,IAAMlH,KAAKiG,MAAQ,IAAK,KAChD,OAAOpE,GAAKsF,QAASF,EAAO,2BAKlCzD,EAAKxC,WAAWoG,GAAG,QAASxH,EAAEyH,MAAM,WAClC7D,EAAKvC,OAAOmB,SACXoB,IAEHA,EAAKxC,WAAWoG,GAAG,UAAW,QAASxH,EAAEyH,MAAM,SAASC,GACtD,GAAIrG,GAASrB,EAAE0H,EAAMC,QACjBC,EAAgBhE,EAAKa,kBAEzB,QAAQiD,EAAMG,OAEZ,IAAK,GACH,GAAsC,IAAlC1F,EAAmBd,EAAO,IAAW,CACvC,GAAIyG,GAAOF,EAAcE,MACrBA,IACFlE,EAAKE,OAAOgE,EAAKvD,KAAK,SAG1B,KAGF,KAAK,IACH,GAAsC,IAAlCpC,EAAmBd,EAAO,IAAW,CACvC,GAAI0G,GAAOH,EAAcG,MACrBA,IACFnE,EAAKE,OAAOiE,EAAKxD,KAAK,SAG1B,KAGF,KAAK,IAEH,GAAIyD,GAAWJ,EAAcE,MACD,KAAxBzG,EAAO0E,MAAM5E,QAAgB6G,EAAS,KACxCA,EAAStD,OAAOkD,GAChBvG,EAAOmB,QAET,MAEF,KAAK,IAEH,GAAIyF,GAAWL,EAAcG,MACD,KAAxB1G,EAAO0E,MAAM5E,QAAgB8G,EAAS,KACxCA,EAAS1G,MAAMqG,GACfvG,EAAOmB,QAET,MACH,SAGOoB,EAAKzD,QAAQ8C,WAAajD,EAAEmF,QAAQuC,EAAMG,MAAOjE,EAAKzD,QAAQiD,cAAgB,IAChFQ,EAAKF,IAAIrC,EAAO0E,OAChB1E,EAAO0E,IAAI,IACX2B,EAAMQ,kBAKZ7G,EAAON,KAAK,OAAQE,KAAKC,IAAId,KAAKY,UAAWK,EAAO0E,MAAM5E,UACzDyC,IAGHA,EAAKxC,WAAWoG,GAAG,QAAS,qBAAsBxH,EAAEyH,MAAM,SAASC,GACjE9D,EAAKE,OAAO9D,EAAE0H,EAAMC,QAAQQ,QAAQ,QAAQ5D,KAAK,UAChDX,IAGCA,EAAKzD,QAAQU,YAAcgC,EAAehC,YACX,UAA7B+C,EAAKtD,SAAS,GAAGG,QACjBmD,EAAKF,IAAIE,EAAKtD,SAASyF,OAEzB/F,EAAE,SAAU4D,EAAKtD,UAAUoF,KAAK,WAC9B9B,EAAKF,IAAI1D,EAAEI,MAAMW,KAAK,UAAU,OASxCqH,QAAS,WACP,GAAIxE,GAAOxD,IAGXwD,GAAKxC,WAAWiH,IAAI,WAAY,SAChCzE,EAAKxC,WAAWiH,IAAI,QAAS,iBAE7BzE,EAAKxC,WAAW0C,SAChBF,EAAKtD,SAASgI,WAAW,aACzB1E,EAAKtD,SAASiI,QAMhB/F,MAAO,WACLpC,KAAKiB,OAAOmB,SAMdgG,MAAO,WACL,MAAOpI,MAAKiB,QAOdoD,iBAAkB,WAGhB,IAFA,GAAIgE,GAAMrI,KAAKiB,OAAO,GAClBqH,EAAYtI,KAAKgB,WAAW,GAC1BqH,GAAOA,EAAIE,aAAeD,GAC9BD,EAAMA,EAAIE,UAEZ,OAAO3I,GAAEyI,KAObzI,EAAEoG,GAAGwC,UAAY,SAASC,EAAMC,GAC9B,GAAIC,KAyBJ,OAvBA3I,MAAKsF,KAAK,WACR,GAAIkD,GAAY5I,EAAEI,MAAMmE,KAAK,YAG7B,IAAKqE,EAWE,CAEL,GAAII,GAASJ,EAAUC,GAAMC,EACd3F,UAAX6F,GACFD,EAAQvE,KAAKwE,OAdfJ,GAAY,GAAI3I,GAAUG,KAAMyI,GAChC7I,EAAEI,MAAMmE,KAAK,YAAaqE,GAC1BG,EAAQvE,KAAKoE,GAEQ,WAAjBxI,KAAKK,SACPT,EAAE,SAAUA,EAAEI,OAAOW,KAAK,WAAY,YAIxCf,EAAEI,MAAM2F,IAAI/F,EAAEI,MAAM2F,SASJ,gBAAR8C,GAEHE,EAAQ5H,OAAS,EAAI4H,EAAUA,EAAQ,GAEvCA,GAIX/I,EAAEoG,GAAGwC,UAAUK,YAAchJ,CAsB7B,IAAI+B,GAAsBhC,EAAE,UA8B5BA,GAAE,WACAA,EAAE,qEAAqE4I,eAExEM,OAAOC"}