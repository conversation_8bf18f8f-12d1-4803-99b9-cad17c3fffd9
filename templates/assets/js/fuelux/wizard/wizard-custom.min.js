﻿(function(n,t){var i=function(t,i){var r;this.$element=n(t);this.options=n.extend({},n.fn.wizard.defaults,i);this.currentStep=1;this.numSteps=this.$element.find("li").length;this.$prevBtn=$("#"+this.$element[0].id+"-actions").find("button.btn-prev");this.$nextBtn=$("#"+this.$element[0].id+"-actions").find("button.btn-next");r=this.$nextBtn.children().detach();this.nextText=n.trim(this.$nextBtn.text());this.$nextBtn.append(r);this.$prevBtn.on("click",n.proxy(this.previous,this));this.$nextBtn.on("click",n.proxy(this.next,this));this.$element.on("click","li.complete",n.proxy(this.stepclicked,this));this.$stepContainer=this.$element.data("target")||"body";this.$stepContainer=n(this.$stepContainer)};i.prototype={constructor:i,setState:function(){var c=this.currentStep>1,l=this.currentStep===1,a=this.currentStep===this.numSteps,t,r,u,f,e,o,s,i,h;this.$prevBtn.attr("disabled",l===!0||c===!1);t=this.$nextBtn.data();t&&t.last&&(this.lastText=t.last,typeof this.lastText!="undefined"&&(r=a!==!0?this.nextText:this.lastText,u=this.$nextBtn.children().detach(),this.$nextBtn.text(r).append(u)));f=this.$element.find("li");f.removeClass("active").removeClass("complete");e="li:lt("+(this.currentStep-1)+")";o=this.$element.find(e);o.addClass("complete");s="li:eq("+(this.currentStep-1)+")";i=this.$element.find(s);i.addClass("active");h=i.data().target;this.$stepContainer.find(".step-pane").removeClass("active");n(h).addClass("active");this.$element.trigger("changed")},stepclicked:function(t){var u=n(t.currentTarget),i=this.$element.find("li").index(u),r=n.Event("stepclick");(this.$element.trigger(r,{step:i+1}),r.isDefaultPrevented())||(this.currentStep=i+1,this.setState())},previous:function(){var i=this.currentStep>1,t;if(i){if(t=n.Event("change"),this.$element.trigger(t,{step:this.currentStep,direction:"previous"}),t.isDefaultPrevented())return;this.currentStep-=1;this.setState()}},next:function(){var i=this.currentStep+1<=this.numSteps,r=this.currentStep===this.numSteps,t;if(i){if(t=n.Event("change"),this.$element.trigger(t,{step:this.currentStep,direction:"next"}),t.isDefaultPrevented())return;this.currentStep+=1;this.setState()}else r&&this.$element.trigger("finished")},selectedItem:function(){return{step:this.currentStep}}};n.fn.wizard=function(r,u){var f,e=this.each(function(){var e=n(this),t=e.data("wizard"),o=typeof r=="object"&&r;t||e.data("wizard",t=new i(this,o));typeof r=="string"&&(f=t[r](u))});return f===t?e:f};n.fn.wizard.defaults={};n.fn.wizard.Constructor=i;n(function(){n("body").on("mousedown.wizard.data-api",".wizard",function(){var t=n(this);t.data("wizard")||t.wizard(t.data())})})})(window.jQuery);
/*
//# sourceMappingURL=wizard-custom.min.js.map
*/