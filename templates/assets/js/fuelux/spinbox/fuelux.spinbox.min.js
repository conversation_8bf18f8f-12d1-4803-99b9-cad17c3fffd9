﻿(function(n){typeof define=="function"&&define.amd?define(["jquery"],n):n(jQuery)})(function(n){var i=n.fn.spinbox,t=function(t,i){this.$element=n(t);this.$element.find(".btn").on("click",function(n){n.preventDefault()});this.options=n.extend({},n.fn.spinbox.defaults,i);this.$input=this.$element.find(".spinbox-input");this.$element.on("focusin.fu.spinbox",this.$input,n.proxy(this.changeFlag,this));this.$element.on("focusout.fu.spinbox",this.$input,n.proxy(this.change,this));this.$element.on("keydown.fu.spinbox",this.$input,n.proxy(this.keydown,this));this.$element.on("keyup.fu.spinbox",this.$input,n.proxy(this.keyup,this));if(this.bindMousewheelListeners(),this.mousewheelTimeout={},this.options.hold){this.$element.on("mousedown.fu.spinbox",".spinbox-up",n.proxy(function(){this.startSpin(!0)},this));this.$element.on("mouseup.fu.spinbox",".spinbox-up, .spinbox-down",n.proxy(this.stopSpin,this));this.$element.on("mouseout.fu.spinbox",".spinbox-up, .spinbox-down",n.proxy(this.stopSpin,this));this.$element.on("mousedown.fu.spinbox",".spinbox-down",n.proxy(function(){this.startSpin(!1)},this))}else{this.$element.on("click.fu.spinbox",".spinbox-up",n.proxy(function(){this.step(!0)},this));this.$element.on("click.fu.spinbox",".spinbox-down",n.proxy(function(){this.step(!1)},this))}this.switches={count:1,enabled:!0};this.switches.speed=this.options.speed==="medium"?300:this.options.speed==="fast"?100:500;this.lastValue=this.options.value;this.render();this.options.disabled&&this.disable()};t.prototype={constructor:t,destroy:function(){return this.$element.remove(),this.$element.find("input").each(function(){n(this).attr("value",n(this).val())}),this.$element[0].outerHTML},render:function(){var t=this.parseInput(this.$input.val()),i="";t!==""&&this.options.value===0?this.value(t):this.output(this.options.value);this.options.units.length&&n.each(this.options.units,function(n,t){t.length>i.length&&(i=t)})},output:function(n,t){return n=(n+"").split(".").join(this.options.decimalMark),t=t||!0,t&&this.$input.val(n),n},parseInput:function(n){return(n+"").split(this.options.decimalMark).join(".")},change:function(){var n=this.parseInput(this.$input.val())||"";this.options.units.length||this.options.decimalMark!=="."?n=this.parseValueWithUnit(n):n/1?n=this.options.value=this.checkMaxMin(n/1):(n=this.checkMaxMin(n.replace(/[^0-9.-]/g,"")||""),this.options.value=n/1);this.output(n);this.changeFlag=!1;this.triggerChangedEvent()},changeFlag:function(){this.changeFlag=!0},stopSpin:function(){this.switches.timeout!==undefined&&(clearTimeout(this.switches.timeout),this.switches.count=1,this.triggerChangedEvent())},triggerChangedEvent:function(){var n=this.value();n!==this.lastValue&&(this.lastValue=n,this.$element.trigger("changed.fu.spinbox",this.output(n,!1)))},startSpin:function(t){if(!this.options.disabled){var i=this.switches.count;i===1?(this.step(t),i=1):i=i<3?1.5:i<8?2.5:4;this.switches.timeout=setTimeout(n.proxy(function(){this.iterate(t)},this),this.switches.speed/i);this.switches.count++}},iterate:function(n){this.step(n);this.startSpin(n)},step:function(n){var f,u,r,t,i,e;this.changeFlag&&this.change();r=this.options.value;t=n?this.options.max:this.options.min;(n?r<t:r>t)?(i=r+(n?1:-1)*this.options.step,this.options.step%1!=0&&(f=(this.options.step+"").split(".")[1].length,u=Math.pow(10,f),i=Math.round(i*u)/u),(n?i>t:i<t)?this.value(t):this.value(i)):this.options.cycle&&(e=n?this.options.min:this.options.max,this.value(e))},value:function(n){if(n||n===0){if(this.options.units.length||this.options.decimalMark!==".")return this.output(this.parseValueWithUnit(n+(this.unit||""))),this;if(!isNaN(parseFloat(n))&&isFinite(n))return this.options.value=n/1,this.output(n+(this.unit?this.unit:"")),this}else return this.changeFlag&&this.change(),this.unit?this.options.value+this.unit:this.output(this.options.value,!1)},isUnitLegal:function(t){var i;return n.each(this.options.units,function(n,r){if(r.toLowerCase()===t.toLowerCase())return i=t.toLowerCase(),!1}),i},parseValueWithUnit:function(n){var t=n.replace(/[^a-zA-Z]/g,""),i=n.replace(/[^0-9.-]/g,"");return t&&(t=this.isUnitLegal(t)),this.options.value=this.checkMaxMin(i/1),this.unit=t||undefined,this.options.value+(t||"")},checkMaxMin:function(n){return isNaN(parseFloat(n))?n:(n<=this.options.max&&n>=this.options.min||(n=n>=this.options.max?this.options.max:this.options.min),n)},disable:function(){this.options.disabled=!0;this.$element.addClass("disabled");this.$input.attr("disabled","");this.$element.find("button").addClass("disabled")},enable:function(){this.options.disabled=!1;this.$element.removeClass("disabled");this.$input.removeAttr("disabled");this.$element.find("button").removeClass("disabled")},keydown:function(n){var t=n.keyCode;t===38?this.step(!0):t===40&&this.step(!1)},keyup:function(n){var t=n.keyCode;(t===38||t===40)&&this.triggerChangedEvent()},bindMousewheelListeners:function(){var t=this.$input.get(0);t.addEventListener?(t.addEventListener("mousewheel",n.proxy(this.mousewheelHandler,this),!1),t.addEventListener("DOMMouseScroll",n.proxy(this.mousewheelHandler,this),!1)):t.attachEvent("onmousewheel",n.proxy(this.mousewheelHandler,this))},mousewheelHandler:function(n){if(!this.options.disabled){var t=window.event||n,i=Math.max(-1,Math.min(1,t.wheelDelta||-t.detail)),r=this;return clearTimeout(this.mousewheelTimeout),this.mousewheelTimeout=setTimeout(function(){r.triggerChangedEvent()},300),i<0?this.step(!0):this.step(!1),t.preventDefault?t.preventDefault():t.returnValue=!1,!1}}};n.fn.spinbox=function(i){var u=Array.prototype.slice.call(arguments,1),r,f=this.each(function(){var e=n(this),f=e.data("fu.spinbox"),o=typeof i=="object"&&i;f||e.data("fu.spinbox",f=new t(this,o));typeof i=="string"&&(r=f[i].apply(f,u))});return r===undefined?f:r};n.fn.spinbox.defaults={value:0,min:0,max:999,step:1,hold:!0,speed:"medium",disabled:!1,cycle:!1,units:[],decimalMark:"."};n.fn.spinbox.Constructor=t;n.fn.spinbox.noConflict=function(){return n.fn.spinbox=i,this};n(document).on("mousedown.fu.spinbox.data-api","[data-initialize=spinbox]",function(t){var i=n(t.target).closest(".spinbox");i.data("fu.spinbox")||i.spinbox(i.data())});n(function(){n("[data-initialize=spinbox]").each(function(){var t=n(this);t.data("fu.spinbox")||t.spinbox(t.data())})})});