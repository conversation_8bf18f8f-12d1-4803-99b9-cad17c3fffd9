﻿(function(n,t){"use strict";typeof define=="function"&&define.amd?define(["jquery"],t):typeof exports=="object"?module.exports=t(require("jquery")):n.bootbox=t(n.jQuery)})(this,function init(n,t){"use strict";function a(n){var t=o[f.locale];return t?t[n]:o.en[n]}function e(t,i,r){t.stopPropagation();t.preventDefault();var u=n.isFunction(r)&&r(t)===!1;u||i.modal("hide")}function v(n){var i,t=0;for(i in n)t++;return t}function r(t,i){var r=0;n.each(t,function(n,t){i(n,t,r++)})}function y(t){var i,u;if(typeof t!="object")throw new Error("Please supply an object of options");if(!t.message)throw new Error("Please specify a message");return t=n.extend({},f,t),t.buttons||(t.buttons={}),t.backdrop=t.backdrop?"static":!1,i=t.buttons,u=v(i),r(i,function(t,r,f){if(n.isFunction(r)&&(r=i[t]={callback:r}),n.type(r)!=="object")throw new Error("button with key "+t+" must be an object");r.label||(r.label=t);r.className||(r.className=u<=2&&f===u-1?"btn-primary":"btn-default")}),t}function p(n,t){var r=n.length,i={};if(r<1||r>2)throw new Error("Invalid argument length");return r===2||typeof n[0]=="string"?(i[t[0]]=n[0],i[t[1]]=n[1]):i=n[0],i}function s(t,i,r){return n.extend(!0,{},t,p(i,r))}function h(n,t,i,r){var u={className:"bootbox-"+n,buttons:c.apply(null,t)};return l(s(u,r,i),t)}function c(){for(var t={},n=0,r=arguments.length;n<r;n++){var i=arguments[n],u=i.toLowerCase(),f=i.toUpperCase();t[u]={label:a(f)}}return t}function l(n,i){var u={};return r(i,function(n,t){u[t]=!0}),r(n.buttons,function(n){if(u[n]===t)throw new Error("button key "+n+" is not allowed (options are "+i.join("\n")+")");}),n}var u={dialog:"<div class='bootbox modal' tabindex='-1' role='dialog'><div class='modal-dialog'><div class='modal-content'><div class='modal-body'><div class='bootbox-body'><\/div><\/div><\/div><\/div><\/div>",header:"<div class='modal-header'><h4 class='modal-title'><\/h4><\/div>",footer:"<div class='modal-footer'><\/div>",closeButton:"<button type='button' class='bootbox-close-button close' data-dismiss='modal' aria-hidden='true'>&times;<\/button>",form:"<form class='bootbox-form'><\/form>",inputs:{text:"<input class='bootbox-input bootbox-input-text form-control' autocomplete=off type=text />",textarea:"<textarea class='bootbox-input bootbox-input-textarea form-control'><\/textarea>",email:"<input class='bootbox-input bootbox-input-email form-control' autocomplete='off' type='email' />",select:"<select class='bootbox-input bootbox-input-select form-control'><\/select>",checkbox:"<div class='checkbox'><label><input class='bootbox-input bootbox-input-checkbox' type='checkbox' /><\/label><\/div>",date:"<input class='bootbox-input bootbox-input-date form-control' autocomplete=off type='date' />",time:"<input class='bootbox-input bootbox-input-time form-control' autocomplete=off type='time' />",number:"<input class='bootbox-input bootbox-input-number form-control' autocomplete=off type='number' />",password:"<input class='bootbox-input bootbox-input-password form-control' autocomplete='off' type='password' />"}},f={locale:"en",backdrop:!0,animate:!0,className:null,closeButton:!0,show:!0,container:"body"},i={},o;return i.alert=function(){var t;if(t=h("alert",["ok"],["message","callback"],arguments),t.callback&&!n.isFunction(t.callback))throw new Error("alert requires callback property to be a function when provided");return t.buttons.ok.callback=t.onEscape=function(){return n.isFunction(t.callback)?t.callback():!0},i.dialog(t)},i.confirm=function(){var t;if(t=h("confirm",["cancel","confirm"],["message","callback"],arguments),t.buttons.cancel.callback=t.onEscape=function(){return t.callback(!1)},t.buttons.confirm.callback=function(){return t.callback(!0)},!n.isFunction(t.callback))throw new Error("confirm requires a callback");return i.dialog(t)},i.prompt=function(){var f,p,h,v,e,w,o,b,y,a,k;if(v=n(u.form),p={className:"bootbox-prompt",buttons:c("cancel","confirm"),value:"",inputType:"text"},f=l(s(p,arguments,["title","callback"]),["cancel","confirm"]),w=f.show===t?!0:f.show,b=["date","time","number"],y=document.createElement("input"),y.setAttribute("type",f.inputType),b[f.inputType]&&(f.inputType=y.type),f.message=v,f.buttons.cancel.callback=f.onEscape=function(){return f.callback(null)},f.buttons.confirm.callback=function(){var t,i;switch(f.inputType){case"text":case"textarea":case"email":case"select":case"date":case"time":case"number":case"password":t=e.val();break;case"checkbox":i=e.find("input:checked");t=[];r(i,function(i,r){t.push(n(r).val())})}return f.callback(t)},f.show=!1,!f.title)throw new Error("prompt requires a title");if(!n.isFunction(f.callback))throw new Error("prompt requires a callback");if(!u.inputs[f.inputType])throw new Error("invalid prompt type");e=n(u.inputs[f.inputType]);switch(f.inputType){case"text":case"textarea":case"email":case"date":case"time":case"number":case"password":e.val(f.value);break;case"select":if(a={},o=f.inputOptions||[],!o.length)throw new Error("prompt with select requires options");r(o,function(i,r){var u=e;if(r.value===t||r.text===t)throw new Error("given options in wrong format");r.group&&(a[r.group]||(a[r.group]=n("<optgroup/>").attr("label",r.group)),u=a[r.group]);u.append("<option value='"+r.value+"'>"+r.text+"<\/option>")});r(a,function(n,t){e.append(t)});e.val(f.value);break;case"checkbox":if(k=n.isArray(f.value)?f.value:[f.value],o=f.inputOptions||[],!o.length)throw new Error("prompt with checkbox requires options");if(!o[0].value||!o[0].text)throw new Error("given options in wrong format");e=n("<div/>");r(o,function(t,i){var o=n(u.inputs[f.inputType]);o.find("input").attr("value",i.value);o.find("label").append(i.text);r(k,function(n,t){t===i.value&&o.find("input").prop("checked",!0)});e.append(o)})}f.placeholder&&e.attr("placeholder",f.placeholder);f.pattern&&e.attr("pattern",f.pattern);v.append(e);v.on("submit",function(n){n.preventDefault();h.find(".btn-primary").click()});h=i.dialog(f);h.off("shown.bs.modal");h.on("shown.bs.modal",function(){e.focus()});return w===!0&&h.modal("show"),h},i.dialog=function(t){var h;t=y(t);var i=n(u.dialog),o=i.find(".modal-body"),c=t.buttons,s="",f={onEscape:t.onEscape};r(c,function(n,t){s+="<button data-bb-handler='"+n+"' type='button' class='btn "+t.className+"'>"+t.label+"<\/button>";f[n]=t.callback});o.find(".bootbox-body").html(t.message);t.animate===!0&&i.addClass("fade");t.className&&i.addClass(t.className);t.title&&o.before(u.header);t.closeButton&&(h=n(u.closeButton),t.title?i.find(".modal-header").prepend(h):h.css("margin-top","-10px").prependTo(o));t.title&&i.find(".modal-title").html(t.title);s.length&&(o.after(u.footer),i.find(".modal-footer").html(s));i.on("hidden.bs.modal",function(n){n.target===this&&i.remove()});i.on("shown.bs.modal",function(){i.find(".btn-primary:first").focus()});i.on("escape.close.bb",function(n){f.onEscape&&e(n,i,f.onEscape)});i.on("click",".modal-footer button",function(t){var r=n(this).data("bb-handler");e(t,i,f[r])});i.on("click",".bootbox-close-button",function(n){e(n,i,f.onEscape)});i.on("keyup",function(n){n.which===27&&i.trigger("escape.close.bb")});return n(t.container).append(i),i.modal({backdrop:t.backdrop,keyboard:!1,show:!1}),t.show&&i.modal("show"),i},i.setDefaults=function(){var t={};arguments.length===2?t[arguments[0]]=arguments[1]:t=arguments[0];n.extend(f,t)},i.hideAll=function(){n(".bootbox").modal("hide")},o={br:{OK:"OK",CANCEL:"Cancelar",CONFIRM:"Sim"},da:{OK:"OK",CANCEL:"Annuller",CONFIRM:"Accepter"},de:{OK:"OK",CANCEL:"Abbrechen",CONFIRM:"Akzeptieren"},en:{OK:"OK",CANCEL:"Cancel",CONFIRM:"OK"},es:{OK:"OK",CANCEL:"Cancelar",CONFIRM:"Aceptar"},fi:{OK:"OK",CANCEL:"Peruuta",CONFIRM:"OK"},fr:{OK:"OK",CANCEL:"Annuler",CONFIRM:"D'accord"},he:{OK:"אישור",CANCEL:"ביטול",CONFIRM:"אישור"},it:{OK:"OK",CANCEL:"Annulla",CONFIRM:"Conferma"},lt:{OK:"Gerai",CANCEL:"Atšaukti",CONFIRM:"Patvirtinti"},lv:{OK:"Labi",CANCEL:"Atcelt",CONFIRM:"Apstiprināt"},nl:{OK:"OK",CANCEL:"Annuleren",CONFIRM:"Accepteren"},no:{OK:"OK",CANCEL:"Avbryt",CONFIRM:"OK"},pl:{OK:"OK",CANCEL:"Anuluj",CONFIRM:"Potwierdź"},ru:{OK:"OK",CANCEL:"Отмена",CONFIRM:"Применить"},sv:{OK:"OK",CANCEL:"Avbryt",CONFIRM:"OK"},tr:{OK:"Tamam",CANCEL:"İptal",CONFIRM:"Onayla"},zh_CN:{OK:"OK",CANCEL:"取消",CONFIRM:"确认"},zh_TW:{OK:"OK",CANCEL:"取消",CONFIRM:"確認"}},i.init=function(t){return init(t||n)},i});
/*
//# sourceMappingURL=bootbox.min.js.map
*/