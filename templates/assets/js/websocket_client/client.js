/**
 * Created by al<PERSON><PERSON> on 21/03/21.
 */

var base_url = window.location.origin;
const audio_url = base_url+"/templates/assets/sound/alert.mp3";

if (base_url.includes("https://")){
    base_url = base_url.replace("https://" , '');
    base_url = server;
    WebSocketUrl = "https://";
} else{
    base_url = base_url.replace("http://" , '');
    base_url = "localhost";
    WebSocketUrl = "http://";
}

var socket = io.connect(WebSocketUrl + base_url + ":" + port , {
    secure: true
});

socket.emit('user' , {
    user_id : user_id
});


socket.on('notification' , function(data){
    console.log("Geting Notification");
    var sample = document.getElementById("play-audio");

    sample.src = audio_url;
    sample.play();
    let badge = document.getElementById("notificationBadge");
    badge.innerText = data.message;
});

socket.on('ticket' , function(data){
    console.log("Geting Ticket");
    var sample = document.getElementById("play-audio");

    sample.src = audio_url;
    sample.play();
    $("#tickets-badge").show();
    ticket.innerText = message["message"];
    let badge = document.getElementById("tickets-badge");
    badge.innerText = data.message;
});

