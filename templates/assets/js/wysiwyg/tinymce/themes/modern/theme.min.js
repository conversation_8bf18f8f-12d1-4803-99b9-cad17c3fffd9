!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("1",window),h("a",tinymce.util.Tools.resolve),g("2",["a"],function(a){return a("tinymce.AddOnManager")}),g("3",["a"],function(a){return a("tinymce.EditorManager")}),g("4",["a"],function(a){return a("tinymce.Env")}),g("5",["a"],function(a){return a("tinymce.ui.Api")}),g("b",["a"],function(a){return a("tinymce.dom.DOMUtils")}),g("c",["a"],function(a){return a("tinymce.ui.Factory")}),g("d",["a"],function(a){return a("tinymce.util.Tools")}),g("e",[],function(){var a=function(a,b){return function(){var c=a.find(b)[0];c&&c.focus(!0)}},b=function(b,c){b.shortcuts.add("Alt+F9","",a(c,"menubar")),b.shortcuts.add("Alt+F10,F10","",a(c,"toolbar")),b.shortcuts.add("Alt+F11","",a(c,"elementpath")),c.on("cancel",function(){b.focus()})};return{addKeys:b}}),g("f",["b"],function(a){var b=a.DOM,c=function(a,c,d){return function(){var e=a.getContentAreaContainer().querySelector("iframe").offsetWidth,f=Math.max(e-a.getDoc().documentElement.offsetWidth,0);b.setStyle(c,"right",f+"px"),d?b.setStyle(c,"top","-16px"):b.setStyle(c,"bottom","1px")}},d=function(a){return function(){b.hide(a)}},e=function(a,b,d){c(a,b,d)(),a.on("NodeChange ResizeEditor",c(a,b,d))},f=function(a,b,c){c.appendChild(b),e(a,b,!0)},g=function(a,b){a.getContainer().appendChild(b),e(a,b,!1)},h=function(a){a.on("SkinLoaded",function(){var c=b.create("div",{"class":"mce-branding-powered-by"}),e=a.getContainer().querySelector(".mce-statusbar");e?f(a,c,e):g(a,c),b.bind(c,"click",d(c))})},i=function(a){a.settings.branding!==!1&&h(a)};return{setup:i}}),g("n",["a"],function(a){return a("tinymce.util.Delay")}),g("o",["a"],function(a){return a("tinymce.geom.Rect")}),g("k",["d","c"],function(a,b){var c="undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image",d=function(c,d,e){var f,g=[];if(d)return a.each(d.split(/[ ,]/),function(a){var d,h=function(){var b=c.selection;a.settings.stateSelector&&b.selectorChanged(a.settings.stateSelector,function(b){a.active(b)},!0),a.settings.disabledStateSelector&&b.selectorChanged(a.settings.disabledStateSelector,function(b){a.disabled(b)})};"|"==a?f=null:(f||(f={type:"buttongroup",items:[]},g.push(f)),c.buttons[a]&&(d=a,a=c.buttons[d],"function"==typeof a&&(a=a()),a.type=a.type||"button",a.size=e,a=b.create(a),f.items.push(a),c.initialized?h():c.on("init",h)))}),{type:"toolbar",layout:"flow",items:g}},e=function(b,e){var f=[],g=b.settings,h=function(a){if(a)return f.push(d(b,a,e)),!0};if(a.isArray(g.toolbar)){if(0===g.toolbar.length)return;a.each(g.toolbar,function(a,b){g["toolbar"+(b+1)]=a}),delete g.toolbar}for(var i=1;i<10&&h(g["toolbar"+i]);i++);if(f.length||g.toolbar===!1||h(g.toolbar||c),f.length)return{type:"panel",layout:"stack",classes:"toolbar-grp",ariaRoot:!0,ariaRemember:!0,items:f}};return{createToolbar:d,createToolbars:e}}),g("g",["b","d","n","c","o","k"],function(a,b,c,d,e,f){var g=a.DOM,h=function(a){return{left:a.x,top:a.y,width:a.w,height:a.h,right:a.x+a.w,bottom:a.y+a.h}},i=function(a){b.each(a.contextToolbars,function(a){a.panel&&a.panel.hide()})},j=function(a,b){a.moveTo(b.left,b.top)},k=function(a,c,d){c=c?c.substr(0,2):"",b.each({t:"down",b:"up"},function(b,e){a.classes.toggle("arrow-"+b,d(e,c.substr(0,1)))}),b.each({l:"left",r:"right"},function(b,e){a.classes.toggle("arrow-"+b,d(e,c.substr(1,1)))})},l=function(a,b,c,d,e,f){return f=h({x:b,y:c,w:f.w,h:f.h}),a&&(f=a({elementRect:h(d),contentAreaRect:h(e),panelRect:f})),f},m=function(a){var h,m=a.settings,n=function(){return a.contextToolbars||[]},o=function(b){var c,d,e;return c=g.getPos(a.getContentAreaContainer()),d=a.dom.getRect(b),e=a.dom.getRoot(),"BODY"===e.nodeName&&(d.x-=e.ownerDocument.documentElement.scrollLeft||e.scrollLeft,d.y-=e.ownerDocument.documentElement.scrollTop||e.scrollTop),d.x+=c.x,d.y+=c.y,d},p=function(b,c){var d,f,h,n,p,q,r,s,t=m.inline_toolbar_position_handler;if(!a.removed){if(!b||!b.toolbar.panel)return void i(a);r=["bc-tc","tc-bc","tl-bl","bl-tl","tr-br","br-tr"],p=b.toolbar.panel,c&&p.show(),h=o(b.element),f=g.getRect(p.getEl()),n=g.getRect(a.getContentAreaContainer()||a.getBody()),s=25,"inline"!==g.getStyle(b.element,"display",!0)&&(h.w=b.element.clientWidth,h.h=b.element.clientHeight),a.inline||(n.w=a.getDoc().documentElement.offsetWidth),a.selection.controlSelection.isResizable(b.element)&&h.w<s&&(h=e.inflate(h,0,8)),d=e.findBestRelativePosition(f,h,n,r),h=e.clamp(h,n),d?(q=e.relativePosition(f,h,d),j(p,l(t,q.x,q.y,h,n,f))):(n.h+=f.h,h=e.intersect(n,h),h?(d=e.findBestRelativePosition(f,h,n,["bc-tc","bl-tl","br-tr"]),d?(q=e.relativePosition(f,h,d),j(p,l(t,q.x,q.y,h,n,f))):j(p,l(t,h.x,h.y,h,n,f))):p.hide()),k(p,d,function(a,b){return a===b})}},q=function(b){return function(){var d=function(){a.selection&&p(u(a.selection.getNode()),b)};c.requestAnimationFrame(d)}},r=function(){h||(h=a.selection.getScrollContainer()||a.getWin(),g.bind(h,"scroll",q(!0)),a.on("remove",function(){g.unbind(h,"scroll")}))},s=function(b){var c;return b.toolbar.panel?(b.toolbar.panel.show(),void p(b)):(r(),c=d.create({type:"floatpanel",role:"dialog",classes:"tinymce tinymce-inline arrow",ariaLabel:"Inline toolbar",layout:"flex",direction:"column",align:"stretch",autohide:!1,autofix:!0,fixed:!0,border:1,items:f.createToolbar(a,b.toolbar.items),oncancel:function(){a.focus()}}),b.toolbar.panel=c,c.renderTo(document.body).reflow(),void p(b))},t=function(){b.each(n(),function(a){a.panel&&a.panel.hide()})},u=function(b){var c,d,e,f=n();for(e=a.$(b).parents().add(b),c=e.length-1;c>=0;c--)for(d=f.length-1;d>=0;d--)if(f[d].predicate(e[c]))return{toolbar:f[d],element:e[c]};return null};a.on("click keyup setContent ObjectResized",function(b){("setcontent"!==b.type||b.selection)&&c.setEditorTimeout(a,function(){var b;b=u(a.selection.getNode()),b?(t(),s(b)):t()})}),a.on("blur hide contextmenu",t),a.on("ObjectResizeStart",function(){var b=u(a.selection.getNode());b&&b.toolbar.panel&&b.toolbar.panel.hide()}),a.on("ResizeEditor ResizeWindow",q(!0)),a.on("nodeChange",q(!1)),a.on("remove",function(){b.each(n(),function(a){a.panel&&a.panel.remove()}),a.contextToolbars={}}),a.shortcuts.add("ctrl+shift+e > ctrl+shift+p","",function(){var b=u(a.selection.getNode());b&&b.toolbar.panel&&b.toolbar.panel.items()[0].focus()})};return{addContextualToolbars:m}}),g("h",["d"],function(a){var b={file:{title:"File",items:"newdocument"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall"},insert:{title:"Insert",items:"|"},view:{title:"View",items:"visualaid |"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript | formats | removeformat"},table:{title:"Table"},tools:{title:"Tools"}},c=function(a,b){var c;return"|"==b?{text:"|"}:c=a[b]},d=function(d,e,f){var g,h,i,j,k;if(k=a.makeMap((e.removed_menuitems||"").split(/[ ,]/)),e.menu?(h=e.menu[f],j=!0):h=b[f],h){g={text:h.title},i=[],a.each((h.items||"").split(/[ ,]/),function(a){var b=c(d,a);b&&!k[a]&&i.push(c(d,a))}),j||a.each(d,function(a){a.context==f&&("before"==a.separator&&i.push({text:"|"}),a.prependToContext?i.unshift(a):i.push(a),"after"==a.separator&&i.push({text:"|"}))});for(var l=0;l<i.length;l++)"|"==i[l].text&&(0!==l&&l!=i.length-1||i.splice(l,1));if(g.menu=i,!g.menu.length)return null}return g},e=function(a){var c,e=[],f=a.settings,g=[];if(f.menu)for(c in f.menu)g.push(c);else for(c in b)g.push(c);for(var h="string"==typeof f.menubar?f.menubar.split(/[ ,]/):g,i=0;i<h.length;i++){var j=h[i];j=d(a.menuItems,a.settings,j),j&&e.push(j)}return e};return{createMenuButtons:e}}),g("9",["b"],function(a){var b=a.DOM,c=function(a){return{width:a.clientWidth,height:a.clientHeight}},d=function(a,d,e){var f,g,h,i,j=a.settings;f=a.getContainer(),g=a.getContentAreaContainer().firstChild,h=c(f),i=c(g),null!==d&&(d=Math.max(j.min_width||100,d),d=Math.min(j.max_width||65535,d),b.setStyle(f,"width",d+(h.width-i.width)),b.setStyle(g,"width",d)),e=Math.max(j.min_height||100,e),e=Math.min(j.max_height||65535,e),b.setStyle(g,"height",e),a.fire("ResizeEditor")},e=function(a,b,c){var e=a.getContentAreaContainer();d(a,e.clientWidth+b,e.clientHeight+c)};return{resizeTo:d,resizeBy:e}}),g("i",["d","c","4"],function(a,b,c){var d=function(a){return{element:function(){return a}}},e=function(a,b,c){var e=a.settings[c];e&&e(d(b.getEl("body")))},f=function(b,c,d){a.each(d,function(a){var d=c.items().filter("#"+a.name)[0];d&&d.visible()&&a.name!==b&&(e(a,d,"onhide"),d.visible(!1))})},g=function(a){a.items().each(function(a){a.active(!1)})},h=function(b,c){return a.grep(b,function(a){return a.name===c})[0]},i=function(a,c,d){return function(i){var j=i.control,k=j.parents().filter("panel")[0],l=k.find("#"+c)[0],m=h(d,c);f(c,k,d),g(j.parent()),l&&l.visible()?(e(m,l,"onhide"),l.hide(),j.active(!1)):(l?(l.show(),e(m,l,"onshow")):(l=b.create({type:"container",name:c,layout:"stack",classes:"sidebar-panel",html:""}),k.prepend(l),e(m,l,"onrender"),e(m,l,"onshow")),j.active(!0)),a.fire("ResizeEditor")}},j=function(){return!c.ie||c.ie>=11},k=function(a){return!(!j()||!a.sidebars)&&a.sidebars.length>0},l=function(b){var c=a.map(b.sidebars,function(a){var c=a.settings;return{type:"button",icon:c.icon,image:c.image,tooltip:c.tooltip,onclick:i(b,a.name,b.sidebars)}});return{type:"panel",name:"sidebar",layout:"stack",classes:"sidebar",items:[{type:"toolbar",layout:"stack",classes:"sidebar-toolbar",items:c}]}};return{hasSidebar:k,createSidebar:l}}),g("j",[],function(){var a=function(a){var b=function(){a._skinLoaded=!0,a.fire("SkinLoaded")};return function(){a.initialized?b():a.on("init",b)}};return{fireSkinLoaded:a}}),g("6",["b","c","d","e","f","g","h","9","i","j","k"],function(a,b,c,d,e,f,g,h,i,j,k){var l=a.DOM,m=function(a){return function(b){a.find("*").disabled("readonly"===b.mode)}},n=function(a){return{type:"panel",name:"iframe",layout:"stack",classes:"edit-area",border:a,html:""}},o=function(a){return{type:"panel",layout:"stack",classes:"edit-aria-container",border:"1 0 0 0",items:[n("0"),i.createSidebar(a)]}},p=function(a,c,p){var q,r,s,t=a.settings;return p.skinUiCss&&l.styleSheetLoader.load(p.skinUiCss,j.fireSkinLoaded(a)),q=c.panel=b.create({type:"panel",role:"application",classes:"tinymce",style:"visibility: hidden",layout:"stack",border:1,items:[t.menubar===!1?null:{type:"menubar",border:"0 0 1 0",items:g.createMenuButtons(a)},k.createToolbars(a,t.toolbar_items_size),i.hasSidebar(a)?o(a):n("1 0 0 0")]}),t.resize!==!1&&(r={type:"resizehandle",direction:t.resize,onResizeStart:function(){var b=a.getContentAreaContainer().firstChild;s={width:b.clientWidth,height:b.clientHeight}},onResize:function(b){"both"===t.resize?h.resizeTo(a,s.width+b.deltaX,s.height+b.deltaY):h.resizeTo(a,null,s.height+b.deltaY)}}),t.statusbar!==!1&&q.add({type:"panel",name:"statusbar",classes:"statusbar",layout:"flow",border:"1 0 0 0",ariaRoot:!0,items:[{type:"elementpath",editor:a},r]}),a.fire("BeforeRenderUI"),a.on("SwitchMode",m(q)),q.renderBefore(p.targetNode).reflow(),t.readonly&&a.setMode("readonly"),p.width&&l.setStyle(q.getEl(),"width",p.width),a.on("remove",function(){q.remove(),q=null}),d.addKeys(a,q),f.addContextualToolbars(a),e.setup(a),{iframeContainer:q.find("#iframe")[0].getEl(),editorContainer:q.getEl()}};return{render:p}}),g("l",["a"],function(a){return a("tinymce.ui.FloatPanel")}),g("7",["d","c","b","l","k","h","g","e","j"],function(a,b,c,d,e,f,g,h,i){var j=function(a,j,k){var l,m,n=a.settings,o=c.DOM;n.fixed_toolbar_container&&(m=o.select(n.fixed_toolbar_container)[0]);var p=function(){if(l&&l.moveRel&&l.visible()&&!l._fixed){var b=a.selection.getScrollContainer(),c=a.getBody(),d=0,e=0;if(b){var f=o.getPos(c),g=o.getPos(b);d=Math.max(0,g.x-f.x),e=Math.max(0,g.y-f.y)}l.fixed(!1).moveRel(c,a.rtl?["tr-br","br-tr"]:["tl-bl","bl-tl","tr-br"]).moveBy(d,e)}},q=function(){l&&(l.show(),p(),o.addClass(a.getBody(),"mce-edit-focus"))},r=function(){l&&(l.hide(),d.hideAll(),o.removeClass(a.getBody(),"mce-edit-focus"))},s=function(){return l?void(l.visible()||q()):(l=j.panel=b.create({type:m?"panel":"floatpanel",role:"application",classes:"tinymce tinymce-inline",layout:"flex",direction:"column",align:"stretch",autohide:!1,autofix:!0,fixed:!!m,border:1,items:[n.menubar===!1?null:{type:"menubar",border:"0 0 1 0",items:f.createMenuButtons(a)},e.createToolbars(a,n.toolbar_items_size)]}),a.fire("BeforeRenderUI"),l.renderTo(m||document.body).reflow(),h.addKeys(a,l),q(),g.addContextualToolbars(a),a.on("nodeChange",p),a.on("activate",q),a.on("deactivate",r),void a.nodeChanged())};return n.content_editable=!0,a.on("focus",function(){k.skinUiCss?o.styleSheetLoader.load(k.skinUiCss,s,s):s()}),a.on("blur hide",r),a.on("remove",function(){l&&(l.remove(),l=null)}),k.skinUiCss&&o.styleSheetLoader.load(k.skinUiCss,i.fireSkinLoaded(a)),{}};return{render:j}}),g("m",["a"],function(a){return a("tinymce.ui.Throbber")}),g("8",["m"],function(a){var b=function(b,c){var d;b.on("ProgressState",function(b){d=d||new a(c.panel.getEl("body")),b.state?d.show(b.time):d.hide()})};return{setup:b}}),g("0",["1","2","3","4","5","6","7","8","9"],function(a,b,c,d,e,f,g,h,i){var j=b.ThemeManager;e.appendTo(a.tinymce?a.tinymce:{});var k=function(a,b,d){var e=a.settings,i=e.skin!==!1&&(e.skin||"lightgray");if(i){var j=e.skin_url;j=j?a.documentBaseURI.toAbsolute(j):c.baseURL+"/skins/"+i,d.skinUiCss=j+"/skin.min.css",a.contentCSS.push(j+"/content"+(a.inline?".inline":"")+".min.css")}return h.setup(a,b),e.inline?g.render(a,b,d):f.render(a,b,d)};return j.add("modern",function(a){return{renderUI:function(b){return k(a,this,b)},resizeTo:function(b,c){return i.resizeTo(a,b,c)},resizeBy:function(b,c){return i.resizeBy(a,b,c)}}}),function(){}}),d("0")()}();