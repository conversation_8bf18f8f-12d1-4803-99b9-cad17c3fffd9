!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("9",Array),h("a",Error),g("1",["9","a"],function(a,b){var c=function(){},d=function(a,b){return function(){return a(b.apply(null,arguments))}},e=function(a){return function(){return a}},f=function(a){return a},g=function(a,b){return a===b},h=function(b){for(var c=new a(arguments.length-1),d=1;d<arguments.length;d++)c[d-1]=arguments[d];return function(){for(var d=new a(arguments.length),e=0;e<d.length;e++)d[e]=arguments[e];var f=c.concat(d);return b.apply(null,f)}},i=function(a){return function(){return!a.apply(null,arguments)}},j=function(a){return function(){throw new b(a)}},k=function(a){return a()},l=function(a){a()},m=e(!1),n=e(!0);return{noop:c,compose:d,constant:e,identity:f,tripleEquals:g,curry:h,not:i,die:j,apply:k,call:l,never:m,always:n}}),h("b",tinymce.util.Tools.resolve),g("2",["b"],function(a){return a("tinymce.dom.DOMUtils")}),g("3",["b"],function(a){return a("tinymce.PluginManager")}),g("4",["b"],function(a){return a("tinymce.util.JSON")}),g("5",["b"],function(a){return a("tinymce.util.Tools")}),g("6",["b"],function(a){return a("tinymce.util.XHR")}),g("7",[],function(){var a=function(a,b){if(a=""+a,a.length<b)for(var c=0;c<b-a.length;c++)a="0"+a;return a},b=function(b,c,d){var e="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),f="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),g="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),h="January February March April May June July August September October November December".split(" ");return d=d||new Date,c=c.replace("%D","%m/%d/%Y"),c=c.replace("%r","%I:%M:%S %p"),c=c.replace("%Y",""+d.getFullYear()),c=c.replace("%y",""+d.getYear()),c=c.replace("%m",a(d.getMonth()+1,2)),c=c.replace("%d",a(d.getDate(),2)),c=c.replace("%H",""+a(d.getHours(),2)),c=c.replace("%M",""+a(d.getMinutes(),2)),c=c.replace("%S",""+a(d.getSeconds(),2)),c=c.replace("%I",""+((d.getHours()+11)%12+1)),c=c.replace("%p",""+(d.getHours()<12?"AM":"PM")),c=c.replace("%B",""+b.translate(h[d.getMonth()])),c=c.replace("%b",""+b.translate(g[d.getMonth()])),c=c.replace("%A",""+b.translate(f[d.getDay()])),c=c.replace("%a",""+b.translate(e[d.getDay()])),c=c.replace("%%","%")};return{getDateTime:b}}),g("8",["5","6","7"],function(a,b,c){var d=function(a,c){return function(){var d=a.templates;return"function"==typeof d?void d(c):void("string"==typeof d?b.send({url:d,success:function(a){c(JSON.parse(a))}}):c(d))}},e=function(b,c,d){return a.each(b.getParam(d),function(a,b){"function"==typeof a&&(a=a(b)),c=c.replace(new RegExp("\\{\\$"+b+"\\}","g"),a)}),c},f=function(b,c){var d=b.dom,e=b.getParam("template_replace_values");a.each(d.select("*",c),function(b){a.each(e,function(a,c){d.hasClass(b,c)&&"function"==typeof e[c]&&e[c](b)})})},g=function(a,b){return new RegExp("\\b"+b+"\\b","g").test(a.className)},h=function(b,d,h){var i,j,k=b.dom,l=b.selection.getContent();h=e(b,h,"template_replace_values"),i=k.create("div",null,h),j=k.select(".mceTmpl",i),j&&j.length>0&&(i=k.create("div",null),i.appendChild(j[0].cloneNode(!0))),a.each(k.select("*",i),function(a){g(a,b.getParam("template_cdate_classes","cdate").replace(/\s+/g,"|"))&&(a.innerHTML=c.getDateTime(b,b.getParam("template_cdate_format",b.getLang("template.cdate_format")))),g(a,b.getParam("template_mdate_classes","mdate").replace(/\s+/g,"|"))&&(a.innerHTML=c.getDateTime(b,b.getParam("template_mdate_format",b.getLang("template.mdate_format")))),g(a,b.getParam("template_selected_content_classes","selcontent").replace(/\s+/g,"|"))&&(a.innerHTML=l)}),f(b,i),b.execCommand("mceInsertContent",!1,i.innerHTML),b.addVisual()};return{createTemplateList:d,replaceTemplateValues:e,replaceVals:f,insertTemplate:h}}),g("0",["1","2","3","4","5","6","7","8"],function(a,b,c,d,e,f,g,h){var i=function(a,b,c){if(c.indexOf("<html>")==-1){var d="";e.each(a.contentCSS,function(b){d+='<link type="text/css" rel="stylesheet" href="'+a.documentBaseURI.toAbsolute(b)+'">'});var f=a.settings.body_class||"";f.indexOf("=")!=-1&&(f=a.getParam("body_class","","hash"),f=f[a.id]||""),c="<!DOCTYPE html><html><head>"+d+'</head><body class="'+f+'">'+c+"</body></html>"}c=h.replaceTemplateValues(a,c,"template_preview_replace_values");var g=b.find("iframe")[0].getEl().contentWindow.document;g.open(),g.write(c),g.close()};return c.add("template",function(c){function d(a){var d,g,j=[];if(!a||0===a.length){var k=c.translate("No templates defined.");return void c.notificationManager.open({text:k,type:"info"})}e.each(a,function(a){j.push({selected:!j.length,text:a.title,value:{url:a.url,content:a.content,description:a.description}})});var l=function(a){var b=a.control.value();b.url?f.send({url:b.url,success:function(a){g=a,i(c,d,g)}}):(g=b.content,i(c,d,g)),d.find("#description")[0].text(a.control.value().description)};d=c.windowManager.open({title:"Insert template",layout:"flex",direction:"column",align:"stretch",padding:15,spacing:10,items:[{type:"form",flex:0,padding:0,items:[{type:"container",label:"Templates",items:{type:"listbox",label:"Templates",name:"template",values:j,onselect:l}}]},{type:"label",name:"description",label:"Description",text:"\xa0"},{type:"iframe",flex:1,border:1}],onsubmit:function(){h.insertTemplate(c,!1,g)},minWidth:Math.min(b.DOM.getViewPort().w,c.getParam("template_popup_width",600)),minHeight:Math.min(b.DOM.getViewPort().h,c.getParam("template_popup_height",500))}),d.find("listbox")[0].fire("select")}c.addCommand("mceInsertTemplate",a.curry(h.insertTemplate,c)),c.addButton("template",{title:"Insert template",onclick:h.createTemplateList(c.settings,d)}),c.addMenuItem("template",{text:"Template",onclick:h.createTemplateList(c.settings,d),context:"insert"}),c.on("PreProcess",function(a){var b=c.dom;e.each(b.select("div",a.node),function(a){b.hasClass(a,"mceTmpl")&&(e.each(b.select("*",a),function(a){b.hasClass(a,c.getParam("template_mdate_classes","mdate").replace(/\s+/g,"|"))&&(a.innerHTML=g.getDateTime(c,c.getParam("template_mdate_format",c.getLang("template.mdate_format"))))}),h.replaceVals(c,a))})})}),function(){}}),d("0")()}();