﻿tinymce.PluginManager.add("smileys",function(n,t){function e(){var n;return n='<table role="presentation" class="mce-grid">',tinymce.each(r,function(t){n+="<tr>";tinymce.each(t,function(t){n+='<td><a href="#" data-mce-url="'+t.url+'" tabindex="-1" title="'+t.title+'"><img src="'+t.url+'" style="width: 16px; height: 16px"><\/a><\/td>'});n+="<\/tr>"}),n+="<\/table>"}function o(n){var i=tinymce.each,t=[];return i(n,function(n){t=t.concat(n)}),t.length>0?t:n}function u(n,t,i,r,u){function y(n,t){var i,r;return t=t||0,i=n.index,t>0&&(r=n[t],i+=n[0].indexOf(r),n[0]=r),[i,i+n[0].length,[n[0]]]}function v(n){var t;if(n.nodeType===3)return n.data;if(h[n.nodeName]&&!f[n.nodeName])return"";if(t="",(f[n.nodeName]||c[n.nodeName])&&(t+="\n"),n=n.firstChild)do t+=v(n);while(n=n.nextSibling);return t}function p(n,t,i){var o,s,v,l,a=[],u=0,r=n,e=t.shift(),y=0;n:for(;;){if((f[r.nodeName]||c[r.nodeName])&&u++,r.nodeType===3&&(!s&&r.length+u>=e[1]?(s=r,l=e[1]-u):o&&a.push(r),!o&&r.length+u>e[0]&&(o=r,v=e[0]-u),u+=r.length),o&&s){if(r=i({startNode:o,startNodeIndex:v,endNode:s,endNodeIndex:l,innerNodes:a,match:e[2],matchIndex:y}),u-=s.length-l,o=null,s=null,a=[],e=t.shift(),y++,!e)break}else if((!h[r.nodeName]||f[r.nodeName])&&r.firstChild){r=r.firstChild;continue}else if(r.nextSibling){r=r.nextSibling;continue}for(;;)if(r.nextSibling){r=r.nextSibling;break}else if(r.parentNode!==n)r=r.parentNode;else break n}}function w(n){var t,i;return typeof n!="function"?(i=n.nodeType?n:o.createElement(n),t=function(){return i.cloneNode(!1)}):t=n,function(n){var f,e,r,s=n.startNode,h=n.endNode,i,u;if(s===h)return i=s,r=i.parentNode,n.startNodeIndex>0&&(f=o.createTextNode(i.data.substring(0,n.startNodeIndex)),r.insertBefore(f,i)),u=t(),r.insertBefore(u,i),n.endNodeIndex<i.length&&(e=o.createTextNode(i.data.substring(n.endNodeIndex)),r.insertBefore(e,i)),i.parentNode.removeChild(i),u}}var l,e=[],s,a=0,o,f,h,c;if(o=t.ownerDocument,f=u.getBlockElements(),h=u.getWhiteSpaceElements(),c=u.getShortEndedElements(),s=v(t),s){while(l=n.exec(s))e.push(y(l,r));return e.length&&(a=e.length,p(t,e,w(i))),a}}function s(t){var e=tinymce.each,r=n.selection.getNode(),i,f;if(typeof t.shortcut=="string")return f=t.shortcut.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&"),i=n.dom.create("img",{src:t.url,title:t.title}),u(new RegExp(f,"gi"),r,i,!1,n.schema);Array.isArray(t.shortcut)&&e(t.shortcut,function(f){return i=n.dom.create("img",{src:t.url,title:t.title}),u(new RegExp(f.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&"),"gi"),r,i,!1,n.schema)})}var f=[[{shortcut:"(^^^)",url:t+"/img/shark.gif",title:"shark"},{shortcut:"O:)",url:t+"/img/angel.png",title:"angel"},{shortcut:"o.O",url:t+"/img/confused.png",title:"confused"},{shortcut:"3:)",url:t+"/img/devil.png",title:"devil"},{shortcut:":-O",url:t+"/img/gasp.png",title:"gasp"},{shortcut:"8-)",url:t+"/img/glasses.png",title:"glasses"},{shortcut:":-D",url:t+"/img/grin.png",title:"grin"}],[{shortcut:":-)",url:t+"/img/smile.png",title:"smile"},{shortcut:":'(",url:t+"/img/cry.png",title:"cry"},{shortcut:"<3",url:t+"/img/heart.png",title:"heart"},{shortcut:"^_^",url:t+"/img/kiki.png",title:"kiki"},{shortcut:":-*",url:t+"/img/kiss.png",title:"kiss"},{shortcut:":v",url:t+"/img/pacman.png",title:"pacman"},{shortcut:"<(�)",url:t+"/img/penguin.gif",title:"penguin"}],[{shortcut:":|]",url:t+"/img/robot.gif",title:"robot"},{shortcut:"-_-",url:t+"/img/squint.png",title:"squint"},{shortcut:"8-|",url:t+"/img/sunglasses.png",title:"sunglasses"},{shortcut:":-P",url:t+"/img/tongue.png",title:"tongue"},{shortcut:":/",url:t+"/img/unsure.png",title:"unsure"},{shortcut:">:O",url:t+"/img/upset.png",title:"upset"},{shortcut:">:(",url:t+"/img/grumpy.png",title:"grumpy"}]],i=n.settings.smileys||f,r=n.settings.extended_smileys?i.concat(n.settings.extended_smileys):i;n.on("keyup",function(){if(!!n.settings.auto_convert_smileys){var t=tinymce.each,i=n.selection,u=i.getNode();u&&t(o(r),function(n){s(n)})}});n.addButton("smileys",{type:"panelbutton",icon:"emoticons",panel:{autohide:!0,html:e,onclick:function(t){var i=n.dom.getParent(t.target,"a");i&&(n.insertContent('<img src="'+i.getAttribute("data-mce-url")+'" title="'+i.getAttribute("title")+'" />'),this.hide())}},tooltip:"Smileys"})});
/*
//# sourceMappingURL=plugin.min.js.map
*/