!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("4",tinymce.util.Tools.resolve),g("1",["4"],function(a){return a("tinymce.PluginManager")}),g("5",["4"],function(a){return a("tinymce.util.VK")}),g("a",["4"],function(a){return a("tinymce.util.Delay")}),g("b",["4"],function(a){return a("tinymce.util.Tools")}),g("c",["4"],function(a){return a("tinymce.util.XHR")}),g("9",[],function(){var a=function(a){return"boolean"==typeof a.link_assume_external_targets&&a.link_assume_external_targets},b=function(a){return"boolean"==typeof a.link_context_toolbar&&a.link_context_toolbar},c=function(a){return a.link_list},d=function(a){return"string"==typeof a.default_link_target},e=function(a){return a.default_link_target},f=function(a){return a.target_list},g=function(a,b){a.settings.target_list=b},h=function(a){return f(a)!==!1},i=function(a){return a.rel_list},j=function(a){return void 0!==i(a)},k=function(a){return a.link_class_list},l=function(a){return void 0!==k(a)},m=function(a){return a.link_title!==!1},n=function(a){return"boolean"==typeof a.allow_unsafe_link_target&&a.allow_unsafe_link_target};return{assumeExternalTargets:a,hasContextToolbar:b,getLinkList:c,hasDefaultLinkTarget:d,getDefaultLinkTarget:e,getTargetList:f,setTargetList:g,shouldShowTargetList:h,getRelList:i,hasRelList:j,getLinkClassList:k,hasLinkClassList:l,shouldShowLinkTitle:m,allowUnsafeLinkTarget:n}}),h("d",RegExp),g("8",["b","9","d"],function(a,b,c){var d=function(b,c){var d=["noopener"],e=b?b.split(/\s+/):[],f=function(b){return a.trim(b.sort().join(" "))},g=function(a){return a=h(a),a.length?a.concat(d):d},h=function(b){return b.filter(function(b){return a.inArray(d,b)===-1})};return e=c?g(e):h(e),e.length?f(e):null},e=function(a){return a.replace(/\uFEFF/g,"")},f=function(a,b){return b=b||a.selection.getStart(),k(b)?a.dom.select("a[href]",b)[0]:a.dom.getParent(b,"a[href]")},g=function(a,b){var c=b?b.innerText||b.textContent:a.getContent({format:"text"});return e(c)},h=function(a){return a&&"A"===a.nodeName&&a.href},i=function(b){return a.grep(b,h).length>0},j=function(a){return!(/</.test(a)&&(!/^<a [^>]+>[^<]+<\/a>$/.test(a)||a.indexOf("href=")==-1))},k=function(a){return a&&"FIGURE"===a.nodeName&&/\bimage\b/i.test(a.className)},l=function(a,c){return function(e){a.undoManager.transact(function(){var g=a.selection.getNode(),h=f(a,g),i={href:e.href,target:e.target?e.target:null,rel:e.rel?e.rel:null,"class":e["class"]?e["class"]:null,title:e.title?e.title:null};b.hasRelList(a.settings)||b.allowUnsafeLinkTarget(a.settings)!==!1||(i.rel=d(i.rel,"_blank"==i.target)),e.href===c.href&&(c.attach(),c={}),h?(a.focus(),e.hasOwnProperty("text")&&("innerText"in h?h.innerText=e.text:h.textContent=e.text),a.dom.setAttribs(h,i),a.selection.select(h),a.undoManager.add()):k(g)?o(a,g,i):e.hasOwnProperty("text")?a.insertContent(a.dom.createHTML("a",i,a.dom.encode(e.text))):a.execCommand("mceInsertLink",!1,i)})}},m=function(a){return function(){a.undoManager.transact(function(){var b=a.selection.getNode();k(b)?n(a,b):a.execCommand("unlink")})}},n=function(a,b){var c,d;d=a.dom.select("img",b)[0],d&&(c=a.dom.getParents(d,"a[href]",b)[0],c&&(c.parentNode.insertBefore(d,c),a.dom.remove(c)))},o=function(a,b,c){var d,e;e=a.dom.select("img",b)[0],e&&(d=a.dom.create("a",c),e.parentNode.insertBefore(d,e),d.appendChild(e))};return{link:l,unlink:m,isLink:h,hasLinks:i,isOnlyTextSelected:j,getAnchorElement:f,getAnchorText:g,toggleTargetRules:d}}),g("6",["a","b","c","8","9"],function(a,b,c,d,e){var f={},g=function(a,b){var d=e.getLinkList(a.settings);"string"==typeof d?c.send({url:d,success:function(c){b(a,JSON.parse(c))}}):"function"==typeof d?d(function(c){b(a,c)}):b(a,d)},h=function(a,c,d){var e=function(a,d){return d=d||[],b.each(a,function(a){var b={text:a.text||a.title};a.menu?b.menu=e(a.menu):(b.value=a.value,c&&c(b)),d.push(b)}),d};return e(a,d||[])},i=function(b,c,d){var e=b.selection.getRng();a.setEditorTimeout(b,function(){b.windowManager.confirm(c,function(a){b.selection.setRng(e),d(a)})})},j=function(a,c){var g,j,k,l,m,n,o,p,q,r,s,t={},u=a.selection,v=a.dom,w=function(a){var b=k.find("#text");(!b.value()||a.lastControl&&b.value()==a.lastControl.text())&&b.value(a.control.text()),k.find("#href").value(a.control.value())},x=function(c){var d=[];if(b.each(a.dom.select("a:not([href])"),function(a){var b=a.name||a.id;b&&d.push({text:b,value:"#"+b,selected:c.indexOf("#"+b)!=-1})}),d.length)return d.unshift({text:"None",value:""}),{name:"anchor",type:"listbox",label:"Anchors",values:d,onselect:w}},y=function(){j||!l||t.text||this.parent().parent().find("#text")[0].value(this.value())},z=function(c){var d=c.meta||{};n&&n.value(a.convertURL(this.value(),"href")),b.each(c.meta,function(a,b){var c=k.find("#"+b);"text"===b?0===j.length&&(c.value(a),t.text=a):c.value(a)}),d.attach&&(f={href:this.value(),attach:d.attach}),d.text||y.call(this)},A=function(a){a.meta=k.toJSON()};l=d.isOnlyTextSelected(u.getContent()),g=d.getAnchorElement(a),t.text=j=d.getAnchorText(a.selection,g),t.href=g?v.getAttrib(g,"href"):"",g?t.target=v.getAttrib(g,"target"):e.hasDefaultLinkTarget(a.settings)&&(t.target=e.getDefaultLinkTarget(a.settings)),(s=v.getAttrib(g,"rel"))&&(t.rel=s),(s=v.getAttrib(g,"class"))&&(t["class"]=s),(s=v.getAttrib(g,"title"))&&(t.title=s),l&&(m={name:"text",type:"textbox",size:40,label:"Text to display",onchange:function(){t.text=this.value()}}),c&&(n={type:"listbox",label:"Link list",values:h(c,function(b){b.value=a.convertURL(b.value||b.url,"href")},[{text:"None",value:""}]),onselect:w,value:a.convertURL(t.href,"href"),onPostRender:function(){n=this}}),e.shouldShowTargetList(a.settings)&&(void 0===e.getTargetList(a.settings)&&e.setTargetList(a,[{text:"None",value:""},{text:"New window",value:"_blank"}]),p={name:"target",type:"listbox",label:"Target",values:h(e.getTargetList(a.settings))}),e.hasRelList(a.settings)&&(o={name:"rel",type:"listbox",label:"Rel",values:h(e.getRelList(a.settings),function(b){e.allowUnsafeLinkTarget(a.settings)===!1&&(b.value=d.toggleTargetRules(b.value,"_blank"===t.target))})}),e.hasLinkClassList(a.settings)&&(q={name:"class",type:"listbox",label:"Class",values:h(e.getLinkClassList(a.settings),function(b){b.value&&(b.textStyle=function(){return a.formatter.getCssText({inline:"a",classes:[b.value]})})})}),e.shouldShowLinkTitle(a.settings)&&(r={name:"title",type:"textbox",label:"Title",value:t.title}),k=a.windowManager.open({title:"Insert link",data:t,body:[{name:"href",type:"filepicker",filetype:"file",size:40,autofocus:!0,label:"Url",onchange:z,onkeyup:y,onbeforecall:A},m,r,x(t.href),n,o,p,q],onSubmit:function(c){var g=e.assumeExternalTargets(a.settings),h=d.link(a,f),k=d.unlink(a),m=b.extend({},t,c.data),n=m.href;return n?(l&&m.text!==j||delete m.text,n.indexOf("@")>0&&n.indexOf("//")==-1&&n.indexOf("mailto:")==-1?void i(a,"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",function(a){a&&(m.href="mailto:"+n),h(m)}):g===!0&&!/^\w+:/i.test(n)||g===!1&&/^\s*www[\.|\d\.]/i.test(n)?void i(a,"The URL you entered seems to be an external link. Do you want to add the required http:// prefix?",function(a){a&&(m.href="http://"+n),h(m)}):void h(m)):void k()}})},k=function(a){g(a,j)};return{open:k}}),g("e",["4"],function(a){return a("tinymce.dom.DOMUtils")}),g("f",["4"],function(a){return a("tinymce.Env")}),g("7",["e","f"],function(a,b){var c=function(a,b){document.body.appendChild(a),a.dispatchEvent(b),document.body.removeChild(a)},d=function(d){if(!b.ie||b.ie>10){var e=document.createElement("a");e.target="_blank",e.href=d,e.rel="noreferrer noopener";var f=document.createEvent("MouseEvents");f.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),c(e,f)}else{var g=window.open("","_blank");if(g){g.opener=null;var h=g.document;h.open(),h.write('<meta http-equiv="refresh" content="0; url='+a.DOM.encode(d)+'">'),h.close()}}};return{open:d}}),g("2",["5","6","7","8","9"],function(a,b,c,d,e){var f=function(a,b){return a.dom.getParent(b,"a[href]")},g=function(a){return f(a,a.selection.getStart())},h=function(a){var b=a.getAttribute("data-mce-href");return b?b:a.getAttribute("href")},i=function(a){var b=a.plugins.contextmenu;return!!b&&b.isContextMenuVisible()},j=function(a){return a.altKey===!0&&a.shiftKey===!1&&a.ctrlKey===!1&&a.metaKey===!1},k=function(a,b){if(b){var d=h(b);if(/^#/.test(d)){var e=a.$(d);e.length&&a.selection.scrollIntoView(e[0],!0)}else c.open(b.href)}},l=function(a){return function(){b.open(a)}},m=function(a){return function(){k(a,g(a))}},n=function(a){return function(b){var c,f,g;return!!(e.hasContextToolbar(a.settings)&&!i(a)&&d.isLink(b)&&(c=a.selection,f=c.getRng(),g=f.startContainer,3==g.nodeType&&c.isCollapsed()&&f.startOffset>0&&f.startOffset<g.data.length))}},o=function(b){b.on("click",function(c){var d=f(b,c.target);d&&a.metaKeyPressed(c)&&(c.preventDefault(),k(b,d))}),b.on("keydown",function(a){var c=g(b);c&&13===a.keyCode&&j(a)&&(a.preventDefault(),k(b,c))})},p=function(a){return function(){var b=this;a.on("nodechange",function(c){b.active(!a.readonly&&!!d.getAnchorElement(a,c.element))})}},q=function(a){return function(){var b=this,c=function(a){d.hasLinks(a.parents)?b.show():b.hide()};d.hasLinks(a.dom.getParents(a.selection.getStart()))||b.hide(),a.on("nodechange",c),b.on("remove",function(){a.off("nodechange",c)})}};return{openDialog:l,gotoSelectedLink:m,leftClickedOnAHref:n,setupGotoLinks:o,toggleActiveState:p,toggleViewLinkState:q}}),g("3",["2","8"],function(a,b){var c=function(c){c.addButton("link",{icon:"link",tooltip:"Insert/edit link",shortcut:"Meta+K",onclick:a.openDialog(c),onpostrender:a.toggleActiveState(c)}),c.addButton("unlink",{icon:"unlink",tooltip:"Remove link",onclick:b.unlink(c),onpostrender:a.toggleActiveState(c)}),c.addContextToolbar&&c.addButton("openlink",{icon:"newtab",tooltip:"Open link",onclick:a.gotoSelectedLink(c)})},d=function(b){b.addMenuItem("openlink",{text:"Open link",icon:"newtab",onclick:a.gotoSelectedLink(b),onPostRender:a.toggleViewLinkState(b),prependToContext:!0}),b.addMenuItem("link",{icon:"link",text:"Link",shortcut:"Meta+K",onclick:a.openDialog(b),stateSelector:"a[href]",context:"insert",prependToContext:!0})},e=function(b){b.addContextToolbar&&b.addContextToolbar(a.leftClickedOnAHref(b),"openlink | link unlink")};return{setupButtons:c,setupMenuItems:d,setupContextToolbars:e}}),g("0",["1","2","3"],function(a,b,c){return a.add("link",function(a){c.setupButtons(a),c.setupMenuItems(a),c.setupContextToolbars(a),b.setupGotoLinks(a),a.addShortcut("Meta+K","",b.openDialog(a)),a.addCommand("mceLink",b.openDialog(a))}),function(){}}),d("0")()}();