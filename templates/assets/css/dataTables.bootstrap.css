﻿.DTTTFooter {
    margin: 0;
    background: #fff;
    overflow: hidden;
    padding: 5px 5px 2px 10px;
    border: 1px solid #ddd;
    border-top: 0px;
    background-color: #eee;
    background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgeG1sbnM9Imh0d…0iMSIgaGVpZ2h0PSIxIiBmaWxsPSJ1cmwoI2xlc3NoYXQtZ2VuZXJhdGVkKSIgLz48L3N2Zz4=);
    background-image: -webkit-linear-gradient(top, #f2f2f2 0, #fafafa 100%);
    background-image: -moz-linear-gradient(top, #f2f2f2 0, #fafafa 100%);
    background-image: linear-gradient(to bottom, #f2f2f2 0, #fafafa 100%);
}

    .DTTTFooter .col-sm-6 {
        padding: 0px;
    }

.dataTables_wrapper {
    position: relative;
}

div.dataTables_length {
    position: absolute;
    top: 0px;
    right: 0px;
}


    div.dataTables_length select {
        width: 75px;
    }

div.dataTables_filter label {
    font-weight: normal;
    float: left;
    margin-bottom: 10px;
    position: relative;
}

.dataTables_filter label:before {
    font-family: "FontAwesome";
    content: "\f002";
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0px;
    width: 30px;
    max-width: 30px;
    overflow: hidden;
    color: #5db2ff;
    text-align: center;
    padding-top: 6px;
}

.dataTables_filter input {
    width: 16em;
    padding-left: 28px;
}



div.dataTables_info {
    padding-top: 8px;
}

div.dataTables_paginate {
    float: right;
    margin: 0;
}

    div.dataTables_paginate ul.pagination {
        margin: 2px;
    }

table.table {
    clear: both;
    max-width: none !important;
}

    table.table thead .sorting,
    table.table thead .sorting_asc,
    table.table thead .sorting_desc,
    table.table thead .sorting_asc_disabled,
    table.table thead .sorting_desc_disabled {
        cursor: pointer;
    }

    table.table thead .sorting {
        background: url('../img/sort_both.png') no-repeat center right;
    }

    table.table thead .sorting_asc {
        background: url('../img/sort_asc.png') no-repeat center right;
    }

    table.table thead .sorting_desc {
        background: url('../img/sort_desc.png') no-repeat center right;
    }

    table.table thead .sorting_asc_disabled {
        background: url('../img/sort_asc_disabled.png') no-repeat center right;
    }

    table.table thead .sorting_desc_disabled {
        background: url('../img/sort_desc_disabled.png') no-repeat center right;
    }

table.dataTable th:active {
    outline: none;
}

/* Scrolling */
div.dataTables_scrollHead table {
    margin-bottom: 0 !important;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

    div.dataTables_scrollHead table thead tr:last-child th:first-child,
    div.dataTables_scrollHead table thead tr:last-child td:first-child {
        border-bottom-left-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
    }

div.dataTables_scrollBody table {
    border-top: none;
    margin-bottom: 0 !important;
}

div.dataTables_scrollBody tbody tr:first-child th,
div.dataTables_scrollBody tbody tr:first-child td {
    border-top: none;
}

div.dataTables_scrollFoot table {
    border-top: none;
}

/*
 * TableTools styles
 */
table.DTTT_selectable tbody tr {
    cursor: pointer;
}

.DTTT.btn-group {
    position: absolute;
    right: 70px;
    top: 0px;
}

div.DTTT .btn {
    color: #333 !important;
    font-size: 12px;
}

    div.DTTT .btn:hover {
        text-decoration: none !important;
    }

ul.DTTT_dropdown.dropdown-menu {
    z-index: 2003;
}

    ul.DTTT_dropdown.dropdown-menu a {
        color: #333 !important; /* needed only when demo_page.css is included */
    }

    ul.DTTT_dropdown.dropdown-menu li {
        position: relative;
    }

        ul.DTTT_dropdown.dropdown-menu li:hover a {
            background-color: #0088cc;
            color: white !important;
        }

/* TableTools information display */
div.DTTT_print_info.modal {
    height: 150px;
    margin-top: -75px;
    text-align: center;
}

div.DTTT_print_info h6 {
    font-weight: normal;
    font-size: 28px;
    line-height: 28px;
    margin: 1em;
}

div.DTTT_print_info p {
    font-size: 14px;
    line-height: 20px;
}



/*
 * FixedColumns styles
 */
div.DTFC_LeftHeadWrapper table,
div.DTFC_LeftFootWrapper table,
div.DTFC_RightHeadWrapper table,
div.DTFC_RightFootWrapper table,
table.DTFC_Cloned tr.even {
    background-color: white;
}

div.DTFC_RightHeadWrapper table,
div.DTFC_LeftHeadWrapper table {
    margin-bottom: 0 !important;
    border-top-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}

    div.DTFC_RightHeadWrapper table thead tr:last-child th:first-child,
    div.DTFC_RightHeadWrapper table thead tr:last-child td:first-child,
    div.DTFC_LeftHeadWrapper table thead tr:last-child th:first-child,
    div.DTFC_LeftHeadWrapper table thead tr:last-child td:first-child {
        border-bottom-left-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
    }

div.DTFC_RightBodyWrapper table,
div.DTFC_LeftBodyWrapper table {
    border-top: none;
    margin-bottom: 0 !important;
}

div.DTFC_RightBodyWrapper tbody tr:first-child th,
div.DTFC_RightBodyWrapper tbody tr:first-child td,
div.DTFC_LeftBodyWrapper tbody tr:first-child th,
div.DTFC_LeftBodyWrapper tbody tr:first-child td {
    border-top: none;
}

div.DTFC_RightFootWrapper table,
div.DTFC_LeftFootWrapper table {
    border-top: none;
}


.dataTable .row-details {
    margin-top: 3px;
    display: inline-block;
    cursor: pointer;
    width: 10px;
        font-size:14px;
    height: 14px;
}

.dataTable .details {
    background-color: #f5f5f5;
}

    .dataTable .details td,
    .dataTable .details th {
        padding: 4px;
        background-color: none;
        border: 0;
    }

    .dataTable .details tr:hover td,
    .dataTable .details tr:hover th {
        background-color: none;
    }

    .dataTable .details tr:nth-child(odd) td,
    .dataTable .details tr:nth-child(odd) th {
        background-color: #f5f5f5;
    }

    .dataTable .details tr:nth-child(even) td,
    .dataTable .details tr:nth-child(even) th {
        background-color: #f5f5f5;
    }

.buttons-print {
    cursor: pointer;
    vertical-align: middle;
    margin: 0;
    position: relative;
    display: inline-block;
    -webkit-box-shadow: 0 1px 0 rgba(0,0,0,.05);
    -moz-box-shadow: 0 1px 0 rgba(0,0,0,.05);
    box-shadow: 0 1px 0 rgba(0,0,0,.05);
    -webkit-transition: all .15s ease;
    -moz-transition: all .15s ease;
    -o-transition: all .15s ease;
    transition: all .15s ease;
    -webkit-border-radius: 2px;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 2px;
    -moz-background-clip: padding;
    border-radius: 2px;
    background-clip: padding-box;
    font-size: 12px;
    padding: 4px 9px;
    line-height: 1.39;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
    color: #444;
    background-color: #fff;
    border-color: #ccc;
    text-align: center;
    white-space: nowrap;
    touch-action: manipulation;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    right: 6em;
}
.buttons-print:before,
.buttons-print:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}