::selection {
    /*background: #C3BC57; !* WebKit/Blink Browsers *!*/
    background: #ccc; /* WebKit/Blink Browsers */
}

::-moz-selection {
    /*background: #C3BC57; !* Gecko Browsers *!*/
    background: #ccc; /* Gecko Browsers */
}

a {
    -webkit-transition: color .5s linear;
    -moz-transition: color .5s linear;
    -ms-transition: color .5s linear;
    -o-transition: color .5s linear;
    transition: color .5s linear;
}

.block-container {
    text-align: center;
    padding: 8%
}

#social {
    margin-top: 1em;
}

#social a {
    color: #c1c1c1;
    text-decoration: none;
    margin: 0 5px;
    z-index: 10;
}

#social a:hover {
    color: #8cc474;
}

#introduction {
    margin-bottom: 1em;
}

.decorator, .decorator-inner {
    background: url("/templates/assets/img/trbgsnso4.png") 60% 0;
    background-repeat: no-repeat;
    height: 8em;
    bottom: 3em;
    left: 2em;
    position: absolute;
    right: 0;
    overflow: hidden
}

.decorator-inner {
    background-position: -285px 120px;
    bottom: 0;
    height: 35em;
    left: 0;
}

#footer {
    background-color: #141414;
    bottom: 0;
    height: 5em;
    left: 0;
    position: absolute;
    right: 0;
    overflow: hidden;
    line-height: 3;
}

#footer a {
    color: #fff;
    text-decoration: none;
    font-weight: bold;
}

#footer a:hover {
    color: #c3bc57;
}

#logo-brand-back {
    left: 42.5%;
    position: absolute;
    width: 7em;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

#logo-brand {
    left: 42.5%;
    z-index: 10;
    position: absolute;
    width: 7em;
    display: block;
    margin-left: auto;
    margin-right: auto;
    -webkit-transition: opacity .5s linear;
    -moz-transition: opacity .5s linear;
    -ms-transition: opacity .5s linear;
    -o-transition: opacity .5s linear;
    transition: opacity .5s linear;
}

#logo-brand:hover {
    content: '';
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
}

#overview input {
    color: #53a93f;
}

.orders-container .orders-list .order-item.top .item-more {
    color: #53a93f !important;
}

.orders-container .orders-list .order-item.top:hover::before {
    background-color: #53a93f !important;
}
.navbar-default .navbar-nav > li > a:hover {
    color: #53a93f;
}