﻿/*Colors*/
.themeprimary {
  color: #ff8f32 !important;
}
.themesecondary {
  color: #7bd148 !important;
}
.themethirdcolor {
  color: #5db2ff !important;
}
.themefourthcolor {
  color: #e75b8d !important;
}
.themefifthcolor {
  color: #ffce55 !important;
}
/*Background Colors*/
.bg-themeprimary {
  background-color: #ff8f32 !important;
}
.bg-themesecondary {
  background-color: #7bd148 !important;
}
.bg-themethirdcolor {
  background-color: #5db2ff !important;
}
.bg-themefourthcolor {
  background-color: #e75b8d !important;
}
.bg-themefifthcolor {
  background-color: #ffce55 !important;
}
/*Before Colors*/
.before-themeprimary:before {
  background-color: #ff8f32 !important;
}
.before-themesecondary:before {
  background-color: #7bd148 !important;
}
.before-themethirdcolor:before {
  background-color: #5db2ff !important;
}
.before-themefourthcolor:before {
  background-color: #e75b8d !important;
}
.before-themefifthcolor:before {
  background-color: #ffce55 !important;
}
/*Border Colors*/
.bordered-themeprimary {
  border-color: #ff8f32 !important;
}
.bordered-themesecondary {
  border-color: #7bd148 !important;
}
.bordered-themethirdcolor {
  border-color: #5db2ff !important;
}
.bordered-themefourthcolor {
  border-color: #e75b8d !important;
}
.bordered-themefifthcolor {
  border-color: #ffce55 !important;
}
/*Typography*/
h1.row-title:before,
h2.row-title:before,
h3.row-title:before,
h4.row-title:before,
h5.row-title:before,
h6.row-title:before {
  background-color: #ff8f32;
}
/*Navbar*/
.navbar .navbar-inner {
  background: #ff8f32;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li > a .badge {
  box-shadow: 1px 1px 0 #ff8f32;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li .dropdown-menu.dropdown-messages li .message-time {
  color: #ff8f32;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li .dropdown-menu.dropdown-login-area > li .avatar-area .caption {
  background-color: #ff8f32;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li .dropdown-menu.dropdown-login-area > li.dropdown-footer {
  border-top: 3px solid #ff8f32;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li.open > a {
  background-color: #ff8f32;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area .login-area .avatar {
  border-left: 2px solid #7bd148;
}
.navbar .navbar-inner .navbar-header .navbar-account .setting-container input[type=checkbox] + .text:before {
  border-color: #ff8f32;
}
.navbar .navbar-inner .navbar-header .navbar-account .setting-container input[type=checkbox]:checked + .text:before {
  border-color: #ff8f32;
  color: #7bd148;
}
.navbar .navbar-inner .navbar-header .navbar-account.setting-open .setting {
  background-color: #ff8f32;
}
/*Sidebar*/
.page-sidebar:hover .sidebar-collapse .collapse-icon {
  color: #ff8f32;
}
.page-sidebar .sidebar-header-wrapper {
  margin-left: 5px;
  height: 40px;
  margin-bottom: 0;
  position: relative;
}
.page-sidebar .sidebar-header-wrapper .searchicon {
  color: #ff8f32;
}
.page-sidebar .sidebar-menu li:before {
  background-color: #ff8f32;
}
.page-sidebar .sidebar-menu li:not(.open):hover:before {
  background-color: #7bd148;
}
.page-sidebar .sidebar-collapse:before {
  background-color: #ff8f32;
}
.page-sidebar .sidebar-collapse .collapse-icon:hover {
  background-color: #ff8f32;
  color: #fff;
}
/*Page Header*/
.page-header .header-buttons a:before {
  background-color: #ff8f32;
}
.page-header .header-buttons a:hover {
  color: #ff8f32;
}
.page-header .header-buttons a.active {
  color: #ff8f32;
}
.page-header .header-buttons a.fullscreen:before {
  background-color: #7bd148;
}
.page-header .header-buttons a.fullscreen:hover,
.page-header .header-buttons a.fullscreen.active {
  color: #7bd148;
}
.page-header .header-buttons a.sidebar-toggler:before {
  background-color: #ff8f32;
}
.page-header .header-buttons a.sidebar-toggler:hover,
.page-header .header-buttons a.sidebar-toggler.active {
  color: #ff8f32;
}
/*Widget*/
.widget:hover .compact i {
  color: #ff8f32;
}
.widget-buttons.compact:hover {
  background-color: #ff8f32;
}
.widget-buttons.compact:hover i {
  color: #fff !important;
}
.widget-buttons.compact:before {
  background-color: #ff8f32;
}
/*Databoxes*/
.databox .databox-left {
  color: #ff8f32;
}
.databox.databox-vertical .databox-top {
  color: #ff8f32;
}
/*Tabs*/
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  border-top: 2px solid #ff8f32;
}
/*Primary Button*/
.btn-primary,
.btn-primary:focus {
  background-color: #ff8f32 !important;
  border-color: #ff8f32;
}
.btn-primary.dropdown-toggle {
  border-left-color: #fdb432 !important;
}
.btn-primary.active {
  background-color: #fd7732 !important;
  border-color: #fd7732;
}
.btn-primary.shiny {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMSAxIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJub25lIj48bGluZWFyR3JhZGllbnQgaWQ9Imxlc3NoYXQtZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iNSUiIHN0b3AtY29sb3I9IiNmZjhmMzIiIHN0b3Atb3BhY2l0eT0iMSIvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iI2ZkNzczMiIgc3RvcC1vcGFjaXR5PSIxIi8+PC9saW5lYXJHcmFkaWVudD48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMSIgaGVpZ2h0PSIxIiBmaWxsPSJ1cmwoI2xlc3NoYXQtZ2VuZXJhdGVkKSIgLz48L3N2Zz4=);
  background-image: -webkit-linear-gradient(center top, #ff8f32 5%, #fd7732 100%);
  background-image: -moz-linear-gradient(center top, #ff8f32 5%, #fd7732 100%);
  background-image: -o-linear-gradient(center top, #ff8f32 5%, #fd7732 100%);
  background-image: linear-gradient(center to bottom, #ff8f32 5%, #fd7732 100%);
}
.btn-primary:hover,
.open .btn-primary.dropdown-toggle {
  background-color: #f5a532 !important;
  border-color: #ff8f32;
}
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  background-color: #ff8f32 !important;
  border-color: #ff8f32;
}
/*Primary Label*/
.badge-primary,
.label-primary {
  background-color: #ff8f32 !important;
}
/*Popover*/
.popover-title {
  border-bottom: 3px solid #ff8f32;
}
/*tooltip*/
.tooltip-primary + .tooltip > .tooltip-inner {
  border: 1px solid #ff8f32;
  background-color: #ff8f32;
}
.tooltip-primary + .tooltip.top .tooltip-arrow {
  border-top-color: #ff8f32;
}
.tooltip-primary + .tooltip.right .tooltip-arrow {
  border-right-color: #ff8f32;
}
.tooltip-primary + .tooltip.left .tooltip-arrow {
  border-left-color: #ff8f32;
}
.tooltip-primary + .tooltip.bottom .tooltip-arrow {
  border-bottom-color: #ff8f32;
}
/*ProgressBar*/
.progress-bar {
  background-color: #ff8f32;
}
/*Notification*/
.toast-primary {
  background-color: #ff8f32 !important;
}
/*Modal*/
.modal-primary .modal-header {
  border-bottom: 3px solid #ff8f32;
}
/*Dropdown*/
.dropdown-primary li a:hover,
.dropdown-primary li a:focus,
.dropdown-primary li a:active,
.dropdown-primary li.active a,
.dropdown-primary li.active a:hover,
.dropdown-primary .dropdown-submenu:hover > a,
.nav-tabs .dropdown-primary li > a:focus {
  background: #ff8f32;
}
/*Pagination*/
.pagination > li.active > a,
.pagination > li.active > a:hover {
  background-color: #ff8f32;
  border-color: #ff8f32;
}
/*Table*/
.table thead.colored-primary > tr > th {
  background-color: #ff8f32;
}
.table thead.colored-primary.bordered-primary > tr > th {
  border-bottom: 3px solid #ff8f32;
}
@media only screen and (max-width: 800px) {
  .table thead.bordered-primary > tr > th {
    border-right: 3px solid #ff8f32;
  }
}
/*Nestable Lists*/
.dd-list > li.bordered-primary > .dd-handle {
  border-left-color: #ff8f32;
}
/*Treeview*/
.tree-loading {
  color: #ff8f32 !important;
}
/*Wizard*/
.wizard ul li.active .step {
  border-color: #ff8f32;
  color: #ff8f32;
}
.wizard ul li.active:before {
  background-color: #ff8f32;
}
/*Inputs*/
input[type=checkbox].colored-primary:checked + .text,
input[type=radio].colored-primary:checked + .text {
  color: #ff8f32;
}
input[type=checkbox].colored-primary:checked + .text:before,
input[type=radio].colored-primary:checked + .text:before {
  border-color: #ff8f32;
  color: #ff8f32;
}
input[type=checkbox].checkbox-slider.colored-primary + .text:after {
  border-color: #ff8f32;
}
input[type=checkbox].checkbox-slider.colored-primary:checked + .text:before {
  background-color: #ff8f32;
  border-color: #ff8f32;
  color: #fff;
}
input[type=checkbox].checkbox-slider.colored-primary:checked + .text:after {
  background-color: #ff8f32;
}
input[type=checkbox].checkbox-slider.colored-primary.slider-icon:checked + .text:after {
  color: #ff8f32;
}
input[type=checkbox].checkbox-slider.colored-primary.slider-icon + .text:after {
  color: #ff8f32;
}
input[type=checkbox].checkbox-slider.colored-primary.toggle:checked + .text:after {
  color: #ff8f32;
}
input[type=checkbox].checkbox-slider.colored-primary.toggle + .text:after {
  background-color: #ff8f32;
}
/*Slider*/
.noUi-connect {
  background: #ff8f32;
}
.ui-rangeSlider.valuelabel-primary .ui-rangeSlider-label {
  background-color: #ff8f32;
}
.ui-rangeSlider.valuelabel-primary .ui-rangeSlider-label-inner {
  border-top: 6px solid #ff8f32;
}
.ui-rangeSlider.silder-primary .ui-rangeSlider-leftArrow:hover .ui-rangeSlider-arrow-inner:before,
.ui-rangeSlider.silder-primary .ui-rangeSlider-rightArrow:hover .ui-rangeSlider-arrow-inner:before {
  color: #ff8f32 !important;
}
.ui-rangeSlider.silder-primary .ui-rangeSlider-bar {
  background: #ff8f32 !important;
}
.ui-rangeSlider-label {
  background-color: #7bd148;
}
.ui-rangeSlider-bar {
  background: #ff8f32;
}
.ui-rangeSlider-label-inner {
  border-top: 6px solid #7bd148;
}
/*Select2*/
.select2-results .select2-highlighted {
  background: #ff8f32;
}
.select2-container-multi .select2-choices .select2-search-choice {
  border: 1px solid #ff8f32;
  background-color: #ff8f32;
}
/*Tags Input*/
.bootstrap-tagsinput > span {
  border: 1px solid #ff8f32;
  background: #ff8f32;
}
/*DatePicker*/
.datepicker td.active,
.datepicker td.active:hover {
  background-color: #ff8f32;
}
.datepicker td span.active {
  background-color: #ff8f32;
}
/*DateRangePicker*/
.daterangepicker .ranges li {
  color: #ff8f32;
}
.daterangepicker .ranges li.active,
.daterangepicker .ranges li:hover {
  background: #ff8f32;
  border: 1px solid #ff8f32;
}
.daterangepicker td.active,
.daterangepicker td.active:hover {
  background-color: #ff8f32;
  border-color: #ff8f32;
}
/*Editors*/
.note-editor .dropdown-menu li a i {
  color: #ff8f32;
}
.note-editor .note-dropzone.hover {
  color: #ff8f32;
  border: 2px dashed #ff8f32;
}
/*Lockscreen*/
.lock-container .lock-box .btn-lock,
.lock-container .lock-box .btn-lock:hover {
  color: #ff8f32;
}
.lock-container .signinbox a {
  color: #ff8f32;
}
/*Pricing Tables*/
.pricing-container .plan .signup {
  background-color: #ff8f32;
}
.profile-container .profile-body .nav-tabs > li.active > a,
.profile-container .profile-body .nav-tabs > li.active > a:hover,
.profile-container .profile-body .nav-tabs > li.active > a:focus {
  border-bottom: 2px solid #ff8f32;
}
/*Comment*/
.comment .comment-footer a:hover {
  color: #ff8f32;
}
/*Mail*/
.mail-container .mail-header .header-buttons li a:hover {
  border-color: #ff8f32;
}
.mail-container .mail-header .header-buttons li a:hover i {
  color: #ff8f32;
}
.mail-container .mail-body .mail-info .mail-sender.mail-sender span,
.mail-container .mail-body .mail-info .mail-date.mail-sender span {
  color: #7bd148;
}
.mail-container .mail-body .mail-reply .reply-form > div a {
  color: #7bd148;
}
.mail-container .mail-body .mail-list .list-item:before {
  background-color: #ff8f32;
}
/*Calendar*/
.external-event {
  border-left: 4px solid #ff8f32;
}
/*Dashboard*/
.dashboard-box .box-header .deadline {
  border-top: 3px solid #ff8f32 !important;
}
.dashboard-box .box-progress .progress-handle {
  background-color: #ff8f32 !important;
}
.dashboard-box .box-progress .progress-handle:after {
  border-top-color: #ff8f32 !important;
}
.dashboard-box .box-days .day-container:hover {
  background-color: #ff8f32;
  border: 1px solid #ff8f32;
}
.dashboard-box .box-days .day-container.highlight {
  background-color: #ff8f32;
  border: 1px solid #ff8f32;
}
.dashboard-box .box-days .day-container.highlight:hover {
  color: #ff8f32;
}
.dashboard-box .box-days .day-container .day-more {
  color: #ff8f32;
}
/*Orders*/
.orders-container .orders-list .order-item:before {
  background-color: #ff8f32 !important;
}
.orders-container .orders-list .order-item.top:hover:before {
  background-color: #7bd148 !important;
}
.orders-container .orders-list .order-item.top .item-more {
  color: #7bd148 !important;
}
.orders-container .orders-list .order-item .item-more {
  color: #ff8f32 !important;
}
/*Error Pages*/
.body-404 {
  background-color: #ff8f32;
}
.error-container h1 {
  color: #ff8f32;
}
.return-btn:hover {
  color: #ff8f32;
}
.body-500 {
  background-color: #7bd148;
}
.body-500 .error-container h1 {
  color: #7bd148;
}
.body-500 .return-btn:hover {
  color: #7bd148;
}
.loading-container {
  background-color: #ff8f32 !important;
}
.breadcrumb > li > a {
  color: #ff8f32;
}
.page-chatbar .chatbar-contacts .contacts-search .searchicon {
  color: #ff8f32;
}
.page-chatbar .chatbar-messages .messages-contact .back i {
  color: #ff8f32;
}
/*# sourceMappingURL=orange.css.map */