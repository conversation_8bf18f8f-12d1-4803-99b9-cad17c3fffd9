﻿/*Colors*/
.themeprimary {
  color: #03b3b2 !important;
}
.themesecondary {
  color: #ed4e2a !important;
}
.themethirdcolor {
  color: #ffce55 !important;
}
.themefourthcolor {
  color: #a0d468 !important;
}
.themefifthcolor {
  color: #e75b8d !important;
}
/*Background Colors*/
.bg-themeprimary {
  background-color: #03b3b2 !important;
}
.bg-themesecondary {
  background-color: #ed4e2a !important;
}
.bg-themethirdcolor {
  background-color: #ffce55 !important;
}
.bg-themefourthcolor {
  background-color: #a0d468 !important;
}
.bg-themefifthcolor {
  background-color: #e75b8d !important;
}
/*Before Colors*/
.before-themeprimary:before {
  background-color: #03b3b2 !important;
}
.before-themesecondary:before {
  background-color: #ed4e2a !important;
}
.before-themethirdcolor:before {
  background-color: #ffce55 !important;
}
.before-themefourthcolor:before {
  background-color: #a0d468 !important;
}
.before-themefifthcolor:before {
  background-color: #e75b8d !important;
}
/*Border Colors*/
.bordered-themeprimary {
  border-color: #03b3b2 !important;
}
.bordered-themesecondary {
  border-color: #ed4e2a !important;
}
.bordered-themethirdcolor {
  border-color: #ffce55 !important;
}
.bordered-themefourthcolor {
  border-color: #a0d468 !important;
}
.bordered-themefifthcolor {
  border-color: #e75b8d !important;
}
/*Typography*/
h1.row-title:before,
h2.row-title:before,
h3.row-title:before,
h4.row-title:before,
h5.row-title:before,
h6.row-title:before {
  background-color: #03b3b2;
}
/*Navbar*/
.navbar .navbar-inner {
  background: #03b3b2;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li > a .badge {
  box-shadow: 1px 1px 0 #03b3b2;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li .dropdown-menu.dropdown-messages li .message-time {
  color: #03b3b2;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li .dropdown-menu.dropdown-login-area > li .avatar-area .caption {
  background-color: #03b3b2;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li .dropdown-menu.dropdown-login-area > li.dropdown-footer {
  border-top: 3px solid #03b3b2;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li.open > a {
  background-color: #03b3b2;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area .login-area .avatar {
  border-left: 2px solid #ed4e2a;
}
.navbar .navbar-inner .navbar-header .navbar-account .setting-container input[type=checkbox] + .text:before {
  border-color: #03b3b2;
}
.navbar .navbar-inner .navbar-header .navbar-account .setting-container input[type=checkbox]:checked + .text:before {
  border-color: #03b3b2;
  color: #ed4e2a;
}
.navbar .navbar-inner .navbar-header .navbar-account.setting-open .setting {
  background-color: #03b3b2;
}
/*Sidebar*/
.page-sidebar:hover .sidebar-collapse .collapse-icon {
  color: #03b3b2;
}
.page-sidebar .sidebar-header-wrapper {
  margin-left: 5px;
  height: 40px;
  margin-bottom: 0;
  position: relative;
}
.page-sidebar .sidebar-header-wrapper .searchicon {
  color: #03b3b2;
}
.page-sidebar .sidebar-menu li:before {
  background-color: #03b3b2;
}
.page-sidebar .sidebar-menu li:not(.open):hover:before {
  background-color: #ed4e2a;
}
.page-sidebar .sidebar-collapse:before {
  background-color: #03b3b2;
}
.page-sidebar .sidebar-collapse .collapse-icon:hover {
  background-color: #03b3b2;
  color: #fff;
}
/*Page Header*/
.page-header .header-buttons a:before {
  background-color: #03b3b2;
}
.page-header .header-buttons a:hover {
  color: #03b3b2;
}
.page-header .header-buttons a.active {
  color: #03b3b2;
}
.page-header .header-buttons a.fullscreen:before {
  background-color: #ed4e2a;
}
.page-header .header-buttons a.fullscreen:hover,
.page-header .header-buttons a.fullscreen.active {
  color: #ed4e2a;
}
.page-header .header-buttons a.sidebar-toggler:before {
  background-color: #03b3b2;
}
.page-header .header-buttons a.sidebar-toggler:hover,
.page-header .header-buttons a.sidebar-toggler.active {
  color: #03b3b2;
}
/*Widget*/
.widget:hover .compact i {
  color: #03b3b2;
}
.widget-buttons.compact:hover {
  background-color: #03b3b2;
}
.widget-buttons.compact:hover i {
  color: #fff !important;
}
.widget-buttons.compact:before {
  background-color: #03b3b2;
}
/*Databoxes*/
.databox .databox-left {
  color: #03b3b2;
}
.databox.databox-vertical .databox-top {
  color: #03b3b2;
}
/*Tabs*/
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  border-top: 2px solid #03b3b2;
}
/*Primary Button*/
.btn-primary,
.btn-primary:focus {
  background-color: #03b3b2 !important;
  border-color: #03b3b2;
}
.btn-primary.dropdown-toggle {
  border-left-color: #06d8d7 !important;
}
.btn-primary.active {
  background-color: #00908f !important;
  border-color: #00908f;
}
.btn-primary.shiny {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMSAxIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJub25lIj48bGluZWFyR3JhZGllbnQgaWQ9Imxlc3NoYXQtZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iNSUiIHN0b3AtY29sb3I9IiMwM2IzYjIiIHN0b3Atb3BhY2l0eT0iMSIvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzAwOTA4ZiIgc3RvcC1vcGFjaXR5PSIxIi8+PC9saW5lYXJHcmFkaWVudD48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMSIgaGVpZ2h0PSIxIiBmaWxsPSJ1cmwoI2xlc3NoYXQtZ2VuZXJhdGVkKSIgLz48L3N2Zz4=);
  background-image: -webkit-linear-gradient(center top, #03b3b2 5%, #00908f 100%);
  background-image: -moz-linear-gradient(center top, #03b3b2 5%, #00908f 100%);
  background-image: -o-linear-gradient(center top, #03b3b2 5%, #00908f 100%);
  background-image: linear-gradient(center to bottom, #03b3b2 5%, #00908f 100%);
}
.btn-primary:hover,
.open .btn-primary.dropdown-toggle {
  background-color: #16c5b2 !important;
  border-color: #03b3b2;
}
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  background-color: #03b3b2 !important;
  border-color: #03b3b2;
}
/*Primary Label*/
.badge-primary,
.label-primary {
  background-color: #03b3b2 !important;
}
/*Popover*/
.popover-title {
  border-bottom: 3px solid #03b3b2;
}
/*tooltip*/
.tooltip-primary + .tooltip > .tooltip-inner {
  border: 1px solid #03b3b2;
  background-color: #03b3b2;
}
.tooltip-primary + .tooltip.top .tooltip-arrow {
  border-top-color: #03b3b2;
}
.tooltip-primary + .tooltip.right .tooltip-arrow {
  border-right-color: #03b3b2;
}
.tooltip-primary + .tooltip.left .tooltip-arrow {
  border-left-color: #03b3b2;
}
.tooltip-primary + .tooltip.bottom .tooltip-arrow {
  border-bottom-color: #03b3b2;
}
/*ProgressBar*/
.progress-bar {
  background-color: #03b3b2;
}
/*Notification*/
.toast-primary {
  background-color: #03b3b2 !important;
}
/*Modal*/
.modal-primary .modal-header {
  border-bottom: 3px solid #03b3b2;
}
/*Dropdown*/
.dropdown-primary li a:hover,
.dropdown-primary li a:focus,
.dropdown-primary li a:active,
.dropdown-primary li.active a,
.dropdown-primary li.active a:hover,
.dropdown-primary .dropdown-submenu:hover > a,
.nav-tabs .dropdown-primary li > a:focus {
  background: #03b3b2;
}
/*Pagination*/
.pagination > li.active > a,
.pagination > li.active > a:hover {
  background-color: #03b3b2;
  border-color: #03b3b2;
}
/*Table*/
.table thead.colored-primary > tr > th {
  background-color: #03b3b2;
}
.table thead.colored-primary.bordered-primary > tr > th {
  border-bottom: 3px solid #03b3b2;
}
@media only screen and (max-width: 800px) {
  .table thead.bordered-primary > tr > th {
    border-right: 3px solid #03b3b2;
  }
}
/*Nestable Lists*/
.dd-list > li.bordered-primary > .dd-handle {
  border-left-color: #03b3b2;
}
/*Treeview*/
.tree-loading {
  color: #03b3b2 !important;
}
/*Wizard*/
.wizard ul li.active .step {
  border-color: #03b3b2;
  color: #03b3b2;
}
.wizard ul li.active:before {
  background-color: #03b3b2;
}
/*Inputs*/
input[type=checkbox].colored-primary:checked + .text,
input[type=radio].colored-primary:checked + .text {
  color: #03b3b2;
}
input[type=checkbox].colored-primary:checked + .text:before,
input[type=radio].colored-primary:checked + .text:before {
  border-color: #03b3b2;
  color: #03b3b2;
}
input[type=checkbox].checkbox-slider.colored-primary + .text:after {
  border-color: #03b3b2;
}
input[type=checkbox].checkbox-slider.colored-primary:checked + .text:before {
  background-color: #03b3b2;
  border-color: #03b3b2;
  color: #fff;
}
input[type=checkbox].checkbox-slider.colored-primary:checked + .text:after {
  background-color: #03b3b2;
}
input[type=checkbox].checkbox-slider.colored-primary.slider-icon:checked + .text:after {
  color: #03b3b2;
}
input[type=checkbox].checkbox-slider.colored-primary.slider-icon + .text:after {
  color: #03b3b2;
}
input[type=checkbox].checkbox-slider.colored-primary.toggle:checked + .text:after {
  color: #03b3b2;
}
input[type=checkbox].checkbox-slider.colored-primary.toggle + .text:after {
  background-color: #03b3b2;
}
/*Slider*/
.noUi-connect {
  background: #03b3b2;
}
.ui-rangeSlider.valuelabel-primary .ui-rangeSlider-label {
  background-color: #03b3b2;
}
.ui-rangeSlider.valuelabel-primary .ui-rangeSlider-label-inner {
  border-top: 6px solid #03b3b2;
}
.ui-rangeSlider.silder-primary .ui-rangeSlider-leftArrow:hover .ui-rangeSlider-arrow-inner:before,
.ui-rangeSlider.silder-primary .ui-rangeSlider-rightArrow:hover .ui-rangeSlider-arrow-inner:before {
  color: #03b3b2 !important;
}
.ui-rangeSlider.silder-primary .ui-rangeSlider-bar {
  background: #03b3b2 !important;
}
.ui-rangeSlider-label {
  background-color: #ed4e2a;
}
.ui-rangeSlider-bar {
  background: #03b3b2;
}
.ui-rangeSlider-label-inner {
  border-top: 6px solid #ed4e2a;
}
/*Select2*/
.select2-results .select2-highlighted {
  background: #03b3b2;
}
.select2-container-multi .select2-choices .select2-search-choice {
  border: 1px solid #03b3b2;
  background-color: #03b3b2;
}
/*Tags Input*/
.bootstrap-tagsinput > span {
  border: 1px solid #03b3b2;
  background: #03b3b2;
}
/*DatePicker*/
.datepicker td.active,
.datepicker td.active:hover {
  background-color: #03b3b2;
}
.datepicker td span.active {
  background-color: #03b3b2;
}
/*DateRangePicker*/
.daterangepicker .ranges li {
  color: #03b3b2;
}
.daterangepicker .ranges li.active,
.daterangepicker .ranges li:hover {
  background: #03b3b2;
  border: 1px solid #03b3b2;
}
.daterangepicker td.active,
.daterangepicker td.active:hover {
  background-color: #03b3b2;
  border-color: #03b3b2;
}
/*Editors*/
.note-editor .dropdown-menu li a i {
  color: #03b3b2;
}
.note-editor .note-dropzone.hover {
  color: #03b3b2;
  border: 2px dashed #03b3b2;
}
/*Lockscreen*/
.lock-container .lock-box .btn-lock,
.lock-container .lock-box .btn-lock:hover {
  color: #03b3b2;
}
.lock-container .signinbox a {
  color: #03b3b2;
}
/*Pricing Tables*/
.pricing-container .plan .signup {
  background-color: #03b3b2;
}
.profile-container .profile-body .nav-tabs > li.active > a,
.profile-container .profile-body .nav-tabs > li.active > a:hover,
.profile-container .profile-body .nav-tabs > li.active > a:focus {
  border-bottom: 2px solid #03b3b2;
}
/*Comment*/
.comment .comment-footer a:hover {
  color: #03b3b2;
}
/*Mail*/
.mail-container .mail-header .header-buttons li a:hover {
  border-color: #03b3b2;
}
.mail-container .mail-header .header-buttons li a:hover i {
  color: #03b3b2;
}
.mail-container .mail-body .mail-info .mail-sender.mail-sender span,
.mail-container .mail-body .mail-info .mail-date.mail-sender span {
  color: #ed4e2a;
}
.mail-container .mail-body .mail-reply .reply-form > div a {
  color: #ed4e2a;
}
.mail-container .mail-body .mail-list .list-item:before {
  background-color: #03b3b2;
}
/*Calendar*/
.external-event {
  border-left: 4px solid #03b3b2;
}
/*Dashboard*/
.dashboard-box .box-header .deadline {
  border-top: 3px solid #03b3b2 !important;
}
.dashboard-box .box-progress .progress-handle {
  background-color: #03b3b2 !important;
}
.dashboard-box .box-progress .progress-handle:after {
  border-top-color: #03b3b2 !important;
}
.dashboard-box .box-days .day-container:hover {
  background-color: #03b3b2;
  border: 1px solid #03b3b2;
}
.dashboard-box .box-days .day-container.highlight {
  background-color: #03b3b2;
  border: 1px solid #03b3b2;
}
.dashboard-box .box-days .day-container.highlight:hover {
  color: #03b3b2;
}
.dashboard-box .box-days .day-container .day-more {
  color: #03b3b2;
}
/*Orders*/
.orders-container .orders-list .order-item:before {
  background-color: #03b3b2 !important;
}
.orders-container .orders-list .order-item.top:hover:before {
  background-color: #ed4e2a !important;
}
.orders-container .orders-list .order-item.top .item-more {
  color: #ed4e2a !important;
}
.orders-container .orders-list .order-item .item-more {
  color: #03b3b2 !important;
}
/*Error Pages*/
.body-404 {
  background-color: #03b3b2;
}
.error-container h1 {
  color: #03b3b2;
}
.return-btn:hover {
  color: #03b3b2;
}
.body-500 {
  background-color: #ed4e2a;
}
.body-500 .error-container h1 {
  color: #ed4e2a;
}
.body-500 .return-btn:hover {
  color: #ed4e2a;
}
.loading-container {
  background-color: #03b3b2 !important;
}
.breadcrumb > li > a {
  color: #03b3b2;
}
.page-chatbar .chatbar-contacts .contacts-search .searchicon {
  color: #03b3b2;
}
.page-chatbar .chatbar-messages .messages-contact .back i {
  color: #03b3b2;
}
/*# sourceMappingURL=teal.css.map */