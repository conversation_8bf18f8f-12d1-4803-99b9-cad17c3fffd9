﻿/*Colors*/
.themeprimary {
  color: #474544 !important;
}
.themesecondary {
  color: #d73d32 !important;
}
.themethirdcolor {
  color: #ffce55 !important;
}
.themefourthcolor {
  color: #a0d468 !important;
}
.themefifthcolor {
  color: #e75b8d !important;
}
/*Background Colors*/
.bg-themeprimary {
  background-color: #474544 !important;
}
.bg-themesecondary {
  background-color: #d73d32 !important;
}
.bg-themethirdcolor {
  background-color: #ffce55 !important;
}
.bg-themefourthcolor {
  background-color: #a0d468 !important;
}
.bg-themefifthcolor {
  background-color: #e75b8d !important;
}
/*Before Colors*/
.before-themeprimary:before {
  background-color: #474544 !important;
}
.before-themesecondary:before {
  background-color: #d73d32 !important;
}
.before-themethirdcolor:before {
  background-color: #ffce55 !important;
}
.before-themefourthcolor:before {
  background-color: #a0d468 !important;
}
.before-themefifthcolor:before {
  background-color: #e75b8d !important;
}
/*Border Colors*/
.bordered-themeprimary {
  border-color: #474544 !important;
}
.bordered-themesecondary {
  border-color: #d73d32 !important;
}
.bordered-themethirdcolor {
  border-color: #ffce55 !important;
}
.bordered-themefourthcolor {
  border-color: #a0d468 !important;
}
.bordered-themefifthcolor {
  border-color: #e75b8d !important;
}
/*Typography*/
h1.row-title:before,
h2.row-title:before,
h3.row-title:before,
h4.row-title:before,
h5.row-title:before,
h6.row-title:before {
  background-color: #474544;
}
/*Navbar*/
.navbar .navbar-inner {
  background: #474544;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li > a .badge {
  box-shadow: 1px 1px 0 #474544;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li .dropdown-menu.dropdown-messages li .message-time {
  color: #474544;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li .dropdown-menu.dropdown-login-area > li .avatar-area .caption {
  background-color: #474544;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li .dropdown-menu.dropdown-login-area > li.dropdown-footer {
  border-top: 3px solid #474544;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li.open > a {
  background-color: #474544;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area .login-area .avatar {
  border-left: 2px solid #d73d32;
}
.navbar .navbar-inner .navbar-header .navbar-account .setting-container input[type=checkbox] + .text:before {
  border-color: #474544;
}
.navbar .navbar-inner .navbar-header .navbar-account .setting-container input[type=checkbox]:checked + .text:before {
  border-color: #474544;
  color: #d73d32;
}
.navbar .navbar-inner .navbar-header .navbar-account.setting-open .setting {
  background-color: #474544;
}
/*Sidebar*/
.page-sidebar:hover .sidebar-collapse .collapse-icon {
  color: #474544;
}
.page-sidebar .sidebar-header-wrapper {
  margin-left: 5px;
  height: 40px;
  margin-bottom: 0;
  position: relative;
}
.page-sidebar .sidebar-header-wrapper .searchicon {
  color: #474544;
}
.page-sidebar .sidebar-menu li:before {
  background-color: #474544;
}
.page-sidebar .sidebar-menu li:not(.open):hover:before {
  background-color: #d73d32;
}
.page-sidebar .sidebar-collapse:before {
  background-color: #474544;
}
.page-sidebar .sidebar-collapse .collapse-icon:hover {
  background-color: #474544;
  color: #fff;
}
/*Page Header*/
.page-header .header-buttons a:before {
  background-color: #474544;
}
.page-header .header-buttons a:hover {
  color: #474544;
}
.page-header .header-buttons a.active {
  color: #474544;
}
.page-header .header-buttons a.fullscreen:before {
  background-color: #d73d32;
}
.page-header .header-buttons a.fullscreen:hover,
.page-header .header-buttons a.fullscreen.active {
  color: #d73d32;
}
.page-header .header-buttons a.sidebar-toggler:before {
  background-color: #474544;
}
.page-header .header-buttons a.sidebar-toggler:hover,
.page-header .header-buttons a.sidebar-toggler.active {
  color: #474544;
}
/*Widget*/
.widget:hover .compact i {
  color: #474544;
}
.widget-buttons.compact:hover {
  background-color: #474544;
}
.widget-buttons.compact:hover i {
  color: #fff !important;
}
.widget-buttons.compact:before {
  background-color: #474544;
}
/*Databoxes*/
.databox .databox-left {
  color: #474544;
}
.databox.databox-vertical .databox-top {
  color: #474544;
}
/*Tabs*/
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  border-top: 2px solid #474544;
}
/*Primary Button*/
.btn-primary,
.btn-primary:focus {
  background-color: #474544 !important;
  border-color: #474544;
}
.btn-primary.dropdown-toggle {
  border-left-color: #bebdbd !important;
}
.btn-primary.active {
  background-color: #282726 !important;
  border-color: #282726;
}
.btn-primary.shiny {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMSAxIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJub25lIj48bGluZWFyR3JhZGllbnQgaWQ9Imxlc3NoYXQtZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iNSUiIHN0b3AtY29sb3I9IiM0NzQ1NDQiIHN0b3Atb3BhY2l0eT0iMSIvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzI4MjcyNiIgc3RvcC1vcGFjaXR5PSIxIi8+PC9saW5lYXJHcmFkaWVudD48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMSIgaGVpZ2h0PSIxIiBmaWxsPSJ1cmwoI2xlc3NoYXQtZ2VuZXJhdGVkKSIgLz48L3N2Zz4=);
  background-image: -webkit-linear-gradient(center top, #474544 5%, #282726 100%);
  background-image: -moz-linear-gradient(center top, #474544 5%, #282726 100%);
  background-image: -o-linear-gradient(center top, #474544 5%, #282726 100%);
  background-image: linear-gradient(center to bottom, #474544 5%, #282726 100%);
}
.btn-primary:hover,
.open .btn-primary.dropdown-toggle {
  background-color: #5b5958 !important;
  border-color: #474544;
}
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  background-color: #474544 !important;
  border-color: #474544;
}
/*Primary Label*/
.badge-primary,
.label-primary {
  background-color: #474544 !important;
}
/*Popover*/
.popover-title {
  border-bottom: 3px solid #474544;
}
/*tooltip*/
.tooltip-primary + .tooltip > .tooltip-inner {
  border: 1px solid #474544;
  background-color: #474544;
}
.tooltip-primary + .tooltip.top .tooltip-arrow {
  border-top-color: #474544;
}
.tooltip-primary + .tooltip.right .tooltip-arrow {
  border-right-color: #474544;
}
.tooltip-primary + .tooltip.left .tooltip-arrow {
  border-left-color: #474544;
}
.tooltip-primary + .tooltip.bottom .tooltip-arrow {
  border-bottom-color: #474544;
}
/*ProgressBar*/
.progress-bar {
  background-color: #474544;
}
/*Notification*/
.toast-primary {
  background-color: #474544 !important;
}
/*Modal*/
.modal-primary .modal-header {
  border-bottom: 3px solid #474544;
}
/*Dropdown*/
.dropdown-primary li a:hover,
.dropdown-primary li a:focus,
.dropdown-primary li a:active,
.dropdown-primary li.active a,
.dropdown-primary li.active a:hover,
.dropdown-primary .dropdown-submenu:hover > a,
.nav-tabs .dropdown-primary li > a:focus {
  background: #474544;
}
/*Pagination*/
.pagination > li.active > a,
.pagination > li.active > a:hover {
  background-color: #474544;
  border-color: #474544;
}
/*Table*/
.table thead.colored-primary > tr > th {
  background-color: #474544;
}
.table thead.colored-primary.bordered-primary > tr > th {
  border-bottom: 3px solid #474544;
}
@media only screen and (max-width: 800px) {
  .table thead.bordered-primary > tr > th {
    border-right: 3px solid #474544;
  }
}
/*Nestable Lists*/
.dd-list > li.bordered-primary > .dd-handle {
  border-left-color: #474544;
}
/*Treeview*/
.tree-loading {
  color: #474544 !important;
}
/*Wizard*/
.wizard ul li.active .step {
  border-color: #474544;
  color: #474544;
}
.wizard ul li.active:before {
  background-color: #474544;
}
/*Inputs*/
input[type=checkbox].colored-primary:checked + .text,
input[type=radio].colored-primary:checked + .text {
  color: #474544;
}
input[type=checkbox].colored-primary:checked + .text:before,
input[type=radio].colored-primary:checked + .text:before {
  border-color: #474544;
  color: #474544;
}
input[type=checkbox].checkbox-slider.colored-primary + .text:after {
  border-color: #474544;
}
input[type=checkbox].checkbox-slider.colored-primary:checked + .text:before {
  background-color: #474544;
  border-color: #474544;
  color: #fff;
}
input[type=checkbox].checkbox-slider.colored-primary:checked + .text:after {
  background-color: #474544;
}
input[type=checkbox].checkbox-slider.colored-primary.slider-icon:checked + .text:after {
  color: #474544;
}
input[type=checkbox].checkbox-slider.colored-primary.slider-icon + .text:after {
  color: #474544;
}
input[type=checkbox].checkbox-slider.colored-primary.toggle:checked + .text:after {
  color: #474544;
}
input[type=checkbox].checkbox-slider.colored-primary.toggle + .text:after {
  background-color: #474544;
}
/*Slider*/
.noUi-connect {
  background: #474544;
}
.ui-rangeSlider.valuelabel-primary .ui-rangeSlider-label {
  background-color: #474544;
}
.ui-rangeSlider.valuelabel-primary .ui-rangeSlider-label-inner {
  border-top: 6px solid #474544;
}
.ui-rangeSlider.silder-primary .ui-rangeSlider-leftArrow:hover .ui-rangeSlider-arrow-inner:before,
.ui-rangeSlider.silder-primary .ui-rangeSlider-rightArrow:hover .ui-rangeSlider-arrow-inner:before {
  color: #474544 !important;
}
.ui-rangeSlider.silder-primary .ui-rangeSlider-bar {
  background: #474544 !important;
}
.ui-rangeSlider-label {
  background-color: #d73d32;
}
.ui-rangeSlider-bar {
  background: #474544;
}
.ui-rangeSlider-label-inner {
  border-top: 6px solid #d73d32;
}
/*Select2*/
.select2-results .select2-highlighted {
  background: #474544;
}
.select2-container-multi .select2-choices .select2-search-choice {
  border: 1px solid #474544;
  background-color: #474544;
}
/*Tags Input*/
.bootstrap-tagsinput > span {
  border: 1px solid #474544;
  background: #474544;
}
/*DatePicker*/
.datepicker td.active,
.datepicker td.active:hover {
  background-color: #474544;
}
.datepicker td span.active {
  background-color: #474544;
}
/*DateRangePicker*/
.daterangepicker .ranges li {
  color: #474544;
}
.daterangepicker .ranges li.active,
.daterangepicker .ranges li:hover {
  background: #474544;
  border: 1px solid #474544;
}
.daterangepicker td.active,
.daterangepicker td.active:hover {
  background-color: #474544;
  border-color: #474544;
}
/*Editors*/
.note-editor .dropdown-menu li a i {
  color: #474544;
}
.note-editor .note-dropzone.hover {
  color: #474544;
  border: 2px dashed #474544;
}
/*Lockscreen*/
.lock-container .lock-box .btn-lock,
.lock-container .lock-box .btn-lock:hover {
  color: #474544;
}
.lock-container .signinbox a {
  color: #474544;
}
/*Pricing Tables*/
.pricing-container .plan .signup {
  background-color: #474544;
}
.profile-container .profile-body .nav-tabs > li.active > a,
.profile-container .profile-body .nav-tabs > li.active > a:hover,
.profile-container .profile-body .nav-tabs > li.active > a:focus {
  border-bottom: 2px solid #474544;
}
/*Comment*/
.comment .comment-footer a:hover {
  color: #474544;
}
/*Mail*/
.mail-container .mail-header .header-buttons li a:hover {
  border-color: #474544;
}
.mail-container .mail-header .header-buttons li a:hover i {
  color: #474544;
}
.mail-container .mail-body .mail-info .mail-sender.mail-sender span,
.mail-container .mail-body .mail-info .mail-date.mail-sender span {
  color: #d73d32;
}
.mail-container .mail-body .mail-reply .reply-form > div a {
  color: #d73d32;
}
.mail-container .mail-body .mail-list .list-item:before {
  background-color: #474544;
}
/*Calendar*/
.external-event {
  border-left: 4px solid #474544;
}
/*Dashboard*/
.dashboard-box .box-header .deadline {
  border-top: 3px solid #474544 !important;
}
.dashboard-box .box-progress .progress-handle {
  background-color: #474544 !important;
}
.dashboard-box .box-progress .progress-handle:after {
  border-top-color: #474544 !important;
}
.dashboard-box .box-days .day-container:hover {
  background-color: #474544;
  border: 1px solid #474544;
}
.dashboard-box .box-days .day-container.highlight {
  background-color: #474544;
  border: 1px solid #474544;
}
.dashboard-box .box-days .day-container.highlight:hover {
  color: #474544;
}
.dashboard-box .box-days .day-container .day-more {
  color: #474544;
}
/*Orders*/
.orders-container .orders-list .order-item:before {
  background-color: #474544 !important;
}
.orders-container .orders-list .order-item.top:hover:before {
  background-color: #d73d32 !important;
}
.orders-container .orders-list .order-item.top .item-more {
  color: #d73d32 !important;
}
.orders-container .orders-list .order-item .item-more {
  color: #474544 !important;
}
/*Error Pages*/
.body-404 {
  background-color: #474544;
}
.error-container h1 {
  color: #474544;
}
.return-btn:hover {
  color: #474544;
}
.body-500 {
  background-color: #d73d32;
}
.body-500 .error-container h1 {
  color: #d73d32;
}
.body-500 .return-btn:hover {
  color: #d73d32;
}
.loading-container {
  background-color: #474544 !important;
}
.breadcrumb > li > a {
  color: #474544;
}
.page-chatbar .chatbar-contacts .contacts-search .searchicon {
  color: #474544;
}
.page-chatbar .chatbar-messages .messages-contact .back i {
  color: #474544;
}
/*# sourceMappingURL=black.css.map */