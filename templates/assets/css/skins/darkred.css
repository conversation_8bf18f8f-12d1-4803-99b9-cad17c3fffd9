﻿/*Colors*/
.themeprimary {
  color: #ac193d !important;
}
.themesecondary {
  color: #7bd148 !important;
}
.themethirdcolor {
  color: #5db2ff !important;
}
.themefourthcolor {
  color: #e75b8d !important;
}
.themefifthcolor {
  color: #ffce55 !important;
}
/*Background Colors*/
.bg-themeprimary {
  background-color: #ac193d !important;
}
.bg-themesecondary {
  background-color: #7bd148 !important;
}
.bg-themethirdcolor {
  background-color: #5db2ff !important;
}
.bg-themefourthcolor {
  background-color: #e75b8d !important;
}
.bg-themefifthcolor {
  background-color: #ffce55 !important;
}
/*Before Colors*/
.before-themeprimary:before {
  background-color: #ac193d !important;
}
.before-themesecondary:before {
  background-color: #7bd148 !important;
}
.before-themethirdcolor:before {
  background-color: #5db2ff !important;
}
.before-themefourthcolor:before {
  background-color: #e75b8d !important;
}
.before-themefifthcolor:before {
  background-color: #ffce55 !important;
}
/*Border Colors*/
.bordered-themeprimary {
  border-color: #ac193d !important;
}
.bordered-themesecondary {
  border-color: #7bd148 !important;
}
.bordered-themethirdcolor {
  border-color: #5db2ff !important;
}
.bordered-themefourthcolor {
  border-color: #e75b8d !important;
}
.bordered-themefifthcolor {
  border-color: #ffce55 !important;
}
/*Typography*/
h1.row-title:before,
h2.row-title:before,
h3.row-title:before,
h4.row-title:before,
h5.row-title:before,
h6.row-title:before {
  background-color: #ac193d;
}
/*Navbar*/
.navbar .navbar-inner {
  background: #ac193d;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li > a .badge {
  box-shadow: 1px 1px 0 #ac193d;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li .dropdown-menu.dropdown-messages li .message-time {
  color: #ac193d;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li .dropdown-menu.dropdown-login-area > li .avatar-area .caption {
  background-color: #ac193d;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li .dropdown-menu.dropdown-login-area > li.dropdown-footer {
  border-top: 3px solid #ac193d;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li.open > a {
  background-color: #ac193d;
}
.navbar .navbar-inner .navbar-header .navbar-account .account-area .login-area .avatar {
  border-left: 2px solid #7bd148;
}
.navbar .navbar-inner .navbar-header .navbar-account .setting-container input[type=checkbox] + .text:before {
  border-color: #ac193d;
}
.navbar .navbar-inner .navbar-header .navbar-account .setting-container input[type=checkbox]:checked + .text:before {
  border-color: #ac193d;
  color: #7bd148;
}
.navbar .navbar-inner .navbar-header .navbar-account.setting-open .setting {
  background-color: #ac193d;
}
/*Sidebar*/
.page-sidebar:hover .sidebar-collapse .collapse-icon {
  color: #ac193d;
}
.page-sidebar .sidebar-header-wrapper {
  margin-left: 5px;
  height: 40px;
  margin-bottom: 0;
  position: relative;
}
.page-sidebar .sidebar-header-wrapper .searchicon {
  color: #ac193d;
}
.page-sidebar .sidebar-menu li:before {
  background-color: #ac193d;
}
.page-sidebar .sidebar-menu li:not(.open):hover:before {
  background-color: #7bd148;
}
.page-sidebar .sidebar-collapse:before {
  background-color: #ac193d;
}
.page-sidebar .sidebar-collapse .collapse-icon:hover {
  background-color: #ac193d;
  color: #fff;
}
/*Page Header*/
.page-header .header-buttons a:before {
  background-color: #ac193d;
}
.page-header .header-buttons a:hover {
  color: #ac193d;
}
.page-header .header-buttons a.active {
  color: #ac193d;
}
.page-header .header-buttons a.fullscreen:before {
  background-color: #7bd148;
}
.page-header .header-buttons a.fullscreen:hover,
.page-header .header-buttons a.fullscreen.active {
  color: #7bd148;
}
.page-header .header-buttons a.sidebar-toggler:before {
  background-color: #ac193d;
}
.page-header .header-buttons a.sidebar-toggler:hover,
.page-header .header-buttons a.sidebar-toggler.active {
  color: #ac193d;
}
/*Widget*/
.widget:hover .compact i {
  color: #ac193d;
}
.widget-buttons.compact:hover {
  background-color: #ac193d;
}
.widget-buttons.compact:hover i {
  color: #fff !important;
}
.widget-buttons.compact:before {
  background-color: #ac193d;
}
/*Databoxes*/
.databox .databox-left {
  color: #ac193d;
}
.databox.databox-vertical .databox-top {
  color: #ac193d;
}
/*Tabs*/
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  border-top: 2px solid #ac193d;
}
/*Primary Button*/
.btn-primary,
.btn-primary:focus {
  background-color: #ac193d !important;
  border-color: #ac193d;
}
.btn-primary.dropdown-toggle {
  border-left-color: #d4353d !important;
}
.btn-primary.active {
  background-color: #a4053d !important;
  border-color: #a4053d;
}
.btn-primary.shiny {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMSAxIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJub25lIj48bGluZWFyR3JhZGllbnQgaWQ9Imxlc3NoYXQtZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iNSUiIHN0b3AtY29sb3I9IiNhYzE5M2QiIHN0b3Atb3BhY2l0eT0iMSIvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iI2E0MDUzZCIgc3RvcC1vcGFjaXR5PSIxIi8+PC9saW5lYXJHcmFkaWVudD48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMSIgaGVpZ2h0PSIxIiBmaWxsPSJ1cmwoI2xlc3NoYXQtZ2VuZXJhdGVkKSIgLz48L3N2Zz4=);
  background-image: -webkit-linear-gradient(center top, #ac193d 5%, #a4053d 100%);
  background-image: -moz-linear-gradient(center top, #ac193d 5%, #a4053d 100%);
  background-image: -o-linear-gradient(center top, #ac193d 5%, #a4053d 100%);
  background-image: linear-gradient(center to bottom, #ac193d 5%, #a4053d 100%);
}
.btn-primary:hover,
.open .btn-primary.dropdown-toggle {
  background-color: #ad273d !important;
  border-color: #ac193d;
}
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  background-color: #ac193d !important;
  border-color: #ac193d;
}
/*Primary Label*/
.badge-primary,
.label-primary {
  background-color: #ac193d !important;
}
/*Popover*/
.popover-title {
  border-bottom: 3px solid #ac193d;
}
/*tooltip*/
.tooltip-primary + .tooltip > .tooltip-inner {
  border: 1px solid #ac193d;
  background-color: #ac193d;
}
.tooltip-primary + .tooltip.top .tooltip-arrow {
  border-top-color: #ac193d;
}
.tooltip-primary + .tooltip.right .tooltip-arrow {
  border-right-color: #ac193d;
}
.tooltip-primary + .tooltip.left .tooltip-arrow {
  border-left-color: #ac193d;
}
.tooltip-primary + .tooltip.bottom .tooltip-arrow {
  border-bottom-color: #ac193d;
}
/*ProgressBar*/
.progress-bar {
  background-color: #ac193d;
}
/*Notification*/
.toast-primary {
  background-color: #ac193d !important;
}
/*Modal*/
.modal-primary .modal-header {
  border-bottom: 3px solid #ac193d;
}
/*Dropdown*/
.dropdown-primary li a:hover,
.dropdown-primary li a:focus,
.dropdown-primary li a:active,
.dropdown-primary li.active a,
.dropdown-primary li.active a:hover,
.dropdown-primary .dropdown-submenu:hover > a,
.nav-tabs .dropdown-primary li > a:focus {
  background: #ac193d;
}
/*Pagination*/
.pagination > li.active > a,
.pagination > li.active > a:hover {
  background-color: #ac193d;
  border-color: #ac193d;
}
/*Table*/
.table thead.colored-primary > tr > th {
  background-color: #ac193d;
}
.table thead.colored-primary.bordered-primary > tr > th {
  border-bottom: 3px solid #ac193d;
}
@media only screen and (max-width: 800px) {
  .table thead.bordered-primary > tr > th {
    border-right: 3px solid #ac193d;
  }
}
/*Nestable Lists*/
.dd-list > li.bordered-primary > .dd-handle {
  border-left-color: #ac193d;
}
/*Treeview*/
.tree-loading {
  color: #ac193d !important;
}
/*Wizard*/
.wizard ul li.active .step {
  border-color: #ac193d;
  color: #ac193d;
}
.wizard ul li.active:before {
  background-color: #ac193d;
}
/*Inputs*/
input[type=checkbox].colored-primary:checked + .text,
input[type=radio].colored-primary:checked + .text {
  color: #ac193d;
}
input[type=checkbox].colored-primary:checked + .text:before,
input[type=radio].colored-primary:checked + .text:before {
  border-color: #ac193d;
  color: #ac193d;
}
input[type=checkbox].checkbox-slider.colored-primary + .text:after {
  border-color: #ac193d;
}
input[type=checkbox].checkbox-slider.colored-primary:checked + .text:before {
  background-color: #ac193d;
  border-color: #ac193d;
  color: #fff;
}
input[type=checkbox].checkbox-slider.colored-primary:checked + .text:after {
  background-color: #ac193d;
}
input[type=checkbox].checkbox-slider.colored-primary.slider-icon:checked + .text:after {
  color: #ac193d;
}
input[type=checkbox].checkbox-slider.colored-primary.slider-icon + .text:after {
  color: #ac193d;
}
input[type=checkbox].checkbox-slider.colored-primary.toggle:checked + .text:after {
  color: #ac193d;
}
input[type=checkbox].checkbox-slider.colored-primary.toggle + .text:after {
  background-color: #ac193d;
}
/*Slider*/
.noUi-connect {
  background: #ac193d;
}
.ui-rangeSlider.valuelabel-primary .ui-rangeSlider-label {
  background-color: #ac193d;
}
.ui-rangeSlider.valuelabel-primary .ui-rangeSlider-label-inner {
  border-top: 6px solid #ac193d;
}
.ui-rangeSlider.silder-primary .ui-rangeSlider-leftArrow:hover .ui-rangeSlider-arrow-inner:before,
.ui-rangeSlider.silder-primary .ui-rangeSlider-rightArrow:hover .ui-rangeSlider-arrow-inner:before {
  color: #ac193d !important;
}
.ui-rangeSlider.silder-primary .ui-rangeSlider-bar {
  background: #ac193d !important;
}
.ui-rangeSlider-label {
  background-color: #7bd148;
}
.ui-rangeSlider-bar {
  background: #ac193d;
}
.ui-rangeSlider-label-inner {
  border-top: 6px solid #7bd148;
}
/*Select2*/
.select2-results .select2-highlighted {
  background: #ac193d;
}
.select2-container-multi .select2-choices .select2-search-choice {
  border: 1px solid #ac193d;
  background-color: #ac193d;
}
/*Tags Input*/
.bootstrap-tagsinput > span {
  border: 1px solid #ac193d;
  background: #ac193d;
}
/*DatePicker*/
.datepicker td.active,
.datepicker td.active:hover {
  background-color: #ac193d;
}
.datepicker td span.active {
  background-color: #ac193d;
}
/*DateRangePicker*/
.daterangepicker .ranges li {
  color: #ac193d;
}
.daterangepicker .ranges li.active,
.daterangepicker .ranges li:hover {
  background: #ac193d;
  border: 1px solid #ac193d;
}
.daterangepicker td.active,
.daterangepicker td.active:hover {
  background-color: #ac193d;
  border-color: #ac193d;
}
/*Editors*/
.note-editor .dropdown-menu li a i {
  color: #ac193d;
}
.note-editor .note-dropzone.hover {
  color: #ac193d;
  border: 2px dashed #ac193d;
}
/*Lockscreen*/
.lock-container .lock-box .btn-lock,
.lock-container .lock-box .btn-lock:hover {
  color: #ac193d;
}
.lock-container .signinbox a {
  color: #ac193d;
}
/*Pricing Tables*/
.pricing-container .plan .signup {
  background-color: #ac193d;
}
.profile-container .profile-body .nav-tabs > li.active > a,
.profile-container .profile-body .nav-tabs > li.active > a:hover,
.profile-container .profile-body .nav-tabs > li.active > a:focus {
  border-bottom: 2px solid #ac193d;
}
/*Comment*/
.comment .comment-footer a:hover {
  color: #ac193d;
}
/*Mail*/
.mail-container .mail-header .header-buttons li a:hover {
  border-color: #ac193d;
}
.mail-container .mail-header .header-buttons li a:hover i {
  color: #ac193d;
}
.mail-container .mail-body .mail-info .mail-sender.mail-sender span,
.mail-container .mail-body .mail-info .mail-date.mail-sender span {
  color: #7bd148;
}
.mail-container .mail-body .mail-reply .reply-form > div a {
  color: #7bd148;
}
.mail-container .mail-body .mail-list .list-item:before {
  background-color: #ac193d;
}
/*Calendar*/
.external-event {
  border-left: 4px solid #ac193d;
}
/*Dashboard*/
.dashboard-box .box-header .deadline {
  border-top: 3px solid #ac193d !important;
}
.dashboard-box .box-progress .progress-handle {
  background-color: #ac193d !important;
}
.dashboard-box .box-progress .progress-handle:after {
  border-top-color: #ac193d !important;
}
.dashboard-box .box-days .day-container:hover {
  background-color: #ac193d;
  border: 1px solid #ac193d;
}
.dashboard-box .box-days .day-container.highlight {
  background-color: #ac193d;
  border: 1px solid #ac193d;
}
.dashboard-box .box-days .day-container.highlight:hover {
  color: #ac193d;
}
.dashboard-box .box-days .day-container .day-more {
  color: #ac193d;
}
/*Orders*/
.orders-container .orders-list .order-item:before {
  background-color: #ac193d !important;
}
.orders-container .orders-list .order-item.top:hover:before {
  background-color: #7bd148 !important;
}
.orders-container .orders-list .order-item.top .item-more {
  color: #7bd148 !important;
}
.orders-container .orders-list .order-item .item-more {
  color: #ac193d !important;
}
/*Error Pages*/
.body-404 {
  background-color: #ac193d;
}
.error-container h1 {
  color: #ac193d;
}
.return-btn:hover {
  color: #ac193d;
}
.body-500 {
  background-color: #7bd148;
}
.body-500 .error-container h1 {
  color: #7bd148;
}
.body-500 .return-btn:hover {
  color: #7bd148;
}
.loading-container {
  background-color: #ac193d !important;
}
.breadcrumb > li > a {
  color: #ac193d;
}
.page-chatbar .chatbar-contacts .contacts-search .searchicon {
  color: #ac193d;
}
.page-chatbar .chatbar-messages .messages-contact .back i {
  color: #ac193d;
}
/*# sourceMappingURL=darkred.css.map */