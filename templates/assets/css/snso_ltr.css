﻿﻿/*#SNSO Override*/
.navbar .navbar-inner .navbar-header .navbar-account .account-area > li > a .badge {
    position: absolute;
    top: 15px;
    right: -7px;
    padding: 2px;
    cursor: pointer;
    background-color: transparent !important;
    /* border: 1px solid #fff; */
    width: 18px;
    height: 18px;
    /* box-shadow: -1px 1px 0 #2dc3e8; */
    -webkit-transition: top .3s ease;
    -moz-transition: top .3s ease;
    -o-transition: top .3s ease;
    transition: top .3s ease;
}


.snsolabel {
    white-space: nowrap
}

.snsoinput {
    background-color: #F0F0F0;
    min-height: 35px;
    margin-top: 2px;
    margin-bottom: 2px;
    padding: 2px
}

.snsoinput input[type=radio] {
    opacity: 1;
}

.snsoinput input[type=checkbox] {
    opacity: 1;
}

.table input[type=radio]{
    opacity: 1;
    position: static;
}

input[type=checkbox] {
    opacity: 1;
}

.snsoinput-site {
    margin-top: 2px;
    margin-bottom: 2px;
    padding: 2px
}

.pagetitle {
    background-color: #E8E8E8;
    color: #333;
    font-weight: 700;
    padding: 5px;
    margin: 0
}

.pagetitle div {
    padding: 0
}

.pagetitle select {
    color: #333
}

.snsowraper {
    padding-left: 15px;
    padding-right: 15px;
    margin-bottom: 20px
}

.snsosmallwraper {
    padding-left: 15px;
    padding-right: 15px;
    margin-bottom: 3px
}

.user_home_title{
    color: #333;
    font-size: 12px;
}


.fin-tag {
    border-top: 3px solid rgb(71, 69, 68) !important;
    -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .15);
    -moz-box-shadow: 0 1px 2px rgba(0, 0, 0, .15);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
    position: absolute;
    font-size: 12px;
    height: 30px;
    top: -3px;
    background-color: white;
    padding: 3px;
    left: 20px;

}

.tag-inline {
    /** this to make them inline **/
    left: 0 !important;
    right: 0 !important;
    display: inline-block !important;
    position: relative !important;
}

.fin-tag-content {
    position: relative;
    line-height: 1.5;
    font-size: 15px;
    padding: 0 1em;
}


.navbar .navbar-inner .navbar-header .navbar-account .account-area > li > a .badge {
    position: absolute;
    top: 14px;
    right: -9px;
    padding: 2px;
    cursor: pointer;
    background-color: transparent !important;
    border: 0px solid #fff;
    width: 18px;
    height: 18px;
    box-shadow: -1px 1px 0 #2dc3e8;
    -webkit-transition: top .3s ease;
    -moz-transition: top .3s ease;
    -o-transition: top .3s ease;
    transition: top .3s ease;
}

.page-body {
    background: #eee;
    padding: 15px 13px 15px;
}

.table-responsive[data-pattern="priority-columns"] > .table > thead > tr > th {
    text-align: center;
    vertical-align: middle;
    color: white;
    font-weight: inherit;
    background-color: #474544;
}

.table thead > tr > th {
    text-align: center;
    vertical-align: middle;
    color: white;
    font-weight: inherit;
    background-color: #474544;
}