body #wrapper {
    background-image: url('/templates/assets/img/home/<USER>');
    background-repeat: repeat;
}

section {
    padding: 30px 0;
}

.khari-baqe-border {
    background-color: white;
    padding: 10px;
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.3) !important;
}

.icon-snso:before {
    content: url('/templates/assets/img/snsoiconbw.png');
}

.social-snso:hover {
    background-color: #C3BC57 !important;
    opacity: 1;
}

.social-snso {
    background-color: #00ACEE;
    opacity: 0.2;
}

div.heading-title {
    position: relative;
    margin-bottom: 20px;
}

/** olive color **/

.btn-olive {
    color: #fff;
    background-color: #AAA831;
    border-color: #AAA831;
}