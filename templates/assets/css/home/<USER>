/** ************************************* **
	<AUTHOR>
	@Website		www.stepofweb.com
	@Last Update	Tuesday, August 25, 2015

	~ ~ ~ UPDATE REASONS ~ ~ ~
	DO NOT CHANGE ANYTHING HERE!
	REWRITE IN A CUSTOM CSS FILE IF REQUIRED!
	~ ~ ~ ~ ~ ~ ~ ~ ~ ~ ~ ~ ~ ~ ~ ~ ~ ~ ~ ~ ~

	
	NOTE: 	- Only minifying this file, 10% is reduced!
			- Using gzip or deflate compression via your 
			  .htaccess, this file will be reduced by 83%


	TABLE CONTENTS
	---------------------------
	01. Resets
	02. Headings
	03. Placeholder
	04. Commons
	05. Progress Bars & Pie Charts
	06. Dropcap
	07. Table
	08. Nav Pills
	09. Blockquotes				[shortcode]
	10. Background Patterns
	11. Colors					[v2.0.0]
	12. Magnific Popup			[v1.0.0]
	13. OWL Carousel 			[v1.3.3]
	14. Animate					[v3.2.3]
	15. Fontawesome 			[v4.4.0]
	16. Et-Line	& Font Icons	[v0.0.0]
	17. Social Icons
	18. Buttons					[shortcode]
	19. Counters & Countdown	[shortcode]
	20. Clients					[shortcode]
	21. Alerts					[shortcode]
	22. Dividers				[shortcode]
	23. Headings				[shortcode]
	24. Word Rotator			[shortcode]
	25. Icon Boxes				[shortcode]
	26. Labels & Badges			[shortcode]
	27. Lightbox Ajax			[shortcode]
	28. Panels					[shortcode]
	29. Modals					[shortcode]
	30. Toastr					[shortcode]
	31. Navigations				[shortcode]
	32. Paginations				[shortcode]
	33. Tables					[shortcode]
	34. Callouts				[shortcode]
	35. Process Steps			[shortcode]
	36. Price Table				[shortcode]
	37. Styled Icons			[shortcode]
	38. Pickers					[plugins]
	39. Select2					[plugin]
	40. Tabs					[shortcode]
	41. Toggles & Accordions	[shortcode]
	42. Box Shadow				[shortcode]
	43. Testimonials			[shortcode]
	44. Flexslider				[plugin]
	45. Widgets					[plugin]
	46. Side Nav
	47. Star Rating
	48. Image Zoom				[plugin]
	49. Forms
	50. Sky Form				[plugin]
	51. Summernote				[HTML EDITOR]
	52. Markdown				[HTML EDITOR]
	---------------------------

 ** ************************************* **/


/**	01. Resets
*************************************************** **/
button::-moz-focus-inner,
input::-moz-focus-inner {
	border: 0;
	padding: 0;
}

::selection {
	color:#fff;
	text-shadow:none;
	background: #333;
}
::-moz-selection {
	color:#fff;
	text-shadow:none;
	background: #333; /* Firefox */
}
::-webkit-selection {
	color:#fff;
	text-shadow:none;
	background: #333; /* Safari */
}

button {
	background: none;
	border: 0; margin: 0; padding: 0;
	cursor: pointer;
}

img {
	border: 0;
	vertical-align: top;
}

input:-webkit-autofill {
	color: #ffffff !important;
}

textarea {
	resize: none;
}

textarea, input, button, *:focus {
	outline:none !important;
}

textarea {
	resize: vertical;
}

select {
	border: 2px solid #E5E7E9;
	height: 46px;
	padding: 12px;
	outline: none;
	line-height:1 !important;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}

label {
	font-weight:400;
}

iframe,
fieldset {
	border: 0;
	margin: 0;
	padding: 0;
}

audio, canvas, img, video {
	vertical-align: middle;
}

p {
	display: block;
	-webkit-margin-before: 0.1em;
	-webkit-margin-after: 0.1em;
	-webkit-margin-start: 0px;
	-webkit-margin-end: 0px;
}

a {
	-webkit-transition: all .300s;
	-moz-transition: all .300s;
	-o-transition: all .300s;
	transition: all .300s;
}

a, a:focus, a:hover, a:active {
	outline: 0;
	cursor:pointer;
}

p, pre, ul, ol, dl, dd, blockquote,
address, table, fieldset, form {
	margin-bottom: 30px;
}

canvas {
	width: 100% !important;
	/* max-width: 800px; causes panorama gmap problems */
	height: auto !important;
}

:active,
:focus { outline: none !important; }


select:focus,
textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus {
	outline: 0 none;

	-webkit-box-shadow: none !important;
	-moz-box-shadow: none !important;
	-o-box-shadow: none !important;
	box-shadow: none !important;
}


pre {
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
section.dark pre {
	color:#ccc;
	border-color:rgba(0,0,0,0.2);
	background-color:rgba(0,0,0,0.2);
}



img.img-responsive {
	display:inline-block;
}

/* IE & Mozilla fix */
div.row>div img.img-responsive {
	width: 100%;
}



/** 02. Headings
 **************************************************************** **/
h1,
h2,
h3,
h4,
h5,
h6 {
	font-family:'Raleway','Open Sans',Arial,Helvetica,sans-serif;
	font-weight:600;
	color:#414141;

	margin: 0 0 30px 0;
	line-height:1.5;
	-webkit-font-smoothing: antialiased;
}

h1 {
	font-size: 36px;
	margin: 0 0 44px 0;
}

h2 {
	font-size: 30px;
	margin: 0 0 32px 0;
}

h3 {
	font-size: 24px;
	letter-spacing: normal;
	margin: 0 0 32px 0;
}

h4 {
	font-size: 18px;
	letter-spacing: normal;
	margin: 0 0 14px 0;
}

h5 {
	font-size: 14px;
	letter-spacing: normal;
	margin: 0 0 20px 0;
}

h6 {
	font-size: 12px;
	letter-spacing: normal;
	margin: 0 0 20px 0;
}

h1 > span,
h2 > span,
h3 > span,
h4 > span,
h5 > span,
h6 > span {
	color: #1ABC9C;
}




/**	03. Placeholder
*************************************************** **/
::-webkit-input-placeholder { 	/* WebKit browsers */
	color: #999;
}

:-moz-placeholder { 				/* Mozilla Firefox 4 to 18 */
	color: #999;
}

::-moz-placeholder { 			/* Mozilla Firefox 19+ */
	color: #999;
}

:-ms-input-placeholder {			/* Internet Explorer 10+ */
	color: #999;
}





/** 04. Commons
 **************************************************************** **/
/* utils */
.size-11 			{ font-size:11px !important; line-height:15px !important; }
.size-12 			{ font-size:12px !important; line-height:16px !important; }
.size-13 			{ font-size:13px !important; line-height:17px !important; }
.size-14 			{ font-size:14px !important; line-height:18px !important; }
.size-15 			{ font-size:15px !important; line-height:19px !important; }
.size-16 			{ font-size:16px !important; line-height:20px !important; }
.size-17 			{ font-size:17px !important; line-height:23px !important; }
.size-18 			{ font-size:18px !important; line-height:24px !important; }
.size-19 			{ font-size:19px !important; line-height:25px !important; }
.size-20 			{ font-size:20px !important; line-height:26px !important; }
.size-25 			{ font-size:25px !important; line-height:30px !important; }
.size-30 			{ font-size:30px !important; line-height:36px !important; }
.size-40 			{ font-size:40px !important; line-height:46px !important; }
.size-50 			{ font-size:50px !important; line-height:56px !important; }
.size-60 			{ font-size:60px !important; line-height:66px !important; }
.size-70 			{ font-size:70px !important; line-height:76px !important; }
.size-80 			{ font-size:80px !important; line-height:86px !important; }
.size-90 			{ font-size:90px !important; line-height:96px !important; }
.size-100 			{ font-size:100px !important; line-height:106px !important; }
.size-150 			{ font-size:150px !important; line-height:166px !important; }
.size-200 			{ font-size:200px !important; line-height:216px !important; }

.weight-300 			{ font-weight:300 !important; 	}
.weight-400 			{ font-weight:400 !important; 	}
.weight-500 			{ font-weight:500 !important; 	}
.weight-600 			{ font-weight:600 !important; 	}
.weight-700 			{ font-weight:700 !important; 	}
.weight-800 			{ font-weight:800 !important; 	}

.width-10			{ width:10px !important; }
.width-20			{ width:20px !important; }
.width-30			{ width:30px !important; }
.width-40			{ width:40px !important; }
.width-50			{ width:50px !important; }
.width-100			{ width:100px !important; }
.width-150			{ width:150px !important; }
.width-200			{ width:200px !important; }
.width-250			{ width:250px !important; }
.width-300			{ width:300px !important; }
.width-350			{ width:350px !important; }
.width-400			{ width:400px !important; }
.width-450			{ width:450px !important; }
.width-500			{ width:500px !important; }
.width-550			{ width:550px !important; }
.width-600			{ width:600px !important; }
.width-650			{ width:650px !important; }
.width-700			{ width:700px !important; }
.width-750			{ width:750px !important; }
.width-800			{ width:800px !important; }

.height-10			{ height:10px !important; }
.height-20			{ height:20px !important; }
.height-30			{ height:30px !important; }
.height-50			{ height:50px !important; }
.height-100			{ height:100px !important; }
.height-150			{ height:150px !important; }
.height-200			{ height:200px !important; }
.height-250			{ height:250px !important; }
.height-300			{ height:300px !important; }
.height-350			{ height:350px !important; }
.height-400			{ height:400px !important; }
.height-450			{ height:450px !important; }
.height-500			{ height:500px !important; }
.height-550			{ height:550px !important; }
.height-600			{ height:600px !important; }
.height-650			{ height:650px !important; }
.height-700			{ height:700px !important; }
.height-750			{ height:750px !important; }
.height-800			{ height:800px !important; }

.padding-0 			{ padding:0 !important; 	}
.padding-3 			{ padding:3px !important; 	}
.padding-6 			{ padding:6px !important; 	}
.padding-8 			{ padding:8px !important; 	}
.padding-10 			{ padding:10px !important; 	}
.padding-15 			{ padding:15px !important; 	}
.padding-20 			{ padding:20px !important; 	}
.padding-30 			{ padding:30px !important; 	}
.padding-40 			{ padding:40px !important; 	}
.padding-50 			{ padding:50px !important; 	}
.padding-60 			{ padding:60px !important; 	}
.padding-70 			{ padding:70px !important; 	}
.padding-80 			{ padding:80px !important; 	}
.padding-90 			{ padding:90px !important; 	}
.padding-100 		{ padding:100px !important; }

.padding-top-0		{ padding-top:0 !important; }
.padding-top-10		{ padding-top:10px !important; }
.padding-top-15		{ padding-top:15px !important; }
.padding-top-20		{ padding-top:20px !important; }
.padding-top-30		{ padding-top:30px !important; }
.padding-top-40		{ padding-top:40px !important; }
.padding-top-50		{ padding-top:50px !important; }
.padding-top-60		{ padding-top:60px !important; }
.padding-top-80		{ padding-top:80px !important; }
.padding-top-100	{ padding-top:100px !important; }
.padding-top-130	{ padding-top:130px !important; }
.padding-top-150	{ padding-top:150px !important; }
.padding-top-180	{ padding-top:180px !important; }
.padding-top-200	{ padding-top:200px !important; }

.padding-bottom-0	{ padding-bottom:0 !important; }
.padding-bottom-10	{ padding-bottom:10px !important; }
.padding-bottom-15	{ padding-bottom:15px !important; }
.padding-bottom-20	{ padding-bottom:20px !important; }
.padding-bottom-30	{ padding-bottom:30px !important; }
.padding-bottom-40	{ padding-bottom:40px !important; }
.padding-bottom-50	{ padding-bottom:50px !important; }
.padding-bottom-60	{ padding-bottom:60px !important; }
.padding-bottom-80	{ padding-bottom:80px !important; }
.padding-bottom-100	{ padding-bottom:100px !important; }
.padding-bottom-130	{ padding-bottom:130px !important; }
.padding-bottom-150	{ padding-bottom:150px !important; }
.padding-bottom-180	{ padding-bottom:180px !important; }
.padding-bottom-200	{ padding-bottom:200px !important; }


.margin-top-0		{ margin-top:0 !important; }
.margin-top-1		{ margin-top:1px !important; }
.margin-top-2		{ margin-top:2px !important; }
.margin-top-3		{ margin-top:3px !important; }
.margin-top-6		{ margin-top:6px !important; }
.margin-top-8		{ margin-top:8px !important; }
.margin-top-10		{ margin-top:10px !important; }
.margin-top-20		{ margin-top:20px !important; }
.margin-top-30		{ margin-top:30px !important; }
.margin-top-40		{ margin-top:40px !important; }
.margin-top-50		{ margin-top:50px !important; }
.margin-top-60		{ margin-top:60px !important; }
.margin-top-80		{ margin-top:80px !important; }
.margin-top-100		{ margin-top:100px !important; }
.margin-top-130		{ margin-top:130px !important; }
.margin-top-150		{ margin-top:150px !important; }
.margin-top-180		{ margin-top:180px !important; }
.margin-top-200		{ margin-top:200px !important; }

.margin-bottom-0	{ margin-bottom:0 !important; }
.margin-bottom-1	{ margin-bottom:1px !important; }
.margin-bottom-2	{ margin-bottom:2px !important; }
.margin-bottom-3	{ margin-bottom:3px !important; }
.margin-bottom-6	{ margin-bottom:6px !important; }
.margin-bottom-8	{ margin-bottom:8px !important; }
.margin-bottom-10	{ margin-bottom:10px !important; }
.margin-bottom-20	{ margin-bottom:20px !important; }
.margin-bottom-30	{ margin-bottom:30px !important; }
.margin-bottom-40	{ margin-bottom:40px !important; }
.margin-bottom-50	{ margin-bottom:50px !important; }
.margin-bottom-60	{ margin-bottom:60px !important; }
.margin-bottom-80	{ margin-bottom:80px !important; }
.margin-bottom-100	{ margin-bottom:100px !important; }
.margin-bottom-130	{ margin-bottom:130px !important; }
.margin-bottom-150	{ margin-bottom:150px !important; }
.margin-bottom-180	{ margin-bottom:180px !important; }
.margin-bottom-200	{ margin-bottom:200px !important; }

.margin-left-0		{ margin-left:0 !important; }
.margin-left-3		{ margin-left:3px !important; }
.margin-left-6		{ margin-left:6px !important; }
.margin-left-8		{ margin-left:8px !important; }
.margin-left-10		{ margin-left:10px !important; }
.margin-left-15		{ margin-left:15px !important; }
.margin-left-20		{ margin-left:20px !important; }
.margin-left-30		{ margin-left:30px !important; }
.margin-left-40		{ margin-left:40px !important; }
.margin-left-50		{ margin-left:50px !important; }
.margin-left-60		{ margin-left:60px !important; }
.margin-left-80		{ margin-left:80px !important; }
.margin-left-100	{ margin-left:100px !important; }
.margin-left-130	{ margin-left:130px !important; }
.margin-left-150	{ margin-left:150px !important; }
.margin-left-180	{ margin-left:180px !important; }
.margin-left-200	{ margin-left:200px !important; }
.margin-left-250	{ margin-left:250px !important; }
.margin-left-300	{ margin-left:300px !important; }

.margin-right-0		{ margin-right:0 !important; }
.margin-right-3		{ margin-right:3px !important; }
.margin-right-6		{ margin-right:6px !important; }
.margin-right-8		{ margin-right:8px !important; }
.margin-right-10	{ margin-right:10px !important; }
.margin-right-15	{ margin-right:15px !important; }
.margin-right-20	{ margin-right:20px !important; }
.margin-right-30	{ margin-right:30px !important; }
.margin-right-40	{ margin-right:40px !important; }
.margin-right-50	{ margin-right:50px !important; }
.margin-right-60	{ margin-right:60px !important; }
.margin-right-80	{ margin-right:80px !important; }
.margin-right-100	{ margin-right:100px !important; }
.margin-right-130	{ margin-right:130px !important; }
.margin-right-150	{ margin-right:150px !important; }
.margin-right-180	{ margin-right:180px !important; }
.margin-right-200	{ margin-right:200px !important; }
.margin-right-250	{ margin-right:250px !important; }
.margin-right-300	{ margin-right:300px !important; }

.line-height-0		{ line-height:0 !important; 	}
.line-height-10		{ line-height:10px !important; 	}
.line-height-20		{ line-height:20px !important; 	}
.line-height-30		{ line-height:30px !important; 	}
.line-height-40		{ line-height:40px !important; 	}
.line-height-50		{ line-height:50px !important; 	}
.line-height-60		{ line-height:60px !important; 	}
.line-height-70		{ line-height:70px !important; 	}
.line-height-80		{ line-height:80px !important; 	}
.line-height-90		{ line-height:90px !important; 	}
.line-height-100	{ line-height:100px !important; }


.nopadding 			{ padding:0 !important; 		}
.nopadding-left 		{ padding-left:0 !important; 	}
.nopadding-right 	{ padding-right:0 !important; 	}
.nopadding-top 		{ padding-top:0 !important; 	}
.nopadding-bottom	{ padding-bottom:0 !important; 	}
.nomargin 			{ margin:0 !important; 			}
.nomargin-left 		{ margin-left:0 !important; 	}
.nomargin-right 		{ margin-right:0 !important; 	}
.nomargin-top		{ margin-top:0 !important; 	}
.nomargin-bottom	{ margin-bottom:0 !important; 	}
.noborder 			{ border:0 !important; 			}
.noborder-left		{ border-left:0 !important; 	}
.noborder-right		{ border-right:0 !important; 	}
.noborder-top		{ border-top:0 !important; 		}
.noborder-bottom	{ border-bottom:0 !important; 	}
.nobg				{ background:transparent; 		}
.lowercase 			{ text-transform:lowercase; 	}
.uppercase 			{ text-transform:uppercase; 	}
.noradius			{ -webkit-border-radius:0 !important; -moz-border-radius:0 !important; border-radius:0 !important; }
.font-style-italic 	{ font-style:italic; 			}
.font-style-normal 	{ font-style:normal; 			}
.pointer 			{ cursor:pointer; 				}
.block 				{ display:block !important; 	}
.block-inline		{ display:inline-block !important; 	}
.inline-block		{ display:inline-block !important; 	}
.bold 				{ font-weight:bold !important; 	}
.fullwidth 			{ width:100% !important; max-width:100% !important; 		}
.halfwidth 			{ width:50% !important; 		}
.justify 			{ text-align:justify; 			}
.relative 			{ position:relative;			}
.absolute			{ position:absolute !important; top:0; left:0; right:0; bottom:0;	}
.text-left 			{ text-align:left !important; }
.text-right 			{ text-align:right !important; }
.noshadow			{ box-shadow:none; text-shadow:none; }
.nofloat			{ float:none !important; 		}
.display-table		{ display:table; width:100%; height:100%; position:relative; z-index:99;	}
.display-table-cell	{ display:table-cell; width:100%; height:100%; }
.vertical-align-middle	{ vertical-align:middle;		}
.txt-no-decoration	{ text-decoration:none !important; 		}
.softhide			{ display:none; 				}
img.pull-left 		{ margin:0 20px 10px 0; }
img.pull-right 		{ margin:0 0 10px 20px; }
ul>li>i				{ margin-right:10px; 	}
.no-text-underline,
.no-text-decoration	{ text-decoration:none; }
.line-through		{ text-decoration: line-through; }
.btn>i 				{ padding-right:6px; 	}
video.fullvideo 		{ width:100%; height:100%; }
.nortl				{ direction: ltr;		}
.rtl				{ direction: rtl;		}
.ltr				{ direction: ltr;		}

/* borders */
.border-top-1 {
	border-top: 1px solid #ddd;
}
.border-top-2 {
	border-top: 2px solid #ddd;
}
.border-top-3 {
	border-top: 3px solid #ddd;
}

.border-bottom-1 {
	border-bottom: 1px solid #ddd;
}
.border-bottom-2 {
	border-bottom: 2px solid #ddd;
}
.border-bottom-3 {
	border-bottom: 3px solid #ddd;
}

section.dark .border-top-1,
section.dark .border-top-2,
section.dark .border-top-3,
section.dark .border-bottom-1,
section.dark .border-bottom-2,
section.dark .border-bottom-3 {
	border-color:#555;
}



.border-bottom-dashed:before {
	content: "";
	display: block;
	width: 100%;
	height: 1px;
	position: absolute;
	bottom: -6px;
	border-bottom: 1px dashed #999;
}
.border-bottom-dotted:before {
	content: "";
	display: block;
	width: 100%;
	height: 1px;
	position: absolute;
	bottom: -6px;
	border-bottom: 1px dotted #999;
}



/* break words */
.break-word {
	white-space: pre;           /* CSS 2.0 */
	white-space: pre-wrap;      /* CSS 2.1 */
	white-space: pre-line;      /* CSS 3.0 */
	white-space: -pre-wrap;     /* Opera 4-6 */
	white-space: -o-pre-wrap;   /* Opera 7 */
	white-space: -moz-pre-wrap; /* Mozilla */
	white-space: -hp-pre-wrap;  /* HP Printers */
	word-wrap: break-word;      /* IE 5+ */
}

/* No Tramsition */
.no-transition {
	-webkit-transition: all 0s !important;
	-moz-transition: all 0s !important;
	-o-transition: all 0s !important;
	transition: all 0s !important;
}

/* greyscale */
.grayscale {
	-webkit-filter: grayscale(100%);
	-moz-filter: grayscale(100%);
	-ms-filter: grayscale(100%);
	-o-filter: grayscale(100%);
	filter: grayscale(100%);
	filter: url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'grayscale\'><feColorMatrix type=\'matrix\' values=\'0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0\'/></filter></svg>#grayscale");
	filter: gray;
}


/* ellipsis */
.elipsis {
	overflow:hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
}

/* no selectrion */
.noselect {
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}


/* text color */
.text-default {
	color:#c6c6c6 !important;
}
.text-danger {
	color:#b92c28 !important;
}
.text-warning {
	color:#e38d13 !important;
}
.text-info {
	color:#28a4c9 !important;
}
.text-primary {
	color:#245580 !important;
}
.text-success {
	color:#02B700 !important;
}
.tex-white {
	color:#fff !important;
}
.tex-black {
	color:#111 !important;
}


/* absolute/fixed position */
.top-left {
	top:0; bottom:auto;
	left:0; right:auto;
}
.top-right {
	top:0; bottom:auto;
	left:auto; right:0;
}
.bottom-left {
	top:auto; bottom:0;
	left:0; right:auto;
}
.bottom-right {
	top:auto; bottom:0;
	left:auto; right:0;
}
.top-center {
	top:0; bottom:auto;
	left:auto; right:auto;
}
.bottom-center {
	top:auto; bottom:0;
	left:auto; right:auto;
}
.position-bottom {
	top:auto;
	bottom:0;
}
.position-top {
	top:0;
	bottom:auto;
}

.opacity-0 {
	filter: Alpha(Opacity=0);
	opacity:0;
}
.opacity-1 {
	filter: Alpha(Opacity=10);
	opacity:0.1;
}
.opacity-2 {
	filter: Alpha(Opacity=20);
	opacity:0.2;
}
.opacity-3 {
	filter: Alpha(Opacity=30);
	opacity:0.3;
}
.opacity-4 {
	filter: Alpha(Opacity=40);
	opacity:0.4;
}
.opacity-5 {
	filter: Alpha(Opacity=50);
	opacity:0.5;
}
.opacity-6 {
	filter: Alpha(Opacity=60);
	opacity:0.6;
}
.opacity-7 {
	filter: Alpha(Opacity=70);
	opacity:0.7;
}
.opacity-8 {
	filter: Alpha(Opacity=80);
	opacity:0.8;
}
.opacity-9 {
	filter: Alpha(Opacity=90);
	opacity:0.9;
}
.opacity-10 {
	filter: Alpha(Opacity=100);
	opacity:1;
}



hr {
	border:0;
	height: 1px;
	background-image: -webkit-linear-gradient(left, transparent, rgba(0, 0, 0, 0.2), transparent);
	background-image: -moz-linear-gradient(left, transparent, rgba(0, 0, 0, 0.2), transparent);
	background-image: -ms-linear-gradient(left, transparent, rgba(0, 0, 0, 0.2), transparent);
	background-image: -o-linear-gradient(left, transparent, rgba(0, 0, 0, 0.2), transparent);
	background-image: linear-gradient(left, transparent, rgba(0, 0, 0, 0.2), transparent);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#d6d6d6', endColorstr='#d6d6d6',GradientType=0 ); /* IE6-9 */

}
section.dark hr {
	border:0;
	height: 1px;
	background-image: -webkit-linear-gradient(left, transparent, rgba(255, 255, 255, 0.2), transparent);
	background-image: -moz-linear-gradient(left, transparent, rgba(255, 255, 255, 0.2), transparent);
	background-image: -ms-linear-gradient(left, transparent, rgba(255, 255, 255, 0.2), transparent);
	background-image: -o-linear-gradient(left, transparent, rgba(255, 255, 255, 0.2), transparent);
	background-image: linear-gradient(left, transparent, rgba(255, 255, 255, 0.2), transparent);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#d6d6d6', endColorstr='#d6d6d6',GradientType=0 ); /* IE6-9 */
}


/* black and white image */
img.image-bw {
	-webkit-filter: grayscale(100%);
	-moz-filter: grayscale(100%);
	-ms-filter: grayscale(100%);
	-o-filter: grayscale(100%);
	filter: grayscale(100%);
	filter: url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'grayscale\'><feColorMatrix type=\'matrix\' values=\'0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0\'/></filter></svg>#grayscale");
	filter: gray;
}


/* box color */
.box-default {
	color:#111;
	background-color:#c6c6c6 !important;
}
.box-danger {
	color:#fff;
	background-color:#b92c28 !important;
}
.box-warning {
	color:#fff;
	background-color:#e38d13 !important;
}
.box-info {
	color:#fff;
	background-color:#28a4c9 !important;
}
.box-primary {
	color:#fff;
	background-color:#245580 !important;
}
.box-success {
	color:#fff;
	background-color:#02B700 !important;
}
.box-white {
	color:#111;
	background-color:#fff !important;
}
.box-black {
	background-color:#111 !important;
}


/* radius */
.radius-0 {
	-webkit-border-radius: 0 !important;
	-moz-border-radius: 0 !important;
	border-radius: 0 !important;
}
.radius-3 {
	-webkit-border-radius: 3px !important;
	-moz-border-radius: 3px !important;
	border-radius: 3px !important;
}
.radius-4{
	-webkit-border-radius: 4px !important;
	-moz-border-radius: 4px !important;
	border-radius: 4px !important;
}
.radius-5 {
	-webkit-border-radius: 5px !important;
	-moz-border-radius: 5px !important;
	border-radius: 5px !important;
}
.radius-6 {
	-webkit-border-radius: 6px !important;
	-moz-border-radius: 6px !important;
	border-radius: 6px !important;
}
.radius-7 {
	-webkit-border-radius: 7px !important;
	-moz-border-radius: 7px !important;
	border-radius: 7px !important;
}
.radius-8 {
	-webkit-border-radius: 8px !important;
	-moz-border-radius: 8px !important;
	border-radius: 8px !important;
}



/* letter spacing */
.letter-spacing-0 {
	letter-spacing: 0em !important;
}
.letter-spacing-1 {
	letter-spacing: 0.1em !important;
}
.letter-spacing-2 {
	letter-spacing: 0.2em !important;
}
.letter-spacing-3 {
	letter-spacing: 0.3em !important;
}
.letter-spacing-4 {
	letter-spacing: 0.4em !important;
}
.letter-spacing-5 {
	letter-spacing: 0.5em !important;
}
.letter-spacing-6 {
	letter-spacing: 0.6em !important;
}
.letter-spacing-7 {
	letter-spacing: 0.7em !important;
}
.letter-spacing-8 {
	letter-spacing: 0.8em !important;
}
.letter-spacing-9 {
	letter-spacing: 0.9em !important;
}
.letter-spacing-10 {
	letter-spacing: 1em !important;
}



/* Tag Cloud */
.tag {
	position:relative;
	display:inline-block;
	margin: 0 6px 3px 0;
}
.tag>span.txt {
	border: 1px solid #e3e3e3;
	color: #666;
	display: inline-block;
	font-size: 11px;
	font-weight: 400;
	letter-spacing: 1px;
	padding: 8px 9px;
	text-transform: uppercase;
	float:left;
}
.tag>span.num {
	background: rgba(0,0,0,0.01);
	border-color: #e3e3e3;
	border-style: solid;
	border-width: 1px;
	display: inline-block;
	font-size: 11px;
	padding: 8px 9px 8px 11px;
	color: #aaa;
	position: relative;
	margin-left: -1px;
	float:left;
}
.tag>span.num:before {
	border-color: rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #C6C6C6;
	border-style: solid;
	border-width: 4px;
	content: "";
	display: block;
	left: 0px;
	position: absolute;
	top: 12px;
}
.tag>span.num:after {
	border-color: rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #fff;
	border-style: solid;
	border-width: 4px;
	content: "";
	display: block;
	left: -1px;
	position: absolute;
	top: 12px;
}

section.dark .tag>span.txt {
	border: 1px solid #666;
	color: #ccc;
}
section.dark .tag>span.num {
	background: rgba(0,0,0,0.01);
	border-color: #666;
	color: #ccc;
}
section.dark  .tag>span.num:before {
	border-color: rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #666;
}
section.dark  .tag>span.num:after {
	border-color: rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #212121;
}



/* Inline Search */
.inline-search {
	display:block;
	position:relative;
}
.inline-search form input.serch-input {
	background: #fff;
	border:#e3e3e3 1px solid;
	color: #aaa;
	float: left;
	font-size: 13px;
	height: 39px;
	letter-spacing: 1px;
	margin: 0;
	padding: 5px 50px 5px 10px;
	width: 100%;
}
.inline-search form button {
	background: rgba(0, 0, 0, 0);
	border-left: #e3e3e3 1px solid;
	font-size: 17px;
	width: 39px;
	height: 39px;
	line-height: 39px;
	vertical-align: bottom;

	position: absolute;
	right: 0;
}
section.dark .inline-search form input.serch-input {
	background:rgba(0,0,0,0.1);
	border:#666 1px solid;
	color:#aaa;
}
section.dark .inline-search form button {
	border-left: #666 1px solid;
	color:#999;
}
section.dark .inline-search form button:hover {
	color:#fff;
}


/* parallax */
.parallax h1,
.parallax h2,
.parallax h3,
.parallax h4,
.parallax h5,
.parallax h6,
.parallax p,
.parallax .btn,
.parallax img,
.parallax div,
.parallax {
	color:#fff;
	position:relative;
	z-index:10;
}
.parallax .btn-default {
	background-color:transparent;
}
.parallax .overlay {
	background-color: rgba(34,34,34, .3);
	position:absolute;
	left:0; right:0;
	top:0; bottom:0;
	z-index:1;
}

.parallax h1 {
	font-size:70px;
}
.parallax h2 {
	font-size:60px;
}
.parallax h3 {
	font-size:50px;
}
@media only screen and (max-width: 760px) {
	/*
		.parallax {
			background-position:center center !important;
		}
		*/
	.parallax h1 {
		font-size:55px;
	}
	.parallax h2 {
		font-size:45px;
	}
	.parallax h3 {
		font-size:45px;
	}
}
@media only screen and (max-width: 500px) {
	.parallax h1 {
		font-size:36px;
	}
	.parallax h2 {
		font-size:30px;
	}
	.parallax h3 {
		font-size:30px;
	}
}



/* video background */
section.section-video {
	border:0;
}
section.section-video .section-container-video {
	position: absolute !important;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	overflow:hidden;
	z-index: 0;
}
section.section-video .section-container-video>video {
	position:absolute;
	top:0;
	left:0;
}
section.section-video h1,
section.section-video h2,
section.section-video h3,
section.section-video h4,
section.section-video h5,
section.section-video h6,
section.section-video p,
section.section-video .btn,
section.section-video img,
section.section-video div,
section.section-video {
	color:#fff;
	position:relative;
	z-index:10;
}
section.section-video .overlay {
	background-color: rgba(34,34,34, .3);
	position:absolute;
	left:0; right:0;
	top:0; bottom:0;
	z-index:1;
}

section.section-video h1 {
	font-size:70px;
}
section.section-video h2 {
	font-size:60px;
}
section.section-video h3 {
	font-size:50px;
}
@media only screen and (max-width: 760px) {
	section.section-video h1 {
		font-size:55px;
	}
	section.section-video h2 {
		font-size:45px;
	}
	section.section-video h3 {
		font-size:45px;
	}
}
@media only screen and (max-width: 500px) {
	section.section-video h1 {
		font-size:36px;
	}
	section.section-video h2 {
		font-size:30px;
	}
	section.section-video h3 {
		font-size:30px;
	}
}



/* Columnize Text */
.columnize-2 {
	margin: 0 0 1.313em;

	-webkit-column-count: 2;
	-moz-column-count: 2;
	column-count: 2;

	-webkit-column-gap: 3em;
	-moz-column-gap: 3em;
	column-gap: 3em;


	-webkit-column-rule: 1px solid rgba(0,0,0,0.05);
	-moz-column-rule: 1px solid rgba(0,0,0,0.05);
	column-rule: 1px solid rgba(0,0,0,0.05);
}

.columnize-3 {
	margin: 0 0 1.313em;

	-webkit-column-count: 3;
	-moz-column-count: 3;
	column-count: 3;

	-webkit-column-gap: 3em;
	-moz-column-gap: 3em;
	column-gap: 3em;


	-webkit-column-rule: 1px solid rgba(0,0,0,0.05);
	-moz-column-rule: 1px solid rgba(0,0,0,0.05);
	column-rule: 1px solid rgba(0,0,0,0.05);
}

.columnize-4 {
	margin: 0 0 1.313em;

	-webkit-column-count: 4;
	-moz-column-count: 4;
	column-count: 4;

	-webkit-column-gap: 3em;
	-moz-column-gap: 3em;
	column-gap: 3em;


	-webkit-column-rule: 1px solid rgba(0,0,0,0.05);
	-moz-column-rule: 1px solid rgba(0,0,0,0.05);
	column-rule: 1px solid rgba(0,0,0,0.05);
}

.columnize-5 {
	margin: 0 0 1.313em;

	-webkit-column-count: 5;
	-moz-column-count: 5;
	column-count: 5;

	-webkit-column-gap: 3em;
	-moz-column-gap: 3em;
	column-gap: 3em;


	-webkit-column-rule: 1px solid rgba(0,0,0,0.05);
	-moz-column-rule: 1px solid rgba(0,0,0,0.05);
	column-rule: 1px solid rgba(0,0,0,0.05);
}

.columnize-6 {
	margin: 0 0 1.313em;

	-webkit-column-count: 6;
	-moz-column-count: 6;
	column-count: 6;

	-webkit-column-gap: 3em;
	-moz-column-gap: 3em;
	column-gap: 3em;


	-webkit-column-rule: 1px solid rgba(0,0,0,0.05);
	-moz-column-rule: 1px solid rgba(0,0,0,0.05);
	column-rule: 1px solid rgba(0,0,0,0.05);
}
@media only screen and (max-width: 767px) {
	.columnize-4,
	.columnize-5,
	.columnize-6 {
		margin: 0 0 1.313em;

		-webkit-column-count: 3;
		-moz-column-count: 3;
		column-count: 3;

		-webkit-column-gap: 3em;
		-moz-column-gap: 3em;
		column-gap: 3em;


		-webkit-column-rule: 1px solid rgba(0,0,0,0.05);
		-moz-column-rule: 1px solid rgba(0,0,0,0.05);
		column-rule: 1px solid rgba(0,0,0,0.05);
	}
}
@media only screen and (max-width: 480px) {
	.columnize-2,
	.columnize-3,
	.columnize-4,
	.columnize-5,
	.columnize-6 {
		font-size:13px;

		-webkit-column-count: 1;
		-moz-column-count: 1;
		column-count: 1;
	}
}






/* overlay */
.overlay {
	background:rgba(0,0,0, 0.2);
	position:absolute;
	left:0; right:0; top:0; bottom:0;
	z-index:1; /* required for IE */

	-webkit-transition: all 1s;
	-moz-transition: all 1s;
	-o-transition: all 1s;
	transition: all 1s;
}
a.overlay:hover {
	background:rgba(255,255,255, 0.2);
}

/* dark overlay */
.overlay.dark-0 {
	background-color: rgba(0,0,0,0);
}
.overlay.dark-1 {
	background-color: rgba(0,0,0,.1);
}
.overlay.dark-2 {
	background-color: rgba(0,0,0,.2);
}
.overlay.dark-3 {
	background-color: rgba(0,0,0,.3);
}
.overlay.dark-4 {
	background-color: rgba(0,0,0,.4);
}
.overlay.dark-5 {
	background-color: rgba(0,0,0,.5);
}
.overlay.dark-6 {
	background-color: rgba(0,0,0,.6);
}
.overlay.dark-7 {
	background-color: rgba(0,0,0,.7);
}
.overlay.dark-8 {
	background-color: rgba(0,0,0,.8);
}
.overlay.dark-9 {
	background-color: rgba(0,0,0,.9);
}
.overlay.dark-10 {
	background-color: rgba(0,0,0,1);
}

/* light overlay */
.overlay.light-0 {
	background-color: rgba(255,255,255,0);
}
.overlay.light-1 {
	background-color: rgba(255,255,255,.1);
}
.overlay.light-2 {
	background-color: rgba(255,255,255,.2);
}
.overlay.light-3 {
	background-color: rgba(255,255,255,.3);
}
.overlay.light-4 {
	background-color: rgba(255,255,255,.4);
}
.overlay.light-5 {
	background-color: rgba(255,255,255,.5);
}
.overlay.light-6 {
	background-color: rgba(255,255,255,.6);
}
.overlay.light-7 {
	background-color: rgba(255,255,255,.7);
}
.overlay.light-8 {
	background-color: rgba(255,255,255,.8);
}
.overlay.light-9 {
	background-color: rgba(255,255,255,.9);
}
.overlay.light-10 {
	background-color: rgba(255,255,255,1);
}

/* rounded */
.rounded {
	-webkit-border-radius: 50% !important;
	-moz-border-radius: 50% !important;
	border-radius: 50% !important;
}



/**
	ICON LIST
**/
ul.list-icons {
	margin-left: 1.75em;
	position:relative;
}
ul.list-icons>li {
	position:relative;
}
ul.list-icons>li>i {
	position: absolute;
	left: -1.75em;
	width: 14px;
	text-align: center;
	top: 5px;
}



/**
	Ribbon
**/
.ribbon {
	width: 115px;
	height: 118px;
	overflow: hidden;
	position: absolute;
	right: -2px;
	top: -2px;
	z-index: 1;
}
.ribbon .ribbon-inner {
	width: 160px;
	left: -8px;
	top: 28px;
}
.ribbon-inner {
	font-family: "Open Sans",Helvetica,Arial,sans-serif;

	-webkit-box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.15);
	-moz-box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.15);
	-o-box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.15);
	box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.15);

	-webkit-transform: translate3d(0, 0, 0);
	-webkit-backface-visibility: hidden;
	-webkit-perspective: 1000;

	-webkit-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	-o-transform: rotate(45deg);
	transform: rotate(45deg);

	background: #1abc9c;
	letter-spacing: 4px;
	text-align: center;
	position: relative;
	font-weight: 700;
	font-size: 14px;
	padding: 7px 0;
	width: 100px;
	color: #fff;
	z-index: 1;
	left: 3px;
	top: 6px;
}


/** Bootstrap 5 column equal grid
 ******************************** **/
.col-xs-5th,
.col-sm-5th,
.col-md-5th,
.col-lg-5th {
	position: relative;
	min-height: 1px;
	padding-right: 10px;
	padding-left: 10px;
	width: 20%;
	float: left;
}

@media (min-width: 768px) {
	.col-sm-5th {
		width: 20%;
		float: left;
	}
}
@media (min-width: 992px) {
	.col-md-5th {
		width: 20%;
		float: left;
	}
}
@media (min-width: 1200px) {
	.col-lg-5th {
		width: 20%;
		float: left;
	}
}

@media only screen and (max-width: 992px) {
	.col-md-5th {
		width:100%;
		float:none;
		display:block;
	}
	.col-md-5th.col-sm-5th {
		width: 20%;
		float: left;
	}
}
@media only screen and (max-width: 769px) {
	.col-md-5th.col-sm-5th {
		width:100%;
		float:none;
		display:block;
	}
}






/** 05. Progress Bars & Pie Charts
 **************************************************************** **/
.progress {
	overflow:visible;
	background:rgba(0,0,0,0.1);

	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	-o-box-shadow: none;
	box-shadow: none;

	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
}
section.dark .progress {
	background-color:#333;
}
.progress.progress-xxs {
	height:3px;
}
.progress.progress-xs {
	height:6px;
}
.progress.progress-lg {
	height:36px;
	overflow:hidden;
}
.progress.progress-lg span {
	line-height:36px;
	text-overflow:ellipsis;
	white-space: nowrap;
}


.progress.progress-lg span.inline-text {
	text-align:left;
	padding:8px;
	display:block;
	font-size:14px;
	position:absolute;
	min-width:50%;
}
.progress-bar-default {
	background-color:rgba(11,11,11,0.9);
}

/* align */
.progress-bar.text-left>span {
	padding-left:10px;
}
.progress-bar.text-right>span {
	padding-right:10px;
}



/** Easy Pie Chart
	 ******************** **/
.piechart {
	position:relative;
	display:inline-block;
	text-align:center;

	display: inline-block;
	margin: 0 15px 15px;
}
.piechart > i {
	position:absolute;
	display:block;
	text-align:center;
	font-size: 42px;
}
.piechart > span {
	position:absolute;
	display:block;
	text-align:center;
	font-size:17px;
	font-weight:bold;
}
.piechart > span.countTo {
	font-size:30px;
}

.piechart > span[class^="size-"],
.piechart > span[class*=" size-"] {
	line-height:inherit;
}

.easyPieChart {
	display: inline-block;
	position: relative;
	text-align: center;
	font-size: 22px;
	font-weight: bold;
	color: #333;
}

.easyPieChart canvas {
	position: absolute;
	top: 0;
	left: 0;
}




/** 06. Dropcap
 **************************************************************** **/
.dropcap:first-letter {
	float: left;
	font-size: 70px;
	line-height: 60px;
	padding: 4px 8px 4px 4px;
	margin-right: 6px;
	margin-top: -3px;
	display:inline-block;
	color:#333;
}

.dropcap.color:first-letter {
	color:#fff;
	background:#333;
	margin-top: 8px;
	padding: 0 8px 3px 4px;

	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
}

section.dark .dropcap:first-letter {
	color:#fff;
}

/** 07. Table
 **************************************************************** **/
/*
table {
	background-color:rgba(0,0,0,0.01);
}
table thead {
	background-color:rgba(0,0,0,0.08);
}
*/
table .btn {
	margin:0 3px;
}

section.dark table tr.odd {
	color:#ddd;
	background-color:#777;
}
section.dark table tr.odd:hover {
	background-color:#888;
}
section.dark table tr.even {
	color:#ddd;
}
section.dark table tr.even:hover {
	background-color:rgba(0,0,0,0.5);
}
section.dark .table-bordered {
	border-color:#666;
}
section.dark .table-bordered>tbody>tr>td,
section.dark .table-bordered>tbody>tr>th,
section.dark .table-bordered>tfoot>tr>td,
section.dark .table-bordered>tfoot>tr>th,
section.dark .table-bordered>thead>tr>td,
section.dark .table-bordered>thead>tr>th {
	border-color:#666;
}

table.table-vertical-middle td {
	vertical-align: middle !important;
}



/** 08. Nav Pills
 **************************************************************** **/
section .nav-pills {
	display:inline-block;
}
section .nav-pills>li>a:hover,
section .nav-pills>li>a:focus,
section .nav-pills>li.active>a,
section .nav-pills>li.active>a:hover,
section .nav-pills>li.active>a:focus {
	background-color:rgba(0,0,0,0.1) !important;
}
section .nav-pills>li>a {
	padding:6px 15px;
	margin-bottom:6px;
	letter-spacing:1px;
}

@media only screen and (max-width: 550px) {
	section .nav-pills {
		background-color:rgba(0,0,0,0.02);
	}
	section .nav-pills,
	section .nav-pills>li,
	section .nav-pills>li>a {
		display:block !important;
		float:none;
	}
}




/** 09. Blockquotes
 **************************************************************** **/
blockquote h1,
blockquote h2,
blockquote h3,
blockquote h4,
blockquote h5,
blockquote h6 {
	font-weight:bold;
	position:relative;
}
blockquote {
	border-left: 5px solid rgba(0,0,0,0.1);
	font-size: 1.3em;
	font-style: normal;
	letter-spacing: -1px;
	margin: 25px 0;
	padding: 0 0 0 25px;
	position: relative;
}
blockquote.reverse {
	border-left: 0;
	border-right: 5px solid rgba(0,0,0,0.1);
	margin: 25px 0;
	padding: 0 25px 0 0;
	text-align:right;
}

section.dark blockquote {
	border-left-color:#666;
}
section.dark blockquote.reverse {
	border-right-color:#666;
}


blockquote.quote {
	border:0;
}
blockquote.quote:before {
	font-family: font-icons;
	font-weight: 400;
	content: "\e7ad";
	position: absolute;
	width: 43px;
	height: 43px;
	line-height: 43px;
	font-size: 43px;
	top: 0;
	left: 0;
	color: #EEE;
	z-index:0;
}
blockquote.quote.reverse:before {
	left:auto;
	right:0;
	text-align:right;
}

blockquote.pull-left {
	max-width:350px;
	padding-right: 20px;
	padding-bottom: 10px;
}
blockquote.pull-right {
	max-width:350px;
	padding-left: 20px;
	padding-bottom: 10px;
}

blockquote p {
	margin-bottom:20px;
	position:relative;
}
blockquote cite {
	display: block;
	font-size: 0.75em;
	color: #9CA6B4;
}
blockquote cite:before {
	content: '\2014 \00A0';
	padding-right:8px;
}
blockquote cite:after {
	content: '\2014 \00A0';
	padding-left:10px;
}



/** 10. Background Patterns
*************************************************** **/
.pattern1 {
	background:url('../images/patterns/pattern1.png') repeat fixed;
}
.pattern2 {
	background:url('../images/patterns/pattern2.png') repeat fixed;
}
.pattern3 {
	background:url('../images/patterns/pattern3.png') repeat fixed;
}
.pattern4 {
	background:url('../images/patterns/pattern4.png') repeat fixed;
}
.pattern5 {
	background:url('../images/patterns/pattern5.png') repeat fixed;
}
.pattern6 {
	background:url('../images/patterns/pattern6.png') repeat fixed;
}
.pattern7 {
	background:url('../images/patterns/pattern7.png') repeat fixed;
}
.pattern8 {
	background:url('../images/patterns/pattern8.png') repeat fixed;
}
.pattern9 {
	background:url('../images/patterns/pattern9.png') repeat fixed;
}
.pattern10 {
	background:url('../images/patterns/pattern10.png') repeat fixed;
}
.pattern11 {
	background:url('../images/patterns/pattern11.png') repeat fixed;
}



/**	11. Colors
   colors.css v2.0.0
   http://clrs.cc

   SKINS
   - Backgrounds
   - Colors
   - Border colors
   - SVG fills
   - SVG Strokes

*************************************************** **/
/* Backgrounds */
.bg-navy {
	background-color: #001F3F !important; }

.bg-blue {
	background-color: #0074D9 !important; }

.bg-aqua {
	background-color: #7FDBFF !important; }

.bg-teal {
	background-color: #39CCCC !important; }

.bg-olive {
	background-color: #3D9970 !important; }

.bg-green {
	background-color: #2ECC40 !important; }

.bg-lime {
	background-color: #01FF70 !important; }

.bg-yellow {
	background-color: #FFDC00 !important; }

.bg-orange {
	background-color: #FF851B !important; }

.bg-red {
	background-color: #FF4136 !important; }

.bg-fuchsia {
	background-color: #F012BE !important; }

.bg-purple {
	background-color: #B10DC9 !important; }

.bg-maroon {
	background-color: #85144B !important; }

.bg-white {
	background-color: #fff !important; }

.bg-gray {
	background-color: #aaa !important; }

.bg-silver {
	background-color: #ddd !important; }

.bg-black {
	background-color: #111 !important; }

/* Text Color */
.text-navy {
	color: #001F3F !important; }

.text-blue {
	color: #0074D9 !important; }

.text-aqua {
	color: #7FDBFF !important; }

.text-teal {
	color: #39CCCC !important; }

.text-olive {
	color: #3D9970 !important; }

.text-green {
	color: #2ECC40 !important; }

.text-lime {
	color: #01FF70 !important; }

.text-yellow {
	color: #FFDC00 !important; }

.text-orange {
	color: #FF851B !important; }

.text-red {
	color: #FF4136 !important; }

.text-fuchsia {
	color: #F012BE !important; }

.text-purple {
	color: #B10DC9 !important; }

.text-maroon {
	color: #85144B !important; }

.text-white {
	color: #fff !important; }

.text-silver {
	color: #ddd !important; }

.text-gray {
	color: #aaa !important; }

.text-black {
	color: #111 !important; }

/* Border colors

   Use with another border utility that sets border-width and style
   i.e .border { border-width: 1px; border-style: solid !important; }
*/
.border-navy {
	border-color: #001F3F !important; }

.border-blue {
	border-color: #0074D9 !important; }

.border-aqua {
	border-color: #7FDBFF !important; }

.border-teal {
	border-color: #39CCCC !important; }

.border-olive {
	border-color: #3D9970 !important; }

.border-green {
	border-color: #2ECC40 !important; }

.border-lime {
	border-color: #01FF70 !important; }

.border-yellow {
	border-color: #FFDC00 !important; }

.border-orange {
	border-color: #FF851B !important; }

.border-red {
	border-color: #FF4136 !important; }

.border-fuchsia {
	border-color: #F012BE !important; }

.border-purple {
	border-color: #B10DC9 !important; }

.border-maroon {
	border-color: #85144B !important; }

.border-white {
	border-color: #fff !important; }

.border-gray {
	border-color: #aaa !important; }

.border-silver {
	border-color: #ddd !important; }

.border-black {
	border-color: #111 !important; }



/**	12. Magnific Popup v1.0.0
*************************************************** **/
.mfp-bg {
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1042;
	overflow: hidden;
	position: fixed;
	background: #0b0b0b;
	opacity: 0.8;
	filter: alpha(opacity=80); }

.mfp-wrap {
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1043;
	position: fixed;
	outline: none !important;
	-webkit-backface-visibility: hidden; }

.mfp-container {
	text-align: center;
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	padding: 0 8px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }

.mfp-container:before {
	content: '';
	display: inline-block;
	height: 100%;
	vertical-align: middle; }

.mfp-align-top .mfp-container:before {
	display: none; }

.mfp-content {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	margin: 0 auto;
	text-align: left;
	z-index: 1045; }

.mfp-inline-holder .mfp-content, .mfp-ajax-holder .mfp-content {
	width: 100%;
	cursor: auto; }

.mfp-ajax-cur {
	cursor: progress; }

.mfp-zoom-out-cur, .mfp-zoom-out-cur .mfp-image-holder .mfp-close {
	cursor: -moz-zoom-out;
	cursor: -webkit-zoom-out;
	cursor: zoom-out; }

.mfp-zoom {
	cursor: pointer;
	cursor: -webkit-zoom-in;
	cursor: -moz-zoom-in;
	cursor: zoom-in; }

.mfp-auto-cursor .mfp-content {
	cursor: auto; }

.mfp-close, .mfp-arrow, .mfp-preloader, .mfp-counter {
	-webkit-user-select: none;
	-moz-user-select: none;
	user-select: none; }

.mfp-loading.mfp-figure {
	display: none; }

.mfp-hide {
	display: none !important; }

.mfp-preloader {
	color: #CCC;
	position: absolute;
	top: 50%;
	width: auto;
	text-align: center;
	margin-top: -0.8em;
	left: 8px;
	right: 8px;
	z-index: 1044; }
.mfp-preloader a {
	color: #CCC; }
.mfp-preloader a:hover {
	color: #FFF; }

.mfp-s-ready .mfp-preloader {
	display: none; }

.mfp-s-error .mfp-content {
	display: none; }

button.mfp-close, button.mfp-arrow {
	overflow: visible;
	cursor: pointer;
	background: transparent;
	border: 0;
	-webkit-appearance: none;
	display: block;
	outline: none;
	padding: 0;
	z-index: 1046;
	-webkit-box-shadow: none;
	box-shadow: none; }
button::-moz-focus-inner {
	padding: 0;
	border: 0; }

.mfp-close {
	width: 44px;
	height: 44px;
	line-height: 44px;
	position: absolute;
	right: 0;
	top: 0;
	text-decoration: none;
	text-align: center;
	opacity: 0.65;
	filter: alpha(opacity=65);
	padding: 0 0 18px 10px;
	color: #FFF;
	font-style: normal;
	font-size: 28px;
	font-family: Arial, Baskerville, monospace; }
.mfp-close:hover, .mfp-close:focus {
	opacity: 1;
	filter: alpha(opacity=100); }
.mfp-close:active {
	top: 1px; }

.mfp-close-btn-in .mfp-close {
	color: #333; }

.mfp-image-holder .mfp-close, .mfp-iframe-holder .mfp-close {
	color: #FFF;
	right: -6px;
	text-align: right;
	padding-right: 6px;
	width: 100%; }

.mfp-counter {
	position: absolute;
	top: 0;
	right: 0;
	color: #CCC;
	font-size: 12px;
	line-height: 18px;
	white-space: nowrap; }

.mfp-arrow {
	position: absolute;
	opacity: 0.65;
	filter: alpha(opacity=65);
	margin: 0;
	top: 50%;
	margin-top: -55px;
	padding: 0;
	width: 90px;
	height: 110px;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0); }
.mfp-arrow:active {
	margin-top: -54px; }
.mfp-arrow:hover, .mfp-arrow:focus {
	opacity: 1;
	filter: alpha(opacity=100); }
.mfp-arrow:before, .mfp-arrow:after, .mfp-arrow .mfp-b, .mfp-arrow .mfp-a {
	content: '';
	display: block;
	width: 0;
	height: 0;
	position: absolute;
	left: 0;
	top: 0;
	margin-top: 35px;
	margin-left: 35px;
	border: medium inset transparent; }
.mfp-arrow:after, .mfp-arrow .mfp-a {
	border-top-width: 13px;
	border-bottom-width: 13px;
	top: 8px; }
.mfp-arrow:before, .mfp-arrow .mfp-b {
	border-top-width: 21px;
	border-bottom-width: 21px;
	opacity: 0.7; }

.mfp-arrow-left {
	left: 0; }
.mfp-arrow-left:after, .mfp-arrow-left .mfp-a {
	border-right: 17px solid #FFF;
	margin-left: 31px; }
.mfp-arrow-left:before, .mfp-arrow-left .mfp-b {
	margin-left: 25px;
	border-right: 27px solid #3F3F3F; }

.mfp-arrow-right {
	right: 0; }
.mfp-arrow-right:after, .mfp-arrow-right .mfp-a {
	border-left: 17px solid #FFF;
	margin-left: 39px; }
.mfp-arrow-right:before, .mfp-arrow-right .mfp-b {
	border-left: 27px solid #3F3F3F; }

.mfp-iframe-holder {
	padding-top: 40px;
	padding-bottom: 40px; }
.mfp-iframe-holder .mfp-content {
	line-height: 0;
	width: 100%;
	max-width: 900px; }
.mfp-iframe-holder .mfp-close {
	top: -40px; }

.mfp-iframe-scaler {
	width: 100%;
	height: 0;
	overflow: hidden;
	padding-top: 56.25%; }
.mfp-iframe-scaler iframe {
	position: absolute;
	display: block;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
	background: #000; }

/* Main image in popup */
img.mfp-img {
	width: auto;
	max-width: 100%;
	height: auto;
	display: block;
	line-height: 0;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding: 40px 0 40px;
	margin: 0 auto; }

/* The shadow behind the image */
.mfp-figure {
	line-height: 0; }
.mfp-figure:after {
	content: '';
	position: absolute;
	left: 0;
	top: 40px;
	bottom: 40px;
	display: block;
	right: 0;
	width: auto;
	height: auto;
	z-index: -1;
	box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
	background: #444; }
.mfp-figure small {
	color: #BDBDBD;
	display: block;
	font-size: 12px;
	line-height: 14px; }
.mfp-figure figure {
	margin: 0; }

.mfp-bottom-bar {
	margin-top: -36px;
	position: absolute;
	top: 100%;
	left: 0;
	width: 100%;
	cursor: auto; }

.mfp-title {
	text-align: left;
	line-height: 18px;
	color: #F3F3F3;
	word-wrap: break-word;
	padding-right: 36px; }

.mfp-image-holder .mfp-content {
	max-width: 100%; }

.mfp-gallery .mfp-image-holder .mfp-figure {
	cursor: pointer; }

@media screen and (max-width: 800px) and (orientation: landscape), screen and (max-height: 300px) {
	/**
       * Remove all paddings around the image on small screen
       */
	.mfp-img-mobile .mfp-image-holder {
		padding-left: 0;
		padding-right: 0; }
	.mfp-img-mobile img.mfp-img {
		padding: 0; }
	.mfp-img-mobile .mfp-figure:after {
		top: 0;
		bottom: 0; }
	.mfp-img-mobile .mfp-figure small {
		display: inline;
		margin-left: 5px; }
	.mfp-img-mobile .mfp-bottom-bar {
		background: rgba(0, 0, 0, 0.6);
		bottom: 0;
		margin: 0;
		top: auto;
		padding: 3px 5px;
		position: fixed;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box; }
	.mfp-img-mobile .mfp-bottom-bar:empty {
		padding: 0; }
	.mfp-img-mobile .mfp-counter {
		right: 5px;
		top: 3px; }
	.mfp-img-mobile .mfp-close {
		top: 0;
		right: 0;
		width: 35px;
		height: 35px;
		line-height: 35px;
		background: rgba(0, 0, 0, 0.6);
		position: fixed;
		text-align: center;
		padding: 0; }
}

@media all and (max-width: 900px) {
	.mfp-arrow {
		-webkit-transform: scale(0.75);
		transform: scale(0.75); }

	.mfp-arrow-left {
		-webkit-transform-origin: 0;
		transform-origin: 0; }

	.mfp-arrow-right {
		-webkit-transform-origin: 100%;
		transform-origin: 100%; }

	.mfp-container {
		padding-left: 6px;
		padding-right: 6px; }
}

.mfp-ie7 .mfp-img {
	padding: 0; }
.mfp-ie7 .mfp-bottom-bar {
	width: 600px;
	left: 50%;
	margin-left: -300px;
	margin-top: 5px;
	padding-bottom: 5px; }
.mfp-ie7 .mfp-container {
	padding: 0; }
.mfp-ie7 .mfp-content {
	padding-top: 44px; }
.mfp-ie7 .mfp-close {
	top: 0;
	right: 0;
	padding-top: 0; }




/**	13. Owl Carousel v1.3.3
*************************************************** **/
/* clearfix */
.owl-carousel .owl-wrapper:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}
/* display none until init */
.owl-carousel{
	display: none;
	position: relative;
	width: 100%;
	-ms-touch-action: pan-y;
}
.owl-carousel .owl-wrapper{
	display: none;
	position: relative;
	-webkit-transform: translate3d(0px, 0px, 0px);
}
.owl-carousel .owl-wrapper-outer{
	overflow: hidden;
	position: relative;
	width: 100%;
	z-index: 0;
}
.owl-carousel .owl-wrapper-outer.autoHeight{
	-webkit-transition: height 500ms ease-in-out;
	-moz-transition: height 500ms ease-in-out;
	-ms-transition: height 500ms ease-in-out;
	-o-transition: height 500ms ease-in-out;
	transition: height 500ms ease-in-out;
}

.owl-carousel .owl-item{
	float: left;
}
.owl-controls .owl-page,
.owl-controls .owl-buttons div{
	cursor: pointer;
}
.owl-controls {
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.owl-carousel.featured .owl-controls .owl-prev {
	margin-right:3px;
}


#progressBar {
	width: 100%;
	background: rgba(0,0,0,0.05);
}
#progressBar #bar {
	height:3px;
	width: 100%;
	background-color:#333;
}

/* mouse grab icon */
.grabbing {
	cursor:url(../images/grabbing.png) 8 8, move;
}

/* fix */
.owl-carousel  .owl-wrapper,
.owl-carousel  .owl-item{
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility:    hidden;
	-ms-backface-visibility:     hidden;
	-webkit-transform: translate3d(0,0,0);
	-moz-transform: translate3d(0,0,0);
	-ms-transform: translate3d(0,0,0);
}
/*
	 *  Owl Carousel CSS3 Transitions
	 *  v1.3.2
	 */
.owl-origin {
	-webkit-perspective: 1200px;
	-webkit-perspective-origin-x : 50%;
	-webkit-perspective-origin-y : 50%;
	-moz-perspective : 1200px;
	-moz-perspective-origin-x : 50%;
	-moz-perspective-origin-y : 50%;
	perspective : 1200px;
}
/* fade */
.owl-fade-out {
	z-index: 10;
	-webkit-animation: fadeOut .7s both ease;
	-moz-animation: fadeOut .7s both ease;
	animation: fadeOut .7s both ease;
}
.owl-fade-in {
	-webkit-animation: fadeIn .7s both ease;
	-moz-animation: fadeIn .7s both ease;
	animation: fadeIn .7s both ease;
}
/* backSlide */
.owl-backSlide-out {
	-webkit-animation: backSlideOut 1s both ease;
	-moz-animation: backSlideOut 1s both ease;
	animation: backSlideOut 1s both ease;
}
.owl-backSlide-in {
	-webkit-animation: backSlideIn 1s both ease;
	-moz-animation: backSlideIn 1s both ease;
	animation: backSlideIn 1s both ease;
}
/* goDown */
.owl-goDown-out {
	-webkit-animation: scaleToFade .7s ease both;
	-moz-animation: scaleToFade .7s ease both;
	animation: scaleToFade .7s ease both;
}
.owl-goDown-in {
	-webkit-animation: goDown .6s ease both;
	-moz-animation: goDown .6s ease both;
	animation: goDown .6s ease both;
}
/* scaleUp */
.owl-fadeUp-in {
	-webkit-animation: scaleUpFrom .5s ease both;
	-moz-animation: scaleUpFrom .5s ease both;
	animation: scaleUpFrom .5s ease both;
}

.owl-fadeUp-out {
	-webkit-animation: scaleUpTo .5s ease both;
	-moz-animation: scaleUpTo .5s ease both;
	animation: scaleUpTo .5s ease both;
}
/* Keyframes */
@-webkit-keyframes empty {
	0% {opacity: 1}
}
@-moz-keyframes empty {
	0% {opacity: 1}
}
@keyframes empty {
	0% {opacity: 1}
}
@-webkit-keyframes fadeIn {
	0% { opacity:0; }
	100% { opacity:1; }
}
@-moz-keyframes fadeIn {
	0% { opacity:0; }
	100% { opacity:1; }
}
@keyframes fadeIn {
	0% { opacity:0; }
	100% { opacity:1; }
}
@-webkit-keyframes fadeOut {
	0% { opacity:1; }
	100% { opacity:0; }
}
@-moz-keyframes fadeOut {
	0% { opacity:1; }
	100% { opacity:0; }
}
@keyframes fadeOut {
	0% { opacity:1; }
	100% { opacity:0; }
}
@-webkit-keyframes backSlideOut {
	25% { opacity: .5; -webkit-transform: translateZ(-500px); }
	75% { opacity: .5; -webkit-transform: translateZ(-500px) translateX(-200%); }
	100% { opacity: .5; -webkit-transform: translateZ(-500px) translateX(-200%); }
}
@-moz-keyframes backSlideOut {
	25% { opacity: .5; -moz-transform: translateZ(-500px); }
	75% { opacity: .5; -moz-transform: translateZ(-500px) translateX(-200%); }
	100% { opacity: .5; -moz-transform: translateZ(-500px) translateX(-200%); }
}
@keyframes backSlideOut {
	25% { opacity: .5; transform: translateZ(-500px); }
	75% { opacity: .5; transform: translateZ(-500px) translateX(-200%); }
	100% { opacity: .5; transform: translateZ(-500px) translateX(-200%); }
}
@-webkit-keyframes backSlideIn {
	0%, 25% { opacity: .5; -webkit-transform: translateZ(-500px) translateX(200%); }
	75% { opacity: .5; -webkit-transform: translateZ(-500px); }
	100% { opacity: 1; -webkit-transform: translateZ(0) translateX(0); }
}
@-moz-keyframes backSlideIn {
	0%, 25% { opacity: .5; -moz-transform: translateZ(-500px) translateX(200%); }
	75% { opacity: .5; -moz-transform: translateZ(-500px); }
	100% { opacity: 1; -moz-transform: translateZ(0) translateX(0); }
}
@keyframes backSlideIn {
	0%, 25% { opacity: .5; transform: translateZ(-500px) translateX(200%); }
	75% { opacity: .5; transform: translateZ(-500px); }
	100% { opacity: 1; transform: translateZ(0) translateX(0); }
}
@-webkit-keyframes scaleToFade {
	to { opacity: 0; -webkit-transform: scale(.8); }
}
@-moz-keyframes scaleToFade {
	to { opacity: 0; -moz-transform: scale(.8); }
}
@keyframes scaleToFade {
	to { opacity: 0; transform: scale(.8); }
}
@-webkit-keyframes goDown {
	from { -webkit-transform: translateY(-100%); }
}
@-moz-keyframes goDown {
	from { -moz-transform: translateY(-100%); }
}
@keyframes goDown {
	from { transform: translateY(-100%); }
}

@-webkit-keyframes scaleUpFrom {
	from { opacity: 0; -webkit-transform: scale(1.5); }
}
@-moz-keyframes scaleUpFrom {
	from { opacity: 0; -moz-transform: scale(1.5); }
}
@keyframes scaleUpFrom {
	from { opacity: 0; transform: scale(1.5); }
}

@-webkit-keyframes scaleUpTo {
	to { opacity: 0; -webkit-transform: scale(1.5); }
}
@-moz-keyframes scaleUpTo {
	to { opacity: 0; -moz-transform: scale(1.5); }
}
@keyframes scaleUpTo {
	to { opacity: 0; transform: scale(1.5); }
}



/**
		CUSTOM REWRITE
	**/
.owl-carousel {
	overflow:hidden;
	margin-bottom:20px;
	position:relative;
}
.owl-carousel.owl-padding-0 .owl-item {
	padding:0 !important;
}
.owl-carousel.owl-padding-1 .owl-item {
	padding:0 1px;
}
.owl-carousel.owl-padding-2 .owl-item {
	padding:0 2px;
}
.owl-carousel.owl-padding-3 .owl-item {
	padding:0 3px;
}
.owl-carousel.owl-padding-6 .owl-item {
	padding:0 6px;
}
.owl-carousel.owl-padding-10 .owl-item {
	padding:0 10px;
}
.owl-carousel.owl-padding-15 .owl-item {
	padding:0 15px;
}
.owl-carousel.owl-padding-20 .owl-item {
	padding:0 20px;
}

/* Cause width problems - better to leave left/right margins
	.owl-carousel .owl-item:first-child {
		padding-left:0;
	}
	.owl-carousel .owl-item:last-child {
		padding-right:0;
	}
	*/

.owl-carousel img {
	display:inline-block;
}
.owl-carousel.buttons-autohide .owl-buttons {
	filter: Alpha(Opacity=0);
	opacity:0;

	-webkit-transition: opacity 0.4s;
	-moz-transition: opacity 0.4s;
	-o-transition: opacity 0.4s;
	transition: opacity 0.4s;
}
.owl-carousel.buttons-autohide:hover .owl-buttons {
	filter: Alpha(Opacity=100);
	opacity:1;
}

.owl-theme .owl-controls .owl-buttons div {
	color:#121212;
	background:#fff;
	border:#fff 1px solid;

	opacity:1;
	filter: Alpha(Opacity=100);

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
section.dark .owl-theme .owl-controls .owl-buttons div {
	color:#fff;
	background:#212121;
	border:#212121 1px solid;
}

.controlls-over .owl-controls .owl-prev {
	position: absolute;
	top: 50%;
	margin-top: -28px;
	right:auto;
	left: -13px;
	zoom: 1;
	width: 36px;
	height: 36px;
	opacity: 0;
}
.controlls-over .owl-controls .owl-next {
	position: absolute;
	top: 50%;
	margin-top: -28px;
	left:auto;
	right: -13px;
	zoom: 1;
	width: 36px;
	height: 36px;
	opacity: 0;
}



.controlls-over .owl-pagination {
	text-align:center;
	position:absolute; width:60%; margin:auto;
	bottom:-20px; left:0; right:0; z-index:1;
}
.owl-theme.controlls-over .owl-controls .owl-page {
	background:rgba(0,0,0,0.3);
	margin-bottom:30px;
	position:relative;
	display:inline-block;

}
.owl-theme.controlls-over .owl-controls .owl-page:first-child {
	-webkit-border-top-left-radius: 10px;
	-webkit-border-bottom-left-radius: 10px;
	-moz-border-radius-topleft: 10px;
	-moz-border-radius-bottomleft: 10px;
	border-top-left-radius: 10px;
	border-bottom-left-radius: 10px;
}
.owl-theme.controlls-over .owl-controls .owl-page:last-child {
	-webkit-border-top-right-radius: 10px;
	-webkit-border-bottom-right-radius: 10px;
	-moz-border-radius-topright: 10px;
	-moz-border-radius-bottomright: 10px;
	border-top-right-radius: 10px;
	border-bottom-right-radius: 10px;
}
.owl-theme.controlls-over .owl-controls .owl-page span {
	background:#fff;
}
.bottom-pagination .owl-controls .owl-page {
	margin-bottom:-40px !important;
}


/* top text caption */
.owl-carousel .owl-item div {
	position:relative;
	text-align:center;
}
.owl-carousel .owl-caption {
	padding:10px;
	position:absolute !important;
	left:0; top:0; right:0;
	margin-top:0; max-width:100%;
	background:rgba(0,0,0,0.3);
	display:block; color:#fff;
}

.owl-carousel .owl-caption p {
	color:#fff;
	font-size:13px;
	line-height:20px;
	padding:0; margin:0;
}
.owl-carousel .owl-caption h1,
.owl-carousel .owl-caption h1 a,
.owl-carousel .owl-caption h2,
.owl-carousel .owl-caption h2 a,
.owl-carousel .owl-caption h3,
.owl-carousel .owl-caption h3 a {
	color:#fff;
	font-size:21px;
	line-height:21px;
	font-weight:bold;
	margin-bottom:10px;
}
.owl-carousel .owl-caption a {
	color:#fff;
	font-weight:bold;
}

.owl-carousel.controls-hover-only .owl-controls {
	filter: alpha(opacity=0);
	opacity: 0;

	-webkit-transition: opacity 0.4s;
	-moz-transition: opacity 0.4s;
	-o-transition: opacity 0.4s;
	transition: opacity 0.4s;
}
.slider:hover .owl-carousel.controls-hover-only .owl-controls,
.owl-carousel.controls-hover-only:hover .owl-controls {
	filter: alpha(opacity=100);
	opacity: 1;
}


/* Featured Item */
.owl-carousel.featured .owl-featured-item {
	width:99%; padding-bottom:15px;
	text-align:center;
	display:block;

	webkit-transition: all .2s ease-in-out;
	transition: all .2s ease-in-out;
}
.owl-carousel.featured .owl-featured-item:hover {
	background-color: #eaeaea;
}
.owl-carousel.featured .owl-featured-item a.figure {
	margin:0; padding:0;
	display:block;
	width:100%;
	height:auto;
	overflow:hidden;
	text-align:center;
	z-index:0;
	position:relative;
}

.owl-carousel.featured .owl-featured-item a.figure>img {
	height:auto !important;
	width:100% !important;
	max-width: 100% !important;
	vertical-align:top;
}
.owl-carousel.featured .owl-featured-detail {
	position:relative;
	padding-top:6px;
}

.owl-carousel.featured .owl-featured-detail:after {
	content: ' ';
	position: absolute;
	width: 0;
	height: 0;
	border-left: 10px solid transparent;
	border-right: 10px solid transparent;
	border-bottom: 10px solid #fff;
	left: 50%;
	margin-left: -10px;
	top: -10px;
	z-index:10;

	webkit-transition: all .2s ease-in-out;
	transition: all .2s ease-in-out;
}

.owl-carousel.featured .owl-featured-item:hover>.owl-featured-detail:after {
	border-bottom: 10px solid #eaeaea;
}

.owl-carousel.featured .owl-featured-detail>a.featured-title {
	color:#333;
	margin-top:6px;
	display:block;
}


.owl-carousel.featured .owl-featured-detail>span.price {
	display:block;
	margin-bottom:6px;
}


.owl-carousel.featured {
	margin-top:6px;
}
.owl-carousel.featured .owl-controls.clickable {
	top:0 !important;
	margin-top:-45px;
	position:absolute;
	right:0;
}
h2.owl-featured {
	font-size:16px;
	line-height:19px;
	border-bottom:rgba(0,0,0,0.2) 1px dashed;
	padding-bottom:6px;
	margin-bottom:10px;;
}

.owl-carousel.featured a.figure>span {
	position:absolute;
	left:0; right:0; top:0; bottom:0;
	background-color:rgba(0,0,0,0.3);
	filter: alpha(opacity=0);
	opacity: 0;

	-webkit-transition: opacity 0.3s;
	-moz-transition: opacity 0.3s;
	-o-transition: opacity 0.3s;
	transition: opacity 0.3s;
}
.owl-carousel.featured a.figure>span>i {
	color:#333;
	position:absolute;
	left:50%; top:50%;
	background:#fff;
	font-size:21px;
	width:50px; height:50px;
	line-height: 50px !important;
	text-align:center;
	margin-left:-20px;
	margin-top:-20px;

	-webkit-transition: all 0.3s;
	-moz-transition: all 0.3s;
	-o-transition: all 0.3s;
	transition: all 0.3s;

	-webkit-border-bottom-right-radius: 20px;
	-webkit-border-top-left-radius: 20px;
	-moz-border-radius-bottomright: 20px;
	-moz-border-radius-topleft: 20px;
	border-bottom-right-radius: 20px;
	border-top-left-radius: 20px;
}
.owl-carousel.featured a.figure:hover>span {
	filter: alpha(opacity=100);
	opacity: 1;
}

.owl-carousel.featured {
	overflow:inherit !important;
}
.owl-carousel.featured .owl-prev,
.owl-carousel.featured .owl-next {
	display:inline-block !important;
	color:#999 !important;
	width:30px !important;
	height:30px !important;
	line-height:30px !important;
	font-size:20px !important;
}
.owl-carousel.featured .owl-prev:hover,
.owl-carousel.featured .owl-next:hover {
	color:#121212 !important;
}


section.dark .owl-carousel.featured .owl-featured-detail>a.featured-title {
	color:#fff;
}
section.dark .owl-carousel.featured .owl-featured-detail:after {
	border-bottom-color: #212121;
}
section.dark .owl-carousel.featured .owl-featured-item:hover {
	background-color: #111;
}
section.dark .owl-carousel.featured .owl-featured-item:hover>.owl-featured-detail:after {
	border-bottom-color: #111;
}
section.dark .owl-carousel.featured .owl-prev,
section.dark .owl-carousel.featured .owl-next {
	color:#999 !important
}
section.dark .owl-carousel.featured .owl-prev:hover,
section.dark .owl-carousel.featured .owl-next:hover {
	color:#fff !important
}
section.dark h2.owl-featured {
	border-bottom-color:#666;
}

@media only screen and (max-width: 768px) {
	/*
	.owl-carousel.featured .owl-controls {
		display:none !important
	}
*/
}



/* buttons bottom */
.owl-carousel.buttons-bottom  .owl-controls{
	top:auto !important;
}
.owl-carousel.buttons-bottom .owl-next,
.owl-carousel.buttons-bottom .owl-prev {
	margin-top:-20px !important;
}

/* special carousel title */
.owl-carousel .owl-carousel-caption {
	background-color:rgba(0,0,0,0.5);
	position:absolute !important;
	top:0; left:0; right:0;
	color:#fff; padding:20px;
	height:96px; overflow:hidden;
}
.owl-carousel .owl-carousel-caption.top {
	top:0;
	bottom:auto;
}
.owl-carousel .owl-carousel-caption.bottom {
	top:auto;
	bottom:29px;
}
.owl-carousel .owl-carousel-caption h2,
.owl-carousel .owl-carousel-caption h3,
.owl-carousel .owl-carousel-caption h4 {
	color:#fff;
	font-size:16px;
	line-height:16px;
	margin-bottom:10px;
	font-weight:bold;
	overflow:hidden;
	display:block;
	width:100%;
	text-overflow:ellipsis;
	white-space: nowrap;
}
.owl-carousel .owl-carousel-caption p {
	color:#fff;
	font-size:12px;
	line-height:15px;
	padding:0; margin:0;
}

.owl-theme .owl-controls .owl-buttons div {
	/* padding:6px 13px; */
}
.owl-controls .owl-page,
.owl-controls .owl-buttons div{
	cursor: pointer;
}

.controlls-over .owl-controls .owl-prev {
	margin-left:10px;
}

.controlls-over .owl-controls .owl-next {
	margin-right:10px;
}


/* Styling Pagination*/
.owl-theme .owl-controls .owl-page{
	display: inline-block;
	zoom: 1;
	*display: inline;/*IE7 life-saver */
}
.owl-theme .owl-controls .owl-page span {
	display: block;
	width: 20px;
	height: 5px;
	margin: 5px 7px;
	filter: Alpha(Opacity=50);/*IE7 fix*/
	opacity: 0.5;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	background: #869791;
}

.owl-theme .owl-controls .owl-page.active span,
.owl-theme .owl-controls.clickable .owl-page:hover span{
	filter: Alpha(Opacity=100);/*IE7 fix*/
	opacity: 1;
}


/* If PaginationNumbers is true */

.owl-theme .owl-controls .owl-page span.owl-numbers{
	height: auto;
	width: auto;
	color: #FFF;
	padding: 2px 10px;
	font-size: 12px;
	-webkit-border-radius: 30px;
	-moz-border-radius: 30px;
	border-radius: 30px;
}

@media only screen and (max-width: 600px) {
	.owl-pagination {
		display:none;
	}
}



/**	14. Animate v3.2.3
	https://github.com/daneden/animate.css
*************************************************** **/
.animated{-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-fill-mode:both;animation-fill-mode:both}.animated.infinite{-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite}.animated.hinge{-webkit-animation-duration:2s;animation-duration:2s}@-webkit-keyframes bounce{0%,100%,20%,53%,80%{-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);transition-timing-function:cubic-bezier(0.215,.61,.355,1);-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}40%,43%{-webkit-transition-timing-function:cubic-bezier(0.755,.050,.855,.060);transition-timing-function:cubic-bezier(0.755,.050,.855,.060);-webkit-transform:translate3d(0,-30px,0);transform:translate3d(0,-30px,0)}70%{-webkit-transition-timing-function:cubic-bezier(0.755,.050,.855,.060);transition-timing-function:cubic-bezier(0.755,.050,.855,.060);-webkit-transform:translate3d(0,-15px,0);transform:translate3d(0,-15px,0)}90%{-webkit-transform:translate3d(0,-4px,0);transform:translate3d(0,-4px,0)}}@keyframes bounce{0%,100%,20%,53%,80%{-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);transition-timing-function:cubic-bezier(0.215,.61,.355,1);-webkit-transform:translate3d(0,0,0);-ms-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}40%,43%{-webkit-transition-timing-function:cubic-bezier(0.755,.050,.855,.060);transition-timing-function:cubic-bezier(0.755,.050,.855,.060);-webkit-transform:translate3d(0,-30px,0);-ms-transform:translate3d(0,-30px,0);transform:translate3d(0,-30px,0)}70%{-webkit-transition-timing-function:cubic-bezier(0.755,.050,.855,.060);transition-timing-function:cubic-bezier(0.755,.050,.855,.060);-webkit-transform:translate3d(0,-15px,0);-ms-transform:translate3d(0,-15px,0);transform:translate3d(0,-15px,0)}90%{-webkit-transform:translate3d(0,-4px,0);-ms-transform:translate3d(0,-4px,0);transform:translate3d(0,-4px,0)}}.bounce{-webkit-animation-name:bounce;animation-name:bounce;-webkit-transform-origin:center bottom;-ms-transform-origin:center bottom;transform-origin:center bottom}@-webkit-keyframes flash{0%,100%,50%{opacity:1}25%,75%{opacity:0}}@keyframes flash{0%,100%,50%{opacity:1}25%,75%{opacity:0}}.flash{-webkit-animation-name:flash;animation-name:flash}@-webkit-keyframes pulse{0%{-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}50%{-webkit-transform:scale3d(1.05,1.05,1.05);transform:scale3d(1.05,1.05,1.05)}100%{-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}}@keyframes pulse{0%{-webkit-transform:scale3d(1,1,1);-ms-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}50%{-webkit-transform:scale3d(1.05,1.05,1.05);-ms-transform:scale3d(1.05,1.05,1.05);transform:scale3d(1.05,1.05,1.05)}100%{-webkit-transform:scale3d(1,1,1);-ms-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}}.pulse{-webkit-animation-name:pulse;animation-name:pulse}@-webkit-keyframes rubberBand{0%{-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}30%{-webkit-transform:scale3d(1.25,.75,1);transform:scale3d(1.25,.75,1)}40%{-webkit-transform:scale3d(0.75,1.25,1);transform:scale3d(0.75,1.25,1)}50%{-webkit-transform:scale3d(1.15,.85,1);transform:scale3d(1.15,.85,1)}65%{-webkit-transform:scale3d(.95,1.05,1);transform:scale3d(.95,1.05,1)}75%{-webkit-transform:scale3d(1.05,.95,1);transform:scale3d(1.05,.95,1)}100%{-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}}@keyframes rubberBand{0%{-webkit-transform:scale3d(1,1,1);-ms-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}30%{-webkit-transform:scale3d(1.25,.75,1);-ms-transform:scale3d(1.25,.75,1);transform:scale3d(1.25,.75,1)}40%{-webkit-transform:scale3d(0.75,1.25,1);-ms-transform:scale3d(0.75,1.25,1);transform:scale3d(0.75,1.25,1)}50%{-webkit-transform:scale3d(1.15,.85,1);-ms-transform:scale3d(1.15,.85,1);transform:scale3d(1.15,.85,1)}65%{-webkit-transform:scale3d(.95,1.05,1);-ms-transform:scale3d(.95,1.05,1);transform:scale3d(.95,1.05,1)}75%{-webkit-transform:scale3d(1.05,.95,1);-ms-transform:scale3d(1.05,.95,1);transform:scale3d(1.05,.95,1)}100%{-webkit-transform:scale3d(1,1,1);-ms-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}}.rubberBand{-webkit-animation-name:rubberBand;animation-name:rubberBand}@-webkit-keyframes shake{0%,100%{-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}10%,30%,50%,70%,90%{-webkit-transform:translate3d(-10px,0,0);transform:translate3d(-10px,0,0)}20%,40%,60%,80%{-webkit-transform:translate3d(10px,0,0);transform:translate3d(10px,0,0)}}@keyframes shake{0%,100%{-webkit-transform:translate3d(0,0,0);-ms-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}10%,30%,50%,70%,90%{-webkit-transform:translate3d(-10px,0,0);-ms-transform:translate3d(-10px,0,0);transform:translate3d(-10px,0,0)}20%,40%,60%,80%{-webkit-transform:translate3d(10px,0,0);-ms-transform:translate3d(10px,0,0);transform:translate3d(10px,0,0)}}.shake{-webkit-animation-name:shake;animation-name:shake}@-webkit-keyframes swing{20%{-webkit-transform:rotate3d(0,0,1,15deg);transform:rotate3d(0,0,1,15deg)}40%{-webkit-transform:rotate3d(0,0,1,-10deg);transform:rotate3d(0,0,1,-10deg)}60%{-webkit-transform:rotate3d(0,0,1,5deg);transform:rotate3d(0,0,1,5deg)}80%{-webkit-transform:rotate3d(0,0,1,-5deg);transform:rotate3d(0,0,1,-5deg)}100%{-webkit-transform:rotate3d(0,0,1,0deg);transform:rotate3d(0,0,1,0deg)}}@keyframes swing{20%{-webkit-transform:rotate3d(0,0,1,15deg);-ms-transform:rotate3d(0,0,1,15deg);transform:rotate3d(0,0,1,15deg)}40%{-webkit-transform:rotate3d(0,0,1,-10deg);-ms-transform:rotate3d(0,0,1,-10deg);transform:rotate3d(0,0,1,-10deg)}60%{-webkit-transform:rotate3d(0,0,1,5deg);-ms-transform:rotate3d(0,0,1,5deg);transform:rotate3d(0,0,1,5deg)}80%{-webkit-transform:rotate3d(0,0,1,-5deg);-ms-transform:rotate3d(0,0,1,-5deg);transform:rotate3d(0,0,1,-5deg)}100%{-webkit-transform:rotate3d(0,0,1,0deg);-ms-transform:rotate3d(0,0,1,0deg);transform:rotate3d(0,0,1,0deg)}}.swing{-webkit-transform-origin:top center;-ms-transform-origin:top center;transform-origin:top center;-webkit-animation-name:swing;animation-name:swing}@-webkit-keyframes tada{0%{-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}10%,20%{-webkit-transform:scale3d(.9,.9,.9) rotate3d(0,0,1,-3deg);transform:scale3d(.9,.9,.9) rotate3d(0,0,1,-3deg)}30%,50%,70%,90%{-webkit-transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg);transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg)}40%,60%,80%{-webkit-transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg);transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg)}100%{-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}}@keyframes tada{0%{-webkit-transform:scale3d(1,1,1);-ms-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}10%,20%{-webkit-transform:scale3d(.9,.9,.9) rotate3d(0,0,1,-3deg);-ms-transform:scale3d(.9,.9,.9) rotate3d(0,0,1,-3deg);transform:scale3d(.9,.9,.9) rotate3d(0,0,1,-3deg)}30%,50%,70%,90%{-webkit-transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg);-ms-transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg);transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg)}40%,60%,80%{-webkit-transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg);-ms-transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg);transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg)}100%{-webkit-transform:scale3d(1,1,1);-ms-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}}.tada{-webkit-animation-name:tada;animation-name:tada}@-webkit-keyframes wobble{0%{-webkit-transform:none;transform:none}15%{-webkit-transform:translate3d(-25%,0,0) rotate3d(0,0,1,-5deg);transform:translate3d(-25%,0,0) rotate3d(0,0,1,-5deg)}30%{-webkit-transform:translate3d(20%,0,0) rotate3d(0,0,1,3deg);transform:translate3d(20%,0,0) rotate3d(0,0,1,3deg)}45%{-webkit-transform:translate3d(-15%,0,0) rotate3d(0,0,1,-3deg);transform:translate3d(-15%,0,0) rotate3d(0,0,1,-3deg)}60%{-webkit-transform:translate3d(10%,0,0) rotate3d(0,0,1,2deg);transform:translate3d(10%,0,0) rotate3d(0,0,1,2deg)}75%{-webkit-transform:translate3d(-5%,0,0) rotate3d(0,0,1,-1deg);transform:translate3d(-5%,0,0) rotate3d(0,0,1,-1deg)}100%{-webkit-transform:none;transform:none}}@keyframes wobble{0%{-webkit-transform:none;-ms-transform:none;transform:none}15%{-webkit-transform:translate3d(-25%,0,0) rotate3d(0,0,1,-5deg);-ms-transform:translate3d(-25%,0,0) rotate3d(0,0,1,-5deg);transform:translate3d(-25%,0,0) rotate3d(0,0,1,-5deg)}30%{-webkit-transform:translate3d(20%,0,0) rotate3d(0,0,1,3deg);-ms-transform:translate3d(20%,0,0) rotate3d(0,0,1,3deg);transform:translate3d(20%,0,0) rotate3d(0,0,1,3deg)}45%{-webkit-transform:translate3d(-15%,0,0) rotate3d(0,0,1,-3deg);-ms-transform:translate3d(-15%,0,0) rotate3d(0,0,1,-3deg);transform:translate3d(-15%,0,0) rotate3d(0,0,1,-3deg)}60%{-webkit-transform:translate3d(10%,0,0) rotate3d(0,0,1,2deg);-ms-transform:translate3d(10%,0,0) rotate3d(0,0,1,2deg);transform:translate3d(10%,0,0) rotate3d(0,0,1,2deg)}75%{-webkit-transform:translate3d(-5%,0,0) rotate3d(0,0,1,-1deg);-ms-transform:translate3d(-5%,0,0) rotate3d(0,0,1,-1deg);transform:translate3d(-5%,0,0) rotate3d(0,0,1,-1deg)}100%{-webkit-transform:none;-ms-transform:none;transform:none}}.wobble{-webkit-animation-name:wobble;animation-name:wobble}@-webkit-keyframes bounceIn{0%,100%,20%,40%,60%,80%{-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);transition-timing-function:cubic-bezier(0.215,.61,.355,1)}0%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}20%{-webkit-transform:scale3d(1.1,1.1,1.1);transform:scale3d(1.1,1.1,1.1)}40%{-webkit-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}60%{opacity:1;-webkit-transform:scale3d(1.03,1.03,1.03);transform:scale3d(1.03,1.03,1.03)}80%{-webkit-transform:scale3d(.97,.97,.97);transform:scale3d(.97,.97,.97)}100%{opacity:1;-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}}@keyframes bounceIn{0%,100%,20%,40%,60%,80%{-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);transition-timing-function:cubic-bezier(0.215,.61,.355,1)}0%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);-ms-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}20%{-webkit-transform:scale3d(1.1,1.1,1.1);-ms-transform:scale3d(1.1,1.1,1.1);transform:scale3d(1.1,1.1,1.1)}40%{-webkit-transform:scale3d(.9,.9,.9);-ms-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}60%{opacity:1;-webkit-transform:scale3d(1.03,1.03,1.03);-ms-transform:scale3d(1.03,1.03,1.03);transform:scale3d(1.03,1.03,1.03)}80%{-webkit-transform:scale3d(.97,.97,.97);-ms-transform:scale3d(.97,.97,.97);transform:scale3d(.97,.97,.97)}100%{opacity:1;-webkit-transform:scale3d(1,1,1);-ms-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}}.bounceIn{-webkit-animation-name:bounceIn;animation-name:bounceIn;-webkit-animation-duration:.75s;animation-duration:.75s}@-webkit-keyframes bounceInDown{0%,100%,60%,75%,90%{-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);transition-timing-function:cubic-bezier(0.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(0,-3000px,0);transform:translate3d(0,-3000px,0)}60%{opacity:1;-webkit-transform:translate3d(0,25px,0);transform:translate3d(0,25px,0)}75%{-webkit-transform:translate3d(0,-10px,0);transform:translate3d(0,-10px,0)}90%{-webkit-transform:translate3d(0,5px,0);transform:translate3d(0,5px,0)}100%{-webkit-transform:none;transform:none}}@keyframes bounceInDown{0%,100%,60%,75%,90%{-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);transition-timing-function:cubic-bezier(0.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(0,-3000px,0);-ms-transform:translate3d(0,-3000px,0);transform:translate3d(0,-3000px,0)}60%{opacity:1;-webkit-transform:translate3d(0,25px,0);-ms-transform:translate3d(0,25px,0);transform:translate3d(0,25px,0)}75%{-webkit-transform:translate3d(0,-10px,0);-ms-transform:translate3d(0,-10px,0);transform:translate3d(0,-10px,0)}90%{-webkit-transform:translate3d(0,5px,0);-ms-transform:translate3d(0,5px,0);transform:translate3d(0,5px,0)}100%{-webkit-transform:none;-ms-transform:none;transform:none}}.bounceInDown{-webkit-animation-name:bounceInDown;animation-name:bounceInDown}@-webkit-keyframes bounceInLeft{0%,100%,60%,75%,90%{-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);transition-timing-function:cubic-bezier(0.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(-3000px,0,0);transform:translate3d(-3000px,0,0)}60%{opacity:1;-webkit-transform:translate3d(25px,0,0);transform:translate3d(25px,0,0)}75%{-webkit-transform:translate3d(-10px,0,0);transform:translate3d(-10px,0,0)}90%{-webkit-transform:translate3d(5px,0,0);transform:translate3d(5px,0,0)}100%{-webkit-transform:none;transform:none}}@keyframes bounceInLeft{0%,100%,60%,75%,90%{-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);transition-timing-function:cubic-bezier(0.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(-3000px,0,0);-ms-transform:translate3d(-3000px,0,0);transform:translate3d(-3000px,0,0)}60%{opacity:1;-webkit-transform:translate3d(25px,0,0);-ms-transform:translate3d(25px,0,0);transform:translate3d(25px,0,0)}75%{-webkit-transform:translate3d(-10px,0,0);-ms-transform:translate3d(-10px,0,0);transform:translate3d(-10px,0,0)}90%{-webkit-transform:translate3d(5px,0,0);-ms-transform:translate3d(5px,0,0);transform:translate3d(5px,0,0)}100%{-webkit-transform:none;-ms-transform:none;transform:none}}.bounceInLeft{-webkit-animation-name:bounceInLeft;animation-name:bounceInLeft}@-webkit-keyframes bounceInRight{0%,100%,60%,75%,90%{-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);transition-timing-function:cubic-bezier(0.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(3000px,0,0);transform:translate3d(3000px,0,0)}60%{opacity:1;-webkit-transform:translate3d(-25px,0,0);transform:translate3d(-25px,0,0)}75%{-webkit-transform:translate3d(10px,0,0);transform:translate3d(10px,0,0)}90%{-webkit-transform:translate3d(-5px,0,0);transform:translate3d(-5px,0,0)}100%{-webkit-transform:none;transform:none}}@keyframes bounceInRight{0%,100%,60%,75%,90%{-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);transition-timing-function:cubic-bezier(0.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(3000px,0,0);-ms-transform:translate3d(3000px,0,0);transform:translate3d(3000px,0,0)}60%{opacity:1;-webkit-transform:translate3d(-25px,0,0);-ms-transform:translate3d(-25px,0,0);transform:translate3d(-25px,0,0)}75%{-webkit-transform:translate3d(10px,0,0);-ms-transform:translate3d(10px,0,0);transform:translate3d(10px,0,0)}90%{-webkit-transform:translate3d(-5px,0,0);-ms-transform:translate3d(-5px,0,0);transform:translate3d(-5px,0,0)}100%{-webkit-transform:none;-ms-transform:none;transform:none}}.bounceInRight{-webkit-animation-name:bounceInRight;animation-name:bounceInRight}@-webkit-keyframes bounceInUp{0%,100%,60%,75%,90%{-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);transition-timing-function:cubic-bezier(0.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(0,3000px,0);transform:translate3d(0,3000px,0)}60%{opacity:1;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}75%{-webkit-transform:translate3d(0,10px,0);transform:translate3d(0,10px,0)}90%{-webkit-transform:translate3d(0,-5px,0);transform:translate3d(0,-5px,0)}100%{-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}@keyframes bounceInUp{0%,100%,60%,75%,90%{-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);transition-timing-function:cubic-bezier(0.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(0,3000px,0);-ms-transform:translate3d(0,3000px,0);transform:translate3d(0,3000px,0)}60%{opacity:1;-webkit-transform:translate3d(0,-20px,0);-ms-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}75%{-webkit-transform:translate3d(0,10px,0);-ms-transform:translate3d(0,10px,0);transform:translate3d(0,10px,0)}90%{-webkit-transform:translate3d(0,-5px,0);-ms-transform:translate3d(0,-5px,0);transform:translate3d(0,-5px,0)}100%{-webkit-transform:translate3d(0,0,0);-ms-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}.bounceInUp{-webkit-animation-name:bounceInUp;animation-name:bounceInUp}@-webkit-keyframes bounceOut{20%{-webkit-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}50%,55%{opacity:1;-webkit-transform:scale3d(1.1,1.1,1.1);transform:scale3d(1.1,1.1,1.1)}100%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}}@keyframes bounceOut{20%{-webkit-transform:scale3d(.9,.9,.9);-ms-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}50%,55%{opacity:1;-webkit-transform:scale3d(1.1,1.1,1.1);-ms-transform:scale3d(1.1,1.1,1.1);transform:scale3d(1.1,1.1,1.1)}100%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);-ms-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}}.bounceOut{-webkit-animation-name:bounceOut;animation-name:bounceOut;-webkit-animation-duration:.75s;animation-duration:.75s}@-webkit-keyframes bounceOutDown{20%{-webkit-transform:translate3d(0,10px,0);transform:translate3d(0,10px,0)}40%,45%{opacity:1;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}100%{opacity:0;-webkit-transform:translate3d(0,2000px,0);transform:translate3d(0,2000px,0)}}@keyframes bounceOutDown{20%{-webkit-transform:translate3d(0,10px,0);-ms-transform:translate3d(0,10px,0);transform:translate3d(0,10px,0)}40%,45%{opacity:1;-webkit-transform:translate3d(0,-20px,0);-ms-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}100%{opacity:0;-webkit-transform:translate3d(0,2000px,0);-ms-transform:translate3d(0,2000px,0);transform:translate3d(0,2000px,0)}}.bounceOutDown{-webkit-animation-name:bounceOutDown;animation-name:bounceOutDown}@-webkit-keyframes bounceOutLeft{20%{opacity:1;-webkit-transform:translate3d(20px,0,0);transform:translate3d(20px,0,0)}100%{opacity:0;-webkit-transform:translate3d(-2000px,0,0);transform:translate3d(-2000px,0,0)}}@keyframes bounceOutLeft{20%{opacity:1;-webkit-transform:translate3d(20px,0,0);-ms-transform:translate3d(20px,0,0);transform:translate3d(20px,0,0)}100%{opacity:0;-webkit-transform:translate3d(-2000px,0,0);-ms-transform:translate3d(-2000px,0,0);transform:translate3d(-2000px,0,0)}}.bounceOutLeft{-webkit-animation-name:bounceOutLeft;animation-name:bounceOutLeft}@-webkit-keyframes bounceOutRight{20%{opacity:1;-webkit-transform:translate3d(-20px,0,0);transform:translate3d(-20px,0,0)}100%{opacity:0;-webkit-transform:translate3d(2000px,0,0);transform:translate3d(2000px,0,0)}}@keyframes bounceOutRight{20%{opacity:1;-webkit-transform:translate3d(-20px,0,0);-ms-transform:translate3d(-20px,0,0);transform:translate3d(-20px,0,0)}100%{opacity:0;-webkit-transform:translate3d(2000px,0,0);-ms-transform:translate3d(2000px,0,0);transform:translate3d(2000px,0,0)}}.bounceOutRight{-webkit-animation-name:bounceOutRight;animation-name:bounceOutRight}@-webkit-keyframes bounceOutUp{20%{-webkit-transform:translate3d(0,-10px,0);transform:translate3d(0,-10px,0)}40%,45%{opacity:1;-webkit-transform:translate3d(0,20px,0);transform:translate3d(0,20px,0)}100%{opacity:0;-webkit-transform:translate3d(0,-2000px,0);transform:translate3d(0,-2000px,0)}}@keyframes bounceOutUp{20%{-webkit-transform:translate3d(0,-10px,0);-ms-transform:translate3d(0,-10px,0);transform:translate3d(0,-10px,0)}40%,45%{opacity:1;-webkit-transform:translate3d(0,20px,0);-ms-transform:translate3d(0,20px,0);transform:translate3d(0,20px,0)}100%{opacity:0;-webkit-transform:translate3d(0,-2000px,0);-ms-transform:translate3d(0,-2000px,0);transform:translate3d(0,-2000px,0)}}.bounceOutUp{-webkit-animation-name:bounceOutUp;animation-name:bounceOutUp}@-webkit-keyframes fadeIn{0%{opacity:0}100%{opacity:1}}@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}.fadeIn{-webkit-animation-name:fadeIn;animation-name:fadeIn}@-webkit-keyframes fadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}100%{opacity:1;-webkit-transform:none;transform:none}}@keyframes fadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-100%,0);-ms-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}100%{opacity:1;-webkit-transform:none;-ms-transform:none;transform:none}}.fadeInDown{-webkit-animation-name:fadeInDown;animation-name:fadeInDown}@-webkit-keyframes fadeInDownBig{0%{opacity:0;-webkit-transform:translate3d(0,-2000px,0);transform:translate3d(0,-2000px,0)}100%{opacity:1;-webkit-transform:none;transform:none}}@keyframes fadeInDownBig{0%{opacity:0;-webkit-transform:translate3d(0,-2000px,0);-ms-transform:translate3d(0,-2000px,0);transform:translate3d(0,-2000px,0)}100%{opacity:1;-webkit-transform:none;-ms-transform:none;transform:none}}.fadeInDownBig{-webkit-animation-name:fadeInDownBig;animation-name:fadeInDownBig}@-webkit-keyframes fadeInLeft{0%{opacity:0;-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}100%{opacity:1;-webkit-transform:none;transform:none}}@keyframes fadeInLeft{0%{opacity:0;-webkit-transform:translate3d(-100%,0,0);-ms-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}100%{opacity:1;-webkit-transform:none;-ms-transform:none;transform:none}}.fadeInLeft{-webkit-animation-name:fadeInLeft;animation-name:fadeInLeft}@-webkit-keyframes fadeInLeftBig{0%{opacity:0;-webkit-transform:translate3d(-2000px,0,0);transform:translate3d(-2000px,0,0)}100%{opacity:1;-webkit-transform:none;transform:none}}@keyframes fadeInLeftBig{0%{opacity:0;-webkit-transform:translate3d(-2000px,0,0);-ms-transform:translate3d(-2000px,0,0);transform:translate3d(-2000px,0,0)}100%{opacity:1;-webkit-transform:none;-ms-transform:none;transform:none}}.fadeInLeftBig{-webkit-animation-name:fadeInLeftBig;animation-name:fadeInLeftBig}@-webkit-keyframes fadeInRight{0%{opacity:0;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}100%{opacity:1;-webkit-transform:none;transform:none}}@keyframes fadeInRight{0%{opacity:0;-webkit-transform:translate3d(100%,0,0);-ms-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}100%{opacity:1;-webkit-transform:none;-ms-transform:none;transform:none}}.fadeInRight{-webkit-animation-name:fadeInRight;animation-name:fadeInRight}@-webkit-keyframes fadeInRightBig{0%{opacity:0;-webkit-transform:translate3d(2000px,0,0);transform:translate3d(2000px,0,0)}100%{opacity:1;-webkit-transform:none;transform:none}}@keyframes fadeInRightBig{0%{opacity:0;-webkit-transform:translate3d(2000px,0,0);-ms-transform:translate3d(2000px,0,0);transform:translate3d(2000px,0,0)}100%{opacity:1;-webkit-transform:none;-ms-transform:none;transform:none}}.fadeInRightBig{-webkit-animation-name:fadeInRightBig;animation-name:fadeInRightBig}@-webkit-keyframes fadeInUp{0%{opacity:0;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}100%{opacity:1;-webkit-transform:none;transform:none}}@keyframes fadeInUp{0%{opacity:0;-webkit-transform:translate3d(0,100%,0);-ms-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}100%{opacity:1;-webkit-transform:none;-ms-transform:none;transform:none}}.fadeInUp{-webkit-animation-name:fadeInUp;animation-name:fadeInUp}@-webkit-keyframes fadeInUpBig{0%{opacity:0;-webkit-transform:translate3d(0,2000px,0);transform:translate3d(0,2000px,0)}100%{opacity:1;-webkit-transform:none;transform:none}}@keyframes fadeInUpBig{0%{opacity:0;-webkit-transform:translate3d(0,2000px,0);-ms-transform:translate3d(0,2000px,0);transform:translate3d(0,2000px,0)}100%{opacity:1;-webkit-transform:none;-ms-transform:none;transform:none}}.fadeInUpBig{-webkit-animation-name:fadeInUpBig;animation-name:fadeInUpBig}@-webkit-keyframes fadeOut{0%{opacity:1}100%{opacity:0}}@keyframes fadeOut{0%{opacity:1}100%{opacity:0}}.fadeOut{-webkit-animation-name:fadeOut;animation-name:fadeOut}@-webkit-keyframes fadeOutDown{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}}@keyframes fadeOutDown{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(0,100%,0);-ms-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}}.fadeOutDown{-webkit-animation-name:fadeOutDown;animation-name:fadeOutDown}@-webkit-keyframes fadeOutDownBig{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(0,2000px,0);transform:translate3d(0,2000px,0)}}@keyframes fadeOutDownBig{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(0,2000px,0);-ms-transform:translate3d(0,2000px,0);transform:translate3d(0,2000px,0)}}.fadeOutDownBig{-webkit-animation-name:fadeOutDownBig;animation-name:fadeOutDownBig}@-webkit-keyframes fadeOutLeft{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}}@keyframes fadeOutLeft{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(-100%,0,0);-ms-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}}.fadeOutLeft{-webkit-animation-name:fadeOutLeft;animation-name:fadeOutLeft}@-webkit-keyframes fadeOutLeftBig{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(-2000px,0,0);transform:translate3d(-2000px,0,0)}}@keyframes fadeOutLeftBig{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(-2000px,0,0);-ms-transform:translate3d(-2000px,0,0);transform:translate3d(-2000px,0,0)}}.fadeOutLeftBig{-webkit-animation-name:fadeOutLeftBig;animation-name:fadeOutLeftBig}@-webkit-keyframes fadeOutRight{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}}@keyframes fadeOutRight{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(100%,0,0);-ms-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}}.fadeOutRight{-webkit-animation-name:fadeOutRight;animation-name:fadeOutRight}@-webkit-keyframes fadeOutRightBig{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(2000px,0,0);transform:translate3d(2000px,0,0)}}@keyframes fadeOutRightBig{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(2000px,0,0);-ms-transform:translate3d(2000px,0,0);transform:translate3d(2000px,0,0)}}.fadeOutRightBig{-webkit-animation-name:fadeOutRightBig;animation-name:fadeOutRightBig}@-webkit-keyframes fadeOutUp{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}}@keyframes fadeOutUp{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(0,-100%,0);-ms-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}}.fadeOutUp{-webkit-animation-name:fadeOutUp;animation-name:fadeOutUp}@-webkit-keyframes fadeOutUpBig{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(0,-2000px,0);transform:translate3d(0,-2000px,0)}}@keyframes fadeOutUpBig{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(0,-2000px,0);-ms-transform:translate3d(0,-2000px,0);transform:translate3d(0,-2000px,0)}}.fadeOutUpBig{-webkit-animation-name:fadeOutUpBig;animation-name:fadeOutUpBig}@-webkit-keyframes flip{0%{-webkit-transform:perspective(400px) rotate3d(0,1,0,-360deg);transform:perspective(400px) rotate3d(0,1,0,-360deg);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}40%{-webkit-transform:perspective(400px) translate3d(0,0,150px) rotate3d(0,1,0,-190deg);transform:perspective(400px) translate3d(0,0,150px) rotate3d(0,1,0,-190deg);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}50%{-webkit-transform:perspective(400px) translate3d(0,0,150px) rotate3d(0,1,0,-170deg);transform:perspective(400px) translate3d(0,0,150px) rotate3d(0,1,0,-170deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}80%{-webkit-transform:perspective(400px) scale3d(.95,.95,.95);transform:perspective(400px) scale3d(.95,.95,.95);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}100%{-webkit-transform:perspective(400px);transform:perspective(400px);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}}@keyframes flip{0%{-webkit-transform:perspective(400px) rotate3d(0,1,0,-360deg);-ms-transform:perspective(400px) rotate3d(0,1,0,-360deg);transform:perspective(400px) rotate3d(0,1,0,-360deg);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}40%{-webkit-transform:perspective(400px) translate3d(0,0,150px) rotate3d(0,1,0,-190deg);-ms-transform:perspective(400px) translate3d(0,0,150px) rotate3d(0,1,0,-190deg);transform:perspective(400px) translate3d(0,0,150px) rotate3d(0,1,0,-190deg);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}50%{-webkit-transform:perspective(400px) translate3d(0,0,150px) rotate3d(0,1,0,-170deg);-ms-transform:perspective(400px) translate3d(0,0,150px) rotate3d(0,1,0,-170deg);transform:perspective(400px) translate3d(0,0,150px) rotate3d(0,1,0,-170deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}80%{-webkit-transform:perspective(400px) scale3d(.95,.95,.95);-ms-transform:perspective(400px) scale3d(.95,.95,.95);transform:perspective(400px) scale3d(.95,.95,.95);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}100%{-webkit-transform:perspective(400px);-ms-transform:perspective(400px);transform:perspective(400px);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}}.animated.flip{-webkit-backface-visibility:visible;-ms-backface-visibility:visible;backface-visibility:visible;-webkit-animation-name:flip;animation-name:flip}@-webkit-keyframes flipInX{0%{-webkit-transform:perspective(400px) rotate3d(1,0,0,90deg);transform:perspective(400px) rotate3d(1,0,0,90deg);-webkit-transition-timing-function:ease-in;transition-timing-function:ease-in;opacity:0}40%{-webkit-transform:perspective(400px) rotate3d(1,0,0,-20deg);transform:perspective(400px) rotate3d(1,0,0,-20deg);-webkit-transition-timing-function:ease-in;transition-timing-function:ease-in}60%{-webkit-transform:perspective(400px) rotate3d(1,0,0,10deg);transform:perspective(400px) rotate3d(1,0,0,10deg);opacity:1}80%{-webkit-transform:perspective(400px) rotate3d(1,0,0,-5deg);transform:perspective(400px) rotate3d(1,0,0,-5deg)}100%{-webkit-transform:perspective(400px);transform:perspective(400px)}}@keyframes flipInX{0%{-webkit-transform:perspective(400px) rotate3d(1,0,0,90deg);-ms-transform:perspective(400px) rotate3d(1,0,0,90deg);transform:perspective(400px) rotate3d(1,0,0,90deg);-webkit-transition-timing-function:ease-in;transition-timing-function:ease-in;opacity:0}40%{-webkit-transform:perspective(400px) rotate3d(1,0,0,-20deg);-ms-transform:perspective(400px) rotate3d(1,0,0,-20deg);transform:perspective(400px) rotate3d(1,0,0,-20deg);-webkit-transition-timing-function:ease-in;transition-timing-function:ease-in}60%{-webkit-transform:perspective(400px) rotate3d(1,0,0,10deg);-ms-transform:perspective(400px) rotate3d(1,0,0,10deg);transform:perspective(400px) rotate3d(1,0,0,10deg);opacity:1}80%{-webkit-transform:perspective(400px) rotate3d(1,0,0,-5deg);-ms-transform:perspective(400px) rotate3d(1,0,0,-5deg);transform:perspective(400px) rotate3d(1,0,0,-5deg)}100%{-webkit-transform:perspective(400px);-ms-transform:perspective(400px);transform:perspective(400px)}}.flipInX{-webkit-backface-visibility:visible!important;-ms-backface-visibility:visible!important;backface-visibility:visible!important;-webkit-animation-name:flipInX;animation-name:flipInX}@-webkit-keyframes flipInY{0%{-webkit-transform:perspective(400px) rotate3d(0,1,0,90deg);transform:perspective(400px) rotate3d(0,1,0,90deg);-webkit-transition-timing-function:ease-in;transition-timing-function:ease-in;opacity:0}40%{-webkit-transform:perspective(400px) rotate3d(0,1,0,-20deg);transform:perspective(400px) rotate3d(0,1,0,-20deg);-webkit-transition-timing-function:ease-in;transition-timing-function:ease-in}60%{-webkit-transform:perspective(400px) rotate3d(0,1,0,10deg);transform:perspective(400px) rotate3d(0,1,0,10deg);opacity:1}80%{-webkit-transform:perspective(400px) rotate3d(0,1,0,-5deg);transform:perspective(400px) rotate3d(0,1,0,-5deg)}100%{-webkit-transform:perspective(400px);transform:perspective(400px)}}@keyframes flipInY{0%{-webkit-transform:perspective(400px) rotate3d(0,1,0,90deg);-ms-transform:perspective(400px) rotate3d(0,1,0,90deg);transform:perspective(400px) rotate3d(0,1,0,90deg);-webkit-transition-timing-function:ease-in;transition-timing-function:ease-in;opacity:0}40%{-webkit-transform:perspective(400px) rotate3d(0,1,0,-20deg);-ms-transform:perspective(400px) rotate3d(0,1,0,-20deg);transform:perspective(400px) rotate3d(0,1,0,-20deg);-webkit-transition-timing-function:ease-in;transition-timing-function:ease-in}60%{-webkit-transform:perspective(400px) rotate3d(0,1,0,10deg);-ms-transform:perspective(400px) rotate3d(0,1,0,10deg);transform:perspective(400px) rotate3d(0,1,0,10deg);opacity:1}80%{-webkit-transform:perspective(400px) rotate3d(0,1,0,-5deg);-ms-transform:perspective(400px) rotate3d(0,1,0,-5deg);transform:perspective(400px) rotate3d(0,1,0,-5deg)}100%{-webkit-transform:perspective(400px);-ms-transform:perspective(400px);transform:perspective(400px)}}.flipInY{-webkit-backface-visibility:visible!important;-ms-backface-visibility:visible!important;backface-visibility:visible!important;-webkit-animation-name:flipInY;animation-name:flipInY}@-webkit-keyframes flipOutX{0%{-webkit-transform:perspective(400px);transform:perspective(400px)}30%{-webkit-transform:perspective(400px) rotate3d(1,0,0,-20deg);transform:perspective(400px) rotate3d(1,0,0,-20deg);opacity:1}100%{-webkit-transform:perspective(400px) rotate3d(1,0,0,90deg);transform:perspective(400px) rotate3d(1,0,0,90deg);opacity:0}}@keyframes flipOutX{0%{-webkit-transform:perspective(400px);-ms-transform:perspective(400px);transform:perspective(400px)}30%{-webkit-transform:perspective(400px) rotate3d(1,0,0,-20deg);-ms-transform:perspective(400px) rotate3d(1,0,0,-20deg);transform:perspective(400px) rotate3d(1,0,0,-20deg);opacity:1}100%{-webkit-transform:perspective(400px) rotate3d(1,0,0,90deg);-ms-transform:perspective(400px) rotate3d(1,0,0,90deg);transform:perspective(400px) rotate3d(1,0,0,90deg);opacity:0}}.flipOutX{-webkit-animation-name:flipOutX;animation-name:flipOutX;-webkit-animation-duration:.75s;animation-duration:.75s;-webkit-backface-visibility:visible!important;-ms-backface-visibility:visible!important;backface-visibility:visible!important}@-webkit-keyframes flipOutY{0%{-webkit-transform:perspective(400px);transform:perspective(400px)}30%{-webkit-transform:perspective(400px) rotate3d(0,1,0,-15deg);transform:perspective(400px) rotate3d(0,1,0,-15deg);opacity:1}100%{-webkit-transform:perspective(400px) rotate3d(0,1,0,90deg);transform:perspective(400px) rotate3d(0,1,0,90deg);opacity:0}}@keyframes flipOutY{0%{-webkit-transform:perspective(400px);-ms-transform:perspective(400px);transform:perspective(400px)}30%{-webkit-transform:perspective(400px) rotate3d(0,1,0,-15deg);-ms-transform:perspective(400px) rotate3d(0,1,0,-15deg);transform:perspective(400px) rotate3d(0,1,0,-15deg);opacity:1}100%{-webkit-transform:perspective(400px) rotate3d(0,1,0,90deg);-ms-transform:perspective(400px) rotate3d(0,1,0,90deg);transform:perspective(400px) rotate3d(0,1,0,90deg);opacity:0}}.flipOutY{-webkit-backface-visibility:visible!important;-ms-backface-visibility:visible!important;backface-visibility:visible!important;-webkit-animation-name:flipOutY;animation-name:flipOutY;-webkit-animation-duration:.75s;animation-duration:.75s}@-webkit-keyframes lightSpeedIn{0%{-webkit-transform:translate3d(100%,0,0) skewX(-30deg);transform:translate3d(100%,0,0) skewX(-30deg);opacity:0}60%{-webkit-transform:skewX(20deg);transform:skewX(20deg);opacity:1}80%{-webkit-transform:skewX(-5deg);transform:skewX(-5deg);opacity:1}100%{-webkit-transform:none;transform:none;opacity:1}}@keyframes lightSpeedIn{0%{-webkit-transform:translate3d(100%,0,0) skewX(-30deg);-ms-transform:translate3d(100%,0,0) skewX(-30deg);transform:translate3d(100%,0,0) skewX(-30deg);opacity:0}60%{-webkit-transform:skewX(20deg);-ms-transform:skewX(20deg);transform:skewX(20deg);opacity:1}80%{-webkit-transform:skewX(-5deg);-ms-transform:skewX(-5deg);transform:skewX(-5deg);opacity:1}100%{-webkit-transform:none;-ms-transform:none;transform:none;opacity:1}}.lightSpeedIn{-webkit-animation-name:lightSpeedIn;animation-name:lightSpeedIn;-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}@-webkit-keyframes lightSpeedOut{0%{opacity:1}100%{-webkit-transform:translate3d(100%,0,0) skewX(30deg);transform:translate3d(100%,0,0) skewX(30deg);opacity:0}}@keyframes lightSpeedOut{0%{opacity:1}100%{-webkit-transform:translate3d(100%,0,0) skewX(30deg);-ms-transform:translate3d(100%,0,0) skewX(30deg);transform:translate3d(100%,0,0) skewX(30deg);opacity:0}}.lightSpeedOut{-webkit-animation-name:lightSpeedOut;animation-name:lightSpeedOut;-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}@-webkit-keyframes rotateIn{0%{-webkit-transform-origin:center;transform-origin:center;-webkit-transform:rotate3d(0,0,1,-200deg);transform:rotate3d(0,0,1,-200deg);opacity:0}100%{-webkit-transform-origin:center;transform-origin:center;-webkit-transform:none;transform:none;opacity:1}}@keyframes rotateIn{0%{-webkit-transform-origin:center;-ms-transform-origin:center;transform-origin:center;-webkit-transform:rotate3d(0,0,1,-200deg);-ms-transform:rotate3d(0,0,1,-200deg);transform:rotate3d(0,0,1,-200deg);opacity:0}100%{-webkit-transform-origin:center;-ms-transform-origin:center;transform-origin:center;-webkit-transform:none;-ms-transform:none;transform:none;opacity:1}}.rotateIn{-webkit-animation-name:rotateIn;animation-name:rotateIn}@-webkit-keyframes rotateInDownLeft{0%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate3d(0,0,1,-45deg);transform:rotate3d(0,0,1,-45deg);opacity:0}100%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:none;transform:none;opacity:1}}@keyframes rotateInDownLeft{0%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate3d(0,0,1,-45deg);-ms-transform:rotate3d(0,0,1,-45deg);transform:rotate3d(0,0,1,-45deg);opacity:0}100%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:none;-ms-transform:none;transform:none;opacity:1}}.rotateInDownLeft{-webkit-animation-name:rotateInDownLeft;animation-name:rotateInDownLeft}@-webkit-keyframes rotateInDownRight{0%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate3d(0,0,1,45deg);transform:rotate3d(0,0,1,45deg);opacity:0}100%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:none;transform:none;opacity:1}}@keyframes rotateInDownRight{0%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate3d(0,0,1,45deg);-ms-transform:rotate3d(0,0,1,45deg);transform:rotate3d(0,0,1,45deg);opacity:0}100%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:none;-ms-transform:none;transform:none;opacity:1}}.rotateInDownRight{-webkit-animation-name:rotateInDownRight;animation-name:rotateInDownRight}@-webkit-keyframes rotateInUpLeft{0%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate3d(0,0,1,45deg);transform:rotate3d(0,0,1,45deg);opacity:0}100%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:none;transform:none;opacity:1}}@keyframes rotateInUpLeft{0%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate3d(0,0,1,45deg);-ms-transform:rotate3d(0,0,1,45deg);transform:rotate3d(0,0,1,45deg);opacity:0}100%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:none;-ms-transform:none;transform:none;opacity:1}}.rotateInUpLeft{-webkit-animation-name:rotateInUpLeft;animation-name:rotateInUpLeft}@-webkit-keyframes rotateInUpRight{0%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate3d(0,0,1,-90deg);transform:rotate3d(0,0,1,-90deg);opacity:0}100%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:none;transform:none;opacity:1}}@keyframes rotateInUpRight{0%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate3d(0,0,1,-90deg);-ms-transform:rotate3d(0,0,1,-90deg);transform:rotate3d(0,0,1,-90deg);opacity:0}100%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:none;-ms-transform:none;transform:none;opacity:1}}.rotateInUpRight{-webkit-animation-name:rotateInUpRight;animation-name:rotateInUpRight}@-webkit-keyframes rotateOut{0%{-webkit-transform-origin:center;transform-origin:center;opacity:1}100%{-webkit-transform-origin:center;transform-origin:center;-webkit-transform:rotate3d(0,0,1,200deg);transform:rotate3d(0,0,1,200deg);opacity:0}}@keyframes rotateOut{0%{-webkit-transform-origin:center;-ms-transform-origin:center;transform-origin:center;opacity:1}100%{-webkit-transform-origin:center;-ms-transform-origin:center;transform-origin:center;-webkit-transform:rotate3d(0,0,1,200deg);-ms-transform:rotate3d(0,0,1,200deg);transform:rotate3d(0,0,1,200deg);opacity:0}}.rotateOut{-webkit-animation-name:rotateOut;animation-name:rotateOut}@-webkit-keyframes rotateOutDownLeft{0%{-webkit-transform-origin:left bottom;transform-origin:left bottom;opacity:1}100%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate3d(0,0,1,45deg);transform:rotate3d(0,0,1,45deg);opacity:0}}@keyframes rotateOutDownLeft{0%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;opacity:1}100%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate3d(0,0,1,45deg);-ms-transform:rotate3d(0,0,1,45deg);transform:rotate3d(0,0,1,45deg);opacity:0}}.rotateOutDownLeft{-webkit-animation-name:rotateOutDownLeft;animation-name:rotateOutDownLeft}@-webkit-keyframes rotateOutDownRight{0%{-webkit-transform-origin:right bottom;transform-origin:right bottom;opacity:1}100%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate3d(0,0,1,-45deg);transform:rotate3d(0,0,1,-45deg);opacity:0}}@keyframes rotateOutDownRight{0%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;opacity:1}100%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate3d(0,0,1,-45deg);-ms-transform:rotate3d(0,0,1,-45deg);transform:rotate3d(0,0,1,-45deg);opacity:0}}.rotateOutDownRight{-webkit-animation-name:rotateOutDownRight;animation-name:rotateOutDownRight}@-webkit-keyframes rotateOutUpLeft{0%{-webkit-transform-origin:left bottom;transform-origin:left bottom;opacity:1}100%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate3d(0,0,1,-45deg);transform:rotate3d(0,0,1,-45deg);opacity:0}}@keyframes rotateOutUpLeft{0%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;opacity:1}100%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate3d(0,0,1,-45deg);-ms-transform:rotate3d(0,0,1,-45deg);transform:rotate3d(0,0,1,-45deg);opacity:0}}.rotateOutUpLeft{-webkit-animation-name:rotateOutUpLeft;animation-name:rotateOutUpLeft}@-webkit-keyframes rotateOutUpRight{0%{-webkit-transform-origin:right bottom;transform-origin:right bottom;opacity:1}100%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate3d(0,0,1,90deg);transform:rotate3d(0,0,1,90deg);opacity:0}}@keyframes rotateOutUpRight{0%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;opacity:1}100%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate3d(0,0,1,90deg);-ms-transform:rotate3d(0,0,1,90deg);transform:rotate3d(0,0,1,90deg);opacity:0}}.rotateOutUpRight{-webkit-animation-name:rotateOutUpRight;animation-name:rotateOutUpRight}@-webkit-keyframes hinge{0%{-webkit-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}20%,60%{-webkit-transform:rotate3d(0,0,1,80deg);transform:rotate3d(0,0,1,80deg);-webkit-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}40%,80%{-webkit-transform:rotate3d(0,0,1,60deg);transform:rotate3d(0,0,1,60deg);-webkit-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out;opacity:1}100%{-webkit-transform:translate3d(0,700px,0);transform:translate3d(0,700px,0);opacity:0}}@keyframes hinge{0%{-webkit-transform-origin:top left;-ms-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}20%,60%{-webkit-transform:rotate3d(0,0,1,80deg);-ms-transform:rotate3d(0,0,1,80deg);transform:rotate3d(0,0,1,80deg);-webkit-transform-origin:top left;-ms-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}40%,80%{-webkit-transform:rotate3d(0,0,1,60deg);-ms-transform:rotate3d(0,0,1,60deg);transform:rotate3d(0,0,1,60deg);-webkit-transform-origin:top left;-ms-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out;opacity:1}100%{-webkit-transform:translate3d(0,700px,0);-ms-transform:translate3d(0,700px,0);transform:translate3d(0,700px,0);opacity:0}}.hinge{-webkit-animation-name:hinge;animation-name:hinge}@-webkit-keyframes rollIn{0%{opacity:0;-webkit-transform:translate3d(-100%,0,0) rotate3d(0,0,1,-120deg);transform:translate3d(-100%,0,0) rotate3d(0,0,1,-120deg)}100%{opacity:1;-webkit-transform:none;transform:none}}@keyframes rollIn{0%{opacity:0;-webkit-transform:translate3d(-100%,0,0) rotate3d(0,0,1,-120deg);-ms-transform:translate3d(-100%,0,0) rotate3d(0,0,1,-120deg);transform:translate3d(-100%,0,0) rotate3d(0,0,1,-120deg)}100%{opacity:1;-webkit-transform:none;-ms-transform:none;transform:none}}.rollIn{-webkit-animation-name:rollIn;animation-name:rollIn}@-webkit-keyframes rollOut{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(100%,0,0) rotate3d(0,0,1,120deg);transform:translate3d(100%,0,0) rotate3d(0,0,1,120deg)}}@keyframes rollOut{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(100%,0,0) rotate3d(0,0,1,120deg);-ms-transform:translate3d(100%,0,0) rotate3d(0,0,1,120deg);transform:translate3d(100%,0,0) rotate3d(0,0,1,120deg)}}.rollOut{-webkit-animation-name:rollOut;animation-name:rollOut}@-webkit-keyframes zoomIn{0%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}50%{opacity:1}}@keyframes zoomIn{0%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);-ms-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}50%{opacity:1}}.zoomIn{-webkit-animation-name:zoomIn;animation-name:zoomIn}@-webkit-keyframes zoomInDown{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,-1000px,0);transform:scale3d(.1,.1,.1) translate3d(0,-1000px,0);-webkit-animation-timing-function:cubic-bezier(0.55,.055,.675,.19);animation-timing-function:cubic-bezier(0.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,60px,0);transform:scale3d(.475,.475,.475) translate3d(0,60px,0);-webkit-animation-timing-function:cubic-bezier(0.175,.885,.32,1);animation-timing-function:cubic-bezier(0.175,.885,.32,1)}}@keyframes zoomInDown{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,-1000px,0);-ms-transform:scale3d(.1,.1,.1) translate3d(0,-1000px,0);transform:scale3d(.1,.1,.1) translate3d(0,-1000px,0);-webkit-animation-timing-function:cubic-bezier(0.55,.055,.675,.19);animation-timing-function:cubic-bezier(0.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,60px,0);-ms-transform:scale3d(.475,.475,.475) translate3d(0,60px,0);transform:scale3d(.475,.475,.475) translate3d(0,60px,0);-webkit-animation-timing-function:cubic-bezier(0.175,.885,.32,1);animation-timing-function:cubic-bezier(0.175,.885,.32,1)}}.zoomInDown{-webkit-animation-name:zoomInDown;animation-name:zoomInDown}@-webkit-keyframes zoomInLeft{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(-1000px,0,0);transform:scale3d(.1,.1,.1) translate3d(-1000px,0,0);-webkit-animation-timing-function:cubic-bezier(0.55,.055,.675,.19);animation-timing-function:cubic-bezier(0.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(10px,0,0);transform:scale3d(.475,.475,.475) translate3d(10px,0,0);-webkit-animation-timing-function:cubic-bezier(0.175,.885,.32,1);animation-timing-function:cubic-bezier(0.175,.885,.32,1)}}@keyframes zoomInLeft{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(-1000px,0,0);-ms-transform:scale3d(.1,.1,.1) translate3d(-1000px,0,0);transform:scale3d(.1,.1,.1) translate3d(-1000px,0,0);-webkit-animation-timing-function:cubic-bezier(0.55,.055,.675,.19);animation-timing-function:cubic-bezier(0.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(10px,0,0);-ms-transform:scale3d(.475,.475,.475) translate3d(10px,0,0);transform:scale3d(.475,.475,.475) translate3d(10px,0,0);-webkit-animation-timing-function:cubic-bezier(0.175,.885,.32,1);animation-timing-function:cubic-bezier(0.175,.885,.32,1)}}.zoomInLeft{-webkit-animation-name:zoomInLeft;animation-name:zoomInLeft}@-webkit-keyframes zoomInRight{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(1000px,0,0);transform:scale3d(.1,.1,.1) translate3d(1000px,0,0);-webkit-animation-timing-function:cubic-bezier(0.55,.055,.675,.19);animation-timing-function:cubic-bezier(0.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(-10px,0,0);transform:scale3d(.475,.475,.475) translate3d(-10px,0,0);-webkit-animation-timing-function:cubic-bezier(0.175,.885,.32,1);animation-timing-function:cubic-bezier(0.175,.885,.32,1)}}@keyframes zoomInRight{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(1000px,0,0);-ms-transform:scale3d(.1,.1,.1) translate3d(1000px,0,0);transform:scale3d(.1,.1,.1) translate3d(1000px,0,0);-webkit-animation-timing-function:cubic-bezier(0.55,.055,.675,.19);animation-timing-function:cubic-bezier(0.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(-10px,0,0);-ms-transform:scale3d(.475,.475,.475) translate3d(-10px,0,0);transform:scale3d(.475,.475,.475) translate3d(-10px,0,0);-webkit-animation-timing-function:cubic-bezier(0.175,.885,.32,1);animation-timing-function:cubic-bezier(0.175,.885,.32,1)}}.zoomInRight{-webkit-animation-name:zoomInRight;animation-name:zoomInRight}@-webkit-keyframes zoomInUp{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,1000px,0);transform:scale3d(.1,.1,.1) translate3d(0,1000px,0);-webkit-animation-timing-function:cubic-bezier(0.55,.055,.675,.19);animation-timing-function:cubic-bezier(0.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);-webkit-animation-timing-function:cubic-bezier(0.175,.885,.32,1);animation-timing-function:cubic-bezier(0.175,.885,.32,1)}}@keyframes zoomInUp{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,1000px,0);-ms-transform:scale3d(.1,.1,.1) translate3d(0,1000px,0);transform:scale3d(.1,.1,.1) translate3d(0,1000px,0);-webkit-animation-timing-function:cubic-bezier(0.55,.055,.675,.19);animation-timing-function:cubic-bezier(0.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);-ms-transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);-webkit-animation-timing-function:cubic-bezier(0.175,.885,.32,1);animation-timing-function:cubic-bezier(0.175,.885,.32,1)}}.zoomInUp{-webkit-animation-name:zoomInUp;animation-name:zoomInUp}@-webkit-keyframes zoomOut{0%{opacity:1}50%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}100%{opacity:0}}@keyframes zoomOut{0%{opacity:1}50%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);-ms-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}100%{opacity:0}}.zoomOut{-webkit-animation-name:zoomOut;animation-name:zoomOut}@-webkit-keyframes zoomOutDown{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);-webkit-animation-timing-function:cubic-bezier(0.55,.055,.675,.19);animation-timing-function:cubic-bezier(0.55,.055,.675,.19)}100%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,2000px,0);transform:scale3d(.1,.1,.1) translate3d(0,2000px,0);-webkit-transform-origin:center bottom;transform-origin:center bottom;-webkit-animation-timing-function:cubic-bezier(0.175,.885,.32,1);animation-timing-function:cubic-bezier(0.175,.885,.32,1)}}@keyframes zoomOutDown{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);-ms-transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);-webkit-animation-timing-function:cubic-bezier(0.55,.055,.675,.19);animation-timing-function:cubic-bezier(0.55,.055,.675,.19)}100%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,2000px,0);-ms-transform:scale3d(.1,.1,.1) translate3d(0,2000px,0);transform:scale3d(.1,.1,.1) translate3d(0,2000px,0);-webkit-transform-origin:center bottom;-ms-transform-origin:center bottom;transform-origin:center bottom;-webkit-animation-timing-function:cubic-bezier(0.175,.885,.32,1);animation-timing-function:cubic-bezier(0.175,.885,.32,1)}}.zoomOutDown{-webkit-animation-name:zoomOutDown;animation-name:zoomOutDown}@-webkit-keyframes zoomOutLeft{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(42px,0,0);transform:scale3d(.475,.475,.475) translate3d(42px,0,0)}100%{opacity:0;-webkit-transform:scale(.1) translate3d(-2000px,0,0);transform:scale(.1) translate3d(-2000px,0,0);-webkit-transform-origin:left center;transform-origin:left center}}@keyframes zoomOutLeft{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(42px,0,0);-ms-transform:scale3d(.475,.475,.475) translate3d(42px,0,0);transform:scale3d(.475,.475,.475) translate3d(42px,0,0)}100%{opacity:0;-webkit-transform:scale(.1) translate3d(-2000px,0,0);-ms-transform:scale(.1) translate3d(-2000px,0,0);transform:scale(.1) translate3d(-2000px,0,0);-webkit-transform-origin:left center;-ms-transform-origin:left center;transform-origin:left center}}.zoomOutLeft{-webkit-animation-name:zoomOutLeft;animation-name:zoomOutLeft}@-webkit-keyframes zoomOutRight{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(-42px,0,0);transform:scale3d(.475,.475,.475) translate3d(-42px,0,0)}100%{opacity:0;-webkit-transform:scale(.1) translate3d(2000px,0,0);transform:scale(.1) translate3d(2000px,0,0);-webkit-transform-origin:right center;transform-origin:right center}}@keyframes zoomOutRight{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(-42px,0,0);-ms-transform:scale3d(.475,.475,.475) translate3d(-42px,0,0);transform:scale3d(.475,.475,.475) translate3d(-42px,0,0)}100%{opacity:0;-webkit-transform:scale(.1) translate3d(2000px,0,0);-ms-transform:scale(.1) translate3d(2000px,0,0);transform:scale(.1) translate3d(2000px,0,0);-webkit-transform-origin:right center;-ms-transform-origin:right center;transform-origin:right center}}.zoomOutRight{-webkit-animation-name:zoomOutRight;animation-name:zoomOutRight}@-webkit-keyframes zoomOutUp{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,60px,0);transform:scale3d(.475,.475,.475) translate3d(0,60px,0);-webkit-animation-timing-function:cubic-bezier(0.55,.055,.675,.19);animation-timing-function:cubic-bezier(0.55,.055,.675,.19)}100%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,-2000px,0);transform:scale3d(.1,.1,.1) translate3d(0,-2000px,0);-webkit-transform-origin:center bottom;transform-origin:center bottom;-webkit-animation-timing-function:cubic-bezier(0.175,.885,.32,1);animation-timing-function:cubic-bezier(0.175,.885,.32,1)}}@keyframes zoomOutUp{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,60px,0);-ms-transform:scale3d(.475,.475,.475) translate3d(0,60px,0);transform:scale3d(.475,.475,.475) translate3d(0,60px,0);-webkit-animation-timing-function:cubic-bezier(0.55,.055,.675,.19);animation-timing-function:cubic-bezier(0.55,.055,.675,.19)}100%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,-2000px,0);-ms-transform:scale3d(.1,.1,.1) translate3d(0,-2000px,0);transform:scale3d(.1,.1,.1) translate3d(0,-2000px,0);-webkit-transform-origin:center bottom;-ms-transform-origin:center bottom;transform-origin:center bottom;-webkit-animation-timing-function:cubic-bezier(0.175,.885,.32,1);animation-timing-function:cubic-bezier(0.175,.885,.32,1)}}.zoomOutUp{-webkit-animation-name:zoomOutUp;animation-name:zoomOutUp}@-webkit-keyframes slideInDown{0%{-webkit-transform:translateY(-100%);transform:translateY(-100%);visibility:visible}100%{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slideInDown{0%{-webkit-transform:translateY(-100%);-ms-transform:translateY(-100%);transform:translateY(-100%);visibility:visible}100%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}}.slideInDown{-webkit-animation-name:slideInDown;animation-name:slideInDown}@-webkit-keyframes slideInLeft{0%{-webkit-transform:translateX(-100%);transform:translateX(-100%);visibility:visible}100%{-webkit-transform:translateX(0);transform:translateX(0)}}@keyframes slideInLeft{0%{-webkit-transform:translateX(-100%);-ms-transform:translateX(-100%);transform:translateX(-100%);visibility:visible}100%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}}.slideInLeft{-webkit-animation-name:slideInLeft;animation-name:slideInLeft}@-webkit-keyframes slideInRight{0%{-webkit-transform:translateX(100%);transform:translateX(100%);visibility:visible}100%{-webkit-transform:translateX(0);transform:translateX(0)}}@keyframes slideInRight{0%{-webkit-transform:translateX(100%);-ms-transform:translateX(100%);transform:translateX(100%);visibility:visible}100%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}}.slideInRight{-webkit-animation-name:slideInRight;animation-name:slideInRight}@-webkit-keyframes slideInUp{0%{-webkit-transform:translateY(100%);transform:translateY(100%);visibility:visible}100%{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slideInUp{0%{-webkit-transform:translateY(100%);-ms-transform:translateY(100%);transform:translateY(100%);visibility:visible}100%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}}.slideInUp{-webkit-animation-name:slideInUp;animation-name:slideInUp}@-webkit-keyframes slideOutDown{0%{-webkit-transform:translateY(0);transform:translateY(0)}100%{visibility:hidden;-webkit-transform:translateY(100%);transform:translateY(100%)}}@keyframes slideOutDown{0%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}100%{visibility:hidden;-webkit-transform:translateY(100%);-ms-transform:translateY(100%);transform:translateY(100%)}}.slideOutDown{-webkit-animation-name:slideOutDown;animation-name:slideOutDown}@-webkit-keyframes slideOutLeft{0%{-webkit-transform:translateX(0);transform:translateX(0)}100%{visibility:hidden;-webkit-transform:translateX(-100%);transform:translateX(-100%)}}@keyframes slideOutLeft{0%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}100%{visibility:hidden;-webkit-transform:translateX(-100%);-ms-transform:translateX(-100%);transform:translateX(-100%)}}.slideOutLeft{-webkit-animation-name:slideOutLeft;animation-name:slideOutLeft}@-webkit-keyframes slideOutRight{0%{-webkit-transform:translateX(0);transform:translateX(0)}100%{visibility:hidden;-webkit-transform:translateX(100%);transform:translateX(100%)}}@keyframes slideOutRight{0%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}100%{visibility:hidden;-webkit-transform:translateX(100%);-ms-transform:translateX(100%);transform:translateX(100%)}}.slideOutRight{-webkit-animation-name:slideOutRight;animation-name:slideOutRight}@-webkit-keyframes slideOutUp{0%{-webkit-transform:translateY(0);transform:translateY(0)}100%{visibility:hidden;-webkit-transform:translateY(-100%);transform:translateY(-100%)}}@keyframes slideOutUp{0%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}100%{visibility:hidden;-webkit-transform:translateY(-100%);-ms-transform:translateY(-100%);transform:translateY(-100%)}}.slideOutUp{-webkit-animation-name:slideOutUp;animation-name:slideOutUp}





/**	15. Fontawesome v4.4.0
	http://fortawesome.github.io/Font-Awesome/
*************************************************** **/
@font-face{
	font-family:'FontAwesome';
	src:url('/templates/assets/fonts/home/<USER>');
	src:url('/templates/assets/fonts/home/<USER>') format('embedded-opentype'),
	url('/templates/assets/fonts/home/<USER>') format('woff2'),
	url('/templates/assets/fonts/home/<USER>') format('woff'),
	url('/templates/assets/fonts/home/<USER>') format('truetype'),
	url('/templates/assets/fonts/home/<USER>') format('svg');
	font-weight:normal;
	font-style:normal;
}

.fa{display:inline-block;font:normal normal normal 14px/1 FontAwesome;font-size:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.fa-lg{font-size:1.33333333em;line-height:.75em;vertical-align:-15%}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-fw{width:1.28571429em;text-align:center}.fa-ul{padding-left:0;margin-left:2.14285714em;list-style-type:none}.fa-ul>li{position:relative}.fa-li{position:absolute;left:-2.14285714em;width:2.14285714em;top:.14285714em;text-align:center}.fa-li.fa-lg{left:-1.85714286em}.fa-border{padding:.2em .25em .15em;border:solid .08em #eee;border-radius:.1em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left{margin-right:.3em}.fa.fa-pull-right{margin-left:.3em}.pull-right{float:right}.pull-left{float:left}.fa.pull-left{margin-right:.3em}.fa.pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}.fa-rotate-90{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=1);-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2);-webkit-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);-webkit-transform:rotate(270deg);-ms-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);-webkit-transform:scale(-1, 1);-ms-transform:scale(-1, 1);transform:scale(-1, 1)}.fa-flip-vertical{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);-webkit-transform:scale(1, -1);-ms-transform:scale(1, -1);transform:scale(1, -1)}:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-flip-horizontal,:root .fa-flip-vertical{filter:none}.fa-stack{position:relative;display:inline-block;width:2em;height:2em;line-height:2em;vertical-align:middle}.fa-stack-1x,.fa-stack-2x{position:absolute;left:0;width:100%;text-align:center}.fa-stack-1x{line-height:inherit}.fa-stack-2x{font-size:2em}.fa-inverse{color:#fff}.fa-glass:before{content:"\f000"}.fa-music:before{content:"\f001"}.fa-search:before{content:"\f002"}.fa-envelope-o:before{content:"\f003"}.fa-heart:before{content:"\f004"}.fa-star:before{content:"\f005"}.fa-star-o:before{content:"\f006"}.fa-user:before{content:"\f007"}.fa-film:before{content:"\f008"}.fa-th-large:before{content:"\f009"}.fa-th:before{content:"\f00a"}.fa-th-list:before{content:"\f00b"}.fa-check:before{content:"\f00c"}.fa-remove:before,.fa-close:before,.fa-times:before{content:"\f00d"}.fa-search-plus:before{content:"\f00e"}.fa-search-minus:before{content:"\f010"}.fa-power-off:before{content:"\f011"}.fa-signal:before{content:"\f012"}.fa-gear:before,.fa-cog:before{content:"\f013"}.fa-trash-o:before{content:"\f014"}.fa-home:before{content:"\f015"}.fa-file-o:before{content:"\f016"}.fa-clock-o:before{content:"\f017"}.fa-road:before{content:"\f018"}.fa-download:before{content:"\f019"}.fa-arrow-circle-o-down:before{content:"\f01a"}.fa-arrow-circle-o-up:before{content:"\f01b"}.fa-inbox:before{content:"\f01c"}.fa-play-circle-o:before{content:"\f01d"}.fa-rotate-right:before,.fa-repeat:before{content:"\f01e"}.fa-refresh:before{content:"\f021"}.fa-list-alt:before{content:"\f022"}.fa-lock:before{content:"\f023"}.fa-flag:before{content:"\f024"}.fa-headphones:before{content:"\f025"}.fa-volume-off:before{content:"\f026"}.fa-volume-down:before{content:"\f027"}.fa-volume-up:before{content:"\f028"}.fa-qrcode:before{content:"\f029"}.fa-barcode:before{content:"\f02a"}.fa-tag:before{content:"\f02b"}.fa-tags:before{content:"\f02c"}.fa-book:before{content:"\f02d"}.fa-bookmark:before{content:"\f02e"}.fa-print:before{content:"\f02f"}.fa-camera:before{content:"\f030"}.fa-font:before{content:"\f031"}.fa-bold:before{content:"\f032"}.fa-italic:before{content:"\f033"}.fa-text-height:before{content:"\f034"}.fa-text-width:before{content:"\f035"}.fa-align-left:before{content:"\f036"}.fa-align-center:before{content:"\f037"}.fa-align-right:before{content:"\f038"}.fa-align-justify:before{content:"\f039"}.fa-list:before{content:"\f03a"}.fa-dedent:before,.fa-outdent:before{content:"\f03b"}.fa-indent:before{content:"\f03c"}.fa-video-camera:before{content:"\f03d"}.fa-photo:before,.fa-image:before,.fa-picture-o:before{content:"\f03e"}.fa-pencil:before{content:"\f040"}.fa-map-marker:before{content:"\f041"}.fa-adjust:before{content:"\f042"}.fa-tint:before{content:"\f043"}.fa-edit:before,.fa-pencil-square-o:before{content:"\f044"}.fa-share-square-o:before{content:"\f045"}.fa-check-square-o:before{content:"\f046"}.fa-arrows:before{content:"\f047"}.fa-step-backward:before{content:"\f048"}.fa-fast-backward:before{content:"\f049"}.fa-backward:before{content:"\f04a"}.fa-play:before{content:"\f04b"}.fa-pause:before{content:"\f04c"}.fa-stop:before{content:"\f04d"}.fa-forward:before{content:"\f04e"}.fa-fast-forward:before{content:"\f050"}.fa-step-forward:before{content:"\f051"}.fa-eject:before{content:"\f052"}.fa-chevron-left:before{content:"\f053"}.fa-chevron-right:before{content:"\f054"}.fa-plus-circle:before{content:"\f055"}.fa-minus-circle:before{content:"\f056"}.fa-times-circle:before{content:"\f057"}.fa-check-circle:before{content:"\f058"}.fa-question-circle:before{content:"\f059"}.fa-info-circle:before{content:"\f05a"}.fa-crosshairs:before{content:"\f05b"}.fa-times-circle-o:before{content:"\f05c"}.fa-check-circle-o:before{content:"\f05d"}.fa-ban:before{content:"\f05e"}.fa-arrow-left:before{content:"\f060"}.fa-arrow-right:before{content:"\f061"}.fa-arrow-up:before{content:"\f062"}.fa-arrow-down:before{content:"\f063"}.fa-mail-forward:before,.fa-share:before{content:"\f064"}.fa-expand:before{content:"\f065"}.fa-compress:before{content:"\f066"}.fa-plus:before{content:"\f067"}.fa-minus:before{content:"\f068"}.fa-asterisk:before{content:"\f069"}.fa-exclamation-circle:before{content:"\f06a"}.fa-gift:before{content:"\f06b"}.fa-leaf:before{content:"\f06c"}.fa-fire:before{content:"\f06d"}.fa-eye:before{content:"\f06e"}.fa-eye-slash:before{content:"\f070"}.fa-warning:before,.fa-exclamation-triangle:before{content:"\f071"}.fa-plane:before{content:"\f072"}.fa-calendar:before{content:"\f073"}.fa-random:before{content:"\f074"}.fa-comment:before{content:"\f075"}.fa-magnet:before{content:"\f076"}.fa-chevron-up:before{content:"\f077"}.fa-chevron-down:before{content:"\f078"}.fa-retweet:before{content:"\f079"}.fa-shopping-cart:before{content:"\f07a"}.fa-folder:before{content:"\f07b"}.fa-folder-open:before{content:"\f07c"}.fa-arrows-v:before{content:"\f07d"}.fa-arrows-h:before{content:"\f07e"}.fa-bar-chart-o:before,.fa-bar-chart:before{content:"\f080"}.fa-twitter-square:before{content:"\f081"}.fa-facebook-square:before{content:"\f082"}.fa-camera-retro:before{content:"\f083"}.fa-key:before{content:"\f084"}.fa-gears:before,.fa-cogs:before{content:"\f085"}.fa-comments:before{content:"\f086"}.fa-thumbs-o-up:before{content:"\f087"}.fa-thumbs-o-down:before{content:"\f088"}.fa-star-half:before{content:"\f089"}.fa-heart-o:before{content:"\f08a"}.fa-sign-out:before{content:"\f08b"}.fa-linkedin-square:before{content:"\f08c"}.fa-thumb-tack:before{content:"\f08d"}.fa-external-link:before{content:"\f08e"}.fa-sign-in:before{content:"\f090"}.fa-trophy:before{content:"\f091"}.fa-github-square:before{content:"\f092"}.fa-upload:before{content:"\f093"}.fa-lemon-o:before{content:"\f094"}.fa-phone:before{content:"\f095"}.fa-square-o:before{content:"\f096"}.fa-bookmark-o:before{content:"\f097"}.fa-phone-square:before{content:"\f098"}.fa-twitter:before{content:"\f099"}.fa-facebook-f:before,.fa-facebook:before{content:"\f09a"}.fa-github:before{content:"\f09b"}.fa-unlock:before{content:"\f09c"}.fa-credit-card:before{content:"\f09d"}.fa-feed:before,.fa-rss:before{content:"\f09e"}.fa-hdd-o:before{content:"\f0a0"}.fa-bullhorn:before{content:"\f0a1"}.fa-bell:before{content:"\f0f3"}.fa-certificate:before{content:"\f0a3"}.fa-hand-o-right:before{content:"\f0a4"}.fa-hand-o-left:before{content:"\f0a5"}.fa-hand-o-up:before{content:"\f0a6"}.fa-hand-o-down:before{content:"\f0a7"}.fa-arrow-circle-left:before{content:"\f0a8"}.fa-arrow-circle-right:before{content:"\f0a9"}.fa-arrow-circle-up:before{content:"\f0aa"}.fa-arrow-circle-down:before{content:"\f0ab"}.fa-globe:before{content:"\f0ac"}.fa-wrench:before{content:"\f0ad"}.fa-tasks:before{content:"\f0ae"}.fa-filter:before{content:"\f0b0"}.fa-briefcase:before{content:"\f0b1"}.fa-arrows-alt:before{content:"\f0b2"}.fa-group:before,.fa-users:before{content:"\f0c0"}.fa-chain:before,.fa-link:before{content:"\f0c1"}.fa-cloud:before{content:"\f0c2"}.fa-flask:before{content:"\f0c3"}.fa-cut:before,.fa-scissors:before{content:"\f0c4"}.fa-copy:before,.fa-files-o:before{content:"\f0c5"}.fa-paperclip:before{content:"\f0c6"}.fa-save:before,.fa-floppy-o:before{content:"\f0c7"}.fa-square:before{content:"\f0c8"}.fa-navicon:before,.fa-reorder:before,.fa-bars:before{content:"\f0c9"}.fa-list-ul:before{content:"\f0ca"}.fa-list-ol:before{content:"\f0cb"}.fa-strikethrough:before{content:"\f0cc"}.fa-underline:before{content:"\f0cd"}.fa-table:before{content:"\f0ce"}.fa-magic:before{content:"\f0d0"}.fa-truck:before{content:"\f0d1"}.fa-pinterest:before{content:"\f0d2"}.fa-pinterest-square:before{content:"\f0d3"}.fa-google-plus-square:before{content:"\f0d4"}.fa-google-plus:before{content:"\f0d5"}.fa-money:before{content:"\f0d6"}.fa-caret-down:before{content:"\f0d7"}.fa-caret-up:before{content:"\f0d8"}.fa-caret-left:before{content:"\f0d9"}.fa-caret-right:before{content:"\f0da"}.fa-columns:before{content:"\f0db"}.fa-unsorted:before,.fa-sort:before{content:"\f0dc"}.fa-sort-down:before,.fa-sort-desc:before{content:"\f0dd"}.fa-sort-up:before,.fa-sort-asc:before{content:"\f0de"}.fa-envelope:before{content:"\f0e0"}.fa-linkedin:before{content:"\f0e1"}.fa-rotate-left:before,.fa-undo:before{content:"\f0e2"}.fa-legal:before,.fa-gavel:before{content:"\f0e3"}.fa-dashboard:before,.fa-tachometer:before{content:"\f0e4"}.fa-comment-o:before{content:"\f0e5"}.fa-comments-o:before{content:"\f0e6"}.fa-flash:before,.fa-bolt:before{content:"\f0e7"}.fa-sitemap:before{content:"\f0e8"}.fa-umbrella:before{content:"\f0e9"}.fa-paste:before,.fa-clipboard:before{content:"\f0ea"}.fa-lightbulb-o:before{content:"\f0eb"}.fa-exchange:before{content:"\f0ec"}.fa-cloud-download:before{content:"\f0ed"}.fa-cloud-upload:before{content:"\f0ee"}.fa-user-md:before{content:"\f0f0"}.fa-stethoscope:before{content:"\f0f1"}.fa-suitcase:before{content:"\f0f2"}.fa-bell-o:before{content:"\f0a2"}.fa-coffee:before{content:"\f0f4"}.fa-cutlery:before{content:"\f0f5"}.fa-file-text-o:before{content:"\f0f6"}.fa-building-o:before{content:"\f0f7"}.fa-hospital-o:before{content:"\f0f8"}.fa-ambulance:before{content:"\f0f9"}.fa-medkit:before{content:"\f0fa"}.fa-fighter-jet:before{content:"\f0fb"}.fa-beer:before{content:"\f0fc"}.fa-h-square:before{content:"\f0fd"}.fa-plus-square:before{content:"\f0fe"}.fa-angle-double-left:before{content:"\f100"}.fa-angle-double-right:before{content:"\f101"}.fa-angle-double-up:before{content:"\f102"}.fa-angle-double-down:before{content:"\f103"}.fa-angle-left:before{content:"\f104"}.fa-angle-right:before{content:"\f105"}.fa-angle-up:before{content:"\f106"}.fa-angle-down:before{content:"\f107"}.fa-desktop:before{content:"\f108"}.fa-laptop:before{content:"\f109"}.fa-tablet:before{content:"\f10a"}.fa-mobile-phone:before,.fa-mobile:before{content:"\f10b"}.fa-circle-o:before{content:"\f10c"}.fa-quote-left:before{content:"\f10d"}.fa-quote-right:before{content:"\f10e"}.fa-spinner:before{content:"\f110"}.fa-circle:before{content:"\f111"}.fa-mail-reply:before,.fa-reply:before{content:"\f112"}.fa-github-alt:before{content:"\f113"}.fa-folder-o:before{content:"\f114"}.fa-folder-open-o:before{content:"\f115"}.fa-smile-o:before{content:"\f118"}.fa-frown-o:before{content:"\f119"}.fa-meh-o:before{content:"\f11a"}.fa-gamepad:before{content:"\f11b"}.fa-keyboard-o:before{content:"\f11c"}.fa-flag-o:before{content:"\f11d"}.fa-flag-checkered:before{content:"\f11e"}.fa-terminal:before{content:"\f120"}.fa-code:before{content:"\f121"}.fa-mail-reply-all:before,.fa-reply-all:before{content:"\f122"}.fa-star-half-empty:before,.fa-star-half-full:before,.fa-star-half-o:before{content:"\f123"}.fa-location-arrow:before{content:"\f124"}.fa-crop:before{content:"\f125"}.fa-code-fork:before{content:"\f126"}.fa-unlink:before,.fa-chain-broken:before{content:"\f127"}.fa-question:before{content:"\f128"}.fa-info:before{content:"\f129"}.fa-exclamation:before{content:"\f12a"}.fa-superscript:before{content:"\f12b"}.fa-subscript:before{content:"\f12c"}.fa-eraser:before{content:"\f12d"}.fa-puzzle-piece:before{content:"\f12e"}.fa-microphone:before{content:"\f130"}.fa-microphone-slash:before{content:"\f131"}.fa-shield:before{content:"\f132"}.fa-calendar-o:before{content:"\f133"}.fa-fire-extinguisher:before{content:"\f134"}.fa-rocket:before{content:"\f135"}.fa-maxcdn:before{content:"\f136"}.fa-chevron-circle-left:before{content:"\f137"}.fa-chevron-circle-right:before{content:"\f138"}.fa-chevron-circle-up:before{content:"\f139"}.fa-chevron-circle-down:before{content:"\f13a"}.fa-html5:before{content:"\f13b"}.fa-css3:before{content:"\f13c"}.fa-anchor:before{content:"\f13d"}.fa-unlock-alt:before{content:"\f13e"}.fa-bullseye:before{content:"\f140"}.fa-ellipsis-h:before{content:"\f141"}.fa-ellipsis-v:before{content:"\f142"}.fa-rss-square:before{content:"\f143"}.fa-play-circle:before{content:"\f144"}.fa-ticket:before{content:"\f145"}.fa-minus-square:before{content:"\f146"}.fa-minus-square-o:before{content:"\f147"}.fa-level-up:before{content:"\f148"}.fa-level-down:before{content:"\f149"}.fa-check-square:before{content:"\f14a"}.fa-pencil-square:before{content:"\f14b"}.fa-external-link-square:before{content:"\f14c"}.fa-share-square:before{content:"\f14d"}.fa-compass:before{content:"\f14e"}.fa-toggle-down:before,.fa-caret-square-o-down:before{content:"\f150"}.fa-toggle-up:before,.fa-caret-square-o-up:before{content:"\f151"}.fa-toggle-right:before,.fa-caret-square-o-right:before{content:"\f152"}.fa-euro:before,.fa-eur:before{content:"\f153"}.fa-gbp:before{content:"\f154"}.fa-dollar:before,.fa-usd:before{content:"\f155"}.fa-rupee:before,.fa-inr:before{content:"\f156"}.fa-cny:before,.fa-rmb:before,.fa-yen:before,.fa-jpy:before{content:"\f157"}.fa-ruble:before,.fa-rouble:before,.fa-rub:before{content:"\f158"}.fa-won:before,.fa-krw:before{content:"\f159"}.fa-bitcoin:before,.fa-btc:before{content:"\f15a"}.fa-file:before{content:"\f15b"}.fa-file-text:before{content:"\f15c"}.fa-sort-alpha-asc:before{content:"\f15d"}.fa-sort-alpha-desc:before{content:"\f15e"}.fa-sort-amount-asc:before{content:"\f160"}.fa-sort-amount-desc:before{content:"\f161"}.fa-sort-numeric-asc:before{content:"\f162"}.fa-sort-numeric-desc:before{content:"\f163"}.fa-thumbs-up:before{content:"\f164"}.fa-thumbs-down:before{content:"\f165"}.fa-youtube-square:before{content:"\f166"}.fa-youtube:before{content:"\f167"}.fa-xing:before{content:"\f168"}.fa-xing-square:before{content:"\f169"}.fa-youtube-play:before{content:"\f16a"}.fa-dropbox:before{content:"\f16b"}.fa-stack-overflow:before{content:"\f16c"}.fa-instagram:before{content:"\f16d"}.fa-flickr:before{content:"\f16e"}.fa-adn:before{content:"\f170"}.fa-bitbucket:before{content:"\f171"}.fa-bitbucket-square:before{content:"\f172"}.fa-tumblr:before{content:"\f173"}.fa-tumblr-square:before{content:"\f174"}.fa-long-arrow-down:before{content:"\f175"}.fa-long-arrow-up:before{content:"\f176"}.fa-long-arrow-left:before{content:"\f177"}.fa-long-arrow-right:before{content:"\f178"}.fa-apple:before{content:"\f179"}.fa-windows:before{content:"\f17a"}.fa-android:before{content:"\f17b"}.fa-linux:before{content:"\f17c"}.fa-dribbble:before{content:"\f17d"}.fa-skype:before{content:"\f17e"}.fa-foursquare:before{content:"\f180"}.fa-trello:before{content:"\f181"}.fa-female:before{content:"\f182"}.fa-male:before{content:"\f183"}.fa-gittip:before,.fa-gratipay:before{content:"\f184"}.fa-sun-o:before{content:"\f185"}.fa-moon-o:before{content:"\f186"}.fa-archive:before{content:"\f187"}.fa-bug:before{content:"\f188"}.fa-vk:before{content:"\f189"}.fa-weibo:before{content:"\f18a"}.fa-renren:before{content:"\f18b"}.fa-pagelines:before{content:"\f18c"}.fa-stack-exchange:before{content:"\f18d"}.fa-arrow-circle-o-right:before{content:"\f18e"}.fa-arrow-circle-o-left:before{content:"\f190"}.fa-toggle-left:before,.fa-caret-square-o-left:before{content:"\f191"}.fa-dot-circle-o:before{content:"\f192"}.fa-wheelchair:before{content:"\f193"}.fa-vimeo-square:before{content:"\f194"}.fa-turkish-lira:before,.fa-try:before{content:"\f195"}.fa-plus-square-o:before{content:"\f196"}.fa-space-shuttle:before{content:"\f197"}.fa-slack:before{content:"\f198"}.fa-envelope-square:before{content:"\f199"}.fa-wordpress:before{content:"\f19a"}.fa-openid:before{content:"\f19b"}.fa-institution:before,.fa-bank:before,.fa-university:before{content:"\f19c"}.fa-mortar-board:before,.fa-graduation-cap:before{content:"\f19d"}.fa-yahoo:before{content:"\f19e"}.fa-google:before{content:"\f1a0"}.fa-reddit:before{content:"\f1a1"}.fa-reddit-square:before{content:"\f1a2"}.fa-stumbleupon-circle:before{content:"\f1a3"}.fa-stumbleupon:before{content:"\f1a4"}.fa-delicious:before{content:"\f1a5"}.fa-digg:before{content:"\f1a6"}.fa-pied-piper:before{content:"\f1a7"}.fa-pied-piper-alt:before{content:"\f1a8"}.fa-drupal:before{content:"\f1a9"}.fa-joomla:before{content:"\f1aa"}.fa-language:before{content:"\f1ab"}.fa-fax:before{content:"\f1ac"}.fa-building:before{content:"\f1ad"}.fa-child:before{content:"\f1ae"}.fa-paw:before{content:"\f1b0"}.fa-spoon:before{content:"\f1b1"}.fa-cube:before{content:"\f1b2"}.fa-cubes:before{content:"\f1b3"}.fa-behance:before{content:"\f1b4"}.fa-behance-square:before{content:"\f1b5"}.fa-steam:before{content:"\f1b6"}.fa-steam-square:before{content:"\f1b7"}.fa-recycle:before{content:"\f1b8"}.fa-automobile:before,.fa-car:before{content:"\f1b9"}.fa-cab:before,.fa-taxi:before{content:"\f1ba"}.fa-tree:before{content:"\f1bb"}.fa-spotify:before{content:"\f1bc"}.fa-deviantart:before{content:"\f1bd"}.fa-soundcloud:before{content:"\f1be"}.fa-database:before{content:"\f1c0"}.fa-file-pdf-o:before{content:"\f1c1"}.fa-file-word-o:before{content:"\f1c2"}.fa-file-excel-o:before{content:"\f1c3"}.fa-file-powerpoint-o:before{content:"\f1c4"}.fa-file-photo-o:before,.fa-file-picture-o:before,.fa-file-image-o:before{content:"\f1c5"}.fa-file-zip-o:before,.fa-file-archive-o:before{content:"\f1c6"}.fa-file-sound-o:before,.fa-file-audio-o:before{content:"\f1c7"}.fa-file-movie-o:before,.fa-file-video-o:before{content:"\f1c8"}.fa-file-code-o:before{content:"\f1c9"}.fa-vine:before{content:"\f1ca"}.fa-codepen:before{content:"\f1cb"}.fa-jsfiddle:before{content:"\f1cc"}.fa-life-bouy:before,.fa-life-buoy:before,.fa-life-saver:before,.fa-support:before,.fa-life-ring:before{content:"\f1cd"}.fa-circle-o-notch:before{content:"\f1ce"}.fa-ra:before,.fa-rebel:before{content:"\f1d0"}.fa-ge:before,.fa-empire:before{content:"\f1d1"}.fa-git-square:before{content:"\f1d2"}.fa-git:before{content:"\f1d3"}.fa-y-combinator-square:before,.fa-yc-square:before,.fa-hacker-news:before{content:"\f1d4"}.fa-tencent-weibo:before{content:"\f1d5"}.fa-qq:before{content:"\f1d6"}.fa-wechat:before,.fa-weixin:before{content:"\f1d7"}.fa-send:before,.fa-paper-plane:before{content:"\f1d8"}.fa-send-o:before,.fa-paper-plane-o:before{content:"\f1d9"}.fa-history:before{content:"\f1da"}.fa-circle-thin:before{content:"\f1db"}.fa-header:before{content:"\f1dc"}.fa-paragraph:before{content:"\f1dd"}.fa-sliders:before{content:"\f1de"}.fa-share-alt:before{content:"\f1e0"}.fa-share-alt-square:before{content:"\f1e1"}.fa-bomb:before{content:"\f1e2"}.fa-soccer-ball-o:before,.fa-futbol-o:before{content:"\f1e3"}.fa-tty:before{content:"\f1e4"}.fa-binoculars:before{content:"\f1e5"}.fa-plug:before{content:"\f1e6"}.fa-slideshare:before{content:"\f1e7"}.fa-twitch:before{content:"\f1e8"}.fa-yelp:before{content:"\f1e9"}.fa-newspaper-o:before{content:"\f1ea"}.fa-wifi:before{content:"\f1eb"}.fa-calculator:before{content:"\f1ec"}.fa-paypal:before{content:"\f1ed"}.fa-google-wallet:before{content:"\f1ee"}.fa-cc-visa:before{content:"\f1f0"}.fa-cc-mastercard:before{content:"\f1f1"}.fa-cc-discover:before{content:"\f1f2"}.fa-cc-amex:before{content:"\f1f3"}.fa-cc-paypal:before{content:"\f1f4"}.fa-cc-stripe:before{content:"\f1f5"}.fa-bell-slash:before{content:"\f1f6"}.fa-bell-slash-o:before{content:"\f1f7"}.fa-trash:before{content:"\f1f8"}.fa-copyright:before{content:"\f1f9"}.fa-at:before{content:"\f1fa"}.fa-eyedropper:before{content:"\f1fb"}.fa-paint-brush:before{content:"\f1fc"}.fa-birthday-cake:before{content:"\f1fd"}.fa-area-chart:before{content:"\f1fe"}.fa-pie-chart:before{content:"\f200"}.fa-line-chart:before{content:"\f201"}.fa-lastfm:before{content:"\f202"}.fa-lastfm-square:before{content:"\f203"}.fa-toggle-off:before{content:"\f204"}.fa-toggle-on:before{content:"\f205"}.fa-bicycle:before{content:"\f206"}.fa-bus:before{content:"\f207"}.fa-ioxhost:before{content:"\f208"}.fa-angellist:before{content:"\f209"}.fa-cc:before{content:"\f20a"}.fa-shekel:before,.fa-sheqel:before,.fa-ils:before{content:"\f20b"}.fa-meanpath:before{content:"\f20c"}.fa-buysellads:before{content:"\f20d"}.fa-connectdevelop:before{content:"\f20e"}.fa-dashcube:before{content:"\f210"}.fa-forumbee:before{content:"\f211"}.fa-leanpub:before{content:"\f212"}.fa-sellsy:before{content:"\f213"}.fa-shirtsinbulk:before{content:"\f214"}.fa-simplybuilt:before{content:"\f215"}.fa-skyatlas:before{content:"\f216"}.fa-cart-plus:before{content:"\f217"}.fa-cart-arrow-down:before{content:"\f218"}.fa-diamond:before{content:"\f219"}.fa-ship:before{content:"\f21a"}.fa-user-secret:before{content:"\f21b"}.fa-motorcycle:before{content:"\f21c"}.fa-street-view:before{content:"\f21d"}.fa-heartbeat:before{content:"\f21e"}.fa-venus:before{content:"\f221"}.fa-mars:before{content:"\f222"}.fa-mercury:before{content:"\f223"}.fa-intersex:before,.fa-transgender:before{content:"\f224"}.fa-transgender-alt:before{content:"\f225"}.fa-venus-double:before{content:"\f226"}.fa-mars-double:before{content:"\f227"}.fa-venus-mars:before{content:"\f228"}.fa-mars-stroke:before{content:"\f229"}.fa-mars-stroke-v:before{content:"\f22a"}.fa-mars-stroke-h:before{content:"\f22b"}.fa-neuter:before{content:"\f22c"}.fa-genderless:before{content:"\f22d"}.fa-facebook-official:before{content:"\f230"}.fa-pinterest-p:before{content:"\f231"}.fa-whatsapp:before{content:"\f232"}.fa-server:before{content:"\f233"}.fa-user-plus:before{content:"\f234"}.fa-user-times:before{content:"\f235"}.fa-hotel:before,.fa-bed:before{content:"\f236"}.fa-viacoin:before{content:"\f237"}.fa-train:before{content:"\f238"}.fa-subway:before{content:"\f239"}.fa-medium:before{content:"\f23a"}.fa-yc:before,.fa-y-combinator:before{content:"\f23b"}.fa-optin-monster:before{content:"\f23c"}.fa-opencart:before{content:"\f23d"}.fa-expeditedssl:before{content:"\f23e"}.fa-battery-4:before,.fa-battery-full:before{content:"\f240"}.fa-battery-3:before,.fa-battery-three-quarters:before{content:"\f241"}.fa-battery-2:before,.fa-battery-half:before{content:"\f242"}.fa-battery-1:before,.fa-battery-quarter:before{content:"\f243"}.fa-battery-0:before,.fa-battery-empty:before{content:"\f244"}.fa-mouse-pointer:before{content:"\f245"}.fa-i-cursor:before{content:"\f246"}.fa-object-group:before{content:"\f247"}.fa-object-ungroup:before{content:"\f248"}.fa-sticky-note:before{content:"\f249"}.fa-sticky-note-o:before{content:"\f24a"}.fa-cc-jcb:before{content:"\f24b"}.fa-cc-diners-club:before{content:"\f24c"}.fa-clone:before{content:"\f24d"}.fa-balance-scale:before{content:"\f24e"}.fa-hourglass-o:before{content:"\f250"}.fa-hourglass-1:before,.fa-hourglass-start:before{content:"\f251"}.fa-hourglass-2:before,.fa-hourglass-half:before{content:"\f252"}.fa-hourglass-3:before,.fa-hourglass-end:before{content:"\f253"}.fa-hourglass:before{content:"\f254"}.fa-hand-grab-o:before,.fa-hand-rock-o:before{content:"\f255"}.fa-hand-stop-o:before,.fa-hand-paper-o:before{content:"\f256"}.fa-hand-scissors-o:before{content:"\f257"}.fa-hand-lizard-o:before{content:"\f258"}.fa-hand-spock-o:before{content:"\f259"}.fa-hand-pointer-o:before{content:"\f25a"}.fa-hand-peace-o:before{content:"\f25b"}.fa-trademark:before{content:"\f25c"}.fa-registered:before{content:"\f25d"}.fa-creative-commons:before{content:"\f25e"}.fa-gg:before{content:"\f260"}.fa-gg-circle:before{content:"\f261"}.fa-tripadvisor:before{content:"\f262"}.fa-odnoklassniki:before{content:"\f263"}.fa-odnoklassniki-square:before{content:"\f264"}.fa-get-pocket:before{content:"\f265"}.fa-wikipedia-w:before{content:"\f266"}.fa-safari:before{content:"\f267"}.fa-chrome:before{content:"\f268"}.fa-firefox:before{content:"\f269"}.fa-opera:before{content:"\f26a"}.fa-internet-explorer:before{content:"\f26b"}.fa-tv:before,.fa-television:before{content:"\f26c"}.fa-contao:before{content:"\f26d"}.fa-500px:before{content:"\f26e"}.fa-amazon:before{content:"\f270"}.fa-calendar-plus-o:before{content:"\f271"}.fa-calendar-minus-o:before{content:"\f272"}.fa-calendar-times-o:before{content:"\f273"}.fa-calendar-check-o:before{content:"\f274"}.fa-industry:before{content:"\f275"}.fa-map-pin:before{content:"\f276"}.fa-map-signs:before{content:"\f277"}.fa-map-o:before{content:"\f278"}.fa-map:before{content:"\f279"}.fa-commenting:before{content:"\f27a"}.fa-commenting-o:before{content:"\f27b"}.fa-houzz:before{content:"\f27c"}.fa-vimeo:before{content:"\f27d"}.fa-black-tie:before{content:"\f27e"}.fa-fonticons:before{content:"\f280"}



/**	16. Et-Line	& Font Icons
*************************************************** **/
/* Et-Line */
@font-face {
	font-family: 'et-line';
	src:url('/templates/assets/fonts/home/<USER>');
	src:url('/templates/assets/fonts/home/<USER>') format('embedded-opentype'),
	url('/templates/assets/fonts/home/<USER>') format('woff'),
	url('/templates/assets/fonts/home/<USER>') format('truetype'),
	url('/templates/assets/fonts/home/<USER>') format('svg');
	font-weight: normal;
	font-style: normal;
}
[data-icon]:before{font-family:et-line;content:attr(data-icon);speak:none;font-weight:400;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;display:inline-block}.et-adjustments,.et-alarmclock,.et-anchor,.et-aperture,.et-attachment,.et-bargraph,.et-basket,.et-beaker,.et-bike,.et-book-open,.et-briefcase,.et-browser,.et-calendar,.et-camera,.et-caution,.et-chat,.et-circle-compass,.et-clipboard,.et-clock,.et-cloud,.et-compass,.et-desktop,.et-dial,.et-document,.et-documents,.et-download,.et-dribbble,.et-edit,.et-envelope,.et-expand,.et-facebook,.et-flag,.et-focus,.et-gears,.et-genius,.et-gift,.et-global,.et-globe,.et-googleplus,.et-grid,.et-happy,.et-hazardous,.et-heart,.et-hotairballoon,.et-hourglass,.et-key,.et-laptop,.et-layers,.et-lifesaver,.et-lightbulb,.et-linegraph,.et-linkedin,.et-lock,.et-magnifying-glass,.et-map,.et-map-pin,.et-megaphone,.et-mic,.et-mobile,.et-newspaper,.et-notebook,.et-paintbrush,.et-paperclip,.et-pencil,.et-phone,.et-picture,.et-pictures,.et-piechart,.et-presentation,.et-pricetags,.et-printer,.et-profile-female,.et-profile-male,.et-puzzle,.et-quote,.et-recycle,.et-refresh,.et-ribbon,.et-rss,.et-sad,.et-scissors,.et-scope,.et-search,.et-shield,.et-speedometer,.et-strategy,.et-streetsign,.et-tablet,.et-target,.et-telescope,.et-toolbox,.et-tools,.et-tools-2,.et-trophy,.et-tumblr,.et-twitter,.et-upload,.et-video,.et-wallet,.et-wine{font-family:et-line;speak:none;font-style:normal;font-weight:400;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;display:inline-block}.et-mobile:before{content:"\e000"}.et-laptop:before{content:"\e001"}.et-desktop:before{content:"\e002"}.et-tablet:before{content:"\e003"}.et-phone:before{content:"\e004"}.et-document:before{content:"\e005"}.et-documents:before{content:"\e006"}.et-search:before{content:"\e007"}.et-clipboard:before{content:"\e008"}.et-newspaper:before{content:"\e009"}.et-notebook:before{content:"\e00a"}.et-book-open:before{content:"\e00b"}.et-browser:before{content:"\e00c"}.et-calendar:before{content:"\e00d"}.et-presentation:before{content:"\e00e"}.et-picture:before{content:"\e00f"}.et-pictures:before{content:"\e010"}.et-video:before{content:"\e011"}.et-camera:before{content:"\e012"}.et-printer:before{content:"\e013"}.et-toolbox:before{content:"\e014"}.et-briefcase:before{content:"\e015"}.et-wallet:before{content:"\e016"}.et-gift:before{content:"\e017"}.et-bargraph:before{content:"\e018"}.et-grid:before{content:"\e019"}.et-expand:before{content:"\e01a"}.et-focus:before{content:"\e01b"}.et-edit:before{content:"\e01c"}.et-adjustments:before{content:"\e01d"}.et-ribbon:before{content:"\e01e"}.et-hourglass:before{content:"\e01f"}.et-lock:before{content:"\e020"}.et-megaphone:before{content:"\e021"}.et-shield:before{content:"\e022"}.et-trophy:before{content:"\e023"}.et-flag:before{content:"\e024"}.et-map:before{content:"\e025"}.et-puzzle:before{content:"\e026"}.et-basket:before{content:"\e027"}.et-envelope:before{content:"\e028"}.et-streetsign:before{content:"\e029"}.et-telescope:before{content:"\e02a"}.et-gears:before{content:"\e02b"}.et-key:before{content:"\e02c"}.et-paperclip:before{content:"\e02d"}.et-attachment:before{content:"\e02e"}.et-pricetags:before{content:"\e02f"}.et-lightbulb:before{content:"\e030"}.et-layers:before{content:"\e031"}.et-pencil:before{content:"\e032"}.et-tools:before{content:"\e033"}.et-tools-2:before{content:"\e034"}.et-scissors:before{content:"\e035"}.et-paintbrush:before{content:"\e036"}.et-magnifying-glass:before{content:"\e037"}.et-circle-compass:before{content:"\e038"}.et-linegraph:before{content:"\e039"}.et-mic:before{content:"\e03a"}.et-strategy:before{content:"\e03b"}.et-beaker:before{content:"\e03c"}.et-caution:before{content:"\e03d"}.et-recycle:before{content:"\e03e"}.et-anchor:before{content:"\e03f"}.et-profile-male:before{content:"\e040"}.et-profile-female:before{content:"\e041"}.et-bike:before{content:"\e042"}.et-wine:before{content:"\e043"}.et-hotairballoon:before{content:"\e044"}.et-globe:before{content:"\e045"}.et-genius:before{content:"\e046"}.et-map-pin:before{content:"\e047"}.et-dial:before{content:"\e048"}.et-chat:before{content:"\e049"}.et-heart:before{content:"\e04a"}.et-cloud:before{content:"\e04b"}.et-upload:before{content:"\e04c"}.et-download:before{content:"\e04d"}.et-target:before{content:"\e04e"}.et-hazardous:before{content:"\e04f"}.et-piechart:before{content:"\e050"}.et-speedometer:before{content:"\e051"}.et-global:before{content:"\e052"}.et-compass:before{content:"\e053"}.et-lifesaver:before{content:"\e054"}.et-clock:before{content:"\e055"}.et-aperture:before{content:"\e056"}.et-quote:before{content:"\e057"}.et-scope:before{content:"\e058"}.et-alarmclock:before{content:"\e059"}.et-refresh:before{content:"\e05a"}.et-happy:before{content:"\e05b"}.et-sad:before{content:"\e05c"}.et-facebook:before{content:"\e05d"}.et-twitter:before{content:"\e05e"}.et-googleplus:before{content:"\e05f"}.et-rss:before{content:"\e060"}.et-tumblr:before{content:"\e061"}.et-linkedin:before{content:"\e062"}.et-dribbble:before{content:"\e063"}

/* Font Icons */
@font-face {
	font-family: 'font-icons';
	src:url('/templates/assets/fonts/home/<USER>');
	src:url('/templates/assets/fonts/home/<USER>') format('embedded-opentype'),
	url('/templates/assets/fonts/home/<USER>') format('woff'),
	url('/templates/assets/fonts/home/<USER>') format('truetype'),
	url('/templates/assets/fonts/home/<USER>') format('svg');
	font-weight: normal;
	font-style: normal;
}
.side-header #primary-menu ul>li.sub-menu>a:after,[class*=" icon-"],[class^=icon-]{display:inline-block;font-family:font-icons;speak:none;font-style:normal;font-weight:400;font-variant:normal;text-transform:none;line-height:inherit;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;} .icon-lg{font-size:1.3333333333333333em;line-height:.75em;vertical-align:-15%}.icon-2x{font-size:2em}.icon-3x{font-size:3em}.icon-4x{font-size:4em}.icon-5x{font-size:5em}.icon-fw{width:1.2857142857142858em;text-align:center}.iconlist{padding-left:0;margin-left:1.75em;list-style-type:none}.iconlist li{position:relative}.iconlist ul{list-style-type:none;margin:5px 0 5px 25px}.iconlist>li [class*=" icon-"],.iconlist>li [class^=icon-]{position:absolute;left:-1.75em;text-align:center;top:1px;width:14px}.iconlist.iconlist-large{font-size:16px}.iconlist.iconlist-large li{margin:4px 0}.iconlist.iconlist-large>li [class*=" icon-"],.iconlist.iconlist-large>li [class^=icon-]{width:16px;margin-right:5px}.iconlist-color li i{color:#1ABC9C}.icon-border{padding:.2em .25em .15em;border:solid .08em #eee;border-radius:.1em}.icon.pull-left{margin-right:.3em}.icon.pull-right{margin-left:.3em}.icon-spin{-webkit-animation:spin 2s infinite linear;-moz-animation:spin 2s infinite linear;-o-animation:spin 2s infinite linear;animation:spin 2s infinite linear}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg)}100%{-moz-transform:rotate(359deg)}}@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg)}100%{-o-transform:rotate(359deg)}}@-ms-keyframes spin{0%{-ms-transform:rotate(0deg)}100%{-ms-transform:rotate(359deg)}}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(359deg)}}.icon-rotate-90{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=1);-webkit-transform:rotate(90deg);-moz-transform:rotate(90deg);-ms-transform:rotate(90deg);-o-transform:rotate(90deg);transform:rotate(90deg)}.icon-rotate-180{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2);-webkit-transform:rotate(180deg);-moz-transform:rotate(180deg);-ms-transform:rotate(180deg);-o-transform:rotate(180deg);transform:rotate(180deg)}.icon-rotate-270{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);-webkit-transform:rotate(270deg);-moz-transform:rotate(270deg);-ms-transform:rotate(270deg);-o-transform:rotate(270deg);transform:rotate(270deg)}.icon-flip-horizontal{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);-webkit-transform:scale(-1,1);-moz-transform:scale(-1,1);-ms-transform:scale(-1,1);-o-transform:scale(-1,1);transform:scale(-1,1)}.icon-flip-vertical{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);-webkit-transform:scale(1,-1);-moz-transform:scale(1,-1);-ms-transform:scale(1,-1);-o-transform:scale(1,-1);transform:scale(1,-1)}.icon-stacked{position:relative;display:inline-block;width:2em;height:2em;line-height:2em;vertical-align:middle}.icon-stacked-1x,.icon-stacked-2x{position:absolute;left:0;width:100%;text-align:center}.icon-stacked-1x{line-height:inherit}.icon-stacked-2x{font-size:2em}.icon-inverse{color:#fff}.icon-type:before{content:"\e600"}.icon-box:before{content:"\e601"}.icon-archive:before{content:"\e602"}.icon-envelope:before{content:"\e603"}.icon-email:before{content:"\e604"}.icon-files:before{content:"\e605"}.icon-printer2:before{content:"\e606"}.icon-folder-add:before{content:"\e607"}.icon-folder-settings:before{content:"\e608"}.icon-folder-check:before{content:"\e609"}.icon-wifi-low:before{content:"\e60a"}.icon-wifi-mid:before{content:"\e60b"}.icon-wifi-full:before{content:"\e60c"}.icon-connection-empty:before{content:"\e60d"}.icon-battery-full:before{content:"\e60e"}.icon-settings:before{content:"\e60f"}.icon-arrow-left:before{content:"\e610"}.icon-arrow-up:before{content:"\e611"}.icon-arrow-down:before{content:"\e612"}.icon-arrow-right:before{content:"\e613"}.icon-reload:before{content:"\e614"}.icon-download:before{content:"\e615"}.icon-tag:before{content:"\e616"}.icon-trashcan:before{content:"\e617"}.icon-search:before{content:"\e618"}.icon-zoom-in:before{content:"\e619"}.icon-zoom-out:before{content:"\e61a"}.icon-chat:before{content:"\e61b"}.icon-clock:before{content:"\e61c"}.icon-printer:before{content:"\e61d"}.icon-home:before{content:"\e61e"}.icon-flag:before{content:"\e61f"}.icon-meter:before{content:"\e620"}.icon-switch:before{content:"\e621"}.icon-forbidden:before{content:"\e622"}.icon-phone-landscape:before{content:"\e623"}.icon-tablet:before{content:"\e624"}.icon-tablet-landscape:before{content:"\e625"}.icon-laptop:before{content:"\e626"}.icon-camera:before{content:"\e627"}.icon-microwave-oven:before{content:"\e628"}.icon-credit-cards:before{content:"\e629"}.icon-map-marker:before{content:"\e62a"}.icon-map:before{content:"\e62b"}.icon-support:before{content:"\e62c"}.icon-newspaper2:before{content:"\e62d"}.icon-barbell:before{content:"\e62e"}.icon-stopwatch:before{content:"\e62f"}.icon-atom:before{content:"\e630"}.icon-image:before{content:"\e631"}.icon-cube:before{content:"\e632"}.icon-bars:before{content:"\e633"}.icon-chart:before{content:"\e634"}.icon-pencil:before{content:"\e635"}.icon-measure:before{content:"\e636"}.icon-eyedropper:before{content:"\e637"}.icon-file-settings:before{content:"\e638"}.icon-file-add:before{content:"\e639"}.icon-file:before{content:"\e63a"}.icon-align-left:before{content:"\e63b"}.icon-align-right:before{content:"\e63c"}.icon-align-center:before{content:"\e63d"}.icon-align-justify:before{content:"\e63e"}.icon-file-broken:before{content:"\e63f"}.icon-browser:before{content:"\e640"}.icon-windows:before{content:"\e641"}.icon-window:before{content:"\e642"}.icon-folder:before{content:"\e643"}.icon-connection-25:before{content:"\e644"}.icon-connection-50:before{content:"\e645"}.icon-connection-75:before{content:"\e646"}.icon-connection-full:before{content:"\e647"}.icon-list:before{content:"\e648"}.icon-grid:before{content:"\e649"}.icon-stack3:before{content:"\e64a"}.icon-battery-charging:before{content:"\e64b"}.icon-battery-empty:before{content:"\e64c"}.icon-battery-25:before{content:"\e64d"}.icon-battery-50:before{content:"\e64e"}.icon-battery-75:before{content:"\e64f"}.icon-refresh:before{content:"\e650"}.icon-volume:before{content:"\e651"}.icon-volume-increase:before{content:"\e652"}.icon-volume-decrease:before{content:"\e653"}.icon-mute:before{content:"\e654"}.icon-microphone:before{content:"\e655"}.icon-microphone-off:before{content:"\e656"}.icon-book:before{content:"\e657"}.icon-checkmark:before{content:"\e658"}.icon-checkbox-checked:before{content:"\e659"}.icon-checkbox:before{content:"\e65a"}.icon-paperclip:before{content:"\e65b"}.icon-chat-1:before{content:"\e65c"}.icon-chat-2:before{content:"\e65d"}.icon-chat-3:before{content:"\e65e"}.icon-comment:before{content:"\e65f"}.icon-calendar:before{content:"\e660"}.icon-bookmark:before{content:"\e661"}.icon-email2:before{content:"\e662"}.icon-heart:before{content:"\e663"}.icon-enter:before{content:"\e664"}.icon-cloud:before{content:"\e665"}.icon-book2:before{content:"\e666"}.icon-star:before{content:"\e667"}.icon-lock:before{content:"\e668"}.icon-unlocked:before{content:"\e669"}.icon-unlocked2:before{content:"\e66a"}.icon-users:before{content:"\e66b"}.icon-user:before{content:"\e66c"}.icon-users2:before{content:"\e66d"}.icon-user2:before{content:"\e66e"}.icon-bullhorn:before{content:"\e66f"}.icon-share:before{content:"\e670"}.icon-screen:before{content:"\e671"}.icon-phone:before{content:"\e672"}.icon-phone-portrait:before{content:"\e673"}.icon-calculator:before{content:"\e674"}.icon-bag:before{content:"\e675"}.icon-diamond:before{content:"\e676"}.icon-drink:before{content:"\e677"}.icon-shorts:before{content:"\e678"}.icon-vcard:before{content:"\e679"}.icon-sun:before{content:"\e67a"}.icon-bill:before{content:"\e67b"}.icon-coffee:before{content:"\e67c"}.icon-tv2:before{content:"\e67d"}.icon-newspaper:before{content:"\e67e"}.icon-stack:before{content:"\e67f"}.icon-syringe:before{content:"\e680"}.icon-health:before{content:"\e681"}.icon-bolt:before{content:"\e682"}.icon-pill:before{content:"\e683"}.icon-bones:before{content:"\e684"}.icon-lab:before{content:"\e685"}.icon-clipboard:before{content:"\e686"}.icon-mug:before{content:"\e687"}.icon-bucket:before{content:"\e688"}.icon-select:before{content:"\e689"}.icon-graph:before{content:"\e68a"}.icon-crop:before{content:"\e68b"}.icon-heart2:before{content:"\e68c"}.icon-cloud2:before{content:"\e68d"}.icon-star2:before{content:"\e68e"}.icon-pen:before{content:"\e68f"}.icon-diamond2:before{content:"\e690"}.icon-display:before{content:"\e691"}.icon-paperplane:before{content:"\e692"}.icon-params:before{content:"\e693"}.icon-banknote:before{content:"\e694"}.icon-vynil:before{content:"\e695"}.icon-truck:before{content:"\e696"}.icon-world:before{content:"\e697"}.icon-tv:before{content:"\e698"}.icon-sound:before{content:"\e699"}.icon-video:before{content:"\e69a"}.icon-trash:before{content:"\e69b"}.icon-user3:before{content:"\e69c"}.icon-key:before{content:"\e69d"}.icon-search2:before{content:"\e69e"}.icon-settings2:before{content:"\e69f"}.icon-camera2:before{content:"\e6a0"}.icon-tag2:before{content:"\e6a1"}.icon-lock2:before{content:"\e6a2"}.icon-bulb:before{content:"\e6a3"}.icon-location:before{content:"\e6a4"}.icon-eye:before{content:"\e6a5"}.icon-bubble:before{content:"\e6a6"}.icon-stack2:before{content:"\e6a7"}.icon-cup:before{content:"\e6a8"}.icon-phone2:before{content:"\e6a9"}.icon-news:before{content:"\e6aa"}.icon-mail:before{content:"\e6ab"}.icon-like:before{content:"\e6ac"}.icon-photo:before{content:"\e6ad"}.icon-note:before{content:"\e6ae"}.icon-clock2:before{content:"\e6af"}.icon-data:before{content:"\e6b0"}.icon-music:before{content:"\e6b1"}.icon-megaphone:before{content:"\e6b2"}.icon-study:before{content:"\e6b3"}.icon-lab2:before{content:"\e6b4"}.icon-food:before{content:"\e6b5"}.icon-t-shirt:before{content:"\e6b6"}.icon-fire:before{content:"\e6b7"}.icon-clip:before{content:"\e6b8"}.icon-shop:before{content:"\e6b9"}.icon-calendar2:before{content:"\e6ba"}.icon-wallet:before{content:"\e6bb"}.icon-duckduckgo:before{content:"\e830"}.icon-lkdto:before{content:"\e896"}.icon-delicious:before{content:"\e832"}.icon-paypal:before{content:"\e833"}.icon-flattr:before{content:"\e834"}.icon-android:before{content:"\e835"}.icon-eventful:before{content:"\e836"}.icon-smashmag:before{content:"\e837"}.icon-gplus:before{content:"\e838"}.icon-wikipedia:before{content:"\e839"}.icon-lanyrd:before{content:"\e83a"}.icon-calendar-1:before{content:"\e83b"}.icon-stumbleupon:before{content:"\e83c"}.icon-bitcoin:before{content:"\e83f"}.icon-w3c:before{content:"\e840"}.icon-foursquare:before{content:"\e841"}.icon-html5:before{content:"\e842"}.icon-ie:before{content:"\e843"}.icon-call:before{content:"\e844"}.icon-grooveshark:before{content:"\e845"}.icon-ninetyninedesigns:before{content:"\e846"}.icon-forrst:before{content:"\e847"}.icon-digg:before{content:"\e848"}.icon-spotify:before{content:"\e849"}.icon-reddit:before{content:"\e84a"}.icon-guest:before{content:"\e84b"}.icon-blogger:before{content:"\e84e"}.icon-cc:before{content:"\e84f"}.icon-dribbble:before{content:"\e850"}.icon-evernote:before{content:"\e851"}.icon-flickr:before{content:"\e852"}.icon-google:before{content:"\e853"}.icon-viadeo:before{content:"\e854"}.icon-instapaper:before{content:"\e855"}.icon-weibo:before{content:"\e856"}.icon-klout:before{content:"\e857"}.icon-linkedin:before{content:"\e858"}.icon-meetup:before{content:"\e859"}.icon-vk:before{content:"\e85a"}.icon-rss:before{content:"\e85d"}.icon-skype:before{content:"\e85e"}.icon-twitter:before{content:"\e85f"}.icon-youtube:before{content:"\e860"}.icon-vimeo:before{content:"\e861"}.icon-windows2:before{content:"\e862"}.icon-aim:before{content:"\e831"}.icon-yahoo:before{content:"\e864"}.icon-chrome:before{content:"\e865"}.icon-email3:before{content:"\e866"}.icon-macstore:before{content:"\e867"}.icon-myspace:before{content:"\e868"}.icon-podcast:before{content:"\e869"}.icon-cloudapp:before{content:"\e86c"}.icon-dropbox:before{content:"\e86d"}.icon-ebay:before{content:"\e86e"}.icon-facebook:before{content:"\e86f"}.icon-github:before{content:"\e870"}.icon-github-circled:before{content:"\e871"}.icon-googleplay:before{content:"\e872"}.icon-itunes:before{content:"\e873"}.icon-plurk:before{content:"\e874"}.icon-songkick:before{content:"\e875"}.icon-lastfm:before{content:"\e876"}.icon-gmail:before{content:"\e877"}.icon-pinboard:before{content:"\e878"}.icon-soundcloud:before{content:"\e87b"}.icon-tumblr:before{content:"\e87c"}.icon-eventasaurus:before{content:"\e87d"}.icon-wordpress:before{content:"\e87e"}.icon-yelp:before{content:"\e87f"}.icon-intensedebate:before{content:"\e880"}.icon-eventbrite:before{content:"\e881"}.icon-scribd:before{content:"\e882"}.icon-posterous:before{content:"\e883"}.icon-stripe:before{content:"\e884"}.icon-opentable:before{content:"\e885"}.icon-cart:before{content:"\e886"}.icon-print:before{content:"\e887"}.icon-dwolla:before{content:"\e88a"}.icon-appnet:before{content:"\e88b"}.icon-statusnet:before{content:"\e88c"}.icon-acrobat:before{content:"\e88d"}.icon-drupal:before{content:"\e88e"}.icon-buffer:before{content:"\e88f"}.icon-pocket:before{content:"\e890"}.icon-bitbucket:before{content:"\e891"}.icon-lego:before{content:"\e892"}.icon-login:before{content:"\e893"}.icon-stackoverflow:before{content:"\e894"}.icon-hackernews:before{content:"\e895"}.icon-xing:before{content:"\e863"}.icon-instagram:before{content:"\e889"}.icon-angellist:before{content:"\e888"}.icon-quora:before{content:"\e87a"}.icon-openid:before{content:"\e879"}.icon-steam:before{content:"\e86b"}.icon-amazon:before{content:"\e86a"}.icon-disqus:before{content:"\e85c"}.icon-plancast:before{content:"\e85b"}.icon-appstore:before{content:"\e84d"}.icon-gowalla:before{content:"\e84c"}.icon-pinterest:before{content:"\e83e"}.icon-fivehundredpx:before{content:"\e83d"}.icon-glass:before{content:"\e6bc"}.icon-music2:before{content:"\e6bd"}.icon-search3:before{content:"\e6be"}.icon-envelope2:before{content:"\e6bf"}.icon-heart3:before{content:"\e6c0"}.icon-star3:before{content:"\e6c1"}.icon-star-empty:before{content:"\e6c2"}.icon-user4:before{content:"\e6c3"}.icon-film:before{content:"\e6c4"}.icon-th-large:before{content:"\e6c5"}.icon-th:before{content:"\e6c6"}.icon-th-list:before{content:"\e6c7"}.icon-ok:before{content:"\e6c8"}.icon-remove:before{content:"\e6c9"}.icon-zoom-in2:before{content:"\e6ca"}.icon-zoom-out2:before{content:"\e6cb"}.icon-off:before{content:"\e6cc"}.icon-signal:before{content:"\e6cd"}.icon-cog:before{content:"\e6ce"}.icon-trash2:before{content:"\e6cf"}.icon-home2:before{content:"\e6d0"}.icon-file2:before{content:"\e6d1"}.icon-time:before{content:"\e6d2"}.icon-road:before{content:"\e6d3"}.icon-download-alt:before{content:"\e6d4"}.icon-download2:before{content:"\e6d5"}.icon-upload:before{content:"\e6d6"}.icon-inbox:before{content:"\e6d7"}.icon-play-circle:before{content:"\e6d8"}.icon-repeat:before{content:"\e6d9"}.icon-refresh2:before{content:"\e6da"}.icon-list-alt:before{content:"\e6db"}.icon-lock3:before{content:"\e6dc"}.icon-flag2:before{content:"\e6dd"}.icon-headphones:before{content:"\e6de"}.icon-volume-off:before{content:"\e6df"}.icon-volume-down:before{content:"\e6e0"}.icon-volume-up:before{content:"\e6e1"}.icon-qrcode:before{content:"\e6e2"}.icon-barcode:before{content:"\e6e3"}.icon-tag3:before{content:"\e6e4"}.icon-tags:before{content:"\e6e5"}.icon-book3:before{content:"\e6e6"}.icon-bookmark2:before{content:"\e6e7"}.icon-print2:before{content:"\e6e8"}.icon-camera3:before{content:"\e6e9"}.icon-font:before{content:"\e6ea"}.icon-bold:before{content:"\e6eb"}.icon-italic:before{content:"\e6ec"}.icon-text-height:before{content:"\e6ed"}.icon-text-width:before{content:"\e6ee"}.icon-align-left2:before{content:"\e6ef"}.icon-align-center2:before{content:"\e6f0"}.icon-align-right2:before{content:"\e6f1"}.icon-align-justify2:before{content:"\e6f2"}.icon-list2:before{content:"\e6f3"}.icon-indent-left:before{content:"\e6f4"}.icon-indent-right:before{content:"\e6f5"}.icon-facetime-video:before{content:"\e6f6"}.icon-picture:before{content:"\e6f7"}.icon-pencil2:before{content:"\e6f8"}.icon-map-marker2:before{content:"\e6f9"}.icon-adjust:before{content:"\e6fa"}.icon-tint:before{content:"\e6fb"}.icon-edit:before{content:"\e6fc"}.icon-share2:before{content:"\e6fd"}.icon-check:before{content:"\e6fe"}.icon-move:before{content:"\e6ff"}.icon-step-backward:before{content:"\e700"}.icon-fast-backward:before{content:"\e701"}.icon-backward:before{content:"\e702"}.icon-play:before{content:"\e703"}.icon-pause:before{content:"\e704"}.icon-stop:before{content:"\e705"}.icon-forward:before{content:"\e706"}.icon-fast-forward:before{content:"\e707"}.icon-step-forward:before{content:"\e708"}.icon-eject:before{content:"\e709"}.icon-chevron-left:before{content:"\e70a"}.icon-chevron-right:before{content:"\e70b"}.icon-plus-sign:before{content:"\e70c"}.icon-minus-sign:before{content:"\e70d"}.icon-remove-sign:before{content:"\e70e"}.icon-ok-sign:before{content:"\e70f"}.icon-question-sign:before{content:"\e710"}.icon-info-sign:before{content:"\e711"}.icon-screenshot:before{content:"\e712"}.icon-remove-circle:before{content:"\e713"}.icon-ok-circle:before{content:"\e714"}.icon-ban-circle:before{content:"\e715"}.icon-arrow-left2:before{content:"\e716"}.icon-arrow-right2:before{content:"\e717"}.icon-arrow-up2:before{content:"\e718"}.icon-arrow-down2:before{content:"\e719"}.icon-share-alt:before{content:"\e71a"}.icon-resize-full:before{content:"\e71b"}.icon-resize-small:before{content:"\e71c"}.icon-plus:before{content:"\e71d"}.icon-minus:before{content:"\e71e"}.icon-asterisk:before{content:"\e71f"}.icon-exclamation-sign:before{content:"\e720"}.icon-gift:before{content:"\e721"}.icon-leaf:before{content:"\e722"}.icon-fire2:before{content:"\e723"}.icon-eye-open:before{content:"\e724"}.icon-eye-close:before{content:"\e725"}.icon-warning-sign:before{content:"\e726"}.icon-plane:before{content:"\e727"}.icon-calendar3:before{content:"\e728"}.icon-random:before{content:"\e729"}.icon-comment2:before{content:"\e72a"}.icon-magnet:before{content:"\e72b"}.icon-chevron-up:before{content:"\e72c"}.icon-chevron-down:before{content:"\e72d"}.icon-retweet:before{content:"\e72e"}.icon-shopping-cart:before{content:"\e72f"}.icon-folder-close:before{content:"\e730"}.icon-folder-open:before{content:"\e731"}.icon-resize-vertical:before{content:"\e732"}.icon-resize-horizontal:before{content:"\e733"}.icon-bar-chart:before{content:"\e734"}.icon-twitter-sign:before{content:"\e735"}.icon-facebook-sign:before{content:"\e736"}.icon-camera-retro:before{content:"\e737"}.icon-key2:before{content:"\e738"}.icon-cogs:before{content:"\e739"}.icon-comments:before{content:"\e73a"}.icon-thumbs-up:before{content:"\e73b"}.icon-thumbs-down:before{content:"\e73c"}.icon-star-half:before{content:"\e73d"}.icon-heart-empty:before{content:"\e73e"}.icon-signout:before{content:"\e73f"}.icon-linkedin-sign:before{content:"\e740"}.icon-pushpin:before{content:"\e741"}.icon-external-link:before{content:"\e742"}.icon-signin:before{content:"\e743"}.icon-trophy:before{content:"\e744"}.icon-github-sign:before{content:"\e745"}.icon-upload-alt:before{content:"\e746"}.icon-lemon:before{content:"\e747"}.icon-phone3:before{content:"\e748"}.icon-check-empty:before{content:"\e749"}.icon-bookmark-empty:before{content:"\e74a"}.icon-phone-sign:before{content:"\e74b"}.icon-twitter2:before{content:"\e74c"}.icon-facebook2:before{content:"\e74d"}.icon-github2:before{content:"\e74e"}.icon-unlock:before{content:"\e74f"}.icon-credit:before{content:"\e750"}.icon-rss2:before{content:"\e751"}.icon-hdd:before{content:"\e752"}.icon-bullhorn2:before{content:"\e753"}.icon-bell:before{content:"\e754"}.icon-certificate:before{content:"\e755"}.icon-hand-right:before{content:"\e756"}.icon-hand-left:before{content:"\e757"}.icon-hand-up:before{content:"\e758"}.icon-hand-down:before{content:"\e759"}.icon-circle-arrow-left:before{content:"\e75a"}.icon-circle-arrow-right:before{content:"\e75b"}.icon-circle-arrow-up:before{content:"\e75c"}.icon-circle-arrow-down:before{content:"\e75d"}.icon-globe:before{content:"\e75e"}.icon-wrench:before{content:"\e75f"}.icon-tasks:before{content:"\e760"}.icon-filter:before{content:"\e761"}.icon-briefcase:before{content:"\e762"}.icon-fullscreen:before{content:"\e763"}.icon-group:before{content:"\e764"}.icon-link:before{content:"\e765"}.icon-cloud3:before{content:"\e766"}.icon-beaker:before{content:"\e767"}.icon-cut:before{content:"\e768"}.icon-copy:before{content:"\e769"}.icon-paper-clip:before{content:"\e76a"}.icon-save:before{content:"\e76b"}.icon-sign-blank:before{content:"\e76c"}.icon-reorder:before{content:"\e76d"}.icon-list-ul:before{content:"\e76e"}.icon-list-ol:before{content:"\e76f"}.icon-strikethrough:before{content:"\e770"}.icon-underline:before{content:"\e771"}.icon-table:before{content:"\e772"}.icon-magic:before{content:"\e773"}.icon-truck2:before{content:"\e774"}.icon-pinterest2:before{content:"\e775"}.icon-pinterest-sign:before{content:"\e776"}.icon-google-plus-sign:before{content:"\e777"}.icon-google-plus:before{content:"\e778"}.icon-money:before{content:"\e779"}.icon-caret-down:before{content:"\e77a"}.icon-caret-up:before{content:"\e77b"}.icon-caret-left:before{content:"\e77c"}.icon-caret-right:before{content:"\e77d"}.icon-columns:before{content:"\e77e"}.icon-sort:before{content:"\e77f"}.icon-sort-down:before{content:"\e780"}.icon-sort-up:before{content:"\e781"}.icon-envelope-alt:before{content:"\e782"}.icon-linkedin2:before{content:"\e783"}.icon-undo:before{content:"\e784"}.icon-legal:before{content:"\e785"}.icon-dashboard:before{content:"\e786"}.icon-comment-alt:before{content:"\e787"}.icon-comments-alt:before{content:"\e788"}.icon-bolt2:before{content:"\e789"}.icon-sitemap:before{content:"\e78a"}.icon-umbrella:before{content:"\e78b"}.icon-paste:before{content:"\e78c"}.icon-lightbulb:before{content:"\e78d"}.icon-exchange:before{content:"\e78e"}.icon-cloud-download:before{content:"\e78f"}.icon-cloud-upload:before{content:"\e790"}.icon-user-md:before{content:"\e791"}.icon-stethoscope:before{content:"\e792"}.icon-suitcase:before{content:"\e793"}.icon-bell-alt:before{content:"\e794"}.icon-coffee2:before{content:"\e795"}.icon-food2:before{content:"\e796"}.icon-file-alt:before{content:"\e797"}.icon-building:before{content:"\e798"}.icon-hospital:before{content:"\e799"}.icon-ambulance:before{content:"\e79a"}.icon-medkit:before{content:"\e79b"}.icon-fighter-jet:before{content:"\e79c"}.icon-beer:before{content:"\e79d"}.icon-h-sign:before{content:"\e79e"}.icon-plus-sign2:before{content:"\e79f"}.icon-double-angle-left:before{content:"\e7a0"}.icon-double-angle-right:before{content:"\e7a1"}.icon-double-angle-up:before{content:"\e7a2"}.icon-double-angle-down:before{content:"\e7a3"}.icon-angle-left:before{content:"\e7a4"}.icon-angle-right:before{content:"\e7a5"}.icon-angle-up:before{content:"\e7a6"}.icon-angle-down:before{content:"\e7a7"}.icon-desktop:before{content:"\e7a8"}.icon-laptop2:before{content:"\e7a9"}.icon-tablet2:before{content:"\e7aa"}.icon-mobile:before{content:"\e7ab"}.icon-circle-blank:before{content:"\e7ac"}.icon-quote-left:before{content:"\e7ad"}.icon-quote-right:before{content:"\e7ae"}.icon-spinner:before{content:"\e7af"}.icon-circle:before{content:"\e7b0"}.icon-reply:before{content:"\e7b1"}.icon-github-alt:before{content:"\e7b2"}.icon-folder-close-alt:before{content:"\e7b3"}.icon-folder-open-alt:before{content:"\e7b4"}.icon-expand-alt:before{content:"\e7b5"}.icon-collapse-alt:before{content:"\e7b6"}.icon-smile:before{content:"\e7b7"}.icon-frown:before{content:"\e7b8"}.icon-meh:before{content:"\e7b9"}.icon-gamepad:before{content:"\e7ba"}.icon-keyboard:before{content:"\e7bb"}.icon-flag-alt:before{content:"\e7bc"}.icon-flag-checkered:before{content:"\e7bd"}.icon-terminal:before{content:"\e7be"}.icon-code:before{content:"\e7bf"}.icon-reply-all:before{content:"\e7c0"}.icon-star-half-full:before{content:"\e7c1"}.icon-location-arrow:before{content:"\e7c2"}.icon-crop2:before{content:"\e7c3"}.icon-code-fork:before{content:"\e7c4"}.icon-unlink:before{content:"\e7c5"}.icon-question:before{content:"\e7c6"}.icon-info:before{content:"\e7c7"}.icon-exclamation:before{content:"\e7c8"}.icon-superscript:before{content:"\e7c9"}.icon-subscript:before{content:"\e7ca"}.icon-eraser:before{content:"\e7cb"}.icon-puzzle:before{content:"\e7cc"}.icon-microphone2:before{content:"\e7cd"}.icon-microphone-off2:before{content:"\e7ce"}.icon-shield:before{content:"\e7cf"}.icon-calendar-empty:before{content:"\e7d0"}.icon-fire-extinguisher:before{content:"\e7d1"}.icon-rocket:before{content:"\e7d2"}.icon-maxcdn:before{content:"\e7d3"}.icon-chevron-sign-left:before{content:"\e7d4"}.icon-chevron-sign-right:before{content:"\e7d5"}.icon-chevron-sign-up:before{content:"\e7d6"}.icon-chevron-sign-down:before{content:"\e7d7"}.icon-html52:before{content:"\e7d8"}.icon-css3:before{content:"\e7d9"}.icon-anchor:before{content:"\e7da"}.icon-unlock-alt:before{content:"\e7db"}.icon-bullseye:before{content:"\e7dc"}.icon-ellipsis-horizontal:before{content:"\e7dd"}.icon-ellipsis-vertical:before{content:"\e7de"}.icon-rss-sign:before{content:"\e7df"}.icon-play-sign:before{content:"\e7e0"}.icon-ticket:before{content:"\e7e1"}.icon-minus-sign-alt:before{content:"\e7e2"}.icon-check-minus:before{content:"\e7e3"}.icon-level-up:before{content:"\e7e4"}.icon-level-down:before{content:"\e7e5"}.icon-check-sign:before{content:"\e7e6"}.icon-edit-sign:before{content:"\e7e7"}.icon-external-link-sign:before{content:"\e7e8"}.icon-share-sign:before{content:"\e7e9"}.icon-compass:before{content:"\e7ea"}.icon-collapse:before{content:"\e7eb"}.icon-collapse-top:before{content:"\e7ec"}.icon-expand:before{content:"\e7ed"}.icon-euro:before{content:"\e7ee"}.icon-gbp:before{content:"\e7ef"}.icon-dollar:before{content:"\e7f0"}.icon-rupee:before{content:"\e7f1"}.icon-yen:before{content:"\e7f2"}.icon-renminbi:before{content:"\e7f3"}.icon-won:before{content:"\e7f4"}.icon-bitcoin2:before{content:"\e7f5"}.icon-file3:before{content:"\e7f6"}.icon-file-text:before{content:"\e7f7"}.icon-sort-by-alphabet:before{content:"\e7f8"}.icon-sort-by-alphabet-alt:before{content:"\e7f9"}.icon-sort-by-attributes:before{content:"\e7fa"}.icon-sort-by-attributes-alt:before{content:"\e7fb"}.icon-sort-by-order:before{content:"\e7fc"}.icon-sort-by-order-alt:before{content:"\e7fd"}.icon-thumbs-up2:before{content:"\e7fe"}.icon-thumbs-down2:before{content:"\e7ff"}.icon-youtube-sign:before{content:"\e800"}.icon-youtube2:before{content:"\e801"}.icon-xing2:before{content:"\e802"}.icon-xing-sign:before{content:"\e803"}.icon-youtube-play:before{content:"\e804"}.icon-dropbox2:before{content:"\e805"}.icon-stackexchange:before{content:"\e806"}.icon-instagram2:before{content:"\e807"}.icon-flickr2:before{content:"\e808"}.icon-adn:before{content:"\e809"}.icon-bitbucket2:before{content:"\e80a"}.icon-bitbucket-sign:before{content:"\e80b"}.icon-tumblr2:before{content:"\e80c"}.icon-tumblr-sign:before{content:"\e80d"}.icon-long-arrow-down:before{content:"\e80e"}.icon-long-arrow-up:before{content:"\e80f"}.icon-long-arrow-left:before{content:"\e810"}.icon-long-arrow-right:before{content:"\e811"}.icon-apple:before{content:"\e812"}.icon-windows3:before{content:"\e813"}.icon-android2:before{content:"\e814"}.icon-linux:before{content:"\e815"}.icon-dribbble2:before{content:"\e816"}.icon-skype2:before{content:"\e817"}.icon-foursquare2:before{content:"\e818"}.icon-trello:before{content:"\e819"}.icon-female:before{content:"\e81a"}.icon-male:before{content:"\e81b"}.icon-gittip:before{content:"\e81c"}.icon-sun2:before{content:"\e81d"}.icon-moon:before{content:"\e81e"}.icon-archive2:before{content:"\e81f"}.icon-bug:before{content:"\e820"}.icon-renren:before{content:"\e821"}.icon-weibo2:before{content:"\e822"}.icon-vk2:before{content:"\e823"}.icon-line-eye:before{content:"\e000"}.icon-line-paper-clip:before{content:"\e001"}.icon-line-mail:before{content:"\e002"}.icon-line-toggle:before{content:"\e003"}.icon-line-layout:before{content:"\e004"}.icon-line-link:before{content:"\e005"}.icon-line-bell:before{content:"\e006"}.icon-line-lock:before{content:"\e007"}.icon-line-unlock:before{content:"\e008"}.icon-line-ribbon:before{content:"\e009"}.icon-line-image:before{content:"\e010"}.icon-line-signal:before{content:"\e011"}.icon-line-target:before{content:"\e012"}.icon-line-clipboard:before{content:"\e013"}.icon-line-clock:before{content:"\e014"}.icon-line-watch:before{content:"\e015"}.icon-line-air-play:before{content:"\e016"}.icon-line-camera:before{content:"\e017"}.icon-line-video:before{content:"\e018"}.icon-line-disc:before{content:"\e019"}.icon-line-printer:before{content:"\e020"}.icon-line-monitor:before{content:"\e021"}.icon-line-server:before{content:"\e022"}.icon-line-cog:before{content:"\e023"}.icon-line-heart:before{content:"\e024"}.icon-line-paragraph:before{content:"\e025"}.icon-line-align-justify:before{content:"\e026"}.icon-line-align-left:before{content:"\e027"}.icon-line-align-center:before{content:"\e028"}.icon-line-align-right:before{content:"\e029"}.icon-line-book:before{content:"\e030"}.icon-line-layers:before{content:"\e031"}.icon-line-stack:before{content:"\e032"}.icon-line-stack-2:before{content:"\e033"}.icon-line-paper:before{content:"\e034"}.icon-line-paper-stack:before{content:"\e035"}.icon-line-search:before{content:"\e036"}.icon-line-zoom-in:before{content:"\e037"}.icon-line-zoom-out:before{content:"\e038"}.icon-line-reply:before{content:"\e039"}.icon-line-circle-plus:before{content:"\e040"}.icon-line-circle-minus:before{content:"\e041"}.icon-line-circle-check:before{content:"\e042"}.icon-line-circle-cross:before{content:"\e043"}.icon-line-square-plus:before{content:"\e044"}.icon-line-square-minus:before{content:"\e045"}.icon-line-square-check:before{content:"\e046"}.icon-line-square-cross:before{content:"\e047"}.icon-line-microphone:before{content:"\e048"}.icon-line-record:before{content:"\e049"}.icon-line-skip-back:before{content:"\e050"}.icon-line-rewind:before{content:"\e051"}.icon-line-play:before{content:"\e052"}.icon-line-pause:before{content:"\e053"}.icon-line-stop:before{content:"\e054"}.icon-line-fast-forward:before{content:"\e055"}.icon-line-skip-forward:before{content:"\e056"}.icon-line-shuffle:before{content:"\e057"}.icon-line-repeat:before{content:"\e058"}.icon-line-folder:before{content:"\e059"}.icon-line-umbrella:before{content:"\e060"}.icon-line-moon:before{content:"\e061"}.icon-line-thermometer:before{content:"\e062"}.icon-line-drop:before{content:"\e063"}.icon-line-sun:before{content:"\e064"}.icon-line-cloud:before{content:"\e065"}.icon-line-cloud-upload:before{content:"\e066"}.icon-line-cloud-download:before{content:"\e067"}.icon-line-upload:before{content:"\e068"}.icon-line-download:before{content:"\e069"}.icon-line-location:before{content:"\e070"}.icon-line-location-2:before{content:"\e071"}.icon-line-map:before{content:"\e072"}.icon-line-battery:before{content:"\e073"}.icon-line-head:before{content:"\e074"}.icon-line-briefcase:before{content:"\e075"}.icon-line-speech-bubble:before{content:"\e076"}.icon-line-anchor:before{content:"\e077"}.icon-line-globe:before{content:"\e078"}.icon-line-box:before{content:"\e079"}.icon-line-reload:before{content:"\e080"}.icon-line-share:before{content:"\e081"}.icon-line-marquee:before{content:"\e082"}.icon-line-marquee-plus:before{content:"\e083"}.icon-line-marquee-minus:before{content:"\e084"}.icon-line-tag:before{content:"\e085"}.icon-line-power:before{content:"\e086"}.icon-line-command:before{content:"\e087"}.icon-line-alt:before{content:"\e088"}.icon-line-esc:before{content:"\e089"}.icon-line-bar-graph:before{content:"\e090"}.icon-line-bar-graph-2:before{content:"\e091"}.icon-line-pie-graph:before{content:"\e092"}.icon-line-star:before{content:"\e093"}.icon-line-arrow-left:before{content:"\e094"}.icon-line-arrow-right:before{content:"\e095"}.icon-line-arrow-up:before{content:"\e096"}.icon-line-arrow-down:before{content:"\e097"}.icon-line-volume:before{content:"\e098"}.icon-line-mute:before{content:"\e099"}.icon-line-content-right:before{content:"\e100"}.icon-line-content-left:before{content:"\e101"}.icon-line-grid:before{content:"\e102"}.icon-line-grid-2:before{content:"\e103"}.icon-line-columns:before{content:"\e104"}.icon-line-loader:before{content:"\e105"}.icon-line-bag:before{content:"\e106"}.icon-line-ban:before{content:"\e107"}.icon-line-flag:before{content:"\e108"}.icon-line-trash:before{content:"\e109"}.icon-line-expand:before{content:"\e110"}.icon-line-contract:before{content:"\e111"}.icon-line-maximize:before{content:"\e112"}.icon-line-minimize:before{content:"\e113"}.icon-line-plus:before{content:"\e114"}.icon-line-minus:before{content:"\e115"}.icon-line-check:before{content:"\e116"}.icon-line-cross:before{content:"\e117"}.icon-line-move:before{content:"\e118"}.icon-line-delete:before{content:"\e119"}.icon-line-menu:before{content:"\e120"}.icon-line-archive:before{content:"\e121"}.icon-line-inbox:before{content:"\e122"}.icon-line-outbox:before{content:"\e123"}.icon-line-file:before{content:"\e124"}.icon-line-file-add:before{content:"\e125"}.icon-line-file-subtract:before{content:"\e126"}.icon-line-help:before{content:"\e127"}.icon-line-open:before{content:"\e128"}.icon-line-ellipsis:before{content:"\e129"}



/**	17. Social icons
*************************************************** **/
.social-icon {
	margin: 0 5px 5px 0;
	width: 40px;
	height: 40px;
	text-shadow: 1px 1px 1px rgba(0,0,0,.3);
	border-color: transparent;
	overflow: hidden;
	display:inline-block;
	text-decoration:none !important;
	text-align: center;
	cursor: pointer;
	font-style: normal;
	letter-spacing: 0em !important;
	color: #eaeaea !important;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
.social-icon:hover>i {
	color:#fff;
}
.social-icon i {
	display: block;
	position: relative;
	width: 40px;
	height: 40px;
	margin-top:6px;
	font-size: 28px;
}

.social-icon:hover i:first-child {
	margin-top: -38px;
}
.social-icon:hover i:last-child {
	margin-top:4px;
}


body:not(.device-touch) .social-icon {
	-webkit-transition: all .3s ease;
	-o-transition: all .3s ease;
	transition: all .3s ease;
}

body:not(.device-touch) .social-icon i {
	-webkit-transition: all .3s ease;
	-o-transition: all .3s ease;
	transition: all .3s ease;
}


/* medium */
a.social-icon>i[class*=" icon-"],
a.social-icon>i[class^=icon-] {
	font-size:20px;
}

/* small */
a.social-icon.social-icon-sm,
a.social-icon.social-icon-sm>i {
	width:30px;
	height:30px;
	margin-top:3px;
	font-size: 17px;
	line-height:23px;
}
a.social-icon.social-icon-sm>i:last-child {
	padding-top:7px;
}
a.social-icon.social-icon-sm>i[class*=" icon-"],
a.social-icon.social-icon-sm>i[class^=icon-] {
	font-size:17px;
}


.social-facebook:hover { background-color: #3B5998 !important; }
.social-facebook { background-color: #3B5998; }
.social-delicious:hover { background-color: #205CC0 !important; }
.social-delicious { background-color: #205CC0; }
.social-paypal:hover { background-color: #00588B !important; }
.social-paypal { background-color: #00588B; }
.social-flattr:hover{ background-color: #F67C1A !important; }
.social-flattr { background-color: #F67C1A; }
.social-android:hover{ background-color: #A4C639 !important; }
.social-android { background-color: #A4C639; }
.social-smashmag:hover { background-color: #E53B2C !important; }
.social-smashmag { background-color: #E53B2C; }
.social-gplus:hover { background-color: #DD4B39 !important; }
.social-gplus { background-color: #DD4B39; }
.social-wikipedia:hover { background-color: #333 !important; }
.social-wikipedia { background-color: #333; }
.social-stumbleupon:hover { background-color: #F74425 !important; }
.social-stumbleupon { background-color: #F74425; }
.social-foursquare:hover { background-color: #25A0CA !important; }
.social-foursquare { background-color: #25A0CA; }
.social-call:hover { background-color: #444 !important; }
.social-call { background-color: #444; }
.social-ninetyninedesigns:hover { background-color: #F26739 !important; }
.social-ninetyninedesigns { background-color: #F26739; }
.social-forrst:hover { background-color: #5B9A68 !important; }
.social-forrst { background-color: #5B9A68; }
.social-digg:hover { background-color: #191919 !important; }
.social-digg { background-color: #191919; }
.social-spotify:hover{ background-color: #81B71A !important; }
.social-spotify { background-color: #81B71A; }
.social-reddit:hover { background-color: #C6C6C6 !important; }
.social-reddit { background-color: #C6C6C6; }
.social-blogger:hover { background-color: #FC4F08 !important; }
.social-blogger { background-color: #FC4F08; }
.social-cc:hover { background-color: #688527 !important; }
.social-cc { background-color: #688527; }
.social-dribbble:hover { background-color: #EA4C89 !important; }
.social-dribbble { background-color: #EA4C89; }
.social-evernote:hover { background-color: #5BA525 !important; }
.social-evernote { background-color: #5BA525; }
.social-flickr:hover { background-color: #FF0084 !important; }
.social-flickr { background-color: #FF0084; }
.social-google:hover { background-color: #DD4B39 !important; }
.social-google { background-color: #DD4B39; }
.social-instapaper:hover { background-color: #333 !important; }
.social-instapaper { background-color: #333; }
.social-klout:hover { background-color: #FF5F52 !important; }
.social-klout { background-color: #FF5F52; }
.social-linkedin:hover { background-color: #0E76A8 !important; }
.social-linkedin { background-color: #0E76A8; }
.social-vk:hover { background-color: #2B587A !important; }
.social-vk { background-color: #2B587A; }
.social-rss:hover { background-color: #EE802F !important; }
.social-rss { background-color: #EE802F; }
.social-skype:hover { background-color: #00AFF0 !important; }
.social-skype { background-color: #00AFF0; }
.social-twitter:hover { background-color: #00ACEE !important; }
.social-twitter { background-color: #00ACEE; }
.social-youtube:hover { background-color: #C4302B !important; }
.social-youtube { background-color: #C4302B; }
.social-vimeo:hover { background-color: #86C9EF !important; }
.social-vimeo { background-color: #86C9EF; }
.social-aim:hover { background-color: #FCD20B !important; }
.social-aim { background-color: #FCD20B; }
.social-yahoo:hover { background-color: #720E9E !important; }
.social-yahoo { background-color: #720E9E; }
.social-email3:hover { background-color: #6567A5 !important; }
.social-email3 { background-color: #6567A5; }
.social-macstore:hover { background-color: #333333 !important; }
.social-macstore { background-color: #333333; }
.social-myspace:hover { background-color: #666666 !important; }
.social-myspace { background-color: #666666; }
.social-podcast:hover { background-color: #E4B21B !important; }
.social-podcast { background-color: #E4B21B; }
.social-cloudapp:hover { background-color: #525557 !important; }
.social-cloudapp { background-color: #525557; }
.social-dropbox:hover { background-color: #3D9AE8 !important; }
.social-dropbox { background-color: #3D9AE8; }
.social-ebay:hover { background-color: #89C507 !important; }
.social-ebay { background-color: #89C507; }
.social-github:hover { background-color: #171515 !important; }
.social-github { background-color: #171515; }
.social-googleplay:hover { background-color: #DD4B39 !important; }
.social-googleplay { background-color: #DD4B39; }
.social-itunes:hover { background-color: #222 !important; }
.social-itunes { background-color: #222; }
.social-plurk:hover { background-color: #CF5A00 !important; }
.social-plurk { background-color: #CF5A00; }
.social-pinboard:hover { background-color: #0000E6 !important; }
.social-pinboard { background-color: #0000E6; }
.social-soundcloud:hover { background-color: #FF7700 !important; }
.social-soundcloud { background-color: #FF7700; }
.social-tumblr:hover { background-color: #34526F !important; }
.social-tumblr { background-color: #34526F; }
.social-wordpress:hover { background-color: #1E8CBE !important; }
.social-wordpress { background-color: #1E8CBE; }
.social-yelp:hover { background-color: #C41200 !important; }
.social-yelp { background-color: #C41200; }
.social-intensedebate:hover { background-color: #009EE4 !important; }
.social-intensedebate { background-color: #009EE4; }
.social-eventbrite:hover { background-color: #F16924 !important; }
.social-eventbrite { background-color: #F16924; }
.social-scribd:hover { background-color: #666666 !important; }
.social-scribd { background-color: #666666; }
.social-stripe:hover { background-color: #008CDD !important; }
.social-stripe { background-color: #008CDD; }
.social-print:hover { background-color: #111 !important; }
.social-print { background-color: #111; }
.social-dwolla:hover { background-color: #FF5C03 !important; }
.social-dwolla { background-color: #FF5C03; }
.social-statusnet:hover { background-color: #131A30 !important; }
.social-statusnet { background-color: #131A30; }
.social-acrobat:hover { background-color: #D3222A !important; }
.social-acrobat { background-color: #D3222A; }
.social-drupal:hover { background-color: #27537A !important; }
.social-drupal { background-color: #27537A; }
.social-buffer:hover { background-color: #333333 !important; }
.social-buffer { background-color: #333333; }
.social-pocket:hover { background-color: #EE4056 !important; }
.social-pocket { background-color: #EE4056; }
.social-bitbucket:hover { background-color: #0E4984 !important; }
.social-bitbucket { background-color: #0E4984; }
.social-stackoverflow:hover { background-color: #EF8236 !important; }
.social-stackoverflow { background-color: #EF8236; }
.social-hackernews:hover { background-color: #FF6600 !important; }
.social-hackernews { background-color: #FF6600; }
.social-xing:hover { background-color: #126567 !important; }
.social-xing { background-color: #126567; }
.social-instagram:hover { background-color: #3F729B !important; }
.social-instagram { background-color: #3F729B; }
.social-quora:hover { background-color: #A82400 !important; }
.social-quora { background-color: #A82400; }
.social-openid:hover { background-color: #E16309 !important; }
.social-openid { background-color: #E16309; }
.social-steam:hover { background-color: #111 !important; }
.social-steam { background-color: #111; }
.social-amazon:hover { background-color: #E47911 !important; }
.social-amazon { background-color: #E47911; }
.social-disqus:hover { background-color: #E4E7EE !important; }
.social-disqus { background-color: #E4E7EE; }
.social-plancast:hover { background-color: #222 !important; }
.social-plancast { background-color: #222; }
.social-appstore:hover { background-color: #000 !important; }
.social-appstore { background-color: #000; }
.social-pinterest:hover { background-color: #C8232C !important; }
.social-pinterest { background-color: #C8232C; }
.social-fivehundredpx:hover { background-color: #111 !important; }
.social-fivehundredpx { background-color: #111; }

/* rounded social icons */
a.social-icon.social-icon-round {
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
}

/* bordered social icons */
a.social-icon.social-icon-border {
	background-color:transparent;
	border:#999 1px solid;
	color:#565656 !important;
	text-shadow:none;
}
a.social-icon.social-icon-border:hover {
	border-color:transparent;
}
a.social-icon.social-icon-border:hover>i {
	color:#fff !important;
}
section.dark a.social-icon.social-icon-border {
	color:#fff !important;
}
section.dark a.social-icon.social-icon-border:hover>i {
	color:#111 !important;
}


/* light social icons */
a.social-icon.social-icon-light {
	background-color:rgba(0,0,0,0.1);
	text-shadow:none;
	color:#888 !important;
}
a.social-icon.social-icon-light:hover>i {
	color:#fff !important;
}
section.dark a.social-icon.social-icon-light {
	background-color:#666;
	color:#fff !important;
}
section.dark a.social-icon.social-icon-light:hover>i {
	color:#111 !important;
}


/* dark social icons */
a.social-icon.social-icon-dark {
	background-color:#444;
	text-shadow:none;
	color:#eee !important;
}
a.social-icon.social-icon-dark:hover>i {
	color:#fff !important;
}
section.dark a.social-icon.social-icon-dark {
	background-color:#111;
	color:#fff !important;
}



/* transparent social icons */
a.social-icon.social-icon-transparent {
	background-color:transparent;
	text-shadow:none;
	color:#666 !important;
}
a.social-icon.social-icon-transparent:hover>i {
	color:#fff !important;
}
section.dark a.social-icon.social-icon-transparent {
	color:#999 !important;
}
section.dark a.social-icon.social-icon-transparent:hover>i {
	color:#fff !important;
}





/**	18. Buttons
*************************************************** **/
.btn,
.btn-group {
	/*margin: 5px 5px 5px 0;*/
}

.btn.pull-right,
.btn-group.pull-right {
	margin: 5px 0 5px 5px;
}

.btn-group .btn {
	margin-right:0;
	margin-left:0;
}
#header .btn,
#footer .btn,
.dropdown .btn,
.input-group .btn {
	margin:0;
}

/* translucid */
.btn-translucid {
	color:#fff;
	border:rgba(0,0,0,0.07) 1px solid;
	background-color:rgba(0,0,0,0.07);

	-webkit-transition: all .400s;
	-moz-transition: all .400s;
	-o-transition: all .400s;
	transition: all .400s;
}
.btn-translucid:hover {
	color:#fff;
	background-color:rgba(0,0,0,0.2);
}

/* 3D Buttons */
.btn-3d {
	border-bottom: 3px solid rgba(0,0,0,.15);
}
.btn-3d:hover {
	opacity: 0.9;
	filter: alpha(opacity=90);
}
.btn-3d.btn-link {
	border-bottom:0;
}

/* Extra Large Buttons */
.btn-xlg {
	padding: 24px 34px;
}

/* Button reveal */
.btn.btn-reveal {
	padding-left:28px;
	padding-right:28px;
	overflow: hidden;
	position:relative;
}

.btn.btn-reveal i {
	display: block;
	position: absolute;
	left: -32px;
	width: 32px;
	top: 0;
	bottom:0;
	margin: 0;
	padding:0;
	font-size:17px;
	line-height:34px;
	text-align: center;
	background-color: rgba(0,0,0,0.1);
}
.btn.btn-reveal span {
	display: inline-block;
	position: relative;
	left: 0;
}
body:not(.device-touch) .btn.btn-reveal i,
body:not(.device-touch) .btn.btn-reveal span {
	-webkit-transition: left 0.3s ease, right 0.3s ease;
	-o-transition: left 0.3s ease, right 0.3s ease;
	transition: left 0.3s ease, right 0.3s ease;
}

/* extra small */
.btn.btn-reveal.btn-xs {
	padding: 0 17px;
}
.btn.btn-reveal.btn-xs i {
	left: -22px;
	width: 22px;
	height: 28px;
	line-height: 20px;
	font-size:12px;
}
/* small */
.btn.btn-reveal.btn-sm {
	padding-left: 22px;
	padding-right: 22px;
}
.btn.btn-reveal.btn-sm i {
	left: -26px;
	width: 26px;
	height: 34px;
	line-height: 28px;
	font-size:14px;
}
/* large */
.btn.btn-reveal.btn-lg {
	padding-left:32px;
	padding-right:32px;
}
.btn.btn-reveal.btn-lg i {
	left: -38px;
	width: 38px;
	height: 46px;
	line-height: 46px;
}
/* extra large */
.btn.btn-reveal.btn-xlg {
	padding-left:40px;
	padding-right:40px;
}
.btn.btn-reveal.btn-xlg i {
	left: -44px;
	width: 44px;
	height: 72px;
	line-height: 72px;
	font-size:20px;
}

.btn.btn-reveal:hover i {
	left: 0;
}
.btn.btn-reveal:hover span {
	left: 16px;
}


/* Button Bordered */
.btn.btn-bordered {
	border-color:#333;
	border-width:2px;
}
.btn.btn-bordered:hover {
	color:#fff;
	background-color:#333;
}

/* button colors */
.btn-red { background-color: #C02942; color: #FFF !important; }
.btn-teal { background-color: #53777A; color: #FFF !important; }
.btn-yellow { background-color: #ECD078; color: #333 !important; }
.btn-green { background-color: #59BA41; color: #FFF !important; }
.btn-brown { background-color: #774F38; color: #FFF !important; }
.btn-aqua { background-color: #40C0CB; color: #FFF !important; }
.btn-lime { background-color: #AEE239; color: #FFF !important; }
.btn-purple { background-color: #5D4157; color: #FFF !important; }
.btn-leaf { background-color: #A8CABA; color: #333 !important; }
.btn-pink { background-color: #F89FA1; color: #FFF !important; }
.btn-dirtygreen { background-color: #1693A5; color: #FFF !important; }
.btn-blue { background-color: #1265A8; color: #FFF !important; }
.btn-amber { background-color: #EB9C4D; color: #FFF !important; }
.btn-black { background-color: #111; color: #FFF !important; }
.btn-white { background-color: #F9F9F9; color: #333 !important; }


.fullwidth.btn-red:hover,
.fullwidth.btn-teal:hover,
.fullwidth.btn-yellow:hover,
.fullwidth.btn-green:hover,
.fullwidth.btn-brown:hover,
.fullwidth.btn-aqua:hover,
.fullwidth.btn-lime:hover,
.fullwidth.btn-purple:hover,
.fullwidth.btn-leaf:hover,
.fullwidth.btn-pink:hover,
.fullwidth.btn-dirtygreen:hover,
.fullwidth.btn-blue:hover,
.fullwidth.btn-amber:hover,
.fullwidth.btn-black:hover,
.fullwidth.btn-white:hover {
	color:#fff !important;
	background-color:#434343;
}



section.dark .btn-default {
	color:#fff;
	background-color:transparent;
	border-color:rgba(255,255,255,0.5);
}


.btn-clean {
	color:#555;
}


/* BOOTSTRAP REWRITE */
.btn,
.form-control {
	height:40px;
}
.btn-sm,
.btn-xs,
.btn-lg,
.btn-xlg {
	height:auto;
}
a.btn {
	line-height:26px;
}
a.btn.btn-xs {
	line-height:inherit;
}

.btn-3d {
	line-height:25px;
	margin-bottom:3px;
}
.btn-3d.btn-xlg {
	line-height:inherit;
}



/* bootstrap modal */
.modal-backdrop {
	background-color:#fff;
}
.modal-content {
	border:0;
	border-top:#333 4px solid;
}
.modal-dialog {
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;

	-webkit-box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
	-moz-box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
	box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}
.modal-header .close {
	margin-top:2px;
}




/*
 * Social Buttons for Bootstrap
 * https://github.com/lipis/bootstrap-social

	<a class="btn btn-block btn-social btn-twitter">
		<i class="fa fa-twitter"></i> Sign in with Twitter
	</a>


	<a class="btn btn-social-icon btn-twitter">
		<i class="fa fa-twitter"></i>
	</a>
 */
.btn-social{position:relative;padding-left:54px;text-align:left;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;line-height:26px; margin:0;}
.btn-social>:first-child{position:absolute;left:0;top:0;bottom:0;width:42px;line-height:40px;font-size:1.6em;text-align:center;border-right:1px solid rgba(0,0,0,0.2)}
.btn-social>i{padding-right:0;height:40px;}
.btn-social.btn-lg{padding-left:61px}.btn-social.btn-lg>:first-child{line-height:45px;width:45px;font-size:1.8em}
.btn-social.btn-sm{padding-left:38px}.btn-social.btn-sm>:first-child{line-height:28px;width:28px;font-size:1.4em}
.btn-social.btn-xs{padding-left:30px}.btn-social.btn-xs>:first-child{line-height:20px;width:20px;font-size:1.2em}
.btn-social-icon{position:relative;padding-left:44px;text-align:left;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;height:34px;width:34px;padding:0}.btn-social-icon>:first-child{position:absolute;left:0;top:0;bottom:0;width:32px;line-height:34px;font-size:1.6em;text-align:center;border-right:1px solid rgba(0,0,0,0.2)}
.btn-social-icon.btn-lg{padding-left:61px}.btn-social-icon.btn-lg>:first-child{line-height:45px;width:45px;font-size:1.8em}
.btn-social-icon.btn-sm{padding-left:38px}.btn-social-icon.btn-sm>:first-child{line-height:28px;width:28px;font-size:1.4em}
.btn-social-icon.btn-xs{padding-left:30px}.btn-social-icon.btn-xs>:first-child{line-height:20px;width:20px;font-size:1.2em}
.btn-social-icon>:first-child{border:none;text-align:center;width:100% !important}
.btn-social-icon.btn-lg{height:45px;width:45px;padding-left:0;padding-right:0}
.btn-social-icon.btn-sm{height:30px;width:30px;padding-left:0;padding-right:0}
.btn-social-icon.btn-xs{height:22px;width:22px;padding-left:0;padding-right:0}
.btn-adn{color:#fff;background-color:#d87a68;border-color:rgba(0,0,0,0.2)}.btn-adn:hover,.btn-adn:focus,.btn-adn:active,.btn-adn.active,.open>.dropdown-toggle.btn-adn{color:#fff;background-color:#ce563f;border-color:rgba(0,0,0,0.2)}
.btn-adn:active,.btn-adn.active,.open>.dropdown-toggle.btn-adn{background-image:none}
.btn-adn.disabled,.btn-adn[disabled],fieldset[disabled] .btn-adn,.btn-adn.disabled:hover,.btn-adn[disabled]:hover,fieldset[disabled] .btn-adn:hover,.btn-adn.disabled:focus,.btn-adn[disabled]:focus,fieldset[disabled] .btn-adn:focus,.btn-adn.disabled:active,.btn-adn[disabled]:active,fieldset[disabled] .btn-adn:active,.btn-adn.disabled.active,.btn-adn[disabled].active,fieldset[disabled] .btn-adn.active{background-color:#d87a68;border-color:rgba(0,0,0,0.2)}
.btn-adn .badge{color:#d87a68;background-color:#fff}
.btn-bitbucket{color:#fff;background-color:#205081;border-color:rgba(0,0,0,0.2)}.btn-bitbucket:hover,.btn-bitbucket:focus,.btn-bitbucket:active,.btn-bitbucket.active,.open>.dropdown-toggle.btn-bitbucket{color:#fff;background-color:#163758;border-color:rgba(0,0,0,0.2)}
.btn-bitbucket:active,.btn-bitbucket.active,.open>.dropdown-toggle.btn-bitbucket{background-image:none}
.btn-bitbucket.disabled,.btn-bitbucket[disabled],fieldset[disabled] .btn-bitbucket,.btn-bitbucket.disabled:hover,.btn-bitbucket[disabled]:hover,fieldset[disabled] .btn-bitbucket:hover,.btn-bitbucket.disabled:focus,.btn-bitbucket[disabled]:focus,fieldset[disabled] .btn-bitbucket:focus,.btn-bitbucket.disabled:active,.btn-bitbucket[disabled]:active,fieldset[disabled] .btn-bitbucket:active,.btn-bitbucket.disabled.active,.btn-bitbucket[disabled].active,fieldset[disabled] .btn-bitbucket.active{background-color:#205081;border-color:rgba(0,0,0,0.2)}
.btn-bitbucket .badge{color:#205081;background-color:#fff}
.btn-dropbox{color:#fff;background-color:#1087dd;border-color:rgba(0,0,0,0.2)}.btn-dropbox:hover,.btn-dropbox:focus,.btn-dropbox:active,.btn-dropbox.active,.open>.dropdown-toggle.btn-dropbox{color:#fff;background-color:#0d6aad;border-color:rgba(0,0,0,0.2)}
.btn-dropbox:active,.btn-dropbox.active,.open>.dropdown-toggle.btn-dropbox{background-image:none}
.btn-dropbox.disabled,.btn-dropbox[disabled],fieldset[disabled] .btn-dropbox,.btn-dropbox.disabled:hover,.btn-dropbox[disabled]:hover,fieldset[disabled] .btn-dropbox:hover,.btn-dropbox.disabled:focus,.btn-dropbox[disabled]:focus,fieldset[disabled] .btn-dropbox:focus,.btn-dropbox.disabled:active,.btn-dropbox[disabled]:active,fieldset[disabled] .btn-dropbox:active,.btn-dropbox.disabled.active,.btn-dropbox[disabled].active,fieldset[disabled] .btn-dropbox.active{background-color:#1087dd;border-color:rgba(0,0,0,0.2)}
.btn-dropbox .badge{color:#1087dd;background-color:#fff}
.btn-facebook{color:#fff;background-color:#3b5998;border-color:rgba(0,0,0,0.2)}.btn-facebook:hover,.btn-facebook:focus,.btn-facebook:active,.btn-facebook.active,.open>.dropdown-toggle.btn-facebook{color:#fff;background-color:#2d4373;border-color:rgba(0,0,0,0.2)}
.btn-facebook:active,.btn-facebook.active,.open>.dropdown-toggle.btn-facebook{background-image:none}
.btn-facebook.disabled,.btn-facebook[disabled],fieldset[disabled] .btn-facebook,.btn-facebook.disabled:hover,.btn-facebook[disabled]:hover,fieldset[disabled] .btn-facebook:hover,.btn-facebook.disabled:focus,.btn-facebook[disabled]:focus,fieldset[disabled] .btn-facebook:focus,.btn-facebook.disabled:active,.btn-facebook[disabled]:active,fieldset[disabled] .btn-facebook:active,.btn-facebook.disabled.active,.btn-facebook[disabled].active,fieldset[disabled] .btn-facebook.active{background-color:#3b5998;border-color:rgba(0,0,0,0.2)}
.btn-facebook .badge{color:#3b5998;background-color:#fff}
.btn-flickr{color:#fff;background-color:#ff0084;border-color:rgba(0,0,0,0.2)}.btn-flickr:hover,.btn-flickr:focus,.btn-flickr:active,.btn-flickr.active,.open>.dropdown-toggle.btn-flickr{color:#fff;background-color:#cc006a;border-color:rgba(0,0,0,0.2)}
.btn-flickr:active,.btn-flickr.active,.open>.dropdown-toggle.btn-flickr{background-image:none}
.btn-flickr.disabled,.btn-flickr[disabled],fieldset[disabled] .btn-flickr,.btn-flickr.disabled:hover,.btn-flickr[disabled]:hover,fieldset[disabled] .btn-flickr:hover,.btn-flickr.disabled:focus,.btn-flickr[disabled]:focus,fieldset[disabled] .btn-flickr:focus,.btn-flickr.disabled:active,.btn-flickr[disabled]:active,fieldset[disabled] .btn-flickr:active,.btn-flickr.disabled.active,.btn-flickr[disabled].active,fieldset[disabled] .btn-flickr.active{background-color:#ff0084;border-color:rgba(0,0,0,0.2)}
.btn-flickr .badge{color:#ff0084;background-color:#fff}
.btn-foursquare{color:#fff;background-color:#f94877;border-color:rgba(0,0,0,0.2)}.btn-foursquare:hover,.btn-foursquare:focus,.btn-foursquare:active,.btn-foursquare.active,.open>.dropdown-toggle.btn-foursquare{color:#fff;background-color:#f71752;border-color:rgba(0,0,0,0.2)}
.btn-foursquare:active,.btn-foursquare.active,.open>.dropdown-toggle.btn-foursquare{background-image:none}
.btn-foursquare.disabled,.btn-foursquare[disabled],fieldset[disabled] .btn-foursquare,.btn-foursquare.disabled:hover,.btn-foursquare[disabled]:hover,fieldset[disabled] .btn-foursquare:hover,.btn-foursquare.disabled:focus,.btn-foursquare[disabled]:focus,fieldset[disabled] .btn-foursquare:focus,.btn-foursquare.disabled:active,.btn-foursquare[disabled]:active,fieldset[disabled] .btn-foursquare:active,.btn-foursquare.disabled.active,.btn-foursquare[disabled].active,fieldset[disabled] .btn-foursquare.active{background-color:#f94877;border-color:rgba(0,0,0,0.2)}
.btn-foursquare .badge{color:#f94877;background-color:#fff}
.btn-github{color:#fff;background-color:#444;border-color:rgba(0,0,0,0.2)}.btn-github:hover,.btn-github:focus,.btn-github:active,.btn-github.active,.open>.dropdown-toggle.btn-github{color:#fff;background-color:#2b2b2b;border-color:rgba(0,0,0,0.2)}
.btn-github:active,.btn-github.active,.open>.dropdown-toggle.btn-github{background-image:none}
.btn-github.disabled,.btn-github[disabled],fieldset[disabled] .btn-github,.btn-github.disabled:hover,.btn-github[disabled]:hover,fieldset[disabled] .btn-github:hover,.btn-github.disabled:focus,.btn-github[disabled]:focus,fieldset[disabled] .btn-github:focus,.btn-github.disabled:active,.btn-github[disabled]:active,fieldset[disabled] .btn-github:active,.btn-github.disabled.active,.btn-github[disabled].active,fieldset[disabled] .btn-github.active{background-color:#444;border-color:rgba(0,0,0,0.2)}
.btn-github .badge{color:#444;background-color:#fff}
.btn-google{color:#fff;background-color:#dd4b39;border-color:rgba(0,0,0,0.2)}.btn-google:hover,.btn-google:focus,.btn-google:active,.btn-google.active,.open>.dropdown-toggle.btn-google{color:#fff;background-color:#c23321;border-color:rgba(0,0,0,0.2)}
.btn-google:active,.btn-google.active,.open>.dropdown-toggle.btn-google{background-image:none}
.btn-google.disabled,.btn-google[disabled],fieldset[disabled] .btn-google,.btn-google.disabled:hover,.btn-google[disabled]:hover,fieldset[disabled] .btn-google:hover,.btn-google.disabled:focus,.btn-google[disabled]:focus,fieldset[disabled] .btn-google:focus,.btn-google.disabled:active,.btn-google[disabled]:active,fieldset[disabled] .btn-google:active,.btn-google.disabled.active,.btn-google[disabled].active,fieldset[disabled] .btn-google.active{background-color:#dd4b39;border-color:rgba(0,0,0,0.2)}
.btn-google .badge{color:#dd4b39;background-color:#fff}
.btn-instagram{color:#fff;background-color:#3f729b;border-color:rgba(0,0,0,0.2)}.btn-instagram:hover,.btn-instagram:focus,.btn-instagram:active,.btn-instagram.active,.open>.dropdown-toggle.btn-instagram{color:#fff;background-color:#305777;border-color:rgba(0,0,0,0.2)}
.btn-instagram:active,.btn-instagram.active,.open>.dropdown-toggle.btn-instagram{background-image:none}
.btn-instagram.disabled,.btn-instagram[disabled],fieldset[disabled] .btn-instagram,.btn-instagram.disabled:hover,.btn-instagram[disabled]:hover,fieldset[disabled] .btn-instagram:hover,.btn-instagram.disabled:focus,.btn-instagram[disabled]:focus,fieldset[disabled] .btn-instagram:focus,.btn-instagram.disabled:active,.btn-instagram[disabled]:active,fieldset[disabled] .btn-instagram:active,.btn-instagram.disabled.active,.btn-instagram[disabled].active,fieldset[disabled] .btn-instagram.active{background-color:#3f729b;border-color:rgba(0,0,0,0.2)}
.btn-instagram .badge{color:#3f729b;background-color:#fff}
.btn-linkedin{color:#fff;background-color:#007bb6;border-color:rgba(0,0,0,0.2)}.btn-linkedin:hover,.btn-linkedin:focus,.btn-linkedin:active,.btn-linkedin.active,.open>.dropdown-toggle.btn-linkedin{color:#fff;background-color:#005983;border-color:rgba(0,0,0,0.2)}
.btn-linkedin:active,.btn-linkedin.active,.open>.dropdown-toggle.btn-linkedin{background-image:none}
.btn-linkedin.disabled,.btn-linkedin[disabled],fieldset[disabled] .btn-linkedin,.btn-linkedin.disabled:hover,.btn-linkedin[disabled]:hover,fieldset[disabled] .btn-linkedin:hover,.btn-linkedin.disabled:focus,.btn-linkedin[disabled]:focus,fieldset[disabled] .btn-linkedin:focus,.btn-linkedin.disabled:active,.btn-linkedin[disabled]:active,fieldset[disabled] .btn-linkedin:active,.btn-linkedin.disabled.active,.btn-linkedin[disabled].active,fieldset[disabled] .btn-linkedin.active{background-color:#007bb6;border-color:rgba(0,0,0,0.2)}
.btn-linkedin .badge{color:#007bb6;background-color:#fff}
.btn-microsoft{color:#fff;background-color:#2672ec;border-color:rgba(0,0,0,0.2)}.btn-microsoft:hover,.btn-microsoft:focus,.btn-microsoft:active,.btn-microsoft.active,.open>.dropdown-toggle.btn-microsoft{color:#fff;background-color:#125acd;border-color:rgba(0,0,0,0.2)}
.btn-microsoft:active,.btn-microsoft.active,.open>.dropdown-toggle.btn-microsoft{background-image:none}
.btn-microsoft.disabled,.btn-microsoft[disabled],fieldset[disabled] .btn-microsoft,.btn-microsoft.disabled:hover,.btn-microsoft[disabled]:hover,fieldset[disabled] .btn-microsoft:hover,.btn-microsoft.disabled:focus,.btn-microsoft[disabled]:focus,fieldset[disabled] .btn-microsoft:focus,.btn-microsoft.disabled:active,.btn-microsoft[disabled]:active,fieldset[disabled] .btn-microsoft:active,.btn-microsoft.disabled.active,.btn-microsoft[disabled].active,fieldset[disabled] .btn-microsoft.active{background-color:#2672ec;border-color:rgba(0,0,0,0.2)}
.btn-microsoft .badge{color:#2672ec;background-color:#fff}
.btn-openid{color:#fff;background-color:#f7931e;border-color:rgba(0,0,0,0.2)}.btn-openid:hover,.btn-openid:focus,.btn-openid:active,.btn-openid.active,.open>.dropdown-toggle.btn-openid{color:#fff;background-color:#da7908;border-color:rgba(0,0,0,0.2)}
.btn-openid:active,.btn-openid.active,.open>.dropdown-toggle.btn-openid{background-image:none}
.btn-openid.disabled,.btn-openid[disabled],fieldset[disabled] .btn-openid,.btn-openid.disabled:hover,.btn-openid[disabled]:hover,fieldset[disabled] .btn-openid:hover,.btn-openid.disabled:focus,.btn-openid[disabled]:focus,fieldset[disabled] .btn-openid:focus,.btn-openid.disabled:active,.btn-openid[disabled]:active,fieldset[disabled] .btn-openid:active,.btn-openid.disabled.active,.btn-openid[disabled].active,fieldset[disabled] .btn-openid.active{background-color:#f7931e;border-color:rgba(0,0,0,0.2)}
.btn-openid .badge{color:#f7931e;background-color:#fff}
.btn-pinterest{color:#fff;background-color:#cb2027;border-color:rgba(0,0,0,0.2)}.btn-pinterest:hover,.btn-pinterest:focus,.btn-pinterest:active,.btn-pinterest.active,.open>.dropdown-toggle.btn-pinterest{color:#fff;background-color:#9f191f;border-color:rgba(0,0,0,0.2)}
.btn-pinterest:active,.btn-pinterest.active,.open>.dropdown-toggle.btn-pinterest{background-image:none}
.btn-pinterest.disabled,.btn-pinterest[disabled],fieldset[disabled] .btn-pinterest,.btn-pinterest.disabled:hover,.btn-pinterest[disabled]:hover,fieldset[disabled] .btn-pinterest:hover,.btn-pinterest.disabled:focus,.btn-pinterest[disabled]:focus,fieldset[disabled] .btn-pinterest:focus,.btn-pinterest.disabled:active,.btn-pinterest[disabled]:active,fieldset[disabled] .btn-pinterest:active,.btn-pinterest.disabled.active,.btn-pinterest[disabled].active,fieldset[disabled] .btn-pinterest.active{background-color:#cb2027;border-color:rgba(0,0,0,0.2)}
.btn-pinterest .badge{color:#cb2027;background-color:#fff}
.btn-reddit{color:#000;background-color:#eff7ff;border-color:rgba(0,0,0,0.2)}.btn-reddit:hover,.btn-reddit:focus,.btn-reddit:active,.btn-reddit.active,.open>.dropdown-toggle.btn-reddit{color:#000;background-color:#bcddff;border-color:rgba(0,0,0,0.2)}
.btn-reddit:active,.btn-reddit.active,.open>.dropdown-toggle.btn-reddit{background-image:none}
.btn-reddit.disabled,.btn-reddit[disabled],fieldset[disabled] .btn-reddit,.btn-reddit.disabled:hover,.btn-reddit[disabled]:hover,fieldset[disabled] .btn-reddit:hover,.btn-reddit.disabled:focus,.btn-reddit[disabled]:focus,fieldset[disabled] .btn-reddit:focus,.btn-reddit.disabled:active,.btn-reddit[disabled]:active,fieldset[disabled] .btn-reddit:active,.btn-reddit.disabled.active,.btn-reddit[disabled].active,fieldset[disabled] .btn-reddit.active{background-color:#eff7ff;border-color:rgba(0,0,0,0.2)}
.btn-reddit .badge{color:#eff7ff;background-color:#000}
.btn-soundcloud{color:#fff;background-color:#f50;border-color:rgba(0,0,0,0.2)}.btn-soundcloud:hover,.btn-soundcloud:focus,.btn-soundcloud:active,.btn-soundcloud.active,.open>.dropdown-toggle.btn-soundcloud{color:#fff;background-color:#c40;border-color:rgba(0,0,0,0.2)}
.btn-soundcloud:active,.btn-soundcloud.active,.open>.dropdown-toggle.btn-soundcloud{background-image:none}
.btn-soundcloud.disabled,.btn-soundcloud[disabled],fieldset[disabled] .btn-soundcloud,.btn-soundcloud.disabled:hover,.btn-soundcloud[disabled]:hover,fieldset[disabled] .btn-soundcloud:hover,.btn-soundcloud.disabled:focus,.btn-soundcloud[disabled]:focus,fieldset[disabled] .btn-soundcloud:focus,.btn-soundcloud.disabled:active,.btn-soundcloud[disabled]:active,fieldset[disabled] .btn-soundcloud:active,.btn-soundcloud.disabled.active,.btn-soundcloud[disabled].active,fieldset[disabled] .btn-soundcloud.active{background-color:#f50;border-color:rgba(0,0,0,0.2)}
.btn-soundcloud .badge{color:#f50;background-color:#fff}
.btn-tumblr{color:#fff;background-color:#2c4762;border-color:rgba(0,0,0,0.2)}.btn-tumblr:hover,.btn-tumblr:focus,.btn-tumblr:active,.btn-tumblr.active,.open>.dropdown-toggle.btn-tumblr{color:#fff;background-color:#1c2d3f;border-color:rgba(0,0,0,0.2)}
.btn-tumblr:active,.btn-tumblr.active,.open>.dropdown-toggle.btn-tumblr{background-image:none}
.btn-tumblr.disabled,.btn-tumblr[disabled],fieldset[disabled] .btn-tumblr,.btn-tumblr.disabled:hover,.btn-tumblr[disabled]:hover,fieldset[disabled] .btn-tumblr:hover,.btn-tumblr.disabled:focus,.btn-tumblr[disabled]:focus,fieldset[disabled] .btn-tumblr:focus,.btn-tumblr.disabled:active,.btn-tumblr[disabled]:active,fieldset[disabled] .btn-tumblr:active,.btn-tumblr.disabled.active,.btn-tumblr[disabled].active,fieldset[disabled] .btn-tumblr.active{background-color:#2c4762;border-color:rgba(0,0,0,0.2)}
.btn-tumblr .badge{color:#2c4762;background-color:#fff}
.btn-twitter{color:#fff;background-color:#55acee;border-color:rgba(0,0,0,0.2)}.btn-twitter:hover,.btn-twitter:focus,.btn-twitter:active,.btn-twitter.active,.open>.dropdown-toggle.btn-twitter{color:#fff;background-color:#2795e9;border-color:rgba(0,0,0,0.2)}
.btn-twitter:active,.btn-twitter.active,.open>.dropdown-toggle.btn-twitter{background-image:none}
.btn-twitter.disabled,.btn-twitter[disabled],fieldset[disabled] .btn-twitter,.btn-twitter.disabled:hover,.btn-twitter[disabled]:hover,fieldset[disabled] .btn-twitter:hover,.btn-twitter.disabled:focus,.btn-twitter[disabled]:focus,fieldset[disabled] .btn-twitter:focus,.btn-twitter.disabled:active,.btn-twitter[disabled]:active,fieldset[disabled] .btn-twitter:active,.btn-twitter.disabled.active,.btn-twitter[disabled].active,fieldset[disabled] .btn-twitter.active{background-color:#55acee;border-color:rgba(0,0,0,0.2)}
.btn-twitter .badge{color:#55acee;background-color:#fff}
.btn-vimeo{color:#fff;background-color:#1ab7ea;border-color:rgba(0,0,0,0.2)}.btn-vimeo:hover,.btn-vimeo:focus,.btn-vimeo:active,.btn-vimeo.active,.open>.dropdown-toggle.btn-vimeo{color:#fff;background-color:#1295bf;border-color:rgba(0,0,0,0.2)}
.btn-vimeo:active,.btn-vimeo.active,.open>.dropdown-toggle.btn-vimeo{background-image:none}
.btn-vimeo.disabled,.btn-vimeo[disabled],fieldset[disabled] .btn-vimeo,.btn-vimeo.disabled:hover,.btn-vimeo[disabled]:hover,fieldset[disabled] .btn-vimeo:hover,.btn-vimeo.disabled:focus,.btn-vimeo[disabled]:focus,fieldset[disabled] .btn-vimeo:focus,.btn-vimeo.disabled:active,.btn-vimeo[disabled]:active,fieldset[disabled] .btn-vimeo:active,.btn-vimeo.disabled.active,.btn-vimeo[disabled].active,fieldset[disabled] .btn-vimeo.active{background-color:#1ab7ea;border-color:rgba(0,0,0,0.2)}
.btn-vimeo .badge{color:#1ab7ea;background-color:#fff}
.btn-vk{color:#fff;background-color:#587ea3;border-color:rgba(0,0,0,0.2)}.btn-vk:hover,.btn-vk:focus,.btn-vk:active,.btn-vk.active,.open>.dropdown-toggle.btn-vk{color:#fff;background-color:#466482;border-color:rgba(0,0,0,0.2)}
.btn-vk:active,.btn-vk.active,.open>.dropdown-toggle.btn-vk{background-image:none}
.btn-vk.disabled,.btn-vk[disabled],fieldset[disabled] .btn-vk,.btn-vk.disabled:hover,.btn-vk[disabled]:hover,fieldset[disabled] .btn-vk:hover,.btn-vk.disabled:focus,.btn-vk[disabled]:focus,fieldset[disabled] .btn-vk:focus,.btn-vk.disabled:active,.btn-vk[disabled]:active,fieldset[disabled] .btn-vk:active,.btn-vk.disabled.active,.btn-vk[disabled].active,fieldset[disabled] .btn-vk.active{background-color:#587ea3;border-color:rgba(0,0,0,0.2)}
.btn-vk .badge{color:#587ea3;background-color:#fff}
.btn-yahoo{color:#fff;background-color:#720e9e;border-color:rgba(0,0,0,0.2)}.btn-yahoo:hover,.btn-yahoo:focus,.btn-yahoo:active,.btn-yahoo.active,.open>.dropdown-toggle.btn-yahoo{color:#fff;background-color:#500a6f;border-color:rgba(0,0,0,0.2)}
.btn-yahoo:active,.btn-yahoo.active,.open>.dropdown-toggle.btn-yahoo{background-image:none}
.btn-yahoo.disabled,.btn-yahoo[disabled],fieldset[disabled] .btn-yahoo,.btn-yahoo.disabled:hover,.btn-yahoo[disabled]:hover,fieldset[disabled] .btn-yahoo:hover,.btn-yahoo.disabled:focus,.btn-yahoo[disabled]:focus,fieldset[disabled] .btn-yahoo:focus,.btn-yahoo.disabled:active,.btn-yahoo[disabled]:active,fieldset[disabled] .btn-yahoo:active,.btn-yahoo.disabled.active,.btn-yahoo[disabled].active,fieldset[disabled] .btn-yahoo.active{background-color:#720e9e;border-color:rgba(0,0,0,0.2)}
.btn-yahoo .badge{color:#720e9e;background-color:#fff}

/* featured buttons */
.btn-featured {
	width: 100%;
	display: inline-block;
	position: relative;
	height:70px;
	padding:0;
	border:0;

	-webkit-transform: translateZ(0);
	transform: translateZ(0);
	box-shadow: 0 0 1px rgba(0, 0, 0, 0);

	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;

	-moz-osx-font-smoothing: grayscale;

	-webkit-transition-duration: 0.3s;
	transition-duration: 0.3s;
	-webkit-transition-property: transform;
	transition-property: transform;
}
.btn-featured.btn-default {
	background-color: #666;
}
.btn-featured:hover,
.btn-featured:focus,
.btn-featured:active {
	-webkit-transform: translateY(-5px);
	transform: translateY(-5px);
	/* move the element up by 5px */
}
.btn-featured:hover:before,
.btn-featured:focus:before,
.btn-featured:active:before {
	opacity: 1;
	-webkit-transform: translateY(5px);
	transform: translateY(5px);
	/* move the element down by 5px (it will stay in place because it's attached to the element that also moves up 5px) */
}

.btn-featured:before {
	pointer-events: none;
	position: absolute;
	z-index: -1;
	content: '';
	top: 100%;
	left: 5%;
	height: 10px;
	width: 90%;
	opacity: 0;
	background: -webkit-radial-gradient(center, ellipse, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0) 80%);
	background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0) 80%);
	/* W3C */
	-webkit-transition-duration: 0.3s;
	transition-duration: 0.3s;
	-webkit-transition-property: "transform, opacity";
	transition-property: "transform, opacity";
}
.btn-featured span {
	float: left;
	height: 70px;
	line-height: 70px;
	text-align: center;
	width: calc(100% - 70px);
	color: #fff;

	overflow:hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
}
.btn-featured i {
	background-color:rgba(0,0,0,0.1);
	float: left;
	width: 70px;
	height: 70px;
	line-height: 70px;
	text-align: center;
	color: #fff;
	font-size: 30px;
	margin:0;
}
.btn-featured.btn-inverse span {
	float:right
}
.btn-featured.btn-inverse i {
	float:right;
}





/**	19. Counters & Countdown
*************************************************** **/
.countTo-sm,
.countTo-md,
.countTo-lg {
	font-family:'Raleway', 'Open Sans', Arial, Helvetica, sans-serif;
}
.countTo-sm span.countTo {
	margin-bottom:20px;
	font-size:25px;
	/*display:block;*/
}
.parallax .countTo-sm i {
	color:#fff;
}
.countTo-sm i {
	font-size:30px;
	margin-bottom:10px;
}

.countTo-md span.countTo {
	margin-bottom:20px;
	font-size:40px;
	display:block;
}
.countTo-md i {
	font-size:50px;
	margin-bottom:10px;
}

.countTo-lg span.countTo {
	margin-bottom:20px;
	font-size:46px;
	display:block;
}
.countTo-lg i {
	font-size:50px;
	margin-bottom:10px;
}

.countTo-sm h4,
.countTo-sm h5,

.countTo-md h4,
.countTo-md h5,

.countTo-lg h4,
.countTo-lg h5 {
	font-weight:300;
}


/* Countdown */
.countdown {
	display: block;
	font-family:'Raleway', 'Open Sans', Arial, Helvetica, sans-serif;
}

.countdown-row {
	display: block;
	position: relative;
	text-align: center;
}

.countdown-section {
	display: inline-block;
	font-size: 11px;
	line-height: 1;
	text-align: center;
	width: 25%;
	border-left: 1px solid rgba(0,0,0,0.1);
	color: #888;
	text-transform: capitalize;
}
section.dark .countdown-section,
.parallax .countdown-section {
	color:#ccc;
	border-left-color:rgba(255,255,255,0.2);
}

.countdown-section:first-child {
	border-left: 0;
}

.countdown-amount {
	display: block;
	font-size: 20px;
	color: #333;
	margin-bottom: 5px;
}
section.dark .countdown-amount,
.parallax .countdown-amount {
	color:#fff;
}

.countdown-descr {
	display: block;
	width: 100%;
}


/* medium */
.countdown-md .countdown-section {
	font-size: 14px;
}
.countdown-md .countdown-amount {
	font-size: 30px;
	margin-bottom: 7px;
}

/* large */
.countdown-lg .countdown-section {
	font-size: 14px;
}

.countdown-lg .countdown-amount {
	font-size: 45px;
	margin-bottom: 7px;
}
/* inline */
.countdown.countdown-inline { display: inline-block; }

.countdown.countdown-inline .countdown-row {
	display: inline-block;
	text-align: center;
}

.countdown.countdown-inline .countdown-section {
	display: inline-block;
	font-size: inherit;
	line-height: inherit;
	width: auto;
	border: none;
	color: inherit;
	margin-left: 7px;
	text-transform: lowercase;
}

.countdown.countdown-inline .countdown-section:first-child { margin-left: 0; }

.countdown.countdown-inline .countdown-amount {
	display: inline-block;
	font-size: inherit;
	color: inherit;
	font-weight: bold;
	margin: 0 3px 0 0;
}

.countdown.countdown-inline .countdown-descr {
	display: inline-block;
	width: auto;
}


/* countdown - slider & footer */
footer .countdown-section,
footer .countdown-amount,
footer .countdown.countdown-inline,
footer .countdown,
#slider .countdown-section,
#slider .countdown-amount,
#slider .countdown.countdown-inline,
#slider .countdown {
	color:#fff;
}
.countdown.squared .countdown-section,
.countdown.circle .countdown-section {
	border-left:0;
}
.countdown.squared .countdown-amount,
.countdown.circle .countdown-amount {
	background-color:rgba(0,0,0,0.3) !important;
	width:100px;
	height:100px;
	line-height:100px;
	font-weight:300;
	font-size:35px;
	color:#fff;

	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;

}
.countdown.dark .countdown-amount {
	background-color:rgba(0,0,0,0.6) !important;
}
.countdown.light .countdown-amount {
	background-color:rgba(255,255,255,0.3) !important;
}

.countdown.squared .countdown-amount {
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
}
.countdown.squared .countdown-section,
.countdown.circle .countdown-section {
	width:100px;
	margin-left:15px;
	font-size:13px;
}

.countdown.bordered-squared .countdown-section,
.countdown.bordered .countdown-section {
	border-left:0;
}
.countdown.bordered-squared .countdown-amount,
.countdown.bordered .countdown-amount {
	border:rgba(0,0,0,0.3) 3px solid !important;
	width:100px;
	height:100px;
	line-height:90px;
	font-weight:300;
	font-size:35px;

	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;

}
.countdown.bordered-squared .countdown-amount {
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
}

.countdown.theme-style .countdown-amount {
	-webkit-border-bottom-right-radius: 40px;
	-webkit-border-top-left-radius: 40px;
	-moz-border-radius-bottomright: 40px;
	-moz-border-radius-topleft: 40px;
	border-bottom-right-radius: 40px;
	border-top-left-radius: 40px;
}


.countdown.bordered-squared .countdown-section,
.countdown.bordered .countdown-section {
	width:100px;
	margin-left:15px;
	font-size:13px;
}

#slider .countdown.bordered .countdown-amount,
footer .countdown.bordered .countdown-amount {
	color:#fff;
}

@media only screen and (max-width: 900px) {
	.countdown-sm .countdown-section,
	.countdown-md .countdown-section,
	.countdown-lg .countdown-section {
		font-size: 12px;
	}
	.countdown-sm .countdown-amount,
	.countdown-md .countdown-amount,
	.countdown-lg .countdown-amount {
		font-size: 20px;
		margin-bottom: 7px;
	}

	footer .countdown-section,
	#slider .countdown-section {
		border-left: 1px dotted rgba(255,255,255,0.3);
	}
	footer .countdown-section:first-child,
	#slider .countdown-section:first-child {
		border-left:0;
	}
	footer .countdown .countdown-amount,
	footer .countdown .countdown-section,
	#slider .countdown .countdown-section,
	#slider .countdown .countdown-amount {
		background-color:transparent !important;
		width:60px;
		height:inherit;
		line-height:inherit;
		font-weight:300;
		font-size:22px;
		text-align:center;
		margin-left:0;

		-webkit-border-radius: 0;
		-moz-border-radius: 0;
		border-radius: 0;
	}
	footer .countdown .countdown-section,
	#slider .countdown .countdown-section {
		font-size:12px;
	}
}





/**	20. Clients
*************************************************** **/
ul.clients-dotted {
	overflow:hidden;
}
ul.clients-dotted>li {
	text-align:center;
	padding-top:30px;
	padding-bottom:30px;
	display:inline-block;
}
ul.clients-dotted>li a {
	text-align:center;
	display:block;
}
ul.clients-dotted>li img {
	display:inline-block;
}
ul.clients-dotted>li:after {
	content: '';
	position: absolute;

	width: 100%;
	height: 0;
	top: auto;
	left: 0;
	bottom: -1px;
	border-bottom: 1px dashed rgba(0,0,0,0.3);
}
ul.clients-dotted>li:before {
	content: '';
	position: absolute;
	height: 100%;
	top: 0;
	left: -1px;
	border-left: 1px dashed rgba(0,0,0,0.3);
}




/**	21. Alerts
*************************************************** **/
div.alert {
	border-left-color:rgba(0,0,0,0.1);
	border-width:1px;
	border-left-width:5px;
}
div.alert-default {
	background-color:rgba(0,0,0,0.1);
}
section.dark .alert-default {
	color:#fff;
	background-color:#111;
}
section.dark .alert-default h1,
section.dark .alert-default h2,
section.dark .alert-default h3,
section.dark .alert-default h4,
section.dark .alert-default h5,
section.dark .alert-default h6,
section.dark .alert-default p {
	color:#fff !important;
}
div.alert.alert-primary,
div.alert.alert-primary a {
	color:#fff;
}
div.alert.alert-bordered-dashed {
	padding:30px 20px;
	border:#ccc 2px dashed;
}
div.alert.alert-bordered-dotted {
	padding:30px 20px;
	border:#ccc 2px dotted;
}
div.alert.alert-bordered {
	padding:30px 20px;
	border:#ccc 2px solid;
}
div.alert.alert-theme-color,
div.alert.alert-dark {
	color:#fff;
	background-color:#333;
	border:0;
	padding:30px 20px;
}
div.alert.alert-theme-color h1,
div.alert.alert-theme-color h2,
div.alert.alert-theme-color h3,
div.alert.alert-theme-color h4,
div.alert.alert-theme-color h5,
div.alert.alert-theme-color h6,
div.alert.alert-dark h1,
div.alert.alert-dark h2,
div.alert.alert-dark h3,
div.alert.alert-dark h4,
div.alert.alert-dark h5,
div.alert.alert-dark h6 {
	color:#fff;
}

section.dark div.alert,
section.dark div.alert h1,
section.dark div.alert h2,
section.dark div.alert h3,
section.dark div.alert h4,
section.dark div.alert h5,
section.dark div.alert h6,
section.dark div.alert p {
	color:#333;
}


section.dark div.callout.alert.alert-border,
section.dark div.callout.alert.alert-border h1,
section.dark div.callout.alert.alert-border h2,
section.dark div.callout.alert.alert-border h3,
section.dark div.callout.alert.alert-border h4,
section.dark div.callout.alert.alert-border h5,
section.dark div.callout.alert.alert-border h6,
section.dark div.callout.alert.alert-border p {
	color:#fff;
}


/* bordered top|botom */
div.alert>.container {
	margin-top:35px;
	margin-bottom:35px;
}
div.alert.bordered-bottom {
	border-bottom:rgba(0,0,0,0.1) 1px solid;
	border-left-width:0;
}
div.alert.bordered-top {
	border-bottom:rgba(0,0,0,0.1) 1px solid;
	border-left-width:0;
}
div.alert.bordered-bottom h4,
div.alert.bordered-top h4,
div.alert.bordered-bottom h3,
div.alert.bordered-top h3 {
	font-weight:400;
	margin:0;
}
div.alert.bordered-bottom p,
div.alert.bordered-top p,
div.alert.bordered-bottom p,
div.alert.bordered-top p {
	margin-top:0;
}

section.dark div.alert.bordered-bottom {
	border-bottom:#666 1px solid;
}
section.dark div.alert.bordered-top {
	border-bottom:#666 1px solid;
}

@media only screen and (max-width: 760px) {
	div.alert.bordered-bottom {
		text-align:center;
	}
	div.alert.bordered-bottom .btn {
		text-align:center;
		display:block;
		margin-top:35px;
	}
}


/* mini alerts */
.alert.alert-mini {
	padding:6px 10px;
	border-left:0;
}



/** 22. Dividers
*************************************************** **/
div.divider {
	margin:40px 0;
	position:relative;
	display:block;
	min-height:20px;
}
div.divider i {
	line-height: 1;
	font-size: 18px;
	color:#ccc;
}

div.divider:after {
	content: '';
	position: absolute;
	top: 8px;
	left:0; right:0;
	height: 0;
	border-top: 1px solid #ddd;
}
section.dark div.divider:after {
	border-top:rgba(255,255,255,0.1) 1px solid;
}
section.dark div.divider:before {
	border-top:rgba(255,255,255,0.1) 1px solid;
}

div.divider.double-line:before {
	content: '';
	position: absolute;
	top: 5px;
	left:0; right:0;
	height: 0;
	border-top: 1px solid #ddd;
}
section.dark div.divider.double-line:after {
	border-top:rgba(255,255,255,0.1) 1px solid;
}
div.divider.divider-left:after {
	left: 30px;
	right: 0;
}
div.divider.divider-left i {
	float:left;
}
div.divider.divider-right:after {
	left: 0;
	right: 30px;
}
div.divider.divider-right i {
	float:right;
}

div.divider.divider-center:after {
	left: 50% !important;
	right: 0;
	margin-left: 20px;
}
div.divider.divider-center:before {
	left: 0 !important;
	right: 50%;
	margin-right: 20px;

	content: '';
	position: absolute;
	top: 8px;
	height: 0;
	border-top: 1px solid #ddd;
}
div.divider.divider-center {
	text-align:center;
}

div.divider.divider-center.divider-short:before {
	left: auto !important;
	right: 50%;
	margin-right: 20px;
	width: 15%;
}
div.divider.divider-center.divider-short:after {
	left: 50% !important;
	right: auto !important;
	margin-left: 20px;
	width: 15%;
}

/* dotted */
div.divider.divider-dotted {
	height:10px;
	border:0;
	background:url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKAQMAAAC3/F3+AAAABlBMVEX///+QkJApn3LQAAAAAnRSTlMAgJsrThgAAAAOSURBVHheYwCCUAdcJAAnnALqo5TBzAAAAABJRU5ErkJggg==') repeat-x center;
}
div.divider.divider-dotted:after {
	display:none;
}


/* color */
div.divider.divider-circle.divider-left:after {
	left:50px;
}
div.divider.divider-circle.divider-right:after {
	right:50px;
}
div.divider.divider-circle.divider-center:after {
	margin-left:30px;
}
div.divider.divider-circle.divider-center:before {
	margin-right:30px;
}
div.divider.divider-circle i {
	width: 40px;
	height: 40px;
	line-height: 40px;
	background-color: #F5F5F5;
	margin-top:-11px;
	text-align:center;
	z-index:1;
	color:#999;

	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
}


/* border */
div.divider.divider-border.divider-left:after {
	left:50px;
}
div.divider.divider-border.divider-right:after {
	right:50px;
}
div.divider.divider-border.divider-center:after {
	margin-left:30px;
}
div.divider.divider-border.divider-center:before {
	margin-right:30px;
}
div.divider.divider-border i {
	width: 40px;
	height: 40px;
	line-height: 40px;
	margin-top:-11px;
	text-align:center;
	z-index:1;
	border:#ddd 1px solid;

	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
}
section.dark div.divider.divider-border i {
	border-color: rgba(255,255,255,0.5);
}

/* scroll to top */
div.divider.divider-border>a>i.fa-chevron-up {
	line-height:36px;
}
div.divider.divider-border>a>i  {
	-webkit-transition: all .300s;
	-moz-transition: all .300s;
	-o-transition: all .300s;
	transition: all .300s;
}
div.divider.divider-border>a:hover>i  {
	color:#333;
}
section.theme-color div.divider.divider-border>a:hover>i.fa-chevron-up {
	color:#fff;
}



/** 23. Headings
*************************************************** **/
div.heading-title {
	position:relative;
	margin-bottom:40px;
}
.heading-title.parallax h1,
.heading-title.parallax h2,
.heading-title.parallax h3,
.heading-title.parallax h4,
.heading-title.parallax h5,
.heading-title.parallax h6 {
	margin:0;
	padding:0;
}
.heading-title.parallax p {
	margin-top:0;
}
div.heading-title h1,
div.heading-title h2,
div.heading-title h3,
div.heading-title h4,
div.heading-title h5,
div.heading-title h6 {
	margin:0;
	padding:0;
	background-color:#fff;
	position:relative;
	display:inline-block;
	padding-left: 0;
	padding-right: 15px;
}
div.heading-title.text-center h1,
div.heading-title.text-center h2,
div.heading-title.text-center h3,
div.heading-title.text-center h4,
div.heading-title.text-center h5,
div.heading-title.text-center h6 {
	padding-left:15px;
	padding-right:15px;
}
div.heading-title.text-right h1,
div.heading-title.text-right h2,
div.heading-title.text-right h3,
div.heading-title.text-right h4,
div.heading-title.text-right h5,
div.heading-title.text-right h6 {
	padding-left:15px;
	padding-right:0;
}
div.heading-title p {
	margin:0;
	padding:0;
	font-weight:300;
}
/* Line : Single|Double */
div.heading-title.heading-line-single:before,
div.heading-title.heading-line-double:before {
	content: '';
	position: absolute;
	width: 100%;
	height: 0;
	left: auto;
	right: 0;
}

div.heading-title.heading-line-single:before {
	top: 54%;
	border-top:#EEE 1px solid;
}
section.dark div.heading-title.heading-line-single:before {
	border-top-color: #666;
}
div.heading-title.heading-line-double:before {
	top: 50%;
	border-top: 3px double #E5E5E5;
}
section.dark div.heading-title.heading-line-double:before {
	border-top-color: #666;
}


/* Border Bottom */
div.heading-title.heading-border-bottom {
	border-bottom:#ccc 2px solid;
}
section.dark div.heading-title.heading-border-bottom {
	border-bottom-color: #666;
}

/* Border Left */
div.heading-title.heading-border {
	padding-left:15px;
	border-left:#ccc 5px solid;
	text-align:left;
}
section.dark div.heading-title.heading-border {
	border-left-color:#666;
}
div.heading-title.heading-border.heading-inverse {
	padding-right:15px;
	border-left:0;
	border-right:#ccc 5px solid;
	text-align:right;
}
section.dark div.heading-title.heading-border.heading-inverse {
	border-right-color:#666;
}

/* Dotted */
div.heading-title.heading-dotted {
	background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKAQMAAAC3/F3+AAAABlBMVEX///+QkJApn3LQAAAAAnRSTlMAgJsrThgAAAAOSURBVHheYwCCUAdcJAAnnALqo5TBzAAAAABJRU5ErkJggg==') repeat-x center;
}
section.alternate div.heading-title h1,
section.alternate div.heading-title h2,
section.alternate div.heading-title h3,
section.alternate div.heading-title h4,
section.alternate div.heading-title h5,
section.alternate div.heading-title h6 {
	background-color:#F9F9F9;
}
section.dark div.heading-title h1,
section.dark div.heading-title h2,
section.dark div.heading-title h3,
section.dark div.heading-title h4,
section.dark div.heading-title h5,
section.dark div.heading-title h6 {
	background-color:#212121;
}
section.dark.alternate div.heading-title h1,
section.dark.alternate div.heading-title h2,
section.dark.alternate div.heading-title h3,
section.dark.alternate div.heading-title h4,
section.dark.alternate div.heading-title h5,
section.dark.alternate div.heading-title h6 {
	background-color:#151515;
}



/* Arrow Heading */
.heading-title.heading-arrow-top,
.heading-title.heading-arrow-bottom {
	color:#fff;
	background-color:#252525;
}
.heading-title.heading-arrow-top h1,
.heading-title.heading-arrow-bottom h1,
.heading-title.heading-arrow-top h2,
.heading-title.heading-arrow-bottom h2,
.heading-title.heading-arrow-top h3,
.heading-title.heading-arrow-bottom h3,
.heading-title.heading-arrow-top h4,
.heading-title.heading-arrow-bottom h4,
.heading-title.heading-arrow-top h5,
.heading-title.heading-arrow-bottom h5,
.heading-title.heading-arrow-top h6,
.heading-title.heading-arrow-bottom h6,
.heading-title.heading-arrow-top p,
.heading-title.heading-arrow-bottom p {
	color:#fff;
	margin:0;
}

.heading-title.heading-arrow-top h1,
.heading-title.heading-arrow-bottom h1 {
	font-size:70px;
	line-height:100px;
}
.heading-title.heading-arrow-top h2,
.heading-title.heading-arrow-bottom h2 {
	font-size:60px;
	line-height:90px;
}
.heading-title.heading-arrow-top h3,
.heading-title.heading-arrow-bottom h3 {
	font-size:50px;
	line-height:80px;
}
@media only screen and (max-width: 760px) {
	.heading-title.heading-arrow-top h1,
	.heading-title.heading-arrow-bottom h1 {
		font-size:55px;
		line-height:85px;
	}
	.heading-title.heading-arrow-top h2,
	.heading-title.heading-arrow-bottom h2 {
		font-size:45px;
		line-height:75px;
	}
	.heading-title.heading-arrow-top h3,
	.heading-title.heading-arrow-bottom h3 {
		font-size:45px;
		line-height:75px;
	}
}
@media only screen and (max-width: 500px) {
	.heading-title.heading-arrow-top h1,
	.heading-title.heading-arrow-bottom h1 {
		font-size:36px;
		line-height:66px;
	}
	.heading-title.heading-arrow-top h2,
	.heading-title.heading-arrow-bottom h2 {
		font-size:30px;
		line-height:60px;
	}
	.heading-title.heading-arrow-top h3,
	.heading-title.heading-arrow-bottom h3 {
		font-size:30px;
		line-height:60px;
	}
}

.heading-title.heading-arrow-bottom:after {
	content: ' ';
	position: absolute;
	width: 0;
	height: 0;
	border-left: 20px solid transparent;
	border-right: 20px solid transparent;
	border-top: 20px solid #252525;
	left: 50%;
	margin-left: -20px;
	bottom: -20px;
}
.heading-title.heading-arrow-top:after {
	content: ' ';
	position: absolute;
	width: 0;
	height: 0;
	border-left: 20px solid transparent;
	border-right: 20px solid transparent;
	border-bottom: 20px solid #252525;
	left: 50%;
	margin-left: -20px;
	top: -20px;
}




/** 24. Word Rotator
*************************************************** **/
.word-rotator {
	visibility: hidden;
	width: 100px;
	height: 0;
	margin-bottom:-11px;
	display: inline-block;
	overflow: hidden;
	text-align: left;
	position: relative;
}
h1 .word-rotator {
	bottom:-4px;
	height: 54px !important;
}
h2 .word-rotator {
	bottom:-1px;
	height: 45px !important;
}
h3 .word-rotator {
	bottom:0px;
	height: 36px !important;
}
h4 .word-rotator {
	bottom:3px;
	height: 27px !important;
}
h5 .word-rotator {
	bottom:5px;
	height: 21px !important;
}
h6 .word-rotator {
	bottom:5px;
	height: 18px !important;
}
p .word-rotator {
	bottom:6px;
	height:21px;
}
p.lead .word-rotator {
	height:29px;
	bottom:4px;
}
.word-rotator.active {
	visibility: visible;
	width: auto;
}
.word-rotator .items {
	position: relative;
	width: 100%;
}
.word-rotator .items span {
	display:block;
	margin-bottom:0;
}

/* Rotator Plugin */
.rotating {
	display: inline-block;
	-webkit-transform-style: preserve-3d;
	-moz-transform-style: preserve-3d;
	-ms-transform-style: preserve-3d;
	-o-transform-style: preserve-3d;
	transform-style: preserve-3d;
	-webkit-transform: rotateX(0) rotateY(0) rotateZ(0);
	-moz-transform: rotateX(0) rotateY(0) rotateZ(0);
	-ms-transform: rotateX(0) rotateY(0) rotateZ(0);
	-o-transform: rotateX(0) rotateY(0) rotateZ(0);
	transform: rotateX(0) rotateY(0) rotateZ(0);
	-webkit-transition: 0.5s;
	-moz-transition: 0.5s;
	-ms-transition: 0.5s;
	-o-transition: 0.5s;
	transition: 0.5s;
	-webkit-transform-origin-x: 50%;
}

.rotating.flip {
	position: relative;
}

.rotating .front, .rotating .back {
	left: 0;
	top: 0;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	-ms-backface-visibility: hidden;
	-o-backface-visibility: hidden;
	backface-visibility: hidden;
}

.rotating .front {
	position: absolute;
	display: inline-block;
	-webkit-transform: translate3d(0,0,1px);
	-moz-transform: translate3d(0,0,1px);
	-ms-transform: translate3d(0,0,1px);
	-o-transform: translate3d(0,0,1px);
	transform: translate3d(0,0,1px);
}

.rotating.flip .front {
	z-index: 1;
}

.rotating .back {
	display: block;
	opacity: 0;
}

.rotating.spin {
	-webkit-transform: rotate(360deg) scale(0);
	-moz-transform: rotate(360deg) scale(0);
	-ms-transform: rotate(360deg) scale(0);
	-o-transform: rotate(360deg) scale(0);
	transform: rotate(360deg) scale(0);
}



.rotating.flip .back {
	z-index: 2;
	display: block;
	opacity: 1;

	-webkit-transform: rotateY(180deg) translate3d(0,0,0);
	-moz-transform: rotateY(180deg) translate3d(0,0,0);
	-ms-transform: rotateY(180deg) translate3d(0,0,0);
	-o-transform: rotateY(180deg) translate3d(0,0,0);
	transform: rotateY(180deg) translate3d(0,0,0);
}

.rotating.flip.up .back {
	-webkit-transform: rotateX(180deg) translate3d(0,0,0);
	-moz-transform: rotateX(180deg) translate3d(0,0,0);
	-ms-transform: rotateX(180deg) translate3d(0,0,0);
	-o-transform: rotateX(180deg) translate3d(0,0,0);
	transform: rotateX(180deg) translate3d(0,0,0);
}

.rotating.flip.cube .front {
	-webkit-transform: translate3d(0,0,100px) scale(0.9,0.9);
	-moz-transform: translate3d(0,0,100px) scale(0.85,0.85);
	-ms-transform: translate3d(0,0,100px) scale(0.85,0.85);
	-o-transform: translate3d(0,0,100px) scale(0.85,0.85);
	transform: translate3d(0,0,100px) scale(0.85,0.85);
}

.rotating.flip.cube .back {
	-webkit-transform: rotateY(180deg) translate3d(0,0,100px) scale(0.9,0.9);
	-moz-transform: rotateY(180deg) translate3d(0,0,100px) scale(0.85,0.85);
	-ms-transform: rotateY(180deg) translate3d(0,0,100px) scale(0.85,0.85);
	-o-transform: rotateY(180deg) translate3d(0,0,100px) scale(0.85,0.85);
	transform: rotateY(180deg) translate3d(0,0,100px) scale(0.85,0.85);
}

.rotating.flip.cube.up .back {
	-webkit-transform: rotateX(180deg) translate3d(0,0,100px) scale(0.9,0.9);
	-moz-transform: rotateX(180deg) translate3d(0,0,100px) scale(0.85,0.85);
	-ms-transform: rotateX(180deg) translate3d(0,0,100px) scale(0.85,0.85);
	-o-transform: rotateX(180deg) translate3d(0,0,100px) scale(0.85,0.85);
	transform: rotateX(180deg) translate3d(0,0,100px) scale(0.85,0.85);
}




/** 25. Icon Boxes
*************************************************** **/
.box-icon {
	margin:30px 0;
}
.box-icon>a,
.box-icon .box-icon-title {
	text-decoration:none !important;
	display:block;
}
.box-icon .box-icon-title>i {
	height: 35px;
	width: 35px;
	line-height: 37px;
	font-size: 18px;
	margin-right:15px;
	background-color:#333;
	text-align:center;
	color:#fff;

	-webkit-transition: all .200s;
	-moz-transition: all .200s;
	-o-transition: all .200s;
	transition: all .200s;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
section.dark .box-icon .box-icon-title>i  {
	background-color:#666;
}
.box-icon .box-icon-title.box-icon-transparent>i {
	color:#414141;
	background-color:transparent;
}
section.dark .box-icon .box-icon-title.box-icon-transparent>i {
	color:#fff;
}
.box-icon.box-icon-left .box-icon-title>i {
	float:left;
}
.box-icon.box-icon-round .box-icon-title>i {
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
}

.box-icon .box-icon-title>h2 {
	font-size:18px;
	line-height:35px;
	margin:0;

	-webkit-transition: all .200s;
	-moz-transition: all .200s;
	-o-transition: all .200s;
	transition: all .200s;
}
.box-icon .box-icon-title>img+h2,
.box-icon .box-icon-title>img+h3,
.box-icon .box-icon-title>img+h4,
.box-icon .box-icon-title>img+h5 {
	font-size:16px;
	line-height:16px;
	margin-top:20px;
}
.box-icon p {
	margin:15px 0;
}
.box-icon a.box-icon-more {
	display:block;
	text-align:right;
	position:relative;
	color:#000;
}
section.dark .box-icon a.box-icon-more {
	color:#fff;
}
.box-icon a.box-icon-more:after {
	font-family: FontAwesome;
	content: "\f105";
	padding-left:10px;
	top:50%;
	margin-top:-2px;
}

.box-icon.box-icon-right .box-icon-title>i {
	float:right;
	margin-right:0;
	margin-left:10px;
}
.box-icon.box-icon-right {
	text-align:right;
}
@media only screen and (max-width: 760px) {
	.box-icon.box-icon-right .box-icon-title>i {
		float:left;
		margin-right:10px;
		margin-left:0;
	}
	.box-icon.box-icon-right {
		text-align:left;
	}
}

/* center */
.box-icon.box-icon-center .box-icon-more,
.box-icon.box-icon-center .box-icon-title,
.box-icon.box-icon-center p {
	text-align:center !important;
}
.box-icon.box-icon-center .box-icon-title>i {
	float:none;
	margin:0 0 10px 0;
}

/* transparent icon */
.box-icon.box-icon-transparent .box-icon-title>i {
	background-color:transparent;
	border:#ccc 1px solid;
	color:#333;
}
section.dark .box-icon.box-icon-transparent .box-icon-title>i {
	color:#fff;
	background-color:#212121;
	border:#555 1px solid;
}

/* large icon */
.box-icon.box-icon-large .box-icon-title>i {
	width: 65px;
	height: 65px;
	line-height: 65px;
	font-size: 24px;
}
.box-icon.box-icon-large a.box-icon-title:hover>i {
	border-color:transparent;
}

/* box content */
.box-icon.box-icon-content {
	background-color:rgba(0,0,0,0.05);
	padding:45px 15px 15px 15px;
	display:block;
	margin-top:33px;

	border-radius: 3px;
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
}
section.dark .box-icon.box-icon-content {
	background-color:rgba(0,0,0,0.1);
}
.box-icon.box-icon-content .box-icon-title>i {
	background-color:#fff;
	top:0;
	margin-left:-33px;
	position:absolute;
}
section.alternate .box-icon.box-icon-content .box-icon-title>i {
	background-color:#F9F9F9;
}


/* box icon side */
.box-icon.box-icon-side {
	position:relative;
	padding-left: 70px;
}
.box-icon.box-icon-side>i {
	position:absolute;
	left:0; top:10px;
	font-size:48px;
}



/* Box Video */
.box-video {
	border:rgba(0,0,0,0.1) 1px solid;
	margin:30px 0;

	border-radius: 3px;
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
}
section.dark .box-video {
	border:rgba(255,255,255,0.1) 1px solid;
}
.box-video .box-video-title {
	text-decoration:none !important;
}
.box-video .box-video-title h2 {
	margin:20px 15px 15px 15px;
	font-size:18px;
	line-height:20px;
}
.box-video p,
.box-video .btn,
.box-video button {
	margin-bottom:15px;
}
.box-video p {
	margin-top:0;
	margin-left:15px;
	margin-right:15px;
}


/* Box Image */
.box-image {
	border:rgba(0,0,0,0.1) 1px solid;
	margin:30px 0;

	border-radius: 3px;
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
}
section.dark .box-image {
	border:rgba(255,255,255,0.1) 1px solid;
}
.box-image .box-image-title {
	text-decoration:none !important;
}
.box-image img {
	margin-bottom:20px;
}
.box-image .box-image-title h2 {
	margin:0 15px 15px 15px;
	font-size:18px;
	line-height:20px;
}
.box-image p,
.box-image .btn,
.box-image button {
	margin-bottom:15px;
}
.box-image p {
	margin-top:0;
	margin-left:15px;
	margin-right:15px;
}




@media only screen and (max-width: 760px) {
	.box-icon {
		margin:60px 0;
	}
	.box-icon.box-icon-content .box-icon-title>i {
		top:-33px;
	}
}


/** FLIP BOXES
 **************** */
.box-flip {
	margin: 0 auto;
	position: relative;
	-webkit-perspective: 600px;
	-moz-perspective: 600px;
	perspective:600;
	width: 100%;

}

.box-flip .front,
.box-flip .back {
	text-align: center;
}

.box-flip .front {
	width: 100%;
	position: absolute;
	top: 0;
	z-index: 1;
	text-align: center;

	-webkit-transform-style: preserve-3d;
	-moz-transform-style: preserve-3d;
	-webkit-transform: rotateX(0deg) rotateY(0deg);
	-moz-transform: rotateX(0deg) rotateY(0deg);
	transform: rotateX(0deg) rotateY(0deg);

	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	-ms-backface-visibility: hidden;
	-o-backface-visibility: hidden;
	backface-visibility: hidden;

	-webkit-transition: all .4s ease-in-out;
	-moz-transition: all .4s ease-in-out;
	-ms-transition: all .4s ease-in-out;
	-o-transition: all .4s ease-in-out;
	transition: all .4s ease-in-out;


}

.box-flip .back {
	width: 100%;
	position: absolute;
	top: 0;
	z-index: 2;

	-webkit-transform-style: preserve-3d;
	-moz-transform-style: preserve-3d;
	-webkit-transform: rotateY(-180deg);
	-moz-transform: rotateY(-180deg);
	-ms-transform: rotateY(-180deg);
	transform: rotateY(-180deg);

	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	-ms-backface-visibility: hidden;
	-o-backface-visibility: hidden;
	backface-visibility: hidden;

	-webkit-transition: all .4s ease-in-out;
	-moz-transition: all .4s ease-in-out;
	-ms-transition: all .4s ease-in-out;
	-o-transition: all .4s ease-in-out;
	transition: all .4s ease-in-out;
}



.box-flip.flip .front {
	width: 100%;
	z-index: 1;

	-webkit-transform: rotateY(-180deg);
	-moz-transform: rotateY(-180deg);
	-ms-transform: rotateY(-180deg);
	-o-transform: rotateY(-180deg);
	transform: rotateY(-180deg);

}
.box-flip.flip .back {
	width: 100%;
	z-index: 2;

	-webkit-transform: rotateY(0deg);
	-moz-transform: rotateY(0deg);
	-ms-transform: rotateY(0deg);
	-o-transform: rotateY(0deg);
	transform: rotateY(0deg);
}
.box-flip .box1 {
	width: 100%;
	background-color:rgba(0,0,0,0.05);
	min-height: 50px;
	margin: 0 auto;
	padding: 20px;
	border-radius: 3px;
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
}
section.dark  .box-flip .box1 {
	background-color:rgba(255,255,255,0.05);
}
.box-flip .box2 {
	width: 100%;
	background-color: #333;
	min-height: 50px;
	margin: 0 auto;
	padding: 20px;
	border-radius: 3px;
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
}
.box-flip hr {
	border-color:rgba(0,0,0,0.1);
}
.box-flip .box-icon-title>i {
	color:#111;
	background-color:rgba(0,0,0,0.07);
}
section.dark .box-flip .box-icon-title>i {
	color:#fff;
}

.box-flip.box-color h1,
.box-flip.box-color h2,
.box-flip.box-color h3,
.box-flip.box-color h4,
.box-flip.box-color h5,
.box-flip.box-color h6,
.box-flip.box-color p,
.box-flip.box-color .box-icon-title>i {
	color:#fff;
}

.box-flip .btn-lg.btn-translucid {
	font-size:14px;
}


.box-flip .box-default,
.box-flip .box-default p,
.box-flip .box-default h1,
.box-flip .box-default h2,
.box-flip .box-default h3,
.box-flip .box-default h4,
.box-flip .box-default h5 {
	color:#000 !important;
}

/* box static */
.box-static {
	padding:15px;
	background-color:rgba(0,0,0,0.05);
	border-top:transparent 3px solid;

	border-radius: 3px;
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
}
.box-static.box-transparent {
	background-color:transparent;
}
.box-static.box-bordered  {
	border:rgba(0,0,0,0.1) 1px solid;
}
.box-static.box-color,
.box-static.box-dark {
	color:#fff;
	background-color:#333;
}
.box-static.box-color h1,
.box-static.box-color h2,
.box-static.box-color h3,
.box-static.box-color h4,
.box-static.box-color h5,
.box-static.box-color h6,
.box-static.box-dark h1,
.box-static.box-dark h2,
.box-static.box-dark h3,
.box-static.box-dark h4,
.box-static.box-dark h5,
.box-static.box-dark h6 {
	color:#fff;
}

.box-static.box-border-top {
	border-top:rgba(0,0,0,0.5) 3px solid;
}
.box-static .box-title {
	margin-bottom:20px;
	border-bottom:rgba(0,0,0,0.1) 1px solid;
}
.box-static.box-color .box-title,
.box-static.box-dark .box-title {
	border-bottom:rgba(255,255,255,0.1) 1px solid;
}
section.dark .box-static.box-bordered  {
	border:rgba(255,255,255,0.1) 1px solid;
}
section.dark .box-static {
	color:#fff;
	padding:15px;
	background-color:#333;
}
section.dark .box-static input,
section.dark .box-static label {
	color:#fff;
}
section.dark .box-static .box-title {
	border-bottom:rgba(255,255,255,0.1) 1px solid;
}

@media all and (max-width: 992px) {
	.box-flip {
		margin-bottom:30px;
	}

}


/** Box Colors */
.box-light {
	padding:15px;
	background-color:rgba(0,0,0,0.05);
}
.box-dark {
	color:#fff;
	padding:15px;
	background-color:#333;
}
.box-dark h1,
.box-dark h2,
.box-dark h3,
.box-dark h4,
.box-dark h5,
.box-dark h6 {
	color:#fff;
}

.box-inner {
	padding:15px;
	display:block;
}
.box-inner h1,
.box-inner h2,
.box-inner h3,
.box-inner h4,
.box-inner h5,
.box-inner h6 {
	font-size:14px;
	line-height:17px;
	padding-bottom:12px;
	border-bottom:rgba(0,0,0,0.1) 1px solid;
	margin-bottom:15px;
}

.box-light .box-inner {
	background-color:#fff;
}
.box-dark .box-inner {
	background-color:#111;
}
.box-footer {
	margin:1px 0;
	padding:8px 15px;
}
.box-light .box-footer {
	background-color:#fff;
}
.box-dark .box-footer {
	background-color:#111;
}
section.dark .box-light {
	background-color:rgba(255,255,255,0.05);
}
section.dark .box-dark {
	background-color:rgba(0,0,0,0.8);
}
section.dark .box-light .box-inner {
	background-color:#111;
}
section.dark .box-dark .box-inner {
	background-color:#111;
}

section.dark .box-light .box-footer {
	background-color:#111;
}
section.dark .box-dark .box-footer {
	background-color:#111;
}
section.dark .box-inner h1,
section.dark .box-inner h2,
section.dark .box-inner h3,
section.dark .box-inner h4,
section.dark .box-inner h5,
section.dark .box-inner h6 {
	border-bottom-color:rgba(255,255,255,0.1);
}
section.dark .box-inner h1>a:hover,
section.dark .box-inner h2>a:hover,
section.dark .box-inner h3>a:hover,
section.dark .box-inner h4>a:hover,
section.dark .box-inner h5>a:hover,
section.dark .box-inner h6>a:hover {
	text-decoration:underline !important;
}

/* successive colored boxes */
.box-gradient {
	color:#fff;
	margin:0;
	text-align:center;
}
.box-gradient>div {
	margin:0;
	padding:50px 8px 30px 8px;

	-webkit-transition: all .400s;
	-moz-transition: all .400s;
	-o-transition: all .400s;
	transition: all .400s;
}
.box-gradient>div>p {
	font-size:15px;
	margin:0;
	height:50px;
	overflow:hidden;
}


.box-gradient h1,
.box-gradient h2,
.box-gradient h3,
.box-gradient h4,
.box-gradient h5,
.box-gradient h6 {
	color:#fff;
	margin-top:10px;
	margin-bottom:20px;
	font-size:50px;
	line-height:50px;
	font-weight:300;
}

/* pink */
.box-pink>div:nth-child(1) {
	background-color:#e2476b;
}
.box-pink>div:nth-child(2) {
	background-color:#e9738f;
}
.box-pink>div:nth-child(3) {
	background-color:#f09fb2;
}
.box-pink>div:nth-child(4) {
	background-color:#f7cbd5;
}

/* blue */
.box-blue>div:nth-child(1) {
	background-color:#004080;
}
.box-blue>div:nth-child(2) {
	background-color:#006fdd;
}
.box-blue>div:nth-child(3) {
	background-color:#2b95ff;
}
.box-blue>div:nth-child(4) {
	background-color:#6cb6ff;
}


/* orange */
.box-orange>div:nth-child(1) {
	background-color:#ea5726;
}
.box-orange>div:nth-child(2) {
	background-color:#ee754d;
}
.box-orange>div:nth-child(3) {
	background-color:#f19272;
}
.box-orange>div:nth-child(4) {
	background-color:#f5b39c;
}


/* yellow */
.box-yellow>div:nth-child(1) {
	background-color:#e3a42d;
}
.box-yellow>div:nth-child(2) {
	background-color:#eaba60;
}
.box-yellow>div:nth-child(3) {
	background-color:#eec882;
}
.box-yellow>div:nth-child(4) {
	background-color:#f2d7a4;
}


/* purple */
.box-purple>div:nth-child(1) {
	background-color:#864699;
}
.box-purple>div:nth-child(2) {
	background-color:#9a50af;
}
.box-purple>div:nth-child(3) {
	background-color:#a96cbb;
}
.box-purple>div:nth-child(4) {
	background-color:#bb89c9;
}


/* red */
.box-red>div:nth-child(1) {
	background-color:#b92c28;
}
.box-red>div:nth-child(2) {
	background-color:#d33834;
}
.box-red>div:nth-child(3) {
	background-color:#d9524f;
}
.box-red>div:nth-child(4) {
	background-color:#e17673;
}


/* brown */
.box-brown>div:nth-child(1) {
	background-color:#633232;
}
.box-brown>div:nth-child(2) {
	background-color:#7b3e3e;
}
.box-brown>div:nth-child(3) {
	background-color:#9d4f4f;
}
.box-brown>div:nth-child(4) {
	background-color:#b36868;
}


/* green */
.box-green>div:nth-child(1) {
	background-color:#0c5849;
}
.box-green>div:nth-child(2) {
	background-color:#117964;
}
.box-green>div:nth-child(3) {
	background-color:#16a387;
}
.box-green>div:nth-child(4) {
	background-color:#1ccaa7;
}


/* black */
.box-black>div:nth-child(1) {
	background-color:#000000;
}
.box-black>div:nth-child(2) {
	background-color:#1d1d1d;
}
.box-black>div:nth-child(3) {
	background-color:#2e2e2e;
}
.box-black>div:nth-child(4) {
	background-color:#454545;
}


/* gray */
.box-gray>div:nth-child(1) {
	background-color:#333333;
}
.box-gray>div:nth-child(2) {
	background-color:#4a4a4a;
}
.box-gray>div:nth-child(3) {
	background-color:#5f5f5f;
}
.box-gray>div:nth-child(4) {
	background-color:#797979;
}


/* teal */
.box-teal>div:nth-child(1) {
	background-color:#426062;
}
.box-teal>div:nth-child(2) {
	background-color:#4f7275;
}
.box-teal>div:nth-child(3) {
	background-color:#618c8f;
}
.box-teal>div:nth-child(4) {
	background-color:#8aadb0;
}


@media all and (max-width: 768px) {
	.box-gradient>div {
		margin:0 !important;
	}
}
@media all and (max-width: 482px) {
	.box-gradient h1,
	.box-gradient h2,
	.box-gradient h3,
	.box-gradient h4,
	.box-gradient h5,
	.box-gradient h6 {
		font-size:36px;
		line-height:36px;
	}
}




/**	26. Labels & Badges
*************************************************** **/
.badge,
.label {
	font-weight:400;
}
.label.label-square {
	font-size:13px;
	width:25px;
	height:25px;
	line-height:25px;
	text-align:center;
	margin-right:10px;
	padding:0;
}
.label.label-square.pull-left {
	margin-left:10px;
}
.label.label-square.pull-right {
	margin-right:10px;
}


h1 .label,
h2 .label,
h3 .label,
h4 .label,
h5 .label,
h6 .label {
	padding:3px 10px;
}
section .nav-pills>li>a,
section .nav-pills>li.active>a:hover,
section .nav-pills>li.active>a {
	color:#111;
}

section.dark .nav-pills>li>a,
section.dark .nav-pills>li.active>a:hover,
section.dark .nav-pills>li.active>a {
	color:#fff;
}

section.dark .nav-pills>li.active>a {
	background-color:rgba(255,255,255,0.3) !important;
}

section.dark a.label,
section.dark .label {
	color:#fff;
}



/* corner */
.badge.badge-corner {
	top: -8px !important;
	right: -6px !important;
	position: absolute !important;
	color:#fff !important;
}

/* colors */
span.badge-default,
span.label-default,
.list-group-item.active>.badge.badge-default {
	background-color:#333 !important;
}

span.label-blue,
span.badge-blue,
.list-group-item.active>.badge {
	background: #3498db !important;
}

span.label-red,
span.badge-red,
.list-group-item.active>.badge {
	background: #e74c3c !important;
}

span.label-green,
span.badge-green,
.list-group-item.active>.badge.badge-green {
	background: #2ecc71 !important;
}

span.label-sea,
span.badge-sea,
.list-group-item.active>.badge.badge-sea {
	background: #1abc9c !important;
}

span.label-orange,
span.badge-orange,
.list-group-item.active>.badge.badge-orange {
	background: #e67e22 !important;
}

span.label-yellow,
span.badge-yellow,
.list-group-item.active>.badge.badge-yellow {
	background: #f1c40f !important;
}

span.label-purple,
span.badge-purple,
.list-group-item.active>.badge.badge-purple {
	background: #9b6bcc !important;
}

span.label-aqua,
span.badge-aqua,
.list-group-item.active>.badge.badge-aqua {
	background: #27d7e7 !important;
}

span.label-brown,
span.badge-brown,
.list-group-item.active>.badge.badge-brown {
	background: #9c8061 !important;
}

span.label-dark-blue,
span.badge-dark-blue,
.list-group-item.active>.badge.badge-dark-blue {
	background: #4765a0 !important;
}

span.label-light-green,
span.badge-light-green,
.list-group-item.active>.badge.badge-light-green {
	background: #79d5b3 !important;
}

span.label-light,
span.badge-light,
.list-group-item.active>.badge.badge-light {
	color: #777;
	background: #ecf0f1 !important;
}

span.label-dark,
span.badge-dark,
.list-group-item.active>.badge.badge-dark {
	background: #555 !important;
}



/** 27. Lightbox Ajax [Magnific Popup]
*************************************************** **/
.lightbox-ajax {
	position: relative;
	background-color: #FFF;
	width:100%;
	max-width: 800px;
	margin: 0 auto;
}
.lightbox-ajax .lightbox-ajax-body {
	padding:20px;
}

.lightbox-ajax >h1,
.lightbox-ajax >h2,
.lightbox-ajax >h3,
.lightbox-ajax >h4,
.lightbox-ajax >h5,
.lightbox-ajax >h6 {
	background-color: #F9F9F9;
	border-bottom: 1px solid #EEE;
	padding: 20px 30px;
	margin:0;
}

@media all and (max-width: 992px) {
	.lightbox-ajax .lightbox-ajax-body .row>div {
		margin-bottom:30px;
	}
}



/** 28. Panels
*************************************************** **/
.panel {
	margin-bottom:30px;
}
section.dark .panel-default>.panel-heading {
	border-color:#666;
}
.panel-footer .social-icon {
	margin-top:0;
	margin-bottom:0;
}

.panel-heading .btn,
.panel-footer .btn {
	margin:0;
}

.panel-footer.panel-footer-transparent,
.panel-heading.panel-heading-transparent {
	background-color:transparent;
}

section.dark .panel  {
	background-color:#373737;
	border-color:#666;
}
section.dark .panel  .btn {
	color:#fff!important;
}
section.dark .panel-footer {
	border-top-color:rgba(255,255,255,0.1);
	background-color:rgba(255,255,255,0.1);
}
.panel .table {
	background-color:transparent;
}
section.dark table {
	color:#fff;
	background-color:#373737;
}
section.dark .panel .panel-heading,
section.dark .panel .panel-heading h2 {
	color:#111 !important;
}
section.dark .panel .panel-heading.panel-heading-transparent,
section.dark .panel .panel-heading.panel-heading-transparent h2 {
	color:#eaeaea !important;
}
.panel table thead {
	background-color:rgba(0,0,0,0.01);
}
section.dark .table>thead>tr>th {
	border-bottom-color:#666;
}

section.dark .panel>.panel-body+.table,
section.dark .panel>.panel-body+.table-responsive,
section.dark .panel>.table+.panel-body,
section.dark .panel>.table-responsive+.panel-body,
section.dark .table>tbody>tr>td,
section.dark .table>tbody>tr>th,
section.dark .table>tfoot>tr>td,
section.dark .table>tfoot>tr>th,
section.dark .table>thead>tr>td,
section.dark .table>thead>tr>th {
	border-top-color:#666;
}
section.dark .table>tbody>tr:hover>td {
	color:#000;
}
section.dark .table-striped>tbody>tr:nth-of-type(odd) {
	color:#000;
}



/** 29. Modals
*************************************************** **/
.modal-content {
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	border-radius:3px;

	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
}
.modal-header .btn,
.modal-footer .btn {
	margin:0;
}
.modal-full {
	width:100% !important;
	margin-left:8px;
}




/** 30. Toastr
*************************************************** **/
.toast-title {
	font-weight: bold;
}
.toast-message {
	-ms-word-wrap: break-word;
	word-wrap: break-word;
}
.toast-message a,
.toast-message label {
	color: #ffffff;
}
.toast-message a:hover {
	color: #cccccc;
	text-decoration: none;
}
.toast-close-button {
	position: relative;
	right: -0.3em;
	top: -0.3em;
	float: right;
	font-size: 20px;
	font-weight: bold;
	color: #ffffff;
	opacity: 0.8;
	-ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=80);
	filter: alpha(opacity=80);
}
.toast-close-button:hover,
.toast-close-button:focus {
	color: #000000;
	text-decoration: none;
	cursor: pointer;
	opacity: 0.4;
	-ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=40);
	filter: alpha(opacity=40);
}
/*Additional properties for button version
 iOS requires the button element instead of an anchor tag.
 If you want the anchor version, it requires `href="#"`.*/
button.toast-close-button {
	padding: 0;
	cursor: pointer;
	background: transparent;
	border: 0;
	-webkit-appearance: none;
}
.toast-top-center {
	top: 0;
	right: 0;
	width: 100%;
}
.toast-bottom-center {
	bottom: 0;
	right: 0;
	width: 100%;
}
.toast-top-full-width {
	top: 0;
	right: 0;
	width: 100%;
}
.toast-bottom-full-width {
	bottom: 0;
	right: 0;
	width: 100%;
}
.toast-top-left {
	top: 12px;
	left: 12px;
}
.toast-top-right {
	top: 12px;
	right: 12px;
}
.toast-bottom-right {
	right: 12px;
	bottom: 12px;
}
.toast-bottom-left {
	bottom: 12px;
	left: 12px;
}
#toast-container {
	position: fixed;
	z-index: 999999;
	/*overrides*/

}
#toast-container * {
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}
#toast-container > div {
	position: relative;
	overflow: hidden;
	margin: 0 0 6px;
	padding: 15px 15px 15px 50px;
	width: 300px;
	-moz-border-radius: 3px 3px 3px 3px;
	-webkit-border-radius: 3px 3px 3px 3px;
	border-radius: 3px 3px 3px 3px;
	background-position: 15px center;
	background-repeat: no-repeat;
	color: #ffffff;
	opacity: 0.8;
	-ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=80);
	filter: alpha(opacity=80);
}
#toast-container > :hover {
	opacity: 1;
	-ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
	filter: alpha(opacity=100);
	cursor: pointer;
}
#toast-container > .toast-info {
	background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhLtZa9SgNBEMc9sUxxRcoUKSzSWIhXpFMhhYWFhaBg4yPYiWCXZxBLERsLRS3EQkEfwCKdjWJAwSKCgoKCcudv4O5YLrt7EzgXhiU3/4+b2ckmwVjJSpKkQ6wAi4gwhT+z3wRBcEz0yjSseUTrcRyfsHsXmD0AmbHOC9Ii8VImnuXBPglHpQ5wwSVM7sNnTG7Za4JwDdCjxyAiH3nyA2mtaTJufiDZ5dCaqlItILh1NHatfN5skvjx9Z38m69CgzuXmZgVrPIGE763Jx9qKsRozWYw6xOHdER+nn2KkO+Bb+UV5CBN6WC6QtBgbRVozrahAbmm6HtUsgtPC19tFdxXZYBOfkbmFJ1VaHA1VAHjd0pp70oTZzvR+EVrx2Ygfdsq6eu55BHYR8hlcki+n+kERUFG8BrA0BwjeAv2M8WLQBtcy+SD6fNsmnB3AlBLrgTtVW1c2QN4bVWLATaIS60J2Du5y1TiJgjSBvFVZgTmwCU+dAZFoPxGEEs8nyHC9Bwe2GvEJv2WXZb0vjdyFT4Cxk3e/kIqlOGoVLwwPevpYHT+00T+hWwXDf4AJAOUqWcDhbwAAAAASUVORK5CYII=") !important;
}
#toast-container > .toast-error {
	background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHOSURBVEhLrZa/SgNBEMZzh0WKCClSCKaIYOED+AAKeQQLG8HWztLCImBrYadgIdY+gIKNYkBFSwu7CAoqCgkkoGBI/E28PdbLZmeDLgzZzcx83/zZ2SSXC1j9fr+I1Hq93g2yxH4iwM1vkoBWAdxCmpzTxfkN2RcyZNaHFIkSo10+8kgxkXIURV5HGxTmFuc75B2RfQkpxHG8aAgaAFa0tAHqYFfQ7Iwe2yhODk8+J4C7yAoRTWI3w/4klGRgR4lO7Rpn9+gvMyWp+uxFh8+H+ARlgN1nJuJuQAYvNkEnwGFck18Er4q3egEc/oO+mhLdKgRyhdNFiacC0rlOCbhNVz4H9FnAYgDBvU3QIioZlJFLJtsoHYRDfiZoUyIxqCtRpVlANq0EU4dApjrtgezPFad5S19Wgjkc0hNVnuF4HjVA6C7QrSIbylB+oZe3aHgBsqlNqKYH48jXyJKMuAbiyVJ8KzaB3eRc0pg9VwQ4niFryI68qiOi3AbjwdsfnAtk0bCjTLJKr6mrD9g8iq/S/B81hguOMlQTnVyG40wAcjnmgsCNESDrjme7wfftP4P7SP4N3CJZdvzoNyGq2c/HWOXJGsvVg+RA/k2MC/wN6I2YA2Pt8GkAAAAASUVORK5CYII=") !important;
}
#toast-container > .toast-success {
	background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADsSURBVEhLY2AYBfQMgf///3P8+/evAIgvA/FsIF+BavYDDWMBGroaSMMBiE8VC7AZDrIFaMFnii3AZTjUgsUUWUDA8OdAH6iQbQEhw4HyGsPEcKBXBIC4ARhex4G4BsjmweU1soIFaGg/WtoFZRIZdEvIMhxkCCjXIVsATV6gFGACs4Rsw0EGgIIH3QJYJgHSARQZDrWAB+jawzgs+Q2UO49D7jnRSRGoEFRILcdmEMWGI0cm0JJ2QpYA1RDvcmzJEWhABhD/pqrL0S0CWuABKgnRki9lLseS7g2AlqwHWQSKH4oKLrILpRGhEQCw2LiRUIa4lwAAAABJRU5ErkJggg==") !important;
}
#toast-container > .toast-warning {
	background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGYSURBVEhL5ZSvTsNQFMbXZGICMYGYmJhAQIJAICYQPAACiSDB8AiICQQJT4CqQEwgJvYASAQCiZiYmJhAIBATCARJy+9rTsldd8sKu1M0+dLb057v6/lbq/2rK0mS/TRNj9cWNAKPYIJII7gIxCcQ51cvqID+GIEX8ASG4B1bK5gIZFeQfoJdEXOfgX4QAQg7kH2A65yQ87lyxb27sggkAzAuFhbbg1K2kgCkB1bVwyIR9m2L7PRPIhDUIXgGtyKw575yz3lTNs6X4JXnjV+LKM/m3MydnTbtOKIjtz6VhCBq4vSm3ncdrD2lk0VgUXSVKjVDJXJzijW1RQdsU7F77He8u68koNZTz8Oz5yGa6J3H3lZ0xYgXBK2QymlWWA+RWnYhskLBv2vmE+hBMCtbA7KX5drWyRT/2JsqZ2IvfB9Y4bWDNMFbJRFmC9E74SoS0CqulwjkC0+5bpcV1CZ8NMej4pjy0U+doDQsGyo1hzVJttIjhQ7GnBtRFN1UarUlH8F3xict+HY07rEzoUGPlWcjRFRr4/gChZgc3ZL2d8oAAAAASUVORK5CYII=") !important;
}
#toast-container.toast-top-center > div,
#toast-container.toast-bottom-center > div {
	width: 300px;
	margin: auto;
}
#toast-container.toast-top-full-width > div,
#toast-container.toast-bottom-full-width > div {
	width: 96%;
	margin: auto;
}
.toast {
	background-color: #030303;
}
#toast-container .toast-primary {
	padding:15px;
}
.toast-primary {
	border:0;
	background-color: #333;
}
.toast-success {
	background-color: #51a351;
}
.toast-error {
	background-color: #bd362f;
}
.toast-info {
	background-color: #2f96b4;
}
.toast-warning {
	background-color: #f89406;
}
.toast-progress {
	position: absolute;
	left: 0;
	bottom: 0;
	height: 2px;
	background-color: #000000;
	opacity: 0.4;
	-ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=40);
	filter: alpha(opacity=40);
}
/*Responsive Design*/
@media all and (max-width: 240px) {
	#toast-container > div {
		padding: 8px 8px 8px 50px;
		width: 11em;
	}
	#toast-container .toast-close-button {
		right: -0.2em;
		top: -0.2em;
	}
}
@media all and (min-width: 241px) and (max-width: 480px) {
	#toast-container > div {
		padding: 8px 8px 8px 50px;
		width: 18em;
	}
	#toast-container .toast-close-button {
		right: -0.2em;
		top: -0.2em;
	}
}
@media all and (min-width: 481px) and (max-width: 768px) {
	#toast-container > div {
		padding: 15px 15px 15px 50px;
		width: 25em;
	}
}






/** 31. Navigations
*************************************************** **/
.navbar-primary .navbar-brand,
.navbar-primary .navbar-nav>li>a,
.navbar-primary button,
.navbar-primary a {
	color:#ddd;
}
.navbar-primary .navbar-brand:hover,
.navbar-primary .navbar-nav>li>a:hover,
.navbar-primary button:hover,
.navbar-primary a:hover {
	color:#fff;
}
.navbar-primary .navbar-nav>.active>a,
.navbar-primary .navbar-nav>.active>a:focus,
.navbar-primary .navbar-nav>.active>a:hover {
	background-color:rgba(0,0,0,0.1);
}


section.dark .navbar-default .navbar-brand,
section.dark .navbar-default .navbar-nav>li>a {
	color:#999;
}
section.dark .navbar-default .navbar-brand:hover,
section.dark .navbar-default .navbar-nav>li>a:hover {
	color:#ddd;
}

section.dark .navbar-default .navbar-nav>.open>a,
section.dark .navbar-default .navbar-nav>.open>a:focus,
section.dark .navbar-default .navbar-nav>.open>a:hover,
section.dark .navbar-default .navbar-nav>.active>a,
section.dark .navbar-default .navbar-nav>.active>a:focus,
section.dark .navbar-default .navbar-nav>.active>a:hover {
	color:#ddd;
	background-color:rgba(0,0,0,0.2);
}

section.dark .navbar-default {
	background-color:#555;
	border-color:#555;
}
section.dark .navbar-inverse {
	background-color:#111;
	border-color:#111;
}

section.dark .nav-tabs>li.active>a,
section.dark .nav-tabs>li.active>a:focus,
section.dark .nav-tabs>li.active>a:hover {
	color:#ddd;
	background-color:#555;
	border-color:#555;
}
section.dark .nav-tabs {
	border-color:#555;
}
section.dark .nav-tabs>li>a:hover {
	border-color:transparent;
	background-color:#555;
}


/* small categories - like tags */
ul.categories>li {
	margin:0;
	padding:1px;
}
ul.categories>li>a {
	letter-spacing:0;
	font-size:12px;
	color:#999;
}
ul.categories>li>a:hover {
	color:#121212;
}
ul.categories>li:after {
	content:' , ';
}
ul.categories>li:last-child:after {
	content:'';
}
section.dark ul.categories>li>a:hover {
	color:#ccc;
}




/** 32. Paginations
*************************************************** **/
.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
	background: rgba(0,0,0,0.05);
}
.pagination > li > a {
	margin-right:4px;
	color:#666 !important;

	-webkit-border-radius: 3px !important;
	-moz-border-radius: 3px !important;
	border-radius: 3px !important;
}

.pagination > li.active>a {
	border-color:#ddd;
	color:#fff !important;
}

section.dark .pagination > li,
section.dark .pagination > li > a {
	color:#ccc !important;
	border-color:#666;
	background-color:transparent;
}
section.dark .pagination > li > a:hover,
section.dark .pagination > li > span:hover {
	background-color:#555;
}
section.dark .pagination > li.active>a {
	color:#fff !important;
}
/* pager */
section.dark .pager li>a,
section.dark .pager li>span {
	background-color:#666;
	border-color:#888;
	color:#fff;
}
section.dark .pager li>a:hover {
	background-color:#555;
}

/* simple pagination */
.pagination.pagination-simple>li>a {
	border:0 !important;
	border-left:#ccc 1px solid !important;
	background-color:transparent !important;
	color:#333 !important;
	padding: 0 12px !important;
	font-weight:bold !important;

	-webkit-border-radius: 0 !important;
	-moz-border-radius: 0 !important;
	border-radius: 0 !important;
}
.pagination.pagination-simple>li.active>a {
	color:#999 !important;
}
.pagination.pagination-simple>li:first-child>a {
	border:0 !important;
}
section.dark .pagination.pagination-simple>li>a {
	border-left:#666 1px solid !important;
	color:#888 !important;
}
section.dark .pagination.pagination-simple>li.active>a {
	color:#eee !important;
}
section.dark .pagination.pagination-simple>li:first-child>a {
	border:0 !important;
}


/** 33. Tables
*************************************************** **/
.responsive-utilities td.is-visible {
	color: #468847;
	background-color: #dff0d8!important;
}


/** 34. Callouts
*************************************************** **/
.callout.alert {
	padding:20px 0;
	color:#111;
	margin-bottom:0;
}
section.callout.alert {
	padding:40px 0;
}
.callout.alert h1,
.callout.alert h2,
.callout.alert h3,
.callout.alert h4,
.callout.alert h5,
.callout.alert h6 {
	color:#111;
	margin-bottom: 3px;
	font-weight:500;
}
.callout.alert p:last-child {
	margin: 0;
}
.callout.alert .btn {
	margin:2px 0 0 0;
}
.callout.alert-border {
	border:rgba(0,0,0,0.1) 2px solid;
}

.callout-box {
	clear: both;
	position: relative;
	overflow: hidden;
	background: #ddd;
	padding: 36px 0;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
.callout-box h1,
.callout-box h2,
.callout-box h3,
.callout-box h4,
.callout-box h5,
.callout-box h6 {
	margin-bottom:0;
}

.callout-dark {
	z-index:10;
	position:relative;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
.callout-dark h1,
.callout-dark h2,
.callout-dark h3,
.callout-dark h4,
.callout-dark h5,
.callout-dark h6 {
	/**
		margin-bottom: 20px !important;
	**/
}
.callout-dark p {
	color: #b1b1b1 !important;
	font-size: 17px !important;
	max-width: 960px !important;
	margin: auto !important;
}
.callout-dark a.social-icon,
.callout-dark .btn {
	color:#fff;
}
.callout-dark.heading-title {
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
}

.callout .row {
	margin-left: 0;
	margin-right: 0;
}

.callout {
	padding: 30px 0;
	color:#fff;

	-webkit-transition: all .400s;
	-moz-transition: all .400s;
	-o-transition: all .400s;
	transition: all .400s;
}

.callout h1,
.callout h2,
.callout h3,
.callout h4,
.callout h5,
.callout h6 {
	color:#fff;
	margin:0 !important;
	font-weight:300;
}
.callout p {
	margin: 0;
	padding: 0;
	font-size: 16px;
	font-weight: 300;
}
.callout .btn {
	margin:6px 0 0 0;
}
.callout.callout-theme-color .btn,
.callout.callout-dark .btn {
	background-color:rgba(255,255,255,0.2);
	border-color:rgba(255,255,255,0.2);
}
.callout.callout-theme-color .btn:hover,
.callout.callout-dark .btn:hover {
	background-color:rgba(255,255,255,0.3);
	border-color:rgba(255,255,255,0.3);
}
.callout.callout-dark {
	background-color:#252525;
}
.callout.callout-hover:hover {
	opacity: 0.85;
	filter: alpha(opacity=85);
}


.callout.callout-transparent,
.callout.callout-transparent h1,
.callout.callout-transparent h2,
.callout.callout-transparent h3,
.callout.callout-transparent h4,
.callout.callout-transparent h5,
.callout.callout-transparent h6,
.callout.callout-transparent p {
	color:#111;
	margin:0 !important;
	font-weight:300;
}

/* dark */
section.dark .callout,
section.dark .callout p,
section.dark .callout.alert {
	color:#111;
}



section.dark .callout h1,
section.dark .callout h2,
section.dark .callout h3,
section.dark .callout h4,
section.dark .callout h5,
section.dark .callout h6,
section.dark .callout p {
	color:#111;
}
section.dark .callout.alert-bolder,
section.dark .callout.alert-bolder h1,
section.dark .callout.alert-bolder h2,
section.dark .callout.alert-bolder h3,
section.dark .callout.alert-bolder h4,
section.dark .callout.alert-bolder h5,
section.dark .callout.alert-bolder h6,
section.dark .callout.alert-bolder p {
	color:#111;
}
section.dark .alert-border,
section.dark .alert-border h1,
section.dark .alert-border h2,
section.dark .alert-border h3,
section.dark .alert-border h4,
section.dark .alert-border h5,
section.dark .alert-border h6,
section.dark .alert-border p,
section.dark .callout .btn {
	color:#fff;
}
section.dark .alert-transparent {
	border-left-color:#444;
}
section.dark .alert-transparent,
section.dark .alert-transparent h1,
section.dark .alert-transparent h2,
section.dark .alert-transparent h3,
section.dark .alert-transparent h4,
section.dark .alert-transparent h5,
section.dark .alert-transparent h6,
section.dark .alert-transparent p,
section.dark .callout .btn {
	color:#fff;
}
section.dark .callout.alert-border {
	border-color:#666;
}

section.dark .callout-box.callout-default,
section.dark .callout-box.callout-default h1,
section.dark .callout-box.callout-default h2,
section.dark .callout-box.callout-default h3,
section.dark .callout-box.callout-default h4,
section.dark .callout-box.callout-default h5,
section.dark .callout-box.callout-default h6,
section.dark .callout-box.callout-default p {
	color:#111;
}
section.dark .callout-box .btn {
	color:#fff;
}

section.dark .callout-theme-color,
section.dark .callout-theme-color h1,
section.dark .callout-theme-color h2,
section.dark .callout-theme-color h3,
section.dark .callout-theme-color h4,
section.dark .callout-theme-color h5,
section.dark .callout-theme-color h6,
section.dark .callout-theme-color p {
	color:#fff;
}

section.dark .callout-dark {
	background-color:#111;
}
section.dark .callout-dark,
section.dark .callout-dark h1,
section.dark .callout-dark h2,
section.dark .callout-dark h3,
section.dark .callout-dark h4,
section.dark .callout-dark h5,
section.dark .callout-dark h6,
section.dark .callout-dark p {
	color:#fff;
}
section.dark .callout-transparent,
section.dark .callout-transparent h1,
section.dark .callout-transparent h2,
section.dark .callout-transparent h3,
section.dark .callout-transparent h4,
section.dark .callout-transparent h5,
section.dark .callout-transparent h6,
section.dark .callout-transparent p {
	color:#fff;
}

@media only screen and (max-width: 960px) {
	.callout a.social-icon {
		margin-top:30px;
		float:none !important;
	}
	.callout div.text-right {
		text-align:left;
	}
	.callout-box.callout-default .btn,
	.callout .btn {
		display:block;
		margin-top:30px !important;
	}

	.callout.alert,
	.callout-box.callout-default {
		padding:20px 15px;
	}
}

@media only screen and (max-width: 480px) {
	.callout.callout-dark,
	.callout.callout-theme-color,
	.callout div.text-left,
	.callout div.text-right {
		text-align:center !important;
	}
	.callout h1,
	.callout h2,
	.callout h3,
	.callout h4,
	.callout h5,
	.callout h6 {
		margin-bottom:30px !important;
	}
	.callout .btn {
		display:block;
		margin-top:30px !important;
	}

}


/* Info Bar */
.info-bar {
	margin:0;
	border:0;
	color:#000;
	background-color:rgba(0,0,0,0.05);
	padding:10px 0;
}
.info-bar div.row>div {
	padding-top:20px;
	padding-bottom:20px;
	margin:0 !important;
	border-right:rgba(0,0,0,0.1) 1px solid;
}
.info-bar div.row>div:last-child {
	border:0;
}
.info-bar div.row>div i {
	color:#333;
	font-size:32px;
	line-height: 1.2;
	margin-right:10px;
	float:left;
}
.info-bar h1,
.info-bar h2,
.info-bar h3,
.info-bar h4,
.info-bar h5,
.info-bar h6,
.info-bar p {
	color:#333;
	font-size:16px;
	line-height:1.5;
	margin:0;
	padding:0;
}
.info-bar p {
	font-size: 12px;
	line-height:1;
}

/* dark */
.info-bar.info-bar-dark {
	background-color:#171717;
}
.info-bar.info-bar-dark a,
.info-bar.info-bar-dark div.row>div i,
.info-bar.info-bar-dark h1,
.info-bar.info-bar-dark h2,
.info-bar.info-bar-dark h3,
.info-bar.info-bar-dark h4,
.info-bar.info-bar-dark h5,
.info-bar.info-bar-dark h6,
.info-bar.info-bar-dark p {
	color:#fff;
}
.info-bar.info-bar-dark div.row>div {
	border-right-color:rgba(255,255,255,0.1);
}

/* color */
.info-bar.info-bar-color a,
.info-bar.info-bar-color div.row>div i,
.info-bar.info-bar-color h1,
.info-bar.info-bar-color h2,
.info-bar.info-bar-color h3,
.info-bar.info-bar-color h4,
.info-bar.info-bar-color h5,
.info-bar.info-bar-color h6,
.info-bar.info-bar-color p {
	color:#fff;
}
.info-bar.info-bar-color div.row>div {
	border-right-color:rgba(0,0,0,0.1);
}

/* clean */
.info-bar.info-bar-clean {
	background-color:#fff;
	border-bottom: rgba(0,0,0,0.1) 1px solid;
}
section.dark .info-bar-light {
	background-color:#fff;
}
section.dark .info-bar-dark {
	background-color:#313131;
}
/* bordered */
.info-bar.info-bar-bordered {
	margin:30px 0;
	border: rgba(0,0,0,0.1) 1px solid;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
.info-bar.info-bar-bordered div.row>div {
	padding-top:15px;
	padding-bottom:15px;
}

section.dark .info-bar.info-bar-bordered {
	border-color: rgba(255,255,255,0.1);
}
section.dark .info-bar div.row>div {
	border-right-color:rgba(255,255,255,0.1);
}
section.dark .info-bar div.row>div i {
	color:#fff;
}
section.dark .info-bar.info-bar-color div.row>div {
	border-right-color:rgba(255,255,255,0.2);
}


/** 35. Process Steps
*************************************************** **/
.process-wizard {padding: 0 0 10px 0;}
.process-wizard > .process-wizard-step {padding: 0; position: relative;}
.process-wizard > .process-wizard-step + .process-wizard-step {}
.process-wizard > .process-wizard-step .process-wizard-stepnum {color: #595959; font-size: 16px; margin-bottom: 5px;}
.process-wizard > .process-wizard-step .process-wizard-info {color: #999; font-size: 14px;}
.process-wizard > .process-wizard-step > .process-wizard-dot {position: absolute; width: 30px; height: 30px; display: block; background: #fbe8aa; top: 47px; left: 50%; margin-top: -15px; margin-left: -15px; border-radius: 50%;}
.process-wizard > .process-wizard-step > .process-wizard-dot:after {content: ' '; width: 14px; height: 14px; background: #fbbd19; border-radius: 50px; position: absolute; top: 8px; left: 8px; }
.process-wizard > .process-wizard-step > .progress {position: relative; border-radius: 0px; height: 8px; box-shadow: none; margin: 20px 0;}
.process-wizard > .process-wizard-step > .progress > .progress-bar {width:0px; box-shadow: none; background: #fbe8aa;}
.process-wizard > .process-wizard-step.complete > .progress > .progress-bar {width:100%;}
.process-wizard > .process-wizard-step.active > .progress > .progress-bar {width:50%;}
.process-wizard > .process-wizard-step:first-child.active > .progress > .progress-bar {width:0%;}
.process-wizard > .process-wizard-step:last-child.active > .progress > .progress-bar {width: 100%;}
.process-wizard > .process-wizard-step.disabled > .process-wizard-dot {background-color: #f5f5f5;}
.process-wizard > .process-wizard-step.disabled > .process-wizard-dot:after {opacity: 0;}
.process-wizard > .process-wizard-step:first-child  > .progress {left: 50%; width: 50%;}
.process-wizard > .process-wizard-step:last-child  > .progress {width: 50%;}
.process-wizard > .process-wizard-step.disabled a.process-wizard-dot{ pointer-events: none; }

/* default */
.process-wizard-default > .process-wizard-step > .process-wizard-dot,
.process-wizard-default > .process-wizard-step > .progress > .progress-bar {
	background:#ccc;
}
.process-wizard-default > .process-wizard-step > .process-wizard-dot:after {
	background-color:#666;
}

/* info */
.process-wizard-info > .process-wizard-step > .process-wizard-dot,
.process-wizard-info > .process-wizard-step > .progress > .progress-bar {
	background:#d9edf7;
}
.process-wizard-info > .process-wizard-step > .process-wizard-dot:after {
	background-color:#31708f;
}

/* warning */
.process-wizard-warning > .process-wizard-step > .process-wizard-dot,
.process-wizard-warning > .process-wizard-step > .progress > .progress-bar {
	background:#fbe8aa;
}
.process-wizard-warning > .process-wizard-step > .process-wizard-dot:after {
	background-color:#fbbd19;
}

/* success */
.process-wizard-success > .process-wizard-step > .process-wizard-dot,
.process-wizard-success > .process-wizard-step > .progress > .progress-bar {
	background:#d6e9c6;
}
.process-wizard-success > .process-wizard-step > .process-wizard-dot:after {
	background-color:#3c763d;
}

/* success */
.process-wizard-danger > .process-wizard-step > .process-wizard-dot,
.process-wizard-danger > .process-wizard-step > .progress > .progress-bar {
	background:#ebccd1;
}
.process-wizard-danger > .process-wizard-step > .process-wizard-dot:after {
	background-color:#a94442;
}


/* Tab Process Steps */
ul.process-steps,
ul.process-steps li {
	border:0 !important;
	text-align: center;
}
ul.process-steps li a {
	width:50px;
	height:50px;
	font-size:30px;
	line-height:30px;
	text-align: center;
	display:inline-block;
	color:#111;
	border:#666 1px solid !important;
	background-color:#fff;

	-webkit-border-radius: 50% !important;
	-moz-border-radius: 50% !important;
	border-radius: 50% !important;
}

ul.process-steps li.active a,
ul.process-steps li.active:hover>a {
	color:#fff !important;
	background-color:#333;
}

ul.process-steps li:after,
ul.process-steps li:before {
	content: '';
	position: absolute;
	top: 26px;
	left: 0;
	width: 50%;
	border-top: 1px dashed #DDD;
}
ul.process-steps li:first-child:before {
	display:none;
}
ul.process-steps li:last-child:after {
	display:none;
}
ul.process-steps li:after {
	left: auto;
	right: 0;
	margin: 0 -26px 0 0;
}
ul.process-steps li h1,
ul.process-steps li h2,
ul.process-steps li h3,
ul.process-steps li h4,
ul.process-steps li h5,
ul.process-steps li h6 {
	margin:20px 0 0 0;
}


ul.process-steps li>a>i {
	margin:0;
	padding:0;
	margin-left:-4px;
	margin-top:-1px;
	font-size:28px;
	line-height:28px;
}
ul.process-steps li>a>i.fa {
	font-size:30px;
	line-height:30px;
}

ul.process-steps.process-steps-square li a {
	-webkit-border-radius: 3px !important;
	-moz-border-radius: 3px !important;
	border-radius: 3px !important;
}

@media only screen and (max-width: 768px) {
	ul.process-steps li:after,
	ul.process-steps li:before  {
		display:none;
	}

	ul.process-steps li h1,
	ul.process-steps li h2,
	ul.process-steps li h3,
	ul.process-steps li h4,
	ul.process-steps li h5,
	ul.process-steps li h6 {
		margin:10px 0 30px 0;
	}

}

@media only screen and (max-width: 482px) {
	ul.process-steps li>a {
		display:inline-block !important;
	}
	ul.process-steps li h1,
	ul.process-steps li h2,
	ul.process-steps li h3,
	ul.process-steps li h4,
	ul.process-steps li h5,
	ul.process-steps li h6 {
		margin:3px 0;
		display:block;
	}
	ul.process-steps li {
		padding:10px 0;
	}
}




/** 36. Price Table
 **************************************************************** **/
div.price-table {
	background:rgba(0,0,0,0.03);
	margin:30px 0;
	text-align:center;
	padding-bottom:30px;
	border-left:#fff 1px solid;
}

div.row.pricetable-container {
	padding:0 15px;
}
div.price-table h3 {
	font-size:25px;
	line-height:25px;
	padding:30px 0;
	border-bottom: rgba(0,0,0,0.1) 2px solid;
	text-transform:uppercase;
	font-weight:300;
}
div.price-table p {
	color: #666;
	font-size: 36px;
	line-height:36px;
	padding: 30px 0;
	font-weight: 400;
	width: 150px;
	height: 150px;
	padding-top: 53px;
	display: inline-block;
	background-color: rgba(0,0,0,0.05);
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	margin-top:0;
}
div.price-table p span {
	display:block;
	font-size:10px;
	line-height:16px;
	font-weight:300;
	text-transform:uppercase;
}
div.price-table ul {
	margin:0;
	padding:0;
}
div.price-table ul li {
	list-style:none;
	font-size:12px;
	border-bottom: rgba(0,0,0,0.1) 1px solid;
	padding:8px;
	text-transform:uppercase;
}
div.price-table.popular,
div.price-table.popular ul li,
div.price-table.popular p,
div.price-table.popular p span,
div.price-table.popular h3 {
	color:#fff;
}
div.price-table.popular {
	background:#676767;
}
div.price-table .btn {
	margin-top:30px;
}
div.price-table .btn-primary {
	background-color:#333;
	border:0;
}

section.dark div.price-table {
	border-left-color:#212121;
	background-color:#444;
}
section.dark div.price-table.popular {
	background-color:#666;
}
section.dark div.price-table h3 {
	border-bottom-color:rgba(255,255,255,0.1);
}
section.dark div.price-table .btn {
	color:#fff;
}

/* mega price table */
div.mega-price-table {
	margin-top:60px;
}
div.mega-price-table .btn .caret.pull-right {
	margin-top:8px;
}
div.mega-price-table .pricing-title,
div.mega-price-table .pricing-head {
	color:#fff;
	text-align:center;
	background-color:rgba(0,0,0,0.6);
	height:75px;
}
div.mega-price-table .pricing-title {
	background-color:transparent !important;
	padding:15px 0 0 0;
	margin:0;
	height:165px;
}
div.mega-price-table .pricing-title h3 {
	font-size:35px;
	line-height:35px;
	margin-bottom:10px;
}

div.mega-price-table .pricing-head h3 {
	margin-bottom:3px;
	display:block;
	color:#fff;
	font-size:30px;
	padding-top:12px;
	height:36px;
	font-weight:300;
}
div.mega-price-table h4 {
	display:block;
	text-align:center;
	font-size:60px;
	padding:20px 0; margin:0;
	font-weight:400;
	color:#666;
	height:85;
	background-color:rgba(0,0,0,0.03);
	font-weight:300;
}
div.mega-price-table .pricing:hover h4 {
	color:#333;
	text-align:center;
}
div.mega-price-table .pricing h4 sup,
div.mega-price-table .pricing h4 sub {
	font-size:34px;
}
div.mega-price-table .pricing-head small {
	font-size:12px;
	line-height:40px;
	display:block;
	color:rgba(255,255,255,0.7);
	font-weight:300;
	font-family:'Open Sans', Arial, Hevletica, sans-serif;
}

div.mega-price-table .pricing-desc li,
div.mega-price-table ul.pricing-table li {
	padding: 10px;
	font-style: normal;
	min-height: 41px;
	text-align:center;
}
div.mega-price-table ul li.alternate {
	background-color:rgba(0,0,0,0.03);
}

div.mega-price-table {
	padding-left:15px;
	padding-right:15px;
}
div.mega-price-table div {
	padding:0;
}
div.mega-price-table .pricing {
	margin-top: 1px;
	margin-left: 1px;
	background: rgba(0,0,0,0.03);
}
section.dark div.mega-price-table ul li.alternate,
section.dark div.mega-price-table .pricing {
	background: rgba(255,255,255, 0.05);
}
div.mega-price-table .pricing-desc div,
div.mega-price-table .pricing-desc li {
	text-align:left !important;
}
div.mega-price-table .btn,
div.mega-price-table .list-unstyled,
div.mega-price-table .btn-group,
div.mega-price-table .btn-toolbar {
	margin:0;

	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
}
div.mega-price-table .pricing.popular h4 small {
	color:#fff;
}
div.mega-price-table .pricing.popular {
	background-color:#676767;
}
div.mega-price-table .pricing.popular,
div.mega-price-table .pricing.popular h3,
div.mega-price-table .pricing.popular h4,
div.mega-price-table .pricing.popular li,
div.mega-price-table .pricing.popular div,
div.mega-price-table  .pricing.popular .pricing-table i.fa {
	color:#fff !important;
}
div.mega-price-table .dropdown-menu {
	width:100%;
}

/* clean price */
.price-clean {
	padding: 30px 10px;
	text-align: center;
	position: relative;
	border: 1px solid #D0D6DF;
	font-family: Arial, Helvetica, sans-serif;

	-webkit-box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.05);
	-moz-box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.05);
	-o-box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.05);
	box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.05);
}
.price-clean h4 {
	font-size: 60px;
	line-height:60px;
	font-weight: 300;
	margin: 0;
	color: #547698;
}
.price-clean h4 sup {
	position: relative;
	font-size: 20px;
	line-height:25px;
	vertical-align: top;
	top: 3px;
}
.price-clean h4 em {
	font-size: 14px;
	font-style:normal;
}
.price-clean h5 {
	text-transform: uppercase;
	font-weight: 300;
	margin: 0;
	font-size: 15px;
	color: #BACDD6;
	letter-spacing: 2px;
}
.price-clean p {
	line-height: 1.5em;
	color: #526066;
	margin-bottom: 0;
}



@media only screen and (max-width: 992px) {
	.price-clean {
		margin-bottom:30px;
	}
	.col-md-5th .price-clean h4 {
		font-size: 40px;
		line-height:40px;
	}
	.col-md-5th .price-clean h4 em {
		font-size:11px;
	}
}
@media only screen and (max-width: 768px) {
	.price-clean h4 {
		font-size: 60px;
		line-height:60px;
	}
	.price-clean h4 em {
		font-size: 14px;
	}
}





/** 37. Styled Icons
 **************************************************************** **/
i.ico-transparent,
i.ico-bordered,
i.ico-rounded,
i.ico-color,
i.ico-light,
i.ico-dark {

	color: #444;
	width:50px; height:50px;
	line-height: 50px;
	font-size:25px;
	border: 1px solid #555;
	background-color: transparent;
	text-align:center;
	display:inline-block;
	margin-bottom:5px;
	margin: 4px 8px 7px 0;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;

	-webkit-transition: all .3s ease;
	-o-transition: all .3s ease;
	transition: all .3s ease;
}
i.ico-transparent {
	border:transparent;
}
i.ico-rounded {
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
}
i.ico-light {
	border:transparent;
	background-color:rgba(0,0,0,0.05);
}
i.ico-dark {
	color:#fff;
	border:transparent;
	background-color:#333;
}
i.ico-color {
	color:#fff;
	border:transparent;
	text-shadow:rgba(0,0,0,.3) 1px 1px 1px;
}
i.ico-hover:hover {
	color:#fff;
	background-color:#111;
}
i.ico-hover-dark:hover {
	color:#fff !important;
	background-color:#111 !important;
}
i.ico-color.ico-hover:hover {
	background-color:#212121;
}

i.ico-xs {
	width:30px; height:30px;
	line-height: 30px;
	font-size:15px;
}
i.ico-lg {
	width:80px; height:80px;
	line-height: 80px;
	font-size:45px;
}


section.dark  i.ico-rounded,
section.dark  i.ico-bordered {
	color:#ddd;
	border-color:#666;
}
section.dark  i.ico-dark {
	color:#ccc;
	background-color:#111;
}
section.dark  i.ico-light {
	color:#ddd;
	background-color:#444;
}
section.dark  i.ico-transparent {
	color:#ddd;
}
section.dark i.ico-color.ico-hover:hover {
	background-color:#666;
}




/**	38. Pickers: Datepicker & Rangepicker & Colorpicker
*************************************************** **/
/*!
 * Datepicker for Bootstrap
 *
 * Copyright 2012 Stefan Petre
 * Improvements by Andrew Rowls
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 */
.datepicker {
	padding: 4px;
	border-radius: 4px;
	direction: ltr;
	/*.dow {
		border-top: 1px solid #ddd !important;
	}*/
}
.datepicker-inline {
	width: 220px;
}
.datepicker.datepicker-rtl {
	direction: rtl;
}
.datepicker.datepicker-rtl table tr td span {
	float: right;
}
.datepicker-dropdown {
	top: 0;
	left: 0;
}
.datepicker-dropdown:before {
	content: '';
	display: inline-block;
	border-left: 7px solid transparent;
	border-right: 7px solid transparent;
	border-bottom: 7px solid #ccc;
	border-top: 0;
	border-bottom-color: rgba(0, 0, 0, 0.2);
	position: absolute;
}
.datepicker-dropdown:after {
	content: '';
	display: inline-block;
	border-left: 6px solid transparent;
	border-right: 6px solid transparent;
	border-bottom: 6px solid #fff;
	border-top: 0;
	position: absolute;
}
.datepicker-dropdown.datepicker-orient-left:before {
	left: 6px;
}
.datepicker-dropdown.datepicker-orient-left:after {
	left: 7px;
}
.datepicker-dropdown.datepicker-orient-right:before {
	right: 6px;
}
.datepicker-dropdown.datepicker-orient-right:after {
	right: 7px;
}
.datepicker-dropdown.datepicker-orient-top:before {
	top: -7px;
}
.datepicker-dropdown.datepicker-orient-top:after {
	top: -6px;
}
.datepicker-dropdown.datepicker-orient-bottom:before {
	bottom: -7px;
	border-bottom: 0;
	border-top: 7px solid #999;
}
.datepicker-dropdown.datepicker-orient-bottom:after {
	bottom: -6px;
	border-bottom: 0;
	border-top: 6px solid #fff;
}
.datepicker > div {
	display: none;
}
.datepicker.days div.datepicker-days {
	display: block;
}
.datepicker.months div.datepicker-months {
	display: block;
}
.datepicker.years div.datepicker-years {
	display: block;
}
.datepicker table {
	margin: 0;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
.datepicker table tr td,
.datepicker table tr th {
	text-align: center;
	width: 30px;
	height: 30px;
	border-radius: 4px;
	border: none;
}
.table-striped .datepicker table tr td,
.table-striped .datepicker table tr th {
	background-color: transparent;
}
.datepicker table tr td.day:hover,
.datepicker table tr td.day.focused {
	background: #eeeeee;
	cursor: pointer;
}
.datepicker table tr td.old,
.datepicker table tr td.new {
	color: #999999;
}
.datepicker table tr td.disabled,
.datepicker table tr td.disabled:hover {
	background: none;
	color: #999999;
	cursor: default;
}
.datepicker table tr td.today,
.datepicker table tr td.today:hover,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover {
	color: #000000;
	background-color: #ffdb99;
	border-color: #ffb733;
}
.datepicker table tr td.today:hover,
.datepicker table tr td.today:hover:hover,
.datepicker table tr td.today.disabled:hover,
.datepicker table tr td.today.disabled:hover:hover,
.datepicker table tr td.today:focus,
.datepicker table tr td.today:hover:focus,
.datepicker table tr td.today.disabled:focus,
.datepicker table tr td.today.disabled:hover:focus,
.datepicker table tr td.today:active,
.datepicker table tr td.today:hover:active,
.datepicker table tr td.today.disabled:active,
.datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.active,
.datepicker table tr td.today:hover.active,
.datepicker table tr td.today.disabled.active,
.datepicker table tr td.today.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td.today,
.open .dropdown-toggle.datepicker table tr td.today:hover,
.open .dropdown-toggle.datepicker table tr td.today.disabled,
.open .dropdown-toggle.datepicker table tr td.today.disabled:hover {
	color: #000000;
	background-color: #ffcd70;
	border-color: #f59e00;
}
.datepicker table tr td.today:active,
.datepicker table tr td.today:hover:active,
.datepicker table tr td.today.disabled:active,
.datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.active,
.datepicker table tr td.today:hover.active,
.datepicker table tr td.today.disabled.active,
.datepicker table tr td.today.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td.today,
.open .dropdown-toggle.datepicker table tr td.today:hover,
.open .dropdown-toggle.datepicker table tr td.today.disabled,
.open .dropdown-toggle.datepicker table tr td.today.disabled:hover {
	background-image: none;
}
.datepicker table tr td.today.disabled,
.datepicker table tr td.today:hover.disabled,
.datepicker table tr td.today.disabled.disabled,
.datepicker table tr td.today.disabled:hover.disabled,
.datepicker table tr td.today[disabled],
.datepicker table tr td.today:hover[disabled],
.datepicker table tr td.today.disabled[disabled],
.datepicker table tr td.today.disabled:hover[disabled],
fieldset[disabled] .datepicker table tr td.today,
fieldset[disabled] .datepicker table tr td.today:hover,
fieldset[disabled] .datepicker table tr td.today.disabled,
fieldset[disabled] .datepicker table tr td.today.disabled:hover,
.datepicker table tr td.today.disabled:hover,
.datepicker table tr td.today:hover.disabled:hover,
.datepicker table tr td.today.disabled.disabled:hover,
.datepicker table tr td.today.disabled:hover.disabled:hover,
.datepicker table tr td.today[disabled]:hover,
.datepicker table tr td.today:hover[disabled]:hover,
.datepicker table tr td.today.disabled[disabled]:hover,
.datepicker table tr td.today.disabled:hover[disabled]:hover,
fieldset[disabled] .datepicker table tr td.today:hover,
fieldset[disabled] .datepicker table tr td.today:hover:hover,
fieldset[disabled] .datepicker table tr td.today.disabled:hover,
fieldset[disabled] .datepicker table tr td.today.disabled:hover:hover,
.datepicker table tr td.today.disabled:focus,
.datepicker table tr td.today:hover.disabled:focus,
.datepicker table tr td.today.disabled.disabled:focus,
.datepicker table tr td.today.disabled:hover.disabled:focus,
.datepicker table tr td.today[disabled]:focus,
.datepicker table tr td.today:hover[disabled]:focus,
.datepicker table tr td.today.disabled[disabled]:focus,
.datepicker table tr td.today.disabled:hover[disabled]:focus,
fieldset[disabled] .datepicker table tr td.today:focus,
fieldset[disabled] .datepicker table tr td.today:hover:focus,
fieldset[disabled] .datepicker table tr td.today.disabled:focus,
fieldset[disabled] .datepicker table tr td.today.disabled:hover:focus,
.datepicker table tr td.today.disabled:active,
.datepicker table tr td.today:hover.disabled:active,
.datepicker table tr td.today.disabled.disabled:active,
.datepicker table tr td.today.disabled:hover.disabled:active,
.datepicker table tr td.today[disabled]:active,
.datepicker table tr td.today:hover[disabled]:active,
.datepicker table tr td.today.disabled[disabled]:active,
.datepicker table tr td.today.disabled:hover[disabled]:active,
fieldset[disabled] .datepicker table tr td.today:active,
fieldset[disabled] .datepicker table tr td.today:hover:active,
fieldset[disabled] .datepicker table tr td.today.disabled:active,
fieldset[disabled] .datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.disabled.active,
.datepicker table tr td.today:hover.disabled.active,
.datepicker table tr td.today.disabled.disabled.active,
.datepicker table tr td.today.disabled:hover.disabled.active,
.datepicker table tr td.today[disabled].active,
.datepicker table tr td.today:hover[disabled].active,
.datepicker table tr td.today.disabled[disabled].active,
.datepicker table tr td.today.disabled:hover[disabled].active,
fieldset[disabled] .datepicker table tr td.today.active,
fieldset[disabled] .datepicker table tr td.today:hover.active,
fieldset[disabled] .datepicker table tr td.today.disabled.active,
fieldset[disabled] .datepicker table tr td.today.disabled:hover.active {
	background-color: #ffdb99;
	border-color: #ffb733;
}
.datepicker table tr td.today:hover:hover {
	color: #000;
}
.datepicker table tr td.today.active:hover {
	color: #fff;
}
.datepicker table tr td.range,
.datepicker table tr td.range:hover,
.datepicker table tr td.range.disabled,
.datepicker table tr td.range.disabled:hover {
	background: #eeeeee;
	border-radius: 0;
}
.datepicker table tr td.range.today,
.datepicker table tr td.range.today:hover,
.datepicker table tr td.range.today.disabled,
.datepicker table tr td.range.today.disabled:hover {
	color: #000000;
	background-color: #f7ca77;
	border-color: #f1a417;
	border-radius: 0;
}
.datepicker table tr td.range.today:hover,
.datepicker table tr td.range.today:hover:hover,
.datepicker table tr td.range.today.disabled:hover,
.datepicker table tr td.range.today.disabled:hover:hover,
.datepicker table tr td.range.today:focus,
.datepicker table tr td.range.today:hover:focus,
.datepicker table tr td.range.today.disabled:focus,
.datepicker table tr td.range.today.disabled:hover:focus,
.datepicker table tr td.range.today:active,
.datepicker table tr td.range.today:hover:active,
.datepicker table tr td.range.today.disabled:active,
.datepicker table tr td.range.today.disabled:hover:active,
.datepicker table tr td.range.today.active,
.datepicker table tr td.range.today:hover.active,
.datepicker table tr td.range.today.disabled.active,
.datepicker table tr td.range.today.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td.range.today,
.open .dropdown-toggle.datepicker table tr td.range.today:hover,
.open .dropdown-toggle.datepicker table tr td.range.today.disabled,
.open .dropdown-toggle.datepicker table tr td.range.today.disabled:hover {
	color: #000000;
	background-color: #f4bb51;
	border-color: #bf800c;
}
.datepicker table tr td.range.today:active,
.datepicker table tr td.range.today:hover:active,
.datepicker table tr td.range.today.disabled:active,
.datepicker table tr td.range.today.disabled:hover:active,
.datepicker table tr td.range.today.active,
.datepicker table tr td.range.today:hover.active,
.datepicker table tr td.range.today.disabled.active,
.datepicker table tr td.range.today.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td.range.today,
.open .dropdown-toggle.datepicker table tr td.range.today:hover,
.open .dropdown-toggle.datepicker table tr td.range.today.disabled,
.open .dropdown-toggle.datepicker table tr td.range.today.disabled:hover {
	background-image: none;
}
.datepicker table tr td.range.today.disabled,
.datepicker table tr td.range.today:hover.disabled,
.datepicker table tr td.range.today.disabled.disabled,
.datepicker table tr td.range.today.disabled:hover.disabled,
.datepicker table tr td.range.today[disabled],
.datepicker table tr td.range.today:hover[disabled],
.datepicker table tr td.range.today.disabled[disabled],
.datepicker table tr td.range.today.disabled:hover[disabled],
fieldset[disabled] .datepicker table tr td.range.today,
fieldset[disabled] .datepicker table tr td.range.today:hover,
fieldset[disabled] .datepicker table tr td.range.today.disabled,
fieldset[disabled] .datepicker table tr td.range.today.disabled:hover,
.datepicker table tr td.range.today.disabled:hover,
.datepicker table tr td.range.today:hover.disabled:hover,
.datepicker table tr td.range.today.disabled.disabled:hover,
.datepicker table tr td.range.today.disabled:hover.disabled:hover,
.datepicker table tr td.range.today[disabled]:hover,
.datepicker table tr td.range.today:hover[disabled]:hover,
.datepicker table tr td.range.today.disabled[disabled]:hover,
.datepicker table tr td.range.today.disabled:hover[disabled]:hover,
fieldset[disabled] .datepicker table tr td.range.today:hover,
fieldset[disabled] .datepicker table tr td.range.today:hover:hover,
fieldset[disabled] .datepicker table tr td.range.today.disabled:hover,
fieldset[disabled] .datepicker table tr td.range.today.disabled:hover:hover,
.datepicker table tr td.range.today.disabled:focus,
.datepicker table tr td.range.today:hover.disabled:focus,
.datepicker table tr td.range.today.disabled.disabled:focus,
.datepicker table tr td.range.today.disabled:hover.disabled:focus,
.datepicker table tr td.range.today[disabled]:focus,
.datepicker table tr td.range.today:hover[disabled]:focus,
.datepicker table tr td.range.today.disabled[disabled]:focus,
.datepicker table tr td.range.today.disabled:hover[disabled]:focus,
fieldset[disabled] .datepicker table tr td.range.today:focus,
fieldset[disabled] .datepicker table tr td.range.today:hover:focus,
fieldset[disabled] .datepicker table tr td.range.today.disabled:focus,
fieldset[disabled] .datepicker table tr td.range.today.disabled:hover:focus,
.datepicker table tr td.range.today.disabled:active,
.datepicker table tr td.range.today:hover.disabled:active,
.datepicker table tr td.range.today.disabled.disabled:active,
.datepicker table tr td.range.today.disabled:hover.disabled:active,
.datepicker table tr td.range.today[disabled]:active,
.datepicker table tr td.range.today:hover[disabled]:active,
.datepicker table tr td.range.today.disabled[disabled]:active,
.datepicker table tr td.range.today.disabled:hover[disabled]:active,
fieldset[disabled] .datepicker table tr td.range.today:active,
fieldset[disabled] .datepicker table tr td.range.today:hover:active,
fieldset[disabled] .datepicker table tr td.range.today.disabled:active,
fieldset[disabled] .datepicker table tr td.range.today.disabled:hover:active,
.datepicker table tr td.range.today.disabled.active,
.datepicker table tr td.range.today:hover.disabled.active,
.datepicker table tr td.range.today.disabled.disabled.active,
.datepicker table tr td.range.today.disabled:hover.disabled.active,
.datepicker table tr td.range.today[disabled].active,
.datepicker table tr td.range.today:hover[disabled].active,
.datepicker table tr td.range.today.disabled[disabled].active,
.datepicker table tr td.range.today.disabled:hover[disabled].active,
fieldset[disabled] .datepicker table tr td.range.today.active,
fieldset[disabled] .datepicker table tr td.range.today:hover.active,
fieldset[disabled] .datepicker table tr td.range.today.disabled.active,
fieldset[disabled] .datepicker table tr td.range.today.disabled:hover.active {
	background-color: #f7ca77;
	border-color: #f1a417;
}
.datepicker table tr td.selected,
.datepicker table tr td.selected:hover,
.datepicker table tr td.selected.disabled,
.datepicker table tr td.selected.disabled:hover {
	color: #ffffff;
	background-color: #999999;
	border-color: #555555;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.datepicker table tr td.selected:hover,
.datepicker table tr td.selected:hover:hover,
.datepicker table tr td.selected.disabled:hover,
.datepicker table tr td.selected.disabled:hover:hover,
.datepicker table tr td.selected:focus,
.datepicker table tr td.selected:hover:focus,
.datepicker table tr td.selected.disabled:focus,
.datepicker table tr td.selected.disabled:hover:focus,
.datepicker table tr td.selected:active,
.datepicker table tr td.selected:hover:active,
.datepicker table tr td.selected.disabled:active,
.datepicker table tr td.selected.disabled:hover:active,
.datepicker table tr td.selected.active,
.datepicker table tr td.selected:hover.active,
.datepicker table tr td.selected.disabled.active,
.datepicker table tr td.selected.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td.selected,
.open .dropdown-toggle.datepicker table tr td.selected:hover,
.open .dropdown-toggle.datepicker table tr td.selected.disabled,
.open .dropdown-toggle.datepicker table tr td.selected.disabled:hover {
	color: #ffffff;
	background-color: #858585;
	border-color: #373737;
}
.datepicker table tr td.selected:active,
.datepicker table tr td.selected:hover:active,
.datepicker table tr td.selected.disabled:active,
.datepicker table tr td.selected.disabled:hover:active,
.datepicker table tr td.selected.active,
.datepicker table tr td.selected:hover.active,
.datepicker table tr td.selected.disabled.active,
.datepicker table tr td.selected.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td.selected,
.open .dropdown-toggle.datepicker table tr td.selected:hover,
.open .dropdown-toggle.datepicker table tr td.selected.disabled,
.open .dropdown-toggle.datepicker table tr td.selected.disabled:hover {
	background-image: none;
}
.datepicker table tr td.selected.disabled,
.datepicker table tr td.selected:hover.disabled,
.datepicker table tr td.selected.disabled.disabled,
.datepicker table tr td.selected.disabled:hover.disabled,
.datepicker table tr td.selected[disabled],
.datepicker table tr td.selected:hover[disabled],
.datepicker table tr td.selected.disabled[disabled],
.datepicker table tr td.selected.disabled:hover[disabled],
fieldset[disabled] .datepicker table tr td.selected,
fieldset[disabled] .datepicker table tr td.selected:hover,
fieldset[disabled] .datepicker table tr td.selected.disabled,
fieldset[disabled] .datepicker table tr td.selected.disabled:hover,
.datepicker table tr td.selected.disabled:hover,
.datepicker table tr td.selected:hover.disabled:hover,
.datepicker table tr td.selected.disabled.disabled:hover,
.datepicker table tr td.selected.disabled:hover.disabled:hover,
.datepicker table tr td.selected[disabled]:hover,
.datepicker table tr td.selected:hover[disabled]:hover,
.datepicker table tr td.selected.disabled[disabled]:hover,
.datepicker table tr td.selected.disabled:hover[disabled]:hover,
fieldset[disabled] .datepicker table tr td.selected:hover,
fieldset[disabled] .datepicker table tr td.selected:hover:hover,
fieldset[disabled] .datepicker table tr td.selected.disabled:hover,
fieldset[disabled] .datepicker table tr td.selected.disabled:hover:hover,
.datepicker table tr td.selected.disabled:focus,
.datepicker table tr td.selected:hover.disabled:focus,
.datepicker table tr td.selected.disabled.disabled:focus,
.datepicker table tr td.selected.disabled:hover.disabled:focus,
.datepicker table tr td.selected[disabled]:focus,
.datepicker table tr td.selected:hover[disabled]:focus,
.datepicker table tr td.selected.disabled[disabled]:focus,
.datepicker table tr td.selected.disabled:hover[disabled]:focus,
fieldset[disabled] .datepicker table tr td.selected:focus,
fieldset[disabled] .datepicker table tr td.selected:hover:focus,
fieldset[disabled] .datepicker table tr td.selected.disabled:focus,
fieldset[disabled] .datepicker table tr td.selected.disabled:hover:focus,
.datepicker table tr td.selected.disabled:active,
.datepicker table tr td.selected:hover.disabled:active,
.datepicker table tr td.selected.disabled.disabled:active,
.datepicker table tr td.selected.disabled:hover.disabled:active,
.datepicker table tr td.selected[disabled]:active,
.datepicker table tr td.selected:hover[disabled]:active,
.datepicker table tr td.selected.disabled[disabled]:active,
.datepicker table tr td.selected.disabled:hover[disabled]:active,
fieldset[disabled] .datepicker table tr td.selected:active,
fieldset[disabled] .datepicker table tr td.selected:hover:active,
fieldset[disabled] .datepicker table tr td.selected.disabled:active,
fieldset[disabled] .datepicker table tr td.selected.disabled:hover:active,
.datepicker table tr td.selected.disabled.active,
.datepicker table tr td.selected:hover.disabled.active,
.datepicker table tr td.selected.disabled.disabled.active,
.datepicker table tr td.selected.disabled:hover.disabled.active,
.datepicker table tr td.selected[disabled].active,
.datepicker table tr td.selected:hover[disabled].active,
.datepicker table tr td.selected.disabled[disabled].active,
.datepicker table tr td.selected.disabled:hover[disabled].active,
fieldset[disabled] .datepicker table tr td.selected.active,
fieldset[disabled] .datepicker table tr td.selected:hover.active,
fieldset[disabled] .datepicker table tr td.selected.disabled.active,
fieldset[disabled] .datepicker table tr td.selected.disabled:hover.active {
	background-color: #999999;
	border-color: #555555;
}
.datepicker table tr td.active,
.datepicker table tr td.active:hover,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover {
	color: #ffffff;
	background-color: #428bca;
	border-color: #357ebd;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.datepicker table tr td.active:hover,
.datepicker table tr td.active:hover:hover,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td.active:focus,
.datepicker table tr td.active:hover:focus,
.datepicker table tr td.active.disabled:focus,
.datepicker table tr td.active.disabled:hover:focus,
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td.active,
.open .dropdown-toggle.datepicker table tr td.active:hover,
.open .dropdown-toggle.datepicker table tr td.active.disabled,
.open .dropdown-toggle.datepicker table tr td.active.disabled:hover {
	color: #ffffff;
	background-color: #3276b1;
	border-color: #285e8e;
}
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td.active,
.open .dropdown-toggle.datepicker table tr td.active:hover,
.open .dropdown-toggle.datepicker table tr td.active.disabled,
.open .dropdown-toggle.datepicker table tr td.active.disabled:hover {
	background-image: none;
}
.datepicker table tr td.active.disabled,
.datepicker table tr td.active:hover.disabled,
.datepicker table tr td.active.disabled.disabled,
.datepicker table tr td.active.disabled:hover.disabled,
.datepicker table tr td.active[disabled],
.datepicker table tr td.active:hover[disabled],
.datepicker table tr td.active.disabled[disabled],
.datepicker table tr td.active.disabled:hover[disabled],
fieldset[disabled] .datepicker table tr td.active,
fieldset[disabled] .datepicker table tr td.active:hover,
fieldset[disabled] .datepicker table tr td.active.disabled,
fieldset[disabled] .datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active:hover.disabled:hover,
.datepicker table tr td.active.disabled.disabled:hover,
.datepicker table tr td.active.disabled:hover.disabled:hover,
.datepicker table tr td.active[disabled]:hover,
.datepicker table tr td.active:hover[disabled]:hover,
.datepicker table tr td.active.disabled[disabled]:hover,
.datepicker table tr td.active.disabled:hover[disabled]:hover,
fieldset[disabled] .datepicker table tr td.active:hover,
fieldset[disabled] .datepicker table tr td.active:hover:hover,
fieldset[disabled] .datepicker table tr td.active.disabled:hover,
fieldset[disabled] .datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td.active.disabled:focus,
.datepicker table tr td.active:hover.disabled:focus,
.datepicker table tr td.active.disabled.disabled:focus,
.datepicker table tr td.active.disabled:hover.disabled:focus,
.datepicker table tr td.active[disabled]:focus,
.datepicker table tr td.active:hover[disabled]:focus,
.datepicker table tr td.active.disabled[disabled]:focus,
.datepicker table tr td.active.disabled:hover[disabled]:focus,
fieldset[disabled] .datepicker table tr td.active:focus,
fieldset[disabled] .datepicker table tr td.active:hover:focus,
fieldset[disabled] .datepicker table tr td.active.disabled:focus,
fieldset[disabled] .datepicker table tr td.active.disabled:hover:focus,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active:hover.disabled:active,
.datepicker table tr td.active.disabled.disabled:active,
.datepicker table tr td.active.disabled:hover.disabled:active,
.datepicker table tr td.active[disabled]:active,
.datepicker table tr td.active:hover[disabled]:active,
.datepicker table tr td.active.disabled[disabled]:active,
.datepicker table tr td.active.disabled:hover[disabled]:active,
fieldset[disabled] .datepicker table tr td.active:active,
fieldset[disabled] .datepicker table tr td.active:hover:active,
fieldset[disabled] .datepicker table tr td.active.disabled:active,
fieldset[disabled] .datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active:hover.disabled.active,
.datepicker table tr td.active.disabled.disabled.active,
.datepicker table tr td.active.disabled:hover.disabled.active,
.datepicker table tr td.active[disabled].active,
.datepicker table tr td.active:hover[disabled].active,
.datepicker table tr td.active.disabled[disabled].active,
.datepicker table tr td.active.disabled:hover[disabled].active,
fieldset[disabled] .datepicker table tr td.active.active,
fieldset[disabled] .datepicker table tr td.active:hover.active,
fieldset[disabled] .datepicker table tr td.active.disabled.active,
fieldset[disabled] .datepicker table tr td.active.disabled:hover.active {
	background-color: #428bca;
	border-color: #357ebd;
}
.datepicker table tr td span {
	display: block;
	width: 23%;
	height: 54px;
	line-height: 54px;
	float: left;
	margin: 1%;
	cursor: pointer;
	border-radius: 4px;
}
.datepicker table tr td span:hover {
	background: #eeeeee;
}
.datepicker table tr td span.disabled,
.datepicker table tr td span.disabled:hover {
	background: none;
	color: #999999;
	cursor: default;
}
.datepicker table tr td span.active,
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active.disabled:hover {
	color: #ffffff;
	background-color: #428bca;
	border-color: #357ebd;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active:hover:hover,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active.disabled:hover:hover,
.datepicker table tr td span.active:focus,
.datepicker table tr td span.active:hover:focus,
.datepicker table tr td span.active.disabled:focus,
.datepicker table tr td span.active.disabled:hover:focus,
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td span.active,
.open .dropdown-toggle.datepicker table tr td span.active:hover,
.open .dropdown-toggle.datepicker table tr td span.active.disabled,
.open .dropdown-toggle.datepicker table tr td span.active.disabled:hover {
	color: #ffffff;
	background-color: #3276b1;
	border-color: #285e8e;
}
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td span.active,
.open .dropdown-toggle.datepicker table tr td span.active:hover,
.open .dropdown-toggle.datepicker table tr td span.active.disabled,
.open .dropdown-toggle.datepicker table tr td span.active.disabled:hover {
	background-image: none;
}
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active:hover.disabled,
.datepicker table tr td span.active.disabled.disabled,
.datepicker table tr td span.active.disabled:hover.disabled,
.datepicker table tr td span.active[disabled],
.datepicker table tr td span.active:hover[disabled],
.datepicker table tr td span.active.disabled[disabled],
.datepicker table tr td span.active.disabled:hover[disabled],
fieldset[disabled] .datepicker table tr td span.active,
fieldset[disabled] .datepicker table tr td span.active:hover,
fieldset[disabled] .datepicker table tr td span.active.disabled,
fieldset[disabled] .datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active:hover.disabled:hover,
.datepicker table tr td span.active.disabled.disabled:hover,
.datepicker table tr td span.active.disabled:hover.disabled:hover,
.datepicker table tr td span.active[disabled]:hover,
.datepicker table tr td span.active:hover[disabled]:hover,
.datepicker table tr td span.active.disabled[disabled]:hover,
.datepicker table tr td span.active.disabled:hover[disabled]:hover,
fieldset[disabled] .datepicker table tr td span.active:hover,
fieldset[disabled] .datepicker table tr td span.active:hover:hover,
fieldset[disabled] .datepicker table tr td span.active.disabled:hover,
fieldset[disabled] .datepicker table tr td span.active.disabled:hover:hover,
.datepicker table tr td span.active.disabled:focus,
.datepicker table tr td span.active:hover.disabled:focus,
.datepicker table tr td span.active.disabled.disabled:focus,
.datepicker table tr td span.active.disabled:hover.disabled:focus,
.datepicker table tr td span.active[disabled]:focus,
.datepicker table tr td span.active:hover[disabled]:focus,
.datepicker table tr td span.active.disabled[disabled]:focus,
.datepicker table tr td span.active.disabled:hover[disabled]:focus,
fieldset[disabled] .datepicker table tr td span.active:focus,
fieldset[disabled] .datepicker table tr td span.active:hover:focus,
fieldset[disabled] .datepicker table tr td span.active.disabled:focus,
fieldset[disabled] .datepicker table tr td span.active.disabled:hover:focus,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active:hover.disabled:active,
.datepicker table tr td span.active.disabled.disabled:active,
.datepicker table tr td span.active.disabled:hover.disabled:active,
.datepicker table tr td span.active[disabled]:active,
.datepicker table tr td span.active:hover[disabled]:active,
.datepicker table tr td span.active.disabled[disabled]:active,
.datepicker table tr td span.active.disabled:hover[disabled]:active,
fieldset[disabled] .datepicker table tr td span.active:active,
fieldset[disabled] .datepicker table tr td span.active:hover:active,
fieldset[disabled] .datepicker table tr td span.active.disabled:active,
fieldset[disabled] .datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active:hover.disabled.active,
.datepicker table tr td span.active.disabled.disabled.active,
.datepicker table tr td span.active.disabled:hover.disabled.active,
.datepicker table tr td span.active[disabled].active,
.datepicker table tr td span.active:hover[disabled].active,
.datepicker table tr td span.active.disabled[disabled].active,
.datepicker table tr td span.active.disabled:hover[disabled].active,
fieldset[disabled] .datepicker table tr td span.active.active,
fieldset[disabled] .datepicker table tr td span.active:hover.active,
fieldset[disabled] .datepicker table tr td span.active.disabled.active,
fieldset[disabled] .datepicker table tr td span.active.disabled:hover.active {
	background-color: #428bca;
	border-color: #357ebd;
}
.datepicker table tr td span.old,
.datepicker table tr td span.new {
	color: #999999;
}
.datepicker th.datepicker-switch {
	width: 145px;
}
.datepicker thead tr:first-child th,
.datepicker tfoot tr th {
	cursor: pointer;
}
.datepicker thead tr:first-child th:hover,
.datepicker tfoot tr th:hover {
	background: #eeeeee;
}
.datepicker .cw {
	font-size: 10px;
	width: 12px;
	padding: 0 2px 0 5px;
	vertical-align: middle;
}
.datepicker thead tr:first-child th.cw {
	cursor: default;
	background-color: transparent;
}
.input-group.date .input-group-addon i {
	cursor: pointer;
	width: 16px;
	height: 16px;
}
.input-daterange input {
	text-align: center;
}
.input-daterange input:first-child {
	border-radius: 3px 0 0 3px;
}
.input-daterange input:last-child {
	border-radius: 0 3px 3px 0;
}
.input-daterange .input-group-addon {
	width: auto;
	min-width: 16px;
	padding: 4px 5px;
	font-weight: normal;
	line-height: 1.428571429;
	text-align: center;
	text-shadow: 0 1px 0 #fff;
	vertical-align: middle;
	background-color: #eeeeee;
	border: solid #cccccc;
	border-width: 1px 0;
	margin-left: -5px;
	margin-right: -5px;
}
.datepicker.dropdown-menu {
	position: absolute;
	top: 100%;
	left: 0;
	z-index: 1000;
	float: left;
	display: none;
	min-width: 160px;
	list-style: none;
	background-color: #ffffff;
	border: 1px solid #ccc;
	border: 1px solid rgba(0, 0, 0, 0.2);
	border-radius: 5px;
	-webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	-webkit-background-clip: padding-box;
	-moz-background-clip: padding;
	background-clip: padding-box;
	*border-right-width: 2px;
	*border-bottom-width: 2px;
	color: #333333;
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	font-size: 13px;
	line-height: 1.428571429;
}
.datepicker.dropdown-menu th,
.datepicker.datepicker-inline th,
.datepicker.dropdown-menu td,
.datepicker.datepicker-inline td {
	padding: 0px 5px;
}









/** RANGE PICKER
 ******************************************* **/
/*!
 * Stylesheet for the Date Range Picker, for use with Bootstrap 3.x
 *
 * Copyright 2013-2015 Dan Grossman ( http://www.dangrossman.info )
 * Licensed under the MIT license. See http://www.opensource.org/licenses/mit-license.php
 *
 * Built for http://www.improvely.com
 */

.daterangepicker.dropdown-menu {
	max-width: none;
	z-index: 3000;
}

.daterangepicker.opensleft .ranges, .daterangepicker.opensleft .calendar {
	float: left;
	margin: 4px;
}

.daterangepicker.opensright .ranges, .daterangepicker.opensright .calendar,
.daterangepicker.openscenter .ranges, .daterangepicker.openscenter .calendar {
	float: right;
	margin: 4px;
}

.daterangepicker.single .ranges, .daterangepicker.single .calendar {
	float: none;
}

.daterangepicker .ranges {
	width: 160px;
	text-align: left;
}

.daterangepicker .ranges .range_inputs>div {
	float: left;
}

.daterangepicker .ranges .range_inputs>div:nth-child(2) {
	padding-left: 11px;
}

.daterangepicker .calendar {
	display: none;
	max-width: 270px;
}

.daterangepicker.show-calendar .calendar {
	display: block;
}

.daterangepicker .calendar.single .calendar-date {
	border: none;
}

.daterangepicker .calendar th, .daterangepicker .calendar td {
	font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
	white-space: nowrap;
	text-align: center;
	min-width: 32px;
}

.daterangepicker .daterangepicker_start_input label,
.daterangepicker .daterangepicker_end_input label {
	color: #333;
	display: block;
	font-size: 11px;
	font-weight: normal;
	height: 20px;
	line-height: 20px;
	margin-bottom: 2px;
	text-shadow: #fff 1px 1px 0px;
	text-transform: uppercase;
	width: 74px;
}

.daterangepicker .ranges input {
	font-size: 11px;
}

.daterangepicker .ranges .input-mini {
	border: 1px solid #ccc;
	border-radius: 4px;
	color: #555;
	display: block;
	font-size: 11px;
	height: 30px;
	line-height: 30px;
	vertical-align: middle;
	margin: 0 0 10px 0;
	padding: 0 6px;
	width: 74px;
}

.daterangepicker .ranges ul {
	list-style: none;
	margin: 0;
	padding: 0;
}

.daterangepicker .ranges li {
	font-size: 13px;
	background: #f5f5f5;
	border: 1px solid #f5f5f5;
	color: #08c;
	padding: 3px 12px;
	margin-bottom: 8px;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	cursor: pointer;
}

.daterangepicker .ranges li.active, .daterangepicker .ranges li:hover {
	background: #08c;
	border: 1px solid #08c;
	color: #fff;
}

.daterangepicker .calendar-date {
	border: 1px solid #ddd;
	padding: 4px;
	border-radius: 4px;
	background: #fff;
}

.daterangepicker .calendar-time {
	text-align: center;
	margin: 8px auto 0 auto;
	line-height: 30px;
}

.daterangepicker {
	position: absolute;
	background: #fff;
	top: 100px;
	left: 20px;
	padding: 4px;
	margin-top: 1px;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
}

.daterangepicker.opensleft:before {
	position: absolute;
	top: -7px;
	right: 9px;
	display: inline-block;
	border-right: 7px solid transparent;
	border-bottom: 7px solid #ccc;
	border-left: 7px solid transparent;
	border-bottom-color: rgba(0, 0, 0, 0.2);
	content: '';
}

.daterangepicker.opensleft:after {
	position: absolute;
	top: -6px;
	right: 10px;
	display: inline-block;
	border-right: 6px solid transparent;
	border-bottom: 6px solid #fff;
	border-left: 6px solid transparent;
	content: '';
}

.daterangepicker.openscenter:before {
	position: absolute;
	top: -7px;
	left: 0;
	right: 0;
	width: 0;
	margin-left: auto;
	margin-right: auto;
	display: inline-block;
	border-right: 7px solid transparent;
	border-bottom: 7px solid #ccc;
	border-left: 7px solid transparent;
	border-bottom-color: rgba(0, 0, 0, 0.2);
	content: '';
}

.daterangepicker.openscenter:after {
	position: absolute;
	top: -6px;
	left: 0;
	right: 0;
	width: 0;
	margin-left: auto;
	margin-right: auto;
	display: inline-block;
	border-right: 6px solid transparent;
	border-bottom: 6px solid #fff;
	border-left: 6px solid transparent;
	content: '';
}

.daterangepicker.opensright:before {
	position: absolute;
	top: -7px;
	left: 9px;
	display: inline-block;
	border-right: 7px solid transparent;
	border-bottom: 7px solid #ccc;
	border-left: 7px solid transparent;
	border-bottom-color: rgba(0, 0, 0, 0.2);
	content: '';
}

.daterangepicker.opensright:after {
	position: absolute;
	top: -6px;
	left: 10px;
	display: inline-block;
	border-right: 6px solid transparent;
	border-bottom: 6px solid #fff;
	border-left: 6px solid transparent;
	content: '';
}

.daterangepicker.dropup{
	margin-top: -5px;
}
.daterangepicker.dropup:before{
	top: initial;
	bottom:-7px;
	border-bottom: initial;
	border-top: 7px solid #ccc;
}
.daterangepicker.dropup:after{
	top: initial;
	bottom:-6px;
	border-bottom: initial;
	border-top: 6px solid #fff;
}

.daterangepicker table {
	width: 100%;
	margin: 0;
}

.daterangepicker td, .daterangepicker th {
	text-align: center;
	width: 20px;
	height: 20px;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	cursor: pointer;
	white-space: nowrap;
}

.daterangepicker td.off {
	color: #999;
}

.daterangepicker td.disabled, .daterangepicker option.disabled {
	color: #999;
}

.daterangepicker td.available:hover, .daterangepicker th.available:hover {
	background: #eee;
}

.daterangepicker td.in-range {
	background: #ebf4f8;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
}

.daterangepicker td.start-date {
	-webkit-border-radius: 4px 0 0 4px;
	-moz-border-radius: 4px 0 0 4px;
	border-radius: 4px 0 0 4px;
}

.daterangepicker td.end-date {
	-webkit-border-radius: 0 4px 4px 0;
	-moz-border-radius: 0 4px 4px 0;
	border-radius: 0 4px 4px 0;
}

.daterangepicker td.start-date.end-date {
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
}

.daterangepicker td.active, .daterangepicker td.active:hover {
	background-color: #357ebd;
	border-color: #3071a9;
	color: #fff;
}

.daterangepicker td.week, .daterangepicker th.week {
	font-size: 80%;
	color: #ccc;
}

.daterangepicker select.monthselect, .daterangepicker select.yearselect {
	font-size: 12px;
	padding: 1px;
	height: auto;
	margin: 0;
	cursor: default;
}

.daterangepicker select.monthselect {
	margin-right: 2%;
	width: 56%;
}

.daterangepicker select.yearselect {
	width: 40%;
}

.daterangepicker select.hourselect, .daterangepicker select.minuteselect, .daterangepicker select.secondselect, .daterangepicker select.ampmselect {
	width: 50px;
	margin-bottom: 0;
}

.daterangepicker_start_input {
	float: left;
}

.daterangepicker_end_input {
	float: left;
	padding-left: 11px
}

.daterangepicker th.month {
	width: auto;
}



/** TIME PICKER
 ******************************************* **/
.time_pick .ti_tx,
.time_pick .mi_tx,
.time_pick .mer_tx {
	width: 100%;
	text-align: center;
	margin: 10px 0;
}

.time_pick .time,
.time_pick .mins,
.time_pick .meridian {
	width: 50px;
	float: left;
	margin: 0;
	font-size: 20px;
	color: #2d2e2e;
	font-family: arial;
	font-weight: 700;
}

.time_pick .prev,
.time_pick .next {
	position:relative;
	cursor: pointer;
	padding: 12px 18px;
	width: 28%;
	height:20px;
	border: 2px solid #ddd;
	margin: auto;
	text-align:center;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
section.dark .time_pick .prev,
section.dark .time_pick .next {
	border-color:#666;
	color:#fff;
}

.time_pick .prev:before,
.time_pick .next:before {
	position:absolute;
	left:8px;
	top:3px;
	content: "\e260";
	font-family: 'Glyphicons Halflings';
	display: inline-block;
	font-weight: 400;
	font-style:normal;
	margin:0;
	padding:0;
	line-height:1;
}
.time_pick .next:before {
	content: "\e259";
}
.time_pick .prev:hover,
.time_pick .next:hover {
	background-color: #ccc;
}

.time_pick .next {
	background-position: 50% 150%;
}

.time_pick .prev {
	background-position: 50% -50%;
}

.time_pick {
	position: relative;
}

.time_pick .timepicker_wrap {
	padding: 10px;
	z-index: 998;
	display: none;
	background: #fff;
	border: 2px solid #ddd;
	float: left;
	position: absolute;
	top:38px !important;
	left: 0;

	-webkit-border-bottom-right-radius: 3px;
	-webkit-border-bottom-left-radius: 3px;
	-moz-border-radius-bottomright: 3px;
	-moz-border-radius-bottomleft: 3px;
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px;

}
.time_pick input.timepicker + .timepicker_wrap {
	border-top:0;
}
section.dark .time_pick .timepicker_wrap {
	color:#eee;
	background:#212121;
	background:#333;
	border-color:#666;
}
.time_pick .arrow_top {
	position: absolute;
	top: -10px;
	left: 20px;
	width: 18px;
	height: 10px;
	z-index: 999;
}
.time_pick input.timepicki-input {
	background: none repeat scroll 0 0 #FFFFFF;
	border: 2px solid #ddd;
	float: none;
	margin: 0;
	text-align: center;
	width: 82%;
	font-weight:300;
	font-size:15px;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
section.dark .time_pick input.timepicki-input {
	color:#333;
	border-color:#666;
}
.time_pick a.reset_time {
	float: left;
	margin-top: 5px;
	color: #000;
}





/** Color PICKER v1.7.0
	https://github.com/bgrins/spectrum
 ******************************************* **/
input.colorpicker {
	display:inline-block !important;
}
.sp-container {
	position:absolute;
	top:0;
	left:0;
	display:inline-block;
	*display: inline;
	*zoom: 1;
	/* https://github.com/bgrins/spectrum/issues/40 */
	z-index: 9999994;
	overflow: hidden;
}
.sp-container.sp-flat {
	position: relative;
}

/* Fix for * { box-sizing: border-box; } */
.sp-container,
.sp-container * {
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
}

/* http://ansciath.tumblr.com/post/7347495869/css-aspect-ratio */
.sp-top {
	position:relative;
	width: 100%;
	display:inline-block;
}
.sp-top-inner {
	position:absolute;
	top:0;
	left:0;
	bottom:0;
	right:0;
}
.sp-color {
	position: absolute;
	top:0;
	left:0;
	bottom:0;
	right:20%;
}
.sp-hue {
	position: absolute;
	top:0;
	right:0;
	bottom:0;
	left:84%;
	height: 100%;
}

.sp-clear-enabled .sp-hue {
	top:33px;
	height: 77.5%;
}

.sp-fill {
	padding-top: 80%;
}
.sp-sat, .sp-val {
	position: absolute;
	top:0;
	left:0;
	right:0;
	bottom:0;
}

.sp-alpha-enabled .sp-top {
	margin-bottom: 18px;
}
.sp-alpha-enabled .sp-alpha {
	display: block;
}
.sp-alpha-handle {
	position:absolute;
	top:-4px;
	bottom: -4px;
	width: 6px;
	left: 50%;
	cursor: pointer;
	border: 1px solid black;
	background: white;
	opacity: .8;
}
.sp-alpha {
	display: none;
	position: absolute;
	bottom: -14px;
	right: 0;
	left: 0;
	height: 8px;
}
.sp-alpha-inner {
	border: solid 1px #333;
}

.sp-clear {
	display: none;
}

.sp-clear.sp-clear-display {
	background-position: center;
}

.sp-clear-enabled .sp-clear {
	display: block;
	position:absolute;
	top:0px;
	right:0;
	bottom:0;
	left:84%;
	height: 28px;
}

/* Don't allow text selection */
.sp-container, .sp-replacer, .sp-preview, .sp-dragger, .sp-slider, .sp-alpha, .sp-clear, .sp-alpha-handle, .sp-container.sp-dragging .sp-input, .sp-container button  {
	-webkit-user-select:none;
	-moz-user-select: -moz-none;
	-o-user-select:none;
	user-select: none;
}

.sp-container.sp-input-disabled .sp-input-container {
	display: none;
}
.sp-container.sp-buttons-disabled .sp-button-container {
	display: none;
}
.sp-container.sp-palette-buttons-disabled .sp-palette-button-container {
	display: none;
}
.sp-palette-only .sp-picker-container {
	display: none;
}
.sp-palette-disabled .sp-palette-container {
	display: none;
}

.sp-initial-disabled .sp-initial {
	display: none;
}


/* Gradients for hue, saturation and value instead of images.  Not pretty... but it works */
.sp-sat {
	background-image: -webkit-gradient(linear,  0 0, 100% 0, from(#FFF), to(rgba(204, 154, 129, 0)));
	background-image: -webkit-linear-gradient(left, #FFF, rgba(204, 154, 129, 0));
	background-image: -moz-linear-gradient(left, #fff, rgba(204, 154, 129, 0));
	background-image: -o-linear-gradient(left, #fff, rgba(204, 154, 129, 0));
	background-image: -ms-linear-gradient(left, #fff, rgba(204, 154, 129, 0));
	background-image: linear-gradient(to right, #fff, rgba(204, 154, 129, 0));
	-ms-filter: "progid:DXImageTransform.Microsoft.gradient(GradientType = 1, startColorstr=#FFFFFFFF, endColorstr=#00CC9A81)";
	filter : progid:DXImageTransform.Microsoft.gradient(GradientType = 1, startColorstr='#FFFFFFFF', endColorstr='#00CC9A81');
}
.sp-val {
	background-image: -webkit-gradient(linear, 0 100%, 0 0, from(#000000), to(rgba(204, 154, 129, 0)));
	background-image: -webkit-linear-gradient(bottom, #000000, rgba(204, 154, 129, 0));
	background-image: -moz-linear-gradient(bottom, #000, rgba(204, 154, 129, 0));
	background-image: -o-linear-gradient(bottom, #000, rgba(204, 154, 129, 0));
	background-image: -ms-linear-gradient(bottom, #000, rgba(204, 154, 129, 0));
	background-image: linear-gradient(to top, #000, rgba(204, 154, 129, 0));
	-ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#00CC9A81, endColorstr=#FF000000)";
	filter : progid:DXImageTransform.Microsoft.gradient(startColorstr='#00CC9A81', endColorstr='#FF000000');
}

.sp-hue {
	background: -moz-linear-gradient(top, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
	background: -ms-linear-gradient(top, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
	background: -o-linear-gradient(top, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
	background: -webkit-gradient(linear, left top, left bottom, from(#ff0000), color-stop(0.17, #ffff00), color-stop(0.33, #00ff00), color-stop(0.5, #00ffff), color-stop(0.67, #0000ff), color-stop(0.83, #ff00ff), to(#ff0000));
	background: -webkit-linear-gradient(top, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
	background: linear-gradient(to bottom, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
}

/* IE filters do not support multiple color stops.
   Generate 6 divs, line them up, and do two color gradients for each.
   Yes, really.
 */
.sp-1 {
	height:17%;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff0000', endColorstr='#ffff00');
}
.sp-2 {
	height:16%;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffff00', endColorstr='#00ff00');
}
.sp-3 {
	height:17%;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00ff00', endColorstr='#00ffff');
}
.sp-4 {
	height:17%;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00ffff', endColorstr='#0000ff');
}
.sp-5 {
	height:16%;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0000ff', endColorstr='#ff00ff');
}
.sp-6 {
	height:17%;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff00ff', endColorstr='#ff0000');
}

.sp-hidden {
	display: none !important;
}

/* Clearfix hack */
.sp-cf:before, .sp-cf:after { content: ""; display: table; }
.sp-cf:after { clear: both; }
.sp-cf { *zoom: 1; }

/* Mobile devices, make hue slider bigger so it is easier to slide */
@media (max-device-width: 480px) {
	.sp-color { right: 40%; }
	.sp-hue { left: 63%; }
	.sp-fill { padding-top: 60%; }
}
.sp-dragger {
	border-radius: 5px;
	height: 5px;
	width: 5px;
	border: 1px solid #fff;
	background: #000;
	cursor: pointer;
	position:absolute;
	top:0;
	left: 0;
}
.sp-slider {
	position: absolute;
	top:0;
	cursor:pointer;
	height: 3px;
	left: -1px;
	right: -1px;
	border: 1px solid #000;
	background: white;
	opacity: .8;
}

/*
Theme authors:
Here are the basic themeable display options (colors, fonts, global widths).
See http://bgrins.github.io/spectrum/themes/ for instructions.
*/

.sp-container {
	border-radius: 0;
	background-color: #eaeaea;
	border: solid 2px #ddd;
	padding: 0;
}
section.dark .sp-container {
	background-color: #262626;
	border-color:#666;
}
.sp-container, .sp-container button, .sp-container input, .sp-color, .sp-hue, .sp-clear {
	font: normal 12px Verdana, sans-serif;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	-ms-box-sizing: border-box;
	box-sizing: border-box;
}
.sp-top {
	margin-bottom: 3px;
}
.sp-color, .sp-hue, .sp-clear {
	border: solid 1px #666;
}

/* Input */
.sp-input-container {
	float:right;
	width: 100px;
	margin-bottom: 4px;
}
.sp-initial-disabled  .sp-input-container {
	width: 100%;
}
.sp-input {
	font-size: 12px !important;
	border: 1px inset;
	padding: 4px 5px;
	margin: 0;
	width: 100%;
	background:transparent;
	border-radius: 3px;
	color: #222;
}
.sp-input:focus  {
	border: 1px solid orange;
}
.sp-input.sp-validation-error {
	border: 1px solid red;
	background: #fdd;
}
.sp-picker-container , .sp-palette-container {
	float:left;
	position: relative;
	padding: 10px;
	padding-bottom: 300px;
	margin-bottom: -290px;
}
.sp-picker-container {
	width: 172px;
	border-left: solid 1px #fff;
}

/* Palettes */
.sp-palette-container {
	border-right: solid 1px #ccc;
}

.sp-palette-only .sp-palette-container {
	border: 0;
}

.sp-palette .sp-thumb-el {
	display: block;
	position:relative;
	float:left;
	width: 24px;
	height: 15px;
	margin: 3px;
	cursor: pointer;
	border:solid 2px transparent;
}
.sp-palette .sp-thumb-el:hover, .sp-palette .sp-thumb-el.sp-thumb-active {
	border-color: orange;
}
.sp-thumb-el {
	position:relative;
}

/* Initial */
.sp-initial {
	float: left;
	border: solid 1px #333;
}
.sp-initial span {
	width: 30px;
	height: 25px;
	border:none;
	display:block;
	float:left;
	margin:0;
}

.sp-initial .sp-clear-display {
	background-position: center;
}

/* Buttons */
.sp-palette-button-container,
.sp-button-container {
	float: right;
}

/* Replacer (the little preview div that shows up instead of the <input>) */
.sp-replacer {
	margin:0;
	overflow:hidden;
	cursor:pointer;
	padding: 4px;
	display:inline-block;
	*zoom: 1;
	*display: inline;
	border: solid 2px #ddd;
	background: #eee;
	color: #333;
	vertical-align: middle;
	height:40px;
}
section.dark .sp-replacer {
	border-color:#666;
}
.sp-replacer:hover, .sp-replacer.sp-active {
	border-color: #F0C49B;
	color: #111;
}
.sp-replacer.sp-disabled {
	cursor:default;
	border-color: silver;
	color: silver;
}
.sp-dd {
	padding: 2px 0;
	height: 16px;
	line-height: 25px;
	float:left;
	font-size:10px;
}
.sp-preview {
	position:relative;
	width:28px;
	height: 28px;
	border: solid 1px #222;
	margin-right: 5px;
	float:left;
	z-index: 0;
}

.sp-palette {
	*width: 220px;
	max-width: 220px;
}
.sp-palette .sp-thumb-el {
	width:16px;
	height: 16px;
	margin:2px 1px;
	border: solid 1px #d0d0d0;
}

.sp-container {
	padding-bottom:0;
}


/* Buttons: http://hellohappy.org/css3-buttons/ */
.sp-container button {
	background-color: #eeeeee;
	background-image: -webkit-linear-gradient(top, #eeeeee, #cccccc);
	background-image: -moz-linear-gradient(top, #eeeeee, #cccccc);
	background-image: -ms-linear-gradient(top, #eeeeee, #cccccc);
	background-image: -o-linear-gradient(top, #eeeeee, #cccccc);
	background-image: linear-gradient(to bottom, #eeeeee, #cccccc);
	border: 1px solid #ccc;
	border-bottom: 1px solid #bbb;
	border-radius: 3px;
	color: #333;
	font-size: 14px;
	line-height: 1;
	padding: 5px 4px;
	text-align: center;
	text-shadow: 0 1px 0 #eee;
	vertical-align: middle;
}
.sp-container button:hover {
	background-color: #dddddd;
	background-image: -webkit-linear-gradient(top, #dddddd, #bbbbbb);
	background-image: -moz-linear-gradient(top, #dddddd, #bbbbbb);
	background-image: -ms-linear-gradient(top, #dddddd, #bbbbbb);
	background-image: -o-linear-gradient(top, #dddddd, #bbbbbb);
	background-image: linear-gradient(to bottom, #dddddd, #bbbbbb);
	border: 1px solid #bbb;
	border-bottom: 1px solid #999;
	cursor: pointer;
	text-shadow: 0 1px 0 #ddd;
}
.sp-container button:active {
	border: 1px solid #aaa;
	border-bottom: 1px solid #888;
	-webkit-box-shadow: inset 0 0 5px 2px #aaaaaa, 0 1px 0 0 #eeeeee;
	-moz-box-shadow: inset 0 0 5px 2px #aaaaaa, 0 1px 0 0 #eeeeee;
	-ms-box-shadow: inset 0 0 5px 2px #aaaaaa, 0 1px 0 0 #eeeeee;
	-o-box-shadow: inset 0 0 5px 2px #aaaaaa, 0 1px 0 0 #eeeeee;
	box-shadow: inset 0 0 5px 2px #aaaaaa, 0 1px 0 0 #eeeeee;
}
.sp-cancel {
	font-size: 11px;
	color: #d93f3f !important;
	margin:0;
	padding:2px;
	margin-right: 5px;
	vertical-align: middle;
	text-decoration:none;

}
.sp-cancel:hover {
	color: #d93f3f !important;
	text-decoration: underline;
}


.sp-palette span:hover, .sp-palette span.sp-thumb-active {
	border-color: #000;
}

.sp-preview, .sp-alpha, .sp-thumb-el {
	position:relative;
	background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==');
}
.sp-preview-inner, .sp-alpha-inner, .sp-thumb-inner {
	display:block;
	position:absolute;
	top:0;left:0;bottom:0;right:0;
}

.sp-palette .sp-thumb-inner {
	background-position: 50% 50%;
	background-repeat: no-repeat;
}

.sp-palette .sp-thumb-light.sp-thumb-active .sp-thumb-inner {
	background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAIVJREFUeNpiYBhsgJFMffxAXABlN5JruT4Q3wfi/0DsT64h8UD8HmpIPCWG/KemIfOJCUB+Aoacx6EGBZyHBqI+WsDCwuQ9mhxeg2A210Ntfo8klk9sOMijaURm7yc1UP2RNCMbKE9ODK1HM6iegYLkfx8pligC9lCD7KmRof0ZhjQACDAAceovrtpVBRkAAAAASUVORK5CYII=');
}

.sp-palette .sp-thumb-dark.sp-thumb-active .sp-thumb-inner {
	background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjEwMPRyoQAAAMdJREFUOE+tkgsNwzAMRMugEAahEAahEAZhEAqlEAZhEAohEAYh81X2dIm8fKpEspLGvudPOsUYpxE2BIJCroJmEW9qJ+MKaBFhEMNabSy9oIcIPwrB+afvAUFoK4H0tMaQ3XtlrggDhOVVMuT4E5MMG0FBbCEYzjYT7OxLEvIHQLY2zWwQ3D+9luyOQTfKDiFD3iUIfPk8VqrKjgAiSfGFPecrg6HN6m/iBcwiDAo7WiBeawa+Kwh7tZoSCGLMqwlSAzVDhoK+6vH4G0P5wdkAAAAASUVORK5CYII=');
}

.sp-clear-display {
	background-repeat:no-repeat;
	background-position: center;
	background-image: url('data:image/gif;base64,R0lGODlhFAAUAPcAAAAAAJmZmZ2dnZ6enqKioqOjo6SkpKWlpaampqenp6ioqKmpqaqqqqurq/Hx8fLy8vT09PX19ff39/j4+Pn5+fr6+vv7+wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEAAP8ALAAAAAAUABQAAAihAP9FoPCvoMGDBy08+EdhQAIJCCMybCDAAYUEARBAlFiQQoMABQhKUJBxY0SPICEYHBnggEmDKAuoPMjS5cGYMxHW3IiT478JJA8M/CjTZ0GgLRekNGpwAsYABHIypcAgQMsITDtWJYBR6NSqMico9cqR6tKfY7GeBCuVwlipDNmefAtTrkSzB1RaIAoXodsABiZAEFB06gIBWC1mLVgBa0AAOw==');
}


input.colorpicker  {
	padding-right:65px;
}
input.colorpicker + .sp-replacer {
	right:0;
	width:55px;
	position:absolute;
	margin-left:-55px;
	z-index:10;
}

input[type=color].colorpicker {
	padding-right:12px !important;
	padding-left:12px !important;
	cursor:pointer;
}
input[type=color].colorpicker + .sp-replacer {
	display:none !important;
}




/**	39. Select2
*************************************************** **/
.select2-container {
	box-sizing: border-box;
	display: inline-block;
	margin: 0;
	position: relative;
	vertical-align: middle; }
.select2-container .select2-selection--single {
	box-sizing: border-box;
	cursor: pointer;
	display: block;
	height: 28px;
	user-select: none;
	-webkit-user-select: none; }
.select2-container .select2-selection--single .select2-selection__rendered {
	display: block;
	padding-left: 8px;
	padding-right: 20px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap; }
.select2-container[dir="rtl"] .select2-selection--single .select2-selection__rendered {
	padding-right: 8px;
	padding-left: 20px; }
.select2-container .select2-selection--multiple {
	box-sizing: border-box;
	cursor: pointer;
	display: block;
	min-height: 32px;
	user-select: none;
	-webkit-user-select: none; }
.select2-container .select2-selection--multiple .select2-selection__rendered {
	display: inline-block;
	overflow: hidden;
	padding-left: 8px;
	text-overflow: ellipsis;
	white-space: nowrap; }
.select2-container .select2-search--inline {
	float: left; }
.select2-container .select2-search--inline .select2-search__field {
	box-sizing: border-box;
	border: none;
	font-size: 100%;
	margin-top: 5px; }
.select2-container .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
	-webkit-appearance: none; }

.select2-dropdown {
	background-color: white;
	border: 1px solid #ddd;
	border-radius: 4px;
	box-sizing: border-box;
	display: block;
	position: absolute;
	left: -100000px;
	width: 100%;
	z-index: 1051; }
section.dark .select2-dropdown {
	border-color:#666;
}
.select2-results {
	display: block; }

.select2-results__options {
	list-style: none;
	margin: 0;
	padding: 0; }

.select2-results__option {
	padding: 6px;
	user-select: none;
	-webkit-user-select: none; }
.select2-results__option[aria-selected] {
	cursor: pointer; }

.select2-container--open .select2-dropdown {
	left: 0; }

.select2-container--open .select2-dropdown--above {
	border-bottom: none;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0; }

.select2-container--open .select2-dropdown--below {
	border-top: none;
	border-top-left-radius: 0;
	border-top-right-radius: 0; }

.select2-search--dropdown {
	display: block;
	padding: 4px; }
.select2-search--dropdown .select2-search__field {
	padding: 4px;
	width: 100%;
	box-sizing: border-box; }
.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
	-webkit-appearance: none; }
.select2-search--dropdown.select2-search--hide {
	display: none; }

.select2-close-mask {
	border: 0;
	margin: 0;
	padding: 0;
	display: block;
	position: fixed;
	left: 0;
	top: 0;
	min-height: 100%;
	min-width: 100%;
	height: auto;
	width: auto;
	opacity: 0;
	z-index: 99;
	background-color: #fff;
	filter: alpha(opacity=0); }

.select2-container--default .select2-selection--single {
	background-color: #fff;
	border: 2px solid #ddd;
	border-radius: 4px; }

section.dark .select2-container--default .select2-selection--single {
	border-color:#666;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
	color: #444;
	line-height: 28px; }
.select2-container--default .select2-selection--single .select2-selection__clear {
	cursor: pointer;
	float: right;
	font-weight: bold; }
.select2-container--default .select2-selection--single .select2-selection__placeholder {
	color: #999; }
.select2-container--default .select2-selection--single .select2-selection__arrow {
	height: 26px;
	position: absolute;
	top: 1px;
	right: 10px;
	width: 20px; }
.select2-container--default .select2-selection--single .select2-selection__arrow b {
	border-color: #888 transparent transparent transparent;
	border-style: solid;
	border-width: 5px 4px 0 4px;
	height: 0;
	left: 50%;
	margin-left: -4px;
	margin-top: -2px;
	position: absolute;
	top: 50%;
	width: 0; }
.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__clear {
	float: left; }
.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__arrow {
	left: 1px;
	right: auto; }
.select2-container--default.select2-container--disabled .select2-selection--single {
	background-color: #eee;
	cursor: default; }
.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {
	display: none; }
.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
	border-color: transparent transparent #888 transparent;
	border-width: 0 4px 5px 4px; }
.select2-container--default .select2-selection--multiple {
	background-color: white;
	border: 1px solid #aaa;
	border-radius: 4px;
	cursor: text; }
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
	box-sizing: border-box;
	list-style: none;
	margin: 0;
	padding: 0 5px;
	width: 100%; }
.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
	color: #999;
	margin-top: 5px;
	float: left; }
.select2-container--default .select2-selection--multiple .select2-selection__clear {
	cursor: pointer;
	float: right;
	font-weight: bold;
	margin-top: 5px;
	margin-right: 10px; }
.select2-container--default .select2-selection--multiple .select2-selection__choice {
	background-color: #e4e4e4;
	border: 1px solid #aaa;
	border-radius: 4px;
	cursor: default;
	float: left;
	margin-right: 5px;
	margin-top: 5px;
	padding: 0 5px; }
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
	color: #999;
	cursor: pointer;
	display: inline-block;
	font-weight: bold;
	margin-right: 2px; }
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
	color: #333; }
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice, .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__placeholder {
	float: right; }
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
	margin-left: 5px;
	margin-right: auto; }
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
	margin-left: 2px;
	margin-right: auto; }
.select2-container--default.select2-container--focus .select2-selection--multiple {
	border: solid black 1px;
	outline: 0; }
.select2-container--default.select2-container--disabled .select2-selection--multiple {
	background-color: #eee;
	cursor: default; }
.select2-container--default.select2-container--disabled .select2-selection__choice__remove {
	display: none; }
.select2-container--default.select2-container--open.select2-container--above .select2-selection--single, .select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
	border-top-left-radius: 0;
	border-top-right-radius: 0; }
.select2-container--default.select2-container--open.select2-container--below .select2-selection--single, .select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0; }
.select2-container--default .select2-search--dropdown .select2-search__field {
	border: 1px solid #aaa; }
.select2-container--default .select2-search--inline .select2-search__field {
	background: transparent;
	border: none;
	outline: 0; }
.select2-container--default .select2-results > .select2-results__options {
	max-height: 200px;
	overflow-y: auto; }
.select2-container--default .select2-results__option[role=group] {
	padding: 0; }
.select2-container--default .select2-results__option[aria-disabled=true] {
	color: #999; }
.select2-container--default .select2-results__option[aria-selected=true] {
	background-color: #ddd; }
.select2-container--default .select2-results__option .select2-results__option {
	padding-left: 1em; }
.select2-container--default .select2-results__option .select2-results__option .select2-results__group {
	padding-left: 0; }
.select2-container--default .select2-results__option .select2-results__option .select2-results__option {
	margin-left: -1em;
	padding-left: 2em; }
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
	margin-left: -2em;
	padding-left: 3em; }
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
	margin-left: -3em;
	padding-left: 4em; }
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
	margin-left: -4em;
	padding-left: 5em; }
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
	margin-left: -5em;
	padding-left: 6em; }
.select2-container--default .select2-results__option--highlighted[aria-selected] {
	background-color: #5897fb;
	color: white; }
.select2-container--default .select2-results__group {
	cursor: default;
	display: block;
	padding: 6px; }

.select2-container--classic .select2-selection--single {
	background-color: #f6f6f6;
	border: 1px solid #aaa;
	border-radius: 4px;
	outline: 0;
	background-image: -webkit-linear-gradient(top, #ffffff 50%, #eeeeee 100%);
	background-image: -o-linear-gradient(top, #ffffff 50%, #eeeeee 100%);
	background-image: linear-gradient(to bottom, #ffffff 50%, #eeeeee 100%);
	background-repeat: repeat-x;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee', GradientType=0); }
.select2-container--classic .select2-selection--single:focus {
	border: 1px solid #5897fb; }
.select2-container--classic .select2-selection--single .select2-selection__rendered {
	color: #444;
	line-height: 28px; }
.select2-container--classic .select2-selection--single .select2-selection__clear {
	cursor: pointer;
	float: right;
	font-weight: bold;
	margin-right: 10px; }
.select2-container--classic .select2-selection--single .select2-selection__placeholder {
	color: #999; }
.select2-container--classic .select2-selection--single .select2-selection__arrow {
	background-color: #ddd;
	border: none;
	border-left: 1px solid #aaa;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
	height: 26px;
	position: absolute;
	top: 1px;
	right: 1px;
	width: 20px;
	background-image: -webkit-linear-gradient(top, #eeeeee 50%, #cccccc 100%);
	background-image: -o-linear-gradient(top, #eeeeee 50%, #cccccc 100%);
	background-image: linear-gradient(to bottom, #eeeeee 50%, #cccccc 100%);
	background-repeat: repeat-x;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#cccccc', GradientType=0); }
.select2-container--classic .select2-selection--single .select2-selection__arrow b {
	border-color: #888 transparent transparent transparent;
	border-style: solid;
	border-width: 5px 4px 0 4px;
	height: 0;
	left: 50%;
	margin-left: -4px;
	margin-top: -2px;
	position: absolute;
	top: 50%;
	width: 0; }
.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__clear {
	float: left; }
.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__arrow {
	border: none;
	border-right: 1px solid #aaa;
	border-radius: 0;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
	left: 1px;
	right: auto; }
.select2-container--classic.select2-container--open .select2-selection--single {
	border: 1px solid #5897fb; }
.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow {
	background: transparent;
	border: none; }
.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {
	border-color: transparent transparent #888 transparent;
	border-width: 0 4px 5px 4px; }
.select2-container--classic.select2-container--open.select2-container--above .select2-selection--single {
	border-top: none;
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	background-image: -webkit-linear-gradient(top, #ffffff 0%, #eeeeee 50%);
	background-image: -o-linear-gradient(top, #ffffff 0%, #eeeeee 50%);
	background-image: linear-gradient(to bottom, #ffffff 0%, #eeeeee 50%);
	background-repeat: repeat-x;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee', GradientType=0); }
.select2-container--classic.select2-container--open.select2-container--below .select2-selection--single {
	border-bottom: none;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0;
	background-image: -webkit-linear-gradient(top, #eeeeee 50%, #ffffff 100%);
	background-image: -o-linear-gradient(top, #eeeeee 50%, #ffffff 100%);
	background-image: linear-gradient(to bottom, #eeeeee 50%, #ffffff 100%);
	background-repeat: repeat-x;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0); }
.select2-container--classic .select2-selection--multiple {
	background-color: white;
	border: 1px solid #aaa;
	border-radius: 4px;
	cursor: text;
	outline: 0; }
.select2-container--classic .select2-selection--multiple:focus {
	border: 1px solid #5897fb; }
.select2-container--classic .select2-selection--multiple .select2-selection__rendered {
	list-style: none;
	margin: 0;
	padding: 0 5px; }
.select2-container--classic .select2-selection--multiple .select2-selection__clear {
	display: none; }
.select2-container--classic .select2-selection--multiple .select2-selection__choice {
	background-color: #e4e4e4;
	border: 1px solid #aaa;
	border-radius: 4px;
	cursor: default;
	float: left;
	margin-right: 5px;
	margin-top: 5px;
	padding: 0 5px; }
.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {
	color: #888;
	cursor: pointer;
	display: inline-block;
	font-weight: bold;
	margin-right: 2px; }
.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {
	color: #555; }
.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
	float: right; }
.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
	margin-left: 5px;
	margin-right: auto; }
.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
	margin-left: 2px;
	margin-right: auto; }
.select2-container--classic.select2-container--open .select2-selection--multiple {
	border: 1px solid #5897fb; }
.select2-container--classic.select2-container--open.select2-container--above .select2-selection--multiple {
	border-top: none;
	border-top-left-radius: 0;
	border-top-right-radius: 0; }
.select2-container--classic.select2-container--open.select2-container--below .select2-selection--multiple {
	border-bottom: none;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0; }
.select2-container--classic .select2-search--dropdown .select2-search__field {
	border: 1px solid #aaa;
	outline: 0; }
.select2-container--classic .select2-search--inline .select2-search__field {
	outline: 0; }
.select2-container--classic .select2-dropdown {
	background-color: white;
	border: 1px solid transparent; }
.select2-container--classic .select2-dropdown--above {
	border-bottom: none; }
.select2-container--classic .select2-dropdown--below {
	border-top: none; }
.select2-container--classic .select2-results > .select2-results__options {
	max-height: 200px;
	overflow-y: auto; }
.select2-container--classic .select2-results__option[role=group] {
	padding: 0; }
.select2-container--classic .select2-results__option[aria-disabled=true] {
	color: grey; }
.select2-container--classic .select2-results__option--highlighted[aria-selected] {
	background-color: #3875d7;
	color: white; }
.select2-container--classic .select2-results__group {
	cursor: default;
	display: block;
	padding: 6px; }
.select2-container--classic.select2-container--open .select2-dropdown {
	border-color: #5897fb; }


/** Rewrite Select2
 ********************** **/
/* the same height as input element */
.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--single .select2-selection__rendered,
.select2-container--default .select2-selection--single .select2-selection__arrow {
	height:40px;
	line-height:36px;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
.select2-dropdown {
	border-color:#ddd;
	border-width:2px;
}
.fancy-form .select2-selection__arrow {
	display:none;
}
.select2-container--default .select2-selection--single {
	background-color:transparent;
}



/**	40. Tabs
*************************************************** **/
.nav .open>a,
.nav .open>a:focus,
.nav .open>a:hover {
	border-color:transparent !important;
	background-color:transparent !important;
}

.nav-tabs>li>a {
	color:#111;
	font-weight:bold;

	-webkit-border-radius: 3px !important;
	-moz-border-radius: 3px !important;
	border-radius: 3px !important;
}
.nav-tabs>li.active>a {
	color:#000;
}
.nav-tabs.nav-justified>li>a {
	-webkit-border-radius: 0 !important;
	-moz-border-radius: 0 !important;
	border-radius: 0 !important;
}
section.dark .nav-tabs>li>a {
	color:#fff;
}
section.dark .nav-tabs.nav-justified>li>a {
	border-bottom-color:#666;
}
.tab-content {
	padding:15px 0;
}


/* clean Tabs */
.nav-tabs.nav-clean>li>a {
	border:0 !important;
}
.nav-tabs.nav-clean>li>a:hover {
	background-color:transparent;
}
.nav-tabs.nav-clean>li.active {
	margin-bottom:0;
}


section.dark .nav-tabs.nav-clean>li>a,
section.dark .nav-tabs.nav-clean>li.active>a {
	color:#fff;
	background-color:transparent !important;
}

/* Top Border */
.nav-tabs.nav-top-border>li>a {
	border-top:transparent 3px solid !important;
}
.nav-tabs.nav-top-border>li.active>a,
.nav-tabs.nav-top-border>li.active>a:hover {
	border-top:#888 3px solid !important;

	-webkit-border-radius: 0 !important;
	-moz-border-radius: 0 !important;
	border-radius: 0 !important;
}
.nav-tabs.nav-top-border>li>a:hover {
	background-color:transparent !important;
	border-color:transparent !important;
}



/* Bottom Border */
.nav-tabs.nav-bottom-border {
	border:0 !important;
}
.nav-tabs.nav-bottom-border>li.active {
	margin-bottom:0 !important;
}
.nav-tabs.nav-bottom-border>li>a {
	border:0 !important;
	border-bottom:transparent 3px solid !important;
}
.nav-tabs.nav-bottom-border>li.active>a,
.nav-tabs.nav-bottom-border>li.active>a:hover {
	border-bottom:#888 3px solid !important;

	-webkit-border-radius: 0 !important;
	-moz-border-radius: 0 !important;
	border-radius: 0 !important;
}
.nav-tabs.nav-bottom-border>li>a:hover {
	background-color:transparent !important;
	border-color:transparent !important;
}

section.dark ul.side-nav a {
	color:#ccc;
}
section.dark .nav-bottom-border li>a {
	background-color:transparent !important;
}

/* Button tabs */
.nav-tabs.nav-button-tabs {
	border:0 !important;
}
.nav-tabs.nav-button-tabs>li>a {
	color:#000 !important;
	border:0 !important;
	background-color:rgba(0,0,0,0.1);
	margin-right: 3px !important;

	-webkit-border-radius: 3px !important;
	-moz-border-radius: 3px !important;
	border-radius: 3px !important;
}
.nav-tabs.nav-button-tabs>li.active>a {
	color:#fff !important;
	background-color:rgba(0,0,0,0.6);
}

section.dark .nav-tabs.nav-button-tabs>li>a {
	color:#fff !important;
}


/* Stacked Tabs */
.nav-tabs.nav-stacked {
	background-color:rgba(0,0,0,0.02);
}
.nav-tabs.nav-stacked>li>a {
	border-left:0 !important;
	border-right:0 !important;
	border:0 !important;
	background-color:#fff;
	color:#111;

	-webkit-border-radius: 0 !important;
	-moz-border-radius: 0 !important;
	border-radius: 0 !important;
}
.nav-tabs.nav-stacked>li.active>a {
	background-color:#fafafa;
}
div.tab-content.tab-stacked {
	padding:17px;
	background-color:#fafafa;
}
section.dark div.tab-content.tab-stacked {
	background-color:#444;
}
section.dark .nav-tabs.nav-stacked>li>a {
	background-color:#666;
}
section.dark .nav-tabs.nav-stacked>li.active>a {
	background-color:#444;
	margin:0;
}

@media only screen and (max-width: 480px) {
	.nav-tabs>li {
		margin-bottom:3px;
	}
	.nav-tabs>li,
	.nav-tabs>li>a {
		display:block !important;
		float:none !important;
		border:0 !important;
		background-color:rgba(0,0,0,0.01);
	}
	.nav-tabs>li>a :focus,
	.nav-tabs>li.active>a {
		background-color:rgba(0,0,0,0.05);
	}
}

/* Stacked Alternate */
.nav-tabs.nav-alternate {
	background-color:transparent;
}
div.tab-content.nav-alternate {
	background-color:transparent;
	padding:0;
}
.nav-tabs.nav-alternate>li>a {
	background-color:#fafafa;
}
.nav-tabs.nav-alternate>li.active>a {
	color:#fff !important;
	background-color:#999;
}
section.dark .tab-content.nav-alternate {
	background-color:transparent !important;
}


/** 41. Toggles & Accordions
*************************************************** **/
div.toggle {
	margin: 10px 0 0;
	position: relative;
	clear: both;
}

div.toggle > label {
	color: #333;
	background:rgba(0,0,0,0.1);

	cursor: pointer;
	font-size: 16px;
	font-weight:normal;
	padding: 10px 20px;
	position: relative;
	display: block;
	border-bottom: rgba(0,0,0,0.03) 1px solid;

	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;

	-webkit-transition: all .10s ease-out;
	-moz-transition: all .10s ease-out;
	-o-transition: all .10s ease-out;
	transition: all .10s ease-out;

	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
section.dark div.toggle > label {
	color: #fff;
	background:rgba(0,0,0,0.5);
}

div.toggle div.toggle-content {
	border: rgba(0,0,0,0.03) 1px solid;
	display: none;
	background:rgba(0,0,0,0.03);
	margin-top: -5px;
	padding: 15px 20px;

	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
}
div.toggle div.toggle-content p:last-child {
	margin-bottom:0;
}
section.dark div.toggle div.toggle-content {
	background:rgba(255,255,255,0.05);
}

div.toggle > label:before {
	content: '';
	border: 6px solid transparent;
	border-top-color: inherit;

	position: absolute;
	top: 50%; right: 14px;
	margin-top: -3px;
}

div.toggle > label:hover {
	background: rgba(0,0,0,0.15);
}

div.toggle > label + p {
	color: #888;
	height: 25px;
	display: block;
	overflow: hidden;
	padding-left: 10px;
}

div.toggle.active > label:before {
	border: 6px solid transparent;
	border-bottom-color:rgba(0,0,0,0.5);
	right: 14px; margin-top: -10px;
}

.toggle .toggle-content {
	border:0 !important;
}

/* transparent body */
.toggle.toggle-transparent .toggle-content,
.toggle.toggle-transparent-body .toggle-content {
	background:transparent;
}
.toggle.toggle-transparent-body label {
	background: rgba(0,0,0,0.03);
}
.toggle.toggle-transparent-body label:hover {
	background: rgba(0,0,0,0.04);
}

/* transparent full */
.toggle.toggle-transparent label:hover,
.toggle.toggle-transparent label {
	background-color:transparent;
	border:0;
}
.toggle.toggle-transparent div.toggle.active label {
	font-weight:bold;
}

/* bordered simple */
.toggle.toggle-bordered-simple label:hover,
.toggle.toggle-bordered-simple label {
	border:#ccc 1px solid;
}


/* bordered full */
.toggle.toggle-bordered-full label:hover,
.toggle.toggle-bordered-full label {
	border:#ccc 1px solid;
	border-bottom:0;
	margin:0;
}
.toggle.toggle-bordered-full div.toggle:last-child>label {
	border-bottom:#ccc 1px solid;
}
.toggle.toggle-bordered-full div.toggle.active>label {
	font-weight:bold;
}
.toggle.toggle-bordered-full .toggle-content {
	border:#ccc 1px solid !important;
	border-top:0 !important;
	border-bottom:0 !important;
}
.toggle.toggle-bordered-full div.toggle {
	margin:0;
}

/* noicon */
.toggle.toggle-noicon div.toggle > label:before {
	display:none;
}


/**	42. Box Shadow
	http://www.paulund.co.uk/creating-different-css3-box-shadows-effects
**************************************************************** **/
.box-shadow-1:after,
.box-shadow-1:before {
	top: 80%;
	left: 5px;
	width: 50%;
	z-index: -1;
	content: "";
	bottom: 15px;
	max-width: 300px;
	position: absolute;
}

/* Shadow 1 */
.box-shadow-1 {
	-webkit-box-shadow: 0 14px 6px -6px #666;
	-moz-box-shadow: 0 14px 6px -6px #666;
	box-shadow: 0 14px 6px -6px #666;
}
section.dark .box-shadow-1 {
	-webkit-box-shadow: 0 14px 6px -6px #111;
	-moz-box-shadow: 0 14px 6px -6px #111;
	box-shadow: 0 14px 6px -6px #111;
}


/* Shadow 2 */
.box-shadow-2 {
	position: relative;
}
.box-shadow-2:before,
.box-shadow-2:after {
	z-index: -1;
	position: absolute;
	content: "";
	bottom: 15px;
	left: 10px;
	width: 50%;
	top: 80%;
	max-width:300px;
	background: #777;

	-webkit-box-shadow: 0 15px 10px #777;
	-moz-box-shadow: 0 15px 10px #777;
	box-shadow: 0 15px 10px #777;

	-webkit-transform: rotate(-3deg);
	-moz-transform: rotate(-3deg);
	-o-transform: rotate(-3deg);
	-ms-transform: rotate(-3deg);
	transform: rotate(-3deg);
}
.box-shadow-2:after {
	right: 10px;
	left: auto;

	-webkit-transform: rotate(3deg);
	-moz-transform: rotate(3deg);
	-o-transform: rotate(3deg);
	-ms-transform: rotate(3deg);
	transform: rotate(3deg);
}
section.dark .box-shadow-2:before,
section.dark .box-shadow-2:after {
	background: #111;
	-webkit-box-shadow: 0 15px 10px #111;
	-moz-box-shadow: 0 15px 10px #111;
	box-shadow: 0 15px 10px #111;
}


/* Shadow 3 */
.box-shadow-3 {
	position: relative;
}
.box-shadow-3:before {
	z-index: -1;
	position: absolute;
	content: "";
	bottom: 15px;
	left: 10px;
	width: 50%;
	top: 80%;
	max-width:300px;
	background: #777;

	-webkit-box-shadow: 0 15px 10px #777;
	-moz-box-shadow: 0 15px 10px #777;
	box-shadow: 0 15px 10px #777;

	-webkit-transform: rotate(-3deg);
	-moz-transform: rotate(-3deg);
	-o-transform: rotate(-3deg);
	-ms-transform: rotate(-3deg);
	transform: rotate(-3deg);
}

section.dark .box-shadow-3:before {
	background: #111;
	-webkit-box-shadow: 0 15px 10px #111;
	-moz-box-shadow: 0 15px 10px #111;
	box-shadow: 0 15px 10px #111;
}

/* Shadow 4 */
.box-shadow-4 {
	position: relative;
}
.box-shadow-4:after {
	z-index: -1;
	position: absolute;
	content: "";
	bottom: 15px;
	right: 10px;
	left: auto;
	width: 50%;
	top: 80%;
	max-width:300px;
	background: #777;

	-webkit-box-shadow: 0 15px 10px #777;
	-moz-box-shadow: 0 15px 10px #777;
	box-shadow: 0 15px 10px #777;

	-webkit-transform: rotate(3deg);
	-moz-transform: rotate(3deg);
	-o-transform: rotate(3deg);
	-ms-transform: rotate(3deg);
	transform: rotate(3deg);
}
section.dark .box-shadow-4:after {
	background: #111;
	-webkit-box-shadow: 0 15px 10px #111;
	-moz-box-shadow: 0 15px 10px #111;
	box-shadow: 0 15px 10px #111;
}


/* Shadow 5 */
.box-shadow-5 {
	position: relative;
}
.box-shadow-5:before,
.box-shadow-5:after {
	z-index: -1;
	position: absolute;
	content: "";
	bottom: 25px;
	left: 10px;
	width: 50%;
	top: 80%;
	max-width:300px;
	background: #777;

	-webkit-box-shadow: 0 35px 20px #777;
	-moz-box-shadow: 0 35px 20px #777;
	box-shadow: 0 35px 20px #777;

	-webkit-transform: rotate(-8deg);
	-moz-transform: rotate(-8deg);
	-o-transform: rotate(-8deg);
	-ms-transform: rotate(-8deg);
	transform: rotate(-8deg);
}
.box-shadow-5:after {
	-webkit-transform: rotate(8deg);
	-moz-transform: rotate(8deg);
	-o-transform: rotate(8deg);
	-ms-transform: rotate(8deg);
	transform: rotate(8deg);
	right: 10px;
	left: auto;
}
section.dark .box-shadow-5:before,
section.dark .box-shadow-5:after {
	background: #111;
	-webkit-box-shadow: 0 35px 20px #111;
	-moz-box-shadow: 0 35px 20px #111;
	box-shadow: 0 35px 20px #111;
}


/* Shadow 6 */
.box-shadow-6 {
	position:relative;
	-webkit-box-shadow:0 1px 4px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 0, 0, 0.1) inset;
	-moz-box-shadow:0 1px 4px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 0, 0, 0.1) inset;
	box-shadow:0 1px 4px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 0, 0, 0.1) inset;
}
.box-shadow-6:before,
.box-shadow-6:after {
	content:"";
	position:absolute;
	z-index:-1;

	-webkit-box-shadow:0 0 20px rgba(0,0,0,0.8);
	-moz-box-shadow:0 0 20px rgba(0,0,0,0.8);
	box-shadow:0 0 20px rgba(0,0,0,0.8);

	top:50%;
	bottom:0;
	left:10px;
	right:10px;
	-moz-border-radius:100px / 10px;
	border-radius:100px / 10px;
}
section.dark .box-shadow-6 {
	position:relative;
	-webkit-box-shadow:0 1px 4px rgba(255, 255, 255, 0.3), 0 0 40px rgba(255, 255, 255, 0.1) inset;
	-moz-box-shadow:0 1px 4px rgba(255, 255, 255, 0.3), 0 0 40px rgba(255, 255, 255, 0.1) inset;
	box-shadow:0 1px 4px rgba(255, 255, 255, 0.3), 0 0 40px rgba(255, 255, 255, 0.1) inset;
}
section.dark .box-shadow-6:before,
section.dark .box-shadow-6:after {
	-webkit-box-shadow:0 0 20px rgba(255,255,255,0.8);
	-moz-box-shadow:0 0 20px rgba(255,255,255,0.8);
	box-shadow:0 0 20px rgba(255,255,255,0.8);
}

/* Shadow 7 */
.box-shadow-7 {
	position:relative;
	-webkit-box-shadow:0 1px 4px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 0, 0, 0.1) inset;
	-moz-box-shadow:0 1px 4px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 0, 0, 0.1) inset;
	box-shadow:0 1px 4px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 0, 0, 0.1) inset;
}
.box-shadow-7:before,
.box-shadow-7:after {
	content:"";
	position:absolute;
	z-index:-1;

	-webkit-box-shadow:0 0 20px rgba(0,0,0,0.8);
	-moz-box-shadow:0 0 20px rgba(0,0,0,0.8);
	box-shadow:0 0 20px rgba(0,0,0,0.8);

	top:0;
	bottom:0;
	left:10px;
	right:10px;
	-moz-border-radius:100px / 10px;
	border-radius:100px / 10px;
}
.box-shadow-7:after {
	right:10px;
	left:auto;
	-webkit-transform:skew(8deg) rotate(3deg);
	-moz-transform:skew(8deg) rotate(3deg);
	-ms-transform:skew(8deg) rotate(3deg);
	-o-transform:skew(8deg) rotate(3deg);
	transform:skew(8deg) rotate(3deg);
}
section.dark .box-shadow-7 {
	position:relative;
	-webkit-box-shadow:0 1px 4px rgba(255, 255, 255, 0.3), 0 0 40px rgba(255, 255, 255, 0.1) inset;
	-moz-box-shadow:0 1px 4px rgba(255, 255, 255, 0.3), 0 0 40px rgba(255, 255, 255, 0.1) inset;
	box-shadow:0 1px 4px rgba(255, 255, 255, 0.3), 0 0 40px rgba(255, 255, 255, 0.1) inset;
}
section.dark .box-shadow-7:before,
section.dark .box-shadow-7:after {
	-webkit-box-shadow:0 0 20px rgba(255,255,255,0.8);
	-moz-box-shadow:0 0 20px rgba(255,255,255,0.8);
	box-shadow:0 0 20px rgba(255,255,255,0.8);
}


/* Shadow 8 */
.box-shadow-8 {
	position:relative;
	-webkit-box-shadow:0 1px 4px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 0, 0, 0.1) inset;
	-moz-box-shadow:0 1px 4px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 0, 0, 0.1) inset;
	box-shadow:0 1px 4px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 0, 0, 0.1) inset;
}
.box-shadow-8:before,
.box-shadow-8:after {
	content:"";
	position:absolute;
	z-index:-1;

	-webkit-box-shadow:0 0 20px rgba(0,0,0,0.8);
	-moz-box-shadow:0 0 20px rgba(0,0,0,0.8);
	box-shadow:0 0 20px rgba(0,0,0,0.8);

	top:10px;
	bottom:10px;
	left:0;
	right:0;
	-moz-border-radius:100px / 10px;
	border-radius:100px / 10px;
}
.box-shadow-8:after {
	right:10px;
	left:auto;

	-webkit-transform:skew(8deg) rotate(3deg);
	-moz-transform:skew(8deg) rotate(3deg);
	-ms-transform:skew(8deg) rotate(3deg);
	-o-transform:skew(8deg) rotate(3deg);
	transform:skew(8deg) rotate(3deg);
}
section.dark .box-shadow-8 {
	position:relative;
	-webkit-box-shadow:0 1px 4px rgba(255, 255, 255, 0.3), 0 0 40px rgba(0, 0, 0, 0.1) inset;
	-moz-box-shadow:0 1px 4px rgba(255, 255, 255, 0.3), 0 0 40px rgba(0, 0, 0, 0.1) inset;
	box-shadow:0 1px 4px rgba(255, 255, 255, 0.3), 0 0 40px rgba(0, 0, 0, 0.1) inset;
}
section.dark .box-shadow-8:before,
section.dark .box-shadow-8:after {
	-webkit-box-shadow:0 0 20px rgba(255, 255, 255,0.8);
	-moz-box-shadow:0 0 20px rgba(255, 255, 255,0.8);
	box-shadow:0 0 20px rgba(255, 255, 255,0.8);
}






/**	43. Testimonials
**************************************************************** **/
ul.testimonial-dotted {
	overflow:hidden;
}
ul.testimonial-dotted>li {
	padding-top:30px;
	padding-bottom:30px;
}
ul.testimonial-dotted>li figure {
	text-align:center;
	display:block;
}
ul.testimonial-dotted>li img {
	display:inline-block;
}
ul.testimonial-dotted>li:after {
	content: '';
	position: absolute;

	width: 100%;
	height: 0;
	top: auto;
	left: 0;
	bottom: -1px;
	border-bottom: 1px dashed rgba(0,0,0,0.3);
}
ul.testimonial-dotted>li:before {
	content: '';
	position: absolute;
	height: 100%;
	top: 0;
	left: -1px;
	border-left: 1px dashed rgba(0,0,0,0.3);
}

section.dark ul.testimonial-dotted>li:after {
	border-bottom: 1px dashed rgba(255,255,255,0.3);
}
section.dark ul.testimonial-dotted>li:before {
	border-left: 1px dashed rgba(255,255,255,0.3);
}

div.testimonial p {
	margin-top:0;
}

div.testimonial>figure>img {
	width:65px;
	height:65px;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}

.testimonial cite {
	font-weight:bold;
	text-shadow:none;
}
.testimonial cite span {
	display:block;
	color:#888;
	font-size:12px;
	font-weight:300;
}
section.parallax .testimonial cite span {
	color:#ddd;
}
.testimonial-content {
	position:relative;
	padding-left:75px;
}

/* owl carousel testimonial */
div.owl-carousel.owl-testimonial .testimonial {
	margin-bottom:50px;
	display:block;
}

/* owl mini carousel testimonial */
div.owl-carousel.owl-mini-testimonial {
	text-align:left;
}
div.owl-carousel.owl-mini-testimonial .testimonial-content {
	text-align:left;
}
div.owl-carousel.owl-mini-testimonial .testimonial {
	margin-bottom:30px;
}
div.owl-carousel.owl-mini-testimonial .testimonial img {
	float:left;
	margin-right:20px;
}
div.owl-carousel.owl-mini-testimonial .testimonial p {
	margin:0;
}
div.owl-carousel.owl-mini-testimonial .owl-controls {
	text-align:right;
}
div.owl-carousel.owl-mini-testimonial .owl-controls .owl-page span {
	width:6px;
	height:6px;
	margin:5px 4px;
}


/* bordered */
div.testimonial-bordered {
	border:#eaeaea 2px solid;
	padding:30px 15px;
	margin:30px 0;
}
section.dark div.testimonial-bordered  {
	border-color:#666;
	background-color:rgba(0,0,0,0.1);
}



/**	44. Flexslider
**************************************************************** **/
.flex-container a:hover,
.flex-slider a:hover,
.flex-container a:focus,
.flex-slider a:focus {
	outline: none;
}
.slides,
.slides > li,
.flex-control-nav,
.flex-direction-nav {
	margin: 0;
	padding: 0;
	list-style: none;
}
.flex-pauseplay span {
	text-transform: capitalize;
}
/* ====================================================================================================================
 * BASE STYLES
 * ====================================================================================================================*/
.flexslider {
	margin: 0;
	padding: 0;
}
.flexslider .slides > li {
	display: none;
	-webkit-backface-visibility: hidden;
}
.flexslider .slides img {
	width: 100%;
	display: block;
}
.flexslider .slides:after {
	content: "\0020";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}
html[xmlns] .flexslider .slides {
	display: block;
}
* html .flexslider .slides {
	height: 1%;
}
.no-js .flexslider .slides > li:first-child {
	display: block;
}
/* ====================================================================================================================
 * DEFAULT THEME
 * ====================================================================================================================*/
.flexslider {
	margin: 0 0 60px;
	background: #ffffff;
	border: 4px solid #ffffff;
	position: relative;
	zoom: 1;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	-webkit-box-shadow: '' 0 1px 4px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: '' 0 1px 4px rgba(0, 0, 0, 0.2);
	-o-box-shadow: '' 0 1px 4px rgba(0, 0, 0, 0.2);
	box-shadow: '' 0 1px 4px rgba(0, 0, 0, 0.2);
}
.flexslider .slides {
	zoom: 1;
}
.flexslider .slides img {
	height: auto;
}
.flex-viewport {
	max-height: 2000px;
	-webkit-transition: all 1s ease;
	-moz-transition: all 1s ease;
	-ms-transition: all 1s ease;
	-o-transition: all 1s ease;
	transition: all 1s ease;
}
.loading .flex-viewport {
	max-height: 300px;
}
.carousel li {
	margin-right: 5px;
}
.flex-direction-nav {
	*height: 0;
}
.flex-direction-nav a {
	text-decoration: none;
	display: block;
	width: 40px;
	height: 40px;
	margin: -20px 0 0;
	position: absolute;
	top: 50%;
	z-index: 10;
	overflow: hidden;
	opacity: 0;
	cursor: pointer;
	color: rgba(0, 0, 0, 0.8);
	text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-ms-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.flexslider:hover .flex-direction-nav .flex-prev {
	opacity: 0.7;
	left: 10px;
}
.flexslider:hover .flex-direction-nav .flex-prev:hover {
	opacity: 1;
}
.flexslider:hover .flex-direction-nav .flex-next {
	opacity: 0.7;
	right: 10px;
}
.flexslider:hover .flex-direction-nav .flex-next:hover {
	opacity: 1;
}
.flex-direction-nav .flex-disabled {
	opacity: 0!important;
	filter: alpha(opacity=0);
	cursor: default;
}
.flex-pauseplay a {
	display: block;
	width: 20px;
	height: 20px;
	position: absolute;
	bottom: 5px;
	left: 10px;
	opacity: 0.8;
	z-index: 10;
	overflow: hidden;
	cursor: pointer;
	color: #000;
}
.flex-pauseplay a:hover {
	opacity: 1;
}
.flex-control-nav {
	width: 100%;
	position: absolute;
	bottom: -40px;
	text-align: center;
}
.flex-control-nav li {
	margin: 0 6px;
	display: inline-block;
	zoom: 1;
	*display: inline;
}
.flex-control-paging li a {
	width: 11px;
	height: 11px;
	display: block;
	background: #666;
	background: rgba(0, 0, 0, 0.5);
	cursor: pointer;
	text-indent: -9999px;
	-webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
	-moz-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
	-o-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
	box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	border-radius: 20px;
}
.flex-control-paging li a:hover {
	background: #333;
	background: rgba(0, 0, 0, 0.7);
}
.flex-control-paging li a.flex-active {
	background: #000;
	background: rgba(0, 0, 0, 0.9);
	cursor: default;
}
.flex-control-thumbs {
	margin: 5px 0 0;
	position: static;
	overflow: hidden;
}
.flex-control-thumbs li {
	width: 25%;
	float: left;
	margin: 0;
}
.flex-control-thumbs img {
	width: 100%;
	height: auto;
	display: block;
	opacity: .7;
	cursor: pointer;
	-webkit-transition: all 1s ease;
	-moz-transition: all 1s ease;
	-ms-transition: all 1s ease;
	-o-transition: all 1s ease;
	transition: all 1s ease;
}
.flex-control-thumbs img:hover {
	opacity: 1;
}
.flex-control-thumbs .flex-active {
	opacity: 1;
	cursor: default;
}
/* ====================================================================================================================
 * RESPONSIVE
 * ====================================================================================================================*/
@media screen and (max-width: 860px) {
	.flex-direction-nav .flex-prev {
		opacity: 1;
		left: 10px;
	}
	.flex-direction-nav .flex-next {
		opacity: 1;
		right: 10px;
	}
}




/** Next | Prev
	 ************************* **/
.flex-prev,
.flex-next {
	background-image:none !important;
	color:#ccc;
	font-size:34px;
	line-height:55px;
	height:auto !important;
	width:56px !important;
	text-align:center;
	background-color:rgba(0,0,0,0.2);

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;

	-webkit-transition: all .400s;
	-moz-transition: all .400s;
	-o-transition: all .400s;
	transition: all .400s;
}
.flex-next {
	right:-3px;
}
.flex-prev {
	left:-3px;
}
.flex-next:hover,
.flex-prev:hover {
	color:#fff;
	background-color:rgba(0,0,0,0.5);
}

/** Custom
	 ************************* **/
.flexslider {
	margin:0;
	border:0;
	padding:0;
	overflow:hidden;
	position:relative;
}
.flex-direction-nav a:before,
.flex-direction-nav a.flex-next:before,
.flex-direction-nav a.flex-prev:before {
	font-family: '';
	content:'';
}
.flex-control-nav {
	bottom:auto;
	top:15px;
	right:15px;
	width:auto;
	display:inline-block;
}
.flex-control-nav li {
	margin:0 2px;
}
.flex-control-paging li a,
.flex-control-paging li a:hover {
	background-color:#fff;
	width:15px;
	height:5px;

	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
}
.flex-caption {
	position:absolute;
	margin-left: 20px;
	bottom: 45px;
	display:inline-block;
	color: #fff;
	background-color:rgba(0,0,0,0.7);
	font-family:'Lato',Arial,Helvetica,sans-serif;
	font-weight:300;
	padding: 6px 15px 8px 15px;
	opacity: 1 !important;
	width:auto;
	max-width:500px;
	font-size:21px;
	text-shadow: 1px 1px 1px rgba(0,0,0,0.15);

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
.flex-direction-nav .flex-prev {
	left:-3px !important;
	margin-top:-26px;
	opacity:1;
	color:#fff;
	text-align:center;
}
.flex-direction-nav .flex-next {
	right:-3px !important;
	margin-top:-26px;
	opacity:1;
	color:#fff;
	text-align:center;
}
.flex-control-nav.flex-control-thumbs li,
.flex-control-nav.flex-control-thumbs li img {
	width:100px !important;
	height:75px !important;
	cursor:pointer;
}
.flex-control-thumbs {
	margin:3px 0 0 ;
}
@media only screen and (max-width: 768px) {
	.flex-caption {
		display:none !important;
	}
	.flex-control-nav.flex-control-thumbs li,
	.flex-control-nav.flex-control-thumbs li img {
		width:80px !important;
		height:60px !important;
	}
	.flex-direction-nav .flex-prev,
	.flex-direction-nav .flex-next {
		margin-top:0;
	}
}
@media only screen and (max-width: 480px) {
	.flex-control-nav.flex-control-thumbs li,
	.flex-control-nav.flex-control-thumbs li img {
		width:60px !important;
		height:45px !important;
	}
}




/**	45. Widgets
**************************************************************** **/
/** Flickr **/
.widget-flickr ul,
.widget-flickr li {
	margin:0;
	padding:0;
	list-style:none;
}
.widget-flickr li {
	display:list-inline;
	float:left;
	margin:1px;

	-webkit-transition: all .300s;
	-moz-transition: all .300s;
	-o-transition: all .300s;
	transition: all .300s;
}
.widget-flickr li:hover {
	opacity:0.85;
	filter: alpha(opacity=85);
}

/** Twitter **/
ul.widget-twitter,
ul.widget-twitter li {
	margin:0;
	padding:0;
	list-style:none;
	font-size:14px;
}
ul.widget-twitter li {
	padding:10px 0;
	border-bottom:rgba(0,0,0,0.06) 1px solid;
}
ul.widget-twitter li>i {
	font-size:24px;
	float:left;
}
ul.widget-twitter li>small {
	display:block;
	margin-top:10px;
	font-size:11px;
	letter-spacing:1px;
}
ul.widget-twitter li>span>a {
	font-size:12px;
	display:block;
}
ul.widget-twitter li>small>a {
	color:#666;
}
ul.widget-twitter li>small,
ul.widget-twitter li>span {
	margin-left:30px;
	display:block;
}
section.dark ul.widget-twitter li {
	color:#ccc;
}


/** Dribbble **/
ul.widget-dribbble,
ul.widget-dribbble li {
	list-style:none;
	margin:0;
	padding:0;

	-webkit-transition: all .300s;
	-moz-transition: all .300s;
	-o-transition: all .300s;
	transition: all .300s;
}
ul.widget-dribbble>li img {
	padding:1px;

	-webkit-transition: all .300s;
	-moz-transition: all .300s;
	-o-transition: all .300s;
	transition: all .300s;
}
ul.widget-dribbble[data-col='1']>li {
	float:left;
	width:100% !important;
}
ul.widget-dribbble[data-col='2']>li {
	float:left;
	width:50% !important;
}
ul.widget-dribbble[data-col='3']>li {
	float:left;
	width:33.3% !important;
}
ul.widget-dribbble>li ,
ul.widget-dribbble[data-col='4']>li {
	float:left;
	width:25% !important;
}
ul.widget-dribbble[data-col='5']>li {
	float:left;
	width:20% !important;
}
ul.widget-dribbble[data-col='6']>li {
	float:left;
	width:16.66% !important;
}
ul.widget-dribbble>li:hover>a>img {
	opacity:0.85;
	filter: alpha(opacity=85);
}
@media only screen and (max-width: 482px) {

	ul.widget-dribbble>li ,
	ul.widget-dribbble[data-col='4']>li {
		float:left;
		width:50% !important;
	}
	ul.widget-dribbble[data-col='5']>li {
		float:left;
		width:50% !important;
	}
	ul.widget-dribbble[data-col='6']>li {
		float:left;
		width:50% !important;
	}

}





/** 46. Side Nav
**************************************************************** **/
div.side-nav li.list-group-item {
	padding:0 15px;
}


div.side-nav ul,
div.side-nav ul>li {
	border:0 !important;
}
div.side-nav ul>li {
	background-color:transparent !important;
}

div.side-nav ul>li:before {
	content: "\f105";
	font-family: FontAwesome;
	position: absolute;
	font-size: 15px;
	left: 0;
	top: 4px;
	color: #999;
}
div.side-nav ul.list-group-noicon>li:before,
div.side-nav ul.list-group-noicon>li>ul>li:before {
	content:'';
	display:none;
}
div.side-nav ul.list-group-noicon>li {
	padding-left:0;
}
div.side-nav ul>li>a {
	display:block;
	padding:4px 0 4px 0;
}
div.side-nav ul>li>a {
	color:#777;
	text-decoration:none;
}
div.side-nav ul>li:hover>a,
div.side-nav ul>li.active>a {
	color:#111 !important;
}
button.page-side-nav-mobile {
	display:none;
}
div.side-nav ul>li>ul>li>a{
	padding:4px 0 4px 15px;
}

ul.side-nav>li>a>i {
	margin-right:8px;
}


/* side nav sub categs */
div.side-nav ul>li>a.dropdown-toggle:before {
	content: "\f196";
	font-family: FontAwesome;
	position: absolute;
	font-size: 17px;
	right: 20px;
	top: 6px;
	color: #999;
}
div.side-nav ul>li.active>a.dropdown-toggle:before {
	content: "\f147";
}
div.side-nav ul>li>ul {
	display:none;
}
div.side-nav ul>li.active>ul {
	display:block;
}

div.side-nav ul>li>ul,
div.side-nav ul>li>ul>li {
	margin:0; padding:0;
	list-style:none;
}
div.side-nav ul>li>ul {
	padding:10px 0;
}
div.side-nav ul>li>ul>li a {
	font-size:12px;
}



/* */
div.side-nav .side-nav-head {
	position:relative;
}
div.side-nav .side-nav-head h4 {
	font-size:16px;
}
div.side-nav ul.list-group-bordered>li>a {
	display:block;
	border-bottom:rgba(0,0,0,0.05) 1px solid;
	padding:6px;
}
div.side-nav ul.list-group-bordered>li>a>span.pull-left,
div.side-nav ul.list-group-bordered>li>a>span.pull-right {
	margin-top:3px;
}
div.side-nav .side-nav-head button {
	display:none;
	font-size:21px;

	background-color:rgba(0,0,0,0.2);
	height:44px;
	width:44px;
}
@media only screen and (min-width: 990px) {
	div.side-nav>ul {
		display:block !important;
	}
}
@media only screen and (max-width: 767px) {
	div.side-nav ul {
		display:none;
		max-height:350px;
		overflow-y:auto;
		margin-bottom:30px;
	}
	div.side-nav ul>li {
		border-bottom:rgba(0,0,0,0.03) 1px solid !important;
	}
	div.side-nav .side-nav-head {
		height:44px;
		background-color:rgba(0,0,0,0.1);
		margin-bottom:30px;
	}
	div.side-nav .side-nav-head>h4 {
		line-height:44px;
		padding-left:15px;
		margin:0;
	}
	div.side-nav .side-nav-head button {
		display:block;
		position:absolute;
		right:0;
	}
}
@media only screen and (max-width: 768px) {
	div.side-nav ul {
		max-height:250px;
		overflow-y:auto;
	}
}





/** 47. Star Rating
	<div class="rating rating-0"><!-- rating-1 ... rating-5 --></div>
**************************************************************** **/
.rating {
	position:relative;
	display:block;
	min-height:17px;
	width:100%;
	color:#999;
	display:inline-block;
	font-family: FontAwesome;
}
section.dark .rating {
	color:#666;
}
.rating-0:after {
	content:"\f006\20\f006\20\f006\20\f006\20\f006";
}
.rating-1:after {
	content:"\f005\20\f006\20\f006\20\f006\20\f006";
}
.rating-2:after {
	content:"\f005\20\f005\20\f006\20\f006\20\f006";
}
.rating-3:after {
	content:"\f005\20\f005\20\f005\20\f006\20\f006";
}
.rating-4:after {
	content:"\f005\20\f005\20\f005\20\f005\20\f006";
}
.rating-5:after {
	content:"\f005\20\f005\20\f005\20\f005\20\f005";
}





/** 48. Image Zoom [plugin]
		assets/plugins/image.zoom
**************************************************************** **/
figure.zoom img::selection {
	background-color: transparent;
}

figure.zoom {
	display:inline-block;
	position: relative;

	-webkit-transition: all .300s;
	-moz-transition: all .300s;
	-o-transition: all .300s;
	transition: all .300s;
}
figure.zoom>a.lightbox {
	position:absolute;
	z-index:20;
	font-size:17px;
	background-color:#666;
	padding:6px 10px;
	width:40px; height:40px;
	line-height:30px;
	text-align:center;
	color:#fff;

	-webkit-border-bottom-right-radius: 15px;
	-webkit-border-top-left-radius: 15px;
	-moz-border-radius-bottomright: 15px;
	-moz-border-radius-topleft: 15px;
	border-bottom-right-radius: 15px;
	border-top-left-radius: 15px;
}
figure.zoom>a.lightbox:hover {
	background-color:#333;
}
/* lightbox button position */
figure.zoom>a.lightbox.bottom-right {
	right:10px; bottom:10px;
}
figure.zoom>a.lightbox.bottom-left {
	left:10px; bottom:10px;
}
figure.zoom>a.lightbox.top-right {
	right:10px; top:10px;
}
figure.zoom>a.lightbox.top-left {
	left:10px; top:10px;
}

.owl-carousel.zoom-more .owl-controls .owl-buttons div {
	background-color:inherit;
	border:0;
}

.owl-carousel.zoom-more .owl-controls.clickable {
	top: auto !important;
	bottom:0 !important;
	margin-top:0;
	margin-bottom:-10px;
	right:-10px;
}

.owl-carousel.zoom-more a,
.owl-carousel.zoom-more a>img {
	text-align:center;

	-webkit-transition: all .300s;
	-moz-transition: all .300s;
	-o-transition: all .300s;
	transition: all .300s;
}
.owl-carousel.zoom-more a>img {
	opacity:0.5;
	filter: alpha(opacity=80);

	-webkit-filter: grayscale(100%);
	-moz-filter: grayscale(100%);
	-ms-filter: grayscale(100%);
	-o-filter: grayscale(100%);
	filter: grayscale(100%);
	filter: url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'grayscale\'><feColorMatrix type=\'matrix\' values=\'0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0\'/></filter></svg>#grayscale");
	filter: gray;
}
.owl-carousel.zoom-more a.active>img {
	opacity:1;
	filter: alpha(opacity=1);

	-webkit-filter: none;
	filter: none;
}


/** 49. Forms
**************************************************************** **/
form div.row:last-child {
	margin-bottom:0;
}

form>select,
form>input,
form>textarea {
	margin-bottom:8px;
}

input.err,
input.error,
textarea.err,
textarea.error,
select.err,
select.error {
	border:#bf6464 2px dashed !important;
	background-color:#fdfbfb !important;

	-webkit-transition: all 0.2s ease-in;
	-moz-transition: all 0.2s ease-in;
	-o-transition: all 0.2s ease-in;
	transition: all 0.2s ease-in;
}


/*
	Form Validation
*/
form.validate label.error {
	position:absolute;
	background-color:#ff0000;
	color:#fff;
	left:0;
	z-index:10;
	bottom:-26px;
	left:36px;
	font-size:11px;
	font-weight:400;
	padding:3px;

	display:none !important;
}


/*
	CUSTOM FILE UPLOAD
	<input class="custom-file-upload" type="file" id="file" name="myfiles[]" multiple />
*/
.custom-file-upload-hidden {
	display: none;
	visibility: hidden;
	position: absolute;
	left: -9999px;
}

.file-upload-wrapper {
	position: relative;
	margin-bottom: 5px;
}

.file-upload-input {
	width: 300px;
	font-size: 16px;
	padding: 11px 17px;

	-moz-transition: all 0.2s ease-in;
	-o-transition: all 0.2s ease-in;
	-webkit-transition: all 0.2s ease-in;
	transition: all 0.2s ease-in;

	float: left;
	/* IE 9 Fix */
}





/* FORM ELEMENTS */
.radio+.radio,
.checkbox+.checkbox {
	margin-top:1px;
}
/**/
/* radios and checkboxes */
/**/
.radio,
.checkbox {
	display:inline-block;
	margin: 0 15px 3px 0;
	padding-left: 27px;
	font-size: 15px;
	line-height: 27px;
	color: #404040;
	cursor: pointer;
}
section.dark .radio,
section.dark .checkbox {
	color: #eee;
}
.radio input,
.checkbox input {
	position: absolute;
	left: -9999px;
}
.radio i,
.checkbox i {
	position: absolute;
	top: 5px;
	left: 0;
	display: block;
	width: 19px;
	height: 19px;
	outline: none;
	border-width: 2px;
	border-style: solid;
	border-color:rgba(0,0,0,0.3);
	background: rgba(255,255,255,0.3);
}
section.dark .radio i,
section.dark .checkbox i {
	border-color:rgba(255,255,255,0.3);
	background: transparent;
}
.radio i {
	-webkit-border-radius: 50% !important;
	-moz-border-radius: 50% !important;
	border-radius: 50% !important;
}
.radio input + i:after,
.checkbox input + i:after {
	position: absolute;
	opacity: 0;
	transition: opacity 0.1s;
	-o-transition: opacity 0.1s;
	-ms-transition: opacity 0.1s;
	-moz-transition: opacity 0.1s;
	-webkit-transition: opacity 0.1s;
}
.radio input + i:after {
	content: '';
	top: 5px;
	left: 5px;
	width: 5px;
	height: 5px;
	border-radius: 50%;
}
.checkbox input + i:after {
	content: '\f00c';
	top: 0;
	left: 0px;
	width: 15px;
	height: 15px;
	font: normal 12px/16px FontAwesome;
	text-align: center;
}
.radio input:checked + i:after,
.checkbox input:checked + i:after {
	opacity: 1;
}
.inline-group {
	margin: 0 -30px -4px 0;
}
.inline-group:after {
	content: '';
	display: table;
	clear: both;
}
.inline-group .radio,
.inline-group .checkbox {
	float: left;
	margin-right: 30px;
}
.inline-group .radio:last-child,
.inline-group .checkbox:last-child {
	margin-bottom: 4px;
}

/**/
/* hover state */
/**/
.input:hover input,
.select:hover select,
.textarea:hover textarea,
.radio:hover i,
.checkbox:hover i,
.toggle:hover i {
	border-color: rgba(0,0,0,0.6);
}
section.dark .input:hover input,
section.dark .select:hover select,
section.dark .textarea:hover textarea,
section.dark .radio:hover i,
section.dark .checkbox:hover i,
section.dark .toggle:hover i {
	border-color: rgba(255,255,255,0.3);
}
.button:hover {
	opacity: 1;
}

/**/
/* focus state */
/**/
.input input:focus,
.select select:focus,
.textarea textarea:focus,
.radio input:focus + i,
.checkbox input:focus + i,
.toggle input:focus + i {
	border-color: rgba(0,0,0,0.6);
}
section.dark .input input:focus,
section.dark .select select:focus,
section.dark .textarea textarea:focus,
section.dark .radio input:focus + i,
section.dark .checkbox input:focus + i,
section.dark .toggle input:focus + i {
	border-color: rgba(255,255,255,0.7);
}


/**/
/* checked state */
/**/
.radio input + i:after {
	background-color:rgba(0,0,0,8);
}
section.dark .radio input + i:after {
	background-color:rgba(255,255,255,8);
}
.checkbox input + i:after {
	color: rgba(0,0,0,8);
}
section.dark .checkbox input + i:after {
	color: rgba(255,255,255,8);
}
.radio input:checked + i,
.checkbox input:checked + i,
.toggle input:checked + i {
	border-color:rgba(0,0,0,8);
}
section.dark .radio input:checked + i,
section.dark .checkbox input:checked + i,
section.dark .toggle input:checked + i {
	border-color:rgba(255,255,255,8);
}


section.dark select>option,
section.dark select>optgroup {
	background-color: #333;
}




/* @toggle switch elements
-------------------------------------------------- */
.switch {
	cursor:pointer;
	position: relative;
	padding-right:10px;
	display: inline-block;
	margin-bottom:5px;
	height: 26px;
}

.switch > .switch-label {
	cursor:pointer;
	display: inline-block;
	position: relative;
	height: 25px;
	width: 58px;
	color: #fff;
	font-size: 10px;
	font-weight: bold;
	line-height: 20px;
	text-align: center;
	background: #B5C1C7;
	border: 2px solid #B5C1C7;
	text-transform: uppercase;
	font-family:Helvetica, Arial, sans-serif;

	-webkit-transition: 0.3s ease-out;
	-moz-transition: 0.3s ease-out;
	-o-transition: 0.3s ease-out;
	transition: 0.3s ease-out;

	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
}
.switch > .switch-label + span{
	display:inline-block;
	padding-left:5px;
	position:relative; top:-7px;
}
.switch > .switch-label:before {
	content: attr(data-off);
	position: absolute;
	top: 1px;
	right: 3px;
	width: 33px;
}

.switch > .switch-label:after {
	content:"";
	margin: 1px;
	width: 19px;
	height: 19px;
	display: block;
	background: #fff;
	-webkit-border-radius: 1px;
	-moz-border-radius: 1px;
	-o-border-radius: 1px;
	border-radius: 1px;
}

.switch > input {
	-webkit-appearance: none;
	position: absolute;
	width: inherit;
	height: inherit;
	opacity: 0;
	left: 0;
	top: 0;

}

/* @toggle switch focus state
-------------------------------------------------------------- */
.switch > input:focus {
	outline: none;
}
.switch > input:focus + .switch-label {
	color: #fff; border-color: #a1a6a9; background:#a1a6a9;
}
.switch > input:focus + .switch-label:after {
	background: #fff;
}

/* @toggle switch normal state
--------------------------------------------------------------- */
.switch > input:checked + .switch-label {
	border-color: #333;
	background: #333;
	padding-left: 33px;
	color: white;
}
.switch.switch-success > input:checked + .switch-label {
	border-color:#4cae4c;
	background:#4cae4c;
}
.switch.switch-danger > input:checked + .switch-label {
	border-color:#d43f3a;
	background:#d43f3a;
}
.switch.switch-warning > input:checked + .switch-label {
	border-color:#eea236;
	background:#eea236;
}
.switch.switch-info > input:checked + .switch-label {
	border-color:#46b8da;
	background:#46b8da;
}
.switch.switch-default > input:checked + .switch-label {
	border-color:rgba(0,0,0,0.1);
	background:rgba(0,0,0,0.1);
}
.switch.switch-default > input:checked + .switch-label:before {
	color:#888;
}

.switch > input:checked + .switch-label:before {
	content: attr(data-on);
	left: 1px;
	top:1px;
}

.switch > input:checked + .switch-label:after {
	margin: 1px;
	width: 19px;
	height: 19px;
	background: white;
}



/* @toggle switch normal state focus
--------------------------------------------------------------------------------- */
.switch-round > .switch-label {
	-webkit-border-radius: 13px;
	-moz-border-radius: 13px;
	-o-border-radius: 13px;
	border-radius: 13px;
}
.switch-round > .switch-label + span{
	top:-2px;
}
.switch-round > .switch-label:before {
	width: 33px;
}
.switch-round > .switch-label:after {
	width: 19px;
	color:#B5C1C7;
	content: "\2022";
	font:20px/20px Times, Serif;
	-webkit-border-radius: 13px;
	-moz-border-radius: 13px;
	-o-border-radius: 13px;
	border-radius: 13px;
}

.switch-round > input:checked + .switch-label {
	padding-left: 33px;
}
.switch-round > input:checked + .switch-label:after{
	color:#333;
}





/* fancy upload
-------------------------------------------------------- */
.fancy-file-upload {
	position:relative;
	height:40px;
	overflow:hidden;
	display:block;
	margin-bottom:3px;
}
.fancy-file-upload>span.button {
	color:#fff;
	background-color:#333;
	position: absolute;
	top: 4px;
	right: 4px;
	top:4px;
	bottom:4px;
	line-height: 34px;
	padding: 0 16px;
	z-index: 10;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
.fancy-file-upload.fancy-file-primary>span.button {
	background-color:#333;
}
.fancy-file-upload.fancy-file-success>span.button {
	background-color:#4cae4c;
}
.fancy-file-upload.fancy-file-danger>span.button {
	background-color:#d43f3a;
}
.fancy-file-upload.fancy-file-warning>span.button {
	background-color:#eea236;
}
.fancy-file-upload.fancy-file-info>span.button {
	background-color:#46b8da;
}
.fancy-file-upload.fancy-file-default>span.button {
	color:#666;
	background-color:rgba(0,0,0,0.1);
}

.fancy-file-upload>input[type=text] {
	background-color:transparent;
	padding-left: 36px;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
.fancy-file-upload>input[type=file] {
	width: 100%;
	height: 100%;
	cursor: pointer;
	padding: 8px 10px;
	position: absolute;
	-moz-opacity: 0;
	opacity: 0;
	z-index: 11;
	bottom: 0;
	right: 0;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
.fancy-file-upload>i {
	position:absolute;
	top: -1px;
	width: 42px;
	height: 42px;
	color: inherit;
	line-height: 42px;
	position: absolute;
	text-align: center;

	color:#888;

	z-index: 10;
}






/* fancy inputs / selects / textareas
-------------------------------------------------------- */
.fancy-form {
	position:relative;
	margin-bottom:3px;
}
.fancy-form .fancy-hint {
	padding:6px 10px;
	border-top:rgba(0,0,0,0.1) 1px solid;
	background-color:rgba(0,0,0,0.05);
	margin-top:-2px;
	border:#ddd 2px solid;
	border-top-width:1px;
	display:block;
	z-index:10;
}
section.dark .fancy-form .fancy-hint {
	border-color:#666;
	background-color:rgba(255,255,255,0.1);
}
.fancy-form>i {
	position:absolute;
	top:50%;
	left:15px;
	margin-top:-7px;
	z-index:10;
	width:14px;
	height:14px;
	color:#888;
}
.fancy-form>textarea + i {
	top:0;
	margin-top:12px;
}
.fancy-form>textarea,
.fancy-form>input {
	padding-left:36px;
	margin:0;
}
.fancy-form.fancy-icon-inverse>i {
	right:15px;
	left:auto;
}
.fancy-form.fancy-icon-inverse>textarea,
.fancy-form.fancy-icon-inverse>input {
	padding-right:36px;
	padding-left:15px;
}


.fancy-form>select {
	display: block;
	padding: 9px 10px;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	outline: none;
	text-indent: 0.01px;
	text-overflow: '';
	z-index: 10;
	margin: 0;
	cursor:pointer;
}
.fancy-form>.fancy-arrow,
.fancy-form>.fancy-arrow-double {
	position: absolute;
	top: 15px;
	right: 5px;
	left:auto;
	width: 24px;
	height: 24px;
	color: #B5C1C7;
	pointer-events: none;
	z-index: 16;
}
.fancy-form>.fancy-arrow:before,
.fancy-form>.fancy-arrow-double:after,
.fancy-form>.fancy-arrow-double:before {
	content: '';
	position: absolute;
	font: 12px monospace;
	font-style: normal;
	pointer-events: none;
	left: 6px;
}
.fancy-form>.fancy-arrow:before {
	content: '\25BC';
	bottom: 4px;
}
.fancy-form>.fancy-arrow-double:before {
	content: '\25BC';
	bottom: -1px;
}
.fancy-form>.fancy-arrow-double:after {
	content: '\25B2';
	top: -1px;
}

.fancy-form-select:before {
	position:absolute;
	content:' ';
	top: 2px;
	right: 2px;
	bottom: 2px;
	width: 37px;
	background: #F2F4FA;
	border-left: 1px solid rgba(0,0,0,0.08);

}

section.dark .fancy-form-select:before {
	background:#262626;
}


/* fancy tooltip
-------------------------------------------------------- */
.fancy-tooltip {
	position: absolute;
	z-index: 10;
	color: #fff;
	background: #313131;
	left: -9999px;
	top:-100%;
	font-size: 11px;
	font-weight:normal;
	margin-top:20px;
	padding:10px;


	-webkit-opacity: 0;
	-khtml-opacity: 0;
	-moz-opacity: 0;
	opacity: 0;
	-ms-filter: alpha(opacity=0);
	filter: alpha(opacity=0);

	-webkit-transition: margin 0.6s, opacity 0.6s;
	-moz-transition: margin 0.6s, opacity 0.6s;
	-ms-transition: margin 0.6s, opacity 0.6s;
	-o-transition: margin 0.6s, opacity 0.6s;
	transition: margin 0.6s, opacity 0.6s;
}

.fancy-tooltip:after {
	content: '';
	position: absolute;

	top: 100%;
	left: 12px;
	border-top: 8px solid #313131;
	border-right: 8px solid transparent;
	border-left: 8px solid transparent;
}
.fancy-tooltip:hover .tooltip,
.fancy-tooltip:focus + .tooltip,
select.fancy-tooltip:focus ~ .tooltip {
	opacity: 1;
	z-index: 999;
}

.fancy-form>select:focus + .fancy-tooltip,
.fancy-form>textarea:focus + .fancy-tooltip,
.fancy-form>input:focus + .fancy-tooltip {
	left:auto;
	top: -100%;
	margin-top:-8px;

	-webkit-opacity: 1;
	-khtml-opacity: 1;
	-moz-opacity: 1;
	opacity: 1;
	-ms-filter: alpha(opacity=100);
	filter: alpha(opacity=100);
}

.fancy-tooltip {
	padding:0;
}
input:focus + .fancy-tooltip {
	padding:10px;
}



/* @tooltip top left
----------------- */
.fancy-tooltip.top-left { top: -100%; left:0; right:auto; margin-bottom: 20px; }
.fancy-tooltip.top-left:after {
	left: 12px; right:auto;
	border-top: 8px solid #313131;
	border-left: 8px solid transparent;
	border-right: 8px solid transparent;
}


/* @tooltip top right
----------------- */
.fancy-tooltip.top-right { top: -100%; left:auto; right:0; margin-bottom: 20px; }
.fancy-tooltip.top-right:after {
	right: 12px; left:auto;
	border-top: 8px solid #313131;
	border-left: 8px solid transparent;
	border-right: 8px solid transparent;
}





/* autosuggest [typehead]
-------------------------------------------------------- */
div.autosuggest pre {
	margin:0;
}
div.autosuggest .twitter-typeahead {
	display:block !important;
}
div.autosuggest .tt-menu {
	top: 38px !important;
	width: 100% !important;
	padding: 6px !important;
	background-color:#fff;
	border: #ccc 2px solid;
	border-top:0;
}
section.dark div.autosuggest .tt-menu {
	border-color:#999;
	background-color:#333;
}
div.autosuggest .tt-menu .tt-suggestion {
	cursor:pointer;
	padding:3px;
	border-bottom:rgba(0,0,0,0.05) 1px solid;
}
div.autosuggest .tt-menu .tt-suggestion:hover {
	background-color:rgba(0,0,0,0.03);
}
div.autosuggest .tt-menu .tt-suggestion:last-child {
	border:0;
}

div.autosuggest .tt-dataset {
	max-height: 200px;
	overflow-y: auto;
}




/* Form Stepper
-------------------------------------------------------- */
.stepper-wrap {
	position: relative;
	display:block;
	font: 11px Arial, sans-serif;
	margin-right:34px !important;
}

.stepper-wrap input {
	padding-right:20px;
	margin-right:10px;
}

.stepper-wrap .gui-input{
	-webkit-border-radius: 3px 0 0 3px;
	-moz-border-radius: 3px 0 0 3px;
	-o-border-radius: 3px 0 0 3px;
	border-radius: 3px 0 0 3px;
}

.stepper-btn-wrap {
	position: absolute;
	top: 0;
	right: -34px;
	width: 39px;
	height: 100%;
	overflow: hidden;
	background: #F2F4FA;
	border: 2px solid #ddd;
	border-width:2px 2px 2px 1px;
	-webkit-background-clip: padding-box;
	-moz-background-clip: padding;
	background-clip: padding-box;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;

}
section.dark .stepper-btn-wrap {
	border-color:#666;
}
.stepper-btn-wrap a {
	display: block;
	height: 50%;
	overflow: hidden;
	line-height: 100%;
	text-align: center;
	text-decoration: none;
	text-shadow: 1px 1px 0 #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	text-indent:-9999px;
	background:#F2F4FA;
	cursor: default;
	color: #666;
}
section.dark .stepper-btn-wrap a {
	background:#262626;
}
.stepper-btn-wrap a:hover {
	background:#F8FAFC;
}

.stepper-btn-up:before,
.stepper-btn-dwn:before {
	content: '';
	border: 4px dashed;
	pointer-events: none;
	border-color: #B5C1C7 rgba(255, 255, 255, 0);
	position: absolute;
	right:13px;
	z-index:1;
	height:0;
	top:32%;
	width:0;
}


.stepper-btn-up:before {
	border-bottom-style: solid;
	border-top: none;
}

.stepper-btn-dwn:before {
	margin-top: 10px;
	border-top-style: solid;
	border-bottom: none;
}





/* UI SLIDER
------------------------------------------------------*/
.ui-slider .ui-slider-range,
.progress > button[type="submit"]:hover,
.progress > button[type="submit"],
.progress-bar > .bar {
	background-size: 16px 16px;
	background-image: -webkit-linear-gradient(top left,
	transparent, transparent 25%, rgba(255, 255, 255, 0.3) 25%, rgba(255, 255, 255, 0.3) 50%,
	transparent 50%, transparent 75%, rgba(255, 255, 255, 0.3) 75%, rgba(255, 255, 255, 0.3));
	background-image: -moz-linear-gradient(top left,
	transparent, transparent 25%, rgba(255, 255, 255, 0.3) 25%, rgba(255, 255, 255, 0.3) 50%,
	transparent 50%, transparent 75%, rgba(255, 255, 255, 0.3) 75%, rgba(255, 255, 255, 0.3));
	background-image: -o-linear-gradient(top left,
	transparent, transparent 25%, rgba(255, 255, 255, 0.3) 25%, rgba(255, 255, 255, 0.3) 50%,
	transparent 50%, transparent 75%, rgba(255, 255, 255, 0.3) 75%, rgba(255, 255, 255, 0.3));
	background-image: linear-gradient(to bottom right,
	transparent, transparent 25%, rgba(255, 255, 255, 0.3) 25%, rgba(255, 255, 255, 0.3) 50%,
	transparent 50%, transparent 75%, rgba(255, 255, 255, 0.3) 75%, rgba(255, 255, 255, 0.3));
}
.slider-wrapper,
.sliderv-wrapper {
	background:#E2E8F1;
	position:relative;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px;
}
section.dark .slider-wrapper,
section.dark .sliderv-wrapper {
	background-color:#222;
}
.ui-slider {
	position: relative;
	text-align: left;
}

.ui-slider .ui-slider-handle {
	position: absolute;
	z-index: 2;
	width: 1.5em;
	height: 1.5em;
	cursor: default;
	background:#fff;
	text-decoration:none;
	border:3px solid #1ABC9C;
	-webkit-border-radius:20px;
	-moz-border-radius:20px;
	-o-border-radius:20px;
	border-radius:20px;
	-ms-touch-action: none;
	touch-action: none;
	margin-top:-3px;
	outline:none;
}

.ui-slider .ui-slider-handle:before{
	content: '';
	width: 7px;
	height: 7px;
	position:absolute;
	background-color: #1ABC9C;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	-o-border-radius: 10px;
	border-radius: 10px;
	z-index: 2;
	left:4px;
	top:4px;
}

.ui-slider .ui-slider-range {
	position: absolute;
	z-index: 1;
	font-size: .7em;
	display: block;
	border: 0;
	background-position: 0 0;
	background-color: #1ABC9C;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px;
}

.ui-slider.ui-state-disabled .ui-slider-handle,
.ui-slider.ui-state-disabled .ui-slider-range { filter: inherit; }
.ui-slider-horizontal { height: .5em; }
.ui-slider-horizontal .ui-slider-handle { top: -.3em; margin-left: -.6em; }
.ui-slider-horizontal .ui-slider-range { top: 0; height: 100%; }
.ui-slider-horizontal .ui-slider-range-min { left: 0; }
.ui-slider-horizontal .ui-slider-range-max { right: 0; }
.ui-slider-vertical,
.sliderv-wrapper { width: .5em; height: 100px; }
.ui-slider-vertical .ui-slider-handle { left: -.45em; margin-left: 0; margin-bottom: -.6em; }
.ui-slider-vertical .ui-slider-range { left: 0; width: 100%; }
.ui-slider-vertical .ui-slider-range-min { bottom: 0; }
.ui-slider-vertical .ui-slider-range-max { top: 0; }
.slider-input{  color:#f6931f!important; border:0; background:none; }
.slider-group .sliderv-wrapper{ height:150px; float:left; margin:15px 15px;   }
.ui-slider .ui-state-active {
	cursor: -webkit-grabbing;
	cursor: -moz-grabbing;
	cursor: grabbing;
}

/* @ui slider tooltip
	--------------------- */
.slider-tip {
	display: block;
	position: absolute;
	text-align: center;
	font: 10pt Tahoma, Arial, sans-serif ;
	background: #34495E;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px;
	padding:10px;
	color: #fff;
}

.slider-wrapper .slider-tip{ top: -50px; left:-15px; }
.slider-wrapper .slider-tip:after {
	content: '';
	position: absolute;
	top: 98%;
	left: 35%;
	border-top: 8px solid #34495E;
	border-right: 8px solid transparent;
	border-left: 8px solid transparent;
}

.sliderv-wrapper .slider-tip{ left: 30px; top:-12px; }
.sliderv-wrapper .slider-tip:after{
	content: '';
	position: absolute;
	top:30%;
	right: 98%;
	border-right: 8px solid #34495E;
	border-top: 8px solid transparent;
	border-bottom: 8px solid transparent;
}

/* @ui slider themes
	--------------------- */
.warning-slider .ui-slider .ui-slider-handle{ border-color:#ed9c28; }
.warning-slider .ui-slider .ui-slider-handle:before,
.warning-slider .ui-slider .ui-slider-range { background-color:#ed9c28;  }
.danger-slider .ui-slider .ui-slider-handle{ border-color:#e74c3c; }
.danger-slider .ui-slider .ui-slider-handle:before,
.danger-slider .ui-slider .ui-slider-range { background-color:#e74c3c;  }
.purple-slider .ui-slider .ui-slider-handle{ border-color:#9B59B6; }
.purple-slider .ui-slider .ui-slider-handle:before,
.purple-slider .ui-slider .ui-slider-range { background-color:#9B59B6;  }
.info-slider .ui-slider .ui-slider-handle{ border-color:#3498db; }
.info-slider .ui-slider .ui-slider-handle:before,
.info-slider .ui-slider .ui-slider-range { background-color:#3498db;  }
.black-slider .ui-slider .ui-slider-handle{ border-color:#34495e; }
.black-slider .ui-slider .ui-slider-handle:before,
.black-slider .ui-slider .ui-slider-range { background-color:#34495e;  }
.success-slider .ui-slider .ui-slider-handle{ border-color:#2ecc71; }
.success-slider .ui-slider .ui-slider-handle:before,
.success-slider .ui-slider .ui-slider-range { background-color:#2ecc71;  }

.yellow-slider .ui-slider .ui-slider-handle{ border-color:#ed9c28; }
.yellow-slider .ui-slider .ui-slider-handle:before,
.yellow-slider .ui-slider .ui-slider-range { background-color:#ed9c28;  }

/* UI slider addons | labels + tooltips
	-------------------- */
.ui-slider-horizontal.ui-slider-pips {  margin-bottom: 0.5em; }
.ui-slider-pips .ui-slider-label,
.ui-slider-pips .ui-slider-pip-hide { display: none; }
.ui-slider-pips .ui-slider-pip-label .ui-slider-label { display: block; }
.ui-slider-pips .ui-slider-pip {
	width: 2em;
	height: 1em;
	line-height: 1em;
	position: absolute;
	font-size: 0.8em;
	color: #999;
	overflow: visible;
	text-align: center;
	top: 10px;
	left: 20px;
	margin-left: -1em;
	cursor: pointer;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.ui-slider-pips .ui-slider-line {
	background: #999;
	width: 1px;
	height: 3px;
	position: absolute;
	left: 50%;
}

.ui-slider-pips .ui-slider-label {
	position: absolute;
	top: 5px;
	left: 50%;
	margin-left: -1em;
	width: 2em;
}

.ui-slider-pip:hover .ui-slider-label { color: black; font-weight: bold; }
.ui-slider-vertical.ui-slider-pips { margin-bottom: 1em; margin-right: 10px; }
.ui-slider-vertical.ui-slider-pips .ui-slider-pip {
	text-align: left;
	top: auto;
	left: 10px;
	margin-left: 0;
	margin-bottom: -0.5em;
}

.ui-slider-vertical.ui-slider-pips .ui-slider-line {
	width: 3px;
	height: 1px;
	position: absolute;
	top: 50%;
	left: 0;
}

.ui-slider-vertical.ui-slider-pips .ui-slider-label {
	top: 50%;
	left: 0.5em;
	margin-left: 0;
	margin-top: -0.5em;
	width: 2em;
}


/* UI slider tooltips
	--------------------- */
.ui-slider-float .ui-slider-handle:focus,
.ui-slider-float .ui-slider-handle:focus .ui-slider-tip,
.ui-slider-float .ui-slider-handle:focus .ui-slider-tip-label { outline: none; }
.ui-slider-float .ui-slider-tip,
.ui-slider-float .ui-slider-tip-label {
	position: absolute;
	visibility: hidden;
	margin-top: -10px;
	display: block;
	width: auto;
	padding:5px 10px;
	margin-left: -50%;
	line-height: 20px;
	background:#000;
	border-radius: 3px;
	text-align: center;
	font-size: 11px;
	font-weight:bold;
	color:#fff;
	opacity:0;
	-webkit-transition-duration: 200ms, 200ms, 0;
	-moz-transition-duration: 200ms, 200ms, 0;
	-ms-transition-duration: 200ms, 200ms, 0;
	transition-duration: 200ms, 200ms, 0;
	-webkit-transition-property: opacity, top, visibility;
	-moz-transition-property: opacity, top, visibility;
	-ms-transition-property: opacity, top, visibility;
	transition-property: opacity, top, visibility;
	-webkit-transition-delay: 0, 0, 200ms;
	-moz-transition-delay: 0, 0, 200ms;
	-ms-transition-delay: 0, 0, 200ms;
	transition-delay: 0, 0, 200ms;
}

.ui-slider-float .ui-slider-handle:hover .ui-slider-tip,
.ui-slider-float .ui-slider-handle:focus .ui-slider-tip,
.ui-slider-float .ui-slider-pip:hover .ui-slider-tip-label {
	opacity: 1;
	top: -30px;
	visibility: visible;
	-webkit-transition-delay:200ms, 200ms, 0;
	-moz-transition-delay:200ms, 200ms, 0;
	-ms-transition-delay:200ms, 200ms, 0;
	transition-delay:200ms, 200ms, 0;
}

.ui-slider-float .ui-slider-pip .ui-slider-tip-label { top: 42px; }
.ui-slider-float .ui-slider-pip:hover .ui-slider-tip-label { top: 32px; font-weight: normal; }

.ui-slider-float .ui-slider-tip:after,
.ui-slider-float .ui-slider-pip .ui-slider-tip-label:after {
	content: " ";
	width: 0;
	height: 0;
	border-top: 8px solid #000;
	border-right: 8px solid transparent;
	border-left: 8px solid transparent;
	position: absolute;
	bottom: -6px;
	left: 30%;
}




/** 50. Sky Form
**************************************************************** **/
.sky-form,.sky-form .label{color:#333;text-align:left;padding:0;font-size:14px;font-weight:300}.checkbox+.checkbox,.radio+.radio{margin-top:12px}.sky-form.boxed{border:rgba(0,0,0,.1) solid}.sky-form header{margin:0;background:-webkit-linear-gradient(top,#fff 1%,#f9f9f9 98%);background:linear-gradient(to bottom,#fff 1%,#f9f9f9 98%);-webkit-box-sizing:border-box;-moz-box-sizing:border-box;border-bottom:rgba(0,0,0,.07) 1px solid;padding:15px 20px;font-size:20px;font-weight:400}.sky-form.boxed fieldset{padding:15px}.sky-form label.captcha>img{position:absolute;top:3px;right:2px}.sky-form fieldset{display:block;padding:25px 30px 5px;border:none}.sky-form fieldset+fieldset{border-top:1px solid rgba(0,0,0,.1)}.sky-form .section,.sky-form section{padding:0;margin-bottom:20px}.sky-form footer{margin:0 15px 10px;border-top:rgba(0,0,0,.03) 1px solid}.sky-form footer:after{content:'';display:table;clear:both}.sky-form .label{display:block;margin-bottom:6px;line-height:19px}.sky-form .label.col{margin:0;padding-top:10px}.sky-form .note{margin-top:6px;padding:0 1px;font-size:12px;line-height:15px;color:rgba(0,0,0,.5);font-weight:400}.sky-form .button,.sky-form .checkbox,.sky-form .input,.sky-form .radio,.sky-form .select,.sky-form .textarea,.sky-form .toggle{position:relative;display:block}.sky-form .input input,.sky-form .select select,.sky-form .textarea textarea{display:block;box-sizing:border-box;-moz-box-sizing:border-box;width:100%;height:40px;padding:8px 10px;outline:0;border-width:2px;border-style:solid;border-radius:0;background:#fff;font:15px/19px 'Open Sans',Helvetica,Arial,sans-serif;color:#404040;appearance:normal;-moz-appearance:none;-webkit-appearance:none}.sky-form .col{float:left;min-height:1px;padding-right:15px;padding-left:15px;box-sizing:border-box;-moz-box-sizing:border-box}.sky-form .input-file .button{position:absolute;top:4px;right:4px;float:none;height:31px;margin:0;padding:0 20px;font-size:13px;line-height:31px}.sky-form .input-file .button:hover{box-shadow:none}.sky-form .input-file .button input{position:absolute;top:0;right:0;padding:0;font-size:30px;cursor:pointer;opacity:0}.sky-form .select i{position:absolute;top:14px;right:14px;width:5px;height:11px;background:#fff;box-shadow:0 0 0 12px #fff}.sky-form .select i:after,.sky-form .select i:before{content:'';position:absolute;right:0;border-right:4px solid transparent;border-left:4px solid transparent}.sky-form .select i:after{bottom:0;border-top:4px solid #404040}.sky-form .select i:before{top:0;border-bottom:4px solid #404040}.sky-form .select-multiple select{height:auto}.sky-form .textarea textarea{height:auto;resize:none}.sky-form .textarea-resizable textarea{resize:vertical}.sky-form .textarea-expandable textarea{height:40px}.sky-form .textarea-expandable textarea:focus{height:auto}.sky-form .checkbox,.sky-form .radio{margin-bottom:4px;padding-left:27px;font-size:15px;line-height:27px;color:#404040;cursor:pointer}.sky-form .checkbox:last-child,.sky-form .radio:last-child{margin-bottom:0}.sky-form .checkbox input,.sky-form .radio input{position:absolute;left:-9999px}.sky-form .checkbox i,.sky-form .radio i{position:absolute;top:5px;left:0;display:block;width:19px;height:19px;outline:0;border-width:2px;border-style:solid;background:#fff}.sky-form .radio i{border-radius:50%}.sky-form .checkbox input+i:after,.sky-form .radio input+i:after{position:absolute;opacity:0;transition:opacity .1s;-o-transition:opacity .1s;-ms-transition:opacity .1s;-moz-transition:opacity .1s;-webkit-transition:opacity .1s}.sky-form .radio input+i:after{content:'';top:5px;left:5px;width:5px;height:5px;border-radius:50%}.sky-form .checkbox input+i:after{content:'\f00c';top:0;left:0;width:15px;height:15px;font:400 12px/16px FontAwesome;text-align:center}.sky-form .checkbox input:checked+i:after,.sky-form .radio input:checked+i:after{opacity:1}.sky-form .inline-group{margin:0 -30px -4px 0}.sky-form .inline-group:after{content:'';display:table;clear:both}.sky-form .inline-group .checkbox,.sky-form .inline-group .radio{float:left;margin-right:30px}.sky-form .inline-group .checkbox:last-child,.sky-form .inline-group .radio:last-child{margin-bottom:4px}.sky-form .toggle{margin-bottom:4px;padding-right:61px;font-size:15px;line-height:27px;color:#404040;cursor:pointer}.sky-form .toggle:last-child{margin-bottom:0}.sky-form .toggle input{position:absolute;left:-9999px}.sky-form .toggle i{content:'';position:absolute;top:5px;right:0;display:block;width:49px;height:22px;border-width:2px;border-style:solid;border-radius:12px;background:#fff}.sky-form .toggle i:after{content:'OFF';position:absolute;top:2px;right:8px;left:8px;font-style:normal;font-size:9px;line-height:13px;font-weight:700;text-align:left;color:#5f5f5f}.sky-form .toggle i:before{content:'';position:absolute;z-index:1;top:4px;right:4px;display:block;width:9px;height:9px;border-radius:50%;opacity:1;transition:right .2s;-o-transition:right .2s;-ms-transition:right .2s;-moz-transition:right .2s;-webkit-transition:right .2s}.sky-form .toggle input:checked+i:after{content:'ON';text-align:right}.sky-form .toggle input:checked+i:before{right:33px}.sky-form .rating{margin-bottom:4px;font-size:15px;line-height:27px;color:#404040}.sky-form .rating:last-child{margin-bottom:0}.sky-form .rating input{position:absolute;left:-9999px}.sky-form .rating label{display:block;float:right;height:17px;margin-top:5px;padding:0 2px;font-size:17px;line-height:17px;cursor:pointer}.sky-form .button{float:right;height:40px;overflow:hidden;margin:10px 0 0 20px;padding:0 25px;outline:0;border:0;font:300 15px/39px 'Open Sans',Helvetica,Arial,sans-serif;text-decoration:none;color:#fff;cursor:pointer}.sky-form .btn{margin:10px 0 0 20px}.sky-form .btn:hover{margin-bottom:0}.sky-form .input-group-btn .btn{margin:0}.sky-form .tooltip{position:absolute;z-index:1;left:-9999px;padding:2px 8px 3px;font-size:11px;line-height:16px;font-weight:400;background:rgba(0,0,0,.9);color:#fff;opacity:0;transition:margin .3s,opacity .3s;-o-transition:margin .3s,opacity .3s;-ms-transition:margin .3s,opacity .3s;-moz-transition:margin .3s,opacity .3s;-webkit-transition:margin .3s,opacity .3s}.sky-form .tooltip:after{content:'';position:absolute}.sky-form .input input:focus+.tooltip,.sky-form .textarea textarea:focus+.tooltip{opacity:1}.sky-form .tooltip-top-right{bottom:100%;margin-bottom:15px}.sky-form .tooltip-top-right:after{top:100%;right:16px;border-top:4px solid rgba(0,0,0,.9);border-right:4px solid transparent;border-left:4px solid transparent}.sky-form .input input:focus+.tooltip-top-right,.sky-form .textarea textarea:focus+.tooltip-top-right{right:0;left:auto;margin-bottom:5px}.sky-form .tooltip-top-left{bottom:100%;margin-bottom:15px}.sky-form .tooltip-top-left:after{top:100%;left:16px;border-top:4px solid rgba(0,0,0,.9);border-right:4px solid transparent;border-left:4px solid transparent}.sky-form .input input:focus+.tooltip-top-left,.sky-form .textarea textarea:focus+.tooltip-top-left{right:auto;left:0;margin-bottom:5px}.sky-form .tooltip-right{top:9px;white-space:nowrap;margin-left:15px}.sky-form .tooltip-right:after{top:6px;right:100%;border-top:4px solid transparent;border-right:4px solid rgba(0,0,0,.9);border-bottom:4px solid transparent}.sky-form .input input:focus+.tooltip-right,.sky-form .textarea textarea:focus+.tooltip-right{left:100%;margin-left:5px}.sky-form .tooltip-left{top:9px;white-space:nowrap;margin-right:15px}.sky-form .tooltip-left:after{top:6px;left:100%;border-top:4px solid transparent;border-bottom:4px solid transparent;border-left:4px solid rgba(0,0,0,.9)}.sky-form .input input:focus+.tooltip-left,.sky-form .textarea textarea:focus+.tooltip-left{right:100%;left:auto;margin-right:5px}.sky-form .tooltip-bottom-right{top:100%;margin-top:15px}.sky-form .tooltip-bottom-right:after{bottom:100%;right:16px;border-right:4px solid transparent;border-bottom:4px solid rgba(0,0,0,.9);border-left:4px solid transparent}.sky-form .input input:focus+.tooltip-bottom-right,.sky-form .textarea textarea:focus+.tooltip-bottom-right{right:0;left:auto;margin-top:5px}.sky-form .tooltip-bottom-left{top:100%;margin-top:15px}.sky-form .tooltip-bottom-left:after{bottom:100%;left:16px;border-right:4px solid transparent;border-bottom:4px solid rgba(0,0,0,.9);border-left:4px solid transparent}.sky-form .input input:focus+.tooltip-bottom-left,.sky-form .textarea textarea:focus+.tooltip-bottom-left{right:auto;left:0;margin-top:5px}.sky-form .checkbox i,.sky-form .ico-append,.sky-form .ico-prepend,.sky-form .input input,.sky-form .radio i,.sky-form .select select,.sky-form .textarea textarea,.sky-form .toggle i{border-color:#e5e5e5;transition:border-color .3s;-o-transition:border-color .3s;-ms-transition:border-color .3s;-moz-transition:border-color .3s;-webkit-transition:border-color .3s}.sky-form .toggle i:before{background-color:#2da5da}.sky-form .rating label{color:#ccc;transition:color .3s;-o-transition:color .3s;-ms-transition:color .3s;-moz-transition:color .3s;-webkit-transition:color .3s}.sky-form .button{background-color:#2da5da;opacity:.8;transition:opacity .2s;-o-transition:opacity .2s;-ms-transition:opacity .2s;-moz-transition:opacity .2s;-webkit-transition:opacity .2s}.sky-form .button.button-secondary{background-color:#b3b3b3}.sky-form .ico-append,.sky-form .ico-prepend{color:#ccc}.sky-form .ico-prepend{left:5px;padding-right:3px;border-right-width:1px;border-right-style:solid}.sky-form .ico-append{right:5px;padding-left:3px;border-left-width:1px;border-left-style:solid}.sky-form .ico-append,.sky-form .ico-prepend{position:absolute;top:5px;width:29px;height:29px;font-size:15px;line-height:29px;text-align:center}.sky-form .input .ico-prepend+.ico-append+input,.sky-form .input .ico-prepend+input,.sky-form .textarea .ico-prepend+.ico-append+textarea,.sky-form .textarea .ico-prepend+textarea{padding-left:46px}.sky-form .checkbox:hover i,.sky-form .input:hover input,.sky-form .radio:hover i,.sky-form .select:hover select,.sky-form .textarea:hover textarea,.sky-form .toggle:hover i{border-color:#8dc9e5}.sky-form .rating input+label:hover,.sky-form .rating input+label:hover~label{color:#2da5da}.sky-form .button:hover{opacity:1}.sky-form .checkbox input:focus+i,.sky-form .input input:focus,.sky-form .radio input:focus+i,.sky-form .select select:focus,.sky-form .textarea textarea:focus,.sky-form .toggle input:focus+i{border-color:#2da5da}.sky-form .radio input+i:after{background-color:#2da5da}.sky-form .checkbox input+i:after{color:#2da5da}.sky-form .checkbox input:checked+i,.sky-form .radio input:checked+i,.sky-form .toggle input:checked+i{border-color:#2da5da}.sky-form .rating input:checked~label{color:#2da5da}.sky-form .checkbox.state-error i,.sky-form .radio.state-error i,.sky-form .state-error input,.sky-form .state-error select,.sky-form .state-error textarea,.sky-form .toggle.state-error i{background:#fff0f0}.sky-form .state-error select+i{background:#fff0f0;box-shadow:0 0 0 12px #fff0f0}.sky-form .toggle.state-error input:checked+i{background:#fff0f0}.sky-form .note-error{color:#fff;padding:3px;display:inline-block;background-color:#ee9393}.sky-form .checkbox.state-success i,.sky-form .radio.state-success i,.sky-form .state-success input,.sky-form .state-success select,.sky-form .state-success textarea,.sky-form .toggle.state-success i{background:#f0fff0}.sky-form .state-success select+i{background:#f0fff0;box-shadow:0 0 0 12px #f0fff0}.sky-form .toggle.state-success input:checked+i{background:#f0fff0}.sky-form .note-success{color:#fff;padding:3px;display:inline-block;background-color:#6fb679}.sky-form .button.state-disabled,.sky-form .checkbox.state-disabled,.sky-form .input.state-disabled input,.sky-form .radio.state-disabled,.sky-form .select.state-disabled,.sky-form .textarea.state-disabled,.sky-form .toggle.state-disabled{cursor:default;opacity:.5}.sky-form .checkbox.state-disabled:hover i,.sky-form .input.state-disabled:hover input,.sky-form .radio.state-disabled:hover i,.sky-form .select.state-disabled:hover select,.sky-form .textarea.state-disabled:hover textarea,.sky-form .toggle.state-disabled:hover i{border-color:#e5e5e5}
.sky-form.boxed {
	border: rgba(0,0,0,0.1) 1px solid;
}
.sky-form footer {
	padding:8px 0;
	margin-bottom:0;
}
.sky-form .btn {
	margin-top:0;
}
.sky-form .btn-social {
	margin:0;
}

.sky-form.boxed {
	background-color:#fff;
}
.sky-form fieldset {
	padding: 0 0 30px 0;
}

.sky-form header {
	background:transparent !important;
}

.sky-form .checkbox input:hover+i,
.sky-form .input input:hover,
.sky-form .radio input:hover+i,
.sky-form .select select:hover,
.sky-form .textarea textarea:hover,
.sky-form .toggle input:hover+i,

.sky-form .checkbox input:focus+i,
.sky-form .input input:focus,
.sky-form .radio input:focus+i,
.sky-form .select select:focus,
.sky-form .textarea textarea:focus,
.sky-form .toggle input:focus+i {
	border-color:#c6c6c6;
}

section.alternate .sky-form.boxed {
	background-color:#F9F9F9;
}

section.dark .sky-form.boxed {
	background-color:#333;
	border:0;
}
section.dark .sky-form header {
	color:#fff;
	background: -webkit-linear-gradient(top,#000 1%,#222 98%);
	background: linear-gradient(to bottom,#000 1%,#222 98%);
	border-bottom: rgba(255,255,255,.07) 1px solid;
}
section.dark .sky-form .checkbox i,
section.dark .sky-form .ico-append,
section.dark .sky-form .ico-prepend,
section.dark .sky-form .input input,
section.dark .sky-form .radio i,
section.dark .sky-form .select select,
section.dark .sky-form .textarea textarea,
section.dark .sky-form .toggle i {
	border-color:#666;
}

section.dark .sky-form .input input,
section.dark .sky-form .select select,
section.dark .sky-form .textarea textarea {
	background-color:transparent;
}
section.dark .sky-form footer {
	border-top-color:#444;
}

section.dark .sky-form .checkbox input:hover+i,
section.dark .sky-form .input input:hover,
section.dark .sky-form .radio input:hover+i,
section.dark .sky-form .select select:hover,
section.dark .sky-form .textarea textarea:hover,
section.dark .sky-form .toggle input:hover+i,

section.dark .sky-form .checkbox input:focus+i,
section.dark .sky-form .input input:focus,
section.dark .sky-form .radio input:focus+i,
section.dark .sky-form .select select:focus,
section.dark .sky-form .textarea textarea:focus,
section.dark .sky-form .toggle input:focus+i {
	border-color:#999;
}








/** 51. Summernote
*************************************************** **/
.note-editor {
	position: relative;
	border: rgba(0,0,0,0.1) 1px solid;
}
.note-editor .row-fluid p {
	display:none;
}
.note-editor .note-dropzone {
	position: absolute;
	z-index: 1;
	display: none;
	color: #87cefa;
	background-color: white;
	border: 2px dashed #87cefa;
	opacity: .95;
	pointer-event: none;
}

.note-editor .note-dropzone .note-dropzone-message {
	display: table-cell;
	font-size: 28px;
	font-weight: bold;
	text-align: center;
	vertical-align: middle;
}

.note-editor .note-dropzone.hover {
	color: #098ddf;
	border: 2px dashed #098ddf;
}

.note-editor.dragover .note-dropzone {
	display: table;
}

.note-editor .note-toolbar {
	background-color: rgba(0,0,0,0.01);
	border-bottom: 1px solid #eee;
}

.note-editor.fullscreen {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 1050;
	width: 100%;
	background-color:#1F252D;
}

.note-editor.fullscreen .note-editable {
	background-color: white;
}

.note-editor.fullscreen .note-resizebar {
	display: none;
}

.note-editor.codeview .note-editable {
	display: none;
}

.note-editor.codeview .note-codable {
	display: block;
}

.note-editor .note-statusbar {
	background-color: #f5f5f5;
}

.note-editor .note-statusbar .note-resizebar {
	width: 100%;
	height: 8px;
	cursor: ns-resize;
	border-top: 1px solid #a9a9a9;
}

.note-editor .note-statusbar .note-resizebar .note-icon-bar {
	width: 20px;
	margin: 1px auto;
	border-top: 1px solid #a9a9a9;
}

.note-editor .note-editable {
	padding: 10px;
	overflow: auto;
	outline: 0;
}

.note-editor .note-editable[contenteditable="false"] {
	background-color: #e5e5e5;
}

.note-editor .note-codable {
	display: none;
	width: 100%;
	padding: 10px;
	margin-bottom: 0;
	font-family: Menlo,Monaco,monospace,sans-serif;
	font-size: 14px;
	color: #ccc;
	background-color: #222;
	border: 0;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	box-shadow: none;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	-ms-box-sizing: border-box;
	box-sizing: border-box;
	resize: none;
}

.note-air-editor {
	outline: 0;
}

.note-popover .popover {
	max-width: none;
}

.note-popover .popover .popover-content a {
	display: inline-block;
	max-width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	vertical-align: middle;
}

.note-popover .popover .arrow {
	left: 20px;
}

.note-popover .popover .popover-content,.note-toolbar {
	padding: 0 5px 5px 5px;
	margin: 0;
}

.note-popover .popover .popover-content>.btn-group,.note-toolbar>.btn-group {
	margin: 5px 5px 0 0;
}
.note-popover .popover .popover-content>.btn-group,.note-toolbar>.btn-group>button {
	margin:0;
	border: rgba(0,0,0,0.2) 1px solid;
	background-color:#fff;
}
.note-popover .popover .popover-content .note-table .dropdown-menu,.note-toolbar .note-table .dropdown-menu {
	min-width: 0;
	padding: 5px;
}

.note-popover .popover .popover-content .note-table .dropdown-menu .note-dimension-picker,.note-toolbar .note-table .dropdown-menu .note-dimension-picker {
	font-size: 18px;
}

.note-popover .popover .popover-content .note-table .dropdown-menu .note-dimension-picker .note-dimension-picker-mousecatcher,.note-toolbar .note-table .dropdown-menu .note-dimension-picker .note-dimension-picker-mousecatcher {
	position: absolute!important;
	z-index: 3;
	width: 10em;
	height: 10em;
	cursor: pointer;
}

.note-popover .popover .popover-content .note-table .dropdown-menu .note-dimension-picker .note-dimension-picker-unhighlighted,.note-toolbar .note-table .dropdown-menu .note-dimension-picker .note-dimension-picker-unhighlighted {
	position: relative!important;
	z-index: 1;
	width: 5em;
	height: 5em;
	background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIj4+Pjp6ekKlAqjAAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKhmnaJzPAAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC') repeat;
}

.note-popover .popover .popover-content .note-table .dropdown-menu .note-dimension-picker .note-dimension-picker-highlighted,.note-toolbar .note-table .dropdown-menu .note-dimension-picker .note-dimension-picker-highlighted {
	position: absolute!important;
	z-index: 2;
	width: 1em;
	height: 1em;
	background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIjd6vvD2f9LKLW+AAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKwNDEVT0AAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC') repeat;
}

.note-popover .popover .popover-content .note-style h1,.note-toolbar .note-style h1,.note-popover .popover .popover-content .note-style h2,.note-toolbar .note-style h2,.note-popover .popover .popover-content .note-style h3,.note-toolbar .note-style h3,.note-popover .popover .popover-content .note-style h4,.note-toolbar .note-style h4,.note-popover .popover .popover-content .note-style h5,.note-toolbar .note-style h5,.note-popover .popover .popover-content .note-style h6,.note-toolbar .note-style h6,.note-popover .popover .popover-content .note-style blockquote,.note-toolbar .note-style blockquote {
	margin: 0;
}

.note-popover .popover .popover-content .note-color .dropdown-toggle,.note-toolbar .note-color .dropdown-toggle {
	width: 20px;
	padding-left: 5px !important;
	text-align:center;
	padding:0;
}

.note-popover .popover .popover-content .note-color .dropdown-menu,.note-toolbar .note-color .dropdown-menu {
	min-width: 340px;
}

.note-popover .popover .popover-content .note-color .dropdown-menu .btn-group,.note-toolbar .note-color .dropdown-menu .btn-group {
	margin: 0;
}

.note-popover .popover .popover-content .note-color .dropdown-menu .btn-group:first-child,.note-toolbar .note-color .dropdown-menu .btn-group:first-child {
	margin: 0 5px;
}

.note-popover .popover .popover-content .note-color .dropdown-menu .btn-group .note-palette-title,.note-toolbar .note-color .dropdown-menu .btn-group .note-palette-title {
	margin: 2px 7px;
	font-size: 12px;
	text-align: center;
	border-bottom: 1px solid #eee;
}

.note-popover .popover .popover-content .note-color .dropdown-menu .btn-group .note-color-reset,.note-toolbar .note-color .dropdown-menu .btn-group .note-color-reset {
	padding: 0 3px;
	margin: 3px;
	font-size: 11px;
	cursor: pointer;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
}

.note-popover .popover .popover-content .note-color .dropdown-menu .btn-group .note-color-row,.note-toolbar .note-color .dropdown-menu .btn-group .note-color-row {
	height: 20px;
}

.note-popover .popover .popover-content .note-color .dropdown-menu .btn-group .note-color-reset:hover,.note-toolbar .note-color .dropdown-menu .btn-group .note-color-reset:hover {
	background: #eee;
}

.note-popover .popover .popover-content .note-para .dropdown-menu,.note-toolbar .note-para .dropdown-menu {
	min-width: 216px;
	padding: 5px;
}

.note-popover .popover .popover-content .note-para .dropdown-menu>div:first-child,.note-toolbar .note-para .dropdown-menu>div:first-child {
	margin-right: 5px;
}

.note-popover .popover .popover-content .dropdown-menu,.note-toolbar .dropdown-menu {
	min-width: 90px;
}

.note-popover .popover .popover-content .dropdown-menu.right,.note-toolbar .dropdown-menu.right {
	right: 0;
	left: auto;
}

.note-popover .popover .popover-content .dropdown-menu.right::before,.note-toolbar .dropdown-menu.right::before {
	right: 9px;
	left: auto!important;
}

.note-popover .popover .popover-content .dropdown-menu.right::after,.note-toolbar .dropdown-menu.right::after {
	right: 10px;
	left: auto!important;
}

.note-popover .popover .popover-content .dropdown-menu li a i,.note-toolbar .dropdown-menu li a i {
	color: deepskyblue;
	visibility: hidden;
}

.note-popover .popover .popover-content .dropdown-menu li a.checked i,.note-toolbar .dropdown-menu li a.checked i {
	visibility: visible;
}

.note-popover .popover .popover-content .note-fontsize-10,.note-toolbar .note-fontsize-10 {
	font-size: 10px;
}

.note-popover .popover .popover-content .note-color-palette,.note-toolbar .note-color-palette {
	line-height: 1;
}

.note-popover .popover .popover-content .note-color-palette div .note-color-btn,.note-toolbar .note-color-palette div .note-color-btn {
	width: 20px;
	height: 20px;
	padding: 0;
	margin: 0;
	border: 1px solid #fff;
}

.note-popover .popover .popover-content .note-color-palette div .note-color-btn:hover,.note-toolbar .note-color-palette div .note-color-btn:hover {
	border: 1px solid #000;
}

.note-dialog>div {
	display: none;
}

.note-dialog .note-image-dialog .note-dropzone {
	min-height: 100px;
	margin-bottom: 10px;
	font-size: 30px;
	line-height: 4;
	color: lightgray;
	text-align: center;
	border: 4px dashed lightgray;
}

.note-dialog .note-help-dialog {
	font-size: 12px;
	color: #ccc;
	background: transparent;
	background-color: #222!important;
	border: 0;
	-webkit-opacity: .9;
	-khtml-opacity: .9;
	-moz-opacity: .9;
	opacity: .9;
	-ms-filter: alpha(opacity=90);
	filter: alpha(opacity=90);
}

.note-dialog .note-help-dialog .modal-content {
	background: transparent;
	border: 1px solid white;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
}

.note-dialog .note-help-dialog a {
	font-size: 12px;
	color: white;
}

.note-dialog .note-help-dialog .title {
	padding-bottom: 5px;
	font-size: 14px;
	font-weight: bold;
	color: white;
	border-bottom: white 1px solid;
}

.note-dialog .note-help-dialog .modal-close {
	font-size: 14px;
	color: #dd0;
	cursor: pointer;
}

.note-dialog .note-help-dialog .note-shortcut-layout {
	width: 100%;
}

.note-dialog .note-help-dialog .note-shortcut-layout td {
	vertical-align: top;
}

.note-dialog .note-help-dialog .note-shortcut {
	margin-top: 8px;
}

.note-dialog .note-help-dialog .note-shortcut th {
	font-size: 13px;
	color: #dd0;
	text-align: left;
}
.note-dialog .note-help-dialog .note-shortcut th,
.note-dialog .note-help-dialog .note-shortcut tr {
	background:transparent !important;
}
.note-dialog .note-help-dialog .note-shortcut td:first-child {
	min-width: 110px;
	padding-right: 10px;
	font-family: "Courier New";
	color: #dd0;
	text-align: right;
}

.note-handle .note-control-selection {
	position: absolute;
	display: none;
	border: 1px solid black;
}

.note-handle .note-control-selection>div {
	position: absolute;
}

.note-handle .note-control-selection .note-control-selection-bg {
	width: 100%;
	height: 100%;
	background-color: black;
	-webkit-opacity: .3;
	-khtml-opacity: .3;
	-moz-opacity: .3;
	opacity: .3;
	-ms-filter: alpha(opacity=30);
	filter: alpha(opacity=30);
}

.note-handle .note-control-selection .note-control-handle {
	width: 7px;
	height: 7px;
	border: 1px solid black;
}

.note-handle .note-control-selection .note-control-holder {
	width: 7px;
	height: 7px;
	border: 1px solid black;
}

.note-handle .note-control-selection .note-control-sizing {
	width: 7px;
	height: 7px;
	background-color: white;
	border: 1px solid black;
}

.note-handle .note-control-selection .note-control-nw {
	top: -5px;
	left: -5px;
	border-right: 0;
	border-bottom: 0;
}

.note-handle .note-control-selection .note-control-ne {
	top: -5px;
	right: -5px;
	border-bottom: 0;
	border-left: none;
}

.note-handle .note-control-selection .note-control-sw {
	bottom: -5px;
	left: -5px;
	border-top: 0;
	border-right: 0;
}

.note-handle .note-control-selection .note-control-se {
	right: -5px;
	bottom: -5px;
	cursor: se-resize;
}

.note-handle .note-control-selection .note-control-selection-info {
	right: 0;
	bottom: 0;
	padding: 5px;
	margin: 5px;
	font-size: 12px;
	color: white;
	background-color: black;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	-webkit-opacity: .7;
	-khtml-opacity: .7;
	-moz-opacity: .7;
	opacity: .7;
	-ms-filter: alpha(opacity=70);
	filter: alpha(opacity=70);
}

.note-toolbar button {
	height:30px;
}

section.dark .note-popover .popover .popover-content>.btn-group,
section.dark .note-toolbar>.btn-group>button {
	background-color:#333 !important;
}
section.dark .note-editor .note-statusbar,
section.dark .note-editor .note-editable {
	background-color:#333 !important;
}
section.dark .note-editor .note-toolbar {
	background-color: rgba(0,0,0,0.3);
	border-bottom: 1px solid #000;
}
section.dark .dropdown-menu,
section.dark .dropdown-menu p,
section.dark .dropdown-menu a,
section.dark .dropdown-menu>li>a,
section.dark .dropdown-menu>li>a>h1,
section.dark .dropdown-menu>li>a>h2,
section.dark .dropdown-menu>li>a>h3,
section.dark .dropdown-menu>li>a>h4,
section.dark .dropdown-menu>li>a>h5,
section.dark .dropdown-menu>li>a>h6,
section.dark .note-popover .popover .popover-content .note-para .dropdown-menu i.fa,
section.dark .note-toolbar .note-para .dropdown-menu i.fa {
	color:#000;
}






/**	52. Markdown
*************************************************** **/
.md-editor .md-footer,.md-editor>.md-header{display:block;padding:6px 4px;background:#f5f5f5}.md-editor>.md-header{margin:0}.md-editor>.md-preview{background:#fff;border-top:1px dashed #ddd;border-bottom:1px dashed #ddd;min-height:10px;overflow:auto}.md-editor>textarea{font-family:monospace;font-size:14px;outline:0;margin:0;display:block;padding:0;width:100%;border:0;border-top:1px dashed #ddd;border-bottom:1px dashed #ddd;border-radius:0;box-shadow:none;background:#eee}.md-editor>textarea:focus{box-shadow:none;background:#fff}.md-editor.active{border-color:#66afe9;outline:0;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6)}.md-editor .md-controls{float:right;padding:3px}.md-editor .md-controls .md-control{right:5px;color:#bebebe;padding:3px 3px 3px 10px}.md-editor .md-controls .md-control:hover{color:#333}.md-editor.md-fullscreen-mode{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99999;padding:60px 30px 15px;background:#fff!important;border:0!important}.md-editor.md-fullscreen-mode .md-footer{display:none}.md-editor.md-fullscreen-mode .md-input,.md-editor.md-fullscreen-mode .md-preview{margin:0 auto!important;height:100%!important;font-size:20px!important;padding:20px!important;color:#999;line-height:1.6em!important;resize:none!important;box-shadow:none!important;background:#fff!important;border:0!important}.md-editor.md-fullscreen-mode .md-preview{color:#333;overflow:auto}.md-editor.md-fullscreen-mode .md-input:focus,.md-editor.md-fullscreen-mode .md-input:hover{color:#333;background:#fff!important}.md-editor.md-fullscreen-mode .md-header{background:0 0;text-align:center;position:fixed;width:100%;top:20px}.md-editor.md-fullscreen-mode .btn-group{float:none}.md-editor.md-fullscreen-mode .btn{border:0;background:0 0;color:#b3b3b3}.md-editor.md-fullscreen-mode .btn.active,.md-editor.md-fullscreen-mode .btn:active,.md-editor.md-fullscreen-mode .btn:focus,.md-editor.md-fullscreen-mode .btn:hover{box-shadow:none;color:#333}.md-editor.md-fullscreen-mode .md-fullscreen-controls{position:absolute;top:20px;right:20px;text-align:right;z-index:1002;display:block}.md-editor.md-fullscreen-mode .md-fullscreen-controls a{color:#b3b3b3;clear:right;margin:10px;width:30px;height:30px;text-align:center}.md-editor.md-fullscreen-mode .md-fullscreen-controls a:hover{color:#333;text-decoration:none}.md-editor.md-fullscreen-mode .md-editor{height:100%!important;position:relative}.md-editor .md-fullscreen-controls{display:none}.md-nooverflow{overflow:hidden;position:fixed;width:100%}

/* custom rewrite */
.md-editor {
	display: block;
	border: 1px solid #ddd;
}

.md-editor>.md-header,.md-editor .md-footer {
	display: block;
	padding: 6px 4px;
	background: #fff;
}

.md-editor>.md-header {
	margin: 0;
}

.md-editor>.md-preview {
	background: #fff;
	border-top: 1px dashed #ddd;
	border-bottom: 1px dashed #ddd;
	min-height: 10px;
	overflow: auto;
}

.md-editor>textarea {
	font-size: 14px;
	outline: 0;
	outline: thin dotted \9;
	margin: 0;
	display: block;
	padding: 0;
	width: 100%;
	border: 0;
	border-top: 1px dashed #ddd;
	border-bottom: 1px dashed #ddd;
	border-radius: 0;
	box-shadow: none;
	background: #fafafa;
}

.md-editor>textarea:focus {
	box-shadow: none;
	background: #fff;
}

.md-editor.active {
	border-color: #999;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(0,0,0,0.1);
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(0,0,0,0.1);
}
.btn-toolbar>.btn, .btn-toolbar>.btn-group, .btn-toolbar>.input-group {
	margin-bottom:0;
}
.btn-toolbar>.btn>i.fa,
.btn-toolbar>.btn>span.glyphicon  {
	color:#fff !important;
}

.md-editor.md-fullscreen-mode .md-controls {
	display:none;
}
.md-editor.md-fullscreen-mode .md-fullscreen-controls {
	top:28px;
}

section.dark .md-editor {
	border-color:transparent;
}
section.dark .md-editor .md-header .btn-default {
	border:0;
	background-color:#333;
}
section.dark .md-editor>.md-header {
	background-color:#000;
}
section.dark .md-editor>textarea {
	color:#fff;
	background-color:#333;
	border-top-color:#111;
	border-bottom-color:#111;
}

section.dark .md-editor.md-fullscreen-mode .md-input,
section.dark .md-editor.md-fullscreen-mode .md-preview,
section.dark .md-editor.md-fullscreen-mode {
	color:#fff;
	background-color:#333 !important;
}
