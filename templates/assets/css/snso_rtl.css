﻿﻿/*#SNSO Override*/
.snsolabel {
    white-space: nowrap
}

.snsoinput {
    background-color: #F0F0F0;
    min-height: 35px;
    margin-top: 2px;
    margin-bottom: 2px;
    padding: 2px;
    padding-right: 5px;
}

/*.snsoinput input[type=radio] {*/
    /*opacity: 1;*/
    /*position: static;*/
/*}*/

/*.snsoinput input[type=checkbox] {*/
    /*opacity: 1;*/
    /*position: static;*/
/*}*/

/*.table input[type=radio]{*/
    /*opacity: 1;*/
    /*position: static;*/
/*}*/

/*input[type=checkbox] {*/
    /*opacity: 1;*/
/*}*/

/*td > input[type=checkbox] {*/
    /*opacity: 1;*/
    /*position: inherit;*/
/*}*/

input[type=text] {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
}

input[type=number] {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
}

input[type=email] {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
}

input[type=password] {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
}

input[type=number].hide-spinner
{
    -moz-appearance:textfield;
    /*-webkit-appearance: none;*/
    /*margin: 0;*/
    /*-webkit-appearance: none;*/
    /*-moz-appearance: none;*/
    /*appearance: none;*/
    /*margin: 0;*/

}
input[type=number].hide-spinner::-webkit-inner-spin-button,
input[type=number].hide-spinner::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.snsoinput-site {
    margin-top: 2px;
    margin-bottom: 2px;
    padding: 2px
}

.pagetitle {
    background-color: #E8E8E8;
    color: #333;
    font-weight: 700;
    padding: 5px;
    margin: 0
}

.pagetitle div {
    padding: 0
}

.pagetitle select {
    color: #333
}

.snsowraper {
    padding-left: 15px;
    padding-right: 15px;
    margin-bottom: 20px
}

.snsosmallwraper {
    padding-left: 15px;
    padding-right: 15px;
    margin-bottom: 3px
}

.profile-container .profile-header .profile-stats .stats-col {
    margin: 15px 0;
    text-align: center;
}

.user_home_title{
    display: block;
    padding-top: 5px;
    color: #1b6d85;
    font-size: 12px;
}

.user_home_title :hover{
    text-decoration: none;
    text-underline: none;
}


.fin-tag {
    border-top: 3px solid rgb(71, 69, 68) !important;
    -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .15);
    -moz-box-shadow: 0 1px 2px rgba(0, 0, 0, .15);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
    position: absolute;
    font-size: 12px;
    height: 30px;
    top: -3px;
    background-color: white;
    padding: 3px;
    left: 20px;

}

.tag-inline {
    /** this to make them inline **/
    left: 0 !important;
    right: 0 !important;
    display: inline-block !important;
    position: relative !important;
}

.fin-tag-content {
    position: relative;
    line-height: 1.5;
    font-size: 15px;
    padding: 0 1em;
    z-index:1;
}

.navbar .navbar-inner .navbar-header .navbar-account .account-area > li > a .badge {
    position: absolute;
    top: 14px;
    right: -9px;
    padding: 2px;
    cursor: pointer;
    background-color: transparent !important;
    border: 0px solid #fff;
    width: 18px;
    height: 18px;
    box-shadow: -1px 1px 0 #2dc3e8;
    -webkit-transition: top .3s ease;
    -moz-transition: top .3s ease;
    -o-transition: top .3s ease;
    transition: top .3s ease;
}

.page-body {
    background: #eee;
    padding: 15px 12px 19px;
}


.table-responsive[data-pattern="priority-columns"] > .table > thead > tr > th {
    text-align: center;
    vertical-align: middle;
    color: white;
    font-weight: inherit;
    background-color: #8CC474;
}

.table thead > tr > th {
    text-align: center;
    vertical-align: middle;
    color: white;
    font-weight: inherit;
    background-color: #A0D468;
}

.widget-header {
    position: relative;
    min-height: 35px;
    background: #fff;
    -webkit-box-shadow: 0 0 4px rgba(0,0,0,.3);
    -moz-box-shadow: 0 0 4px rgba(0,0,0,.3);
    box-shadow: 0 0 1px rgba(0,0,0,.3);
    color: #555;
    padding-right: 12px;
    text-align: left;
}

.widget-body {
    background-color: #fbfbfb;
    -webkit-box-shadow: -1px 0 10px 1px rgba(0,0,0,.3);
    -moz-box-shadow: -1px 0 10px 1px rgba(0,0,0,.3);
    box-shadow: 0 0 1px rgba(0,0,0,.3);
    padding: 12px;
}

.wizard ul li {
    float: right;
    margin: 0;
    padding: 0 23px 0 14px;
    line-height: 46px;
    position: relative;
    background: #f5f5f5;
    color: #d0d0d0;
    font-size: 12px;
    cursor: default;
    -webkit-transition: all .218s ease;
    -moz-transition: all .218s ease;
    -o-transition: all .218s ease;
    transition: all .218s ease;
}
/*#region Register*/
.register-container222 {
    position: relative;
    /*margin: 8% auto;*/
    /*max-width: 350px;*/
}
.register-container222 .registerbox {
    position: relative;
    /*width: 350px !important;*/
    /*height: 560px !important;*/
    padding: 0;
    -webkit-box-shadow: 0 0 14px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 0 14px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 14px rgba(0, 0, 0, 0.1);
}
.register-container222 .registerbox .registerbox-title {
    position: relative;
    text-align: left;
    width: 100%;
    height: 35px;
    padding: 20px 20px 0;
    font-family: 'Lucida Sans', 'trebuchet MS', Arial, Helvetica;
    font-size: 18px;
    text-transform: uppercase;
    font-weight: normal;
    color: #444444;
}
.register-container222 .registerbox .registerbox-caption {
    font-size: 14px;
    font-weight: 500;
    color: darkgray;
    padding: 15px 20px 0;
}
.register-container222 .registerbox .registerbox-textbox {
    padding: 5px 25px;
}
.register-container222 .registerbox .registerbox-textbox .form-control {
    -webkit-border-radius: 3px !important;
    -webkit-background-clip: padding-box !important;
    -moz-border-radius: 3px !important;
    -moz-background-clip: padding !important;
    border-radius: 3px !important;
    background-clip: padding-box !important;
}
.register-container222 .registerbox .registerbox-submit {
    padding: 0 20px;
}
.register-container222 .logobox {
    /*width: 350px !important;*/
    /*height: 50px !important;*/
    padding: 5px;
    margin-top: 15px;
    -webkit-box-shadow: 0 0 14px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 0 14px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 14px rgba(0, 0, 0, 0.1);
    background-color: #ffffff;
    text-align: left;
}
