version: '3.8'

services:
  # SNSO ERP Application
  snso-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: snso-erp-app
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
      - ./docker/logs/apache:/var/log/apache2
      - ./docker/logs/php:/var/log/php
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html
      - PHP_MEMORY_LIMIT=512M
      - PHP_UPLOAD_MAX_FILESIZE=100M
      - PHP_POST_MAX_SIZE=100M
    depends_on:
      - snso-db
      - snso-redis
    networks:
      - snso-network
    restart: unless-stopped

  # MySQL 5.5 Database (Legacy)
  snso-db:
    image: mysql:5.5
    container_name: snso-erp-db
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: snso_root_password
      MYSQL_DATABASE: snso_erp
      MYSQL_USER: snso_user
      MYSQL_PASSWORD: snso_password
    volumes:
      - snso_db_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf:/etc/mysql/conf.d
      - ./docker/logs/mysql:/var/log/mysql
    command: --sql_mode="" --innodb_use_native_aio=0
    networks:
      - snso-network
    restart: unless-stopped

  # Redis for caching and sessions
  snso-redis:
    image: redis:5.0-alpine
    container_name: snso-erp-redis
    ports:
      - "6379:6379"
    volumes:
      - snso_redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - snso-network
    restart: unless-stopped

  # PhpMyAdmin for database management
  snso-phpmyadmin:
    image: phpmyadmin/phpmyadmin:4.9
    container_name: snso-erp-phpmyadmin
    ports:
      - "8081:80"
    environment:
      PMA_HOST: snso-db
      PMA_PORT: 3306
      PMA_USER: snso_user
      PMA_PASSWORD: snso_password
      MYSQL_ROOT_PASSWORD: snso_root_password
    depends_on:
      - snso-db
    networks:
      - snso-network
    restart: unless-stopped

  # Node.js development server for frontend assets
  snso-node:
    image: node:10-alpine
    container_name: snso-erp-node
    working_dir: /app
    volumes:
      - .:/app
      - snso_node_modules:/app/node_modules
    command: sh -c "npm install && npm run watch"
    networks:
      - snso-network
    restart: unless-stopped

volumes:
  snso_db_data:
    driver: local
  snso_redis_data:
    driver: local
  snso_node_modules:
    driver: local

networks:
  snso-network:
    driver: bridge
