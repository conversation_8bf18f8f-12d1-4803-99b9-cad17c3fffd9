{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_change_it_manager#} </h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form action='{url urltype="path" url_string="bsc/P056/clientManagerDashboard/show/0/{$smarty.session.lang}/changeItManager/{$smarty.session.s_clientManagerDashboard_token}"}' method='POST' id='form'>
                <div class="col-lg-12">
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_employee#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        {$i=1}
                        <select name="itManagerId">
                            {foreach $employees as $employee}
                                <option value="{$employee->sh_user_id}" {if $employee->sh_user_id eq $itManager->id} selected="selected" {/if}>{$employee->sh_user_full_name}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button></div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}