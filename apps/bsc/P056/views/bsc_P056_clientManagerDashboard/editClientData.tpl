{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script type="text/javascript">

        $(document).ready(function () {

                $.get('/framework/core/functions/ajax/country_change.php', function (data) {
                    $("#region").html(data);

                $.get('/framework/core/functions/ajax/region_change.php', function (data) {
                    $("#town").html(data);
                });

            });

        });

        $("#country").change(function () {
            $.get('/framework/core/functions/ajax/country_change.php?country_selected_id=' + $(this).val(), function (data) {
                $("#region").html(data);
            });
            $.get('/framework/core/functions/ajax/region_change.php?region_selected_id=999999', function (data) {
                $("#town").html(data);
            });

        });

        $("#region").change(function () {
            $.get('/framework/core/functions/ajax/region_change.php?region_selected_id=' + $(this).val(), function (data) {
                $("#town").html(data);
            });
        });
    </script>
{/block}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_client_data#} </h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form action='{url urltype="path" url_string="bsc/P056/clientManagerDashboard/show/0/{$smarty.session.lang}/editClientData/{$smarty.session.s_clientManagerDashboard_token}"}' method="post">
                <div class="row snsowraper">
                    <div class="col-lg-12">
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_org_name#}<label style="color: rgb(251, 100, 30); ">*</label></div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <input type="text" class="form-control" id="name" name="name" value="{$client->name}" placeholder="{#p_org_name#}" required>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_org_type#}<label style="color: rgb(251, 100, 30); ">*</label></div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <select name="type_id" required>
                                <option value="">{#gnr_choose_from_menu#}</option>
                                {foreach $organizationTypes as $type}
                                    <option value="{$type->id}" {if $type->id eq $client->type_id}selected="selected"{/if}>{$type->translatedName}</option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_country#}<label style="color: rgb(251, 100, 30); ">*</label></div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <select name="country" id="country" required class="no-search form-control" style="height: 50px !important;">
                                {foreach $countries as $country}
                                    <option value="{$country->id}" {if $country->id eq $client->country} selected="selected" {/if}>{$country->translatedName}</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_region#}<label style="color: rgb(251, 100, 30); ">*</label></div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <select name="region" id="region" required class="no-search form-control" style="height: 50px !important;"></select>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_city#}<label style="color: rgb(251, 100, 30); ">*</label></div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <select name="town" id="town" required class="no-search form-control" style="height: 50px !important;"></select>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_address#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                <textarea class="form-control" id="address" name="address" placeholder="{#gnr_address#}">{$client->address}</textarea>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_vat_tax#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <textarea class="form-control" id="vat_tax" name="vat_tax" placeholder="">{$client->vat_tax}</textarea>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_definition#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <textarea class="form-control" rows="5" id="about" name="about" placeholder="{#define#}">{$client->about}</textarea>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_view#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <textarea rows="7" class="form-control" name="vission" placeholder="">{$client->vission}</textarea>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_mession#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <textarea rows="7" class="form-control" name="mission" placeholder="">{$client->mission}</textarea>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_aim#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <textarea rows="7" class="form-control" name="aim" placeholder="">{$client->aim}</textarea>
                        </div>


                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <button type="submit" class="btn btn-warning sharp">{#gnr_update_data#}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}