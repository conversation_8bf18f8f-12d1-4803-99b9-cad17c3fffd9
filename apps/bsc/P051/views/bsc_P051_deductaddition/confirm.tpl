{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_delete_row#} </h4>
    </div>
    <div class="modal-body">
        <div class="row snsowraper">
            <div class="snsowraper text-center">
                {#gnr_delete_row_confirm#}<br><br>
            </div>
            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_employee_name#}</div>
            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=sh_user id=$row->user_id}</div>

            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_request_type#}</div>
            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$row->type}</div>


            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_amount#}</div>
            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$row->amount}</div>

            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">&nbsp;&nbsp;</div>
            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsolabel">
                {url check=0 urltype="delete" opr_code='deductaddition' url_string="bsc/P051/deductaddition/show/0/{$smarty.session.lang}/delete/{$smarty.session.s_deductaddition_token}/{$row->id}"}
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}