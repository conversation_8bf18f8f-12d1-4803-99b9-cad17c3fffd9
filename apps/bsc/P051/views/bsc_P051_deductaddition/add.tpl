{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add_row#} </h4>
    </div>
    <div class="modal-body">

        {if $grfExistenceNum eq 1}
            <div class="row">

                <div class="col-lg-12">

                    <form method="post" action='{url urltype="path" url_string="bsc/P051/deductaddition/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_deductaddition_token}"}'>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_employee#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <select name="user_id" id="user_id">
                                <option value="">{#gnr_choose_employee#}</option>
                                {foreach $employees as $employee}
                                    <option value="{$employee->sh_user_id}">{$employee->sh_user_full_name}</option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_request_type#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                {foreach $deductaddition_options_list as $conrow}
                                    <div class="radio">
                                        <label>
                                            <input name="type" value="{$conrow->id}" id="{$conrow->id}" type="radio">
                                            <span class="text">{$conrow->translatedName}</span>
                                        </label>
                                    </div>
                                {/foreach}
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_amount#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <input type="number"
                                   step="any"
                                   id="amount"
                                   name="amount"
                                   placeholder="{#gnr_amount#}"
                                   required>
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <input type="text"
                                   id="amount_in_words"
                                   name="amount_in_words"
                                   class="form-control"
                                   placeholder="{#gnr_amount_words#}"
                                   readonly>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_excuses#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <textarea name="reasons" class="form-control" placeholder="{#gnr_excuses#}"></textarea>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                        </div>

                    </form>

                </div>

            </div>
        {/if}

        {if $grfExistenceNum eq 0}
            {GraphNotPreparedCorrectlly oprCode='deductaddition'}
        {/if}

    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
{block name=footer}
    <script src="/templates/assets/resources/dist/app.js"></script>
    <script>
        $(document).ready(function () {
            var amount = $('#amount');
            var amount_in_words = $('#amount_in_words');
            amount.keyup(lodash.throttle(function () {
                $.get('/api.php/number-to-words/' + amount.val())
                    .then(function (data) {
                        amount_in_words.val(data.number);
                    })
            }, 500));
        });
    </script>
{/block}
