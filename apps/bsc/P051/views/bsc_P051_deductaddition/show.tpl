{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />
{/block}
{block name=page_body}

    <div class="row">
        <div class="col-lg-12">
            <div class="margin-bottom-20">
                <a href="{url urltype="path" url_string="bsc/P051/PersonnelArchive/show/0/{$smarty.session.lang}/deductaddition"}" 
                   class="btn btn-warning shiny">
                    <i class="fa fa-archive"></i>
                    الطلبات المؤرشفة
                </a>
            </div>
        </div>
    </div>

    <div id="deductaddition" class="tab-pane {if $smarty.session.s_personnel_tab eq 'deductaddition'} active {/if}">
    
        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
            <thead>
                <tr>
                    <th width="2%" style="background-color: #A0D468 !important;">
                        {url check=1 urltype="madd" opr_code='deductaddition' url_string="bsc/P051/deductaddition/add/0/{$smarty.session.lang}"}
                    </th>
                    <th width="20%" style="background-color: #A0D468 !important;">{#gnr_employee_name#}</th>
                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_type#}</th>
                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_amount#}</th>
                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_request_status#}</th>
                    <th width="15%" style="background-color: #A0D468 !important;" align="center">{#gnr_settings#}</th>
                    <th width="18%" style="background-color: #A0D468 !important;" align="center">المسير</th>
                </tr>
            </thead>
            <tbody>
                {$i=1}
                {foreach $deductadditionRequests as $request}

                    <tr>
                        <td align="center">{$i++}</td>
                        <td align="center">{$request->userObject->full_name|default:'-'}</td>
                        <td align="center" style="background-color: {if $request->type == 916}#FFE4E1{else}#E0FFE0{/if}">
                            {if $request->type == 916}
                                <span class="label label-danger" style="cursor: pointer;" data-toggle="modal"
                                    data-target="#reasonsModal{$request->id}">طلب خصم</span>
                            {else}
                                <span class="label label-success" style="cursor: pointer;" data-toggle="modal"
                                    data-target="#reasonsModal{$request->id}">طلب إضافة</span>
                            {/if}

                            <!-- Modal Reasons -->
                            <div class="modal fade" id="reasonsModal{$request->id}" tabindex="-1" role="dialog"
                                aria-labelledby="reasonsModalLabel{$request->id}">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                                    >&times;</span></button>
                                            <h4 class="modal-title" id="reasonsModalLabel{$request->id}">
                                                {if $request->type == 916}
                                                    مبررات طلب الخصم
                                                {else}
                                                    مبررات طلب الإضافة
                                                {/if}
                                            </h4>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="well"
                                                        style="background-color: #f8f9fa; border: 1px solid #ddd; padding: 15px; border-radius: 4px;">
                                                        <h5 style="margin-top: 0; color: #666;">المبررات:</h5>
                                                        <p style="white-space: pre-line;">
                                                            {$request->reasons|default:'لا توجد مبررات'}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" data-dismiss="modal">إغلاق</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td align="center">{$request->amount|number_format:2:".":","|default:'0.00'}</td>
                        <td align="center">
                            <span class="label label-success">معتمد</span>
                        </td>
                        <td align="center" nowrap>
                            {if !$request->manipulation_batche_id}
                                {url check=1 urltype="medit" opr_code='deductaddition' url_string="bsc/P051/deductaddition/edit/0/{$smarty.session.lang}/{$request->id}/tab3"}
                                {url check=1 urltype="mdelete" opr_code='deductaddition' url_string="bsc/P051/deductaddition/confirm/0/{$smarty.session.lang}/{$request->id}"}
                                

                            {else}
                                <span class="text-muted" style="display: inline-block; padding: 5px 10px; background-color: #f8f9fa; border-radius: 4px; border: 1px solid #ddd;">
                                    <i class="fa fa-lock"></i>
                                    <span style="margin-right: 5px;">مرتبط بمسير - لا يمكن التعديل</span>
                                </span>
                            {/if}
                            {url check=0 urltype="button" opr_code='deductaddition' oprvtype=3 url_string="bsc/P051/deductaddition/show/0/{$smarty.session.lang}/archive/{$smarty.session.s_deductaddition_token}/{$request->id}" text_value="{#gnr_archiving#}"}
                        </td>
                        <td align="center">
                            {if $request->manipulation_batche_id}
                                <span class="label label-info" style="cursor: pointer;" data-toggle="modal"
                                    data-target="#batchModal{$request->manipulation_batche_id}">
                                    {if $request->batchObject}
                                        {$request->batchObject->payroll_batch_name}
                                    {else}
                                        {getname table=prl_batches id=$request->manipulation_batche_id}
                                    {/if}
                                </span>

                                <!-- Modal -->
                                <div class="modal fade" id="batchModal{$request->manipulation_batche_id}" tabindex="-1"
                                    role="dialog" aria-labelledby="batchModalLabel{$request->manipulation_batche_id}">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                                        >&times;</span></button>
                                                <h4 class="modal-title" id="batchModalLabel{$request->manipulation_batche_id}">
                                                    تفاصيل الدفعة</h4>
                                            </div>
                                            <div class="modal-body">
                                                {if $request->batchObject}
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <p><strong>اسم الدفعة:</strong> {$request->batchObject->payroll_batch_name}
                                                            </p>
                                                            <p><strong>نوع الدفعة:</strong> {$request->batchObject->payroll_batch_type}
                                                            </p>
                                                            <p><strong>من تاريخ:</strong>
                                                                {$request->batchObject->payroll_batch_from_date}</p>
                                                            <p><strong>إلى تاريخ:</strong>
                                                                {$request->batchObject->payroll_batch_to_date}</p>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <p><strong>عدد الأيام:</strong> {$request->batchObject->payroll_batch_days}
                                                            </p>
                                                            <p><strong>تاريخ الإنشاء:</strong>
                                                                {$request->batchObject->payroll_batch_created_date}</p>
                                                            <p><strong>حالة التأكيد:</strong>
                                                                {if $request->batchObject->payroll_batch_confirm_status eq 357}
                                                                    <span class="label label-success">تم التأكيد</span>
                                                                {elseif $request->batchObject->payroll_batch_confirm_status eq 358}
                                                                    <span class="label label-warning">قيد الانتظار</span>
                                                                {elseif $request->batchObject->payroll_batch_confirm_status eq 359}
                                                                    <span class="label label-danger">تم الرفض</span>
                                                                {/if}
                                                            </p>
                                                        </div>
                                                    </div>
                                                {else}
                                                    <p class="text-center">لا توجد بيانات متاحة للدفعة</p>
                                                {/if}
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-default" data-dismiss="modal">إغلاق</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {else}
                                <span class="label label-default">غير محدد</span>
                            {/if}
                        </td>
                    </tr>

                {/foreach}
            </tbody>
        </table>
    </div>

{/block}