{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
    <script src="/templates/assets/js/Chart.bundle.min.js"></script>
{/block}

{block name=page_body}
    <div class="widget flat radius-bordered">
        <div class="tabbable tabs-left">
            <ul class="nav nav-tabs hidden-print" id="myTab3">
                <li class="tab-sky active">
                    <a aria-expanded="false" data-toggle="tab" href="#edu"><span
                                class="badge badge-sky badge-square">1</span> {#p_qualifications#} </a>
                </li>
                <li class="tab-sky">
                    <a aria-expanded="false" data-toggle="tab" href="#jobs"><span
                                class="badge badge-sky badge-square">2</span> {#p_jobs#} </a>
                </li>
                <li class="tab-sky">
                    <a aria-expanded="false" data-toggle="tab" href="#nation"><span
                                class="badge badge-sky badge-square">3</span> {#p_nationalities#} </a>
                </li>
                <li class="tab-sky">
                    <a aria-expanded="false" data-toggle="tab" href="#doam"><span
                                class="badge badge-sky badge-square">4</span> {#p_doam#} </a>
                </li>
                <li class="tab-sky">
                    <a aria-expanded="false" data-toggle="tab" href="#sex"><span
                                class="badge badge-sky badge-square">5</span> {#gnr_sex#} </a>
                </li>
                <li class="tab-sky">
                    <a aria-expanded="false" data-toggle="tab" href="#unit"><span
                                class="badge badge-sky badge-square">6</span> {#gnr_units#} </a>
                </li>
                <li class="tab-sky">
                    <a aria-expanded="false" data-toggle="tab" href="#socialStatus"><span
                                class="badge badge-sky badge-square">6</span> {#gnr_social_status#} </a>
                </li>
            </ul>
            <div class="tab-content" style="overflow-x: hidden">
                <div id="edu" class="tab-pane active">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-12"><h3>{#p_qualifications#}</h3>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="col-lg-6 col-md-5 col-sm-12 col-xs-12">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th width="5%"></th>
                                        <th width="40%">{#gnr_category#}</th>
                                        <th width="20%" class="text-center">{#gnr_number#}</th>
                                        <th width="20%" class="text-center">{#gnr_percentage#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $eData.EmployeesCount as $key => $value}
                                        {if $value ne 0}
                                            <tr>
                                                <td align="center">{$i++}</td>
                                                <td>{$key}</td>
                                                <td class="text-center">{$value}</td>
                                                <td class="text-center">{getpercent value=$value sum=$eData.sum}%</td>
                                            </tr>
                                        {/if}
                                    {/foreach}
                                    </tbody>
                                    <tfoot>
                                    <tr>
                                        <th class="bg-gray"></th>
                                        <th class="bg-gray">{#gnr_total#}</th>
                                        <th class="bg-gray text-center">{$eData.sum}</th>
                                        <th class="bg-gray text-center">100%</th>
                                    </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <div class="col-lg-6 col-md-5 col-sm-12 col-xs-12">
                                {chart config=$educationChart }
                            </div>

                        </div>
                    </div>
                </div>
                <div id="jobs" class="tab-pane">

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-12"><h3>{#gnr_jobs#}</h3>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <table class="table table-bordered table-hover">
                                <thead>
                                <tr>
                                    <th width="5%"></th>
                                    <th width="40%">{#gnr_category#}</th>
                                    <th width="20%" class="text-center">{#gnr_number#}</th>
                                    <th width="20%" class="text-center">{#gnr_percentage#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i = 1}
                                {foreach $jData.EmployeesCount as $key => $value}
                                    {if $value ne 0}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td>{$key}</td>
                                            <td class="text-center">{$value}</td>
                                            <td class="text-center">{getpercent value=$value sum=$jData.sum}%</td>
                                        </tr>
                                    {/if}
                                {/foreach}
                                </tbody>
                                <tfoot>
                                <tr>
                                    <th class="bg-gray"></th>
                                    <th class="bg-gray">{#gnr_total#}</th>
                                    <th class="bg-gray text-center">{$jData.sum}</th>
                                    <th class="bg-gray text-center">100%</th>
                                </tr>
                                </tfoot>
                            </table>

                        </div>
                        <div class="col-lg-6 col-md-5 col-sm-12 col-xs-12">
                            {chart config=$jobChart}
                        </div>

                    </div>

                </div>
                <div id="nation" class="tab-pane">

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-12">
                                <h3>{#p_nationalities#}</h3>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th width="5%"></th>
                                        <th width="40%">{#gnr_category#}</th>
                                        <th width="20%">{#gnr_number#}</th>
                                        <th width="20%">{#gnr_percentage#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i = 1}
                                    {foreach $nData.EmployeesCount as $key => $value}
                                        {if $value ne 0}
                                            <tr>
                                                <td align="center">{$i++}</td>
                                                <td>{$key}</td>
                                                <td class="text-center">{$value}</td>
                                                <td class="text-center">{getpercent value=$value sum=$nData.sum}%</td>
                                            </tr>
                                        {/if}
                                    {/foreach}
                                    </tbody>
                                    <tfoot>
                                    <tr>
                                        <th class="bg-gray"></th>
                                        <th class="bg-gray">{#gnr_total#}</th>
                                        <th class="bg-gray text-center">{$nData.sum}</th>
                                        <th class="bg-gray text-center">100%</th>
                                    </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <div class="col-lg-6 col-md-5 col-sm-12 col-xs-12">
                                {chart config=$nationalityChart}
                            </div>
                        </div>
                    </div>

                </div>
                <div id="doam" class="tab-pane">

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-12">
                                <h3>{#p_doam#}</h3>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th width="5%"></th>
                                        <th width="40%">{#gnr_category#}</th>
                                        <th width="20%">{#gnr_number#}</th>
                                        <th width="20%">{#gnr_percentage#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i = 1}
                                    {foreach $dData.EmployeesCount as $key => $value}
                                        {if $value ne 0}
                                            <tr>
                                                <td align="center">{$i++}</td>
                                                <td>{$key}</td>
                                                <td class="text-center">{$value}</td>
                                                <td class="text-center">{getpercent value=$value sum=$dData.sum}%</td>
                                            </tr>
                                        {/if}
                                    {/foreach}
                                    </tbody>
                                    <tfoot>
                                    <tr>
                                        <th class="bg-gray"></th>
                                        <th class="bg-gray">{#gnr_total#}</th>
                                        <th class="bg-gray text-center">{$dData.sum}</th>
                                        <th class="bg-gray text-center">100%</th>
                                    </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <div class="col-lg-6 col-md-5 col-sm-12 col-xs-12">
                                {chart config=$dowmChart}
                            </div>

                        </div>
                    </div>

                </div>
                <div id="sex" class="tab-pane">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-12">
                                <h3>{#gnr_sex#}</h3>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <table class="table table-bordered table-hover">
                                <thead>
                                <tr>
                                    <th width="5%"></th>
                                    <th width="40%">{#gnr_category#}</th>
                                    <th width="20%">{#gnr_number#}</th>
                                    <th width="20%">{#gnr_percentage#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i = 1}
                                {foreach $gData.EmployeesCount as $key => $value}
                                    {if $value ne 0}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td>{$key}</td>
                                            <td class="text-center">{$value}</td>
                                            <td class="text-center">{getpercent value=$value sum=$gData.sum}%</td>
                                        </tr>
                                    {/if}
                                {/foreach}
                                </tbody>
                                <tfoot>
                                <tr>
                                    <th class="bg-gray"></th>
                                    <th class="bg-gray">{#gnr_total#}</th>
                                    <th class="bg-gray text-center">{$gData.sum}</th>
                                    <th class="bg-gray text-center">100%</th>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                        <div class="col-lg-6 col-md-5 col-sm-12 col-xs-12">
                            {chart config=$genderChart}
                        </div>
                    </div>
                </div>
                <div id="unit" class="tab-pane">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-12">
                                <h3>{#gnr_units#}</h3>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <table class="table table-bordered table-hover">
                                <thead>
                                <tr>
                                    <th width="5%"></th>
                                    <th width="40%">{#gnr_category#}</th>
                                    <th width="20%">{#gnr_number#}</th>
                                    <th width="20%">{#gnr_percentage#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i = 1}
                                {foreach $uData.EmployeesCount as $key => $value}
                                    {if $value ne 0}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td>{$key}</td>
                                            <td class="text-center">{$value}</td>
                                            <td class="text-center">{getpercent value=$value sum=$uData.sum}%</td>
                                        </tr>
                                    {/if}
                                {/foreach}
                                </tbody>
                                <tfoot>
                                <tr>
                                    <th class="bg-gray"></th>
                                    <th class="bg-gray">{#gnr_total#}</th>
                                    <th class="bg-gray text-center">{$uData.sum}</th>
                                    <th class="bg-gray text-center">100%</th>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                        <div class="col-lg-6 col-md-5 col-sm-12 col-xs-12">
                            {chart config=$unitChart}
                        </div>

                    </div>
                </div>
                <div id="socialStatus" class="tab-pane">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-12">
                                <h3>{#gnr_social_status#}</h3>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <table class="table table-bordered table-hover">
                                <thead>
                                <tr>
                                    <th width="5%"></th>
                                    <th width="40%">{#gnr_category#}</th>
                                    <th width="20%">{#gnr_number#}</th>
                                    <th width="20%">{#gnr_percentage#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i = 1}
                                {foreach $sData.EmployeesCount as $key => $value}
                                    {if $value ne 0}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td>{$key}</td>
                                            <td class="text-center">{$value}</td>
                                            <td class="text-center">{getpercent value=$value sum=$sData.sum}%</td>
                                        </tr>
                                    {/if}
                                {/foreach}
                                </tbody>
                                <tfoot>
                                <tr>
                                    <th class="bg-gray"></th>
                                    <th class="bg-gray">{#gnr_total#}</th>
                                    <th class="bg-gray text-center">{$sData.sum}</th>
                                    <th class="bg-gray text-center">100%</th>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                        <div class="col-lg-6 col-md-5 col-sm-12 col-xs-12">
                            {chart config=$socialStatusChart}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}