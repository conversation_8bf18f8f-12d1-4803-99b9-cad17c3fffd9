{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=title}{#gnr_payroll#}{/block}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" xmlns:x-on="http://www.w3.org/1999/xhtml"
        xmlns:x-bind="http://www.w3.org/1999/xhtml" xmlns:x-bind="http://www.w3.org/1999/xhtml" />
{/block}
{block name=page_body}
    <div class="widget flat radius-bordered">
        <div class="tabbable tabs-left">
            <ul class="nav nav-tabs" id="myTab3">
                <li class="tab-sky {$smarty.session.tab0} {$smarty.session.tab1}">
                    <a aria-expanded="false" data-toggle="tab" href="#tab0"><span
                            class="badge badge-sky badge-square sharp">3</span> {#p_payslips_preparation_new#}</a>
                </li>
                <li class="tab-sky {$smarty.session.tab3}">
                    <a aria-expanded="false" data-toggle="tab" href="#tab3"><span
                            class="badge badge-sky badge-square">2</span> {#p_adjust_employees#}</a>
                </li>
                <li class="tab-sky {$smarty.session.tab4}">
                    <a aria-expanded="false" data-toggle="tab" href="#tab4"><span
                            class="badge badge-sky badge-square">1</span> {#p_salaries_template#}</a>
                </li>
            </ul>

            <div class="tab-content">
                <div id="tab0" class="tab-pane {$smarty.session.tab0} {$smarty.session.tab1}">
                    <div class="tabbable">
                        <ul class="nav nav-tabs nav-justified" id="myTab0">
                            <li class="{if $smarty.session.s_tab_0_top_tab eq 'payrollView'} active {/if}">
                                <a data-toggle="tab" href="#payrollView">
                                    <span>{#p_payroll_batch_view#}</span>
                                </a>
                            </li>
                            <li class="tab-red {if $smarty.session.s_tab_0_top_tab eq 'payrollSave'} active {/if}">
                                <a data-toggle="tab" href="#payrollSave">
                                    <span>{#p_payroll_batch_save#}</span>
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            <div id="payrollView"
                                class="tab-pane {if $smarty.session.s_tab_0_top_tab eq 'payrollView'} active {/if}">
                                <div class="widget mt-6">
                                    <div class="widget-header bg-blue">
                                        <i class="widget-icon fa fa-arrow-left"></i>
                                        <span class="widget-caption">{#gnr_view#}</span>
                                        <div class="widget-buttons">
                                            <a href="#" data-toggle="collapse">
                                                <i class="fa fa-minus"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="widget-body">
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <form
                                                    action="{url urltype=path url_string="bsc/P051/payroll/show/0/{$smarty.session.lang}/save_session/tab0/browse"
                                                }" method="post">
                                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">
                                                        <label for="name">{#p_payslip_name#}</label>
                                                    </div>
                                                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                        <input type="text" class="form-control" name="name" id="name"
                                                            required
                                                            value="{($smarty.session.payroll_name neq null) ? $smarty.session.payroll_name : $batch->name}">
                                                    </div>

                                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">
                                                        {#gnr_type#}</div>
                                                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput" required>
                                                        <div class="control-group">
                                                            <div class="radio">
                                                                <label>
                                                                    <input type="radio" name="payroll_type" value="units"
                                                                        {if $smarty.session.payroll_type eq 'all_units' or $smarty.session.payroll_type eq 'units'}
                                                                        checked="checked" {/if} id="unitRadioButton">
                                                                    <span class="text">{#gnr_management_unit#}</span>
                                                                </label>
                                                            </div>
                                                            <div class="radio">
                                                                <label>
                                                                    <input type="radio" name="payroll_type"
                                                                        value="templates"
                                                                        {if $smarty.session.payroll_type eq 'all_templates' or $smarty.session.payroll_type eq 'templates'}
                                                                        checked="checked" {/if} id="templatesRadioButton">
                                                                    <span class="text">{#p_salaries_template#}</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div id="units-dev" class="hidden">
                                                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">
                                                            {#gnr_all#}</div>
                                                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                            <div class="control-group">
                                                                <div class="checkbox">
                                                                    <label>
                                                                        <input type="checkbox" name="payroll_type_all"
                                                                            value="all_units"
                                                                            {if $smarty.session.payroll_type eq 'all_units'}
                                                                            checked {/if} id="allUnitRadioButton">
                                                                        <span class="text">{#gnr_all_units#}</span>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div id="units-select">
                                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">
                                                                <label for="unit_id">{#gnr_management_unit#}</label>
                                                            </div>
                                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                                <select name="unit_id[]" class="units" id="unit_id"
                                                                    {if empty($smarty.session.s_unit_id)}
                                                                    placeholder="{#gnr_unspecified#}" {/if} multiple>
                                                                    {foreach $units as $unit}
                                                                        {if $batch}
                                                                            <option value="{$unit->sh_unt_id}"
                                                                                {(in_array($unit->sh_unt_id, json_decode($unit_id))) ? 'selected' : ''}>
                                                                                {$unit->sh_unt_name}</option>
                                                                        {else}
                                                                            <option value="{$unit->sh_unt_id}"
                                                                                {(($smarty.session.s_unit_id eq $unit->sh_unt_id) || in_array($unit->sh_unt_id, $smarty.session.s_unit_id)) ? 'selected' : ''}>
                                                                                {$unit->sh_unt_name}</option>
                                                                        {/if}
                                                                    {/foreach}
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div id="templates-dev" class="hidden">
                                                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">
                                                            {#gnr_all#}</div>
                                                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                            <div class="control-group">
                                                                <div class="checkbox">
                                                                    <label>
                                                                        <input type="checkbox" name="payroll_type_all"
                                                                            value="all_templates"
                                                                            {if $smarty.session.payroll_type eq 'all_templates'}
                                                                            checked="checked" {/if}
                                                                            id="allTemplatesRadioButton">
                                                                        <span class="text">{#gnr_all_templates#}</span>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div id="templates-select">
                                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">
                                                                <label for="templates_id">{#gnr_template#}</label>
                                                            </div>
                                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                                <select name="templates_id[]" multiple id="templates_id">
                                                                    <option value="">{#gnr_unspecified#}</option>
                                                                    {foreach $templates as $template}
                                                                        {if $batch}
                                                                            <option value="{$template->id}"
                                                                                {(in_array($template->id, json_decode($templates_ids))) ? 'selected' : ''}>
                                                                                {$template->name}</option>
                                                                        {else}
                                                                            <option value="{$template->id}"
                                                                                {(($smarty.session.template_id eq $template->id) || in_array($template->id, $smarty.session.templates_id)) ? 'selected' : ''}>
                                                                                {$template->name}</option>
                                                                        {/if}
                                                                    {/foreach}
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">
                                                        <label for="rev_user_id">{#p_auditor#}</label>
                                                    </div>
                                                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                        <select name="rev_user_id" id="rev_user_id" required>
                                                            <option value="">{#gnr_unspecified#}</option>
                                                            {foreach $employeesList as $employee}
                                                                <option value="{$employee->sh_user_id}"
                                                                    {if $batch.rev_user_id eq $employee->sh_user_id or $smarty.session.rev_user_id eq $employee->sh_user_id}
                                                                    selected {/if}>
                                                                    {$employee->sh_user_full_name}
                                                                </option>
                                                            {/foreach}
                                                        </select>
                                                    </div>

                                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">
                                                        <label for="prov_user_id">{#p_approved_by#}</label>
                                                    </div>
                                                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                        <select name="prov_user_id" id="prov_user_id" required>
                                                            <option value="">{#gnr_unspecified#}</option>
                                                            {foreach $employeesList as $employee}
                                                                <option value="{$employee->sh_user_id}"
                                                                    {if $batch.prov_user_id eq $employee->sh_user_id or $smarty.session.prov_user_id eq $employee->sh_user_id}
                                                                    selected {/if}>
                                                                    {$employee->sh_user_full_name}
                                                                </option>
                                                            {/foreach}
                                                        </select>
                                                    </div>

                                                    {if isset($batch)}
                                                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-6 snsolabel">
                                                            {#gnr_from_date#}</div>
                                                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                            {getdate type=report row=$batch->payroll_batch_from_date col="from_date" required=true}
                                                        </div>
                                                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-6 snsolabel">
                                                            {#gnr_to_date#}</div>
                                                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                            {getdate type=report row=$batch->payroll_batch_to_date col="to_date" required=true}
                                                        </div>
                                                    {else}
                                                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-6 snsolabel">
                                                            {#gnr_from_date#}</div>
                                                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                            {getdate type=report row=$smarty.session.parameters.from_date col="from_date"}
                                                        </div>
                                                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-6 snsolabel">
                                                            {#gnr_to_date#}</div>
                                                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                            {getdate type=report row=$smarty.session.parameters.to_date col="to_date"}
                                                        </div>
                                                    {/if}

                                                    <div class="col-lg-1 col-md-1 m-2 text-right">
                                                        <button type="submit" name="browse" value="browse"
                                                            class="btn btn-lg btn-default shiny">{#gnr_view#}</button>
                                                    </div>

                                                    {if $employees}
                                                        {if $allConfirmed != true}
                                                            <div
                                                                class="col-lg-2 col-md-2 col-lg-push-8 col-md-push-8 mt-2 text-left">
                                                                {url check=0 urltype="mbutton" url_string="bsc/P051/payroll/newConfirmAll/0/{$smarty.session.lang}/{$batch->id}"
                                                                text_value="{#p_adjust_reveiwing_status#}"}
                                                            </div>
                                                        {/if}

                                                        <div class="row mt-5">
                                                            <div class="col-lg-12">
                                                                <!-- 1. حساب الإجماليات -->
                                                                {assign var="totalBasic" value=0}
                                                                {assign var="totalHousingAllowance" value=0}
                                                                {assign var="totalOtherAllowances" value=0}
                                                                {assign var="totalExtraAdditions" value=0}
                                                                {assign var="totalAllAllowances" value=0}
                                                                {assign var="totalInsuranceDeduction" value=0}
                                                                {assign var="totalOtherDeductions" value=0}
                                                                {assign var="totalAbsenceDeduction" value=0}
                                                                {assign var="totalAllDeductions" value=0}
                                                                {assign var="totalNet" value=0}

                                                                {foreach from=$employees item=row}
                                                                    {if $row->confirm_status neq 872}
                                                                        <!-- تصفير المتغيرات لكل موظف -->
                                                                        {assign var="basicSalary" value=0}
                                                                        {assign var="housingAllowance" value=0}
                                                                        {assign var="otherAllowances" value=0}
                                                                        {assign var="extraAdditions" value=0}
                                                                        {assign var="allAllowances" value=0}
                                                                        {assign var="insuranceDeduction" value=0}
                                                                        {assign var="otherDeductions" value=0}
                                                                        {assign var="absenceDeduction" value=0}
                                                                        {assign var="allDeductions" value=0}
                                                                        {assign var="netSalary" value=0}

                                                                        <!-- الراتب الأساسي -->
                                                                        {assign var="basicSalary" value=$row->salary}
                                                                        {assign var="totalBasic" value=$totalBasic + $basicSalary}

                                                                        <!-- البدلات -->
                                                                        {foreach from=$row->allowancesDetails key=allowanceName item=allowanceValue}
                                                                            {if $allowanceName == "بدل السكن"}
                                                                                {assign var="housingAllowance" value=$allowanceValue}
                                                                                {assign var="totalHousingAllowance" value=$totalHousingAllowance + $housingAllowance}
                                                                            {else}
                                                                                {assign var="otherAllowances" value=$otherAllowances + $allowanceValue}
                                                                                {assign var="totalOtherAllowances" value=$totalOtherAllowances + $allowanceValue}
                                                                            {/if}
                                                                        {/foreach}

                                                                        <!-- الإضافات الأخرى -->
                                                                        {assign var="extraAdditions" value=$row->additions_list_amount}
                                                                        {assign var="totalExtraAdditions" value=$totalExtraAdditions + $extraAdditions}

                                                                        <!-- مجموع البدلات -->
                                                                        {assign var="allAllowances" value=$housingAllowance + $otherAllowances + $extraAdditions}
                                                                        {assign var="totalAllAllowances" value=$totalAllAllowances + $allAllowances}

                                                                        {* Deductions *}
                                                                        {foreach from=$row->deductionDetails key=deductionName item=deductionValue}
                                                                            {if $deductionName == "خصم التأمينات الاجتماعية"}
                                                                                {assign var="insuranceDeduction" value=$deductionValue}
                                                                                {assign var="totalInsuranceDeduction" value=$totalInsuranceDeduction + $insuranceDeduction}
                                                                            {else}
                                                                                {assign var="otherDeductions" value=$otherDeductions + $deductionValue}
                                                                                {assign var="totalOtherDeductions" value=$totalOtherDeductions + $deductionValue}
                                                                            {/if}
                                                                        {/foreach}

                                                                        {* Other Deductions from extraValueWithDetails *}
                                                                        {if isset($row->extraValueWithDetails) && is_array($row->extraValueWithDetails)}
                                                                            {foreach from=$row->extraValueWithDetails item=deduction}

                                                                                {if $deduction['is_absent'] eq 0}
                                                                                    {assign var="deductionValueFloat" value=$deduction['value']}
                                                                                    {assign var="otherDeductions" value=$otherDeductions + $deductionValueFloat}
                                                                                    {assign var="totalOtherDeductions" value=$totalOtherDeductions + $deductionValueFloat}
                                                                                {/if}
                                                                            {/foreach}
                                                                        {/if}


                                                                        {* Absence Deduction *}
                                                                        {if isset($row->extraString->employeeAbsenceDays) && $row->extraString->employeeAbsenceDays > 0}
                                                                            {assign var="dailyCost" value=($basicSalary + $allAllowances - $insuranceDeduction - $otherDeductions) / 30}
                                                                            {assign var="absenceDeduction" value=$dailyCost * $row->extraString->employeeAbsenceDays}
                                                                            {assign var="totalAbsenceDeduction" value=$totalAbsenceDeduction + $absenceDeduction}
                                                                        {/if}

                                                                        {* Total Deductions *}
                                                                        {assign var="allDeductions" value=$insuranceDeduction + $otherDeductions + $absenceDeduction}
                                                                        {assign var="totalAllDeductions" value=$totalAllDeductions + $allDeductions}

                                                                        <!-- الراتب الصافي -->
                                                                        {assign var="netSalary" value=$basicSalary + $allAllowances - $allDeductions}
                                                                        {assign var="totalNet" value=$totalNet + $netSalary}
                                                                    {/if}
                                                                {/foreach}

                                                                <!-- 2. عرض الجدول -->
                                                                <table
                                                                    class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                                                    <thead>
                                                                        <tr>
                                                                            <th width="5%"
                                                                                style="background-color: #A0D468 !important;">
                                                                            </th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                {#gnr_employee#}</th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                {#gnr_job#}</th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                {#gnr_basic#}</th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                بدل السكن </th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                بدلات أخرى</th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                {#gnr_other_deductionAddition#}</th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                المجموع</th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                خصم التأمينات</th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                الغياب</th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                خصومات أخرى</th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                المجموع</th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                {#gnr_net#}</th>
                                                                            <th width="10%"
                                                                                style="background-color: #A0D468 !important;">
                                                                                الحالة</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        {$i=1}
                                                                        {foreach $employees as $row}
                                                                            {if $row->confirm_status neq 872}
                                                                                <!-- تصفير المتغيرات لكل صف -->
                                                                                {assign var="basicSalary" value=0}
                                                                                {assign var="housingAllowance" value=0}
                                                                                {assign var="otherAllowances" value=0}
                                                                                {assign var="extraAdditions" value=0}
                                                                                {assign var="allAllowances" value=0}
                                                                                {assign var="insuranceDeduction" value=0}
                                                                                {assign var="otherDeductions" value=0}
                                                                                {assign var="absenceDeduction" value=0}
                                                                                {assign var="allDeductions" value=0}
                                                                                {assign var="netSalary" value=0}

                                                                                <tr>
                                                                                    <td align="center">{$i++}</td>
                                                                                    <td align="center">{$row->userObject->full_name}
                                                                                    </td>
                                                                                    <td align="center">{$row->jobObject->sh_job_name}
                                                                                    </td>

                                                                                    <!-- الراتب الأساسي -->
                                                                                    {assign var="basicSalary" value=$row->salary}
                                                                                    <td align="center">
                                                                                        {abs($basicSalary)|number_format:2:".":","}</td>

                                                                                    <!-- بدل السكن -->
                                                                                    {foreach from=$row->allowancesDetails key=allowanceName item=allowanceValue}
                                                                                        {if $allowanceName == "بدل السكن"}
                                                                                            {assign var="housingAllowance" value=$allowanceValue}
                                                                                        {else}
                                                                                            {assign var="otherAllowances" value=$otherAllowances + $allowanceValue}
                                                                                        {/if}
                                                                                    {/foreach}
                                                                                    <td align="center">
                                                                                        {$housingAllowance|number_format:2:".":","|default:'0.00'}
                                                                                    </td>

                                                                                    <!-- بدلات أخرى -->
                                                                                    <td align="center">
                                                                                        <!-- Button trigger modal -->
                                                                                        <button type="button" class="btn btn-light" data-toggle="modal" data-target="#allowanceModal{$row->userObject->id}">
                                                                                            {$otherAllowances|number_format:2:".":","}
                                                                                        </button>

                                                                                        <!-- Modal -->
                                                                                        <div class="modal fade" id="allowanceModal{$row->userObject->id}" tabindex="-1" role="dialog"
                                                                                            aria-labelledby="allowanceModalLabel{$row->userObject->id}" >
                                                                                            <div class="modal-dialog" role="document">
                                                                                                <div class="modal-content">
                                                                                                    <div class="modal-header">
                                                                                                        <h5 class="modal-title" id="allowanceModalLabel{$row->userObject->id}">تفاصيل البدلات الأخرى</h5>
                                                                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                                                            <span >&times;</span>
                                                                                                        </button>
                                                                                                    </div>
                                                                                                    <div class="modal-body">
                                                                                                        <div class="allowance-details">
                                                                                                            {foreach from=$row->allowancesDetails key=allowanceName item=allowanceValue}
                                                                                                                {if $allowanceName != "بدل السكن"}
                                                                                                                    <div class="border-bottom py-2" style="display: flex; justify-content: center; align-items: center;">
                                                                                                                        <div class="">
                                                                                                                            <span style="font-size: 18px; font-weight: bold;">{$allowanceName} : </span>
                                                                                                                        </div>
                                                                                                                        &nbsp;
                                                                                                                        &nbsp;
                                                                                                                        <div class="">
                                                                                                                            <span class="text-success" style="font-size: 18px; font-weight: bold;">
                                                                                                                                {$allowanceValue|number_format:2:".":","|default:'0.00'}
                                                                                                                            </span>
                                                                                                                        </div>
                                                                                                                    </div>
                                                                                                                {/if}
                                                                                                            {/foreach}
                                                                                                            {if $otherAllowances == 0}
                                                                                                                <div class="alert alert-info text-center" role="alert">
                                                                                                                    <i class="fa fa-info-circle"></i> لا توجد بدلات
                                                                                                                </div>
                                                                                                            {/if}
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div class="modal-footer">
                                                                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </td>

                                                                                    <!-- الإضافات الأخرى -->
                                                                                    {assign var="extraAdditions" value=$row->additions_list_amount}
                                                                                    <td align="center">
                                                                                        {abs($extraAdditions)|number_format:2:".":","}
                                                                                        {url check=1 urltype="mbutton" oprvtype=3 opr_code='payroll' url_string="bsc/P051/payroll/secondPayslipAdditions/0/{$smarty.session.lang}/{$row->userObject->id}/{$row->batch_id}"
                                                                                        text_value="{#gnr_settings#}"}
                                                                                        {if abs($extraAdditions) eq 0}
                                                                                            <span class="text-red"></span>
                                                                                        {/if}
                                                                                    </td>

                                                                                    <!-- مجموع البدلات -->
                                                                                    {assign var="allAllowances" value=$housingAllowance + $otherAllowances + $extraAdditions}
                                                                                    <td align="center">
                                                                                        {abs($allAllowances)|number_format:2:".":","}
                                                                                    </td>

                                                                                    <!-- خصم التأمينات -->
                                                                                    {foreach from=$row->deductionDetails key=deductionName item=deductionValue}
                                                                                        {if $deductionName == "خصم التأمينات الاجتماعية"}
                                                                                            {assign var="insuranceDeduction" value=$deductionValue}
                                                                                        {else}
                                                                                            {assign var="otherDeductions" value=$otherDeductions + $deductionValue}
                                                                                        {/if}
                                                                                    {/foreach}
                                                                                    <td align="center">
                                                                                        {$insuranceDeduction|number_format:2:".":","|default:'0.00'}
                                                                                    </td>

                                                                                    <!-- خصم الغياب -->
                                                                                    <td align="center">
                                                                                        {if isset($row->extraString->employeeAbsenceDays) && $row->extraString->employeeAbsenceDays > 0}
                                                                                            {assign var="dailyCost" value=($basicSalary + $allAllowances - $insuranceDeduction - $otherDeductions) / 30}
                                                                                            {assign var="absenceDeduction" value=$dailyCost * $row->extraString->employeeAbsenceDays}
                                                                                            {$absenceDeduction|number_format:2:".":","}
                                                                                            <br>
                                                                                            ({$row->extraString->employeeAbsenceDays} أيام)
                                                                                        {else}
                                                                                            {'0.00'|number_format:2:".":","}
                                                                                        {/if}
                                                                                    </td>


                                                                                    <!-- خصومات أخرى -->
                                                                                    <td>
                                                                                        <div class="deduction-details">
                                                                                            {assign var="otherDeductions" value=0}
                                                                                            <!-- الخصومات من deductionDetails -->
                                                                                            {foreach from=$row->deductionDetails key=deductionName item=deductionValue}
                                                                                                {if $deductionName != "خصم التأمينات الاجتماعية"}
                                                                                                    {assign var="deductionValueFloat" value=$deductionValue}
                                                                                                    {assign var="otherDeductions" value=$otherDeductions + $deductionValueFloat}
                                                                                                    <div class="deduction-item"
                                                                                                        style="text-align: right;">
                                                                                                        <div>{$deductionName}:</div>
                                                                                                        <div
                                                                                                            style="margin-top: 4px; color:#28a745;">
                                                                                                            {$deductionValueFloat|number_format:2:".":","}
                                                                                                        </div>
                                                                                                    </div>
                                                                                                {/if}
                                                                                            {/foreach}
                                                                                            <!-- الخصومات من extraValueWithDetails -->


                                                                                            {if isset($row->extraValueWithDetails) && is_array($row->extraValueWithDetails)}
                                                                                                {foreach from=$row->extraValueWithDetails item=deduction}
                                                                                                    <div class="deduction-item"
                                                                                                        style="text-align: right;">
                                                                                                        {if isset($deduction.inout) && $deduction.is_absent eq 0}
                                                                                                            {assign var="deductionValueFloat" value=$deduction.value}
                                                                                                            {if $deduction.inout eq 'out'}
                                                                                                                {assign var="otherDeductions" value=$otherDeductions + $deductionValueFloat}
                                                                                                                <div
                                                                                                                    style="margin-top: 4px; ">
                                                                                                                    {$deduction.desc}:
                                                                                                                    {$deductionValueFloat|number_format:2:".":","}
                                                                                                                </div>
                                                                                                            {elseif $deduction.inout eq 'in'}
                                                                                                                {assign var="otherDeductions" value=$otherDeductions + $deductionValueFloat}
                                                                                                                <div
                                                                                                                    style="margin-top: 4px; ">
                                                                                                                    {$deduction.desc}:
                                                                                                                    {$deductionValueFloat|number_format:2:".":","}
                                                                                                                </div>
                                                                                                            {/if}
                                                                                                        {/if}
                                                                                                    </div>
                                                                                                {/foreach}
                                                                                                {if $otherDeductions == 0}
                                                                                                    {'0.00'|number_format:2:".":","}
                                                                                                {/if}
                                                                                            {/if}


                                                                                            {url check=1 urltype="mbutton" oprvtype=3 opr_code='payroll' url_string="bsc/P051/payroll/secondPayslipDeductions/0/{$smarty.session.lang}/{$row->userObject->id}/{$row->batch_id}"
                                                                                            text_value="{#gnr_settings#}"}
                                                                                            {if abs($otherDeductions) eq 0}
                                                                                                <span class="text-red">♦</span>
                                                                                            {/if}
                                                                                    </td>



                                                                                    <!-- مجموع الخصومات -->
                                                                                    {assign var="allDeductions" value=$insuranceDeduction + $otherDeductions + $absenceDeduction}
                                                                                    <td align="center">
                                                                                        {abs($allDeductions)|number_format:2:".":","}
                                                                                    </td>

                                                                                    <!-- الراتب الصافي -->
                                                                                    {assign var="netSalary" value=$basicSalary + $allAllowances - $allDeductions}
                                                                                    <td align="center">
                                                                                        <span class="{if $netSalary lt 0}text-red{/if}">
                                                                                            {$netSalary|number_format:2:".":","}
                                                                                        </span>
                                                                                    </td>

                                                                                    <!-- الحالة -->
                                                                                    <td align="center">
                                                                                    
                                                                                        {if $row->confirm_status eq 356}<span
                                                                                            class="success">{#p_reveiwed#}</span>{/if}
                                                                                        {if $row->confirm_status eq 357}<span
                                                                                            class="danger">{#p_not_reveiwed#}</span>{/if}
                                                                                        {url check=0 urltype="mbutton" oprvtype=3 opr_code='payroll' url_string="bsc/P051/payroll/secondPayslip/0/{$smarty.session.lang}/{$row->userObject->id}/{$row->batch_id}"
                                                                                        text_value="{#p_reveiwing_status#}"}
                                                                                        {if abs($row->confirm_status) neq 356}
                                                                                            <span class="text-red">♦</span>
                                                                                        {/if}
                                                                                    </td>
                                                                                </tr>
                                                                            {/if}
                                                                        {/foreach}
                                                                    </tbody>
                                                                    <tfoot>
                                                                        <tr>
                                                                            <td align="center"></td>
                                                                            <td align="center"></td>
                                                                            <td align="center"></td>
                                                                            <td align="center">
                                                                                {abs($totalBasic)|number_format:2:".":","}</td>
                                                                            <td align="center">
                                                                                {abs($totalHousingAllowance)|number_format:2:".":","}
                                                                            </td>
                                                                            <td align="center">
                                                                                {abs($totalOtherAllowances)|number_format:2:".":","}
                                                                            </td>
                                                                            <td align="center">
                                                                                {abs($totalExtraAdditions)|number_format:2:".":","}
                                                                            </td>
                                                                            <td align="center">
                                                                                {abs($totalAllAllowances)|number_format:2:".":","}
                                                                            </td>
                                                                            <td align="center">
                                                                                {abs($totalInsuranceDeduction)|number_format:2:".":","}
                                                                            </td>
                                                                            <td align="center">
                                                                                {abs($totalAbsenceDeduction)|number_format:2:".":","}
                                                                            </td>
                                                                            <td align="center">
                                                                                {abs($totalOtherDeductions)|number_format:2:".":","}
                                                                            </td>
                                                                            <td align="center">
                                                                                {abs($totalAllDeductions)|number_format:2:".":","}
                                                                            </td>
                                                                            <td align="center">
                                                                                {abs($totalNet)|number_format:2:".":","}</td>
                                                                            <td align="center"></td>
                                                                        </tr>
                                                                    </tfoot>
                                                                </table>
                                                            </div>
                                                        </div>

                                                        <div
                                                            class="col-lg-4 col-md-4 col-lg-push-8 col-md-push-8 m-1 text-left mt-4">
                                                            {if isset($batch)}
                                                                <button type="submit" name="submit" value="update"
                                                                    class="btn btn-lg btn-info shiny">{#gnr_update#}</button>
                                                            {else}
                                                                <button type="submit" name="submit" value="save"
                                                                    class="btn btn-lg btn-success shiny float-right"
                                                                    {if $allConfirmed != true} style="pointer-events: none;"
                                                                    disabled {/if}>{#gnr_save#}</button>
                                                                {if $allConfirmed != true}
                                                                    <p class="text-left">
                                                                        <span
                                                                            style="color: red;">{#p_there_are_not_reveiwed_records#}</span>
                                                                    </p>
                                                                {/if}
                                                            {/if}
                                                        </div>
                                                    {/if}
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="payrollSave"
                                class="tab-pane {if $smarty.session.s_tab_0_top_tab eq 'payrollSave'} active {/if}">
                                <table
                                    class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                    <thead>
                                        <tr>
                                            <th width="5%" style="background-color: #A0D468 !important;"> </th>
                                            <th width="30%" style="background-color: #A0D468 !important;">{#p_payslip#}</th>
                                            <th width="15%" style="background-color: #A0D468 !important;">{#gnr_type#}</th>
                                            <th width="20%" style="background-color: #A0D468 !important;">
                                                {#gnr_from_duration#}</th>
                                            <th width="10%" style="background-color: #A0D468 !important;">{#gnr_view#}</th>
                                            <th width="20%" style="background-color: #A0D468 !important;">
                                                {#gnr_review_and_approve#}</th>
                                            <th width="20%" style="background-color: #A0D468 !important;">{#gnr_setting#}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {$i=1}
                                        {foreach $mosairsList as $row}
                                            <tr>
                                                <td align="center">{$i++}</td>
                                                <td>{$row->name}</td>
                                                <td align="center">{render view='/HumanResource/payrollBatchType' row=$row}</td>
                                                <td align="center">
                                                    <span style="font-size: smaller; color: darkslategrey">
                                                        {$row->from_date} -> {$row->to_date} => {$row->days} {#gnr_day#}
                                                    </span>
                                                </td>
                                                <td nowrap align="center">
                                                    {url check=0 urltype="alinkn" oprvtype=1 opr_code="payroll" url_string="bsc/P051/payroll/secondPayrollsheetbrowse/0/{$smarty.session.lang}/{$row->id}"
                                                    text_value="{#gnr_view#}"}
                                                </td>
                                                <td align="center">
                                                    <i class="fa fa-check-circle success" ></i>
                                                    {workflow requestId=$row->request->wf_request_id backTo="bsc/P051/payroll/show/0/{$smarty.session.lang}/menu"}
                                                </td>
                                                <td class="center">
                                                    {if $requestId=$row->request->wf_request_send_status eq 440}
                                                        {url check=1 urltype="medit" opr_code='payroll' url_string="bsc/P051/payroll/updatePayRollBatch/0/{$smarty.session.lang}/{$row->id}/editPayRollBatch"}
                                                        {url check=1 urltype="mdelete" opr_code='payroll' url_string="bsc/P051/payroll/updatePayRollBatch/0/{$smarty.session.lang}/{$row->id}/deletePayRollBatch"}
                                                    {/if}
                                                </td>
                                            </tr>
                                        {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="tab3" class="tab-pane {$smarty.session.tab3}">
                    <div class="tabbable">
                        <ul class="nav nav-tabs nav-justified" id="myTab5">
                            <li class="{if $smarty.session.s_tab_3_top_tab eq 'monthly'} active {/if}">
                                <a aria-expanded="false" data-toggle="tab"
                                    href="#monthly">{#p_monthly_salary_employees#}</a>
                            </li>
                            <li class="tab-red {if $smarty.session.s_tab_3_top_tab eq 'daily'} active {/if}">
                                <a aria-expanded="true" data-toggle="tab" href="#daily">{#p_daily_salary_employees#}</a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            <div id="monthly"
                                class="tab-pane {if $smarty.session.s_tab_3_top_tab eq 'monthly'} in active {/if}">
                                <table
                                    class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                    <thead>
                                        <tr>
                                            <th width="5%" style="background-color: #A0D468 !important;"> </th>
                                            <th width="25%" style="background-color: #A0D468 !important;">{#gnr_employee#}
                                            </th>
                                            <th width="25%" style="background-color: #A0D468 !important;">{#gnr_job#}</th>
                                            <th width="45%" style="background-color: #A0D468 !important;">
                                                {#p_salary_template#}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {$i=1}
                                        {foreach $monthly_users_list as $employee}
                                            <tr>
                                                <td align="center">{$i++}</td>
                                                <td>{$employee->sh_user_full_name}</td>
                                                <td>{$employee->sh_job_name}</td>
                                                <td>
                                                    {url check=1 urltype="mbutton" opr_code="payroll" oprvtype=3 url_string="bsc/P051/payroll/selecttemp/0/{$smarty.session.lang}/{$employee->sh_uao_id}/{$employee->sh_user_id}/865"
                                                    text_value=" <i class='fa fa-edit'></i>"}
                                                    {getname table=prl_templates id=$employee->sh_uao_payroll_template_id}
                                                </td>
                                            </tr>
                                        {/foreach}
                                    </tbody>
                                </table>
                            </div>
                            <div id="daily"
                                class="tab-pane {if $smarty.session.s_tab_3_top_tab eq 'daily'} in active {/if}">
                                <table
                                    class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                    <thead>
                                        <tr>
                                            <th width="5%" style="background-color: #A0D468 !important;"> </th>
                                            <th width="20%" style="background-color: #A0D468 !important;">{#gnr_employee#}
                                            </th>
                                            <th width="20%" style="background-color: #A0D468 !important;">{#gnr_job#}</th>
                                            <th width="45%" style="background-color: #A0D468 !important;">
                                                {#p_salary_template#}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {$i=1}
                                        {foreach $daily_users_list as $employee}
                                            <tr>
                                                <td align="center">{$i++}</td>
                                                <td>{$employee->sh_user_full_name}</td>
                                                <td>{$employee->sh_job_name}</td>
                                                <td>
                                                    {url check=1 urltype="mbutton" opr_code="payroll" oprvtype=3 url_string="bsc/P051/payroll/selecttemp/0/{$smarty.session.lang}/{$employee->sh_uao_id}/{$employee->sh_user_id}/866"
                                                    text_value=" <i class='fa fa-edit'></i>"}
                                                    {getname table=prl_templates id=$employee->sh_uao_payroll_template_id}
                                                </td>
                                            </tr>
                                        {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="tab4" class="tab-pane {$smarty.session.tab4}">
                    <div class="tabbable">
                        <ul class="nav nav-tabs nav-justified" id="myTab5">
                            <li class="{$smarty.session.rulestabone}">
                                <a aria-expanded="false" data-toggle="tab" href="#rulestabone">
                                    <span>{#p_salaries_template#}</span>
                                </a>
                            </li>
                            <li class="tab-red {$smarty.session.rulestabtow}">
                                <a aria-expanded="true" href="#rulestabtow">
                                    <span>{#p_template_create#}</span>
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            <div id="rulestabone" class="tab-pane {$smarty.session.rulestabone}">
                                <table id="snsotable-1"
                                    class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                        <tr>
                                            <th width="5%">
                                                {url check=1 urltype="madd" opr_code='payroll' url_string="bsc/P051/payroll/addtemp/0/{$smarty.session.lang}"}
                                            </th>
                                            <th width="35%">{#gnr_template#}</th>
                                            <th width="20%">{#p_template_type#}</th>
                                            <th width="20%">{#p_template_create#}</th>
                                            <th width="20%">{#gnr_settings#}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {$i=1}
                                        {foreach $templates as $row}
                                            <tr>
                                                <td align="center">{$i++}</td>
                                                <td>{$row->name}</td>
                                                <td align="center">{t v=$row->type}</td>
                                                <td align="center">
                                                    {url check=1 urltype="button" oprvtype=3 opr_code="payroll" url_string="bsc/P051/payroll/show/0/{$smarty.session.lang}/save_session/tab4/buildtemplate/{$row->id}"
                                                    text_value="{#p_template_create#}"}
                                                    {url check=1 urltype="mbutton" oprvtype=3 opr_code="payroll" url_string="bsc/P051/payroll/dayCostStructure/0/{$smarty.session.lang}/{$row->id}"
                                                    text_value="{#p_estimate_day_price#}"}
                                                </td>
                                                <td nowrap align="center">
                                                    {url check=1 urltype="medit" opr_code='payroll' url_string="bsc/P051/payroll/edittemp/0/{$smarty.session.lang}/{$row->id}"}
                                                    {if count($row->rules) lte 1 and $row->employeesCount() eq 0}
                                                        {url check=1 urltype="mdelete" opr_code='payroll' url_string="bsc/P051/payroll/confirmtemplate/0/{$smarty.session.lang}/{$row->id}"}
                                                    {/if}
                                                    {url check=0 urltype="button" opr_code='payroll' url_string="bsc/P051/payroll/templateEmployees/0/{$smarty.session.lang}/{$row->id}"
                                                    style="btn btn-default shiny" text_value="{#gnr_employees#}<span
                                                class='badge bg-palegreen mt-2-px mr-3-px text-white'>{$row->employeesCount()}<span>"}
                                                </td>
                                            </tr>
                                        {/foreach}
                                    </tbody>
                                </table>
                            </div>

                            {if isset($smarty.session.s_prl_templates_id)}
                                <div id="rulestabtow" class="tab-pane {$smarty.session.rulestabtow}">
                                    <h5 class="row-title before-blue">
                                        <i class="glyphicon glyphicon-list-alt blue"></i>
                                        {t v=$row->type} » {$row->name}
                                    </h5>
                                    <table id="snsotable-1"
                                        class="table table-hover table-striped table-bordered table-condensed">
                                        <thead>
                                            <tr>
                                                <th width="5%">
                                                    {if isset($smarty.session.s_prl_templates_id)}
                                                        {url check=1 urltype="madd" opr_code='payroll' url_string="bsc/P051/payroll/addrule/0/{$smarty.session.lang}/rulestabone/{$row->id}"}
                                                    {/if}
                                                </th>
                                                <th width="20%">{#gnr_term#}</th>
                                                <th width="20%">{#gnr_type#}</th>
                                                <th width="20%">{#p_affecting_day_price_estimation#}</th>
                                                <th width="20%">{#gnr_order#}</th>
                                                <th width="20%">{#gnr_settings#}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {$i=1}
                                            {foreach $temp_rules as $row}
                                                <tr>
                                                    <td align="center">{$i++}</td>
                                                    <td>{$row->name}</td>
                                                    <td align="center">{t v=$row->category_id}</td>
                                                    <td align="center">{t v=$row->effect_in_day_cost}</td>
                                                    <td align="center">{$row->order}</td>
                                                    <td nowrap align="center">
                                                        {if $row->category_id neq 644}
                                                            {url check=1 urltype="medit" opr_code='payroll' url_string="bsc/P051/payroll/editrule/0/{$smarty.session.lang}/{$row->id}/rulestabone"}
                                                            {url check=1 urltype="mdelete" opr_code='payroll' url_string="bsc/P051/payroll/confirm/0/{$smarty.session.lang}/{$row->id}/tab5/rulestabone"}
                                                        {/if}
                                                    </td>
                                                </tr>
                                            {/foreach}
                                        </tbody>
                                    </table>
                                </div>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CSS مخصص لتحسين الشكل -->
    <style>
        .table {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            border-collapse: collapse;
            width: 100%;
        }

        .table th,
        .table td {
            padding: 10px;
            text-align: center;
            border: 1px solid #ddd;
        }

        .table th {
            background-color: #A0D468 !important;
            color: white;
            font-weight: bold;
        }

        .table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .table tbody tr:hover {
            background-color: #e9ecef;
        }

        .table tfoot td {
            font-weight: bold;
            background-color: #f1f3f5;
            color: #2c3e50;
        }

        .success {
            color: #28a745;
        }

        .danger {
            color: #dc3545;
        }

        .text-red {
            color: #dc3545;
        }

        .table small {
            display: block;
            color: #666;
            font-size: 12px;
        }

        .allowance-details,
        .deduction-details {
            display: flex;
            flex-direction: column;
            gap: 6px;
            padding: 6px;
            font-size: 13px;
            line-height: 1.4;
            text-align: right;
        }

        .allowance-name,
        .deduction-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .allowance-value,
        .deduction-value {
            color: #28a745;
            font-weight: 500;
        }

        @media print {

            .allowance-details,
            .deduction-details {
                font-size: 11pt;
                padding: 5px;
            }

            .allowance-name,
            .deduction-name {
                font-weight: 600;
            }

            .allowance-value,
            .deduction-value {
                font-weight: 500;
            }
        }
    </style>

{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#allUnitRadioButton').click(function() {
                if ($(this).prop("checked") == true) {
                    $('#units-select').addClass("hidden");
                } else if ($(this).prop("checked") == false) {
                    $('#units-select').removeClass("hidden");
                }
            });

            $('#allTemplatesRadioButton').click(function() {
                if ($(this).prop("checked") == true) {
                    $('#templates-select').addClass("hidden");
                } else if ($(this).prop("checked") == false) {
                    $('#templates-select').removeClass("hidden");
                }
            });

            if ($("#allTemplatesRadioButton").is(":checked")) {
                $('#templates-dev').removeClass("hidden");
                $('#templates-select').addClass("hidden");
            }

            if ($("#allUnitRadioButton").is(":checked")) {
                $('#units-select').addClass("hidden");
            }

            if ($("#unitRadioButton").is(":checked")) {
                $('#units-dev').removeClass("hidden");
            }

            if ($("#templatesRadioButton").is(":checked")) {
                $('#templates-dev').removeClass("hidden");
                $('#allUnitRadioButton').prop('checked', false);
            }

            $("#unitRadioButton").click(function() {
                if ($("#unitRadioButton").is(":checked")) {
                    $('#units-dev').removeClass("hidden");
                    $('#templates-dev').addClass("hidden");
                    $('#units-select').removeClass("hidden");
                }
            });

            $("#templatesRadioButton").click(function() {
                if ($("#templatesRadioButton").is(":checked")) {
                    $('#units-dev').addClass("hidden");
                    $('#templates-select').removeClass("hidden");
                    $('#templates-dev').removeClass("hidden");
                    $('#allUnitRadioButton').prop('checked', false);
                    $("#allTemplatesRadioButton").prop('checked', false);
                }
            });
        });
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }
            };
        }();
        InitiateSimpleDataTable.init();
    </script>

{/block}