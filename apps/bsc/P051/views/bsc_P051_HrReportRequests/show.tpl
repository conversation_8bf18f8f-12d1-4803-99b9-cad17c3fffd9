{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}

{block name=page_body}
    <div class="widget-body">

        <div class="horizontal-space"></div>

        <div class="row">

            <div class="col-lg-12">
                <form method="post"
                      action="{url urltype="path" url_string="bsc/P051/HrReportRequests/show/0/{$smarty.session.lang}/save_session"}">
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="type" required>
                            <option value="">{#p_choose_request_type#}</option>
                            {foreach $requests_types as $type}
                                <option value="{$type->id}" {if $type->id eq $smarty.post['type']} selected {/if} >{$type->translatedName}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_status#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $request_status as $status}
                                {if $status->id == 440} {continue} {/if}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="status"
                                               value="{$status->id}" {if $status->id eq $smarty.post['status']} checked {/if}>
                                        <span class="text">{$status->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_employee#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <div class="radio">
                                <label>
                                    <span class="text">{#p_creator#}</span>
                                </label>
                            </div>

                            <div id="creatorsList">
                                <select name="created_by">
                                    <option value="">{#gnr_unspecified#}</option>
                                    {foreach $employees as $employee}
                                        <option value="{$employee->sh_user_id}" {if $smarty.post['created_by'] eq $employee->sh_user_id} selected {/if}>{$employee->sh_user_full_name}</option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="radio">
                                <label>
                                    <span class="text">{#p_owner#}</span>
                                </label>
                            </div>

                            <div id="usersList">
                                <select name="user_id">
                                    <option value="">{#gnr_unspecified#}</option>
                                    {foreach $employees as $employee}
                                        <option value="{$employee->sh_user_id}" {if $smarty.post['user_id'] eq $employee->sh_user_id} selected {/if}>{$employee->sh_user_full_name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div id="specificDateInterval">
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_from#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            {getdate type="edit" row=$smarty.post col="from"}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_to#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            {getdate type="edit" row=$smarty.post col="to"}
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <input type="submit" class="btn btn-default shiny" value="{#gnr_view#}">
                    </div>
                </form>
            </div>

        </div>
    </div>

    <div class="horizontal-space"></div>

    {if count($requests)}
        <div class="row">


            <div class="col-lg-12">

                <div class="mb-1">
                    {url check=0 urltype="alinkn" url_string="bsc/P051/HrReportRequests/print/0/{$smarty.session.lang}/save_session" text_value="<i class='fa fa-print black'></i>&nbsp;{#gnr_print#}&nbsp;" style="btn btn-default"}
                </div>

                <table class="table table-bordered table-hover dataTable no-footer sortable-table">
                    <thead>
                    <tr>
                        <th width="5%" class="text-center"></th>
                        <th width="25%" class="text-center">{#p_creator#}</th>
                        <th width="25%" class="text-center">{#p_owner#}</th>
                        <th width="15%" class="text-center">{#gnr_date#}</th>
                        <th width="10%" class="text-center">{#gnr_details#}</th>
                        <th width="15%" class="text-center">{#p_request_status#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $requests as $request}
                        <tr>
                            <td class="text-center">{$i++}</td>
                            <td>{$request->creator_name}</td>
                            <td>{$request->owner_name}</td>
                            <td class="text-center">{getdate type=show row=$request col=wf_request_created_date}</td>
                            <td class="text-center">
                                {url check=0 urltype="mbutton" url_string="gnr/X000/wfrequest/browse/0/{$smarty.session.lang}/{$request->wf_request_id}" text_value="{#gnr_view#}" modal="modal" style="btn btn-info shiny"}
                            </td>
                            <td class="text-center">{$request->wf_request_success}</td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>

        </div>
    {elseif count($requests) eq 0 and $smarty.session.report_parm neq null}
        <div class="alert alert-warning">{#gnr_no_records#}</div>
    {/if}

{/block}