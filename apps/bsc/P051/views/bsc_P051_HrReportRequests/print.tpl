{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}
    <div class="row">
        <h4 class="text-center well">{#p_human_resource_information#}</h4>
    </div>
    <div class="row">

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#gnr_type#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">{$type->translatedName}</div>

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#gnr_status#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">
        {t v=$smarty.session['report_parm']['status']}
        </div>

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#gnr_from#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">{if $smarty.session['report_parm']['from']} {$smarty.session['report_parm']['from']} {else} - {/if}</div>

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#gnr_to#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">{if $smarty.session['report_parm']['to']} {$smarty.session['report_parm']['to']} {else} - {/if}</div>

    </div>
    <div class="horizontal-space"></div>
    <div class="row">

        <div class="col-lg-12">

            <table class="table table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th width="5%" class="text-center"></th>
                    <th width="25%" class="text-center">{#p_creator#}</th>
                    <th width="25%" class="text-center">{#p_owner#}</th>
                    <th width="15%" class="text-center">{#gnr_date#}</th>
                    <th width="15%" class="text-center">{#p_request_status#}</th>
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $requests as $request}
                    <tr>
                        <td class="text-center">{$i++}</td>
                        <td>{$request->creator_name}</td>
                        <td>{$request->owner_name}</td>
                        <td class="text-center">{getdate type=show row=$request col=wf_request_created_date}</td>
                        <td class="text-center">{$request->wf_request_success}</td>
                    </tr>
                {/foreach}
                </tbody>
            </table>
        </div>

    </div>
{/block}
