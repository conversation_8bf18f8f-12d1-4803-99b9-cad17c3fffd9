{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}
    <div class="row">
        <div class="col-lg-12">
            <div class="widget-body">
                <div class="row">
                    <div class="col-lg-12">
                        <form action='{url urltype="path" url_string="bsc/P051/HrEmployeesLeavesCredit/show/0/{$smarty.session.lang}/search"}'
                              method="post">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_employee#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <select name="employee_id">
                                    <option>{#gnr_unspecified#}</option>
                                    {foreach $employees as $employee}
                                        <option value="{$employee->userObject->id}"
                                                {if $smarty.session['employee_id'] eq $employee->userObject->id}selected{/if}>
                                            {$employee->userObject->full_name}
                                        </option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <button type="submit" class="btn btn-default shiny">{#gnr_view#}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="horizontal-space"></div>

    {if count($Allowedleaves)}

        {url check=0 urltype="alinkn" url_string="bsc/P051/HrEmployeesLeavesCredit/employeeRecordPrint/0/{$smarty.session.lang}/{$smarty.session['employee_id']}" text_value="<i class='fa fa-print black'></i>&nbsp;{#gnr_print#}&nbsp;" style="btn btn-default shiny"}
        <div class="horizontal-space"></div>
        <div class="row">
            <div class="col-lg-8">
                <div class="widget radius-bordered">
                    <div class="widget-header bg-darkgray">
                        <span class="widget-caption">{#gnr_employee#}</span>
                        <div class="widget-buttons">
                            <a href="#" data-toggle="collapse">
                                <i class="fa fa-minus white "></i>
                            </a>
                        </div>
                    </div>
                    <div class="widget-body">
                        <div class="row snsowraper">
                            <div class="col-lg-2">
                                <img src="/framework/core/functions/image.php?image={User::userProfilePicture($userRow->id)}&width=100&height=150"
                                     class="img-fluid img-responsive rounded">
                            </div>
                            <div class="col-lg-10">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">{getname table=sh_user id=$userRow->id}</div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_email#}</div>
                                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">{$userRow->email}</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_appointment_date#}</div>
                                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">{if !empty($uaoRow->job_date_of_appointment)} {getdate table=sh_uao col=job_date_of_appointment type=show row=$uaoRow} {else}
                                                <span class="small red">{#gnr_appointment_date_unknown#}</span>
                                            {/if}</div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_mobile_number#}</div>
                                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">{$userRow->tell}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="widget">
                    <div class="widget-header bg-darkgray">
                        <span class="widget-caption">{#gnr_duty_days#}</span>
                        <div class="widget-buttons">
                            <a href="#" data-toggle="collapse">
                                <i class="fa fa-minus white "></i>
                            </a>
                        </div>
                    </div>
                    <div class="widget-body bg-white" style="color: #333;">
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                {foreach $week_days as $crow}
                                    <label>
                                        {if in_array($crow->id,$workDays)}
                                            <i class="fa fa-check-circle green"></i>
                                        {else}
                                            <i class="fa fa-circle darkorange"></i>
                                        {/if}
                                        <span class="text">{$crow->translatedName}</span>
                                    </label>
                                    <br>
                                {/foreach}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
                    <thead>
                    <tr>
                        <th width="5%"></th>
                        <th width="15%">{#p_leave_name#}</th>
                        <th width="20%">{#p_credit_type#}</th>
                        <th width="10%">{#gnr_balance#}</th>
                        <th width="15%">{#p_credit_max_limit_when_transfer#}</th>
                        <th width="15%">{#p_employee_available_credit#}</th>
                        <th width="10%">{#gnr_record#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $Allowedleaves as $item}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>
                                {url check=0 urltype="mbutton" url_string="gnr/X000/reportView/reportView/0/{$smarty.session.lang}/leaveAllowedCardHTML/&p[id]={$item->id}" text_value="<i class='fa fa-question-circle'></i>" modal="modal"}
                                &nbsp;
                                {getname table=hr_levst id=$item->leaveType->id}
                            </td>
                            <td align="right">{getname table=st_setting id=$item->leaveType->annual_intrvl_type}</td>
                            <td align="center">{$item->credit}&nbsp;{#gnr_day#}</td>
                            <td align="center">
                                {if $item->leaveType->transfer_type eq 838}
                                    <span>{#p_untransferable_leave#}</span>
                                {/if}
                                {if $item->leaveType->transfer_type eq 808}
                                    <span>{#p_unlimited#}</span>
                                {/if}
                                {if $item->leaveType->transfer_type eq 809}
                                    {$item->max_credit}&nbsp;{#gnr_day#}
                                {/if}
                            </td>
                            <td align="center">{leaveCredit LeaveAllowedObject=$item}</td>
                            <td align="center" nowrap>
                                {url urltype="button" url_string="bsc/P051/HrEmployeesLeavesCredit/leaveshistory/0/{$smarty.session.lang}/{$item->id}" text_value="<i class='fa fa-file-text'></i>&nbsp;{#p_the_record#}" modal="modal"}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
        {if $editcreditrequest_grf eq 0}
            <div class="widget">
                <div class="widget-header bg-lightred">
                    <span class="widget-caption"
                          style="text-align: center;">{#gnr_need_to_determine_workflow_first#}</span>
                </div><!--Widget Header-->
                <div class="widget-body">
                    <code>
                        <span>{#gnr_click_here_to_move_to_workflow_operation#} : </span>
                        {url check=1 urltype="button" oprvtype=1 opr_code="workflow" url_string="bsc/P052/workflow/show/0/{$smarty.session.lang}"  text_value="{#gnr_view#}"}
                        <br><br><br><br>
                        <span>{#gnr_determine_workflow_button_appear_only_for_authorized_users#}</span>
                    </code>
                </div><!--Widget Body-->
            </div>
        {/if}
    {elseif count($Allowedleaves) eq 0 and $smarty.session.report_data neq null}
        <div class="alert alert-warning">{#gnr_no_records#}</div>
    {/if}
{/block}