{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}
    <div class="row">
        <h4 class="text-center well">{#p_employement_leave_reocrd_report#}</h4>
    </div>
    <div class="horizontal-space"></div>
    <div class="row">

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#gnr_employee#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">{$user->sh_user_full_name}</div>


    </div>
    <div class="mt-1 row">
        <div class="col-lg-12">
            <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
                <thead>
                <tr>
                    <th width="5%"></th>
                    <th width="15%">{#p_leave_name#}</th>
                    <th width="20%">{#p_credit_type#}</th>
                    <th width="10%">{#gnr_balance#}</th>
                    <th width="15%">{#p_credit_max_limit_when_transfer#}</th>
                    <th width="15%">{#p_employee_available_credit#}</th>
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $Allowedleaves as $item}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td>
                            {url check=0 urltype="mbutton" url_string="gnr/X000/reportView/reportView/0/{$smarty.session.lang}/leaveAllowedCardHTML/&p[id]={$item->id}" text_value="<i class='fa fa-question-circle'></i>" modal="modal"}
                            &nbsp;
                            {getname table=hr_levst id=$item->leaveType->id}
                        </td>
                        <td align="right">{getname table=st_setting id=$item->leaveType->annual_intrvl_type}</td>
                        <td align="center">{$item->credit}&nbsp;{#gnr_day#}</td>
                        <td align="center">
                            {if $item->leaveType->transfer_type eq 838}
                                <span>{#p_untransferable_leave#}</span>
                            {/if}
                            {if $item->leaveType->transfer_type eq 808}
                                <span>{#p_unlimited#}</span>
                            {/if}
                            {if $item->leaveType->transfer_type eq 809}
                                {$item->max_credit}&nbsp;{#gnr_day#}
                            {/if}
                        </td>
                        <td align="center">{leaveCredit LeaveAllowedObject=$item rawNumber=true} {#gnr_day#}</td>
                    </tr>
                {/foreach}
                </tbody>
            </table>
        </div>
    </div>
{/block}
