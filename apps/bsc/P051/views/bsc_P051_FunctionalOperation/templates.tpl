{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{getname table=sh_user id=$UserRow->id}&nbsp;&raquo;&nbsp;{#p_types_of_available_leaves#}</h4>
    </div>
    <div class="modal-body">
        {if !empty($UserRow->gender)}
            <form  method="post" action='{url urltype="path"
                   url_string="bsc/P051/FunctionalOperation/leavesetting/0/{$smarty.session.lang}/template/{$smarty.session.s_employees_token}/{$UserRow->id}"}'>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 snsolabel">
                        <div class="checkbox">
                            {foreach $leaves as $item}
                                <label>
                                    <input type="checkbox" name="leaves_ids[]"
                                        value="{$item->id}" {if in_array($item->id,$selected_array)} checked="checked" {/if}>
                                    <span class="text">{$item->name}</span>
                                </label><br>
                            {/foreach}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-12 col-md-7 col-sm-12 col-xs-12">
                        <button type="submit" class="btn btn-warning">{#gnr_update#}</button>
                    </div>
                </div>
            </form>
        {else}
            <span class="danger small">{#p_need_to_determine_gender_first#}</span>
        {/if}
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}