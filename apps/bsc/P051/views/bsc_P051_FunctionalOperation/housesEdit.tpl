{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <h3>{#p_add_new_house#}</h3>
    </div>
    <div  class="modal-body">
        <form  method="post" action='{url urltype="path"
            url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/Houses/update/{$smarty.session.s_resume_token}/{$house->id}"}'>
            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                <div class="control-group">
                    <input type="text" required id="{$hrow->id}" name="name" value="{$house->name}">
                </div>
            </div>
            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_housing_type#}</div>
            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                <div class="control-group">
                    {foreach $housing_type_list as $hrow}
                        <div class="radio">
                            <label>
                                <input required type="radio" id="{$hrow->id}" name="type"
                                       value="{$hrow->id}" {if $hrow->id eq $house->type} checked {/if}>
                                <span class="text">{$hrow->translatedName}</span>
                            </label>
                        </div>
                    {/foreach}
                </div>


            </div>

            <div id="367div">
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_ownership_type#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                    {foreach $owning_type_list as $key => $hrow}
                        <div class="radio">
                            <label>
                                <input type="radio" id="{$hrow->id}"
                                       name="owning_level"
                                       value="{$hrow->id}"
                                        {if $hrow->id eq $house->owning_level}
                                            checked {/if} >
                                <span class="text">{$hrow->translatedName}</span>
                            </label>
                        </div>
                    {/foreach}
                </div>
            </div>

            <div id="368div">
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_rent_value#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                    <input type="text" id="rent_amount"
                           size="10" name="rent_amount"
                           value="{$house->rent_amount}" placeholder="{#p_rent_value#}"> &nbsp; {#gnr_per_year#}</div>
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_contract_from#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=db_houses col=contract_from type=edit row=$house}</div>
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_contract_to#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=db_houses col=contract_to type=edit row=$house}</div>

            </div>

            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_accommodation_size#}</div>
            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                {foreach $housing_size_list as $hrow}
                    <div class="radio">
                        <label>
                            <input type="radio" required id="{$hrow->id}" name="space"
                                   value="{$hrow->id}" {if $hrow->id eq $house->space} checked {/if}>
                            <span class="text">{$hrow->translatedName}</span>
                        </label>
                    </div>
                {/foreach}
            </div>

            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_floor_number#}</div>
            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                <div class="control-group">
                    <input type="number" min="1" id="{$hrow->id}" name="floor_number" value="{$house->floor_number}" required>
                </div>
            </div>

            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_national_id_number#}</div>
            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                <div class="control-group">
                    <input type="text" id="{$hrow->id}" name="national_id_number" value="{$house->national_id_number}" required>
                </div>
            </div>

            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_build_type#}</div>
            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                {foreach $housing_kind_list as $hrow}
                    <div class="radio">
                        <label>
                            <input type="radio" required id="{$hrow->id}"
                                   name="building_type" value="{$hrow->id}"
                                    {if $hrow->id eq $house->building_type} checked {/if}>
                            <span class="text">{$hrow->translatedName}</span>
                        </label>
                    </div>
                {/foreach}
            </div>


            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_current_house#}</div>
            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                <div class="checkbox">
                    <label>
                        <input type="checkbox" id="#status" name="status"
                               value="1" {if !! $house->status} checked {/if}>
                        <span class="text"></span>
                    </label>
                </div>
            </div>

            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
            </div>
        </form>
    </div>
{/block}
{block name="header"}
    <script>
        $(document).ready(function () {

            $("#367div").css("display", "none");
            $("#368div").css("display", "none");


            if ($("#367").is(":checked")) {
                $("#367div").show("fast");
                $("#368div").hide("fast");
            }
            if ($("#368").is(":checked")) {
                $("#368div").show("fast");
                $("#367div").hide("fast");
            }

            $("#367").click(function () {

                if ($("#367").is(":checked")) {
                    $("#367div").show("fast");
                    $("#368div").hide("fast");
                }
            });

            $("#368").click(function () {

                if ($("#368").is(":checked")) {
                    $("#368div").show("fast");
                    $("#367div").hide("fast");
                }
            });

        });
    </script>
{/block}