{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_edit_row#}</h4>
    </div>
    <div class="modal-body">
    <div class="row">
        <form method="post"
              action='{url urltype="path"
                url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/WorkerData/update/{$smarty.session.s_resume_token}/{$row->id}"}'>
            <div class="col-lg-12">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_worker_type#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="text" class="form-control" id="type" name="type" placeholder="{#p_worker_type#}" required value="{$row->type}">
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_worker_nationality#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="nationality" required="required">
                        <option value="{$row->nationality}" selected>{getname table=st_country id=$row->nationality}</option>
                        {foreach key=id item=ltrow from=$country_list}
                            <option value="{$ltrow->id}">{$ltrow->translatedName}</option>
                        {/foreach}
                    </select>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_worker_stillworking#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <div class="control-group">
                        <div class="radio">
                            <label>
                                <input type="radio" id="yes" name="stillworking" value="yes"
                                        {if $row->stillworking eq 'yes' } checked {/if}>
                                <span class="text">{#gnr_yes#}</span>
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input type="radio" id="no" name="stillworking" value="no"
                                        {if $row->stillworking eq 'no' } checked {/if}>
                                <span class="text">{#gnr_no#}</span>
                            </label>
                        </div>
                    </div>
                </div>



                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-warning sharp">{#gnr_udpate#}</button>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}