{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add_row#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url urltype="path"
                url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/TrainingData/insert/{$smarty.session.s_resume_token}"}'>
                <div class="col-lg-12">
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_course_name#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <input type="text" name="name" placeholder="{#p_course_name#}">
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_course_to#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <input type="text" name="inst" placeholder="{#p_course_to#}">
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_course_location#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <input type="text" name="place" placeholder="{#p_course_location#}">
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_duration_in_days#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <input type="number" class="hide-spinner" name="days" placeholder="{#gnr_duration_in_days#}">
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_duration_in_hours#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <input type="number" class="hide-spinner" name="hours" placeholder="{#gnr_duration_in_hours#}">
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_certification_date#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        {getdate table=db_train col=issuedate type=add row=$row}
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}