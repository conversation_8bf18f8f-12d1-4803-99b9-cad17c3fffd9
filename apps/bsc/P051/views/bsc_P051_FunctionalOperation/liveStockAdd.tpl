{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add_row#}</h4>
    </div>
    <div class="modal-body">
    <div class="row">
        <form method="post"
              action='{url urltype="path"
                url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/LiveStockData/insert/{$smarty.session.s_resume_token}"}'>
            <div class="col-lg-12">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_livestock_type#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="type" required="required">
                        <option value="0" selected>{#gnr_select_from_menu#}</option>
                        {foreach key=id item=etrow from=$livestock_type_list}
                            <option value="{$etrow->id}">{$etrow->translatedName}</option>
                        {/foreach}
                    </select>
                </div>


                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_livestock_amount#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" min="1" class="form-control" id="amount" name="amount" placeholder="{#p_livestock_amount#}" required>
                </div>


                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}