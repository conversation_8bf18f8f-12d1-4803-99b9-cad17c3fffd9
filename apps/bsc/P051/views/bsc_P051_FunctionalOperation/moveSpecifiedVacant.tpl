{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />{/block}
{block name=page_body}

	<div class="row">
		<!-- Project select menu -->
		<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <h4 class="title">{#p_move#}{$vacant->userObject->full_name} {#p_to_another_job#}</h4>
		</div>
	</div>
	<div class="row">
		<!-- Project select menu -->
		<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

			<form method="post" action='{url urltype="path" url_string="bsc/P051/FunctionalOperation/moveSpecifiedVacant/0/{$smarty.session.lang}/{$vacant->id}"}'>
                
				<select {if empty($smarty.session.s_recruitment_unit_id)} placeholder="{#p_choose_unit#}" {else} placeholder="{$currentUnit->sh_unt_name}" {/if}
						name="unitId" class="w-full" required onchange="this.form.submit()">
					<option></option>
                    {$i=1}
                    {foreach $units as $unit}
						<option value="{$unit->id}" >
                            {$i++} - {$unit->name}
						</option>
                    {/foreach}

				</select>

			</form>

		</div>
	</div>

	<div class="horizontal-space"></div>

	<table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
		<thead>
		<tr>
			<th width="5%">&nbsp</th>
			<th width="20%">{#gnr_job#}</th>
			<th width="15%">{#gnr_code#}</th>
			<th width="15%">{#gnr_availability#}</th>
			<th width="20%">{#gnr_sex#}</th>
			<th width="20%">{#gnr_user#}</th>
			<th width="25%">{#gnr_settings#}</th>
		</tr>
		</thead>
		<tbody>
        {$i=1}
        {foreach $vacancies as $vacancy}
            {if $vacancy->job_occupied eq 112}
			<tr>
				<td class="text-center">{$i++}</td>
				<td class="text-center">{$vacancy->jobObject->sh_job_name}</td>
				<td class="text-center">{$vacancy->job_code}</td>
				<td class="text-center">
                    {if $vacancy->job_occupied eq 112}
						<span class="green">{t id=$vacancy->job_occupied}</span>
                    {/if}

                    {if $vacancy->job_occupied eq 113}
						<span class="red">{t var=$vacancy->job_occupied}</span>
                    {/if}
				</td>
				<td class="text-center">{getname table=st_setting id=$vacancy->job_gender}</td>
				<td class="text-center">
                    {if $vacancy->job_occupied eq 112}
						<span class="green">{t id=$vacancy->job_occupied}</span>
                    {/if}

                    {if $vacancy->job_occupied eq 113}
                        {$vacancy->userObject->full_name}
                    {/if}
				</td>
				<td class="text-center" nowrap>
					{url check=0 urltype="mbutton" url_string="bsc/P051/FunctionalOperation/ConfirmRecruimentForSpecifiedVacant/0/{$smarty.session.lang}/{$vacant->userObject->id}/{$vacancy->id}/{$oldVacant}/tab1/unclassified" text_value="{#p_recruite#}"}
					{if $vacant->userObject->activation_status eq 1}
						{url check=0 urltype="mbutton" url_string="bsc/P051/recruitment/sendRecruitmentRequest/0/{$smarty.session.lang}/{$vacant->userObject->id}/{$vacancy->id}/tab1/employee" text_value="ارسال طلب تعيين"}
					{else}
						<span style="color: gray; font-size: small">{#gnr_user_not_active#}</span>
					{/if}
					{url check=0 urltype="mbutton" url_string="bsc/P051/recruitment/browseRecruitRequestSent/0/{$smarty.session.lang}/{$vacant->userObject->id}/{$vacancy->id}" text_value="?"}

				</td>
			</tr>
            {/if}
        {/foreach}
		</tbody>
	</table>
{/block}
{block name=page_header}
	<script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
	<script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
	<script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
	<script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
	<script>
		var InitiateSimpleDataTable = function() {
			return {
				init: function() {
					//Datatable Initiating
					var oTable = $('.sortable-table').dataTable({
						"sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
						"iDisplayLength": 50,
						"oTableTools": {
							"aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
							],
							"sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
						},
						"language": {
							"search": "",
							"sLengthMenu": "_MENU_",
							"oPaginate": {
								"sPrevious": "{#gnr_previous#}",
								"sNext": "{#gnr_next#}"
							}
						}
					});
				}

			};

		}();

		InitiateSimpleDataTable.init();
	</script>
{/block}