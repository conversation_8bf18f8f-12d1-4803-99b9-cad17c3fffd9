{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_recruite#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_user#}</div>
            <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">{$user->full_name}</div>

            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_category#}</div>
            <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                {$c_ids = ","|explode:$user->classification}
                {foreach $c_ids as $ids}
                    {getname table=sh_userclasses id=$ids}<br>
                {/foreach}
            </div>

            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
            <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                {url check=0 urltype="button" url_string="bsc/P051/FunctionalOperation/RecruitSpecifiedVacant/0/{$smarty.session.lang}/recruit/{$smarty.session.s_recruitment_token}/{$user->id}/{$vacant->id}/{$backTab}" text_value="{#gnr_confirm#}"}
                <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_cancel#}</button>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}