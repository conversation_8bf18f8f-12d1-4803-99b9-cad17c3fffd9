{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}

{if !empty($ErrorMessage) and $ErrorMessage eq 'SizeNotAllowed'}
	<div class="alert alert-danger fade in">{#SizeNotAllowed#}</div>
{/if}
{if !empty($ErrorMessage) and $ErrorMessage eq 'TypeNotAllowed'}
	<div class="alert alert-danger fade in">{#TypeNotAllowed#}</div>
{/if}

<div class="row snsowraper">
	<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
		<thead>
		<tr>
			<th width="5%">
				{url check=0 urltype="madd"
					url_string="bsc/P051/FunctionalOperation/createDocument/0/{$smarty.session.lang}"}
			</th>
			<th width="20%">{#gnr_title#}</th>
			<th width="30%">{#gnr_name#}</th>
			<th width="15%">{#gnr_download_date#}</th>
			<th width="15%">{#gnr_download#}</th>
			<th width="15%">{#gnr_settings#}</th>
		</tr>
	</thead>
	<tbody>
	{$i=1}
    {foreach $documents as $document}
        {$permission = json_decode($document->permission,true)}

        {if ($document->created_by eq $smarty.session.user->id)}
		<tr>
			<td align="center">{$i++}</td>
			<td>{$document->name}</td>
			<td style="direction: ltr; text-align: center;">{$document->code}</td>
			<td align="center">
                {getdate table=sh_document col=created_date type=showauto row=$document->created_date}
            </td>

			<td align="center">
			  	{url check=0 urltype="button" oprvtype=3
					url_string="bsc/P051/FunctionalOperation/documentDownload/0/{$smarty.session.lang}/{$document->id}"
					text_value="{#gnr_download#}"}
                {url check=0 urltype="alinkn" oprvtype=3
					url_string="bsc/P051/FunctionalOperation/preview/0/{$smarty.session.lang}/{$document->id}"
					text_value="{#gnr_view#}"}
			</td>

			<td align="center" nowrap>
				{url check=0 urltype="medit"
					url_string="bsc/P051/FunctionalOperation/editDocument/0/{$smarty.session.lang}/{$document->id}"}
				{url check=0 urltype="mdelete"
					url_string="bsc/P051/FunctionalOperation/confirm/0/{$smarty.session.lang}/{$document->id}"}
			</td>
		</tr>
		{/if}
        {if !(is_null($document->permission)) and not ($document->created_by eq $smarty.session.user->id)}
			<tr>

				<td align="center">{$i++}</td>
				<td>{$document->name}</td>
				<td style="direction: ltr; text-align: center;">{$document->code}</td>
				<td align="center">{getdate table=sh_document col=created_date type=showauto row=$document->created_date}</td>
				<td align="center">
        			{if isset($permission['download'])}
            			{url check=0 urltype="button" oprvtype=3
							url_string="bsc/P051/FunctionalOperation/download/0/{$smarty.session.lang}/{$document->id}"
							text_value="{#gnr_download#}"}
            		{/if}
        			{if isset($permission['view'])}
            			{url check=0 urltype="alinkn" oprvtype=3
							url_string="bsc/P051/FunctionalOperation/preview/0/{$smarty.session.lang}/{$document->id}"
							text_value="{#gnr_view#}"}
					{/if}
				</td>

				<td align="center" nowrap>
                    {url check=0 urltype="mbutton"
						url_string="bsc/P051/FunctionalOperation/information/0/{$smarty.session.lang}"
						text_value="<i class='fa fa-question-circle'></i>"}
				</td>
			</tr>
        {/if}
    {/foreach}
	</tbody>
	</table>
	</div>
{/block}
{if $smarty.session.s_document_back_path}
    {block name=back}{url urltype="path" url_string="{$smarty.session.s_document_back_path}"}{/block}
{/if}
