{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script>
        $(document).ready(function(){

            $("#loanTypes").css("display","none");
            $("#InstallmentTypes").css("display","none");

            $("#1206").click(function(){

                if ($("#1206").is(":checked"))
                {
                    $("#loanTypes").show("fast");
                    $("#InstallmentTypes").hide("fast");
                }
            });

            $("#1207").click(function(){

                if ($("#1207").is(":checked"))
                {
                    $("#loanTypes").hide("fast");
                    $("#InstallmentTypes").show("fast");
                }
            });

        });
    </script>

{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add_row#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-lg-12">
                <form  method="post" action='{url urltype="path"
                    url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/DebtData/insert/{$smarty.session.s_resume_token}"}'>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $LoanAdvanceTypes as $key => $type}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="type" id="{$type->id}" value="{$type->id}">
                                        <span class="text">{$type->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div id="loanTypes">
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_loan_type#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                {foreach $LoanTypes as $loan}
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="loan_type" id="{$loan->id}" value="{$loan->id}">
                                            <span class="text">{$loan->translatedName}</span>
                                        </label>
                                    </div>
                                {/foreach}
                            </div>
                        </div>
                    </div>

                    <div id="InstallmentTypes">
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_installment_type#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                {foreach $InstallmentsTypes as $installment}
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="installment_type" id="{$installment->id}"
                                                   value="{$installment->id}">
                                            <span class="text">{$installment->translatedName}</span>
                                        </label>
                                    </div>
                                {/foreach}
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_loan_value#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <input type="number" min="1" class="form-control" required
                               name="loan_value" placeholder="{#p_loan_value#}">
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_installment_value#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <input type="number" min="1" class="form-control" required
                               name="installment_value" placeholder="{#p_installment_value#}">
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_comment#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <textarea class="form-control" name="comment" placeholder="{#gnr_comment#}"></textarea>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"> {#gnr_from#} </div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        {getdate table=db_debt col=from_date type=add row=$row required=true}
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_to#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        {getdate table=db_debt col=to_date type=add row=$row required=true}
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}