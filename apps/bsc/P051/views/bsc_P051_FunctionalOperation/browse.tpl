{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=title}{$operation->translatedName}{/block}
{block name=body}
<div class="row snsowraper">
	<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
		<thead>
		<tr>
			<th width="5%"></th>
			<th width="20%">{#p_attachment_title#}</th>
			<th width="30%">{#p_attachment_name#}</th>
			<th width="15%">{#gnr_download_date#}</th>
			<th width="15%">{#gnr_download#}</th>
            <th width="15%">{#gnr_view#}</th>
		</tr>
	</thead>
	<tbody>
	{$i=1}
    {foreach $documents as $document}
		<tr>
			<td align="center">{$i++}</td>
			<td>{$document->name}</td>
			<td style="direction: ltr; text-align: center;">{$document->code}</td>
			<td align="center">{getdate table=sh_document col=created_date type=showauto row=$document->created_date}</td>
			<td align="center">{url check=0 urltype="button" oprvtype=3 url_string="gnr/X000/documents/download/0/{$smarty.session.lang}/{$document->id}" text_value="{#gnr_download#}"}</td>
            <td align="center">
                {url check=0 urltype="alinkn" oprvtype=3 url_string="gnr/X000/documents/preview/0/{$smarty.session.lang}/{$document->id}" text_value="{#gnr_view#}"}
            </td>
		</tr>
    {/foreach}
	</tbody>
	</table>
	</div>
{/block}
{block name=back}{url urltype="path" url_string="{$smarty.session.s_document_back_path}"}{/block}
