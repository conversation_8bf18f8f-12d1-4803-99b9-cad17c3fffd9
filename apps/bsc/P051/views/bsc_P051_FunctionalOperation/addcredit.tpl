{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add_row#} </h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form method="post" action='{url urltype="path"
                url_string="bsc/P051/FunctionalOperation/leavesetting/0/{$smarty.session.lang}/leave/insert/{$smarty.session.s_employees_token}"}'>
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">
                    {#p_leave_type#}
                </div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $levstList as $levstrow}
                                {if in_array($levstrow->leaveType->annual_intrvl_type,[831,837])}
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="allowedleave_id" value="{$levstrow->id}" required>
                                            <span class="text">{$levstrow->leaveType->name}&nbsp;&raquo;&nbsp;{$levstrow->employeeLeaveCredit}&nbsp;{#gnr_day#}</span>
                                        </label>
                                    </div>
                                {/if}
                            {/foreach}
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_request_type#}</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    <div class="control-group">
                        {foreach $creditoperations as $copr}
                            <div class="radio">
                                <label>
                                    <input type="radio" name="credit_opr_type" value="{$copr->id}" required>
                                    <span class="text">{$copr->translatedName}</span>
                                </label>
                            </div>
                        {/foreach}
                    </div>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_num_of_required_days#}</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    <input type="text" pattern="[0-9]+([.][0-9]+)?" id="credit"
                       name="credit" placeholder="{#p_duration_by_days#}" required>&nbsp;{#gnr_day#}
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_from_date#}</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    {showdate date={$smarty.now|date_format:"%Y/%m/%d"}}
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_request_reasons#}</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    <textarea class="form-control" id="reasons"
                        name="reasons" placeholder="{#gnr_request_reasons#}">
                    </textarea>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                </div>

            </form>

        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}