{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">
            {#p_convert_job_to_primary#}
        </h4>
    </div>
    <div class="modal-body">
        <div class="row snsowraper">
            <div class="alert alert-warning fade in">
                {getname table=sh_unt id=$vacant->jobObject->sh_job_unit_id}
                &nbsp;|&nbsp;
                {$vacant->jobObject->sh_job_name}
                &nbsp;|&nbsp;
                {if $vacant->basic eq 1}{#gnr_primary#}{/if}
                {if $vacant->basic eq 0}{#gnr_secondary#}{/if}
                <br>
                {#p_converting_job_to_brimary_make_all_others_secondary#}
            </div>

            {url check=0 urltype="button"
                url_string="bsc/P051/FunctionalOperation/vacancies/0/{$smarty.session.lang}/settobasic/{$smarty.session.s_empdata_token}/{$vacant->id}" text_value="{#gnr_confirm#}"}
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}

