{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">
            {#gnr_dismissal#}
        </h4>
    </div>
    <div class="modal-body">
        <form method="post" action="{url urltype="path"
            url_string="bsc/P051/FunctionalOperation/vacancies/0/{$smarty.session.lang}/dismiss/{$smarty.session.s_empdata_token}/{$vacant->id}"}">

            <div class="row snsowraper">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_user#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {$vacant->userObject->full_name}
                </div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_job#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {$vacant->jobObject->sh_job_name}
                </div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_dismissal_date#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {getdate table=hr_dismiss col=date type=add row=[]}
                </div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">
                    {#p_dismissal_reasons#}
                </div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <textarea class="form-control" name="reasons"
                        placeholder="{#p_dismissal_reason#}"></textarea>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_notice#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <span style="color: red">{#p_dismiss_employee_alert#}</span>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-danger sharp">
                        {#p_dismissal_implementation#}
                    </button>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}

