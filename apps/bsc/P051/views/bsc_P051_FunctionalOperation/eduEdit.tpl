{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}{/block}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#gnr_edit_row#}</h4>
	</div>
	<div class="modal-body">
		<div class="row">
			<form  method="post" action='{url urltype="path"
				url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/EducationalData/update/{$smarty.session.s_resume_token}/{$row->id}"}'>
				<div class="col-lg-12">

					<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_qualification#}</div>
					<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
						<select name="type" required="required">
							<option value="{$row->type}" selected>{getname table=st_setting id=$row->type}</option>
                            {foreach key=id item=etrow from=$edu_type_list}
								<option value="{$etrow->id}">{$etrow->translatedName}</option>
                            {/foreach}
						</select>
					</div>

					<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_specialization#}</div>
					<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" id="name" class="form-control" name="name" value="{$row->name}" placeholder="{#gnr_specialization#}" required="required"></div>

					<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_educational_institution#}</div>
					<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" id="unv" class="form-control" name="unv" value="{$row->unv}" placeholder="{#gnr_educational_institution#}" required="required"></div>

					<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_graduation_certificate_date#}</div>
					<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=db_edu col=date type=edit row=$row}</div>

					<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
					<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
						<button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
					</div>

				</div>
			</form>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}