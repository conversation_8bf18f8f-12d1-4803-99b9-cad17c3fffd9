{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}
    <div class="widget collapsed">
        <div class="widget-header bg-sky">
            <i class="widget-icon fa fa-tags" aria-hidden="true"></i>
            <span class="widget-caption">
				{getname table=sh_unt id=$vacant->job_unt_id}
                &nbsp;&raquo;&nbsp;
                {$vacant->jobObject->sh_job_name}
			</span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                    <i class="fa fa-plus"></i>
                </a>
            </div><!--Widget Buttons-->
        </div><!--Widget Header-->
        <div style="display: none;" class="widget-body">
            <div class="row">
                <div class="col-md-12">
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_unit#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=sh_unt id=$vacant->job_unt_id}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_job#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$vacant->jobObject->sh_job_name}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_code#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$vacant->job_code}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_sex#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$vacant->job_gender}</div>

                </div>
            </div>
        </div><!--Widget Body-->
    </div>

    {if $requestSentStatus eq 'Success'}

        <div class="alert alert-success fade in">
            {#p_request_sent_successfully#}
        </div>

    {/if}

    {if $vacant->user_id eq 0}

        <div class="row">
            <div class="col-md-12">
                <h5 class="col-lg-12 row-title before-maroon">
                    <form method="post"
                          action='{url urltype="path" url_string="bsc/P051/recruitment/recruit/0/{$smarty.session.lang}/search"}'>
                        {#gnr_user_name#}
                        <div class="horizontal-space"></div>
                        <input type="text" class="form-control" id="user_show_name" name="user_show_name"
                               value="{$smarty.session.s_search_word}" placeholder="{#gnr_user_name#}" required>
                        <div class="horizontal-space"></div>
                        <button type="submit" class="btn btn-success">{#gnr_search#}</button>
                    </form>
                </h5>
            </div>
            <div class="col-md-12">
                {if $searchResult}
                    <div class="row snsowraper">
                        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                            <thead>
                            <tr style="background-color: #A0D468 !important;">
                                <th width="5%" style="background-color: #A0D468 !important;">&nbsp;</th>
                                <th width="70%"
                                    style="background-color: #A0D468 !important;">{#gnr_user_name#}</th>
                                <th width="25%"
                                    style="background-color: #A0D468 !important;">{#gnr_add#}</th>
                            </tr>
                            </thead>
                            <tbody>
                            {$i=1}
                            {foreach $searchResult as $userFromSearch}
                                <tr>
                                    <td align="center">{$i++}</td>
                                    <td>
                                        <a data-toggle="modal" data-target="#modal"
                                           href="{url check=0 urltype="path" url_string="gnr/X000/resume/show/0/{$smarty.session.lang}/{$userFromSearch->id}"}"
                                           class="btn btn-default btn-sm">
                                            <i class='fa fa-user'></i>
                                        </a>
                                        {$userFromSearch->full_name}
                                    </td>
                                    <td style="text-align: center;">
                                        {url check=0 urltype="mbutton" url_string="bsc/P051/recruitment/confirm/0/{$smarty.session.lang}/{$userFromSearch->id}/{$vacant->id}/tab1/unclassified" text_value="{#p_recruite#}"}
                                        {if $userFromSearch->activation_status eq 1}
                                            {url check=0 urltype="mbutton" url_string="bsc/P051/recruitment/sendRecruitmentRequest/0/{$smarty.session.lang}/{$userFromSearch->id}/{$vacant->id}/tab1/employee" text_value="{#p_send_belonging_request#}"}
                                        {else}
                                            <span style="color: gray; font-size: small">{#gnr_user_not_active#}</span>
                                        {/if}
                                        {url check=0 urltype="mbutton" url_string="bsc/P051/recruitment/browseRecruitRequestSent/0/{$smarty.session.lang}/{$userFromSearch->id}/{$vacant->id}" text_value="?"}
                                    </td>
                                </tr>
                            {/foreach}
                            </tbody>
                        </table>
                    </div>
                {elseif $num eq 2}
                    <div class="alert alert-warning">
                        {#gnr_user_not_found#}
                    </div>
                {/if}
            </div>
        </div>

    {else}
        <div class="alert alert-success white fade in">
            <i class="fa-fw fa fa-times"></i>
            <strong>{#p_recruite_success#}</strong>
        </div>
    {/if}
{/block}

{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}
{block name=back}{url urltype="path" url_string="bsc/P051/recruitment/show/0/{$smarty.session.lang}"}{/block}
