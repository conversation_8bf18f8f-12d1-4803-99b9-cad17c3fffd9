{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script type="text/javascript">
        $(document).ready(function () {

            if($("#amount_type_650").is(":checked")){
                $("#650").removeClass('hidden');
            }
            if($("#amount_type_651").is(":checked")){
                $("#651").removeClass('hidden');
            }
            $( "#name" ).focus();

            $("#condition").css("display", "none");

            $("#649").click(function () {
                if ($("#649").is(":checked")) {
                    $("#condition").show();
                }
            });

            $("#648").click(function () {
                if ($("#648").is(":checked")) {
                    $("#condition").hide();
                }
            });

            // إخفاء select الخصومات/الإضافات الجاهزة في البداية
            $("#predefined_terms_select").hide();
            
            // التحكم في عرض input أو select للاسم
            $("#use_predefined_terms").change(function() {
                if ($(this).is(":checked")) {
                    // $("#manual_name_input").hide();
                    $("#manual_name_input").css("display", "none");
                    $("#predefined_terms_select").show();
                    // تفريغ قيمة الحقل اليدوي
                    $("#name").val("");
                } else {
                    $("#manual_name_input").css("display", "block");
                    $("#predefined_terms_select").hide();
                    // تفريغ قيمة select
                    $("#predefined_terms").val("");
                }
            });

            // نسخ القيمة المختارة من select إلى input
            $("#predefined_terms").change(function() {
                var selectedText = $(this).find("option:selected").text();
                var selectedValue = $(this).val();
                
                console.log('Selected value:', selectedValue);
                console.log('Selected text:', selectedText);
                
                if (selectedValue !== "") {
                    // نسخ النص المختار إلى input name ليتم إرساله مع النموذج
                    $("#name").val(selectedText);
                    console.log('Name input updated to:', $("#name").val());
                } else {
                    $("#name").val("");
                }
            });

            // التحقق الشامل قبل الإرسال وضمان إرسال name بشكل صحيح
            $('form').on('submit', function(e) {
                var errors = [];
                var finalName = "";
                
                console.log('Form submission started...');
                
                // التحقق من اسم البند وتحديد القيمة النهائية
                var isUsingPredefined = $('#use_predefined_terms').is(':checked');
                console.log('Using predefined terms:', isUsingPredefined);
                
                if (isUsingPredefined) {
                    var predefinedValue = $('#predefined_terms').val();
                    console.log('Predefined value:', predefinedValue);
                    
                    if (predefinedValue === '' || predefinedValue === null) {
                        errors.push('يرجى اختيار بند من القائمة الجاهزة');
                    } else {
                        // استخدام النص المختار من select كاسم البند
                        finalName = $('#predefined_terms option:selected').text();
                        console.log('Final name from select:', finalName);
                        
                        // التأكد من تحديث input name
                        $("#name").val(finalName);
                        console.log('Name input final value:', $("#name").val());
                    }
                } 
                
                // التحقق من نوع البند
                if (!$('input[name="category_id"]:checked').length) {
                    errors.push('يرجى اختيار نوع البند');
                }
                
                // التحقق من نوع القيمة
                if (!$('input[name="amount_type"]:checked').length) {
                    errors.push('يرجى اختيار نوع القيمة');
                }
                
                // التحقق من تأثير تقدير اليوم
                if (!$('input[name="effect_in_day_cost"]:checked').length) {
                    errors.push('يرجى اختيار تأثير تقدير اليوم');
                }
                
                // عرض الأخطاء إن وجدت
                if (errors.length > 0) {
                    alert(errors.join('\n'));
                    e.preventDefault();
                    return false;
                }
                
                // التأكد من أن name يحتوي على القيمة الصحيحة قبل الإرسال
                console.log('اسم البند الذي سيُرسل:', finalName);
                console.log('Name input before submit:', $("#name").val());
                console.log('Name input is visible:', $("#name").is(':visible'));
                console.log('Form data:', $(this).serialize());
                
                // إضافة تأخير صغير للتأكد من تحديث القيم
                if (finalName && $("#name").val() !== finalName) {
                    $("#name").val(finalName);
                    console.log('Force updated name input to:', $("#name").val());
                }
                
                return true;
            });
        });


        $(document).ready(function() {

//            $("#650").css("display", "none");
//            $("#651").css("display", "none");

            $("input[name$='amount_type']").click(function() {
                var sid = $(this).val();
                $("div.amount").hide();
                $("#" + sid).removeClass("hidden");
                $("#" + sid).show();
            });
        });
    </script>
{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_term#} -> {$rule->template->name}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form method="post"
                action='{url urltype="path"
                url_string="bsc/P051/FunctionalOperation/vacancies/0/{$smarty.session.lang}/editPayrollTemplate/{$rule->id}/{$smarty.session.s_rand_trans_num}"}'>

                <!-- اختيار استخدام الخصومات/الإضافات الجاهزة -->
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">استخدام خصومات/إضافات جاهزة</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" id="use_predefined_terms" name="use_predefined_terms" value="1">
                            <span class="text">اختيار من الخصومات والإضافات الجاهزة</span>
                        </label>
                    </div>
                </div>

                <!-- select للخصومات/الإضافات الجاهزة -->
                <div id="predefined_terms_select" class="col-lg-12">
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">اختيار البند الجاهز</div>
                    <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                        <select id="predefined_terms" name="predefined_terms">
                            <option value="">-- اختر البند --</option>
                            
                            <!-- الإضافات الجاهزة -->
                            <optgroup label="الإضافات">
                                <option value="بدل السكن">بدل السكن</option>
                            </optgroup>
                            
                            <!-- الخصومات الجاهزة -->
                            <optgroup label="الخصومات">
                                <option value="خصم التأمينات الاجتماعية">خصم التأمينات الاجتماعية</option>
                            </optgroup>
                        </select>
                    </div>
                </div>

                <!-- input للإدخال اليدوي -->
                <div id="manual_name_input">
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_term_name#}</div>
                    <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                        <input type="text" class="form-control" name="name" id="name" value="{$rule->template->name}">
                    </div>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    <div class="control-group">
                        <div class="radio">
                        {foreach $rule_types_list as $srow}
                            {if $srow->id neq 644}
                                <label>
                                    <input name="category_id" value="{$srow->id}"
                                           type="radio"
                                           {if $rule->template->category_id eq $srow->id}
                                            checked
                                           {/if}>
                                    <span class="text">{$srow->translatedName}</span>
                                </label>
                            {/if}
                        {/foreach}
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_order#}</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" step="any" class="form-control" name="order" value="{$rule->template->order}"></div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_value#}</div>

                    <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                        {foreach $rule_amount_list as $srow}
                            <div class="radio">
                                <label>
                                    <input name="amount_type" value="{$srow->id}"
                                           id="amount_type_{$srow->id}"
                                           type="radio"
                                            {if $rule->template->amount_type eq $srow->id}
                                                checked="checked"
                                            {/if}>
                                    <span class="text">{$srow->translatedName}</span>
                                </label>
                            </div>
                        {/foreach}
                    </div>

                    <div id="650" class="amount hidden">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_value#}</div>
                        <div class="col-lg-5 col-md-5 col-sm-12 col-xs-12 snsoinput">
                            <input type="number" step="any" class="form-control" name="amount_fixed_value" value="{$rule->template->amount_fixed_value}"></div>
                    </div>

                    <div id="651" class="amount hidden">

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_based_on#}</div>
                        <div class="col-lg-5 col-md-5 col-sm-12 col-xs-12 snsoinput">
                            {foreach $rules_list as $srow}
                                <div class="checkbox">
                                    <label>
                                        <input name="amount_percentage_based_on[]"
                                               value="{$srow->id}"
                                                {if  in_array($srow->id,json_decode($rule->template->amount_percentage_based_on))}
                                                    aria-selected="true"
                                                    checked="checked"
                                                {/if}
                                               type="checkbox">
                                        <span class="text">{$srow->name}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_percentage#}</div>
                        <div class="col-lg-5 col-md-5 col-sm-12 col-xs-12 snsoinput"><input type="number" step="any" class="form-control" name="amount_percentage_value" value="{$rule->template->amount_percentage_value}">%</div>

                    </div>


                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_affecting_day_price_estimation#}</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    <div class="control-group">
                        {foreach $yesNoList as $ynrow}
                            <div class="radio">
                                <label>
                                    <input name="effect_in_day_cost" value="{$ynrow->id}"
                                           type="radio"
                                            {if $rule->template->effect_in_day_cost eq $ynrow->id}
                                                checked="checked"
                                            {/if}>
                                    <span class="text">{$ynrow->translatedName}</span>
                                </label>
                            </div>
                        {/foreach}
                    </div>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-success sharp">{#gnr_update#}</button>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
