{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}

{block name=page_header}
    <script>
        $("#BasicSalary").css("display", "none");
        $("#MonthTypeDays").css("display", "none");
        $("#DaysTypeDays").css("display", "none");
        $("#hourTypeCost").css("display", "none");

        $("#doam_div").css("display", "none");
        $("#fingerprint_div").css("display", "none");

        $("#227").click(function () {
            if ($("#227").is(":checked")) {
                $("#BasicSalary").show('fast');
                $("#MonthTypeDays").show('fast');
                $("#DaysTypeDays").hide('fast');
                $("#hourTypeCost").hide('fast');
            }
        });

        $("#861").click(function () {
            if ($("#861").is(":checked")) {
                $("#BasicSalary").hide('fast');
                $("#MonthTypeDays").hide('fast');
                $("#DaysTypeDays").show('fast');
                $("#hourTypeCost").hide('fast');
            }
        });

        $("#228").click(function () {
            if ($("#228").is(":checked")) {
                $("#BasicSalary").hide('fast');
                $("#MonthTypeDays").hide('fast');
                $("#DaysTypeDays").hide('fast');
                $("#hourTypeCost").show('fast');
            }
        });

        $("#826").click(function () {
            if ($("#826").is(":checked")) {
                $("#BasicSalary").hide('fast');
                $("#MonthTypeDays").hide('fast');
                $("#DaysTypeDays").hide('fast');
                $("#hourTypeCost").hide('fast');
            }
        });

        $("#type1").click(function () {
            if ($("#type1").is(":checked")) {
                $("#doam_div").hide('fast');
                $("#fingerprint_div").hide('fast');
            }
        });

        $("#type2").click(function () {
            if ($("#type2").is(":checked")) {
                $("#doam_div").show('fast');
                $("#fingerprint_div").show('fast');
            }
        });

        $("#type3").click(function () {
            if ($("#type3").is(":checked")) {
                $("#doam_div").hide('fast');
                $("#fingerprint_div").show('fast');
            }
        });
        $('#BasicTemplatSelect').on('change', function() {
            var $select = $('#changing_template_alert_message');
            $select.show();
           $('#browseButton').show();
           $('#saveButton').hide();
        });
    </script>
    {$jscode1}
    {$jscode}
{/block}
{block name=page_body}
    <div class="widget">
        <div class="widget-body bordered-left bordered-palegreen">
            <div class="row">
                <div class="col-lg-12">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="title">{#p_primary_data#}: </h4>
                        <img src="/framework/core/functions/image.php?image={User::userProfilePicture((int) $user_employee->sh_user_id)}"
                                class="avatar-thumbnail">
                    </div>

                    <div class="mb-1">

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {$user_employee->full_name}
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_sex#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {t v=$user_employee->gender}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_neighborhood#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {getname table=st_neighborhood id=$user_employee->address_neighborhood}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_job_name#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {$user_employee->basicJob[0]->name}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_job_unit_name#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {$user_employee->basicJob[0]->unit->name}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_direct_boss#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {$user_employee->basicVacant->directBoss->full_name}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="widget collapsed">
        <div class="widget-header bg-info">
            <i class="widget-icon fa fa-tags" aria-hidden="true"></i>
            <span class="widget-caption">
				{translate('dismissal_process_for')}  {$basicVacant->sh_user_full_name}
			</span>
        </div>
        <div class="tabbable tabs-left">
            <ul class="nav nav-tabs" id="myTab3">
                <li class=" {if $tab eq 'projects'} active {/if}">
                        <a aria-expanded="false" data-toggle="tab" style="pointer-events: none; cursor: default;">
                        <span class="badge badge-sky badge-square">1</span>
                            المشاريع
                    </a>
                </li>
                <li class=" {if $tab eq 'FingerPrintAndDoamTab'} active {/if}" >
                    <a aria-expanded="false" data-toggle="tab" style="pointer-events: none; cursor: default;">
                        <span class="badge badge-sky badge-square">2</span>
                        سجلات البصمة
                    </a>
                </li>
                <li class=" {if $tab eq 'Requests'} active {/if} ">
                    <a aria-expanded="false" data-toggle="tab" style="pointer-events: none; cursor: default;">
                        <span class="badge badge-sky badge-square">3</span>
                        الطلبات
                    </a>
                </li>
                <li class=" {if $tab eq 'CovenantManagementTab'} active {/if} ">
                    <a aria-expanded="false" data-toggle="tab" style="pointer-events: none; cursor: default;">
                        <span class="badge badge-sky badge-square">4</span>
                        إدارة العهد
                    </a>
                </li>
                <li class=" {if $tab eq 'EndOfServiceCalculationTab'} active {/if} ">
                    <a aria-expanded="false" data-toggle="tab" style="pointer-events: none; cursor: default;">
                        <span class="badge badge-sky badge-square">5</span>
                        حساب نهاية الخدمة
                    </a>
                </li>
            </ul>
            <div class="tab-content">
                <div id="projects"
                         class="tab-pane  {if $tab eq 'projects'} active {/if}">
                        <div class="col-lg-12">
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                {url urltype="button" check=1 oprvtype=3 opr_code="FunctionalOperation"
                                url_string="bsc/P051/FunctionalOperation/DismissalProcedures/0/{$smarty.session.lang}/{$basicVacant->sh_user_id}/FingerPrintAndDoamTab"
                                text_value="{#gnr_next#}"}
                            </div>
                        </div>
                    </div>
                <div id="FingerPrint"
                     class="tab-pane  {if $tab eq 'FingerPrintAndDoamTab'} active {/if}">
                    <div class="col-lg-12">
                        <div>
                            <div class="tab-content">
                                <h6> سجلات البصمة </h6>
                                <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                    <thead>
                                    <tr>
                                        <th style="background-color: #A0D468 !important;" width="20%">{#gnr_device_name#}</th>
                                        <th style="background-color: #A0D468 !important;" width="20%">{#gnr_device_type#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="text-center">
                                            <td>
                                                {$device->name}
                                            </td>
                                            <td>
                                                {{t v=$device->type}}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <br>
                            <div class="tab-content">
                                <h6> سجلات دوام العمل </h6>
                                <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                    <thead>
                                    <tr>
                                        <th style="background-color: #A0D468 !important;" width="25%">{#gnr_work#}</th>
                                        <th style="background-color: #A0D468 !important;" width="20%">{#gnr_working_days#}</th>
                                        <th style="background-color: #A0D468 !important;" width="20%">{#gnr_leave_days#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr class="text-center">
                                        <td>
                                            {$doam->name}
                                        </td>
                                        <td>
                                            {foreach $weekDays as $day}
                                                {if in_array($day->id,explode(',',$doam->workdays))}
                                                    {$day->translatedName}
                                                {/if}
                                            {/foreach}
                                        </td>
                                        <td>
                                            {foreach $weekDays as $day}
                                                {if in_array($day->id,explode(',',$doam->weekend))}
                                                    {$day->translatedName}
                                                {/if}
                                            {/foreach}
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <br>
                            <span class="text-red">تنبيه: سيتم حفظ سجلات البصمة وسجلات دوام العمل للموظف</span>
                            <br>
                            <br>
                            {url urltype="button" check=1 oprvtype=3 opr_code="FunctionalOperation"
                            url_string="bsc/P051/FunctionalOperation/DismissalProcedures/0/{$smarty.session.lang}/{$basicVacant->sh_user_id}/Requests"
                            text_value="{#gnr_next#}"}
                        </div>
                    </div>
                </div>
                <div id="DoamWardiah"
                     class="tab-pane  {if $tab eq 'Requests'} active {/if}">
                    <div class="col-lg-12">
                        <div>
                            <div>
                                <div class="tab-content">
                                    <h6> الطلبات المسندة للموظف </h6>
                                    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                        <thead>
                                            <tr>
                                                <th style="background-color: #A0D468 !important;" width="20%">منشئ الطلب</th>
                                                <th style="background-color: #A0D468 !important;" width="20%">المستخدم</th>
                                                <th style="background-color: #A0D468 !important;" width="20%">التاريخ</th>
                                                <th style="background-color: #A0D468 !important;" width="20%">التحكم</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="text-center">Osman Khojali</td>
                                                <td class="text-center">خالد منصور</td>
                                                <td class="text-center">2025-05-31 09:12:51</td>
                                                <td class="text-center" width="20%">
                                                    <button disabled class="btn btn-warning sharp">{#gnr_update#}</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <br>
                                    <span class="text-red">تنبيه: سيتم إعادة جميع الطلبات لحالة عدم الإرسال</span>
                                </div>
                                <br>
                                <div class="tab-content">
                                    <h6> الموظفون الموظف رئيسا مباشرا لهم </h6>
                                    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                        <thead>
                                        <tr>
                                            <th style="background-color: #A0D468 !important;" width="60%">الموظفون</th>
                                            <th style="background-color: #A0D468 !important;" width="20%">المدير المباشر</th>
                                            <th style="background-color: #A0D468 !important;" width="20%">التحكم</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr class="text-center">
                                            <td colspan="3" style="padding: 0;">
                                                <form method="post"
                                                      action='{url urltype="path" url_string="bsc/P051/FunctionalOperation/DismissalProcedures/0/{$smarty.session.lang}/{$basicVacant->sh_user_id}/Requests/updateDirectBoss"}'>
                                                    <table style="width: 100%; margin: 0;">
                                                        <tr>
                                                            <td width="60%">
                                                                {if $EmployeesByDirectBoss}
                                                                    {foreach $EmployeesByDirectBoss as $user name="userloop"}
                                                                        {$user->sh_user_full_name}{if !$smarty.foreach.userloop.last} - {/if}
                                                                    {/foreach}
                                                                {else}
                                                                    لا يوجد موظفون
                                                                {/if}
                                                            </td>
                                                            <td width="20%">
                                                                <select name="new_boss" class="form-control" {if !$EmployeesByDirectBoss} disabled {/if}>
                                                                    {foreach $directManagersList  as $userVacant}
                                                                        <option value="{$userVacant->sh_user_id}" {if $userVacant->sh_user_id eq $DirectBossId} selected {/if}>
                                                                            {$userVacant->sh_user_full_name}
                                                                        </option>
                                                                    {/foreach}
                                                                </select>
                                                            </td>
                                                            <td width="20%">
                                                                <button type="submit" {if !$EmployeesByDirectBoss} disabled {/if} class="btn btn-warning sharp">{#gnr_update#}</button>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </form>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>

                                    <br>
                                    <span class="text-red">تنبيه: يجب اختيار رئيس مباشر آخر لاستكمال عملية الاقالة</span>
                                </div>
                                <br>
                            </div>
                            <br>
                            {url urltype="button" check=1 oprvtype=3 opr_code="FunctionalOperation"
                            url_string="bsc/P051/FunctionalOperation/DismissalProcedures/0/{$smarty.session.lang}/{$basicVacant->sh_user_id}/CovenantManagementTab"
                            text_value="{#gnr_next#}"}
                        </div>
                    </div>
                </div>
                <div id="CovenantManagement"
                     class="tab-pane  {if $tab eq 'CovenantManagementTab'} active {/if}">
                    <div class="col-lg-12">
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            {url urltype="button" check=1 oprvtype=3 opr_code="FunctionalOperation"
                            url_string="bsc/P051/FunctionalOperation/DismissalProcedures/0/{$smarty.session.lang}/{$basicVacant->sh_user_id}/EndOfServiceCalculationTab"
                            text_value="{#gnr_next#}"}
                        </div>
                    </div>
                </div>
                <div id="EndOfServiceCalculation"
                     class="tab-pane  {if $tab eq 'EndOfServiceCalculationTab'} active {/if}">
                    <div class="col-lg-12">
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            {url urltype="button" check=1 oprvtype=3 opr_code="FunctionalOperation"
                            url_string="bsc/P051/FunctionalOperation/DismissalProcedures/0/{$smarty.session.lang}/{$basicVacant->sh_user_id}/EndOfServiceCalculationTab"
                            text_value="{#gnr_next#}"}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}
{block name=back}
    {url urltype="path"
        url_string="bsc/P051/FunctionalOperation/vacancies/0/{$smarty.session.lang}/save_session/{$basicVacant->sh_user_id}/{$basicVacant->sh_uao_id}"}
{/block}
