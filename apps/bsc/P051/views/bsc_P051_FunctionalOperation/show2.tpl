{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>{/block}
{block name=page_body}
    <div class="widget">
        <div class="widget-header bg-blue">
            <i class="widget-icon fa fa-arrow-left"></i>
            <span class="widget-caption">{#gnr_search#}</span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                    <i class="fa fa-minus"></i>
                </a>
            </div><!--Widget Buttons-->
        </div><!--Widget Header-->
        <div class="widget-body">
            <form action="{url urltype=path url_string="bsc/P051/FunctionalOperation/show2/0/{$smarty.session.lang}/search"}"
                    method="post">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_unit_or_depart#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <select name="unit_id" required>
                                <option>{#p_choose_unit#}</option>
                                {foreach $units as $unit}
                                    <option {if $smarty.session.unit_id eq $unit->sh_unt_id} selected {/if}
                                            value="{$unit->sh_unt_id}">{$unit->sh_unt_name}</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_job#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <select name="vacancyId">
                                <option value="">{#gnr_select_job#}</option>
                                {foreach $vacancies as $vacancy}
                                    <option value="{$vacancy->sh_job_id}"
                                        {if $vacancy->sh_job_id eq $smarty.session.search_employees['vacancyId']}
                                            selected
                                        {/if}>
                                        {$vacancy->sh_job_name}
                                    </option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_employee#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <input type="text"
                                class="form-control"
                                name="name"
                                value="{$smarty.session.search_employees['name']}">
                        </div>
                        

                    </div>
                    <div class="col-lg-3">
                        <button type="submit" class="btn btn-default shiny">{#gnr_search#}</button>
                    </div>
                </div>
            </form>

            <br>
        </div><!--Widget Body-->
    </div>

    {if $smarty.session.search_employees}
        <div class="well text-center">
            {url urltype=alink
                url_string="bsc/P051/FunctionalOperation/show2/0/{$smarty.session.lang}/cancel_search"
                text_value="{#gnr_cancel#}"}
        </div>
    {/if}

    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
        <thead>
        <tr style="background-color: #A0D468 !important;">
            <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
            <th style="background-color: #A0D468 !important;" width="15%"> {#gnr_management_unit#} </th>
            <th style="background-color: #A0D468 !important;" width="15%"> {#gnr_job#} </th>
            <th style="background-color: #A0D468 !important;" width="25%"> {#gnr_employee_name#} </th>
            <th style="background-color: #A0D468 !important;" width="30%"> {#gnr_functional_operations#} </th>
        </tr>
        </thead>
        <tbody>
        {if $employees->count() eq 0}
            <tr><td colspan="5" class="text-center">{#gnr_no_data#}</td></tr>
        {/if}
        {$i=1}
        {foreach $employees as $employee}
            <tr>
                <td align="center">{$i++}</td>
                <td>{$employee->oneVacant->unit->sh_unt_name}</td>
                <td>{$employee->oneVacant->job->sh_job_name}</td>
                <td>
                    {$employee->sh_user_full_name}
                </td>
                <td align="center">
                     {url urltype="button" check=0 oprvtype=3 opr_code="FunctionalOperation"
                        url_string="bsc/P051/FunctionalOperation/vacancies/0/{$smarty.session.lang}/save_session/{$employee->sh_user_id}/{$employee->basicVacant->id}"
                        style="btn btn-default"
                        text_value="<span class='badge badge-default badge-square'></span>&nbsp;{#gnr_jobs#}"}
                    {if in_array($smarty.session.organization->extra, [863, 864])}
                        {url urltype="button" check=1 oprvtype=3
                            opr_code="FunctionalOperation"
                            style="btn btn-default"
                            url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/save_session/{$employee->sh_user_id}/bsc/P051/FunctionalOperation/showResume/0/{$smarty.session.lang}"
                            text_value="<span class='badge badge-default badge-square'></span>&nbsp;{#gnr_data#}"}
                    {/if}

                    {url urltype="button" check=0 oprvtype=3 opr_code="FunctionalOperation"
                        url_string="bsc/P051/FunctionalOperation/leavesetting/0/{$smarty.session.lang}/save_session/{$employee->sh_user_id}/{$employee->basicVacant->id}"
                        style="btn btn-default"
                        text_value="<span class='badge badge-default badge-square'></span>&nbsp;{#gnr_leaves#}"}

                    {url urltype="button" check=0 oprvtype=3 opr_code="FunctionalOperation"
                        url_string="bsc/P051/FunctionalOperation/documents/0/{$smarty.session.lang}/save_session/{$employee->sh_user_id}"
                        style="btn btn-default"
                        text_value="<span class='badge badge-default badge-square'></span>&nbsp;{#gnr_documents#}"}
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
    {*{if not $smarty.session.s_employee_name}*}
        {*<div class="text-center margin-top-20">*}
            {*{paginate startpage=$smarty.session.s_startpage lastpage=$smarty.session.s_lastpage}*}
        {*</div>*}
    {*{/if}*}
{/block}
{block name=page_header}
{if $employees->count() neq 0 }
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
//        function exportTo(ID, type) {
//            $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
//        }
        {/literal}
    </script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 10,
                        "oTableTools": {
                            "aButtons": [

                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                    $("tfoot input").keyup(function() {
                        /* Filter on the column (the index) of this element */
                        oTable.fnFilter(this.value, $("tfoot input").index(this));
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/if}
{/block}