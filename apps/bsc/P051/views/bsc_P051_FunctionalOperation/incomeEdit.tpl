{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script>
        $(document).ready(function(){
            $("#365").click(function(){

                if ($("#365").is(":checked"))
                {
                    $("#365div").show("fast");
                    $("#366div").hide("fast");
                }
            });

            $("#366").click(function(){

                if ($("#366").is(":checked"))
                {
                    $("#366div").show("fast");
                    $("#365div").hide("fast");
                }
            });
            {if $row->monthly_income_type eq 365}
            $("#365div").show("fast");
            $("#366div").hide("fast");
            {/if}
            {if $row->monthly_income_type eq 366}
            $("#366div").show("fast");
            $("#365div").hide("fast");
            {/if}
        });
    </script>
{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_edit_row#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url urltype="path"
                url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/IncomeData/update/{$smarty.session.s_resume_token}/{$row->id}"}'>
                <div class="col-lg-12">
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_work_type#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="work_type">
                            <option value="{$row->work_type}" selected>{getname table=st_setting id=$row->work_type}</option>
                            {foreach $work_list as $wrow}
                                <option value="{$wrow->id}">{$wrow->translatedName}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_monthly_income#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <input class="form-control" type="text" name="monthly_incole_value"
                               value="{$row->monthly_incole_value}" min="0" required>
                        <div class="control-group">
                            {foreach $inc_montyly_list as $mrow}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="monthly_income_type"
                                               id="{$mrow->id}" value="{$mrow->id}" {if $mrow->id eq $row->monthly_income_type} checked {/if}>
                                        <span class="text">{$mrow->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                        <span id="366div">
							{#p_from#} {getdate table=db_inc col=monthly_incole_vfrom type=edit row=$row}
                            <br>
                            {#p_to#} {getdate table=db_inc col=monthly_incole_vto type=edit row=$row}
						</span>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}