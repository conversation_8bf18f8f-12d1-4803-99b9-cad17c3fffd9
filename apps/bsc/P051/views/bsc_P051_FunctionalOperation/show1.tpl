{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>{/block}
{block name=page_body}

    <div class="row">
        <div class="col-lg-12">
            <form action="{url urltype=path url_string="bsc/P051/FunctionalOperation/show/0/{$smarty.session.lang}/units"}"
                  method="post">
                <select name="unit_id" onchange="this.form.submit()" required {if empty($smarty.session.s_unit_id)}
                    placeholder="{#p_choose_unit#}" {else} placeholder="{$currentUnit->sh_unt_name}" {/if}>
                    <option>{#p_choose_unit#}</option>
                    {foreach $units as $unit}
                        <option {if $unit_id eq $unit->sh_unt_id} selected {/if} value="{$unit->sh_unt_id}">
                            {$unit->sh_unt_name}
                        </option>
                    {/foreach}
                </select>
            </form>
        </div>
    </div>
    {if $unit_id neq 0}
        <div class="row">
            <div class="col-lg-12">
                <form action="{url
                    urltype=path url_string="bsc/P051/FunctionalOperation/getEmployee/0/{$smarty.session.lang}/{$unit_id}/units/"}" method="post">
                    <select name="user_id" onchange="this.form.submit()"
                            required >
                        <option>{#p_choose_employee#}</option>
                        {foreach $employees as $employee}
                            <option value="{$employee->sh_user_id}"
                            {if $user_employee->sh_user_id eq $employee->sh_user_id} selected {/if} >{$employee->full_name}</option>
                        {/foreach}
                    </select>
                </form>
            </div>
        </div>
        {if $user_employee->sh_user_id neq 0 }
            <div class="widget-body col">
                    {url urltype="button" check=0 oprvtype=3 opr_code="FunctionalOperation"
                        url_string="bsc/P051/FunctionalOperation/vacancies/0/{$smarty.session.lang}/save_session/{$user_employee->sh_user_id}/{$sh_uao_id}"
                        style="btn btn-default"
                        text_value="<span class='badge badge-default badge-square'></span>&nbsp;{#gnr_jobs#}"}

                    {if in_array($smarty.session.organization->extra, [863, 864])}
                        {url urltype="button" check=1 oprvtype=3
                            opr_code="FunctionalOperation"
                            style="btn btn-default"
                            url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/save_session/{$user_employee->sh_user_id}/bsc/P051/FunctionalOperation/showResume/0/{$smarty.session.lang}"
                            text_value="<span class='badge badge-default badge-square'></span>&nbsp;{#gnr_data#}"}
                    {/if}

                    {url urltype="button" check=0 oprvtype=3 opr_code="FunctionalOperation"
                        url_string="bsc/P051/FunctionalOperation/leavesetting/0/{$smarty.session.lang}/save_session/{$user_employee->sh_user_id}/{$sh_uao_id}"
                        style="btn btn-default"
                        text_value="<span class='badge badge-default badge-square'></span>&nbsp;{#gnr_leaves#}"}

                    {url urltype="button" check=0 oprvtype=3 opr_code="FunctionalOperation"
                        url_string="bsc/P051/FunctionalOperation/documents/0/{$smarty.session.lang}/save_session/{$user_employee->sh_user_id}"
                        style="btn btn-default"
                        text_value="<span class='badge badge-default badge-square'></span>&nbsp;{#gnr_documents#}"}

                    {url check=0 urltype="button"
                        url_string="bsc/P051/FunctionalOperation/printEmployee/0/{$smarty.session.lang}/{$user_employee->sh_user_id}"
                        text_value="<span class='badge badge-default badge-square'></span>&nbsp;{#p_employee_record_data#}"
                        style="btn btn-default"}
            </div>

            <div class="widget">
                <div class="widget-body bordered-left bordered-palegreen">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="title">{#p_primary_data#}: </h4>
                                <img src="/framework/core/functions/image.php?image={User::userProfilePicture((int) $user_employee->sh_user_id)}"
                                     class="avatar-thumbnail">
                            </div>

                            <div class="mb-1">

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                    {$user_employee->full_name}
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_sex#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                    {t v=$user_employee->gender}
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_neighborhood#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                    {getname table=st_neighborhood id=$user_employee->address_neighborhood}
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_job_name#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                    {$user_employee->basicJob[0]->name}
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_job_unit_name#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                    {$user_employee->basicJob[0]->unit->name}
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_direct_boss#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                    {$user_employee->basicVacant->directBoss->full_name}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {/if}
    {/if}

{/block}

