{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=title}
    {$user->full_name}
{/block}
{block name=body}
    <table cellspacing="0" id="tech-companies-1" class="table table-small-font table-bordered table-striped">
        <thead>
            <tr>
                <th>
                    {url check=0  urltype="madd"
                    url_string="bsc/P051/FunctionalOperation/addDocument/0/{$smarty.session.lang}/userdata"
                    text_value="{#gnr_attach_file#}"
                    style="btn btn-success sharp"}
                </th>
                <th width="20%">{#gnr_title#}</th>
                <th width="30%">{#gnr_name#}</th>
                <th width="15%">{#gnr_download_date#}</th>
                <th width="15%">{#gnr_download#}</th>
                <th width="15%">{#gnr_settings#}</th>
            </tr>
        </thead>

        <tbody>

            {$order = 1}
            {foreach $documents as $document}


                {if ($document->created_by eq $smarty.session.user->id)}
                    {$permission = json_decode($document->permission)}
                    <tr>
                        <td width="5%" align="center">{$order++}</td>
                        <td>{$document->name}</td>
                        <td>{getname table=sh_user id=$document->created_by}</td>
                        <td>{getdate table=sh_document col=created_date type=showauto row=$document->created_date}</td>
                        <td class="text-center">
                            {url check=0 urltype="button"
                                    url_string="gnr/X000/documents/download/0/{$smarty.session.lang}/{$document->id}"
                            text_value={#gnr_download#}}
                            {url check=0 urltype="alinkn"
                                    oprvtype=3 url_string="gnr/X000/documents/preview/0/{$smarty.session.lang}/{$document->id}"
                            text_value="{#gnr_view#}"}
                        </td>
                        <td class="text-center">
                            {url check=0 urltype="medit"
                                    url_string="bsc/P051/FunctionalOperation/documentdit/0/{$smarty.session.lang}/{$document->id}/userdata"}
                            {if $document->created_by eq $smarty.session.user->id}
                                {url check=0 urltype="mdelete"
                                        url_string="bsc/P051/FunctionalOperation/documentconfirm/0/{$smarty.session.lang}/{$document->id}"}
                            {/if}
                        </td>
                    </tr>
                {/if}
                {if !(is_null($document->permission)) and not ($document->created_by eq $smarty.session.user->id)}
                    {$permission = json_decode($document->permission , true)}
                    <tr>
                        <td width="5%" align="center">{$order++}</td>
                        <td>{$document->name}</td>
                        <td>{getname table=sh_user id=$document->created_by}</td>
                        <td>{getdate table=sh_document col=created_date type=showauto row=$document->created_date}</td>
                        <td class="text-center">
                            {if isset($permission['download'])}
                                {url check=0 urltype="button"
                                                url_string="gnr/X000/documents/download/0/{$smarty.session.lang}/{$document->id}"
                                text_value={#gnr_download#}}
                            {/if}
                            {if isset($permission['view'])}
                                {url check=0 urltype="alinkn"
                                    oprvtype=3 url_string="gnr/X000/documents/preview/0/{$smarty.session.lang}/{$document->id}"
                                text_value="{#gnr_view#}"}
                            {/if}
                        </td>
                        <td class="text-center">
                            {url check=0 urltype="mbutton"
                                        url_string="gnr/X000/documents/information/0/{$smarty.session.lang}"
                            text_value="<i class='fa fa-question-circle'></i>"}
                        </td>
                    </tr>
                {/if}
            {/foreach}
        </tbody>
    </table>
{/block}
{block name=back}
    {url urltype="path"
        url_string="bsc/P051/FunctionalOperation/show/0/{$smarty.session.lang}/{$user->sh_user_id}/{$smarty.session.unit_id}"}
{/block}