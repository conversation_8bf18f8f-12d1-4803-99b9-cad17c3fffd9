{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />{/block}
{block name=page_body}
    <div class="row">
        <div class="col-lg-8">
            <div class="widget radius-bordered">
                <div class="widget-header bg-darkgray">
                    <span class="widget-caption">{#gnr_employee#}</span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-minus white "></i>
                        </a>
                    </div>
                </div>
                <div class="widget-body">
                    <div class="row snsowraper">
                        <div class="col-lg-2">
                            <img
                                src="/framework/core/functions/image.php?image={User::userProfilePicture($userRow->id)}&width=100&height=150" class="img-fluid img-responsive rounded">
                        </div>
                        <div class="col-lg-10">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                        {getname table=sh_user id=$userRow->id}
                                    </div>

                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_email#}</div>
                                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">{$userRow->email}</div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_appointment_date#}</div>
                                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                        {if !empty($uaoRow->job_date_of_appointment)}
                                            {getdate table=sh_uao col=job_date_of_appointment type=show row=$uaoRow}
                                        {else}
                                            <span class="small red">{#gnr_appointment_date_unknown#}</span>
                                        {/if}
                                    </div>

                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_mobile_number#}</div>
                                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">{$userRow->tell}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="widget">
                <div class="widget-header bg-darkgray">
                    <span class="widget-caption">{#gnr_duty_days#}</span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-minus white "></i>
                        </a>
                    </div>
                </div>
                <div class="widget-body bg-white" style="color: #333;">
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            {foreach $week_days as $crow}
                                <label>
                                    {if in_array($crow->id,$workDays)}
                                        <i class="fa fa-check-circle green"></i>
                                    {else}
                                        <i class="fa fa-circle darkorange"></i>
                                    {/if}
                                    <span class="text">{$crow->translatedName}</span>
                                </label>
                                <br>
                            {/foreach}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="fin-tag tag-inline pull-left" style="z-index: 1; background-color: transparent;">
                <div class="fin-tag-content">
                    <a href="{url urltype="path"
                        url_string="bsc/P051/FunctionalOperation/templates/0/{$smarty.session.lang}/{$smarty.session.s_leave_user_id}"}"
                        data-toggle="modal" data-target="#modal" class="btn btn-warning btn-xs">
                        <span class="small black">{#p_adjust_leave_templates#}</span>
                    </a>
                </div>
            </div>
            <div class="widget radius-bordered">
                <div class="widget-header bg-darkgray">
                    <span class="widget-caption">{#p_employees_available_leaves#}</span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-minus white "></i>
                        </a>
                    </div>
                </div>
                <div class="widget-body">
                    <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
                        <thead>
                        <tr>
                            <th width="5%"></th>
                            <th width="15%">{#p_leave_name#}</th>
                            <th width="20%">{#p_credit_type#}</th>
                            <th width="10%">{#gnr_balance#}</th>
                            <th width="15%">{#p_credit_max_limit_when_transfer#}</th>
                            <th width="15%">{#p_employee_available_credit#}</th>
                            <th width="10%">{#gnr_record#}</th>
                            <th width="10%">{#gnr_update#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {$i=1}
                        {foreach $Allowedleaves as $item}
                            <tr>
                                <td align="center">{$i++}</td>
                                <td>
                                    {url check=0 urltype="mbutton"
                                        url_string="gnr/X000/reportView/reportView/0/{$smarty.session.lang}/leaveAllowedCardHTML/&p[id]={$item->id}"
                                        text_value="<i class='fa fa-question-circle'></i>" modal="modal"}
                                    {getname table=hr_levst id=$item->leaveType->id}
                                </td>
                                <td align="right">{getname table=st_setting id=$item->leaveType->annual_intrvl_type}</td>
                                <td align="center">{$item->credit}&nbsp;{#gnr_day#}</td>
                                <td align="center">
                                    {if $item->leaveType->transfer_type eq 838}
                                        <span>{#p_untransferable_leave#}</span>
                                    {/if}
                                    {if $item->leaveType->transfer_type eq 808}
                                        <span>{#p_unlimited#}</span>
                                    {/if}
                                    {if $item->leaveType->transfer_type eq 809}
                                        {$item->max_credit}&nbsp;{#gnr_day#}
                                    {/if}
                                </td>
                                <td align="center">{leaveCredit LeaveAllowedObject=$item}</td>
                                <td align="center" nowrap>
                                    {url urltype="button"
                                        url_string="bsc/P051/employees/leaveshistory/0/{$smarty.session.lang}/{$item->id}"
                                        text_value="<i class='fa fa-file-text'></i>&nbsp;{#p_the_record#}" modal="modal"}
                                </td>
                                <td align="center" nowrap>
                                    {url check=0 urltype="mbutton"
                                        url_string="bsc/P051/FunctionalOperation/employeeleavesetting/0/{$smarty.session.lang}/{$item->id}"
                                        text_value="<i class='fa fa-cog'></i>&nbsp;{#gnr_settings#}"}
                                </td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {if $editcreditrequest_grf eq 0}
        <div class="widget">
            <div class="widget-header bg-lightred">
                <span class="widget-caption" style="text-align: center;">{#gnr_need_to_determine_workflow_first#}</span>
            </div><!--Widget Header-->
            <div class="widget-body">
                <code>
                    <span>{#gnr_click_here_to_move_to_workflow_operation#} : </span>
                    {url check=1 urltype="button"
                        oprvtype=1 opr_code="workflow"
                        url_string="bsc/P052/workflow/show/0/{$smarty.session.lang}"
                        text_value="{#gnr_view#}"}
                    <br><br><br><br>
                    <span>{#gnr_determine_workflow_button_appear_only_for_authorized_users#}</span>
                </code>
            </div><!--Widget Body-->
        </div>
    {else}
        <div class="widget radius-bordered">
            <div class="widget-header bg-darkgray">
                <span class="widget-caption">{#p_add_deduct_leave_credit#}</span>
                <div class="widget-buttons">
                    <a href="#" data-toggle="collapse">
                        <i class="fa fa-minus white "></i>
                    </a>
                </div>
            </div>
            <div class="widget-body bg-white" style="color: #333;">
                <table class="table table-striped table-bordered table-hover">
                    <thead>
                    <tr>
                        <th width="5%" style="background-color: #A0D468 !important;">
                            {url check=0 urltype="madd"
                                opr_code='FunctionalOperation'
                                url_string="bsc/P051/FunctionalOperation/addcredit/0/{$smarty.session.lang}"}
                        </th>
                        <th width="20%" style="background-color: #A0D468 !important;">{#p_leave_name#}</th>
                        <th width="15%" style="background-color: #A0D468 !important;">{#gnr_request_type#}</th>
                        <th width="15%" style="background-color: #A0D468 !important;">{#gnr_no_of_days#}</th>
                        <th width="15%" style="background-color: #A0D468 !important;">{#gnr_date#}</th>
                        <th width="20%" style="background-color: #A0D468 !important;">{#gnr_request_status#}</th>
                        <th width="10%" style="background-color: #A0D468 !important;" align="center">{#gnr_settings#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $crediteditList as $request}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>{getname table=hr_levst id=$request->dataObject->leave_id}</td>
                            <td align="center">{getname table=st_setting id=$request->dataObject->credit_opr_type} </td>
                            <td align="center">{$request->dataObject->credit}&nbsp;{#gnr_day#}</td>
                            <td align="center">
                                {getdate table=hr_creditedit col=take_effect_date type=show row=$request->dataObject}
                            </td>
                            <td>
                                {workflow requestId=$request->id
                                    backTo="bsc/P051/FunctionalOperation/leavesetting/0/{$smarty.session.lang}"}
                            </td>
                            <td align="center" nowrap="nowrap">
                                {if $request->send_status eq Request::REQUEST_IS_NOT_SEND}
                                    {url check=0
                                        urltype="attach"
                                        url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/editcreditrequest/leavesetting/{$request->dataObject->id}/{$smarty.session.user->id}/bsc/P051/employees/leavesetting/0/{$smarty.session.lang}/save_session/{$request->dataObject->user_id}/{$uaoRow->id}"}
                                    {url check=0
                                        urltype="medit"
                                        opr_code='FunctionalOperation'
                                        url_string="bsc/P051/FunctionalOperation/editcredit/0/{$smarty.session.lang}/{$request->dataObject->id}"}
                                    {url check=0
                                        urltype="mdelete"
                                        opr_code='FunctionalOperation'
                                        url_string="bsc/P051/FunctionalOperation/confirmcredit/0/{$smarty.session.lang}/{$request->dataObject->id}"}
                                {/if}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
    {/if}
{/block}
{block name=back}{url urltype="path"
        url_string="bsc/P051/FunctionalOperation/show2/0/{$smarty.session.lang}/{$basicVacant->sh_user_id}/{$smarty.session.unit_id}"}
{/block}