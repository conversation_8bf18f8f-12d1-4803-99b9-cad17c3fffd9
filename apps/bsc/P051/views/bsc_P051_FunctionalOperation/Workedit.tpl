{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
	<script>
		$(document).ready(function(){
			$('#filter1').change(function ()
			{
				if (this.checked)
				{
					$('#search1').hide('fast');
				}else{
					$('#search1').show('fast');
				}
			});
		});
	</script>
{/block}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#gnr_edit_row#}</h4>
	</div>
	<div class="modal-body">
		<div class="row">
			<div class="col-lg-12">
				<form  method="post" action='{url urltype="path"
					url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/ExperianceData/update/{$smarty.session.s_resume_token}"}/{$row->id}"}'>
					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_work_occupation#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<input type="test" id="name" class="form-control" name="name"
							value="{$row->name}" placeholder="{#p_name_of_work_or_employment#}">
					</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_from#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						{getdate table=db_work col=from type=edit row=$row}</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_to#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<div id="search1">
                            {getdate table=db_work col=to type=edit row=$row}<br>
						</div>
						<div class="control-group">
							<div class="radio">
								<label>
									<input type="checkbox" id="filter1" name="onwork" {if $row->onwork eq 'on'} checked {/if}>
									<span class="text">{#p_on_the_job#}</span>
								</label>
							</div>
						</div>
					</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_enterprise_company#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<input type="text" id="company" class="form-control"
					   		name="company" value="{$row->company}" placeholder="{#gnr_enterprise_company#}">
					</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_address#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<textarea id="address" class="form-control" name="address"
				  			placeholder="{#gnr_address#}">{$row->address}
						</textarea>
					</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<button type="submit" class="btn btn-warning sharp">{#gnr_udpate#}</button>
					</div>
			</form>
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}