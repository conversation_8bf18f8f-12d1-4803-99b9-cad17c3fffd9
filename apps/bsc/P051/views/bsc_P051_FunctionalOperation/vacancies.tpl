{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}

{block name=page_header}
    <script>
        $("#BasicSalary").css("display", "none");
        $("#MonthTypeDays").css("display", "none");
        $("#DaysTypeDays").css("display", "none");
        $("#hourTypeCost").css("display", "none");

        $("#doam_div").css("display", "none");
        $("#fingerprint_div").css("display", "none");

        $("#227").click(function () {
            if ($("#227").is(":checked")) {
                $("#BasicSalary").show('fast');
                $("#MonthTypeDays").show('fast');
                $("#DaysTypeDays").hide('fast');
                $("#hourTypeCost").hide('fast');
            }
        });

        $("#861").click(function () {
            if ($("#861").is(":checked")) {
                $("#BasicSalary").hide('fast');
                $("#MonthTypeDays").hide('fast');
                $("#DaysTypeDays").show('fast');
                $("#hourTypeCost").hide('fast');
            }
        });

        $("#228").click(function () {
            if ($("#228").is(":checked")) {
                $("#BasicSalary").hide('fast');
                $("#MonthTypeDays").hide('fast');
                $("#DaysTypeDays").hide('fast');
                $("#hourTypeCost").show('fast');
            }
        });

        $("#826").click(function () {
            if ($("#826").is(":checked")) {
                $("#BasicSalary").hide('fast');
                $("#MonthTypeDays").hide('fast');
                $("#DaysTypeDays").hide('fast');
                $("#hourTypeCost").hide('fast');
            }
        });

        $("#type1").click(function () {
            if ($("#type1").is(":checked")) {
                $("#doam_div").hide('fast');
                $("#fingerprint_div").hide('fast');
            }
        });

        $("#type2").click(function () {
            if ($("#type2").is(":checked")) {
                $("#doam_div").show('fast');
                $("#fingerprint_div").show('fast');
            }
        });

        $("#type3").click(function () {
            if ($("#type3").is(":checked")) {
                $("#doam_div").hide('fast');
                $("#fingerprint_div").show('fast');
            }
        });
        $('#BasicTemplatSelect').on('change', function() {
            var $select = $('#changing_template_alert_message');
            $select.show();
           $('#browseButton').show();
           $('#saveButton').hide();
        });
    </script>
    {$jscode1}
    {$jscode}
{/block}
{block name=page_body}
    <div class="widget">
        <div class="widget-body bordered-left bordered-palegreen">
            <div class="row">
                <div class="col-lg-12">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="title">{#p_primary_data#}: </h4>
                        <img src="/framework/core/functions/image.php?image={User::userProfilePicture((int) $user_employee->sh_user_id)}"
                                class="avatar-thumbnail">
                    </div>

                    <div class="mb-1">

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {$user_employee->full_name}
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_sex#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {t v=$user_employee->gender}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_neighborhood#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {getname table=st_neighborhood id=$user_employee->address_neighborhood}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_job_name#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {$user_employee->basicJob[0]->name}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_job_unit_name#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {$user_employee->basicJob[0]->unit->name}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_direct_boss#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {$user_employee->basicVacant->directBoss->full_name}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="widget collapsed">
        <div class="widget-header bg-info">
            <i class="widget-icon fa fa-tags" ></i>
            <span class="widget-caption">
				{$basicVacant->sh_user_full_name}
			</span>
        </div>
        <div class="tabbable tabs-left">
            <ul class="nav nav-tabs" id="myTab3">
                {$i=1}
                {foreach $UserVacanciesList as $vacant}
                    <li class="{if $smarty.session.s_empdata_uao_tab eq $vacant->sh_uao_id} active {/if}">
                        <a aria-expanded="false" data-toggle="tab" href="#uao{$vacant->sh_uao_id}">
                            <span class="badge badge-sky badge-square">{$i++}</span>
                            {getname table=sh_unt id=$vacant->sh_uao_job_unt_id}
                            &nbsp;|&nbsp;
                            {getname table=sh_job id=$vacant->sh_uao_job_id}
                            &nbsp;|&nbsp;
                            {if $vacant->sh_uao_basic eq 1}{#gnr_primary#}{/if}
                            {if $vacant->sh_uao_basic eq 0}{#gnr_secondary#}{/if}
                        </a>
                    </li>
                    {if $vacant->sh_uao_basic eq 1}
                        <li class="{if $smarty.session.s_empdata_uao_tab eq 'PayrollTemplate'} active {/if}">
                            <a aria-expanded="false" data-toggle="tab" href="#templates">
                                <span class="badge badge-sky badge-square mr-2">-</span>
                                {#p_salary#}
                            </a>
                        </li>
                    {/if}
                {/foreach}
            </ul>
            <div class="tab-content">
                {$i=1}
                {foreach $UserVacanciesList as $vacant}
                    <div id="uao{$vacant->sh_uao_id}"
                         class="tab-pane {if $smarty.session.s_empdata_uao_tab eq $vacant->sh_uao_id} active {/if} ">
                        <div class="col-lg-12">
                            <form method="post"
                                  action='{url urltype="path" url_string="bsc/P051/FunctionalOperation/vacancies/0/{$smarty.session.lang}/update/{$smarty.session.s_empdata_token}/{$vacant->sh_uao_id}"}'>
                                <div class="row">

                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                        {if $vacant->sh_uao_basic eq 0}
                                            {url urltype="mbutton" check=1 oprvtype=3 opr_code="FunctionalOperation"
                                                url_string="bsc/P051/FunctionalOperation/settoBasicVacant/0/{$smarty.session.lang}/{$vacant->sh_uao_id}"
                                                text_value="<i class='fa fa-list-alt'></i>&nbsp;{#p_convert_job_to_primary#}"}
                                        {/if}
                                        {url urltype="button" check=1 oprvtype=3 opr_code="FunctionalOperation"
                                            url_string="bsc/P051/FunctionalOperation/DismissalProcedures/0/{$smarty.session.lang}/{$basicVacant->sh_user_id}"
                                            text_value="<i class='fa fa-list-alt'></i>&nbsp;{#p_dismiss_from_job#}"}
                                        {if $vacant->sh_uao_basic eq 1}
                                        {url urltype="button" check=1 oprvtype=3 opr_code="FunctionalOperation"
                                            url_string="bsc/P051/FunctionalOperation/moveSpecifiedVacant/0/{$smarty.session.lang}/{$vacant->sh_uao_id}"
                                            text_value="<i class='fa fa-pencil fa-fw'></i>&nbsp;{#p_move_to_another_job#}"}
                                        {/if}
                                    </div>

                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_date_of_appointment#}</div>
                                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate table=sh_uao col=sh_uao_job_date_of_appointment type=edit row=$vacant id={$vacant->sh_uao_id+999}}</div>

                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_end_date_of_appointment#}</div>
                                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate table=sh_uao col=sh_uao_job_end_date_of_appointment type=edit row=$vacant id=$vacant->sh_uao_id}</div>

                                    {*{if $vacant->sh_uao_basic eq 1}*}
                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_direct_chief#}</div>
                                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                        <select  name="direct_boss_emp">
                                            {foreach $directManagersList  as $userVacant}
                                                <option value="{$userVacant->sh_user_id}" {if $userVacant->sh_user_id eq $vacant->sh_uao_direct_boss_emp} selected {/if}>{$userVacant->sh_user_full_name}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                    {*{/if}*}

                                </div>

                                {if $vacant->sh_uao_basic eq 1}
                                    <hr>
                                    <div class="row">
                                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_job_salary_type#}</div>
                                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                            <div class="control-group">
                                                {foreach $salaryDeliveryList as $irow}
                                                    <div class="radio">
                                                        <label>
                                                            <input type="radio" name="salary_delivery"
                                                                   value="{$irow->id}" id="{$irow->id}"
                                                                    {if $vacant->sh_uao_salary_delivery eq $irow->id}
                                                                checked="checked"
                                                                    {/if}>
                                                            <span class="text">{$irow->translatedName}</span>
                                                        </label>
                                                    </div>
                                                {/foreach}
                                            </div>
                                        </div>

                                        <div id="BasicSalary">
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_base_salary#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input
                                                        type="text" step="any" name="salary_basic" id="salary_basic"
                                                        value="{$vacant->sh_uao_salary_basic}"></div>
                                        </div>

                                        <div id="MonthTypeDays">
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_no_of_days#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                30 {#gnr_day#}</div>

                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_payroll_template#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getname table=prl_template id=$vacant->sh_uao_template_id}</div>
                                        </div>

                                        <div id="DaysTypeDays">
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_no_of_days_per_month#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input
                                                        type="number" name="days_in_month"
                                                        value="{$vacant->sh_uao_days_in_month}"></div>

                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_day_price#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input
                                                        type="number" step="any" name="days_salary"
                                                        value="{$vacant->sh_uao_days_salary}"></div>
                                        </div>

                                        <div id="hourTypeCost">
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_hour_price#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input
                                                        type="text" step="any" name="hour_salary"
                                                        value="{$vacant->sh_uao_hour_salary}"></div>
                                        </div>

                                    </div>
                                    <hr>
                                    <div class="row">

                                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_subject_to_presense_and_leave#}</div>
                                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                            <div class="control-group">
                                                <div class="radio">
                                                    <label>
                                                        <input type="radio" id="type1" name="att_status"
                                                               {if $vacant->sh_uao_att_status eq 0}checked{/if}
                                                               value="0">
                                                        <span class="text">{#p_not_subjected#}</span>
                                                    </label>
                                                </div>
                                                <div class="radio">
                                                    <label>
                                                        <input type="radio" id="type2" name="att_status"
                                                               {if $vacant->sh_uao_att_status eq 1}checked{/if}
                                                               value="1">
                                                        <span class="text">{#p_subject_to_presense_and_leave_based_on_job_time#}</span>
                                                    </label>
                                                </div>
                                                <div class="radio">
                                                    <label>
                                                        <input type="radio" id="type3" name="att_status"
                                                               {if $vacant->sh_uao_att_status eq 2}checked{/if}
                                                               value="2">
                                                        <span class="text">{#p_subject_to_presense_and_leave_based_on_hour#}</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="doam_div">
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_work#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                <select name="att_doam_id">
                                                    <option value="{$vacant->sh_uao_att_doam_id}"
                                                            selected>{getname table=hr_doam id=$vacant->sh_uao_att_doam_id}</option>
                                                    {foreach $doamList as $dorow}
                                                        <option value="{$dorow->id}">{$dorow->name}</option>
                                                    {/foreach}
                                                </select>
                                            </div>
                                        </div>

                                        <div id="fingerprint_div">

                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_fingerprint_device#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                <select name="att_device_id">
                                                    <option value="{$vacant->sh_uao_att_device_id}"
                                                            selected>{getname table=fp_dev id=$vacant->sh_uao_att_device_id}</option>
                                                    {foreach $AttendanceDevicesList as $attdrow}
                                                        <option value="{$attdrow->id}">{getname table=fp_dev id=$attdrow->id}</option>
                                                    {/foreach}
                                                </select>
                                            </div>

                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_no_on_fingerprint_device#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input
                                                        type="number" min="0" class="form-control" id="att_device_num"
                                                        name="att_device_num" value="{$vacant->sh_uao_att_device_num}"
                                                        placeholder="{#p_user_no_in_presense_and_leave_device#}"></div>

                                        </div>

                                    </div>
                                    <hr>
                                {/if}
                                <div class="row">

                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                        <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                {/foreach}
                <div id="templates"
                     class="tab-pane {if $smarty.session.s_empdata_uao_tab eq 'PayrollTemplate'} active {/if}">
                    <div class="col-lg-12" style="padding-bottom: 10%;">
                        <div>
                            <form method="post" class="pt-2" id="PayrollTemplate"
                                  action='{url urltype="path"
                                  url_string="bsc/P051/FunctionalOperation/vacancies/0/{$smarty.session.lang}/PayrollTemplate/{$smarty.session.s_empdata_token}"}'>
                                <input type="hidden" name="user_id" value="{$basicVacant->sh_user_id}">
                                <div class="row mb-2">

                                    {if $templates}
                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel mb-2">
                                            <span class="left">{#gnr_template#}</span>
                                        </div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                            {if can('edit', 'FunctionalOperation')}
                                                <select name="template" required id="BasicTemplatSelect">
                                                    <option value="">{#gnr_unspecified#}</option>
                                                    {foreach $templates  as $template}
                                                        <option value="{$template->id}"
                                                                {if $temp_rules2}
                                                                    {if $template->id eq $temp_template_id2}
                                                                        selected
                                                                    {/if}
                                                                {elseif $template->id eq $basicVacant->sh_uao_payroll_template_id}
                                                                    selected
                                                                {/if}
                                                        >{$template->name}</option>
                                                    {/foreach}
                                                </select>
                                            {else}
                                                {getname table=prl_templates id=$basicVacant->sh_uao_payroll_template_id}
                                            {/if}
                                        </div>
                                        <div id="changing_template_alert_message" style="display: none;" class="mb-4">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                                <span class="red">{#changing_will_delete_all_specfic_rules_alert#}</span>
                                            </div>
                                        </div>
                                        <div id="changing_template_alert_message" style="display: none;" class="mb-4">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                                <span class="red">{#changing_will_delete_all_specfic_rules_alert#}</span>
                                            </div>
                                        </div>
                                        <div class="col-lg-10 col-lg-offset-2 mt-1 mb-1" style="display: none" id="browseButton">
                                            <div class="text-right">
                                                <button type="submit" value="view" name="submit" class="btn btn-default sharp">{#gnr_view#}</button>
                                            </div>
                                        </div>
                                        {if $temp_rules2}
                                            <div class="col-lg-10 col-lg-offset-2 mt-1 mb-1" id="saveButton">
                                                <div class="text-right">
                                                    <button type="submit" value="update" name="submit" class="btn btn-warning sharp">{#gnr_save#}</button>
                                                </div>
                                            </div>
                                        {/if}

                                    {/if}

                                    <div id="rulestabone" class="tab-pane">
                                        <table id="snsotable-1"
                                               class="table table-hover table-striped table-bordered table-condensed">
                                            <thead>
                                            <tr>
                                                <th width="5%">
                                                    {if isset($basicVacant->sh_uao_payroll_template_id) and not isset($temp_rules2)}
                                                        {url check=1 urltype="madd" opr_code='FunctionalOperation'
                                                        url_string="bsc/P051/FunctionalOperation/addrule/0/{$smarty.session.lang}/rulestabone/{$basicVacant->sh_uao_payroll_template_id}/{$basicVacant->sh_user_id}"}
                                                    {else}
                                                        <span class="btn btn-default btn-sm sharp icon-only text-gray-dark" disabled>
                                                                <i class="fa fa-plus"></i>
                                                            </span>
                                                    {/if}
                                                </th>
                                                <th width="20%">{#gnr_term#}</th>
                                                <th width="20%">{#gnr_type#}</th>
                                                <th width="20%">{#p_affecting_day_price_estimation#}</th>
                                                <th width="20%">{#gnr_order#}</th>
                                                <th width="20%">{#gnr_value#}</th>
                                                <th width="20%">{#gnr_settings#}</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            {$i=1}
                                            {if $row}
                                              {if $temp_rules2}
                                                {*temporary rules*}
                                                {foreach $temp_rules2 as $srow}
                                                    <tr style="background-color: #cac4c4">
                                                        <td align="center">{$srow@iteration}</td>
                                                        <td align="center">{$srow->name}</td>
                                                        <td align="center">{t v=$srow->category_id}</td>
                                                        <td align="center">{t v=$srow->effect_in_day_cost}</td>
                                                        <td align="center">{$srow->order}</td>
                                                        <td align="center">
                                                            {if $srow->order == 1}
                                                                {abs($employee_salary)|number_format:2:".":","}
                                                            {elseif $srow->amount_type == 650}
                                                                <span class="{if $srow->category_id == 645} text-green text-lg {else} text-red text-lg {/if}">
                                                                                {abs($srow->amount_fixed_value)|number_format:2:".":","}
                                                                            </span>
                                                            {else}
                                                                %{$srow->amount_percentage_value}
                                                                = <span class="{if $srow->category_id == 645} text-green text-lg {else} text-red text-lg {/if}">
                                                                        {abs($srow->getParentPayrollValue($employee_salary))|number_format:2:".":","}
                                                                       </span>
                                                            {/if}
                                                        </td>
                                                        <td nowrap align="center"></td>
                                                    </tr>
                                                {/foreach}
                                              {else}
                                                {foreach $temp_rules as $row}
                                                    <tr>
                                                        <td align="center">{$i++}</td>
                                                        <td align="center">{$row->name}</td>
                                                        <td align="center">{t v=$row->category_id}</td>
                                                        <td align="center">{t v=$row->effect_in_day_cost}</td>
                                                        <td align="center">{$row->order}</td>
                                                        <td align="center">
                                                            {if $row->order == 1}
                                                                {abs($employee_salary)|number_format:2:".":","}
                                                            {elseif $row->amount_type == 650}
                                                                <span class="{if $row->category_id == 645} text-green text-lg {else} text-red text-lg {/if}">
                                                                                {abs($row->amount_fixed_value)|number_format:2:".":","}
                                                                            </span>
                                                            {else}
                                                                %{$row->amount_percentage_value}
                                                                = <span class="{if $row->category_id == 645} text-green text-lg {else} text-red text-lg {/if}">
                                                                        {abs($row->getParentPayrollValue($employee_salary))|number_format:2:".":","}
                                                                       </span>
                                                            {/if}
                                                        </td>
                                                        <td nowrap align="center"></td>
                                                    </tr>
                                                {/foreach}
                                                {foreach $employeeSpecificTemplate as $specificTemplate}
                                                    {if !$specificTemplate->isNull()}
                                                        <tr style="background-color: #cac4c4">
                                                            <td align="center">{$i++}</td>
                                                            <td align="center">{$specificTemplate->template->name}</td>
                                                            <td align="center">{t v=$specificTemplate->template->category_id}</td>
                                                            <td align="center">{t v=$specificTemplate->template->effect_in_day_cost}</td>
                                                            <td align="center">{$specificTemplate->template->order}</td>
                                                            <td align="center">
                                                                {if $specificTemplate->template->amount_percentage_value neq null  }
                                                                    %{$specificTemplate->template->amount_percentage_value}
                                                                    = <span class="{if $specificTemplate->template->category_id == 645} text-green text-lg {else} text-red text-lg {/if}">
                                                                                {abs($specificTemplate->getParentPayrollValue($employee_salary))|number_format:2:".":","}
                                                                    <span class="mr-1">
                                                                                    {url check=0 urltype="mbutton" opr_code='payroll'
                                                                                    url_string="bsc/P051/FunctionalOperation/payrollDetails/0/{$smarty.session.lang}/{$specificTemplate->id}"  text_value="?" style="btn btn-sm sharp"}
                                                                                </span>
                                                                            </span>
                                                                {else}
                                                                    <span class="{if $specificTemplate->template->category_id == 645} text-green text-lg {else} text-red text-lg {/if}">
                                                                                {abs($specificTemplate->template->amount_fixed_value)|number_format:2}
                                                                            </span>
                                                                {/if}
                                                            </td>
                                                            <td nowrap align="center">
                                                                {url check=1 urltype="medit" opr_code="FunctionalOperation" url_string="bsc/P051/FunctionalOperation/EditRule/0/{$smarty.session.lang}/{$specificTemplate->id}/{$smarty.session.s_prl_templates_id}"}
                                                                {url check=1 urltype="mdelete" opr_code="FunctionalOperation"
                                                                url_string="bsc/P051/FunctionalOperation/ruleConfirm/0/{$smarty.session.lang}/{$specificTemplate->id}/{$smarty.session.s_prl_templates_id}"}
                                                            </td>
                                                        </tr>
                                                    {/if}
                                                {/foreach}
                                              {/if}
                                                <tr style="background-color: #8cc474">
                                                    <td align="center"></td>
                                                    <td></td>
                                                    <td align="center"></td>
                                                    <td align="center"></td>
                                                    <td align="center">
                                                        <b>{#gnr_total_salary#}</b>
                                                    </td>
                                                    <td nowrap align="center">
                                                        <b>{$employee_total_salary|number_format:2:".":","}</b>
                                                    </td>
                                                    <td align="center"></td>
                                                </tr>
                                            {/if}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <span class="hidden">{$i++}</span>
            </div>
        </div>
    </div>
{/block}
{block name=back}
    {url urltype="path"
        url_string="bsc/P051/FunctionalOperation/show2/0/{$smarty.session.lang}/{$basicVacant->sh_user_id}/{$smarty.session.unit_id}"}
{/block}
