{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script>
        $(document).ready(function(){

            $("#366div").css("display","none");

            $("#365").click(function(){

                if ($("#365").is(":checked"))
                {
                    $("#366div").hide("fast");
                }
            });

            $("#366").click(function(){

                if ($("#366").is(":checked"))
                {
                    $("#366div").show("fast");
                    $("#365div").hide("fast");
                }
            });

        });
    </script>

{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add_row#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url urltype="path"
                url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/IncomeData/insert/{$smarty.session.s_resume_token}"}'>
                <div class="col-lg-12">
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_work_type#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="work_type" required>
                            <option value="" selected>{#p_select_from_menu#}</option>
                            {foreach $work_list as $wrow}
                                <option value="{$wrow->id}">{$wrow->translatedName}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_monthly_income#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <input class="form-control" type="text" name="monthly_incole_value" min="0" required>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_income_status#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $inc_montyly_list as $mrow}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="monthly_income_type" id="{$mrow->id}" value="{$mrow->id}">
                                        <span class="text">{$mrow->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                        <span id="366div">
							{#p_from#} {getdate table=db_inc col=monthly_incole_vfrom type=add row=$row}
                            <br>
                            {#p_to#} {getdate table=db_inc col=monthly_incole_vto type=add row=$row}
						</span>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}