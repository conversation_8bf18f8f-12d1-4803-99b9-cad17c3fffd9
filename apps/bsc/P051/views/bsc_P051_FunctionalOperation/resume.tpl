{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <ul class="nav nav-tabs">
        <li class="tab-sky {if $smarty.session.s_active_userdata_tab eq 'usercv'} active {/if}">
            <a aria-expanded="false" data-toggle="tab" href="#usercv">
                <span>{#p_data#}</span>
            </a>
        </li>
        <li class="tab-red {if $smarty.session.s_active_userdata_tab eq 'userImage'} active {/if}">
            <a aria-expanded="false" data-toggle="tab" href="#userImage">
                <span>{#p_image#}</span>
            </a>
        </li>
        <li class="tab-red {if $smarty.session.s_active_userdata_tab eq 'userClassification'} active {/if}">
            <a aria-expanded="false" data-toggle="tab" href="#userClassification">
                <span>{#gnr_category#}</span>
            </a>
        </li>
    </ul>
    <div class="tab-content">
        <div id="usercv" class="tab-pane {if $smarty.session.s_active_userdata_tab eq 'usercv'} active {/if}">
            <div class="row">
                <div class="col-md-12">
                    <div class="tabbable tabs-left">
                        <ul class="nav nav-tabs">
                            {$i=1}
                            {foreach $allowedUserClasses as $userInfo}
                                {if $userInfo->id neq 746}
                                    <li class="tab-sky {if $smarty.session.s_active_cv_tab eq $userInfo->code} active {/if}">
                                        <a aria-expanded="false" data-toggle="tab"
                                           href="#tab{$userInfo->id}">
                                            <span class="badge badge-sky badge-square">{$i++}</span>
                                            {$userInfo->translatedName}
                                        </a>
                                    </li>
                                {/if}
                            {/foreach}
                        </ul>

                        <div class="tab-content">
                            <div id="tab743"
                                 class="tab-pane {if $smarty.session.s_active_cv_tab eq 'PrimaryData'} active {/if}">
                                <form method="post"
                                      action='{url urltype="path" url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/PrimaryData/update/{$smarty.session.s_resume_token}"}'>

                                    <div class="col-lg-12">
                                        <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_first_name#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <input type="text" class="form-control input-sm"
                                                       id="fname" name="fr_name"
                                                       value="{$user->fr_name}"
                                                       placeholder="{#gnr_first_name#}" required>
                                            </div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_second_name#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <input type="text" class="form-control input-sm"
                                                       id="sname" name="secd_name"
                                                       value="{$user->secd_name}"
                                                       placeholder="{#gnr_second_name#}"></div>
                                        </div>

                                        <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_third_name#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <input type="text" class="form-control input-sm"
                                                       id="tname" name="thrd_name"
                                                       value="{$user->thrd_name}"
                                                       placeholder="{#gnr_third_name#}"></div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_family_name#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <input type="text" class="form-control input-sm"
                                                       id="lname" name="fm_name"
                                                       value="{$user->fm_name}"
                                                       placeholder="{#gnr_family_name#}"></div>
                                        </div>

                                        <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_english_name#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <input type="text" class="form-control input-sm"
                                                       id="ename" name="e_name"
                                                       value="{$user->e_name}"
                                                       placeholder="{#p_name_in_english#}"></div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_sex#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <div class="control-group">
                                                    <div class="radio">
                                                        <label>
                                                            <input type="radio" id="male"
                                                                   name="gender"
                                                                   value="51"
                                                                   {if $user->gender eq 51}checked{/if}>
                                                            <span class="text">{#gnr_male#}</span>
                                                        </label>

                                                        <label>
                                                            <input type="radio" id="male"
                                                                   name="gender"
                                                                   value="52"
                                                                   {if $user->gender eq 52}checked{/if}>
                                                            <span class="text">{#gnr_female#}</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_social_status#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <select name="social_status">
                                                    <option value="{$user->social_status}"
                                                            selected>{getname table=st_setting id=$user->social_status}</option>
                                                    {foreach key=id item=srow from=$social_list}
                                                        <option value="{$srow->id}">{$srow->translatedName}</option>
                                                    {/foreach}
                                                </select>
                                            </div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_nationality#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <select name="nationality">
                                                    <option value="{$user->nationality}"
                                                            selected>{getname table=st_country id=$user->nationality}</option>
                                                    {foreach key=id item=nrow from=$nationality_list}
                                                        <option value="{$nrow->id}">{getname table=st_country id=$nrow->id}</option>
                                                    {/foreach}
                                                </select>
                                            </div>

                                        </div>

                                        <div class="row">

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_work_status#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <select name="work_status">
                                                    <option value="{$user->work_status}"
                                                            selected>{getname table=st_setting id=$user->work_status}</option>
                                                    {foreach key=id item=wrow from=$work_status_list}
                                                        <option value="{$wrow->id}">{$wrow->translatedName}</option>
                                                    {/foreach}
                                                </select>
                                            </div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_education_level#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <select name="educational_level">
                                                    <option value="{$user->educational_level}"
                                                            selected>{getname table=st_setting id=$user->educational_level}</option>
                                                    {foreach key=id item=erow from=$educational_levels_list}
                                                        <option value="{$erow->id}">{$erow->translatedName}</option>
                                                    {/foreach}
                                                </select>
                                            </div>

                                        </div>

                                        <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_birthday#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">{getdate table=sh_user col=birth_date type=edit row=$user}</div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_birth_place#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <input type="text" class="form-control input-sm"
                                                       id="birth_place" name="birth_place"
                                                       value="{$user->birth_place}"
                                                       placeholder="{#gnr_birth_place#}"></div>
                                        </div>

                                        <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_age#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <input type="text" class="form-control input-sm" disabled
                                                       value="{$user->age}"></div>

                                        </div>

                                        <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_id_type#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <select name="identity_type">
                                                    <option value="{$user->identity_type}"
                                                            selected>{getname table=st_setting id=$user->identity_type}</option>
                                                    {foreach key=id item=irow from=$identity_list}
                                                        <option value="{$irow->id}">{$irow->translatedName}</option>
                                                    {/foreach}
                                                </select>
                                            </div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_id_no#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <input type="text" class="form-control input-sm"
                                                       id="identity_number" name="identity_number"
                                                       maxlength="10"
                                                       value="{$user->identity_number}"
                                                       placeholder="{#gnr_id_no#}"></div>
                                        </div>

                                        <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_issue_date#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">{getdate table=sh_user col=identity_essue_date type=edit row=$user}</div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_expiration_date#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">{getdate table=sh_user col=identity_expairy_date type=edit row=$user}</div>
                                        </div>

                                        <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_issue_place#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <input type="text" class="form-control input-sm"
                                                       id="identity_essue_place"
                                                       name="identity_essue_place"
                                                       placeholder="{#gnr_issue_place#}"
                                                       value="{$user->identity_essue_place}"></div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_employment_id_number#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <input type="text" class="form-control input-sm"
                                                       id="employment_number"
                                                       name="employment_number"
                                                       placeholder="{#p_employment_id_number#}"
                                                       value="{$user->employment_number}"></div>
                                        </div>

                                        <div class="row">
                                            <div id="bank">
                                                <div id="alrajhi">
                                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_bank_name#}</div>
                                                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                        <select onchange="getInputTag(event)" name="bank_code">
                                                            <option value="">{$user->bank_name}</option>
                                                            {foreach $bank_list as $irow}
                                                                <option value="{$irow->code}"
                                                                        {if $irow->code eq $user->bank_code}selected{/if}>{$irow->translatedName}</option>
                                                            {/foreach}
                                                            <option value="OTHER">{#gnr_other#}</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div id="other" style="display: none;">
                                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_bank_name#}</div>
                                                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                        <input class="form-control hide-spinner input-sm"
                                                               id="bank_name" name="bank_name"
                                                               value="{$user->bank_name}"
                                                               placeholder="{#gnr_bank_name#}">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_iBan_number#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <input class="form-control hide-spinner input-sm"
                                                       id="bank_number" name="bank_number"
                                                       placeholder="{#p_iBan_number#}"
                                                       value="{$user->bank_number}"></div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_neighborhood#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <select name="address_neighborhood" id="country"
                                                        style='height:50px !important'>
                                                    <option value=""></option>
                                                    {foreach $neighborhoods as $neighborhood}
                                                        <option value="{$neighborhood->id}" {if $neighborhood->id eq $user->address_neighborhood} selected {/if}>{getname table=st_city id=$neighborhood->city_id}
                                                            &nbsp;&raquo;&nbsp;{$neighborhood->name}</option>
                                                    {/foreach}
                                                </select>
                                            </div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_home_no#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-2 col-xs-12 snsoinput">
                                                <input type="text" class="form-control input-sm"
                                                       id="address_house_number"
                                                       name="address_house_number"
                                                       value="{$user->address_house_number}"
                                                       placeholder="{#gnr_home_no#}"></div>

                                        </div>

                                        <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_health_status#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <select name="health_status">
                                                    <option value=""
                                                            selected>{#gnr_select_from_menu#}</option>
                                                    {foreach $health_status_list as $hrow}
                                                        <option value="{$hrow->id}"
                                                                {if $user->health_status eq $hrow->id}selected{/if}>{t v=$hrow->id}</option>
                                                    {/foreach}
                                                </select>
                                            </div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_health_status_desc#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                            <textarea class="form-control"
                                                                      name="health_status_desc">{$user->health_status_desc}</textarea>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_about_me#}</div>
                                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                                            <textarea class="form-control input-sm" id="about_me"
                                                                      name="about_me"
                                                                      placeholder="{#p_talk_about_your_self#}">{$user->about_me}</textarea>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 snsoinput">
                                                <button type="submit"
                                                        class="btn btn-warning sharp">{#gnr_update#}</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <div id="tab744"
                                 class="tab-pane {if $smarty.session.s_active_cv_tab eq 'EducationalData'} active {/if}">
                                <table id="snsotable-1"
                                       class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th>{url check=0 urltype="madd"
                                            url_string="bsc/P051/FunctionalOperation/Eduadd/0/{$smarty.session.lang}"}</th>
                                        <th>{#gnr_qualification#}</th>
                                        <th>{#gnr_graduation_certificate_date#}</th>
                                        <th>{#gnr_specialization#}</th>
                                        <th>{#gnr_educational_institution#}</th>
                                        <th>{#gnr_settings#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $db_edu_list as $row}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td>{t v=$row->type}</td>
                                            <td>{getdate table=db_edu col=date type=show row=$row}</td>
                                            <td>{$row->name}</td>
                                            <td>{$row->unv}</td>
                                            <td align="center" nowrap>
                                                {url check=0 urltype="attach"
                                                    url_string="bsc/P051/FunctionalOperation/resumeDocuments/0/{$smarty.session.lang}/save_session/resume/db_edu/{$row->id}/{$smarty.session.user->id}/bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/{$user->id}"}
                                                {url check=0 urltype="medit"
                                                    url_string="bsc/P051/FunctionalOperation/Eduedit/0/{$smarty.session.lang}/{$row->id}"}
                                                {url check=0 urltype="mdelete"
                                                    url_string="bsc/P051/FunctionalOperation/eduConfirm/0/{$smarty.session.lang}/{$row->id}"}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>

                            <div id="tab745"
                                 class="tab-pane {if $smarty.session.s_active_cv_tab eq 'ExperianceData'} active {/if}">
                                <table id="snsotable-1"
                                       class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th>{url check=0 urltype="madd"
                                            url_string="bsc/P051/FunctionalOperation/Workadd/0/{$smarty.session.lang}"}</th>
                                        <th>{#gnr_work_occupation#}</th>
                                        <th data-priority="1">{#gnr_from#}</th>
                                        <th data-priority="2">{#gnr_to#}</th>
                                        <th data-priority="3">{#gnr_enterprise_company#}</th>
                                        <th data-priority="4">{#gnr_address#}</th>
                                        <th>{#gnr_settings#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $db_work_list as $row}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td>{$row->name}</td>
                                            <td align="center">{getdate table=db_work col=from type=show row=$row}</td>
                                            <td align="center">
                                                {if $row->onwork eq 'on'}
                                                    {#p_on_the_job#}
                                                {else}
                                                    {getdate table=db_work col=to type=show row=$row}
                                                {/if}
                                            </td>
                                            <td align="center">{$row->company}</td>
                                            <td align="center">{$row->address}</td>
                                            <td align="center" nowrap>
                                                {url check=0 urltype="attach"
                                                    url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/resume/db_work/{$row->id}/{$smarty.session.user->id}/gnr/X000/resume/edit/0/{$smarty.session.lang}/{$user->id}"}
                                                {url check=0 urltype="medit"
                                                    url_string="gnr/X000/resume/Workedit/0/{$smarty.session.lang}/{$row->id}"}
                                                {url check=0 urltype="mdelete"
                                                    url_string="bsc/P051/FunctionalOperation/Workconfirm/0/{$smarty.session.lang}/{$row->id}"}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>


                            <div id="tab747"
                                 class="tab-pane {if $smarty.session.s_active_cv_tab eq 'TrainingData'} active {/if}">
                                <table id="snsotable-1"
                                       class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th>{url check=0 urltype="madd"
                                            url_string="bsc/P051/FunctionalOperation/trainAdd/0/{$smarty.session.lang}"}
                                        </th>
                                        <th>{#p_course_name#}</th>
                                        <th data-priority="2">{#p_course_to#}</th>
                                        <th data-priority="4">{#p_course_location#}</th>
                                        <th data-priority="5">{#gnr_duration_in_days#}</th>
                                        <th data-priority="6">{#gnr_duration_in_hours#}</th>
                                        <th data-priority="7">{#gnr_certification_date#}</th>
                                        <th> {#gnr_settings#} </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $db_train_list as $row}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td>{$row->name}</td>
                                            <td>{$row->inst}</td>
                                            <td>{$row->place}</td>
                                            <td>{$row->days}</td>
                                            <td>{$row->hours}</td>
                                            <td>{getdate table=db_train col=issuedate type=show row=$row}</td>
                                            <td align="center" nowrap>
                                                {url check=0 urltype="attach"
                                                    url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/resume/db_train/{$row->id}/{$smarty.session.user->id}/gnr/X000/resume/edit/0/{$smarty.session.lang}/{$user->id}"}
                                                {url check=0 urltype="medit"
                                                    url_string="bsc/P051/FunctionalOperation/trainEdit/0/{$smarty.session.lang}/{$row->id}"}
                                                {url check=0 urltype="mdelete"
                                                    url_string="bsc/P051/FunctionalOperation/trainConfirm/0/{$smarty.session.lang}/{$row->id}"}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>

                            <div id="tab748"
                                 class="tab-pane {if $smarty.session.s_active_cv_tab eq 'HomeAddress'} active {/if}">

                                <table id="snsotable-1"
                                       class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%">{url check=0 urltype="madd"
                                            url_string="bsc/P051/FunctionalOperation/houseAdd/0/{$smarty.session.lang}/{$user->id}"}</th>
                                        <th width="15%" data-priority="1">{#gnr_name#}</th>
                                        <th width="10%" data-priority="1">{#p_house_type#}</th>
                                        <th width="10%" data-priority="1">{#p_building_type#}</th>
                                        <th width="10%" data-priority="1">{#p_ownership_type#}</th>
                                        <th width="10%" data-priority="1">{#p_house_space#}</th>
                                        <th width="10%" data-priority="1">{#p_floor_number#}</th>
                                        <th width="20%" data-priority="1">{#p_national_id_number#}</th>
                                        <th width="10%" data-priority="1">{#p_current_house#}</th>
                                        <th width="10%"> {#gnr_settings#} </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $houses_list as $row}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td align="center">{$row->name}</td>
                                            <td>{t v=$row->type}</td>
                                            <td align="center">{t v=$row->building_type}</td>
                                            <td align="center">
                                                {t v=$row->owning_level}
                                            </td>
                                            <td align="center">{t v=$row->space}</td>
                                            <td align="center">{$row->floor_number}</td>
                                            <td align="center">{$row->national_id_number}</td>
                                            <td align="center">
                                                <label>
                                                    {if !! $row->status}
                                                        <i class="fa fa-circle green"></i>
                                                    {else}
                                                        <i class="fa fa-circle gray"></i>
                                                    {/if}
                                                </label>
                                            </td>
                                            <td align="center" nowrap>
                                                {url check=0 urltype="attach"
                                                    url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/resume/db_houses/{$row->id}/{$smarty.session.user->id}/gnr/X000/resume/edit/0/{$smarty.session.lang}/{$user->id}"}
                                                {url check=0 urltype="medit"
                                                    url_string="bsc/P051/FunctionalOperation/housesEdit/0/{$smarty.session.lang}/{$row->id}"}
                                                {if !! $row->status}
                                                {else}
                                                    {url check=0 urltype="mdelete"
                                                        url_string="bsc/P051/FunctionalOperation/housesConfirm/0/{$smarty.session.lang}/{$row->id}/{$user->id}"}
                                                {/if}

                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>

                            <div id="tab749"
                                 class="tab-pane {if $smarty.session.s_active_cv_tab eq 'IncomeData'} active {/if}">
                                <table id="snsotable-1"
                                       class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%">{url check=0 urltype="madd"
                                            url_string="bsc/P051/FunctionalOperation/incomeAdd/0/{$smarty.session.lang}"}</th>
                                        <th width="20%" data-priority="1">{#p_income_type#}</th>
                                        <th width="15%" data-priority="1">{#p_income_value#}</th>
                                        <th width="15%" data-priority="1">{#p_income_status#}</th>
                                        <th width="15%" data-priority="1">{#gnr_from#}</th>
                                        <th width="15%" data-priority="1">{#gnr_to#}</th>
                                        <th width="15%"> {#gnr_settings#} </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $db_inc_list as $row}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td>{t v=$row->work_type}</td>
                                            <td align="center">{$row->monthly_incole_value}</td>
                                            <td align="center">{t v=$row->monthly_income_type}</td>
                                            <td align="center">{if $row->monthly_income_type eq 366}{getdate table=sh_user col=monthly_incole_vfrom type=show row=$row}{else} ---- {/if}</td>
                                            <td align="center">{if $row->monthly_income_type eq 366}{getdate table=sh_user col=monthly_incole_vto type=show row=$row}{else}   ---- {/if}</td>
                                            <td align="center" nowrap>
                                                {url check=0 urltype="attach"
                                                    url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/resume/db_inc/{$row->id}/{$smarty.session.user->id}/gnr/X000/resume/edit/0/{$smarty.session.lang}/{$user->id}"}
                                                {url check=0 urltype="medit"
                                                    url_string="bsc/P051/FunctionalOperation/incomeEdit/0/{$smarty.session.lang}/{$row->id}"}
                                                {url check=0 urltype="mdelete"
                                                    url_string="bsc/P051/FunctionalOperation/incomeConfirm/0/{$smarty.session.lang}/{$row->id}"}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>

                            <div id="tab1200"
                                 class="tab-pane {if $smarty.session.s_active_cv_tab eq 'VehicleData'} active {/if}">
                                <table id="snsotable-1"
                                       class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%">{url check=0 urltype="madd"
                                            url_string="bsc/P051/FunctionalOperation/vehicleAdd/0/{$smarty.session.lang}"}</th>
                                        <th width="20%" data-priority="1">{#p_vehicle_type#}</th>
                                        <th width="20%" data-priority="1">{#p_vehicle_model#}</th>
                                        <th width="15%"> {#gnr_settings#} </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $db_vehicle_list as $row}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td class="text-center">{$row->type}</td>
                                            <td class="text-center">{$row->model}</td>
                                            <td align="center" nowrap>
                                                {url check=0 urltype="attach"
                                                    url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/resume/db_vehical/{$row->id}/{$smarty.session.user->id}/gnr/X000/resume/edit/0/{$smarty.session.lang}/{$user->id}"}
                                                {url check=0 urltype="medit"
                                                    url_string="bsc/P051/FunctionalOperation/Vehicleedit/0/{$smarty.session.lang}/{$row->id}"}
                                                {url check=0 urltype="mdelete"
                                                    url_string="bsc/P051/FunctionalOperation/vehicleConfirm/0/{$smarty.session.lang}/{$row->id}"}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>

                            <div id="tab1203"
                                 class="tab-pane {if $smarty.session.s_active_cv_tab eq 'WorkerData'} active {/if}">
                                <table id="snsotable-1"
                                       class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%">{url check=0 urltype="madd"
                                            url_string="bsc/P051/FunctionalOperation/workerAdd/0/{$smarty.session.lang}"}</th>
                                        <th width="20%" data-priority="1">{#p_worker_type#}</th>
                                        <th width="20%" data-priority="1">{#p_worker_nationality#}</th>
                                        <th width="20%" data-priority="1">{#p_worker_stillworking#}</th>
                                        <th width="15%"> {#gnr_settings#} </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $db_worker_list as $row}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td class="text-center">{$row->type}</td>
                                            <td class="text-center">{getname table=st_country id=$row->nationality}</td>
                                            <td class="text-center">
                                                {if $row->stillworking eq 'yes'}
                                                    {#gnr_yes#}
                                                {else}
                                                    {#gnr_no#}
                                                {/if}
                                            </td>
                                            <td align="center" nowrap>
                                                {url check=0 urltype="attach"
                                                    url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/resume/db_worker/{$row->id}/{$smarty.session.user->id}/gnr/X000/resume/edit/0/{$smarty.session.lang}/{$user->id}"}
                                                {url check=0 urltype="medit"
                                                    url_string="bsc/P051/FunctionalOperation/workerEdit/0/{$smarty.session.lang}/{$row->id}"}
                                                {url check=0 urltype="mdelete"
                                                    url_string="bsc/P051/FunctionalOperation/workerConfirm/0/{$smarty.session.lang}/{$row->id}"}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>
                            <div id="tab1201"
                                 class="tab-pane {if $smarty.session.s_active_cv_tab eq 'DebtData'} active {/if}">
                                <table id="snsotable-1"
                                       class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%">{url check=0 urltype="madd"
                                            url_string="bsc/P051/FunctionalOperation/depitAdd/0/{$smarty.session.lang}"}</th>
                                        <th width="10%">{#gnr_type#}</th>
                                        <th width="10%">{#p_loan_type#}</th>
                                        <th width="10%"> {#p_installment_type#} </th>
                                        <th width="10%"> {#p_loan_value#} </th>
                                        <th width="10%"> {#p_installment_value#} </th>
                                        <th width="15%"> {#gnr_comment#} </th>
                                        <th width="10%"> {#gnr_from#} </th>
                                        <th width="10%"> {#gnr_to#} </th>
                                        <th width="10%"> {#gnr_settings#} </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $db_debt_list as $debt}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td class="text-center">{t v=$debt->type}</td>
                                            <td class="text-center">{t v=$debt->loan_type}</td>
                                            <td class="text-center">{t v=$debt->installment_type}</td>
                                            <td class="text-center">{$debt->loan_value}</td>
                                            <td class="text-center">{$debt->installment_value}</td>
                                            <td class="text-center">{$debt->comment}</td>
                                            <td class="text-center">{getdate table=db_debt col=from_date type=show row=$debt}</td>
                                            <td class="text-center">{getdate table=db_debt col=to_date type=show row=$debt}</td>
                                            <td align="center" nowrap>
                                                {url check=0 urltype="attach"
                                                    url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/resume/db_debt/{$row->id}/{$smarty.session.user->id}/gnr/X000/resume/edit/0/{$smarty.session.lang}/{$user->id}"}
                                                {url check=0 urltype="medit"
                                                    url_string="bsc/P051/FunctionalOperation/depitEdit/0/{$smarty.session.lang}/{$debt->id}"}
                                                {url check=0 urltype="mdelete"
                                                    url_string="bsc/P051/FunctionalOperation/depitConfirm/0/{$smarty.session.lang}/{$debt->id}"}
                                                    url_string="gnr/X000/resume/Depitedit/0/{$smarty.session.lang}/{$debt->id}"}
                                                {url check=0 urltype="mdelete"
                                                    url_string="gnr/X000/resume/Depitconfirm/0/{$smarty.session.lang}/{$debt->id}"}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>

                            <div id="tab1202"
                                 class="tab-pane {if $smarty.session.s_active_cv_tab eq 'LiveStockData'} active {/if}">
                                <table id="snsotable-1"
                                       class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%">{url check=0 urltype="madd"
                                            url_string="bsc/P051/FunctionalOperation/liveStockAdd/0/{$smarty.session.lang}"}</th>
                                        <th width="20%" data-priority="1">{#p_livestock_amount#}</th>
                                        <th width="20%" data-priority="1">{#p_livestock_type#}</th>
                                        <th width="15%"> {#gnr_settings#} </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $db_livestock_list as $row}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td class="text-center">{$row->amount}</td>
                                            <td class="text-center">{getname table=st_setting id=$row->type}</td>
                                            <td align="center" nowrap>
                                                {url check=0 urltype="attach"
                                                    url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/resume/db_livestock/{$row->id}/{$smarty.session.user->id}/gnr/X000/resume/edit/0/{$smarty.session.lang}/{$user->id}"}
                                                {url check=0 urltype="medit"
                                                    url_string="bsc/P051/FunctionalOperation/liveStockEdit/0/{$smarty.session.lang}/{$row->id}"}
                                                {url check=0 urltype="mdelete"
                                                    url_string="gnr/X000/resume/LiveStockconfirm/0/{$smarty.session.lang}/{$row->id}"}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="userImage"
             class="tab-pane {if $smarty.session.s_active_userdata_tab eq 'userImage'} active {/if}">
            <table cellspacing="0" id="tech-companies-1"
                   class="table table-small-font table-bordered table-striped">
                <thead>
                <tr>
                    <th width="5%">{url check=0 urltype="madd"
                        url_string="bsc/P051/FunctionalOperation/addProfilePicture/0/{$smarty.session.lang}"}</th>
                    <th width="35%">{#p_profile_picture#}</th>
                    <th width="30%">{#gnr_status#}</th>
                    <th width="30%">{#gnr_settings#}</th>
                </tr>
                </thead>
                <tbody>
                {$order = 1}
                {foreach $userProfilePictures as $userProfilePicture}
                    <tr>
                        <td align="center">{$order++}</td>
                        <td>
                            {$userProfilePicture->name}<br>
                            <img src="/framework/core/functions/image.php?image={$userProfilePicture->absolutePath}&width=100&height=100"
                                 class="img-thumbnail">
                        </td>
                        <td align="center">

                            {if $userProfilePicture->activation eq 1}
                                <li class="fa fa-circle fa-fw green"></li>
                            {/if}

                            {if $userProfilePicture->activation eq 0}
                                <li class="fa fa-circle fa-fw gray"></li>
                                {url check=0 urltype="button"
                                    url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/documents/activationProfilePicture/{$smarty.session.s_resume_token}/{$userProfilePicture->id}" text_value="{#gnr_activation#}"}
                            {/if}
                        </td>
                        <td class="text-center">
                            {url check=0 urltype="medit"
                                url_string="bsc/P051/FunctionalOperation/editProfilePicture/0/{$smarty.session.lang}/{$userProfilePicture->id}"}
                            {url check=0 urltype="mdelete"
                                url_string="bsc/P051/FunctionalOperation/deleteProfilePicture/0/{$smarty.session.lang}/{$userProfilePicture->id}"}
                        </td>
                    </tr>
                {/foreach}
                </tbody>
            </table>
        </div>
        <div id="userClassification"
             class="tab-pane {if $smarty.session.s_active_userdata_tab eq 'userClassification'} active {/if}">
            {if $userclassifications}
                {foreach $userclassifications as $id}
                    <h5 class="row-title">
                        <i class="fa fa-tag darkorange" ></i>
                        {getname table=sh_userclasses id=$id}&nbsp;
                    </h5>
                {/foreach}
            {/if}

            <div class="row">
                <div class="col-lg-12">
                    <form method="post"
                          action='{url urltype="path" url_string="bsc/P051/FunctionalOperation/resume/0/{$smarty.session.lang}/Classifications/{$smarty.session.s_resume_token}"}'>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_category#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                {foreach $User_Type_List as $UTrow}
                                    <div class="checkbox">
                                        <label>
                                            <input name="classification[]" value="{$UTrow->id}"
                                                   type="checkbox" {if in_array($UTrow->id,','|explode:$user->classification)} checked="checked" {/if}>
                                            <span class="text">{getname table=sh_userclasses id=$UTrow->id}</span>
                                        </label>
                                    </div>
                                {/foreach}
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
{/block}
{block name=page_header}
    <script src="/templates/assets/resources/dist/app.js"></script>
    <script>
        $(document).ready(function () {
            $("#country").change(function () {
                $.get('/framework/core/functions/ajax/country_change.php?country_selected_id=' + $(this).val(), function (data) {
                    $("#region").html(data);
                });
                $.get('/framework/core/functions/ajax/region_change.php?region_selected_id=' + $(this).val(), function (data) {
                    $("#city").html(data);
                });
            });

            $("#region").change(function () {
                $.get('/framework/core/functions/ajax/region_change.php?region_selected_id=' + $(this).val(), function (data) {
                    $("#city").html(data);
                });
            });

            $("#phone").keypress(function (e) {
                //if the letter is not digit then display error and don't type anything
                if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which > 57)) {
                    //display error message
                    $("#errmsg1").html("{#gnr_only_numbers#}").show().fadeOut("slow");
                }
            });

            $("#tell").keypress(function (e) {
                //if the letter is not digit then display error and don't type anything
                if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which > 57)) {
                    //display error message
                    $("#errmsg2").html("{#gnr_only_numbers#}").show().fadeOut("slow");
                }
            });


        });
        function getInputTag(e) {
            if (e.target.value === "OTHER") {
                $("#alrajhi").remove();
                $("#other").show();
            }
        }


    </script>
    <script>
        $(document).ready(function () {

            $("#367div").css("display", "none");
            $("#368div").css("display", "none");

            $("#367").click(function () {
                debugger
                if ($("#367").is(":checked")) {
                    $("#367div").show("fast");
                    $("#368div").hide("fast");
                }
            });

            $("#368").click(function () {

                if ($("#368").is(":checked")) {
                    $("#368div").show("fast");
                    $("#367div").hide("fast");
                }
            });
            var cleave = new Cleave('#bank_number', {
                prefix: 'SA',
                numericOnly: true,
                blocks: [4, 4, 4, 4, 4, 4]
            })
        });
    </script>
    {$codeone}

{/block}
{block name=back}
    {url urltype="path"
        url_string="bsc/P051/FunctionalOperation/show2/0/{$smarty.session.lang}/{$basicVacant->sh_user_id}/{$smarty.session.unit_id}"}
{/block}
