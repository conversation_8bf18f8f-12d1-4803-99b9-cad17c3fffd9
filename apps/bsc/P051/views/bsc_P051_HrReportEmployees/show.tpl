{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}

{block name=page_header}
{/block}
{block name=page_body}
<div class="widget-body">
    <div class="horizontal-space"></div>
    <div class="row">
        <div class="col-lg-12 hidden-print">
            <form action='{url urltype="path"
                url_string="bsc/P051/HrReportEmployees/show/0/{$smarty.session.lang}/save_session"}'
                  method="post">
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_employee#}</div>
                <div class="col-lg-10 col-md-10 col-sm-10 col-xs-12 snsoinput">
                    <div class="col-lg-12" id="employees">
                        <select name="employee_id">
                            <option value="">{#gnr_unspecified#}</option>
                            {foreach $employees as $employee_dropdown}
                                <option value="{$employee_dropdown->userObject->id}"
                                        {if $post['employee_id'] eq $employee_dropdown->userObject->id}selected{/if}>
                                    {$employee_dropdown->userObject->full_name}
                                </option>
                            {/foreach}
                        </select>
                    </div>
                </div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-default shiny">{#gnr_view#}</button>
                </div>
            </form>
        </div>
    </div>
</div>

    <div class="horizontal-space"></div>
    <div class="row snsowraper">
        {if !is_null($post['employee_id'])}
        <div class="widget">
            <div class="widget-body bordered-left bordered-palegreen">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="mb-1">
                            {url check=0 urltype="alinkn"
                                url_string="bsc/P051/HrReportEmployees/print/0/{$smarty.session.lang}/{$employee->id}" text_value="{#gnr_print#}" style="btn btn-defualt"}
                        </div>

                        <div class="flex justify-between items-center mb-4">
                            <h4 class="title">{#p_primary_data#}: </h4>
                            <img src='{User::userProfilePicture((int)$employee->id)}'
                                 class="avatar-thumbnail">
                        </div>

                        <div class="mb-1">

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {$employee->full_name}
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_sex#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {getname table=st_setting id=$employee->gender}
                            </div>


                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_birthday#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {$employee->birth_date}
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_age#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {$employee->age}
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_birth_place#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {$employee->birth_place}
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_neighborhood#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {getname table=st_neighborhood id=$employee->address_neighborhood}
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_home_no#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {$employee->address_house_number}
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_id_no#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {$employee->identity_number}
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_id_type#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {getname table=st_setting id=$employee->identity_type}
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_bank_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {$employee->bank_name}
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_iBan_number#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {$employee->bank_number}
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_social_status#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {getname table=st_setting id=$employee->social_status}
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_educational_level#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {getname table=st_setting id=$employee->educational_level}
                            </div>

                        </div>
                    </div>
                </div>

                <div class="row mt-2">
                    <hr class="mx-2 my-2">
                    <div class="col-lg-12">
                        <div>
                            <div class="flex items-center mb-4">
                                <h4 class="title">{#gnr_jobs#}:</h4>
                            </div>
                        </div>
                        <div class="mb-2">
                            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                <thead>
                                <tr>
                                    <th width="5%"></th>
                                    <th width="25%">{#p_job_title#}</th>
                                    <th width="15%">{#p_job_type#}</th>
                                    <th width="20%">{#p_direct_master_job#}</th>
                                    <th width="10%">{#p_contract_start_data#}</th>
                                    <th width="10%">{#p_contract_end_data#}</th>
                                    <th width="15%">{#p_view_job_card#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i=1}
                                {foreach $employeeJobs as $job}
                                    <tr>
                                        <td class="text-center">{$i++}</td>
                                        <td>{getname table=sh_job id=$job->sh_job_id}
                                            | {getname table=sh_unt id=$job->sh_job_unit_id}</td>
                                        <td>
                                            {if $job->directBoss['basic'] eq 1}
                                                {#gnr_primary#}
                                            {else}
                                                {#gnr_secondary#}
                                            {/if}
                                        </td>
                                        <td>
                                            {$job->directBoss->user->full_name}
                                        </td>
                                        <td>
                                            {$job->directBoss['date_of_appointment']}
                                        </td>
                                        <td>
                                            {$job->directBoss['end_date_of_appointment']}
                                            {*{getdate*}
                                                {*type=show*}
                                                {*row=$job->directBoss['end_date_of_appointment']}*}
                                        </td>
                                        <td class="text-center">
                                            {url check=0 urltype="mbutton"
                                                url_string="bsc/P051/HrReportEmployees/jobCardHTML/0/{$smarty.session.lang}/{$job->id}" style="btn btn-default shiny" text_value="<i class='fa fa-folder-o'></i>"}
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>

                        </div>
                    </div>
                </div>

                <div class="row">
                    <hr class="mx-2 my-2">

                    <div class="col-lg-12">
                        <div>
                            <div class="flex items-center mb-4">
                                <h4 class="title">{#gnr_requests#}:</h4>
                            </div>
                        </div>

                        <div class="profile-container mb-4">
                            <div class="profile-header no-shadow row">
                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 profile-stats">
                                    <div class="row">
                                        <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 stats-col">
                                            <div class="stats-value pink padding-top-50">{$request['total'] }</div>
                                            <div class="stats-title">{#p_request#}</div>
                                        </div>

                                        <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 stats-col">
                                            <div class="stats-value pink padding-top-50">{$request['requestNotSend'] }</div>
                                            <div class="stats-title">{#p_not_send_requests#}</div>
                                        </div>

                                        <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 stats-col">
                                            <div class="stats-value pink padding-top-50">{$request['requestUnderProcess'] }</div>
                                            <div class="stats-title">{#p_sent_requests#}</div>
                                        </div>

                                        <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 stats-col">
                                            <div class="stats-value pink padding-top-50">{$request['requestReachToEnd'] }</div>
                                            <div class="stats-title">{#p_finish_requests#}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        {if in_array(Setting::EMPLOYEE_DASHBOARD_PERMISSION_REQUEST,$clientConfigurationArray)}
                            <div class="mb-4">
                                <h6 class="text-muted">
                                    {#p_request_perm#}:
                                </h6>
                                <table class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="4%"></th>
                                        <th width="16%"> {#p_permission_type#} </th>
                                        <th width="20%"> {#p_job_period#} </th>
                                        <th width="25%"> {#p_permission_date#} </th>
                                        <th width="7%"> {#gnr_request_status#}    </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $hr_perm_list as $request}
                                        <tr>
                                            <td class="text-center">{$i++}</td>
                                            <td>{t v=$request->dataObject->type}</td>
                                            <td>{getname table=hr_wrdiah id=$request->dataObject->workinterval_id}</td>
                                            <td class="text-center">
                                                {getdate table=hr_perm col=date type=show row=$request->dataObject}
                                            </td>
                                            <td class="text-center">
                                                {workflow hideStatus="yes" requestId=$request->id
                                                    backTo="usr/P002/employeeRequestsDashboard/show/0/{$smarty.session.lang}"}
                                            </td>
                                        </tr>
                                        {foreachelse}
                                        <tr>
                                            <td colspan="100%"
                                                class="text-center text-muted">
                                                {#p_no_perm_requests#}
                                            </td>
                                        </tr>
                                    {/foreach}


                                    </tbody>
                                </table>
                            </div>
                        {/if}


                        {if in_array(Setting::EMPLOYEE_DASHBOARD_ADVANCE_REQUEST,$clientConfigurationArray)}
                            <div class="mb-4">
                                <h6 class="text-muted">
                                    {#p_request_adv#}
                                </h6>
                                <table class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="4%"></th>
                                        <th width="16%">{#p_adv_amount#}</th>
                                        <th width="20%">{#p_months#}</th>
                                        <th width="25%">{#gnr_date_start#}</th>
                                        <th width="7%">{#p_request_status#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $hr_advancerequest_list as $request}
                                        <tr>
                                            <td class="text-center">{$i++}</td>
                                            <td class="text-center">{$request->dataObject->amount}</td>
                                            <td class="text-center">{$request->dataObject->months_num}</td>
                                            <td class="text-center">{getdate table=hr_advancerequest col=starting_date type=show row=$request->dataObject}</td>
                                            <td class="text-center">
                                                {workflow hideStatus="yes" requestId=$request->id
                                                    backTo="usr/P002/employeeRequestsDashboard/show/0/{$smarty.session.lang}"}
                                            </td>
                                        </tr>
                                        {foreachelse}
                                        <tr>
                                            <td colspan="100%"
                                                class="text-muted text-center">
                                                {#p_no_advance_requests#}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        {/if}


                        {if in_array(Setting::EMPLOYEE_DASHBOARD_LEAVE_CREDIT_REQUEST,$clientConfigurationArray)}
                            <div class="mb-4">
                                <h6 class="text-muted">
                                    {#p_request_edit_credit_request#}:
                                </h6>
                                <table class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th width="4%"></th>
                                        <th width="16%">{#p_leave_name#}</th>
                                        <th width="20%">{#gnr_no_of_days#}</th>
                                        <th width="25%">{#gnr_date#}</th>
                                        <th width="7%">{#gnr_request_status#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $leave_credit_request_list as $request}
                                        <tr>
                                            <td class="text-center">{$i++}</td>
                                            <td>{getname table=hr_levst id=$request->dataObject->leave_id}</td>
                                            <td class="text-center">{$request->dataObject->credit}&nbsp;{#gnr_day#}</td>
                                            <td class="text-center">
                                                {getdate table=hr_creditedit col=take_effect_date type=show row=$request->dataObject}
                                            </td>
                                            <td class="text-center">
                                                {workflow hideStatus="yes" requestId=$request->id
                                                    backTo="usr/P002/employeeRequestsDashboard/show/0/{$smarty.session.lang}"}
                                            </td>
                                        </tr>
                                        {foreachelse}
                                        <tr>
                                            <td colspan="100%"
                                                class="text-muted text-center">
                                                {#p_no_leave_credit_requests#}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        {/if}



                        {if in_array(Setting::EMPLOYEE_DASHBOARD_LEAVE_REQUEST,$clientConfigurationArray)}
                            <div class="mb-4">
                                <h6 class="text-muted">
                                    {#p_request_leave#}:
                                </h6>
                                <table class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="4%"></th>
                                        <th width="16%">{#p_leave_type#}</th>
                                        <th width="20%">{#p_leave_credit#}</th>
                                        <th width="25%">{#gnr_period#}</th>
                                        <th width="7%">{#gnr_request_status#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $hr_lev_list as $request}
                                        <tr>
                                            <td class="text-center">{$i++}</td>
                                            <td>{getname table=hr_levst id=$request->dataObject->levst_id}</td>
                                            <td class="text-center">{$request->dataObject->intrvl_lngth} {#gnr_day#}</td>
                                            <td class="text-center">
                                                {getdate table=hr_lev col=start_date type=show row=$request->dataObject}
                                                -
                                                {getdate table=hr_lev col=end_date type=show row=$request->dataObject}
                                            </td>
                                            <td class="text-center">
                                                {workflow hideStatus="yes" requestId=$request->id
                                                backTo="usr/P002/employeeRequestsDashboard/show/0/{$smarty.session.lang}"}
                                            </td>
                                        </tr>
                                        {foreachelse}
                                        <tr>
                                            <td colspan="100%"
                                                class="text-muted text-center">
                                                {#p_no_leave_requests#}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        {/if}


                        {if in_array(Setting::EMPLOYEE_DASHBOARD_RETREAT_LEAVE_REQUEST,$clientConfigurationArray)}
                            <div class="mb-4">
                                <h6 class="text-muted">
                                    {#p_request_retreat_Leave#}:
                                </h6>
                                <table class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%"></th>
                                        <th width="25%">الاجازة</th>
                                        <th width="25%">الفترة</th>
                                        <th width="10%">عدد الايام المتراجع عنها</th>
                                        <th width="25%">التاريخ</th>
                                        <th width="10%">الحاله</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}

                                    {foreach $hr_retreat_leave as $request}
                                        <tr>
                                            <td class="text-center">{$i++}</td>
                                            <td>{getname table=hr_levst
                                                    id=$request->dataObject->leaveRequestEntity->hr_lev_levst_id}</td>
                                            <td class="text-center">
                                                {getdate table=hr_lev
                                                    col=hr_lev_start_date type=show row=$request->dataObject->leaveRequestEntity}
                                                -
                                                {getdate table=hr_lev col=hr_lev_end_date type=show row=$request->dataObject->leaveRequestEntity}
                                            </td>
                                            <td class="text-center">{$request->dataObject->retreatDaysNum}
                                                &nbsp;{#gnr_day#}</td>
                                            <td class="text-center">
                                                {getdate table=hr_retreat_leave col=created_date type=show row=$request->dataObject}
                                            </td>
                                            <td class="text-center">
                                                {workflow hideStatus="yes"
                                                    requestId=$request->id
                                                    backTo="usr/P002/employeeRequestsDashboard/show/0/{$smarty.session.lang}"}
                                            </td>

                                        </tr>
                                        {foreachelse}
                                        <tr>
                                            <td colspan="100%"
                                                class="text-center text-muted"
                                            >
                                                {#p_no_leave_retrive_requests#}
                                            </td>
                                        </tr>
                                    {/foreach}

                                    </tbody>
                                </table>
                            </div>
                        {/if}


                        {if in_array(Setting::EMPLOYEE_DASHBOARD_ADDITIONAL_WORK_REQUEST,$clientConfigurationArray)}
                            <div class="mb-4">
                                <h6 class="text-muted">
                                    {#p_request_out_work#}:
                                </h6>
                                <table class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="4%"></th>
                                        <th width="16%">{#p_emp_name#}</th>
                                        <th width="20%"> {#p_outwork_period#} </th>
                                        <th width="25%">{#p_daily_rate#}</th>
                                        <th width="7%"> {#gnr_request_status#} </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $hr_otwrk_list as $request}
                                        <tr>
                                            <td class="text-center">{$i++}</td>
                                            <td>{$request->dataObject->userObject->full_name}</td>
                                            <td class="text-center">
                                                {getdate table=hr_otwrk col=date type=show row=$request->dataObject}
                                                - {getdate table=hr_otwrk col=end_date type=show row=$request->dataObject}
                                            </td>
                                            <td class="text-center">
                                                {if !is_null($request->dataObject->rate)} {$request->dataObject->rate} {#p_hour#} {/if}
                                            </td>
                                            <td class="text-center">
                                                {workflow hideStatus="yes"
                                                    requestId=$request->id backTo="usr/P002/employeeRequestsDashboard/show/0/{$smarty.session.lang}"}
                                            </td>
                                        </tr>
                                        {foreachelse}
                                        <tr>
                                            <td colspan="100%"
                                                class="text-center text-muted">
                                                {#p_no_out_work_requests#}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>

                                </table>
                            </div>
                        {/if}


                        {if in_array(Setting::EMPLOYEE_DASHBOARD_ADDITIONAL_WORK_REQUEST,$clientConfigurationArray)}
                            <div class="mb-4">
                                <h6 class="text-muted">
                                    {#p_request_out_work_salary#}:
                                </h6>

                                <table class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%"></th>
                                        <th width="35%"> {#p_assign_from#} </th>
                                        <th width="35%"> {#p_outwork_period#} </th>
                                        <th width="15%">{#p_daily_rate#}</th>
                                        <th width="10%"> {#gnr_request_status#} </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $hr_outworkfees_list as $request}
                                        <tr>
                                            <td class="text-center">{$i++}</td>
                                            <td class="text-center">{getname table=sh_user id=$request->dataObject->outWorkRequest->user_id}</td>
                                            <td class="text-center">
                                                {getdate table=hr_otwrk col=date type=show row=$request->dataObject->outWorkRequest}
                                                - {getdate table=hr_otwrk col=end_date type=show row=$request->dataObject->outWorkRequest}
                                            </td>
                                            <td class="text-center">
                                                {$request->dataObject->outWorkRequest->rate} {#p_hours#}
                                            </td>
                                            <td class="text-center">
                                                {workflow hideStatus="yes" requestId=$request->id
                                                    backTo="usr/P002/employeeRequestsDashboard/show/0/{$smarty.session.lang}"}
                                            </td>
                                        </tr>
                                        {foreachelse}
                                        <tr>
                                            <td colspan="100%"
                                                class="text-muted text-center">
                                                {#p_no_outwork_fees_requests#}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        {/if}


                        {if in_array(Setting::EMPLOYEE_DASHBOARD_MANDATE_REQUEST,$clientConfigurationArray)}
                            <div class="mb-4">
                                <h6 class="text-muted">
                                    {#p_request_mandate#}:
                                </h6>
                                <table class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%"></th>
                                        <th width="15%">{#p_emp_name#}</th>
                                        <th width="10%"> {#gnr_city#} </th>
                                        <th width="5%"> {#p_mandate_days_number#} </th>
                                        <th width="25%"> {#p_mandate_period#} </th>
                                        <th width="30%">{#p_mandate_reason#}</th>
                                        <th width="10%"> {#gnr_request_status#} </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $hr_mndt_list as $request}
                                        <tr>
                                            <td class="text-center">{$i++}</td>
                                            <td>{$request->dataObject->userObject->full_name}</td>
                                            <td>{$request->dataObject->city}</td>
                                            <td>{$request->dataObject->days_number}</td>
                                            <td class="text-center">{getdate table=hr_mndt col=start_date type=show row=$request->dataObject}
                                                - {getdate table=hr_mndt col=end_date type=show row=$request->dataObject}</td>
                                            <td>
                                                {$request->dataObject->reason}
                                            </td>
                                            <td class="text-center">
                                                {workflow hideStatus="yes" requestId=$request->id
                                                    backTo="usr/P002/employeeRequestsDashboard/show/0/{$smarty.session.lang}"}
                                            </td>
                                        </tr>
                                        {foreachelse}
                                        <tr>
                                            <td colspan="100%"
                                                class="text-muted text-center">
                                                {#p_no_mandate_requests#}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        {/if}


                        {if in_array(Setting::EMPLOYEE_DASHBOARD_MANDATE_REQUEST,$clientConfigurationArray)}
                            <div>
                                <h6 class="text-muted">
                                    {#p_request_mandate_salary#}:
                                </h6>
                                <table class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%"></th>
                                        <th width="20%"> {#p_assign_from#} </th>
                                        <th width="10%"> {#gnr_city#} </th>
                                        <th width="25%"> {#p_outwork_period#} </th>
                                        <th width="30%">{#p_outwork_reason#}</th>
                                        <th width="10%"> {#gnr_request_status#} </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $hr_mndtfees_list as $request}
                                        <tr>
                                            <td class="text-center">{$i++}</td>
                                            <td class="text-center">
                                                {getname table=sh_user id=$request->dataObject->mandateRequest->user_id}
                                            </td>
                                            <td>{$request->dataObject->mandateRequest->city}</td>
                                            <td class="text-center">
                                                {getdate table=hr_mndt col=start_date type=show row=$request->dataObject->mandateRequest}
                                                - {getdate table=hr_mndt col=end_date type=show row=$request->dataObject->mandateRequest}
                                            </td>
                                            <td>
                                                {$request->dataObject->mandateRequest->reason}
                                            </td>

                                            <td class="text-center">
                                                {workflow hideStatus="yes" requestId=$request->id
                                                    backTo="usr/P002/employeeRequestsDashboard/show/0/{$smarty.session.lang}"}
                                            </td>
                                        </tr>
                                        {foreachelse}
                                        <tr>
                                            <td colspan="100%"
                                                class="text-muted text-center">
                                                {#p_no_mndtfees_requests#}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        {/if}


                        {if in_array(Setting::EMPLOYEE_DASHBOARD_FINEXCH_REQUEST,$clientConfigurationArray)}
                            <div class="mb-4">
                                <h6 class="text-muted">
                                    {#p_finexch_request#}:
                                </h6>
                                <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                    <thead>
                                    <tr>
                                        <th width="5%"
                                        ></th>
                                        <th width="10%">{#gnr_date#}</th>
                                        <th width="25%"
                                        >{#gnr_beneficiary#}</th>
                                        <th width="10%"
                                        >{#p_amount_as_number#}</th>
                                        <th width="10%"
                                        >{#gnr_position_procedure#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $fin_exch_request_list as $request}
                                        <tr>
                                            <td class="text-center">{$i++}</td>
                                            <td class="text-center">
                                                {getdate table=wf_request col=created_date type=showauto
                                                    row=$request->created_date}</td>
                                            <td>{$request->dataObject->beneficiary}</td>
                                            <td class="text-center">{$request->dataObject->amount_number}</td>
                                            <td class="text-center">
                                                {workflow hideStatus="yes" requestId=$request->id
                                                    backTo="usr/P002/employeeRequestsDashboard/show/0/{$smarty.session.lang}"}
                                            </td>
                                        </tr>
                                        {foreachelse}
                                        <tr>
                                            <td colspan="100%"
                                                class="text-muted text-centered">
                                                {#p_no_finexch_requests#}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        {/if}


                    </div>
                </div>
                <div class="row mt-2">
                    <hr class="mx-2 my-2">

                    <div class="col-lg-12">
                        <div>
                            <div class="flex items-center mb-4">
                                <h4 class="title">{#gnr_Accountabilites#}:</h4>
                            </div>
                        </div>

                        <div class="mb-2">
                            <h6 class="text-muted">
                                {#p_attandace_accountabilites#}:
                            </h6>
                            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                <thead>
                                <tr>
                                    <th width="4%"
                                    ></th>
                                    <th width="16%"
                                    >{#gnr_employee_name#}</th>
                                    <th width="20%">{#gnr_duration#}</th>
                                    <th width="25%">{#gnr_period#}</th>
                                    <th width="7%">{#gnr_view#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i=1}
                                {foreach $attendanceRequests as $request}
                                    <tr>
                                        <td class="text-center">{$i++}</td>
                                        <td class="text-right">{getname table=sh_user id=$request->user_id}</td>
                                        <td class="text-center">{$request->dataObject->duration}&nbsp;{#gnr_day#}</td>
                                        <td class="text-center">
                                            {getdate type=show row=$request->dataObject col=start_date}
                                            &nbsp;&raquo;&nbsp;
                                            {getdate type=show row=$request->dataObject col=end_date}
                                            &nbsp;&raquo;&nbsp;
                                        </td>

                                        <td class="text-center">
                                            {workflow hideStatus="yes" requestId=$request->id
                                                hideStatus="yes" backTo="bsc/P051/PersonnelArchive/show/0/{$smarty.session.lang}"}
                                        </td>
                                    </tr>
                                    {foreachelse}
                                    <tr>
                                        <td colspan="100%"
                                            class="text-center text-muted">
                                            {#p_no_attandace_accountabilites#}
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>
                        </div>

                        <div class="mb-2">
                            <h6 class="text-muted">
                                {#p_latency_accountabilites#}:
                            </h6>
                            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                <thead>
                                <tr>
                                    <th width="4%"></th>
                                    <th width="16%">{#gnr_employee_name#}</th>
                                    <th width="20%">{#gnr_duration#}</th>
                                    <th width="25%">{#gnr_period#}</th>
                                    <th width="7%">{#gnr_view#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i=1}
                                {foreach $latenciesRequests as $request}
                                    <tr>
                                        <td class="text-center">{$i++}</td>
                                        <td class="text-right">{getname table=sh_user id=$request->dataObject->user_id}</td>
                                        <td class="text-center">{$request->dataObject->duration}&nbsp;{#gnr_day#}</td>
                                        <td class="text-center">
                                            {$request->dataObject->start_date}
                                            &nbsp;&raquo;&nbsp;
                                            {$request->dataObject->end_date}
                                            &nbsp;&raquo;&nbsp;
                                        </td>
                                        <td class="text-center">
                                            {workflow requestId=$request->id
                                                hideStatus="yes" backTo="bsc/P051/PersonnelArchive/show/0/{$smarty.session.lang}"}
                                        </td>
                                    </tr>
                                    {foreachelse}
                                    <tr>
                                        <td colspan="100%"
                                            class="text-center text-muted">
                                            {#p_no_latency_accountabilites#}
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row mt-2">

                    <hr class="mx-2 my-2">

                    <div class="col-lg-12">
                        <div>
                            <div class="flex items-center mb-4">
                                <h4 class="title">{#gnr_vacations#}:</h4>
                            </div>
                        </div>
                        <div class="mb-2">
                            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                <thead>
                                <tr>
                                    <th width="5%"></th>
                                    <th width="45%">{#p_vacation_name#}</th>
                                    <th width="40%">{#p_yearly_credit#}</th>
                                    <th width="10%">{#p_avalible_credit#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i = 1}
                                {foreach $vacations as $vacation}
                                    <tr>
                                        <td class="text-center">{$i++}</td>
                                        <td class="text-center">
                                            {getname table=hr_levst id=$vacation->leaveType->id}
                                        </td>
                                        <td class="text-center">
                                            {$vacation->credit}
                                        </td>
                                        <td class="text-center">
                                            {leaveCredit LeaveAllowedObject=$vacation}
                                        </td>
                                    </tr>
                                    {foreachelse}
                                    <tr>
                                        <td colspan="100%"
                                            class="text-center text-muted">
                                            {#p_no_vacations#}
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row mt-2">

                    <hr class="mx-2 my-2">

                    <div class="col-lg-12">
                        <div>
                            <div class="flex items-center mb-4">
                                <h4 class="title">{#gnr_committees#}:</h4>
                            </div>
                        </div>

                        <div class="mb-2">
                            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                <thead>
                                <tr>
                                    <th width="5%"></th>
                                    <th width="25%">{#p_commitee_name#}</th>
                                    <th width="40%">{#p_committee_purpose#}</th>
                                    <th width="20%">{#p_committee_roles#}</th>
                                    <th width="10%">{#gnr_view#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i=1}
                                {foreach $employeeCommittees as $committee}
                                    <tr>
                                        <td class="text-center">{$i++}</td>
                                        <td>{$committee->name}</td>
                                        <td>{$committee->purpose}</td>
                                        <td>
                                            <table>
                                                <tbody>
                                                {$j=1}
                                                {foreach $committee->memberships as $membership}
                                                    <tr>
                                                        <td>{$j++} - {$membership->role}</td>
                                                    </tr>
                                                {/foreach}

                                                </tbody>
                                            </table>
                                        </td>
                                        <td class="text-center">
                                            {url check=0 urltype="mbutton" opr_code='committeebuilding'
                                                url_string="gnr/X000/committee/browse/0/{$smarty.session.lang}/{$committee->id}"
                                                text_value="<i class='fa fa-folder-o'></i>"}
                                        </td>
                                    </tr>
                                    {foreachelse}
                                    <tr>
                                        <td colspan="100%"
                                            class="text-center text-muted">
                                            {#p_no_committees#}
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        {/if}
    </div>
{/block}
