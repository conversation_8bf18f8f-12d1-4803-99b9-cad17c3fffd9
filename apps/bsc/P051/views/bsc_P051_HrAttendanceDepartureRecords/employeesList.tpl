{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_employees_list#}: {$modalTitle}</h4>
    </div>
    <div class="modal-body">

        {if count($employees)}
            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">

                <thead>
                <tr>
                    <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                    <th style="background-color: #A0D468 !important;" width="25%">{#gnr_name#}</th>
                    <th style="background-color: #A0D468 !important;" width="10%">{#gnr_job#}</th>
                    <th style="background-color: #A0D468 !important;" width="10%">{#gnr_unit#}</th>
                    <th style="background-color: #A0D468 !important;" width="15%">{#gnr_work#}</th>
                    <th style="background-color: #A0D468 !important;" width="15%">{#gnr_finger_print_device#}</th>
                    <th style="background-color: #A0D468 !important;" width="10%">{#gnr_view#}</th>
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $employees as $employee}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td>{$employee->sh_user_full_name}</td>
                        <td>{$employee->sh_job_name}</td>
                        <td>{$employee->sh_unt_name}</td>
                        <td>{$employee->hr_doam_name}</td>
                        <td>{$employee->fp_dev_name}</td>
                        <td align="center">
                            {url check=0 urltype="button" opr_code='HrAttendanceDepartureRecords' url_string="bsc/P051/HrAttendanceDepartureRecords/records/0/{$smarty.session.lang}/menu/{$employee->hr_doam_id}/{$employee->sh_uao_id}" text_value="{#gnr_view#}"}
                        </td>
                    </tr>
                {/foreach}
                </tbody>
            </table>
        {else}
            <div class="alert alert-warning">{#gnr_no_records#}</div>
        {/if}

    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}