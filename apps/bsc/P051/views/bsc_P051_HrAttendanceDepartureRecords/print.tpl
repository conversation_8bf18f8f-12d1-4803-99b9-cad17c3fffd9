{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}

    <div class="row">
        <h4 class="text-center well mb-0">{#p_human_resource_attendance_departure_records#}</h4>
    </div>

    <div class="horizontal-space"></div>

    <div class="row">

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#p_employee_name#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">{$employee->userObject->full_name}</div>

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#gnr_job#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">{$employee->jobObject->sh_job_name}</div>

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#gnr_unit#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">{getname table=sh_unt id=$employee->job_unt_id}</div>

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#gnr_work#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">{getname table=hr_doam id=$employee->att_doam_id}</div>

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#gnr_finger_print_device#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">{getname table=fp_dev id=$employee->att_device_id}</div>

    </div>

    <div class="horizontal-space"></div>

    <div class="row">

        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table searchable">
            <thead>
            <tr>
                <th width="5%">&nbsp;</th>
                <th width="10%">{#gnr_date#}</th>
                <th width="15%">{#p_original_finger_print_records#}</th>
                <th width="23%">{#p_edited_finger_print_records#}</th>
                <th width="8%">{#p_work_duration#}</th>
                <th width="8%">{#p_delay_duration#}</th>
                <th width="8%">{#p_extra_duration#}</th>
                <th width="8%">{#p_absence_duration#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $fingerprintRecords as $day}

                <tr style="{if $day->workday_or_weekend eq 0} background-color: #d4d4d4;{/if}">
                    <td align="center">{$i++}</td>
                    <td>
                    <span >
                    {t v=$day->day_in_week}
                        &nbsp;&raquo;&nbsp;
                        {$day->date}
                    </span>
                    </td>
                    <td>{implode('&nbsp;&raquo;&nbsp;',$day->fingerPrintsOrigin)}</td>
                    <td>
                        {if $day->workday_or_weekend eq 1}
                            {if $day->correctness_status eq 0}<i class="fa fa-circle red"></i>{/if}
                            {if $day->correctness_status eq 1}<i class="fa fa-circle green"></i>{/if}
                            &nbsp;
                            <span style="color: {$day->editingStatus};">{implode('&nbsp;&raquo;&nbsp;',$day->fingerPrintsEdited)}</span>
                        {/if}
                    </td>
                    <td align="center">{$day->work_duration|number_format:2:".":","}</td>
                    <td align="center" {if $day->delay_duration} style="color: blue" {/if}>{$day->delay_duration|number_format:2:".":","}</td>
                    <td align="center" {if $day->extra_duration} style="color: blue" {/if}>{$day->extra_duration|number_format:2:".":","}</td>
                    <td align="center" {if $day->absense_duration} style="color: blue" {/if}>{$day->absense_duration|number_format:2:".":","}</td>
                </tr>

                {assign var="WorkSum" value="`$WorkSum+$day->work_duration`"}
                {assign var="DelaySum" value="`$DelaySum+$day->delay_duration`"}
                {assign var="ExtraSum" value="`$ExtraSum+$day->extra_duration`"}
                {assign var="AbsenceSum" value="`$AbsenceSum+$day->absense_duration`"}

            {/foreach}

            </tbody>

            <tfoot>
            <tr style="background-color: gray; color: white">
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td align="center">{$WorkSum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
                <td align="center">{$DelaySum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
                <td align="center">{$ExtraSum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
                <td align="center">{$AbsenceSum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
            </tr>
            </tfoot>

            {assign var="WorkSum" value=0}
            {assign var="DelaySum" value=0}
            {assign var="ExtraSum" value=0}
            {assign var="AbsenceSum" value=0}
        </table>

    </div>
{/block}