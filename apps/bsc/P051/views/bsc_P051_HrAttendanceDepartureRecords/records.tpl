{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_header}
    test
{/block}

{block name=page_body}

    <div class="widget radius-bordered">

        <div class="widget-header bg-darkgray">
            <span class="widget-caption">

            </span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                    <i class="fa fa-minus white "></i>
                </a>
            </div>
        </div>

        <div class="widget-body">
            <div class="row">
                <div class="col-lg-3 text-center">
                    <img src="/framework/core/functions/image.php?image=&width=100&height=150" class="img-fluid img-responsive rounded" >
                </div>
                <div class="col-lg-9">
                    <form action="{url urltype=path url_string="bsc/P051/HrAttendanceDepartureRecords/records/0/{$smarty.session.lang}/save_session/{$doam->id}/{$employee->id}"}" method="post">
                        <div class="row">
                            <div class="col-lg-12">

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$employee->userObject->full_name}</div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_work#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                    {getname table=hr_doam id=$employee->att_doam_id}
                                    {url check=0 urltype="mbutton" opr_code='hrdoamwrdiah' url_string="gnr/X000/reportView/reportView/0/ar/doamViewIntervalsHTML/&p[doamId]={$employee->att_doam_id}" text_value="<i class='fa fa-question-circle'></i>" modal="modal"}
                                </div>


                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_from#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                    <div class="control-group">
                                        {getdate table=fp_day col=from_date type=report row={$smarty.post['from_date']|default:''}}
                                    </div>
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_to#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                    {getdate table=fp_day col=to_date type=report row={$smarty.post['to_date']|default:''}}
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-default shiny">{#gnr_view#}</button></div>

                            </div>

                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>

    {if count($fingerprintRecords)}
        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table searchable">
        <thead>
        <tr>
            <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
            <th style="background-color: #A0D468 !important;" width="10%">{#gnr_date#}</th>
            <th style="background-color: #A0D468 !important;" width="15%">{#p_original_finger_print_records#}</th>
            <th style="background-color: #A0D468 !important;" width="23%">{#p_edited_finger_print_records#}</th>
            <th style="background-color: #A0D468 !important;" width="8%">{#p_work_duration#}</th>
            <th style="background-color: #A0D468 !important;" width="8%">{#p_delay_duration#}</th>
            <th style="background-color: #A0D468 !important;" width="8%">{#p_extra_duration#}</th>
            <th style="background-color: #A0D468 !important;" width="8%">{#p_absence_duration#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $fingerprintRecords as $day}

            <tr style="{if $day->workday_or_weekend eq 0} background-color: #d4d4d4;{/if}">
                <td align="center">{$i++}</td>
                <td>
                    <span >
                    {t v=$day->day_in_week}
                        &nbsp;&raquo;&nbsp;
                        {$day->date}
                    </span>
                </td>
                <td>{implode('&nbsp;&raquo;&nbsp;',$day->fingerPrintsOrigin)}</td>
                <td>
                    {if $day->workday_or_weekend eq 1}
                        {if $day->correctness_status eq 0}<i class="fa fa-circle red"></i>{/if}
                        {if $day->correctness_status eq 1}<i class="fa fa-circle green"></i>{/if}
                        &nbsp;
                        <span style="color: {$day->editingStatus};">{implode('&nbsp;&raquo;&nbsp;',$day->fingerPrintsEdited)}</span>
                    {/if}
                </td>
                <td align="center">{$day->work_duration|number_format:2:".":","}</td>
                <td align="center" {if $day->delay_duration} style="color: blue" {/if}>{$day->delay_duration|number_format:2:".":","}</td>
                <td align="center" {if $day->extra_duration} style="color: blue" {/if}>{$day->extra_duration|number_format:2:".":","}</td>
                <td align="center" {if $day->absense_duration} style="color: blue" {/if}>{$day->absense_duration|number_format:2:".":","}</td>
            </tr>

            {assign var="WorkSum" value="`$WorkSum+$day->work_duration`"}
            {assign var="DelaySum" value="`$DelaySum+$day->delay_duration`"}
            {assign var="ExtraSum" value="`$ExtraSum+$day->extra_duration`"}
            {assign var="AbsenceSum" value="`$AbsenceSum+$day->absense_duration`"}

        {/foreach}

        </tbody>

        <tfoot>
        <tr style="background-color: gray; color: white">
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td align="center">{$WorkSum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
            <td align="center">{$DelaySum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
            <td align="center">{$ExtraSum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
            <td align="center">{$AbsenceSum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
        </tr>
        </tfoot>

        {assign var="WorkSum" value=0}
        {assign var="DelaySum" value=0}
        {assign var="ExtraSum" value=0}
        {assign var="AbsenceSum" value=0}
    </table>
    {/if}
{/block}

{block name=back}{url urltype="path" url_string="bsc/P051/HrAttendanceDepartureRecords/show/0/{$smarty.session.lang}"}{/block}