{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <script src="/templates/assets/js/Chart.bundle.min.js"></script>
{/block}
{block name=page_body}
    <div class="row">
        <div class="col-sm-3">
            <div class="databox radius-bordered databox-shadowed">
                <div class="databox-right bg-gray">
                    <div class="databox-icon">
                        <i class="fa fa-paperclip"></i>
                    </div>
                </div>
                <div class="databox-left text-align-center bg-white text-dark">
                    <span class="databox-text">{$employeesRequestsTotal + $generalHrRequestsTotal}</span>
                    <span class="databox-text">{#gnr_request#}</span>
                </div>
            </div>
        </div>

        <div class="col-sm-3">
            <div class="databox radius-bordered databox-shadowed">
                <div class="databox-right bg-red">
                    <div class="databox-icon">
                        <i class="fa fa-times-circle-o"></i>
                    </div>
                </div>
                <div class="databox-left bg-white text-dark text-align-center">
                    <span class="databox-text">{$unsentEmployeesRequestsCount +$unsentGeneralRequestsCount }</span>
                    <span class="databox-text">{#gnr_request#} {#p_requests_un_sent#}</span>
                </div>
            </div>
        </div>

        <div class="col-sm-3">
            <div class="databox radius-bordered databox-shadowed">
                <div class="databox-right bg-yellow">
                    <div class="databox-icon">
                        <i class="fa fa-spinner"></i>
                    </div>
                </div>
                <div class="databox-left bg-white text-dark text-align-center">
                    <span class="databox-text">{$underProcessEmployeesRequestsCount +$underProcessGeneralRequestsCount}</span>
                    <span class="databox-text">{#gnr_request#} {#p_requests_under_process#}</span>
                </div>
            </div>
        </div>

        <div class="col-sm-3">
            <div class="databox radius-bordered databox-shadowed">
                <div class="databox-right bg-green">
                    <div class="databox-icon">
                        <i class="fa fa-check-circle-o"></i>
                    </div>
                </div>
                <div class="databox-left text-align-center bg-white text-dark">
                    <span class="databox-text">{$sentEmployeesRequestsCount +$sentGeneralRequestsCount}</span>
                    <span class="databox-text">{#gnr_request#} {#p_requests_sent#}</span>
                </div>
            </div>
        </div>

    </div>
    <div class="row">
        <div class="col-lg-12">

            <div class="widget  col-sm-6 lg:pr-12-px text-center">
                <div class="widget-body min-h-35 bordered-left bordered-green">
                    <div class="text-center" style="width: 400px;height: 400px;">
                    <chart-view
                            :graph="'g6'"
                            :type="'pie'"
                            :lables= "['طلب إجازه','طلب سلفة','طلب إنتداب','طلب اجرة إنتداب','طلب عمل إضافي','طلب اجرة عمل إضافي','طلب إذن']"
                            :colors="['#1abc9c', '#34495e','#34ff00','#55ee00','#ffee00','#0f0fee','#ee00ff']"
                            :values="{json_encode($graphData)}"
                            title="الطلبات"></chart-view>
                    </div>
                </div>
            </div>


            <div class="widget  col-sm-6 lg:pr-12-px">
                <div class="widget-body min-h-35 bordered-left bordered-green">
                    <h6 class="text-muted">
                        {#p_dowams#} :
                    </h6>
                    <table class="table table-striped">
                        <tbody>
                        <tr>
                            <th></th>
                            <th class="text-muted">{#p_work_days#}</th>
                            <th class="text-muted">{#P_weekends#}</th>
                        </tr>
                        {foreach $dowams as $dowam}
                            <tr>
                                <td>
                                    {url urltype="alink" url_string="bsc/P051/hrdoamwrdiah/show/0/ar/save_session/{$dowam->id}" text_value="{$dowam->name}"}
                                </td>
                                <td>
                                    {foreach $weekDays as $day}
                                        {if in_array($day->id,explode(',',$dowam->workdays))}
                                            {$day->translatedName}
                                        {/if}
                                    {/foreach}
                                </td>
                                <td>

                                    {foreach $weekDays as $day}
                                        {if in_array($day->id,explode(',',$dowam->weekend))}
                                            {$day->translatedName}
                                        {/if}
                                    {/foreach}

                                </td>
                            </tr>
                            {foreachelse}
                            <tr>
                                <td class="text-muted items-center"
                                    colspan="100%">
                                    {#p_there_is_no_dowams#}
                                </td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>

                </div>
                {$countries_keys}
            </div>

        </div>
    </div>

    <div class="widget mx-auto row mb-2">
        <div class=" col-lg-12">
            <div class="widget-body bordered-right bordered-blue col-lg-4 col-md-6 mb-2 col-sm-12 mb-2">
                <chart-view
                        :graph="'g1'"
                        :type="'pie'"
                        :lables= "['اخري' ,'السعودية']"
                        :colors="['#1abc9c', '#34495e']"
                        :values="{json_encode($countries_values)}"
                        title=" نسبة الموظفين السعوديين مقارنة مع باقي الدول"></chart-view>
            </div>
            <div class="widget-body bordered-right bordered-blue col-lg-4 col-md-6 mb-2 col-sm-12 mb-2">
                <chart-view
                        :graph="'g2'"
                        :type="'pie'"
                        :lables="['المشغولة', 'الشاغرة']"
                        :colors="['#4b77a9', '#2dc3e8']"
                        :values="{json_encode($jobs_values)}"
                        title=" نسبة الوظائف المشغولة والشاغرة"></chart-view>
            </div>
            <div class="widget-body col-lg-4 col-md-6 mb-2 col-sm-12 mb-2">
                <chart-view
                        :graph="'g3'"
                        :type="'pie'"
                        :lables="['ذكر', 'انثى']"
                        :colors="['#5f255f', '#bc5456']"
                        :values= "{json_encode($gender_values)}"
                        title="نسبة الذكور والاناث"></chart-view>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="widget col-sm-6 lg:pl-12-px">
                <div class="widget-body bordered-left bordered-blue">
                    <h6 class="text-muted">
                        {#p_hr_requests#} :
                    </h6>
                    <table class="table table-striped">
                        <tbody>
                        <tr>
                            <th class="text-muted">{#p_requests_type#}</th>
                            <th class="text-muted text-center">{#p_requests_un_sent#}</th>
                            <th class="text-muted text-center">{#p_requests_under_process#}</th>
                            <th class="text-muted text-center">{#p_requests_sent#}</th>
                            <th class="text-muted text-center">{#p_requests_total#}</th>
                        </tr>
                        {foreach $generalHrRequestsDetails as $key => $requestsTypeData}
                            <tr>
                                <td>
                                    {Translation::translate($smarty.session.program,$key)}
                                </td>
                                <td class="text-center text-muted text-lg">
                                    {$requestsTypeData->where(Request::SEND_STATUS, Request::REQUEST_IS_NOT_SEND)->count()}
                                </td>
                                <td class="text-center text-muted text-lg">
                                    {$requestsTypeData->where(Request::SEND_STATUS, Request::REQUEST_IS_UNDER_PROCESS)->count()}
                                </td>
                                <td class="text-center text-muted text-lg">
                                    {$requestsTypeData->where(Request::SEND_STATUS, Request::REQUEST_REACH_END)->count()}
                                </td>
                                <td class="text-center">
                                    {$requestsTypeData->count()}
                                </td>
                            </tr>
                            {foreachelse}
                            <tr>
                                <td class="text-muted text-center"
                                    colspan="100%">
                                    {#no_recent_unimported_transactions#}
                                </td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>


            <div class="widget col-sm-6 lg:pr-12-px">
                <div class="widget-body bordered-left bordered-blue">
                    <h6 class="text-muted">
                        {#p_employees_requests#} :
                    </h6>
                    <table class="table table-striped">
                        <tbody>
                        <tr>
                            <th class="text-muted">{#p_requests_type#}</th>
                            <th class="text-muted text-center">{#p_requests_un_sent#}</th>
                            <th class="text-muted text-center">{#p_requests_under_process#}</th>
                            <th class="text-muted text-center">{#p_requests_sent#}</th>
                            <th class="text-muted text-center">{#p_requests_total#}</th>
                        </tr>
                        {foreach $employeesRequestsDetails as $key => $requestsTypeData}
                            <tr>
                                <td>
                                    {Translation::translate($smarty.session.program,$key)}
                                </td>
                                <td class="text-center text-muted text-lg">
                                    {$requestsTypeData->where(Request::SEND_STATUS, Request::REQUEST_IS_NOT_SEND)->count()}
                                </td>
                                <td class="text-center text-muted text-lg">
                                    {$requestsTypeData->where(Request::SEND_STATUS, Request::REQUEST_IS_UNDER_PROCESS)->count()}
                                </td>
                                <td class="text-center text-muted text-lg">
                                    {$requestsTypeData->where(Request::SEND_STATUS, Request::REQUEST_REACH_END)->count()}
                                </td>
                                <td class="text-center">
                                    {$requestsTypeData->count()}
                                </td>
                            </tr>
                            {foreachelse}
                            <tr>
                                <td class="text-muted text-center"
                                    colspan="100%">
                                    {#no_recent_unimported_transactions#}
                                </td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
{/block}

