{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}
    <div class="row">
        <h4 class="text-center well mb-0">{#p_human_resource_opportunities_occupied_job#}</h4>
    </div>
    <div class="row">

        <div class="col-lg-12">

            {$unitsCounter=1}
            <div class="mt-2">
                <h5 class="row-title before-blue">
                    <i class="glyphicon glyphicon-arrow-left blue"></i>
                    <span class="badge badge-secondary badge-square">{$unitsCounter++}</span>
                    {$unit->sh_unt_name}
                </h5>
            </div>

            <div class="well">

                <div class="row">
                    <div class="col-lg-6">

                        <table class="table table-striped table-bordered">
                            <tbody>
                            <tr style="background-color: #A0D468 !important;">
                                <th>{#p_total_count_of_jobs_in_units#}</th>
                                <th>{#p_total_digits#}</th>
                                <th>{#p_count_of_occupied_jobs#}</th>
                                <th>{#p_count_of_opportunities_jobs#}</th>
                            </tr>
                            <tr>
                                <td class="text-muted">{$unit->jobsCount}</td>
                                <td class="text-muted">{count($unit->occupiedJobs) + count($unit->vacantJobs)}</td>
                                <td class="text-muted">{count($unit->occupiedJobs)}</td>
                                <td class="text-muted">{count($unit->vacantJobs)}</td>
                            </tr>

                            </tbody>
                        </table>

                    </div>

                </div>

                <div class="horizontal-space"></div>

                <h5 class="row-title before-gray">{#p_couupied_jobs#}</h5>
                <table class="table table-striped table-bordered">
                    <tbody>
                    <tr style="background-color: #A0D468 !important;">
                        <th>#</th>
                        <th>{#gnr_job#}</th>
                        <th>{#gnr_employee#}</th>
                        <th>{#p_job_type#}</th>
                    </tr>
                    {$i=1}
                    {foreach $unit->occupiedJobs as $vacant}
                        <tr>
                            <td class="text-muted">{$i++}</td>
                            <td class="text-muted">{$vacant->sh_job_name}</td>
                            <td class="text-muted">
                                {$vacant->sh_user_full_name}
                            </td>
                            <td class="text-muted">

                                {if $vacant->sh_uao_basic eq 1}
                                    <span>{#gnr_primary#}</span>
                                {/if}

                                {if $vacant->sh_uao_basic eq 0}
                                    <span>{#gnr_secondary#}</span>
                                {/if}
                            </td>
                        </tr>
                        {foreachelse}
                        <tr>
                            <td class="text-muted text-center"
                                colspan="100%">
                                <div>
                                    {#gnr_no_records#}
                                </div>
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>

                <div class="horizontal-space"></div>

                <h5 class="row-title before-gray">{#p_opportunities_jobs#}</h5>
                <table class="table table-striped table-bordered">
                    <tbody>
                    <tr style="background-color: #A0D468 !important;">
                        <th width="5%">&nbsp;</th>
                        <th width="65%">{#gnr_job#}</th>
                        <th width="15%">{#gnr_code#}</th>
                        <th width="15%">{#p_job_type#}</th>
                    </tr>
                    {$i=1}
                    {foreach $unit->vacantJobs as $item}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>{$item->sh_job_name}</td>
                            <td>{$item->sh_uao_job_code}</td>
                            <td class="text-muted">
                                {if $vacant->sh_uao_basic eq 1}
                                    <span>{#gnr_primary#}</span>
                                {/if}

                                {if $vacant->sh_uao_basic eq 0}
                                    <span>{#gnr_secondary#}</span>
                                {/if}
                            </td>
                        </tr>
                        {foreachelse}
                        <tr>
                            <td class="text-muted text-center"
                                colspan="100%">
                                <div>
                                    {#gnr_no_records#}
                                </div>
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{/block}