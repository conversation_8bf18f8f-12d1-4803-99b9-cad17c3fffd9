{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <script src="/templates/assets/js/Chart.bundle.min.js"></script>
    <style>
        .tr-background{
            background-color: #A0D468 !important;
        }

        .chart-design{
            position: relative;
            height: 100px;
        }
    </style>
{/block}
{block name=page_body}



    <div class="row">
        <div class="col-lg-12">
            <form action="{url urltype=path
                url_string="bsc/P051/HrReportOpportunitiesOccupiedJobs/show/0/{$smarty.session.lang}/units"}"
                  method="post">
                <select name="unit_id" onchange="this.form.submit()" required>
                    <option>{#p_choose_unit#}</option>
                    {foreach $units as $unit}
                        <option {if $smarty.session.unit_id eq $unit->sh_unt_id} selected {/if} value="{$unit->sh_unt_id}">
                            {$unit->sh_unt_name}
                        </option>
                    {/foreach}
                </select>
            </form>
        </div>
    </div>
    <div class="horizontal-space"></div>



    {if isset($unit)}
        <div class="div widget-body">
        <div id="{$unit->sh_unt_id}" class=" my-2">
            <div class="mb-1">
                {url check=0 urltype="alinkn"
                url_string="bsc/P051/HrReportOpportunitiesOccupiedJobs/print/0/{$smarty.session.lang}/units/{$unit->id}"
                text_value="<i class='fa fa-print black'></i>&nbsp;{#gnr_print#}&nbsp;" style="btn btn-default shiny"}
            </div>


            <div class="horizontal-space"></div>

            <h3 class="row-title before-gry p-1">{$unit->sh_unt_name}</h3>

                <div class="row">
                    <div class="col-lg-12">
                        <div class="well">

                            <table class="table table-striped">
                                <tbody>
                                <tr class="tr-background">
                                    <th>{#p_total_count_of_jobs_in_units#}</th>
                                    <th>{#p_total_digits#}</th>
                                    <th>{#p_count_of_occupied_jobs#}</th>
                                    <th>{#p_count_of_opportunities_jobs#}</th>
                                </tr>
                                <tr>
                                    <td class="text-muted">{$unit->jobsCount}</td>
                                    <td class="text-muted">{count($unit->occupiedJobs) + count($unit->vacantJobs)}</td>
                                    <td class="text-success">{count($unit->occupiedJobs)}</td>
                                    <td class="text-danger">{count($unit->vacantJobs)}</td>
                                </tr>
                                </tbody>
                            </table>

                        </div>

                    </div>

                </div>

                <div class="row">
                    <div class="col-lg-12 col-md-6">
                        <div class="widget-body">
                            <vacant-bar
                                    :graph="'vacant-chart'"
                                    :type="'bar'"
                                    :lables="['الشاغرة','المشغولة']"
                                    :colors="['#bc5456', '#53a93f']"
                                    :values= "{json_encode($vacant_values)}"
                                    title="{$title}"></vacant-bar>
                        </div>
                    </div>
                </div>

                <div class="row">

                    <div class="col-lg-12">

                        <h5 class="row-title before-success">{#p_couupied_jobs#}</h5>

                        <div class="well">

                            <table class="table table-striped">
                                <tbody>
                                <tr class="tr-background">
                                    <th>#</th>
                                    <th>{#gnr_job#}</th>
                                    <th>{#gnr_employee#}</th>
                                    <th>{#p_job_type#}</th>
                                </tr>
                                {$i=1}
                                {foreach $unit->occupiedJobs as $vacant}
                                    <tr>
                                        <td class="text-muted">{$i++}</td>
                                        <td class="text-muted">{$vacant->sh_job_name}</td>
                                        <td class="text-muted">
                                            {$vacant->sh_user_full_name}
                                        </td>
                                        <td class="text-muted">

                                            {if $vacant->sh_uao_basic eq 1}
                                                <span>{#gnr_primary#}</span>
                                            {/if}

                                            {if $vacant->sh_uao_basic eq 0}
                                                <span>{#gnr_secondary#}</span>
                                            {/if}
                                        </td>
                                    </tr>
                                    {foreachelse}
                                    <tr>
                                        <td class="text-muted text-center"
                                            colspan="100%">
                                            <div class="">
                                                {#gnr_no_records#}
                                            </div>
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>

                        </div>
                    </div>

                    <div class="col-lg-12">
                        <h5 class="row-title before-danger">{#p_opportunities_jobs#}</h5>

                        <div class="well">
                            <table class="table table-striped">
                                <tbody>
                                <tr class="tr-background">
                                    <th>#</th>
                                    <th>{#gnr_job#}</th>
                                    <th>{#gnr_code#}</th>
                                    <th>{#p_job_type#}</th>
                                </tr>
                                {$i=1}
                                {foreach $unit->vacantJobs as $item}
                                    <tr>
                                        <td class="text-muted text-lg">{$i++}</td>
                                        <td class="text-muted text-lg">{$item->sh_job_name}</td>
                                        <td class="text-muted">{$item->sh_uao_job_code}</td>
                                        <td class="text-muted">
                                            {if $vacant->sh_uao_basic eq 1}
                                                <span>{#gnr_primary#}</span>
                                            {/if}

                                            {if $vacant->sh_uao_basic eq 0}
                                                <span>{#gnr_secondary#}</span>
                                            {/if}
                                        </td>
                                    </tr>
                                    {foreachelse}
                                    <tr>
                                        <td class="text-muted text-center"
                                            colspan="100%">
                                            <div class="">
                                                {#gnr_no_records#}
                                            </div>
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>
                        </div>

                    </div>

                </div>

            </div>
    </div>
    {/if}
{/block}