{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_delete_row#} </h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="snsowraper text-center">
                {#gnr_delete_row_confirm#}<br><br>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_employee_name#}</div>
            <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                {getname table=sh_user id=$row->user_id}
            </div>


            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_date_start#}</div>
            <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                {getdate table=hr_attendance col=start_date type=show row=$row}
            </div>

            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 snsolabel">&nbsp;&nbsp;</div>
            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">&nbsp;&nbsp;</div>
            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel  text-center">
                {url check=0 urltype="delete" opr_code='attendance' url_string="bsc/P051/attendance/show/0/{$smarty.session.lang}/delete/{$smarty.session.s_attendance_token}/{$row->id}"}
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}