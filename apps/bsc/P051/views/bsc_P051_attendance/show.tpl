{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}

    <div id="absence" class="tab-pane {if $smarty.session.s_personnel_tab eq 'absence'} active {/if}">
        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
            <thead>
            <tr>
                <th width="5%"  style="background-color: #A0D468 !important;">{url check=1 urltype="madd" opr_code='attendance' url_string="bsc/P051/attendance/add/0/{$smarty.session.lang}"}</th>
                <th width="15%" style="background-color: #A0D468 !important;">{#gnr_employee_name#}</th>
                <th width="15%" style="background-color: #A0D468 !important;">{#gnr_duration#}</th>
                <th width="25%" style="background-color: #A0D468 !important;">{#gnr_period#}</th>
                <th width="20%" style="background-color: #A0D468 !important;">{#gnr_status#}</th>
                <th width="10%" style="background-color: #A0D468 !important;" align="center">{#gnr_settings#}</th>
                <th width="25%" style="background-color: #A0D468 !important;" align="center">{#gnr_process#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $attendanceRequests as $request}
                <tr>
                    <td align="center">{$i++}</td>
                    <td align="right">{$request->requestable->user->full_name}</td>
                    <td align="center">{$request->requestable->duration}&nbsp;{#gnr_day#}</td>
                    <td align="center">
                        {getdate type=show row=$request->requestable col=hr_attendance_start_date}
                        &nbsp;&raquo;&nbsp;
                        {getdate type=show row=$request->requestable col=hr_attendance_end_date}
                        &nbsp;&raquo;&nbsp;

                        {if $request->send_status eq Request::REQUEST_IS_NOT_SEND}
                            {if $request->dataObject->record_type eq HRAttendanceRequest::ATTENDANCE_UPON_DOAM_RECORDS}
                                {url check=0 urltype="mbutton" url_string="bsc/P051/attendance/doamStructure/0/{$smarty.session.lang}/{$request->requestable->id}" text_value="{#gnr_edit#}"}
                            {/if}
                            {if $request->requestable->record_type eq HRAttendanceRequest::ATTENDANCE_UPON_FINGER_PRINTS_RECORDS}
                                {url check=0 urltype="alinkn" url_string="bsc/P051/attendance/fingerPrintStructure/0/{$smarty.session.lang}/{$request->requestable->id}" text_value="{#gnr_edit#}"}
                            {/if}
                        {else}
                            {url check=0 urltype="mbutton" url_string="gnr/X000/reportView/reportView/0/{$smarty.session.lang}/personnelAbsenceDaysHTML/&p[requestId]={$request->requestable->id}" text_value="{#gnr_view#}"}
                        {/if}

                    </td>
                    <td align="center">
                        {if $request->requestable->duration}
                            {workflow requestId=$request->id backTo="bsc/P051/attendance/show/0/{$smarty.session.lang}"}
                        {/if}

                        {if $request->send_status eq Request::REQUEST_REACH_END}
                            {url check=0 urltype="button" opr_code='personnel' oprvtype=3 url_string="bsc/P051/attendance/show/0/{$smarty.session.lang}/archive/{$smarty.session.s_attendance_token}/{$request->requestable->id}" text_value="{#gnr_archiving#}"}
                        {/if}
                    </td>
                    <td align="center">

                        {if $request->send_status eq Request::REQUEST_IS_NOT_SEND}
                            {url check=1 urltype="medit" opr_code='attendance' url_string="bsc/P051/attendance/edit/0/{$smarty.session.lang}/{$request->requestable->id}/tab3"}
                            {url check=1 urltype="mdelete" opr_code='attendance' url_string="bsc/P051/attendance/confirm/0/{$smarty.session.lang}/{$request->requestable->id}"}
                        {/if}

                        {if $request->send_status eq Request::REQUEST_REACH_END}
                            {if empty($request->requestable->manipulation_trans_id) and $request->requestable->manipulation_type eq 0}
                                {url check=1 urltype="button" opr_code='attendance' oprvtype=3 url_string="bsc/P051/attendance/manipulate/0/{$smarty.session.lang}/save_session/{$request->requestable->id}" text_value="{#gnr_process#}"}
                            {/if}

                        {/if}
                    </td>
                    <td>

	                    {if $request->requestable->manipulation_type neq 0}
		                    {getname table=st_setting id=$request->requestable->manipulation_type}<br>
	                    {/if}

                        {if $request->requestable->manipulation_type eq 355 and $request->requestable->manipulation_trans_id neq 0}
                            <span style="color: darkred;">{#p_salary_deducted#}</span>
                        {/if}

                        {if $request->requestable->manipulation_type eq 354 and $request->requestable->manipulation_trans_id neq 0}
                            <span style="color: darkred;">{#p_leave_credit_deducted#}</span>
                        {/if}

	                    {if $request->requestable->manipulation_type eq 359 and $request->requestable->manipulation_trans_id neq 0}
                            <span style="color: darkred;">{#p_no_deducted#}</span>
	                    {/if}

                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>

        {render view="/_pagination" rows=$attendanceRequests url="bsc/P051/attendance/show"}
    </div>

{/block}