{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_edit_row#} </h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-lg-12">
                <form method="post" action='{url urltype="path" url_string="bsc/P051/attendance/show/0/{$smarty.session.lang}/update/{$smarty.session.s_attendance_token}/{$row->id}"}'>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_employee_name#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <select name="user_id" id="user_id" required>
                            {foreach $employees as $employee}
                                <option value="{$employee->sh_user_id}" {if $employee->sh_user_id eq $row->user_id} selected="selected" {/if}>{$employee->sh_user_full_name}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_from_date#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getdate table=hr_attendance col=start_date type=edit row=$row}</div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_to_date#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getdate table=hr_attendance col=end_date type=edit row=$row}</div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#record_type#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $absenceRecordTypes as $type}
                                <div class="radio">
                                    <label>
                                        <input name="record_type" value="{$type->id}" id="{$type->id}" {if $type->id eq $row->record_type} checked {/if} type="radio" required>
                                        <span class="text">{$type->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-warning sharp">{#gnr_edit#}</button>
                    </div>

                </form>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}