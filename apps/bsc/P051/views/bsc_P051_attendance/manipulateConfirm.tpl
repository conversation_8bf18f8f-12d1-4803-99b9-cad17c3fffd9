{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{t v=$type}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                {if $type eq 354}
                    <div class="row">
                        <div class="col-lg-12"><span class="small red">{#p_you_can_choose_one_of_leaves_below_to_deduct_absence_days_from_it#}</span></div>
                        <div class="col-lg-12">
                            <table class="table table-hover table-striped table-bordered table-condensed">
                                <thead>
                                <tr>
                                    <th width="5%"></th>
                                    <th width="50%" style="text-align: right;">{#gnr_leave_name#}</th>
                                    <th width="45%">{#gnr_available_balance#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i=1}
                                {foreach $leaves as $item}
                                    <tr>
                                        <td align="center">{$i++}</td>
                                        <td style="text-align: right;">{$item->leaveType->name}</td>
                                        <td align="right">

                                            {leaveCredit LeaveAllowedObject=$item}

                                            {if in_array($item->leaveType->annual_intrvl_type,[831,837]) and $attendance->created_date gte $item->created_date|date_format:"%Y-%m-%d"}

                                                {* Open credit *}
                                                {if $item->leaveType->annual_intrvl_type eq 830}
                                                    {url urltype="button" url_string="bsc/P051/attendance/manipulate/0/{$smarty.session.lang}/update/{$smarty.session.s_attendance_token}/{$type}/{$item->id}" text_value="{#gnr_choose#}"}
                                                {/if}

                                                {* Fixed gradual credit *}
                                                {if $item->leaveType->annual_intrvl_type eq 831}
                                                    {if $item->employeeLeaveCredit gte 1}
                                                        {url urltype="button" url_string="bsc/P051/attendance/manipulate/0/{$smarty.session.lang}/update/{$smarty.session.s_attendance_token}/{$type}/{$item->id}" text_value="{#gnr_choose#}"}
                                                    {/if}
                                                {/if}

                                                {* Fixed from first credit *}
                                                {if $item->leaveType->annual_intrvl_type eq 837}
                                                    {if $item->employeeLeaveCredit gte 1}
                                                        {url urltype="button" url_string="bsc/P051/attendance/manipulate/0/{$smarty.session.lang}/update/{$smarty.session.s_attendance_token}/{$type}/{$item->id}" text_value="{#gnr_choose#}"}
                                                    {/if}
                                                {/if}

                                                {* Fixed credit taken as one *}
                                                {if $item->leaveType->annual_intrvl_type eq 894}
                                                    {url urltype="button" url_string="bsc/P051/attendance/manipulate/0/{$smarty.session.lang}/update/{$smarty.session.s_attendance_token}/{$type}/{$item->id}" text_value="{#gnr_choose#}"}
                                                {/if}

                                            {else}
                                                {#p_cannot_deduct_from_this_leave_type#}
                                            {/if}
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>
                        </div>
                        <div class="col-lg-12"><span style="color: red; font-size: smaller">{#p_cannot_deduct_from_this_leave_type_note#}</span></div>
                    </div>

                {/if}

                {if $type eq 355}
                    <div class="row">
                        <div class="col-lg-12"><span class="small red">{#p_you_will_be_able_to_deduct_absence_amount_from_user_salary_when_prepare_payslip#}</span></div>
                    </div>
                    <div class="horizontal-space"></div>

                    <div class="row">
                        <div class="col-lg-12">{url check=0 urltype="button" oprvtype=3 url_string="bsc/P051/attendance/manipulate/0/{$smarty.session.lang}/update/{$smarty.session.s_attendance_token}/{$type}" text_value="{#gnr_confirm#}"}</div>
                    </div>

                {/if}

                {if $type eq 359}
                    <div class="row">
                        <div class="col-lg-12"><span class="small red">{#p_when_choose_not_to_deduct_absence_will_not_affect_employee_leave_credit_or_his_salary#}</span></div>
                    </div>
                    <div class="horizontal-space"></div>

                    <div class="row">
                        <div class="col-lg-12">{url check=0 urltype="button" oprvtype=3 url_string="bsc/P051/attendance/manipulate/0/{$smarty.session.lang}/update/{$smarty.session.s_attendance_token}/{$type}" text_value="{#gnr_confirm#}"}</div>
                    </div>

                {/if}
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}