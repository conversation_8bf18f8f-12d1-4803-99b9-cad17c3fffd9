{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}

    <div class="row snsowraper">

        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
            {if $attendance->record_type eq HRAttendanceRequest::ATTENDANCE_UPON_DOAM_RECORDS}
                <div class="widget">
                    <div class="widget-header">
                        <span class="widget-caption">{#gnr_absent#}</span>
                    </div>
                    <div class="widget-body bordered-top bordered-sky">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_employee#}</div>
                                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getname table=sh_user id=$attendance->user_id}</div>

                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_duration#}</div>
                                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$attendance->duration}&nbsp;{#gnr_day#}</div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">{#gnr_date#}</div>
                                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{#gnr_work_periods#}</div>
                                {foreach $details as $day => $detail}
                                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">
                                        {if $detail.type eq 'AV'}<span class="green">{$day}:{getname table=st_setting id=$detail.name}</span>{/if}
                                        {if $detail.type eq 'NAV'}<span class="red">{$day}:{getname table=st_setting id=$detail.name}</span>{/if}
                                    </div>
                                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                                        {foreach $detail.interval as $id => $data}
                                            {if $data.status eq 1}<i class="fa fa-check-circle green"></i>{else}<i class="fa fa-times-circle red"></i>{/if}
                                            <span class="text">{$data.name}&nbsp;:&nbsp;{if !empty($id)} {$data.duration}&nbsp;{#gnr_hour#} &nbsp;&raquo;&nbsp; {/if} {$data.wieght}&nbsp;%</span><br>
                                        {/foreach}
                                    </div>
                                {/foreach}
                            </div>
                        </div>
                    </div>
                </div>
            {/if}

            {if $attendance->record_type eq HRAttendanceRequest::ATTENDANCE_UPON_FINGER_PRINTS_RECORDS}
                <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table searchable">
                    <thead>
                    <tr>
                        <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                        <th style="background-color: #A0D468 !important;" width="10%">{#gnr_date#}</th>
                        <th style="background-color: #A0D468 !important;" width="15%">{#p_original_finger_print_records#}</th>
                        <th style="background-color: #A0D468 !important;" width="15%">{#p_edited_finger_print_records#}</th>
                        <th style="background-color: #A0D468 !important;" width="10%">{#p_work_duration#}</th>
                        <th style="background-color: #A0D468 !important;" width="10%">{#p_delay_duration#}</th>
                        <th style="background-color: #A0D468 !important;" width="10%">{#p_extra_duration#}</th>
                        <th style="background-color: #A0D468 !important;" width="10%">{#p_absence_duration#}</th>
                        <th style="background-color: #A0D468 !important;" width="15%">{#p_no_compound_request#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $fingerPrintRecords as $day}

                        <tr style="{if $day->workday_or_weekend eq 0} background-color: #d4d4d4;{/if}">
                            <td align="center">{$i++}</td>
                            <td>
                    <span >
                    {t v=$day->day_in_week}
                        &nbsp;&raquo;&nbsp;
                        {$day->date}
                    </span>
                            </td>
                            <td>{implode('&nbsp;&raquo;&nbsp;',$day->fingerPrintsOrigin)}</td>
                            <td>
                                {if $day->workday_or_weekend eq 1}
                                    {if $day->correctness_status eq 0}<i class="fa fa-circle red"></i>{/if}
                                    {if $day->correctness_status eq 1}<i class="fa fa-circle green"></i>{/if}
                                    &nbsp;
                                    <span style="color: {$day->editingStatus};">{implode('&nbsp;&raquo;&nbsp;',$day->fingerPrintsEdited)}</span>
                                {/if}
                            </td>
                            <td align="center">{$day->work_duration|number_format:2:".":","}</td>
                            <td align="center" {if $day->delay_duration} style="color: blue" {/if}>{$day->delay_duration|number_format:2:".":","}</td>
                            <td align="center" {if $day->extra_duration} style="color: blue" {/if}>{$day->extra_duration|number_format:2:".":","}</td>
                            <td align="center" {if $day->absense_duration} style="color: blue" {/if}>{$day->absense_duration|number_format:2:".":","}</td>
                            <td align="center">
                                {if $day->request_ids}
                                    {foreach $day->request_ids as $requestId}
                                        {workflow requestId=$requestId backTo="#" hideStatus=yes}
                                    {/foreach}
                                {/if}
                            </td>
                        </tr>

                        {assign var="WorkSum" value="`$WorkSum+$day->work_duration`"}
                        {assign var="DelaySum" value="`$DelaySum+$day->delay_duration`"}
                        {assign var="ExtraSum" value="`$ExtraSum+$day->extra_duration`"}
                        {assign var="AbsenceSum" value="`$AbsenceSum+$day->absense_duration`"}

                    {/foreach}

                    </tbody>

                    <tfoot>
                    <tr style="background-color: gray; color: white">
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td align="center">{$WorkSum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
                        <td align="center">{$DelaySum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
                        <td align="center">{$ExtraSum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
                        <td align="center">{$AbsenceSum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
                        <td></td>
                    </tr>
                    </tfoot>

                    {assign var="WorkSum" value=0}
                    {assign var="DelaySum" value=0}
                    {assign var="ExtraSum" value=0}
                    {assign var="AbsenceSum" value=0}
                </table>
            {/if}
        </div>

        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
            <div class="widget">
                <div class="widget-header">
                    <span class="widget-caption">{#gnr_process#}</span>
                </div>
                <div class="widget-body bordered-top bordered-sky">
                    <div class="row">
                        <div class="col-lg-12">

                            {if empty($attendance->manipulation_type)}

                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_options#}</div>
                                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                                    <div class="control-group">
                                        {foreach $manipulateList as $srow}
                                            {url check=1 urltype="mbutton" opr_code='attendance' oprvtype=3 url_string="bsc/P051/attendance/manipulateConfirm/0/{$smarty.session.lang}/{$srow->id}/{$attendance->id}" text_value={$srow->translatedName}}
                                        {/foreach}
                                    </div>
                                </div>

                            {/if}

                            {*{if $attendance->manipulation_type eq Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_LEAVE_BALANCE}*}

                            {*<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_options#}</div>*}
                            {*<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">*}
                            {*<div class="control-group">*}
                            {*{foreach $manipulateList as $srow}*}
                            {*{url check=1 urltype="mbutton" opr_code='personnel' oprvtype=3 url_string="bsc/P051/personnel/manipulateConfirm/0/{$smarty.session.lang}/{$srow->id}/{$attendance->id}" text_value={$srow->translatedName}}*}
                            {*{/foreach}*}
                            {*</div>*}
                            {*</div>*}

                            {*{/if}*}

                            {*{if $attendance->manipulation_type eq Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_SALARY and empty($attendance->payrollBatch->request_success)}*}

                            {*<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_options#}</div>*}
                            {*<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">*}
                            {*<div class="control-group">*}
                            {*{foreach $manipulateList as $srow}*}
                            {*{url check=1 urltype="mbutton" opr_code='personnel' oprvtype=3 url_string="bsc/P051/personnel/manipulateConfirm/0/{$smarty.session.lang}/{$srow->id}/{$attendance->id}" text_value={$srow->translatedName}}*}
                            {*{/foreach}*}
                            {*</div>*}
                            {*</div>*}

                            {*{/if}*}

                            {*{if $attendance->manipulation_type eq Setting::PERSONNEL_ABSENCE_NO_DEDUCTED}*}

                            {*<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_options#}</div>*}
                            {*<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">*}
                            {*<div class="control-group">*}
                            {*{foreach $manipulateList as $srow}*}
                            {*{url check=1 urltype="mbutton" opr_code='personnel' oprvtype=3 url_string="bsc/P051/personnel/manipulateConfirm/0/{$smarty.session.lang}/{$srow->id}/{$attendance->id}" text_value={$srow->translatedName}}*}
                            {*{/foreach}*}
                            {*</div>*}
                            {*</div>*}

                            {*{/if}*}

                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_process#}</div>
                            <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">

                                {t v=$attendance->manipulation_type}

                                {if $attendance->manipulation_type eq Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_LEAVE_BALANCE and !empty($attendance->leaveType)}
                                    <span>[&nbsp;{$attendance->leaveType->name}&nbsp;]</span>
                                {/if}

                                {if $attendance->manipulation_type eq Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_SALARY and !empty($attendance->payrollBatch)}
                                    <span>[&nbsp;{$attendance->payrollBatch->name}&nbsp;]</span>
                                {/if}

                                <br>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}
{block name=back}{url urltype="path" url_string="bsc/P051/attendance/show/0/{$smarty.session.lang}"}{/block}