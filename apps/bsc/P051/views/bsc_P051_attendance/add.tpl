{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script type="text/javascript">
        $(document).ready(function () {
            $("#user_id").change(function () {
                $.get('/framework/core/functions/ajax/JobWardiah.php?userId=' + $(this).val(), function (data) {
                    var var1 = data.replace("wardiah_ids", 'wardiah_ids1');
                    $("#wardiah1").html(var1);
                    var var2 = data.replace("wardiah_ids", 'wardiah_ids2');
                    $("#wardiah2").html(var2);
                });
            });
        });

        function protected(name)
        {
            $('#warning').addClass('alert-warning').html('(' + name + ') {#p_not_belong_to_doam#}');
        }
    </script>
{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_create_absence_record#}</h4>
    </div>
    <div class="modal-body">
        <div class="alert" id="warning"></div>
        {if $grfExistenceNum eq 1}
            <div class="row">
                <div class="col-lg-12">
                    <form method="post"
                          action='{url urltype="path" url_string="bsc/P051/attendance/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_attendance_token}"}'>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_employee#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="user_id" id="user_id" class="no-search" required>
                                <option value="">{#gnr_choose_employee#}</option>
                                {foreach $employees as $employee}
                                    {if empty($employee->sh_uao_att_doam_id)}
                                        <option value="{$employee->sh_user_id}" disabled
                                                onclick="return protected('{$employee->sh_user_full_name}')">{$employee->sh_user_full_name}</option>
                                    {else}
                                        <option value="{$employee->sh_user_id}">{$employee->sh_user_full_name}</option>
                                    {/if}
                                {/foreach}
                            </select>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_from_date#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            {getdate table=hr_attendance col=start_date type=add row=$row}
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_to_date#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            {getdate table=hr_attendance col=end_date type=add row=$row}
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#record_type#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                {foreach $absenceRecordTypes as $type}
                                    <div class="radio">
                                        <label>
                                            <input name="record_type" value="{$type->id}" id="{$type->id}" {if $type->id eq HRAttendanceRequest::ATTENDANCE_UPON_DOAM_RECORDS} checked {/if} type="radio" required>
                                            <span class="text">{$type->translatedName}</span>
                                        </label>
                                    </div>
                                {/foreach}
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                        </div>

                    </form>

                </div>

            </div>
        {/if}

        {if $grfExistenceNum eq 0}
            {GraphNotPreparedCorrectlly oprCode='attendance'}
        {/if}
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}