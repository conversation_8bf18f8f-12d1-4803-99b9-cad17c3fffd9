{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_absence_details#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form method="post" action='{url urltype="path" url_string="bsc/P051/attendance/show/0/{$smarty.session.lang}/structure/{$smarty.session.s_attendance_token}/{$row->id}"}'>
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_employee#}</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">{getname table=sh_user id=$row->user_id}</div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_date#}</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">{#p_work_periods#}</div>

                {foreach $details as $day => $detail}
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">
                        {if $detail.type eq 'AV'}<span class="green">{$day}:{t v=$detail.name}</span>{/if}
                        {if $detail.type eq 'NAV'}<span class="red">{$day}:{t v=$detail.name}</span>{/if}
                    </div>
                    <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                        {foreach $detail.interval as $id => $data}
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="intervalsID[]" value="{$day}*{$id}" {if $data.status eq 1} checked="checked" {/if}>
                                    <span class="text">{$data.name}&nbsp;:&nbsp;{if !empty($id)} {$data.duration}&nbsp;{#gnr_hour#} &nbsp;&raquo;&nbsp; {/if} {$data.wieght|number_format:2:".":","}&nbsp;%</span>
                                </label>
                            </div>

                        {/foreach}
                    </div>
                {/foreach}

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-warning sharp">{#gnr_edit#}</button>
                </div>

            </form>

        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}