{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>{/block}
{block name=page_body}
    <div class="widget radius-bordered">

        <div class="widget-header bg-darkgray">
            <span class="widget-caption">{$interval->name}&nbsp;&raquo;&nbsp;{getdate table=fp_interval col=start_date type=show row=$interval} &nbsp;&raquo;&nbsp;{getdate table=fp_interval col=end_date type=show row=$interval}</span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                    <i class="fa fa-minus white "></i>
                </a>
            </div>
        </div>

        <div class="widget-body">
            <div class="row snsowraper">
                <div class="col-lg-2">
                    <img src="/framework/core/functions/image.php?image={User::userProfilePicture($employee->userObject->id)}&width=100&height=150" class="img-fluid img-responsive rounded">
                </div>
                <div class="col-lg-10">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">{$employee->userObject->full_name}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_work#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                {getname table=hr_doam id=$employee->att_doam_id}
                                {url check=0 urltype="mbutton" opr_code='hrdoamwrdiah' url_string="gnr/X000/reportView/reportView/0/ar/doamViewIntervalsHTML/&p[doamId]={$employee->att_doam_id}" text_value="<i class='fa fa-question-circle'></i>" modal="modal"}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table searchable">
        <thead>
        <tr>
            <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
            <th style="background-color: #A0D468 !important;" width="10%">{#gnr_date#}</th>
            <th style="background-color: #A0D468 !important;" width="15%">{#p_original_finger_print_records#}</th>
            <th style="background-color: #A0D468 !important;" width="15%">{#p_edited_finger_print_records#}</th>
            <th style="background-color: #A0D468 !important;" width="10%">{#p_work_duration#}</th>
            <th style="background-color: #A0D468 !important;" width="10%">{#p_delay_duration#}</th>
            <th style="background-color: #A0D468 !important;" width="10%">{#p_extra_duration#}</th>
            <th style="background-color: #A0D468 !important;" width="10%">{#p_absence_duration#}</th>
            <th style="background-color: #A0D468 !important;" width="15%">{#p_no_compound_request#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $fingerPrintRecords as $day}

            <tr style="{if $day->workday_or_weekend eq 0} background-color: #d4d4d4;{/if}">
                <td align="center">{$i++}</td>
                <td>
                    <span >
                    {t v=$day->day_in_week}
                        &nbsp;&raquo;&nbsp;
                        {$day->date}
                    </span>
                </td>
                <td>{implode('&nbsp;&raquo;&nbsp;',$day->fingerPrintsOrigin)}</td>
                <td>
                    {if $day->workday_or_weekend eq 1}
                        {if $day->correctness_status eq 0}<i class="fa fa-circle red"></i>{/if}
                        {if $day->correctness_status eq 1}<i class="fa fa-circle green"></i>{/if}
                        &nbsp;
                        <span style="color: {$day->editingStatus};">{implode('&nbsp;&raquo;&nbsp;',$day->fingerPrintsEdited)}</span>
                    {/if}
                </td>
                <td align="center">{$day->work_duration|number_format:2:".":","}</td>
                <td align="center" {if $day->delay_duration} style="color: blue" {/if}>{$day->delay_duration|number_format:2:".":","}</td>
                <td align="center" {if $day->extra_duration} style="color: blue" {/if}>{$day->extra_duration|number_format:2:".":","}</td>
                <td align="center" {if $day->absense_duration} style="color: blue" {/if}>{$day->absense_duration|number_format:2:".":","}</td>
                <td align="center">
                    {if $day->request_ids}
                        {foreach $day->request_ids as $requestId}
                            {workflow requestId=$requestId backTo="#" hideStatus=yes}
                        {/foreach}
                    {/if}
                </td>
            </tr>

            {assign var="WorkSum" value="`$WorkSum+$day->work_duration`"}
            {assign var="DelaySum" value="`$DelaySum+$day->delay_duration`"}
            {assign var="ExtraSum" value="`$ExtraSum+$day->extra_duration`"}
            {assign var="AbsenceSum" value="`$AbsenceSum+$day->absense_duration`"}

        {/foreach}

        </tbody>

        <tfoot>
        <tr style="background-color: gray; color: white">
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td align="center">{$WorkSum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
            <td align="center">{$DelaySum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
            <td align="center">{$ExtraSum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
            <td align="center">{$AbsenceSum|number_format:2:".":","}&nbsp;{#gnr_hour#}</td>
            <td></td>
        </tr>
        </tfoot>

        {assign var="WorkSum" value=0}
        {assign var="DelaySum" value=0}
        {assign var="ExtraSum" value=0}
        {assign var="AbsenceSum" value=0}
    </table>

{/block}
{block name=page_header}

    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
        function exportTo(ID, type) {
            $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
        }
        {/literal}
    </script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [

                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                    $("tfoot input").keyup(function() {
                        /* Filter on the column (the index) of this element */
                        oTable.fnFilter(this.value, $("tfoot input").index(this));
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>

{/block}