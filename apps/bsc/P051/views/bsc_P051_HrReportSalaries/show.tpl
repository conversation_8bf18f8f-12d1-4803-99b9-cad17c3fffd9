{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}

{block name=page_body}
    <div class="widget-body">

        <div class="horizontal-space"></div>

        <div class="row">

            <div class="col-lg-12">
                <form method="post"
                      action="{url urltype="path" url_string="bsc/P051/HrReportSalaries/show/0/{$smarty.session.lang}/save_session"}">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_employee#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="employee">
                            <option value=""></option>
                            {foreach $employees as $emp}
                                <option value="{$emp->sh_user_id}"
                                        {if $emp->sh_user_id eq $employee->sh_user_id}selected{/if}>{$emp->sh_user_full_name}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_template#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="template">
                            <option value=""></option>
                            {foreach $templates as $temp}
                                <option value="{$temp->prl_templates_id}" {if $temp->prl_templates_id eq $template->prl_templates_id} selected {/if} >{$temp->prl_templates_name}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_with_slip#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="batch">
                            <option value=""></option>
                            {foreach $batches as $bat}
                                <option value="{$bat->prl_batches_id}" {if $bat->prl_batches_id eq $batch->prl_batches_id} selected {/if} >{$bat->prl_batches_name}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <input type="submit" class="btn btn-default shiny" value="{#gnr_view#}">
                    </div>
                </form>
            </div>

        </div>
    </div>

    <div class="horizontal-space"></div>
    {if count($salaries)}
        <div class="row">


            <div class="col-lg-12">

                <div class="mb-1">
                    {url check=0 urltype="alinkn" url_string="bsc/P051/HrReportSalaries/print/0/{$smarty.session.lang}/save_session" text_value="<i class='fa fa-print black'></i>&nbsp;{#gnr_print#}&nbsp;" style="btn btn-default"}
                </div>

                <table class="table table-bordered table-hover dataTable no-footer sortable-table">
                    <thead>
                    <tr>
                        <th width="3%">#</th>
                        <th width="19%">{#gnr_name#}</th>
                        <th width="13%">{#p_slip#}</th>
                        <th width="13%">{#gnr_basic#}</th>
                        <th width="13%">{#gnr_bonuses#}</th>
                        <th width="13%">{#gnr_discount#}</th>
                        <th width="13%">{#gnr_other_deductions#}</th>
                        <th width="13%">{#gnr_net#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i = 1}
                    {assign var="basic_sum" value=0}
                    {assign var="net_sum" value=0}
                    {foreach $salaries as $salary}
                        <tr>
                            <td>{$i++}</td>
                            <td>{getname table=sh_user id=$salary->prl_trans_user_id}</td>
                            <td>{getname table=prl_batches id=$salary->prl_trans_batch_id}</td>
                            <td>{$salary->prl_trans_basic_salary}</td>
                            {assign var="basic_sum" value=$basic_sum + $salary->prl_trans_basic_salary}
                            <td>{$salary->prl_trans_allowances}</td>
                            <td>{$salary->prl_trans_deductions}</td>
                            <td>{abs($salary->prl_trans_extravalue)|number_format:1}</td>
                            <td>{$salary->prl_trans_net}</td>
                            {assign var="net_sum" value=$net_sum + $salary->prl_trans_net}
                        </tr>
                    {/foreach}
                    </tbody>
                    <tfoot>
                    <tr>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th style="background-color: #aaa" class="text-center">{$basic_sum}</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th style="background-color: #aaa" class="text-center">{$net_sum}</th>
                    </tr>
                    </tfoot>
                </table>
            </div>

        </div>
    {elseif count($salaries) eq 0 and $smarty.session.report_data neq null}
        <div class="alert alert-warning">{#gnr_no_records#}</div>
    {/if}
{/block}