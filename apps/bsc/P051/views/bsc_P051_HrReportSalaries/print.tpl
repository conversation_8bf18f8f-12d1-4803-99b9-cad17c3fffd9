{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}

    <div class="row">
        <h4 class="text-center well">{#p_salaries_report#}</h4>
    </div>

    <div class="row">

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#gnr_employee#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">{$employee->sh_user_full_name|default:'-'}</div>

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#gnr_template#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">
            {$template->prl_templates_name|default:'-'}
        </div>

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#p_with_slip#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">
            {$batch->prl_batches_name|default:'-'}
        </div>

    </div>

    <div class="horizontal-space"></div>
    <div class="row">

        <div class="col-lg-12">

            <table class="table table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th width="3%">#</th>
                    <th width="19%">{#gnr_name#}</th>
                    <th width="13%">{#p_slip#}</th>
                    <th width="13%">{#gnr_basic#}</th>
                    <th width="13%">{#gnr_bonuses#}</th>
                    <th width="13%">{#gnr_discount#}</th>
                    <th width="13%">{#gnr_other_deductions#}</th>
                    <th width="13%">{#gnr_net#}</th>
                </tr>
                </thead>
                <tbody>
                {$i = 1}
                {assign var="basic_sum" value=0}
                {assign var="net_sum" value=0}
                {foreach $salaries as $salary}
                    <tr>
                        <td>{$i++}</td>
                        <td>{getname table=sh_user id=$salary->prl_trans_user_id}</td>
                        <td>{getname table=prl_batches id=$salary->prl_trans_batch_id}</td>
                        <td>{$salary->prl_trans_basic_salary}</td>
                        {assign var="basic_sum" value=$basic_sum + $salary->prl_trans_basic_salary}
                        <td>{$salary->prl_trans_allowances}</td>
                        <td>{$salary->prl_trans_deductions}</td>
                        <td>{abs($salary->prl_trans_extravalue)|number_format:1}</td>
                        <td>{$salary->prl_trans_net}</td>
                        {assign var="net_sum" value=$net_sum + $salary->prl_trans_net}
                    </tr>
                {/foreach}
                </tbody>
                <tfoot>
                <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td style="background-color: #aaa" class="text-center">{$basic_sum}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td style="background-color: #aaa" class="text-center">{$net_sum}</td>
                </tr>
                </tfoot>
            </table>

        </div>

    </div>
{/block}
