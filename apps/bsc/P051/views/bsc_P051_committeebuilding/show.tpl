{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />{/block}
{block name=body}
    <div class="widget flat radius-bordered">
        <div class="tabbable">

            <ul class="nav nav-tabs nav-justified" id="myTab5">
                <li class="{if $smarty.session.s_active_tab eq "committee"}active{/if}">
                    <a aria-expanded="false" data-toggle="tab" href="#committee">
                        {#p_commitee_building#}
                    </a>
                </li>
                <li class="tab-red {if $smarty.session.s_active_tab eq "members"}active{/if}">
                    <a aria-expanded="true" href="#members">
                        {#gnr_members#}
                    </a>
                </li>
            </ul>

            <div class="tab-content">
                <div id="committee" class="tab-pane {if $smarty.session.s_active_tab eq "committee"}active{/if}">
                    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                        <thead>
                        <tr>
                            <th width="5%" style="background-color: #A0D468 !important;">{url check=1 urltype="madd" opr_code='committeebuilding' url_string="bsc/P051/committeebuilding/add/0/{$smarty.session.lang}"}</th>
                            <th data-priority="1" width="25%"  style="background-color: #A0D468 !important;">{#p_commitee_name#}</th>
                            <th data-priority="2" width="40%" style="background-color: #A0D468 !important;">{#p_committee_purpose#}</th>
                            <th data-priority="2" width="15%" style="background-color: #A0D468 !important;">{#p_committee_members#}</th>
                            <th data-priority="3" width="15%" style="background-color: #A0D468 !important;">{#gnr_settings#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {$i=1}
                        {foreach $committees as $committee}
                            <tr>
                                <td align="center">{$i++}</td>
                                <td>{$committee->name}</td>
                                <td>{$committee->purpose}</td>
                                <td align="center">
                                    {url check=0 urltype="button" opr_code='committeebuilding' url_string="bsc/P051/committeebuilding/show/0/{$smarty.session.lang}/save_session/{$committee->id}" text_value="<i class='fa fa-folder-o'></i> {#p_committee_members#}"}
                                </td>
                                <td align="center">
                                    {url check=0 urltype="mbutton" opr_code='committeebuilding' url_string="gnr/X000/committee/browse/0/{$smarty.session.lang}/{$committee->id}" text_value="<i class='fa fa-folder-o'></i>"}
                                    {url check=1 urltype="medit" opr_code='committeebuilding' url_string="bsc/P051/committeebuilding/edit/0/{$smarty.session.lang}/{$committee->id}"}
                                    {url check=1 urltype="mdelete" opr_code='committeebuilding' url_string="bsc/P051/committeebuilding/confirm/0/{$smarty.session.lang}/{$committee->id}"}
                                </td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>
                </div>
                <div id="members" class="tab-pane {if $smarty.session.s_active_tab eq "members"}active{/if}">
                    <h5 class="row-title before-blue">
                    <i class="glyphicon glyphicon-list-alt blue"></i>
                    {$currentCommittee->name}
                    </h5>

                    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                        <thead>
                        <tr>
                            <th width="5%">{url check=1 urltype="madd" opr_code='committeebuilding' url_string="bsc/P051/committeebuilding/addmember/0/{$smarty.session.lang}"}</th>
                            <th data-priority="1" width="20%">{#p_member_name#}</th>
                            <th data-priority="1" width="20%">{#gnr_job#}</th>
                            <th data-priority="2" width="30%">{#p_committeemember_role#}</th>
                            <th data-priority="2" width="15%">{#gnr_activation_status#}</th>
                            <th data-priority="3" width="15%">{#gnr_settings#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {$i=1}
                        {foreach $members as $member}
                            <tr>
                                <td align="center">{$i++}</td>
                                <td>{$member->vacantObject->userObject->full_name}</td>
                                <td>{$member->vacantObject->jobObject->sh_job_name}</td>
                                <td>{$member->role}</td>
                                <td>{getname table=st_setting id=$member->activation_status}</td>
                                <td align="center">
                                    {url check=1 urltype="medit" opr_code='committeebuilding' url_string="bsc/P051/committeebuilding/editmember/0/{$smarty.session.lang}/{$member->id}"}
                                    {url check=1 urltype="mdelete" opr_code='committeebuilding' url_string="bsc/P051/committeebuilding/confirmmember/0/{$smarty.session.lang}/{$member->id}"}
                                </td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}