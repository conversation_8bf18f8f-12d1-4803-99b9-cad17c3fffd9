{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script type="text/javascript">
        $(document).ready(function () {
            $("#extra1").hide("fast");
            $("#extra2").hide("fast");

            $("#type1").click(function () {
                if ($("#type1").is(":checked")) {
                    $("#extra1").show("fast");
                }
            });

            $("#type2").click(function () {
                if ($("#type2").is(":checked")) {
                    $("#extra1").hide("fast");
                }
            });

            {if $committee->time_range eq 1}
                $("#extra1").show("fast");
                $("#extra2").show("fast");
            {/if}
        });
    </script>
{/block}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>

        <h4 class="modal-title"> {#p_edit_committee#}  </h4>

    </div>
    <div class="modal-body">
        <div class="row snsowraper">

            <form method="post"
                  action='{url urltype="path" url_string="bsc/P051/committeebuilding/show/0/{$smarty.session.lang}/update/{$smarty.session.s_committeebuilding_token}/{$committee->id}"}'>
                <div class="row snsowraper">
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_commitee_name#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                        class="form-control"
                                                                                        id="name"
                                                                                        name="name"
                                                                                        placeholder="{#p_commitee_name#}"
                                                                                        value="{$committee->name}"
                                                                                        required></div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_committee_purpose#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><textarea id="purpose" class="form-control"
                                                                                           name="purpose" placeholder="{#p_committee_purpose#}">{$committee->purpose}</textarea>
                    </div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_interval#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <div class="radio">
                                <label>
                                    <input name="time_range" id="type1" value="1" {if $committee->time_range eq 1}checked="checked"{/if}
                                           type="radio">
                                    <span class="text">{#gnr_temporary#}</span>
                                </label>
                            </div>
                            <div class="radio">
                                <label>
                                    <input name="time_range" id="type2" value="2" {if $committee->time_range eq 2}checked="checked"{/if}
                                           type="radio">
                                    <span class="text">{#gnr_permanent#}</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_date_start#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getdate table=es_committee col=start_date type=edit row=$committee}</div>

                    <div id="extra1">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_date_end#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getdate table=es_committee col=end_date type=edit row=$committee}</div>
                    </div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                    </div>
                </div>
            </form>

        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}