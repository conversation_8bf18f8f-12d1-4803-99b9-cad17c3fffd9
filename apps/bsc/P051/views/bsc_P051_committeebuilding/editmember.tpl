{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_member#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form method="post"
                  action='{url urltype="path" url_string="bsc/P051/committeebuilding/show/0/{$smarty.session.lang}/member/update/{$smarty.session.s_committeebuilding_token}/{$member->id}"}'>

                <div class="row snsowraper">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_member_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="user_id" id="user_id">
                                {foreach $employees as $employee}
                                    <option value="{$employee->sh_user_id}" {if $employee->sh_user_id eq $member->user_id} selected {/if}
                                    >{$employee->sh_user_full_name}</option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_committeemember_role#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <textarea name="role" class="form-control" placeholder="{#p_committeemember_role#}">{$member->role}</textarea>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_notification_method#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                {foreach $notifications as $notification}
                                    <div class="checkbox">
                                        <label>
                                            <input name="notify_types[]" value="{$notification->id}" {if in_array($notification->id,','|explode:$member->notify_types)} checked="checked" {/if} type="checkbox">
                                            <span class="text">{$notification->translatedName}</span>
                                        </label>
                                    </div>
                                {/foreach}
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_activation_status#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            {foreach $activations as $activation}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="activation_status" value="{$activation->id}" {if $activation->id eq $member->activation_status} checked {/if}>
                                        <span class="text">{$activation->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}