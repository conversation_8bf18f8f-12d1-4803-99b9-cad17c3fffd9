{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}

    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_delete_row#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">

            <div class="snsowraper  text-center">
                {#gnr_delete_row_confirm#}<br><br>
                {$committee->name}<br><br>
                {if $smarty.session.s_active_tabe eq "tab2"}
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 bg-warning header-title">
                        <br>{#p_delete_commitee_will_delete_related_data_in_other_programs#}
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                {/if}
                {url check=1 urltype="delete" opr_code='committeebuilding' url_string="bsc/P051/committeebuilding/show/0/{$smarty.session.lang}/delete/{$smarty.session.s_committeebuilding_token}/{$committee->id}"}

            </div>

        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>

{/block}