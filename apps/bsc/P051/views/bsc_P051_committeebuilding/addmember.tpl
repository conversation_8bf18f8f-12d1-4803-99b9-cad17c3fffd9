{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>

        <h4 class="modal-title"> {#p_add_new_member#} </h4>

    </div>
    <div class="modal-body">
        <div class="row">
            <form method="post"
                  action='{url urltype="path" url_string="bsc/P051/committeebuilding/show/0/{$smarty.session.lang}/member/insert/{$smarty.session.s_committeebuilding_token}"}'>

                <div class="row snsowraper">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_member_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="user_id" id="user_id" required>
                                <option value="">{#p_choose_member#}</option>
                                {foreach $employees as $employee}
                                    <option value="{$employee->sh_user_id}">{$employee->sh_user_full_name}</option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_committeemember_role#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <textarea name="role" class="form-control" placeholder="{#p_committeemember_role#}"></textarea>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_notification_method#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                {foreach $notifications as $notification}
                                    <div class="checkbox">
                                        <label>
                                            <input name="notify_types[]" value="{$notification->id}" type="checkbox">
                                            <span class="text">{$notification->translatedName}</span>
                                        </label>
                                    </div>
                                {/foreach}
                            </div>
                        </div>



                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_activation_status#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            {foreach $activations as $activation}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="activation_status" value="{$activation->id}" {if $activation->id eq 653}checked{/if}>
                                        <span class="text">{$activation->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}