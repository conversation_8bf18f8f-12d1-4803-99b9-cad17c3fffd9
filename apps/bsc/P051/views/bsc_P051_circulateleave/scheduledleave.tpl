{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">
            {if $type eq 'circulate'}
                {#p_populate_scheduled_leave#}
            {else}
                {#p_cancel_leave_populatiton#}
            {/if}
        </h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="snsowraper  text-center">
                {if $type eq 'circulate'}
                    <span style="color: red">{#p_are_you_really_wants_to_populate_this_schedule_leave_all_employees_day_record_will_be_affected#}</span><br><br><hr>
                {/if}

                {if $type eq 'uncirculate'}
                    <span style="color: red">{#p_are_you_really_wants_to_cancel_populating_this_leave_all_employees_day_record_will_be_affected#}</span><br><br><hr>
                {/if}

                {getname table=hr_levst id=$row->leav_id}<br><hr>

                {url check=0 urltype="button" url_string="bsc/P051/circulateleave/show/0/{$smarty.session.lang}/circulate/{$smarty.session.s_circulateleave_token}/{$row->id}/{$type}" text_value="{#gnr_confirm#}"}
                &nbsp;&nbsp;&nbsp;

            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}