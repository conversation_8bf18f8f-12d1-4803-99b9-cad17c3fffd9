{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add_row#} </h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-lg-12">
                <form method="post" action='{url urltype="path" url_string="bsc/P051/circulateleave/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_circulateleave_token}"}'>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_leave_name#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="leav_id" required>
                            <option value="">{#p_choose_scheduled_leave#}</option>
                            {foreach $hr_levst_schedholid_list as $row}
                                <option value="{$row->id}">{$row->name}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_date_start#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=hr_schedholid col=start_date type=add row=$row}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_duration#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="number"
                                                                                          name="duration"
                                                                                          placeholder="{#gnr_duration#}" required="required">
                        &nbsp; {#gnr_day#}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_alert_message#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control"
                                                                                             id="notification_message"
                                                                                             name="notification_message"
                                                                                             placeholder="{#gnr_alert_message#}"></textarea>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}