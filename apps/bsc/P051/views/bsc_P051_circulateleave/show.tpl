{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}

    <div id="tabulated" class="tab-pane {if $smarty.session.s_personnel_tab eq 'tabulated'}in active{/if}">
        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
            <thead>
            <tr>
                <th width="5%" style="background-color: #A0D468 !important;">
                    {url check=1 urltype="madd" opr_code='circulateleave' url_string="bsc/P051/circulateleave/add/0/{$smarty.session.lang}"}
                </th>
                <th width="25%" style="background-color: #A0D468 !important;">{#gnr_name#}</th>
                <th width="15%" style="background-color: #A0D468 !important;">{#gnr_duration#}</th>
                <th width="20%" style="background-color: #A0D468 !important;">{#gnr_interval#}</th>
                <th width="25%" style="background-color: #A0D468 !important;">{#gnr_status#}</th>
                <th width="10%" style="background-color: #A0D468 !important;">{#gnr_settings#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $hr_schedholid_list as $request}
                <tr>
                    <td align="center">{$i++}</td>
                    <td>{getname table=hr_levst id=$request->leav_id}</td>
                    <td align="center">{$request->duration}&nbsp;{#gnr_day#}</td>
                    <td align="center">
                        {getdate table=hr_schedholid col=start_date type=show row=$request}
                        &nbsp;&raquo;&nbsp;
                        {getdate table=hr_schedholid col=end_date type=show row=$request}


                        {url check=0 urltype="mbutton" url_string="gnr/X000/mediacenter/circulateleavedayslist/0/{$smarty.session.lang}/{$request->start_date}/{$request->duration}" text_value='<i class="fa fa-question-circle" ></i>'}
                    </td>
                    <td align="center">

                        {if $request->circulate_status eq 0}
                            <span>{#p_not_populated#}</span>
                            {url check=1 urltype="mbutton" opr_code='deductaddition' oprvtype=3 url_string="bsc/P051/circulateleave/scheduledleave/0/{$smarty.session.lang}/{$request->id}/circulate" text_value="{#p_populate_leave#}"}
                        {/if}

                        {if $request->circulate_status eq 1}

                            <span>{#p_populated#}</span>

                            {url check=1 urltype="mbutton" opr_code='deductaddition' oprvtype=3 url_string="bsc/P051/circulateleave/scheduledleave/0/{$smarty.session.lang}/{$request->id}/uncirculate" text_value="{#p_cancel_population#}"}

                        {/if}
                    </td>

                    <td align="center" nowrap>
                        {if $request->circulate_status eq 0}
                            {url check=1 urltype="medit" opr_code='deductaddition' url_string="bsc/P051/circulateleave/edit/0/{$smarty.session.lang}/{$request->id}"}
                            {if $request->type eq 0}
                                {url check=1 urltype="mdelete" opr_code='deductaddition' url_string="bsc/P051/circulateleave/confirm/0/{$smarty.session.lang}/{$request->id}"}
                            {/if}
                        {/if}
                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>
    </div>

{/block}