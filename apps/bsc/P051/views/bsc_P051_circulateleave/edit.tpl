{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script type="text/javascript">
        $(document).ready(function () {

            $("#extra1").css("display", "none");
            $("#extra2").css("display", "none");
            $("#credit_annualy").css("display", "none");
            $("#credit_annualy2").css("display", "none");
            $("#credit_annualy3").css("display", "none");

            $("#checkme1").click(function () {

                if ($("#checkme1").is(":checked")) {
                    $("#extra1").hide();
                }
            });

            $("#814n").click(function () {

                if ($("#814n").is(":checked")) {
                    $("#credit_annualy3").show();
                }
            });

            $("#814").click(function () {

                if ($("#814").is(":checked")) {
                    $("#credit_annualy").show();
                }
            });


            $("#809").click(function () {

                if ($("#809").is(":checked")) {
                    $("#credit_annualy2").show();
                }
            });

            $("#808").click(function () {

                if ($("#808").is(":checked")) {
                    $("#credit_annualy2").hide();
                }
            });

            $("#809").click(function () {

                if ($("#809").is(":checked")) {
                    $("#credit_annualy2").show();
                }
            });

            $("#checkme2").click(function () {

                // If checked
                if ($("#checkme2").is(":checked")) {
                    //show the hidden div
                    $("#extra1").show();
                }
            });

            $("#checkme3").click(function () {

                if ($("#checkme3").is(":checked")) {
                    $("#extra2").show();
                }
            });

            $("#checkme4").click(function () {

                // If checked
                if ($("#checkme4").is(":checked")) {
                    //show the hidden div
                    $("#extra2").hide();
                }
            });

            {$check_two}

        });
    </script>
{/block}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_edit_row#} </h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-lg-12">
                <form method="post"
                      action='{url urltype="path" url_string="bsc/P051/circulateleave/show/0/{$smarty.session.lang}/update/{$smarty.session.s_circulateleave_token}/{$row->id}"}'>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_leave_name#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="leav_id" required>
                            <option value="{$row->leav_id}">{getname table=hr_levst id=$row->leav_id}</option>
                            {foreach $hr_levst_schedholid_list as $row}
                                <option value="{$row->id}">{$row->name}</option>
                            {/foreach}
                        </select>
                    </div>


                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_date_start#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=hr_schedholid col=start_date type=edit row=$row}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_duration#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="number"
                                                                                          name="duration"
                                                                                          value="{$row->duration}"
                                                                                          placeholder="{#gnr_duration#}" required="required">
                        &nbsp; {#gnr_day#}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_alert_message#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control"
                                                                                             id="notification_message"
                                                                                             name="notification_message"
                                                                                             placeholder="{#gnr_alert_message#}">{$row->notification_message}</textarea>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}