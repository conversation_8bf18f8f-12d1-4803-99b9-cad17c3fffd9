{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}
 
    <div class="row">
        <div class="col-lg-12">
            <div class="widget flat radius-bordered">
                <div class="tabbable tabs-left">
                    <ul class="nav nav-tabs" id="myTab3">
                        <li class="tab-sky {if $smarty.session.s_personnel_tab eq 'absence'} active {/if}">
                            <a aria-expanded="false" data-toggle="tab" href="#absence">
                                <span class="badge badge-sky badge-square">1</span>
                                <span>{#p_absence#}</span></a>
                        </li>
                        <li class="tab-sky {if $smarty.session.s_personnel_tab eq 'latency'} active {/if}">
                            <a aria-expanded="false" data-toggle="tab" href="#latency">
                                <span class="badge badge-sky badge-square">2</span>
                                <span>{#p_latency#}</span></a>
                        </li>
                        <li class="tab-sky {if $smarty.session.s_personnel_tab eq 'deductaddition'} active {/if}">
                            <a aria-expanded="false" data-toggle="tab" href="#deductaddition">
                                <span class="badge badge-sky badge-square">3</span>
                                <span>{#p_addition_deduction_request#}</span></a>
                        </li>
                    </ul>
                    <div class="tab-content">
                        <div id="absence"
                             class="tab-pane {if $smarty.session.s_personnel_tab eq 'absence'} active {/if}">
                            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                <thead>
                                <tr>
                                    <th width="5%"
                                        style="background-color: #A0D468 !important;"></th>
                                    <th width="15%"
                                        style="background-color: #A0D468 !important;">{#gnr_employee_name#}</th>
                                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_duration#}</th>
                                    <th width="25%" style="background-color: #A0D468 !important;">{#gnr_period#}</th>
                                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_view#}</th>
                                    <th width="15%"
                                        style="background-color: #A0D468 !important;">{#p_undo_archiving#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i=1}
                                {foreach $attendanceRequests as $request}
                                    <tr>
                                        <td align="center">{getCurrentPage() + $i++}</td>
                                        <td align="right">{$request->user_name}</td>
                                        <td align="center">{$request->duration}&nbsp;{#gnr_day#}</td>
                                        <td align="center">
                                            {getdate type=show row=$request col=start_date}
                                            &nbsp;&raquo;&nbsp;
                                            {getdate type=show row=$request col=end_date}
                                            &nbsp;&raquo;&nbsp;

                                            {if $request->send_status eq Request::REQUEST_IS_NOT_SEND}
                                                {if $request->record_type eq HRAttendanceRequest::ATTENDANCE_UPON_DOAM_RECORDS}
                                                    {url check=0 urltype="mbutton" url_string="bsc/P051/personnel/absenceDoamStructure/0/{$smarty.session.lang}/{$request->attendance_id}" text_value="{#gnr_edit#}"}
                                                {/if}
                                                {if $request->record_type eq HRAttendanceRequest::ATTENDANCE_UPON_FINGER_PRINTS_RECORDS}
                                                    {url check=0 urltype="alinkn" url_string="bsc/P051/personnel/absenceFingerPrintStructure/0/{$smarty.session.lang}/{$request->attendance_id}" text_value="{#gnr_edit#}"}
                                                {/if}
                                            {else}
                                                {url check=0 urltype="mbutton" url_string="gnr/X000/reportView/reportView/0/{$smarty.session.lang}/personnelAbsenceDaysHTML/&p[requestId]={$request->id}" text_value="{#gnr_view#}"}
                                            {/if}

                                        </td>

                                        <td align="center">
                                            {workflow requestId=$request->id hideStatus="yes" backTo="bsc/P051/PersonnelArchive/show/0/{$smarty.session.lang}"}
                                        </td>

                                        <td align="center">
                                            {url check=0 urltype="mbutton" url_string="bsc/P051/PersonnelArchive/confirmAbsence/0/{$smarty.session.lang}/{$request->attendance_id}" text_value="{#p_undo_archiving#}"}
                                        </td>

                                    </tr>
                                {/foreach}

                                </tbody>

                            </table>
                            {render view="/_pagination" rows=$attendanceRequests url="bsc/P051/PersonnelArchive/show" param='absence'}
                        </div>
                        <div id="latency"
                             class="tab-pane {if $smarty.session.s_personnel_tab eq 'latency'} active {/if}">
                            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                <thead>
                                <tr>
                                    <th width="5%"
                                        style="background-color: #A0D468 !important;"></th>
                                    <th width="15%"
                                        style="background-color: #A0D468 !important;">{#gnr_employee_name#}</th>
                                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_duration#}</th>
                                    <th width="25%" style="background-color: #A0D468 !important;">{#gnr_period#}</th>
                                    <th width="10%" style="background-color: #A0D468 !important;">{#gnr_view#}</th>
                                    <th width="15%"
                                        style="background-color: #A0D468 !important;">{#p_undo_archiving#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i=1}
                                {foreach $latenciesRequests as $request}
                                    <tr>
                                        <td align="center">{getCurrentPage() + $i++}</td>
                                        <td align="right">{$request->user_name}</td>
                                        <td align="center">{$request->duration}&nbsp;{#gnr_day#}</td>
                                        <td align="center">
                                            {$request->start_date}
                                            &nbsp;&raquo;&nbsp;
                                            {$request->end_date}
                                            &nbsp;&raquo;&nbsp;
                                            {url check=0 urltype="mbutton" url_string="gnr/X000/reportView/reportView/0/{$smarty.session.lang}/personnelLatencyDaysHTML/&p[requestId]={$request->latency_id}" text_value="{#gnr_view#}"}
                                        </td>
                                        <td align="center">
                                            {workflow requestId=$request->id hideStatus="yes" backTo="bsc/P051/PersonnelArchive/show/0/{$smarty.session.lang}"}
                                        </td>
                                        <td align="center">
                                            {url check=0 urltype="mbutton" url_string="bsc/P051/PersonnelArchive/confirmLatency/0/{$smarty.session.lang}/{$request->latency_id}" text_value="{#p_undo_archiving#}"}
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>
                            {render view="/_pagination" rows=$latenciesRequests url="bsc/P051/PersonnelArchive/show" param='latency'}
                        </div>
                        <div id="deductaddition"
                             class="tab-pane {if $smarty.session.s_personnel_tab eq 'deductaddition'} active {/if}">
                                <div class="row">
                                <div class="col-lg-12">
                                    <div class="margin-bottom-20">
                                        <a href="{url urltype="path" url_string="bsc/P051/deductaddition/show/0/{$smarty.session.lang}/menu"}" 
                                           class="btn btn-success shiny">
                                            <i class="fa fa-arrow-right"></i>
                                            العودة للطلبات النشطة
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                <thead>
                                <tr>
                                    <th width="2%" style="background-color: #A0D468 !important;">#</th>
                                    <th width="20%" style="background-color: #A0D468 !important;">{#gnr_employee_name#}</th>
                                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_type#}</th>
                                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_amount#}</th>
                                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_request_status#}</th>
                                    <th width="15%" style="background-color: #A0D468 !important;" align="center">{#gnr_settings#}</th>
                                    <th width="18%" style="background-color: #A0D468 !important;" align="center">المسير</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i=1}
                                {foreach $deductadditionRequests as $request}
                                    <tr>
                                        <td align="center">{$i++}</td>
                                        <td align="center">{$request->user_name|default:'-'}</td>
                                        <td align="center" style="background-color: {if $request->hr_deductaddition_type == 916}#FFE4E1{else}#E0FFE0{/if}">
                                            {if $request->hr_deductaddition_type == 916}
                                                <span class="label label-danger" style="cursor: pointer;" data-toggle="modal"
                                                    data-target="#reasonsModal{$request->hr_deductaddition_id}">طلب خصم</span>
                                            {else}
                                                <span class="label label-success" style="cursor: pointer;" data-toggle="modal"
                                                    data-target="#reasonsModal{$request->hr_deductaddition_id}">طلب إضافة</span>
                                            {/if}

                                            <!-- Modal Reasons -->
                                            <div class="modal fade" id="reasonsModal{$request->hr_deductaddition_id}" tabindex="-1" role="dialog"
                                                aria-labelledby="reasonsModalLabel{$request->hr_deductaddition_id}">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                                                    >&times;</span></button>
                                                            <h4 class="modal-title" id="reasonsModalLabel{$request->hr_deductaddition_id}">
                                                                {if $request->hr_deductaddition_type == 916}
                                                                    مبررات طلب الخصم
                                                                {else}
                                                                    مبررات طلب الإضافة
                                                                {/if}
                                                            </h4>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="row">
                                                                <div class="col-md-12">
                                                                    <div class="well"
                                                                        style="background-color: #f8f9fa; border: 1px solid #ddd; padding: 15px; border-radius: 4px;">
                                                                        <h5 style="margin-top: 0; color: #666;">المبررات:</h5>
                                                                        <p style="white-space: pre-line;">
                                                                            {$request->hr_deductaddition_reasons|default:'لا توجد مبررات'}</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-default" data-dismiss="modal">إغلاق</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td align="center">{$request->hr_deductaddition_amount|number_format:2:".":","|default:'0.00'}</td>
                                        <td align="center">
                                            <span class="label label-warning">مؤرشف</span>
                                        </td>              
                                        <td align="center" nowrap>
                                            {url check=0 urltype="mbutton" url_string="bsc/P051/PersonnelArchive/confirmDeductaddition/0/{$smarty.session.lang}/{$request->hr_deductaddition_id}" text_value="{#p_undo_archiving#}"}
                                        </td>
                                        <td align="center">
                                            {if $request->hr_deductaddition_manipulation_batche_id}
                                                <span class="label label-info" style="cursor: pointer;" data-toggle="modal"
                                                    data-target="#batchModal{$request->hr_deductaddition_manipulation_batche_id}">
                                                    {$request->batch_name|default:'غير محدد'}
                                                </span>

                                                <!-- Modal -->
                                                <div class="modal fade" id="batchModal{$request->hr_deductaddition_manipulation_batche_id}" tabindex="-1"
                                                    role="dialog" aria-labelledby="batchModalLabel{$request->hr_deductaddition_manipulation_batche_id}">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                                                        >&times;</span></button>
                                                                <h4 class="modal-title" id="batchModalLabel{$request->hr_deductaddition_manipulation_batche_id}">
                                                                    تفاصيل الدفعة</h4>
                                                            </div>
                                                            <div class="modal-body">
                                                                <div class="row">
                                                                    <div class="col-md-6">
                                                                        <p><strong>اسم الدفعة:</strong> {$request->batchObject->payroll_batch_name|default:'غير محدد'}</p>
                                                                        <p><strong>نوع الدفعة:</strong> {$request->batchObject->payroll_batch_type|default:'غير محدد'}</p>
                                                                        <p><strong>من تاريخ:</strong> {$request->batchObject->payroll_batch_from_date|default:'غير محدد'}</p>
                                                                        <p><strong>إلى تاريخ:</strong> {$request->batchObject->payroll_batch_to_date|default:'غير محدد'}</p>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <p><strong>عدد الأيام:</strong> {$request->batchObject->payroll_batch_days|default:'غير محدد'}</p>
                                                                        <p><strong>تاريخ الإنشاء:</strong> {$request->batchObject->payroll_batch_created_date|default:'غير محدد'}</p>
                                                                        <p><strong>حالة التأكيد:</strong>
                                                                        {if $request->batchObject->payroll_batch_confirm_status eq 357}
                                                                            <span class="label label-success">تم التأكيد</span>
                                                                        {elseif $request->batchObject->payroll_batch_confirm_status eq 358}
                                                                            <span class="label label-warning">قيد الانتظار</span>
                                                                        {elseif $request->batchObject->payroll_batch_confirm_status eq 359}
                                                                            <span class="label label-danger">تم الرفض</span>
                                                                        {elseif $request->batchObject->payroll_batch_confirm_status eq 360}
                                                                            <span class="label label-default">تم الإلغاء</span>
                                                                        {else}
                                                                            <span class="label label-default">غير محدد</span>
                                                                        {/if}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-default" data-dismiss="modal">إغلاق</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            {else}
                                                <span class="label label-default">غير محدد</span>
                                            {/if}
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>
                            {render view="/_pagination" rows=$deductadditionRequests url="bsc/P051/PersonnelArchive/show" param='deductaddition'}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}
{*{block name=page_header}*}
    {*<script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>*}
    {*<script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>*}
    {*<script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>*}
    {*<script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>*}
    {*<script>*}
        {*var InitiateSimpleDataTable = function () {*}
            {*return {*}
                {*init: function () {*}
                    {*//Datatable Initiating*}
                    {*var oTable = $('.sortable-table').dataTable({*}
                        {*"sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",*}
                        {*"iDisplayLength": 50,*}
                        {*"oTableTools": {*}
                            {*"aButtons": [*}
{*//                                "copy", "csv", "xls", "pdf", "print"*}
                            {*],*}
                            {*"sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"*}
                        {*},*}
                        {*"language": {*}
                            {*"search": "",*}
                            {*"sLengthMenu": "_MENU_",*}
                            {*"oPaginate": {*}
                                {*"sPrevious": "{#gnr_previous#}",*}
                                {*"sNext": "{#gnr_next#}"*}
                            {*}*}
                        {*}*}
                    {*});*}
                {*}*}

            {*};*}

        {*}();*}

        {*InitiateSimpleDataTable.init();*}
    {*</script>*}
{*{/block}*}