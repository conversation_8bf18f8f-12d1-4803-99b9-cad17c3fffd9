{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>{/block}
{block name=page_body}
<div class="row">
    <div class="col-lg-12">
        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
            <thead>
            <tr>
                <th class="text-muted text-center">#</th>
                <th class="text-muted text-center">{#gnr_employee_name#}</th>
                <th class="text-muted text-center">{#gnr_job#}</th>
                <th class="text-muted text-center">{#gnr_unit#}</th>
                <th class="text-muted text-center">{#gnr_expiration_date#}</th>
                <th class="text-muted text-center">{#gnr_notification_control#}</th>
            </tr>
            </thead>
            <tbody>
            {foreach $users as $user}
                <tr>
                    <th class="text-center">{$user@iteration}</th>
                    <th class="text-center">{$user->full_name}</th>
                    <th class="text-center">{$user->basicJob[0]->name}</th>
                    <th class="text-center">{$user->basicJob[0]->unit->name}</th>
                    <th class="text-center">{$user->identity_expairy_date->format('Y-m-d')}</th>
                    <td class="text-center">
                        {url check=0 urltype="medit" url_string="bsc/P051/IdentityReport/expiredIdentityUsers/0/ar/{$user->id}"}
                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>
    </div>
</div>
{/block}

{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [

                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                    $("tfoot input").keyup(function() {
                        /* Filter on the column (the index) of this element */
                        oTable.fnFilter(this.value, $("tfoot input").index(this));
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}
