{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}

{block name=page_header}
{/block}
{block name=page_body}

    <div class="widget mb-0 {if $smarty.session['report_data']}collapsed{/if}">
        <div class="widget-header bg-blue">
            <i class="widget-icon fa fa-arrow-left"></i>
            <span class="widget-caption">{#p_build_report#}</span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                    <i class="fa fa-{if $smarty.session['report_data']}plus{else}minus{/if}"></i>
                </a>
            </div><!--Widget Buttons-->
        </div><!--Widget Header-->
        <div class="widget-body">

                <form action='{url urltype="path" url_string="bsc/P051/HrReportEmployeeDataSheet/show/0/{$smarty.session.lang}/save_session"}'
                      method="post">

                    <div class="row">

                        <div class="col-lg-12">

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_unit#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-10 col-xs-12 snsoinput">
                                <select name="unit" required>
                                    <option value=""></option>
                                    {foreach $units as $unt}
                                        <option value="{$unt->sh_unt_id}" {if $unt->sh_unt_id eq $unit->sh_unt_id} selected {/if}>{$unt->sh_unt_name}</option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_data#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-10 col-xs-12 snsoinput">
                                <div class="col-lg-12">
                                    <div class="control-group">
                                        <div class="checkbox">
                                            <label><input {if in_array('nationality', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                        type="checkbox" name="dataSelected[]" value="nationality"><span
                                                        class="text">{#gnr_nationality#}</span></label>
                                        </div>
                                        <div class="checkbox">
                                            <label><input {if in_array('sex', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                        type="checkbox" name="dataSelected[]" value="sex"><span
                                                        class="text">{#gnr_sex#}</span></label>
                                        </div>
                                        <div class="checkbox">
                                            <label><input {if in_array('basic_job', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                        type="checkbox" name="dataSelected[]" value="basic_job"><span
                                                        class="text">{#p_basic_job#}</span></label>
                                        </div>
                                        <div class="checkbox">
                                            <label><input {if in_array('start_working_with_org_date', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                        type="checkbox" name="dataSelected[]"
                                                        value="start_working_with_org_date"><span
                                                        class="text">{#p_start_working_with_organization_date#}</span></label>
                                        </div>
                                        <div class="checkbox">
                                            <label><input {if in_array('end_working_with_org_date', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                        type="checkbox" name="dataSelected[]"
                                                        value="end_working_with_org_date"><span
                                                        class="text">{#p_end_working_with_organization_date#}</span></label>
                                        </div>
                                        <div class="checkbox">
                                            <label><input {if in_array('template', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                        type="checkbox" name="dataSelected[]" value="template"><span
                                                        class="text">{#p_template#}</span></label>
                                        </div>
                                        <div class="checkbox">
                                            <label><input {if in_array('basic_salary', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                        type="checkbox" name="dataSelected[]" value="basic_salary"><span
                                                        class="text">{#p_basic_salary#}</span></label>
                                        </div>
                                        <div class="checkbox">
                                            <label><input {if in_array('account_name', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                        type="checkbox" name="dataSelected[]" value="account_name"><span
                                                        class="text">{#p_bank_account_name#}</span></label>
                                        </div>
                                        <div class="checkbox">
                                            <label><input {if in_array('bank_number', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                        type="checkbox" name="dataSelected[]" value="bank_number"><span
                                                        class="text">{#p_ayban_number#}</span></label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <button type="submit" class="btn btn-default">{#gnr_view#}</button>
                            </div>

                        </div>

                    </div>

                </form>

        </div>

    </div>

    <div class="horizontal-space"></div>

    <div class="row">
        <div class="col-lg-12">
            {if count($users)}

            <div class="row">
                <div class="col-lg-12">
                    <h5 class="row-title before-blue ml-1">{#gnr_unit#}: {$unit->sh_unt_name}</h5>
                    {url check=0 urltype="alinkn" url_string="bsc/P051/HrReportEmployeeDataSheet/print/0/{$smarty.session.lang}/save_session" text_value="<i class='fa fa-print black'></i>&nbsp;{#gnr_print#}&nbsp;" style="btn btn-default"}
                </div>
            </div>

            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table searchable">
                <thead>
                <tr>
                    <th></th>
                    <th>{#gnr_name#}</th>
                    <th>{#p_identity_number#}</th>
                    {if in_array('nationality', $smarty.session['report_data']['dataSelected'])}
                        <th>{#gnr_nationality#}</th>{/if}
                    {if in_array('sex', $smarty.session['report_data']['dataSelected'])}
                        <th>{#gnr_sex#}</th>{/if}
                    {if in_array('basic_job', $smarty.session['report_data']['dataSelected'])}
                        <th>{#p_basic_job#}</th>{/if}
                    {if in_array('start_working_with_org_date', $smarty.session['report_data']['dataSelected'])}
                        <th>{#p_start_working_with_organization_date#}</th>{/if}
                    {if in_array('end_working_with_org_date', $smarty.session['report_data']['dataSelected'])}
                        <th>{#p_end_working_with_organization_date#}</th>{/if}
                    {if in_array('template', $smarty.session['report_data']['dataSelected'])}
                        <th>{#p_template#}</th>{/if}
                    {if in_array('basic_salary', $smarty.session['report_data']['dataSelected'])}
                        <th>{#p_basic_salary#}</th>{/if}
                    {if in_array('account_name', $smarty.session['report_data']['dataSelected'])}
                        <th>{#p_bank_account_name#}</th>{/if}
                    {if in_array('bank_number', $smarty.session['report_data']['dataSelected'])}
                        <th>{#p_ayban_number#}</th>{/if}
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $users as $user}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td>{$user->sh_user_full_name}</td>
                        <td>{$user->sh_user_identity_number}</td>
                        {if in_array('nationality', $smarty.session['report_data']['dataSelected'])}
                            <td>{getname table=st_country id=$user->sh_user_nationality}</td>{/if}
                        {if in_array('sex', $smarty.session['report_data']['dataSelected'])}
                            <td>{getname table=st_setting id=$user->sh_user_gender}</td>{/if}
                        {if in_array('basic_job', $smarty.session['report_data']['dataSelected'])}
                            <td>{$user->sh_job_name}</td>{/if}
                        {if in_array('start_working_with_org_date', $smarty.session['report_data']['dataSelected'])}
                            <td align="center">{getdate type=show row=$user col=sh_uao_job_date_of_appointment}</td>{/if}
                        {if in_array('end_working_with_org_date', $smarty.session['report_data']['dataSelected'])}
                            <td align="center">{getdate type=show row=$user col=sh_uao_job_end_date_of_appointment}</td>{/if}
                        {if in_array('template', $smarty.session['report_data']['dataSelected'])}
                            <td>{$user->prl_templates_name}</td>{/if}
                        {if in_array('basic_salary', $smarty.session['report_data']['dataSelected'])}
                            <td>{$user->sh_uao_salary_basic}</td>{/if}
                        {if in_array('account_name', $smarty.session['report_data']['dataSelected'])}
                            <td>{$user->sh_user_bank_name}</td>{/if}
                        {if in_array('bank_number', $smarty.session['report_data']['dataSelected'])}
                            <td>{$user->sh_user_bank_number}</td>{/if}
                    </tr>
                {/foreach}
                </tbody>
            </table>
        {/if}
        </div>
    </div>
{/block}

