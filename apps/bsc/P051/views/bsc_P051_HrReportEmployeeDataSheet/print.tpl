{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}

    <div class="row">
        <h4 class="text-center well">{#p_employee_data_sheet_reprot#}</h4>
    </div>

    <div class="horizontal-space"></div>

    <div class="row">

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-3 snsolabel">{#gnr_unit#}</div>
        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-8 snsoinput">{$unit->sh_unt_name}</div>

    </div>

    <div class="horizontal-space"></div>

    <div class="row">

        <div class="col-lg-12">

            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table searchable">
                <thead>
                <tr>
                    <th></th>
                    <th>{#gnr_name#}</th>
                    <th>{#p_identity_number#}</th>
                    {if in_array('nationality', $smarty.session['report_data']['dataSelected'])}<th>{#gnr_nationality#}</th>{/if}
                    {if in_array('sex', $smarty.session['report_data']['dataSelected'])}<th>{#gnr_sex#}</th>{/if}
                    {if in_array('unit', $smarty.session['report_data']['dataSelected'])}<th>{#gnr_unit#}</th>{/if}
                    {if in_array('basic_job', $smarty.session['report_data']['dataSelected'])}<th>{#p_basic_job#}</th>{/if}
                    {if in_array('start_working_with_org_date', $smarty.session['report_data']['dataSelected'])}<th>{#p_start_working_with_organization_date#}</th>{/if}
                    {if in_array('end_working_with_org_date', $smarty.session['report_data']['dataSelected'])}<th>{#p_end_working_with_organization_date#}</th>{/if}
                    {if in_array('template', $smarty.session['report_data']['dataSelected'])}<th>{#p_template#}</th>{/if}
                    {if in_array('basic_salary', $smarty.session['report_data']['dataSelected'])}<th>{#p_basic_salary#}</th>{/if}
                    {if in_array('account_name', $smarty.session['report_data']['dataSelected'])}<th>{#p_bank_account_name#}</th>{/if}
                    {if in_array('bank_number', $smarty.session['report_data']['dataSelected'])}<th>{#p_ayban_number#}</th>{/if}
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $users as $user}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td>{$user->sh_user_full_name}</td>
                        <td>{$user->sh_user_identity_number}</td>
                        {if in_array('nationality', $smarty.session['report_data']['dataSelected'])}<td>{getname table=st_country id=$user->sh_user_nationality}</td>{/if}
                        {if in_array('sex', $smarty.session['report_data']['dataSelected'])}<td>{getname table=st_setting id=$user->sh_user_gender}</td>{/if}
                        {if in_array('unit', $smarty.session['report_data']['dataSelected'])}<td>{$user->sh_unt_name}</td>{/if}
                        {if in_array('basic_job', $smarty.session['report_data']['dataSelected'])}<td>{$user->sh_job_name}</td>{/if}
                        {if in_array('start_working_with_org_date', $smarty.session['report_data']['dataSelected'])}<td align="center">{getdate type=show row=$user col=sh_uao_job_date_of_appointment}</td>{/if}
                        {if in_array('end_working_with_org_date', $smarty.session['report_data']['dataSelected'])}<td align="center">{getdate type=show row=$user col=sh_uao_job_end_date_of_appointment}</td>{/if}
                        {if in_array('template', $smarty.session['report_data']['dataSelected'])}<td>{$user->prl_templates_name}</td>{/if}
                        {if in_array('basic_salary', $smarty.session['report_data']['dataSelected'])}<td>{$user->prl_trans_basic_salary}</td>{/if}
                        {if in_array('account_name', $smarty.session['report_data']['dataSelected'])}<td>{$user->sh_user_bank_name}</td>{/if}
                        {if in_array('bank_number', $smarty.session['report_data']['dataSelected'])}<td>{$user->sh_user_bank_number}</td>{/if}
                    </tr>
                {/foreach}
                </tbody>
            </table>

        </div>

    </div>
{/block}
