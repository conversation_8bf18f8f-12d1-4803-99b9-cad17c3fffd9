{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}

{block name="body"}
    <div class="row snsowraper">
        <div class="widget">
            <h4>{#p_employee_report#}:</h4>
            <div class="widget-body bordered-left bordered-palegreen">
                <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                    <thead>
                    <tr style="background-color: #A0D468 !important;">
                        <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                        <th style="background-color: #A0D468 !important;" width="35%"> {#gnr_employee#} </th>
                        <th style="background-color: #A0D468 !important;" width="20%"> {#gnr_management_unit#} </th>
                        <th style="background-color: #A0D468 !important;" width="15%"> {#gnr_job#} </th>
                        <th style="background-color: #A0D468 !important;" width="25%" class="text-center">{#p_job_type#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $employees as $employee}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>
                                {$employee->sh_user_full_name}
                            </td>
                            <td>{$employee->oneVacant->unit->sh_unt_name}</td>
                            <td>{$employee->oneVacant->job->sh_job_name}</td>
                            <td align="center">
                                {*<a data-toggle="modal" data-target="#modal"*}
                                {*href="{url check=0 urltype="path"*}
                                {*url_string="bsc/P051/employees/employeeJobs/0/{$smarty.session.lang}/{$employee->sh_user_id}"}"*}
                                {*class="btn btn-default btn-sm">*}


                                {if $employee['vacancies_count'] gte 2 }
                                    <i class='fa fa-tasks '>
                                <span class="ml-1">
                                    {$employee['vacancies_count']}
                                </span>
                                    </i>
                                {else}
                                    {if $employee->oneVacant->sh_uao_basic eq 1}
                                        {#gnr_primary#}
                                    {else}
                                        {#gnr_secondary#}
                                    {/if}
                                {/if}
                                {*</a>*}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

{/block}