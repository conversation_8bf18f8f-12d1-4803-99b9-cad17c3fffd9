{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>{/block}
{block name=page_body}
    <div class="row">
        <div class ="col-lg-1">
            <label class="mt-1" for="">{#p_unit_or_depart#}</label>
        </div>
        <div class="col-lg-11">
            <form action="{url urltype=path url_string="bsc/P051/employees/show/0/{$smarty.session.lang}/save_session"}" method="post">
                <select name="unit_id" onchange="this.form.submit()" required>
                    <option>{#p_choose_unit#}</option>
                    <option {if $smarty.session.unit_id eq "all"} selected {/if}
                            value="all">{#gnr_select_all#}</option>
                    {foreach $units as $unit}
                        <option {if $smarty.session.unit_id eq $unit->sh_unt_id} selected {/if}
                                value="{$unit->sh_unt_id}">{$unit->sh_unt_name}</option>
                    {/foreach}
                </select>
            </form>
        </div>
    </div>

    <div class="horizontal-space"></div>

    <div class="widget">
        <div class="widget-header bg-blue">
            <i class="widget-icon fa fa-arrow-left"></i>
            <span class="widget-caption">{#gnr_search#}</span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                    <i class="fa fa-minus"></i>
                </a>
            </div><!--Widget Buttons-->
        </div><!--Widget Header-->
        <div class="widget-body">
            <form action="{url urltype=path url_string="bsc/P051/employees/show/0/{$smarty.session.lang}/search"}"
                    method="post">
                <div class="row">
                    <div class="col-lg-6">

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_job#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <select name="vacancyId">
                                <option value="">{#gnr_unspecified#}</option>
                                {foreach $vacancies as $vacancy}
                                    <option value="{$vacancy->sh_job_id}"
                                        {if $vacancy->sh_job_id eq $smarty.session.search_employees['vacancyId']}
                                            selected
                                        {/if}>
                                        {$vacancy->sh_job_name}
                                    </option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_employee#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <input type="text"
                                class="form-control"
                                name="name"
                                value="{$smarty.session.search_employees['name']}">
                        </div>
                        

                    </div>
                    <div class="col-lg-3">

                        <div class="checkbox">
                            {foreach $sexList as $setting}
                                {if $setting->id neq 53}
                                    <label>
                                        <input type="checkbox" name="filters[]"
                                               value="{$setting->id}"
                                                {if in_array($setting->id,$smarty.session.search_employees['filters'])}
                                                    checked
                                                {/if}>
                                        <span class="text">{$setting->translatedName}</span>
                                    </label>
                                    <br>
                                {/if}
                            {/foreach}

                            <div class="horizontal-space"></div>

                        </div>

                    </div>
                    <div class="col-lg-3">
                        <button type="submit" class="btn btn-default shiny">{#gnr_search#}</button>
                    </div>
                </div>
            </form>

            <br>
        </div><!--Widget Body-->
    </div>

    {if $smarty.session.search_employees}
        <div class="well text-center">
            {url urltype=alink
                url_string="bsc/P051/employees/show/0/{$smarty.session.lang}/cancel_search"
                text_value="{#gnr_cancel#}"}
        </div>
    {/if}

    {if $employees->count() neq 0 }
        <div class="mb-1">
            {url check=0 urltype="alinkn"
                url_string="bsc/P051/employees/employeeReportPrint/0/{$smarty.session.lang}/{$employee->id}"
                text_value="{#gnr_print#}" style="btn btn-defualt"}
        </div>
    {/if}

    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
        <thead>
        <tr style="background-color: #A0D468 !important;">
            <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
            <th style="background-color: #A0D468 !important;" width="35%"> {#gnr_employee#} </th>
            <th style="background-color: #A0D468 !important;" width="20%"> {#gnr_management_unit#} </th>
            <th style="background-color: #A0D468 !important;" width="15%"> {#gnr_job#} </th>
            <th style="background-color: #A0D468 !important;" width="25%">{#p_job_type#}</th>
        </tr>
        </thead>
        <tbody>
        {if $employees->count() eq 0}
            <tr><td colspan="5" class="text-center">{#gnr_no_data#}</td></tr>
        {/if}
        {$i=1}
        {foreach $employees as $employee}
            <tr>
                <td align="center">{$i++}</td>
                <td>
                    {$employee->sh_user_full_name}
                </td>
                <td>{$employee->oneVacant->unit->sh_unt_name}</td>
                <td>{$employee->oneVacant->job->sh_job_name}</td>
                <td align="center">
                    <a data-toggle="modal" data-target="#modal"
                        href="{url check=0 urltype="path"
                        url_string="bsc/P051/employees/employeeJobs/0/{$smarty.session.lang}/{$employee->sh_user_id}"}"
                        class="btn btn-default btn-sm">


                        {if $employee['vacancies_count'] gte 2 }
                            <i class='fa fa-tasks '>
                                <span class="ml-1">
                                    {$employee['vacancies_count']}
                                </span>
                            </i>
                        {else}
                            {if $employee->oneVacant->sh_uao_basic eq 1}
                                {#gnr_primary#}
                            {else}
                                {#gnr_secondary#}
                            {/if}
                        {/if}
                    </a>
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
    {*{if not $smarty.session.s_employee_name}*}
        {*<div class="text-center margin-top-20">*}
            {*{paginate startpage=$smarty.session.s_startpage lastpage=$smarty.session.s_lastpage}*}
        {*</div>*}
    {*{/if}*}
{/block}
{block name=page_header}

    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
//        function exportTo(ID, type) {
//            $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
//        }
        {/literal}
    </script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [

                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                    $("tfoot input").keyup(function() {
                        /* Filter on the column (the index) of this element */
                        oTable.fnFilter(this.value, $("tfoot input").index(this));
                    });
                }

            };

        }();

//        InitiateSimpleDataTable.init();
    </script>

{/block}