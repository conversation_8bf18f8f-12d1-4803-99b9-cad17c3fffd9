{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_adjust_employee_leave#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="row col-lg-12">
                <form method="post" action='{url urltype="path" url_string="bsc/P051/employees/leavesetting/0/{$smarty.session.lang}/updatecredit/{$smarty.session.s_employees_token}/{$row->id}"}'>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_leave_type#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getname table=hr_levst id=$row->leave_id}</div>

                    {if $row->leaveType->annual_intrvl_type neq LeaveType::CREDIT_TYPE_OPEN}

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_leave_credit#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <input type="number" name="credit" class="hide-spinner"  value="{$row->credit}" placeholder="{#p_leave_credit#}">&nbsp;{#gnr_day#}
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_max_credit_limit#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            {if $row->leaveType->transfer_type eq LeaveType::WITHOUT_TRANSFER}
                                <span>{#p_untransferable_leave#}</span>
                            {/if}
                            {if $row->leaveType->transfer_type eq LeaveType::TRANSFER_WITHOUT_LIMIT}
                                <span>{#p_unlimited#}</span>
                            {/if}
                            {if $row->leaveType->transfer_type eq LeaveType::TRANSFER_WITH_LIMIT}
                                <input type="number" class="hide-spinner" name="max_credit" value="{$row->max_credit}" placeholder="{#p_allowedleave_max_credit#}">
                                {#gnr_day#}
                            {/if}
                        </div>

                    {/if}

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_start_counting_leave_date#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getdate table=hr_allowedleave col=created_date type=showauto row=$row->created_date}</div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}