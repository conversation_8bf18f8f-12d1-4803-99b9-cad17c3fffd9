{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}
    <h5 class="row-title" style="margin-right: 4px;">
        <i class="fa fa-user sky"></i>
        {getname table=sh_user id=$allowedLeave->user_id}
        &nbsp;&raquo;&nbsp;
        {$allowedLeave->leaveType->name}
        {url check=0 urltype="mbutton" url_string="gnr/X000/reportView/reportView/0/ar/leaveAllowedCardHTML/&p[id]={$allowedLeave->id}" text_value="<i class='fa fa-question-circle'></i>" modal="modal"}
        {url urltype="mbutton" url_string="gnr/X000/mediacenter/leaves/0/{$smarty.session.lang}/{$allowedLeave->id}" text_value="<i class='fa fa-file-text'></i>&nbsp;{#gnr_emloyees_requests#}" modal="modal"}
        {*{url urltype="mbutton" url_string="bsc/P051/employees/confirmdeleteleavehistory/0/{$smarty.session.lang}" text_value="<i class='fa fa-file-text'></i>&nbsp;حذف السجل" modal="modal"}*}
    </h5>
    {if $allowedLeave->leaveType->annual_intrvl_type eq 830}
        <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
            <thead>
            <tr>
                <th width="5%"></th>
                <th width="20%">{#gnr_year_month#}</th>
                <th width="75%">{#gnr_consumed_credit#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {$credit=0}
            {foreach $historyList as $row}
                <tr>
                    <td align="center">{$i++}</td>
                    <td align="center">{$row->year}/{$row->month}</td>
                    <td align="center">{$row->used_credit}&nbsp;{#gnr_day#}</td>
                </tr>
            {/foreach}
            </tbody>
            <thead>
            <tr>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="right" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
            </tr>
            </thead>
        </table>
    {/if}

    {if $allowedLeave->leaveType->annual_intrvl_type eq 831}
        <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
            <thead>
            <tr>
                <th width="5%"></th>
                <th width="15%">{#gnr_year_month#}</th>
                <th width="10%">{#p_annual_leave_credit#}</th>
                <th width="10%">{#p_monthly_credit#}</th>
                <th width="10%">{#gnr_consumed_credit#}</th>
                <th width="10%">{#p_added_credit#}</th>
                <th width="10%">{#p_deducted_credit#}</th>
                <th width="10%">{#gnr_absent#}</th>
                <th width="10%">{#gnr_latency#}</th>
                <th width="10%">{#gnr_credit#}</th>
                <th width="10%">{#p_transfer_credit#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {$credit=0}
            {foreach $historyList as $row}
                <tr>
                    <td align="center">{$i++}</td>
                    <td align="center">{$row->year}/{$row->month}</td>
                    <td align="center">{$row->leaveAllowed->credit}</td>
                    <td align="center">{$row->month_credit}&nbsp;{#gnr_day#}<span style="display: none;">{$credit = $credit + $row->credit}</span></td>
                    <td align="center">{$row->used_credit}</td>
                    <td align="center">{$row->added_credit}</td>
                    <td align="center">{$row->deleted_credit}</td>
                    <td align="center">{$row->absence_credit}</td>
                    <td align="center">{$row->latency_credit}</td>
                    <td align="center">{$row->credit}&nbsp;{#gnr_day#}<span style="display: none;">{$credit = $credit + $row->credit}</span></td>
                    <td align="center">{$row->breakpoint}</td>
                </tr>
            {/foreach}
            </tbody>
            <thead>
            <tr>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">{$row->credit} &nbsp;{#gnr_day#}</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
            </tr>
            </thead>
        </table>
    {/if}

    {if $allowedLeave->leaveType->annual_intrvl_type eq 837}
        <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
            <thead>
            <tr>
                <th width="5%"></th>
                <th width="15%">{#gnr_year_month#}</th>
                <th width="10%">{#p_annual_leave_credit#}</th>
                <th width="10%">{#gnr_consumed_credit#}</th>
                <th width="10%">{#p_added_credit#}</th>
                <th width="10%">{#p_deducted_credit#}</th>
                <th width="10%">{#gnr_absent#}</th>
                <th width="10%">{#gnr_latency#}</th>
                <th width="10%">{#gnr_credit#}</th>
                <th width="10%">{#p_transfer_credit#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {$credit=0}
            {foreach $historyList as $row}
                <tr>
                    <td align="center">{$i++}</td>
                    <td align="center">{$row->year}/{$row->month}</td>
                    <td align="center">{$row->allowed_credit}</td>
                    <td align="center">{$row->used_credit}</td>
                    <td align="center">{$row->added_credit}</td>
                    <td align="center">{$row->deleted_credit}</td>
                    <td align="center">{$row->absence_credit}</td>
                    <td align="center">{$row->latency_credit}</td>
                    <td align="center">{$row->credit}&nbsp;{#gnr_day#}<span style="display: none;">{$credit = $credit + $row->credit}</span></td>
                    <td align="center">{$row->breakpoint}</td>
                </tr>
            {/foreach}
            </tbody>
            <thead>
            <tr>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">{$row->credit} &nbsp;{#gnr_day#}</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
            </tr>
            </thead>
        </table>
    {/if}

    {if $allowedLeave->leaveType->annual_intrvl_type eq 894}
        <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
            <thead>
            <tr>
                <th width="5%"></th>
                <th width="20%">{#gnr_year_month#}</th>
                <th width="75%">{#gnr_consumed_credit#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {$credit=0}
            {foreach $historyList as $row}
                <tr>
                    <td align="center">{$i++}</td>
                    <td align="center">{$row->year}/{$row->month}</td>
                    <td align="center">{$row->used_credit}</td>
                </tr>
            {/foreach}
            </tbody>
            <thead>
            <tr>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
                <td align="center" style="background-color: #0a2b1d !important; color: white;">&nbsp;</td>
            </tr>
            </thead>
        </table>
    {/if}

{/block}
{block name=back}{url urltype="path" url_string="bsc/P051/employees/leavesetting/0/{$smarty.session.lang}"}{/block}