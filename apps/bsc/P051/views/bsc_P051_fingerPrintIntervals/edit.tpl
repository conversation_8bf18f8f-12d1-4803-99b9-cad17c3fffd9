{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#p_edit_attendance_record#}</h4>
	</div>
	<div class="modal-body">
		<form  method="post" action='{url urltype="path" url_string="bsc/P051/fingerPrintIntervals/show/0/{$smarty.session.lang}/update/{$smarty.session.s_fingerPrintIntervals_token}/{$interval->id}"}'>
			<div class="row">
				<div class="col-lg-12">

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_period_name#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="name" value="{$interval->name}" placeholder="{#p_record_name#}" required></div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_date_start#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate table=fp_interval col=start_date type=edit row=$interval}</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_date_end#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate table=fp_interval col=end_date type=edit row=$interval}</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn sharp btn-warning">{#gnr_update#}</button></div>
					
				</div>
			</div>
		</form>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}
