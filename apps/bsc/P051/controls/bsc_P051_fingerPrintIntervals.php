<?php
// Deny Direct Script Access
use Models\HumanResource\FingerPrint\Data;
use Models\HumanResource\FingerPrint\Day;

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bsc_P051_fingerPrintIntervals extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case Operation::INSERT:

                if ($_SESSION['s_fingerPrintIntervals_token'] === $parm[1]) {

                    $this->createInterval($post);

                }

                break;

            case Operation::UPDATE:

                if ($_SESSION['s_fingerPrintIntervals_token'] === $parm[1]) {

                    $this->updateInterval($post, $parm[2]);

                }

                break;

            case Operation::DELETE:

                if ($_SESSION['s_fingerPrintIntervals_token'] === $parm[1]) {

                    try {

                        $interval = fp_interval::readByID((int)$parm[2]);
                        $interval->delete();

                        try{
                            fp_data::simpleDeleteByProperty([FingerPrintData::INTERVAL_ID => $interval->fp_interval_id]);
                        }catch (ModelException $e){

                        }

                        Notification::deletedAlert();

                    } catch (ModelException $e) {

                    }

                }

                break;
        }

        try {

            $this->Smarty->assign('intervals', FingerPrintInterval::read([
                FingerPrintInterval::ORG_ID => $_SESSION['organization']->id,
            ], [
                0 => [
                    'property' => FingerPrintInterval::ID,
                    'sort' => 'DESC'
                ]
            ]));

        } catch (FingerPrintIntervalException $e) {

            $this->Smarty->assign('intervals', []);

        }

        $_SESSION['s_fingerPrintIntervals_token'] = Helper::generateToken();
    }

    public function add($parm, $post)
    {
        $_SESSION['s_fingerPrintIntervals_token'] = Helper::generateToken();
    }

    public function edit($parm, $post)
    {

        try {
            $this->Smarty->assign('interval', FingerPrintInterval::readID((int)$parm[0] ?? 0));
        } catch (FingerPrintIntervalException $e) {
            $this->Smarty->assign('interval', []);
        }

        $_SESSION['s_fingerPrintIntervals_token'] = Helper::generateToken();
    }

    public function confirm($parm, $post)
    {

        try {
            $this->Smarty->assign('interval', FingerPrintInterval::readID((int)$parm[0] ?? 0));
        } catch (FingerPrintIntervalException $e) {
            $this->Smarty->assign('interval', []);
        }

        $_SESSION['s_fingerPrintIntervals_token'] = Helper::generateToken();
    }

    public function getdocs($parm, $post)
    {

        try {

            $this->Smarty->assign('data', FingerPrintData::readID((int)$parm[0]));

        } catch (FingerPrintDataException $e) {

            $this->Smarty->assign('data', []);

        }

        $_SESSION['s_fingerPrintIntervals_token'] = Helper::generateToken();
    }

    public function dataBrowse($parm, $post)
    {

        $data =  Data::with([
            'interval' => function($query){
                $query->select('fp_interval_id' , 'fp_interval_name' , 'fp_interval_start_date' , 'fp_interval_end_date');
            },
            'device' => function($query){
                $query->select('fp_dev_name' , 'fp_dev_id');
            }
        ])
            ->select('fp_data_id' , 'fp_data_string' , 'fp_data_interval_id'  ,'fp_data_device_id')

            ->find($parm[0]);

        $days = Day::where('fp_day_device_id' , $data->device_id)->with([
            'user' => function($query){
                $query->select('sh_user_full_name' , 'sh_user_id');
            },
            'doam' => function($query){
                $query->select('hr_doam_name' , 'hr_doam_id');
            },
        ])->select(
            'fp_day_workday_or_weekend' , 'fp_day_fingerprint_data_origin' , 'fp_day_fingerprint_data_edited' ,
            'fp_day_date' , 'fp_day_device_id' , 'fp_day_user_id' ,
            'fp_day_doam_id' , 'fp_day_id')
            ->orderBy('fp_day_id', 'desc')
            ->paginated(parent::PER_PAGE);

//        return $days;

//        try {
//            $data = FingerPrintData::readID((int)$parm[0] ?? 0);
//
//        } catch (FingerPrintDataException $e) {
//            $data = null;
//        }

        if ($data) {

            $this->Smarty->assign('data', $data);
            $this->Smarty->assign('days', $days);
            $this->Smarty->assign('daysList', json_decode($data->string, true));

//            try {
//
////                $this->Smarty->assign('days', FingerPrintDay::read([
////                    FingerPrintDay::ORG_ID => $data->org_id,
////                    FingerPrintDay::DEVICE_ID => $data->device_id
////                ]));
//
//            } catch (FingerPrintDayException $e) {
//                $this->Smarty->assign('days', []);
//            }

        }


        $_SESSION['s_fingerPrintIntervals_token'] = Helper::generateToken();
    }

    public function deviceList($parm, $post)
    {

        switch ($parm[0]) {

            case 'save_session':
                $_SESSION['s_interval_id'] = $parm[1];
                break;

            case 'readFingerPrintFile':

                if ($_SESSION['s_fingerPrintIntervals_token'] === $parm[1]) {

                    try {
                        $i = 0;
                        FingerPrintData::readFingerPrintDataFile($_FILES, (int)$parm[2]);

                    } catch (DocumentException $e) {

                        switch ($e->getCode()) {

                            case 404041:

                                Notification::alertMessage(Notification::SUCCESS, 'no_employees_connected_with_finger_print_device');

                                break;

                            case 404042:

                                Notification::alertMessage(Notification::SUCCESS, 'no_data_extracted_from_finger_print_data_file');

                                break;

                            case 404043:

                                Notification::alertMessage(Notification::SUCCESS, 'file_is_not_exist_or_readable');

                                break;

                            case 404044:

                                Notification::alertMessage(Notification::WARNING, 'data_manipulation_failed_check_interval_or_file_pattern');

                                break;

                        }

                    }

                }

                break;

        }

        try {

            $this->Smarty->assign('interval', FingerPrintInterval::readID((int)$_SESSION['s_interval_id'] ?? 0));

        } catch (FingerPrintIntervalException $e) {

            $this->Smarty->assign('interval', []);

        }

    }

    public function DoamTypesList($parm, $post)
    {
        $this->Smarty->assign('IntervalID', (int)$parm[0] ?? 0);

        try {

            $this->Smarty->assign('DoamList', Doam::read([Doam::ORG_ID => $_SESSION['organization']->id]));

        } catch (DoamException $e) {

            $this->Smarty->assign('DoamList', []);

        }

    }

    public function Employees($parm, $post)
    {

        try {
            $interval = FingerPrintInterval::readID((int)$parm[0]);
            $this->Smarty->assign('interval', $interval);
        } catch (FingerPrintIntervalException $e) {
            $this->Smarty->assign('interval', []);
        }

        try {
            $doam = Doam::readID((int)$parm[1]);
            $this->Smarty->assign('doam', $doam);
        } catch (DoamException $e) {
            $this->Smarty->assign('doam', []);
        }

        if ($parm[1] && $doam) {
            $this->Smarty->assign('employees', Vacant::getEmployeesByAttendance($_SESSION['organization'], 1, $doam->id));
        } else {
            $this->Smarty->assign('employees', Vacant::getEmployeesByAttendance($_SESSION['organization'], 2));
        }

    }

    public function Records($parm, $post)
    {
        $x = 0 ;

        switch ($parm[3]) {

            case 'updateFingerPrintRecord':

                if ($_SESSION['s_fingerPrintIntervals_token'] === $parm[4]) {

                    if (FingerPrintData::UpdateEmployeeFingerPrintData($post, (int)$parm[5])) {

                        Notification::updatedAlert();

                    } else {

                        Notification::alertMessage(Notification::WARNING, 'sorry_finger_print_records_is_not_correct');

                    }

                }

                break;

            case 'resetFingerPrintRecord':

                if ($_SESSION['s_fingerPrintIntervals_token'] === $parm[4]) {

                    FingerPrintData::ResetEmployeeFingerPrintData((int)$parm[5]);
                    Notification::createdAlert();

                }

                break;

        }

        try {
            $interval = FingerPrintInterval::readID((int)$parm[0] ?? 0);
        } catch (FingerPrintIntervalException $e) {
            $interval = null;
        }

        $this->Smarty->assign('interval', $interval);

        try {
            $doam = Doam::readID((int)$parm[1] ?? 0);
        } catch (DoamException $e) {
            $doam = null;
        }

        $this->Smarty->assign('doam', $doam);

        try {
            $employee = Vacant::readID((int)$parm[2] ?? 0);
        } catch (VacantException $e) {
            $employee = null;
        }

        $this->Smarty->assign('employee', $employee);

        if ($employee && $doam && $interval) {

            try {

                $fingerPrintRecords = FingerPrintDay::read([
                    FingerPrintDay::ORG_ID => $interval->org_id,
                    FingerPrintDay::DOAM_ID => $doam->id,
                    FingerPrintDay::INTERVAL_ID => $interval->id,
                    FingerPrintDay::USER_ID => $employee->user_id
                ], [
                    0 => [
                        'property' => FingerPrintDay::DATE,
                        'sort' => 'ASC'
                    ]
                ]);
//                $fingerPrintRecords->updateFingerPrintDay();
                $this->Smarty->assign('fingerPrintRecords', $fingerPrintRecords);

            } catch (FingerPrintDayException $e) {

                $this->Smarty->assign('fingerPrintRecords', []);

            }

        }

        $_SESSION['s_fingerPrintIntervals_token'] = Helper::generateToken();
    }

    public function dayWrdiahFingerPrintBrowse($parm, $post)
    {

        try {
            $interval = FingerPrintInterval::readID((int)$parm[0] ?? 0);
        } catch (FingerPrintIntervalException $e) {
            $interval = null;
        }

        $this->Smarty->assign('interval', $interval);

        try {
            $doam = Doam::readID((int)$parm[1] ?? 0);
        } catch (DoamException $e) {
            $doam = null;
        }

        $this->Smarty->assign('doam', $doam);

        try {
            $employee = Vacant::readID((int)$parm[2] ?? 0);
        } catch (VacantException $e) {
            $employee = null;
        }

        $this->Smarty->assign('employee', $employee);

        try {

            $fingerPrintDay = FingerPrintDay::readID((int)$parm[3] ?? 0);

        } catch (FingerPrintDayException $e) {
            $fingerPrintDay = null;
        }

        $this->Smarty->assign('day', $fingerPrintDay);

        if ($fingerPrintDay && $doam) {

            $cases = [];

            $wrdias = Wrdiah::getWrdiahForSpecificDayInWeek((int)$doam->id, (int)$fingerPrintDay->day_in_week);
            $this->Smarty->assign('WrdiahList', $wrdias);

            if ($wrdias) {

                $i = 0;
                foreach ($wrdias as $key => $wrdia) {

                    $cases[$key] = new FingerPrintCase($wrdia, $wrdias[$i - 1], $wrdias[$i + 1], hr_doam::readByID((int)$wrdia->doam_id), $fingerPrintDay);
                    $i++;

                }

                $this->Smarty->assign('cases', $cases);

            }

        }

    }

    public function createInterval($post)
    {

        $interval = new FingerPrintInterval();
        $interval->bindProperties($post);
        $interval->org_id = $_SESSION['organization']->id;
        $interval->created_by = $_SESSION['user']->id;
        $interval->created_date = date('Y-m-d');
        $interval->save();

        Notification::createdAlert();

    }

    public function updateInterval($post, int $id)
    {

        try {

            $interval = FingerPrintInterval::readID((int)$id);
            $interval->bindProperties($post);
            $interval->save();

            Notification::updatedAlert();

        } catch (FingerPrintIntervalException $e) {

        }

    }

}