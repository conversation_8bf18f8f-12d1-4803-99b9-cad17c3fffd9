<?php

/**
 * Created by PhpStorm.
 * User: joseph
 * Date: 28/03/19
 * Time: 03:00 م
 */
class bsc_P051_HrEmployeesLeavesCredit extends controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]){
            case 'menu':
                $_SESSION['report_data'] = null;
                break;

            case 'search':
                $_SESSION['report_data'] = $post;
                break;
        }

        try {
            $this->Smarty->assign('employees', Vacant::getEmployeeList($_SESSION['organization']));
        } catch (UserException $e) {
            $this->Smarty->assign('employees', []);
        }
        if ($parm[0] == 'search') {
            if ($post['employee_id']){
                $employeeId = $_SESSION['employee_id'] = $post['employee_id'];
            }else{
                $employeeId = $_SESSION['employee_id'] = $parm[1];
            }

            LeaveData::updateEmployeeLeavesCredit($_SESSION['organization']->id, (int)$employeeId);
            $employee = DB::table('sh_uao')
                ->where('sh_uao_user_id', $employeeId)->first();

            // Credit
            try {
                $this->Smarty->assign('crediteditList', Request::read([
                    Request::ORG_ID => $_SESSION['organization']->id,
                    Request::USER_ID => (int)$employeeId,
                    Request::PRG_ID => Program::PROGRAM_HUMAN_RESOURCE_P051,
                    Request::TABLE_NAME => hr_creditedit::class
                ],
                    [
                        0 => [
                            'property' => Request::ID,
                            'sort' => 'DESC'
                        ]
                    ]));
            } catch (RequestException $e) {
            }

            try {
                $this->Smarty->assign('editcreditrequest_grf', wf_graph::count([Graph::OPR_ID => 650, Graph::STATUS => 1]));
            } catch (ModelException $e) {
                $this->Smarty->assign('editcreditrequest_grf', 0);
            }

            try {
                $this->Smarty->assign('Allowedleaves', LeaveAllowed::read([
                    LeaveAllowed::ORG_ID => (int)$_SESSION['organization']->id,
                    LeaveAllowed::USER_ID => (int)$employeeId ?? 0
                ]));
            } catch (LeaveAllowedException $e) {
            }

            try {
                $uaoRow = Vacant::readID($employee->sh_uao_id ?? 0);
                $this->Smarty->assign('uaoRow', $uaoRow);
            } catch (VacantException $e) {
                $this->Smarty->assign('uaoRow', []);
            }

            try {
                $this->Smarty->assign('userRow', User::readID($uaoRow->user_id ?? 0));
            } catch (UserException $e) {
                $this->Smarty->assign('userRow', []);
            }

            if (!empty($uaoRow->att_doam_id)) {

                try {

                    $employeeDoam = Doam::readID((int)$uaoRow->att_doam_id ?? 0);
                    $workDays = explode(',', $employeeDoam->workdays);
                    $this->Smarty->assign('workDays', $workDays ? $workDays : []);

                } catch (DoamException $e) {

                    $this->Smarty->assign('workDays', []);

                }

            }

            $this->Smarty->assign('week_days', Setting::getList(25));

        }
    }

    public function leaveshistory($parm, $post)
    {

        try {

            $leaveAllowed = LeaveAllowed::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('allowedLeave', $leaveAllowed);

        } catch (LeaveAllowedException $e) {

            $this->Smarty->assign('allowedLeave', []);

        }

        if ($leaveAllowed) {

            LeaveData::updateEmployeeLeavesCredit($leaveAllowed->org_id, $leaveAllowed->user_id);

            try {

                $this->Smarty->assign('historyList', LeaveData::read([
                    LeaveData::ORG_ID => $leaveAllowed->org_id,
                    LeaveData::USER_ID => $leaveAllowed->user_id,
                    LeaveData::LEAVE_ID => $leaveAllowed->leave_id
                ], [
                    0 => [
                        'property' => LeaveData::FIRST_OF_THE_MONTH,
                        'sort' => 'ASC'
                    ]
                ]));

            } catch (LeaveDataException $e) {
                $this->Smarty->assign('historyList', []);
            }

        }

    }

    public function employeeleavesetting($parm, $post)
    {
        try {
            $this->Smarty->assign('row', LeaveAllowed::readID((int)$parm[0] ?? 0));
        } catch (LeaveAllowedException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function employeeRecordPrint($parm, $post)
    {

        $user = DB::table(sh_user::class)
            ->where(User::ID, $_SESSION['employee_id'] ?? 0)
            ->first();

        try {
            $this->Smarty->assign('Allowedleaves', LeaveAllowed::read([
                LeaveAllowed::ORG_ID => (int)$_SESSION['organization']->id,
                LeaveAllowed::USER_ID => (int)$_SESSION['employee_id']?? 0
            ]));
        } catch (LeaveAllowedException $e) {

        }
        $this->Smarty->assign('user',$user);
        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));

    }

    public function employeeRecord($parm, $post)
    {
        switch ($parm[0]){
            case 'menu':
                $_SESSION['emp_record_data'] = null;
                $_SESSION['employee_id'] = $parm[1];
                break;

            case 'save_session':
                $_SESSION['employee_id'] = $post;
                break;
        }

        $user = DB::table(sh_user::class)
            ->where(User::ID, $_SESSION['employee_id'] ?? 0)
            ->first();

        if ($user){

            $_SESSION['emp_record_data']['from'] = $_SESSION['emp_record_data']['from'] ?? \Carbon\Carbon::now()->startOfMonth()->toDateString();
            $_SESSION['emp_record_data']['to'] = $_SESSION['emp_record_data']['to'] ?? \Carbon\Carbon::now()->endOfMonth()->toDateString();

            $activities = DB::table(sh_user_activity::class)
                ->where([
                    [UserActivity::ORG_ID, (int)$_SESSION['organization']->id],
                    [UserActivity::USER_ID, $user->sh_user_id],
                ])
                ->whereBetween(UserActivity::DATE, [
                    $_SESSION['emp_record_data']['from'],
                    $_SESSION['emp_record_data']['to']
                ])
                ->orderBy(UserActivity::ID, 'DESC')
                ->get();

            $activities->map(function ($activity){
                $activity->operations = explode(',', $activity->sh_user_activity_operations);
            });

            $this->Smarty->assign('user', $user);
            $this->Smarty->assign('activities', $activities);

        }

    }

}