<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bsc_P051_personnelMandateRequest extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case 'insert':

                if ($_SESSION['s_personnelMandateRequest_token'] == $parm[1]) {

                    if (hr_mndt::count([
                            HRMandateRequest::USER_ID => $post['user_id'],
                            HRMandateRequest::START_DATE => $this->Date->get_date('ad', $post['start_date']),
                            HRMandateRequest::END_DATE => $this->Date->get_date('ad', date("Y-m-d", strtotime($post['start_date'] . " + " . ($post['days_number'] - 1) . " day")))

                        ]) === 0
                    ) {

                        $mandate = new HRMandateRequest();
                        $mandate->bindProperties($post);
                        $mandate->org_id = $_SESSION['organization']->id;
                        $mandate->user_id = $post['user_id'];
                        $mandate->start_date = $this->Date->get_date('ad', $post['start_date']);
                        $mandate->end_date = $this->Date->get_date('ad', date("Y-m-d", strtotime($post['start_date'] . " + " . ($post['days_number'] - 1) . " day")));
                        $mandate->show_type = 1;
                        $mandate->created_by = $_SESSION['user']->id;
                        $mandate->created_date = date('Y-m-d');
                        if ($mandate->create(HRMandateRequest::HR_MANDATE_REQUEST_FROM_PERSONNEL)) {
                            Notification::createdAlert();
                        }

                    } else {
                        Notification::alertMessage(Notification::WARNING, 'sorry_this_employee_has_mandate_in_same_interval');
                    }


                }

                break;

            case 'update':

                if ($_SESSION['s_personnelMandateRequest_token'] == $parm[1]) {

                    try {

                        $mandate = HRMandateRequest::readID((int)$parm[2]);
                        $mandate->bindProperties($post);
                        $mandate->end_date = $this->Date->get_date('ad', date("Y-m-d", strtotime($post['start_date'] . " + " . ($post['days_number'] - 1) . " day")));
                        if ($mandate->save()) {
                            Notification::updatedAlert();
                        }

                    } catch (HRMandateRequestException $exception) {

                    }

                }

                break;

            case 'delete':

                if ($_SESSION['s_personnelMandateRequest_token'] == $parm[1]) {

                    try {

                        $mandate = HRMandateRequest::readID((int)$parm[2]);

                    } catch (HRMandateRequestException $exception) {

                    }

                    if ($mandate) {

                        try {

                            Request::deleteWFRequest(
                                $_SESSION['organization']->id,
                                $_SESSION['program']->id,
                                $mandate->user_id,
                                'mandate',
                                'hr_mndt',
                                $mandate->id);

                            Notification::deletedAlert();

                        } catch (RequestException $e) {

                        }

                    }

                }

                break;


                break;

        }
        try {
            $requests = Request::read([
                Request::TABLE_NAME => hr_mndt::class,
                Request::ORG_ID => $_SESSION['organization']->id,
                Request::PRG_ID => $_SESSION['program']->id,
            ]);
        } catch (RequestException $e) {
            $requests = [];
        }
        $this->Smarty->assign('requests', $requests);

        $_SESSION['s_personnelMandateRequest_token'] = md5(rand(0, 10000000));

    }

    public function add($parm, $post)
    {
        try {
            $this->Smarty->assign('employees', Vacant::getEmployeeList($_SESSION['organization']));
        } catch (VacantException $e) {
            $this->Smarty->assign('employees', []);
        }

        try {
            $this->Smarty->assign('grfExistenceNum', Graph::count([
                Graph::STATUS => 1,
                Graph::OPR_CODE => 'personnelMandateRequest'
            ]));

        } catch (ModelException $e) {
            $this->Smarty->assign('grfExistenceNum', 0);
        }

        try {
            $this->Smarty->assign('FeesGrfExistenceNum', Graph::count([
                Graph::STATUS => 1,
                Graph::OPR_CODE => 'mandatefees'
            ]));

        } catch (ModelException $e) {
            $this->Smarty->assign('FeesGrfExistenceNum', 0);
        }

        $_SESSION['s_personnelMandateRequest_token'] = md5(rand(0, 10000000));

    }

    public function edit($parm, $post)
    {
        try {
            $this->Smarty->assign('request', HRMandateRequest::readID((int)$parm[0]));
        } catch (HRMandateRequestException $exception) {
            $this->Smarty->assign('request', []);
        }

        try {
            $this->Smarty->assign('employees', Vacant::getEmployeeList($_SESSION['organization']));
        } catch (VacantException $e) {
            $this->Smarty->assign('employees', []);
        }

        try {
            $this->Smarty->assign('grfExistenceNum', Graph::count([
                Graph::STATUS => 1,
                Graph::OPR_CODE => 'personnelMandateRequest'
            ]));

        } catch (ModelException $e) {
            $this->Smarty->assign('grfExistenceNum', 0);
        }

        try {
            $this->Smarty->assign('FeesGrfExistenceNum', Graph::count([
                Graph::STATUS => 1,
                Graph::OPR_CODE => 'mandatefees'
            ]));

        } catch (ModelException $e) {
            $this->Smarty->assign('FeesGrfExistenceNum', 0);
        }

        $_SESSION['s_personnelMandateRequest_token'] = md5(rand(0, 10000000));
    }

    public function confirm($parm, $post)
    {

        try {
            $this->Smarty->assign('request', HRMandateRequest::readID((int)$parm[0]));
        } catch (HRMandateRequestException $e) {
            $this->Smarty->assign('request', []);
        }

        $_SESSION['s_employeeRequestsDashboard_token'] = md5(rand(0, 10000000));
    }
}