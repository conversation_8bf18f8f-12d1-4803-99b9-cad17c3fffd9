<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

use Models\EmployeePayrollTemplate;
use Models\User as UserModel;
use Models\HumanResource\Unit as UnitModel;

class bsc_P051_employees extends Controller
{
    const PAGINATION_LIMIT = 50;

    public function show($parm, $post, $file)
    {
        switch ($parm[0]) {
            case 'menu':
                $_SESSION['search_employees'] = null;
                $_SESSION['unit_id'] = null;
                break;
            case 'save_session':
                $_SESSION['unit_id']  = $post['unit_id'] ?? 0;
                break;
            case 'search':
                $_SESSION['search_employees'] = $post;
                break;
            case 'cancel_search':
                $_SESSION['search_employees'] = null;
                $_SESSION['unit_id'] = null;
                break;
        }



        $unitQuery = UnitModel::query();

        if(session('unit_id') && session('unit_id') !== 'all'){
            $unit = $unitQuery->with('vacancies.job')->find(session('unit_id'));
            $vacancies = collect($unit->vacancies)->map(function ($vacant){
                return $vacant->job;
            })->unique('sh_job_id');
        }else{
            $units = $unitQuery->with('vacancies.job')->get();
            $vacancies = collect($units)->map(function ($vacant){
                return collect($vacant->vacancies)->map(function($vacant){
                    return $vacant->job;
                })->unique('sh_job_id');
            })->flatten();
        }

        // Search & Filter *********************************************************************************************
        if ($_SESSION['search_employees']['name'] || $_SESSION['search_employees']['vacancyId']
            || $_SESSION['search_employees']['filters']) {

            if($_SESSION['search_employees']['name']){
                $trimmedName = str_replace(' ', '', $_SESSION['search_employees']['name']);
                $employees = UserModel::filterByFullName($trimmedName);

                $_SESSION['unit_id'] = null;
                $_SESSION['search_employees']['vacancyId'] = null;

            }

            if(session('search_employees.vacancyId')){
                $unit_id   = $_SESSION['unit_id'];
                $vacant_id = $_SESSION['search_employees']['vacancyId'];
                $employees = UserModel::filterByVacantAndUnit($unit_id, $vacant_id);
            }

            if($_SESSION['search_employees']['filters']){
                $genders = $_SESSION['search_employees']['filters'];
                $employees = UserModel::filterByGender($genders);
            }


        } else {
            $employees  = UserModel::filterByUnit($_SESSION['unit_id']);
        }

        // filters using only vacant
        if($post['unit_id'] == null && $post['vacancyId'] != ""){
            if($genders = $_SESSION['search_employees']['filters']){
                $employees = UserModel::filterByVacantAndGender($vacant_id, $genders);
            }else{
                $vacant_id = (int) $post['vacancyId'];
                $employees = UserModel::filterByVacantOnly($vacant_id );
            }
        }

        $this->Smarty->assign([
            'employees' => $employees,
            'units' => UnitModel::active()->get(),
            'vacancies' => $vacancies
        ]);

        $this->Smarty->assign('sexList', Setting::getList(27));

    }

    public function employeeReportPrint($parm, $post)
    {
        $this->show($parm, $post);
        DocumentProcessor::outputPDF(
            $this->Smarty->fetch(
                DocumentProcessor::setSmartyFetchConfig()
            )
        );
    }

    private function updateEmployeeSpecificPayroll($templateId, $userId)
    {
        $employeeSpecificTemplate = EmployeePayrollTemplate::where('user_id', (int)$userId ?? 0)
            ->get();
        foreach ($employeeSpecificTemplate as $temp) {
            $temp->template_id = $templateId;
            $temp->template->template_id = $templateId;
            $temp->update();
        }
    }

    public function contract($parm, $post, $file)
    {
        try {
            $this->Smarty->assign('vacant', Vacant::readID((int)$parm[0] ?? 0));
        } catch (VacantException $e) {
            $this->Smarty->assign('vacant', []);
        }

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));
    }

    public function documents($parm, $post, $files)
    {

        switch ($parm[0]) {

            case 'save_session':
                $_SESSION['s_doc_employee_user_id'] = $parm[1];
                break;

            case 'insert':

                if ($_SESSION['s_employees_token'] === $parm[1]) {

                    try {

                        $document = new Document();
                        $post['permission'] = isset($post['permission']) ? json_encode($post['permission']) : null;
                        $document->bindProperties($post);
                        $document->fileArray = $files['fileArray'];
                        $document->client_id = $_SESSION['organization']->id;
                        $document->user_id = $_SESSION['user']->id;
                        $document->operation_code = 'userdata';
                        $document->table_name = 'sh_user';
                        $document->row_id = (int)$_SESSION['s_doc_employee_user_id'] ?? 0;
                        $document->created_by = $_SESSION['user']->id;
                        $document->created_date = date('Y-m-d');
                        $document->save();

                    } catch (DocumentException $e) {

                    }

                }
                break;

            case 'update':

                if ($_SESSION['s_employees_token'] === $parm[1]) {

                    try {

                        $document = Document::readID((int)$parm[2] ?? 0);
                        $post['permission'] = isset($post['permission']) ? json_encode($post['permission']) : null;
                        $document->bindProperties($post);
                        $document->update();

                    } catch (DocumentException $e) {

                    }

                    $this->Smarty->assign('alertmessage', 'update');

                }

                break;

            case 'delete':

                if ($_SESSION['s_employees_token'] === $parm[1]) {

                    try {

                        $document = Document::readID((int)$parm[2] ?? 0);
                        $document->delete();

                    } catch (DocumentException $e) {

                    }

                    $this->Smarty->assign('alertmessage', 'delete');
                }

                break;
        }

        try {
            $this->Smarty->assign('user', UserModel::has('basicJob')->find((int)$_SESSION['s_doc_employee_user_id']));
        } catch (UserException $e) {
            $this->Smarty->assign('user', []);
        }

        try {

            $this->Smarty->assign('documents', Document::read([
                Document::CLIENT_ID => $_SESSION['organization']->id,
                Document::OPERATION_CODE => 'userdata',
                Document::ROW_ID => $_SESSION['s_doc_employee_user_id']
            ]));

        } catch (DocumentException $e) {
            $this->Smarty->assign('documents', []);
        }

    }

    public function employeeRecordPrint($parm, $post)
    {

        $this->employeeRecord($parm, $post);

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));

    }

    public function employeeRecord($parm, $post)
    {
        switch ($parm[0]) {
            case 'menu':
                $_SESSION['emp_record_data'] = null;
                $_SESSION['emp_user_id'] = $parm[1];
                break;

            case 'save_session':
                $_SESSION['emp_record_data'] = $post;
                break;
        }

        $user = UserModel::has('basicJob')->find((int) $_SESSION['emp_user_id']);
        if ($user) {

            $_SESSION['emp_record_data']['from'] = $this->Date->get_date('ad', date($_SESSION['emp_record_data']['from']))?? \Carbon\Carbon::now()->startOfMonth()->toDateString();
            $_SESSION['emp_record_data']['to'] = $this->Date->get_date('ad', date($_SESSION['emp_record_data']['to'])) ?? \Carbon\Carbon::now()->endOfMonth()->toDateString();

            $activities = DB::table(sh_user_activity::class)
                ->where([
                    [UserActivity::ORG_ID, (int)$_SESSION['organization']->id],
                    [UserActivity::USER_ID, $user->sh_user_id],
                ])
                ->whereBetween(UserActivity::DATE, [
                    $_SESSION['emp_record_data']['from'],
                    $_SESSION['emp_record_data']['to']
                ])
                ->orderBy(UserActivity::ID, 'DESC')
                ->get();

            $activities->map(function ($activity) {
                $activity->operations = explode(',', $activity->sh_user_activity_operations);
            });

            $this->Smarty->assign('user', $user);
            $this->Smarty->assign('activities', $activities);

        }

    }

    public function dismiss($parm, $post)
    {

        try {
            $this->Smarty->assign('vacant', Vacant::readID((int)$parm[0] ?? 0));
        } catch (VacantException $e) {
            $this->Smarty->assign('vacant', []);
        }

        $_SESSION['s_empdata_token'] = md5(rand(0, 99999));

    }

    public function settobasic($parm, $post)
    {
        try {

            $this->Smarty->assign('vacant', Vacant::readID((int)$parm[0] ?? 0));

        } catch (VacantException $e) {
            $this->Smarty->assign('vacant', []);
        }

        $_SESSION['s_empdata_token'] = md5(rand(0, 99999));
    }

    public function payrollDetails($parm, $post)
    {
        try {
            $temp = EmployeePayrollTemplate::find($parm[0] ?? 0);
            $this->Smarty->assign('payroll', $temp);
            $basedOnPayrollNames = '';
            foreach (collect(json_decode($temp->template->amount_percentage_based_on)) as $tempId) {
                $basedOnPayrollNames .= PayrollRule::getPayrollRuleName($tempId) . '<br>';
            }
            $this->Smarty->assign('basedOnPayrollNames', $basedOnPayrollNames);
        } catch (PayrollTemplateException $e) {
            $this->Smarty->assign('payroll', null);
        }
    }

    public function leavesetting($parm, $post)
    {
//        return $parm;
        switch ($parm[0]) {

            case 'save_session':
                $_SESSION['s_leave_user_id'] = $parm[1];
                $_SESSION['s_leave_uao_id'] = $parm[2];
                break;

            case 'leave':

                switch ($parm[1]) {

                    case 'credit':

                        $_SESSION['s_leave_user_id'] = $parm[2];

                        break;

                    case 'insert':

                        if ($_SESSION['s_employees_token'] == $parm[2]) {

                            try {
                                $allowedLeave = LeaveAllowed::readID($post['allowedleave_id'] ?? 0);
                            } catch (LeaveAllowedException $e) {
                            }

                            if ($allowedLeave) {

                                $creditRequest = new LeaveCreditRequest();
                                $creditRequest->bindProperties($post);
                                $creditRequest->leave_id = $allowedLeave->leave_id;
                                $creditRequest->org_id = $_SESSION['organization']->id;
                                $creditRequest->user_id = $_SESSION['s_leave_user_id'];
                                $creditRequest->take_effect_date = date('Y-m-d');
                                $creditRequest->created_by = $_SESSION['user']->id;

                                if ($creditRequest->credit_opr_type == Setting::LEAVE_CREDIT_ADD_BALANCE) {
                                    if ($this->balanceHitLimit($allowedLeave, $post['credit']) || $allowedLeave->leaveType->transfer_type != LeaveType::TRANSFER_WITH_LIMIT) {
                                        $creditRequest->create();
                                        Notification::createdAlert();
                                    } else {
                                        alert(translate('gnr_maximum_leave_credit_hit'), 'warning');
                                    }
                                }

                                if ($creditRequest->credit_opr_type == Setting::LEAVE_CREDIT_DISCOUNT_FROM_BALANCE) {
                                    if ($allowedLeave->employeeLeaveCredit >= $creditRequest->credit) {
                                        $creditRequest->create();
                                        Notification::createdAlert();
                                    } else {
                                        Notification::alertMessage(Notification::WARNING, 'p_employee_leave_credit_less_than_discounted_credit');
                                    }
                                }

                            }

                        }

                        break;

                    case 'update':

                        if ($_SESSION['s_employees_token'] == $parm[2]) {

                            try {
                                $allowedLeave = LeaveAllowed::readID($post['allowedleave_id'] ?? 0);
                            } catch (LeaveAllowedException $e) {
                            }

                            if ($allowedLeave) {

                                try {

                                    $creditRequest = LeaveCreditRequest::readID($parm[3] ?? 0);
                                    $creditRequest->bindProperties($post);
                                    $creditRequest->leave_id = $allowedLeave->leave_id;
                                    $creditRequest->take_effect_date = date('Y-m-d');
                                    $creditRequest->save();

                                    Notification::updatedAlert();

                                } catch (LeaveCreditRequestException $e) {
                                }
                            }

                        }

                        break;

                    case 'delete':

                        if ($_SESSION['s_employees_token'] == $parm[2]) {

                            try {

                                $creditRequest = LeaveCreditRequest::readID($parm[3] ?? 0);
                                $creditRequest->delete();

                                Notification::deletedAlert();

                            } catch (LeaveCreditRequestException $e) {
                            }

                        }

                        break;
                }

                $this->Smarty->assign('creditList', 1);
                $_SESSION['s_empleaves_tab'] = "leave";
                $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
                break;

            case 'updatecredit':

                if ($_SESSION['s_employees_token'] == $parm[1]) {

                    try {

                        $leaveAllowed = LeaveAllowed::readID($parm[2] ?? 0);
                        $leaveAllowed->bindProperties($post);
                        $leaveAllowed->update();

                        Notification::updatedAlert();

                    } catch (LeaveAllowedException $e) {
                    }

                    $_SESSION['s_empleaves_tab'] = "leave";
                    $_SESSION['s_employees_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'template':

                if ($_SESSION['s_employees_token'] == $parm[1]) {
                    $user = User::readID((int)$parm[2] ?? 0);
                    $allowedLeaves = LeaveType::getEmployeeLeaves($_SESSION['organization'], $user);
                    $lastSelectedLeaves = $this->getSelectedUserLeaves($user);
                    LeaveAllowed::updateUserLeaves($post, $user->id);
                    $newSelectedLeaves = $this->getSelectedUserLeaves($user);
                    event('event.AllowedLeavesTemplateUpdated', [$allowedLeaves, $lastSelectedLeaves, $newSelectedLeaves, $user->full_name, $user->id]);
                    $_SESSION['s_empleaves_tab'] = "leave";
                    $_SESSION['s_employees_token'] = md5(rand(0000, 9999));

                }

                break;
        }

        LeaveData::updateEmployeeLeavesCredit($_SESSION['organization']->id, (int)$_SESSION['s_leave_user_id']);

        // Credit
        try {
            $this->Smarty->assign('crediteditList', Request::read([
                Request::ORG_ID => $_SESSION['organization']->id,
                Request::USER_ID => (int)$_SESSION['s_leave_user_id'],
                Request::PRG_ID => Program::PROGRAM_HUMAN_RESOURCE_P051,
                Request::TABLE_NAME => hr_creditedit::class
            ],
                [
                    0 => [
                        'property' => Request::ID,
                        'sort' => 'DESC'
                    ]
                ]));
        } catch (RequestException $e) {
        }

        try {
            $this->Smarty->assign('editcreditrequest_grf', wf_graph::count([Graph::OPR_ID => 650, Graph::STATUS => 1]));
        } catch (ModelException $e) {
            $this->Smarty->assign('editcreditrequest_grf', 0);
        }

        try {
            $this->Smarty->assign('Allowedleaves', LeaveAllowed::read([
                LeaveAllowed::ORG_ID => (int)$_SESSION['organization']->id,
                LeaveAllowed::USER_ID => (int)$_SESSION['s_leave_user_id'] ?? 0
            ]));
        } catch (LeaveAllowedException $e) {
        }

        try {
            $uaoRow = Vacant::readID($_SESSION['s_leave_uao_id'] ?? 0);
            $this->Smarty->assign('uaoRow', $uaoRow);
        } catch (VacantException $e) {
            $this->Smarty->assign('uaoRow', []);
        }

        try {
            $this->Smarty->assign('userRow', UserModel::has('basicJob')->find((int) $uaoRow->user_id));
        } catch (UserException $e) {
            $this->Smarty->assign('userRow', []);
        }

        if (!empty($uaoRow->att_doam_id)) {

            try {

                $employeeDoam = Doam::readID((int)$uaoRow->att_doam_id ?? 0);
                $workDays = explode(',', $employeeDoam->workdays);
                $this->Smarty->assign('workDays', $workDays ? $workDays : []);

            } catch (DoamException $e) {

                $this->Smarty->assign('workDays', []);

            }

        }

        $this->Smarty->assign('week_days', Setting::getList(25));

    }

    /**
     * Fetch collection of vacancies related with the user
     *
     * @param string user_id $parm[0]
     * @return mixed
     */
    public function employeeJobs($parm)
    {
        $user = UserModel::jobDetails()->find($parm[0]);

        $this->Smarty->assign([
            'jobs' => $user->vacancies,
            'user_name' => $user->full_name
        ]);
    }

    /**
     * @param $allowedLeave
     * @param $newCredit
     * @return bool
     * @internal param $post
     */
    private function balanceHitLimit($allowedLeave, $newCredit)
    {
        return ($allowedLeave->employeeLeaveCredit + $newCredit) <= $allowedLeave->max_credit;
    }

    /**
     * @param $userRow
     * @return array
     */
    private function getSelectedUserLeaves($userRow): array
    {
        $SelectedArray = [];
        try {

            $user_leaves_types = LeaveAllowed::read([
                LeaveAllowed::ORG_ID => $_SESSION['organization']->id,
                LeaveAllowed::USER_ID => $userRow->id
            ]);

        } catch (LeaveAllowedException $e) {
        }

        if ($user_leaves_types)
            foreach ($user_leaves_types as $item) {
                $SelectedArray[] = $item->leave_id;
            }

        return $SelectedArray;
    }

    public function employeeleavesetting($parm, $post)
    {
        try {
            $this->Smarty->assign('row', LeaveAllowed::readID((int)$parm[0] ?? 0));
        } catch (LeaveAllowedException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function templates($parm, $post)
    {
        try {
            $userRow = User::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('UserRow', $userRow);
        } catch (UserException $e) {
            $this->Smarty->assign('UserRow', []);
        }

        if ($userRow) {

            $this->Smarty->assign('leaves', LeaveType::getEmployeeLeaves($_SESSION['organization'], $userRow));
            $this->Smarty->assign('selected_array',
                $this->getSelectedUserLeaves($userRow)
            );

        }


        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function addcredit($parm, $post)
    {

        if (!empty($_SESSION['s_leave_user_id'])) {

            try {

                $this->Smarty->assign('levstList', LeaveAllowed::read([
                    LeaveAllowed::ORG_ID => $_SESSION['organization']->id,
                    LeaveAllowed::USER_ID => $_SESSION['s_leave_user_id']
                ]));

            } catch (LeaveAllowedException $e) {

                $this->Smarty->assign('levstList', []);

            }

        }

        $this->Smarty->assign('creditoperations', Setting::getList(182));
        $_SESSION['s_empleaves_tab'] = "credit";
        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function editcredit($parm, $post)
    {

        try {
            $this->Smarty->assign('row', LeaveCreditRequest::readID((int)$parm[0] ?? 0));
        } catch (LeaveCreditRequestException $e) {
            $this->Smarty->assign('row', []);
        }

        try {

            $this->Smarty->assign('levstList', LeaveAllowed::read([
                LeaveAllowed::ORG_ID => $_SESSION['organization']->id,
                LeaveAllowed::USER_ID => $_SESSION['s_leave_user_id'] ?? 0
            ]));

        } catch (LeaveAllowedException $e) {
            $this->Smarty->assign('levstList', []);
        }

        $this->Smarty->assign('creditoperations', Setting::getList(182));
        $_SESSION['s_empleaves_tab'] = "credit";
        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function confirmcredit($parm, $post)
    {
        try {
            $this->Smarty->assign('row', LeaveCreditRequest::readID((int)$parm[0] ?? 0));
        } catch (LeaveCreditRequestException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_empleaves_tab'] = "credit";
        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function leaveshistory($parm, $post)
    {

        switch ($parm[1]) {

            case 'deleteleavehistory':

                if ($_SESSION['s_leaveshistory_token'] == $parm[2]) {

                    try {

                        hr_leavedata::simpleDeleteByProperty([
                            LeaveData::ORG_ID => $_SESSION['organization']->id,
                            LeaveData::USER_ID => $_SESSION['s_leaveshistory_userID'],
                            LeaveData::LEAVE_ID => $_SESSION['s_leaveshistory_LeaveID']
                        ]);

                    } catch (ModelException $e) {
                    }

                }

                $_SESSION['s_leaveshistory_token'] = md5(rand(0000, 9999));
                break;
        }

        try {

            $leaveAllowed = LeaveAllowed::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('allowedLeave', $leaveAllowed);

        } catch (LeaveAllowedException $e) {

            $this->Smarty->assign('allowedLeave', []);

        }

        if ($leaveAllowed) {

            LeaveData::updateEmployeeLeavesCredit($leaveAllowed->org_id, $leaveAllowed->user_id);

            try {

                $this->Smarty->assign('historyList', LeaveData::read([
                    LeaveData::ORG_ID => $leaveAllowed->org_id,
                    LeaveData::USER_ID => $leaveAllowed->user_id,
                    LeaveData::LEAVE_ID => $leaveAllowed->leave_id
                ], [
                    0 => [
                        'property' => LeaveData::FIRST_OF_THE_MONTH,
                        'sort' => 'ASC'
                    ]
                ]));

            } catch (LeaveDataException $e) {
                $this->Smarty->assign('historyList', []);
            }

        }

    }

    public function confirmdeleteleavehistory($parm, $post)
    {
        $_SESSION['s_leaveshistory_token'] = md5(rand(0000, 9999));
    }

    public function documentadd($parm, $post)
    {
        $this->Smarty->assign('user', User::readID((int)$_SESSION['s_doc_employee_user_id'] ?? 0));

        try {
            $this->Smarty->assign('operation', Operation::readByCode((string)$parm[0] ?? ''));
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        try {
            $this->Smarty->assign('document_max_size_limit',
                number_format((((int)(new ConfigurationParser(CLIENT_CONFIG))->getSection('document')['document_max_size_limit']) / 1024) / 1024,
                    2, '.', ','));
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('document_max_size_limit', 0);
        }

        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function documentdit($parm, $post)
    {
        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

        try {
            $this->Smarty->assign('operation', Operation::readByCode((string)$parm[1] ?? ''));
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        try {
            $this->Smarty->assign('document_max_size_limit',
                number_format((((int)(new ConfigurationParser(CLIENT_CONFIG))->getSection('document')['document_max_size_limit']) / 1024) / 1024,
                    2, '.', ','));
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('document_max_size_limit', 0);
        }


        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function documentconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function addrule($parm, $post)
    {
        try {

            $this->Smarty->assign('row', PayrollTemplate::readID((int)$parm[1] ?? 0));
            $this->Smarty->assign('user_id', (int)$parm[2] ?? 0);

        } catch (PayrollTemplateException $e) {

            $this->Smarty->assign('row', []);

        }

        $this->Smarty->assign('yesNoList', Setting::getList(119));
        $this->Smarty->assign('rule_types_list', Setting::getList(144));
        $this->Smarty->assign('rule_condition_list', Setting::getList(145));
        $this->Smarty->assign('rule_active_list', Setting::getList(147));
        $this->Smarty->assign('rule_amount_list', Setting::getList(146));

        try {
            $this->Smarty->assign('rules_list', PayrollRule::read([
                PayrollRule::ORG_ID => $_SESSION['organization']->id,
                PayrollRule::TEMPLATE_ID => $_SESSION['s_prl_templates_id']
            ]));
        } catch (PayrollRuleException $e) {
        }


        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));

        $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
    }

    public function confirm($parm, $post)
    {
        try {
            $this->Smarty->assign('rule', EmployeePayrollTemplate::find((int)$parm[0] ?? 0));
        } catch (PayrollRuleException $e) {
        }
        $_SESSION['s_prl_templates_id'] = $parm[1];
        $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
    }
}