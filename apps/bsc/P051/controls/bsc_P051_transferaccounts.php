<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bsc_P051_transferaccounts extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case 'menu':

                try{
                    $_SESSION['s_transferaccounts_unit_tab'] = Unit::read([
                        Unit::ORG_ID => $_SESSION['organization']->id,
                        Unit::ACTIVATION => 23
                    ], [0 => ['property' => Unit::ORDER, 'sort' => 'ASC']])[0]->id;
                }catch (UnitException $e){

                }

                break;

            case 'update':

                unset($post['submit']);

                foreach ($post as $key => $value) {

                    try{

                        $user = User::readID((int) $key ?? 0);
                        $user->transfer_account_emp_id = $value;
                        $user->update();


                    }catch (UserException $e){

                    }

                }

                Notification::updatedAlert();

                break;
        }

        try{

            $this->Smarty->assign('units', Unit::getUnitAndEmployeeStructure($_SESSION['organization']));

        }catch (UnitException $e){
            $this->Smarty->assign('units',[]);
        }

        $this->Smarty->assign('employees', Vacant::getEmployeeListByEntity($_SESSION['organization'],Vacant::EMPLOYEE_ALL_VACANT));

        $this->Smarty->assign('EditPrivilege',Privilege::isAnEmployeeHasAPrivilegeOnOperation(
            $_SESSION['organization'],
            $_SESSION['user'],
            Operation::readByCode('transferaccounts'),
            '',
            Privilege::UPDATE));

    }

}
