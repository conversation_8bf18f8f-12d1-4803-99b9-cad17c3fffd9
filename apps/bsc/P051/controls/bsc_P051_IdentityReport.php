<?php
use Models\User as UserModel;

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

/**
 * Created by PhpStorm.
 * User: jodeveloper
 * Date: 12/08/20
 * Time: 10:06 ص
 */
class bsc_P051_IdentityReport extends Controller
{
    /**
     * @param $parm
     * @param $post
     */
    public function show($parm, $post)
    {

        // employees who's identity expiration date has come
        $users = UserModel::has('basicJob')
            ->where(User::IDENTITY_EXPAIRY_DATE, '<=', now()->subDays(15))
            ->get();
        $this->Smarty->assign('users', $users);
    }


    /**
     * @param $parm
     * @param $post
     */
    public function expiredIdentityUsers($parm, $post)
    {
        $user = UserModel::findOrFail($parm[0]);
        $this->Smarty->assign('user', $user);
    }

    /**
     * @param $parm
     * @param $post
     */
    public function UpdateUserExpiredIdentity($parm, $post)
    {
        $user = UserModel::findOrFail($parm[0]);
        $user->identity_expairy_date = $post['identity_expairy_date'];
        $user->update();
        Notification::updatedAlert();
        redirect('IdentityReport/show');
    }
}