<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bsc_P051_hrdoamwrdiah extends Controller
{

    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case 'menu':
                $_SESSION['s_hrdoamwrdiah_tab'] = 'doam';
                break;

            case 'save_session':
                $_SESSION['s_doam_id'] = $parm[1];
                $_SESSION['s_hrdoamwrdiah_tab'] = 'wrdiah';
                break;

            case 'doam':

                switch ($parm[1]) {

                    case 'insert':

                        if ($_SESSION['s_hrdoamwrdiah_token'] == $parm[2]) {

                            $doam = new Doam();
                            $doam->bindProperties($post);
                            $doam->org_id = $_SESSION['organization']->id;
                            $doam->created_by = $_SESSION['user']->id;
                            $doam->created_date = date('Y-m-d');
                            $doam->save();

                            Notification::createdAlert();

                        }

                        break;

                    case 'update':

                        if ($_SESSION['s_hrdoamwrdiah_token'] == $parm[2]) {

                            try {

                                $doam = Doam::readID((int) $parm[3] ?? 0);
                                $doam->bindProperties($post);
                                $doam->save();

                                Notification::updatedAlert();

                            } catch (DoamException $e) {}

                        }

                        break;

                    case 'delete':

                        if ($_SESSION['s_hrdoamwrdiah_token'] == $parm[2]) {

                            try {

                                $Doam = Doam::readID((int) $parm[3] ?? 0);
                                $Doam->delete();

                                Notification::deletedAlert();

                            } catch (DoamException $e) {}

                        }

                        break;
                }

                $_SESSION['s_hrdoamwrdiah_tab'] = 'doam';
                break;

            case 'wrdiah':

                switch ($parm[1]) {

                    case 'insert':

                        if ($_SESSION['s_hrdoamwrdiah_token'] == $parm[2]) {

                            $wrdiah = new Wrdiah();
                            $wrdiah->bindProperties($post);
                            $wrdiah->org_id = $_SESSION['organization']->id;
                            $wrdiah->doam_id = $_SESSION['s_doam_id'];
                            $wrdiah->week_end_ids = implode(',', $post['week_end_ids']);
                            $wrdiah->in_1 = $post['in1Hour'] . ":" . $post['in1Minute'];
                            $wrdiah->in_2 = $post['in2Hour'] . ":" . $post['in2Minute'];
                            $wrdiah->in_3 = $post['in3Hour'] . ":" . $post['in3Minute'];
                            $wrdiah->in_4 = $post['in4Hour'] . ":" . $post['in4Minute'];
                            $wrdiah->out_1 = $post['out1Hour'] . ":" . $post['out1Minute'];
                            $wrdiah->out_2 = $post['out2Hour'] . ":" . $post['out2Minute'];
                            $wrdiah->out_3 = $post['out3Hour'] . ":" . $post['out3Minute'];
                            $wrdiah->out_4 = $post['out4Hour'] . ":" . $post['out4Minute'];
                            $wrdiah->weight = 0;
                            $wrdiah->created_by = $_SESSION['user']->id;
                            $wrdiah->created_date = date('Y-m-d');
                            $wrdiah->create();

                            Notification::createdAlert();

                        }

                        break;

                    case 'update':

                        if ($_SESSION['s_hrdoamwrdiah_token'] == $parm[2]) {

                            try {

                                $wrdiah = Wrdiah::readID((int) $parm[3] ?? 0);
                                $wrdiah->bindProperties($post);
                                $wrdiah->week_end_ids = implode(',', $post['week_end_ids']);
                                $wrdiah->in_1 = $post['in1Hour'] . ":" . $post['in1Minute'];
                                $wrdiah->in_2 = $post['in2Hour'] . ":" . $post['in2Minute'];
                                $wrdiah->in_3 = $post['in3Hour'] . ":" . $post['in3Minute'];
                                $wrdiah->in_4 = $post['in4Hour'] . ":" . $post['in4Minute'];
                                $wrdiah->out_1 = $post['out1Hour'] . ":" . $post['out1Minute'];
                                $wrdiah->out_2 = $post['out2Hour'] . ":" . $post['out2Minute'];
                                $wrdiah->out_3 = $post['out3Hour'] . ":" . $post['out3Minute'];
                                $wrdiah->out_4 = $post['out4Hour'] . ":" . $post['out4Minute'];
                                $wrdiah->weight = 0;
                                $wrdiah->update();

                                Notification::updatedAlert();

                            } catch (WrdiahException $e) {
                            }

                        }

                        break;

                    case 'delete':

                        if ($_SESSION['s_hrdoamwrdiah_token'] == $parm[2]) {

                            try {

                                $wrdiah = Wrdiah::readID((int) $parm[3] ?? 0);
                                $wrdiah->delete();

                                Notification::deletedAlert();

                            } catch (WrdiahException $e) {}


                        }

                        break;
                }

                $_SESSION['s_hrdoamwrdiah_tab'] = 'wrdiah';

                break;
        }

        try{
            $this->Smarty->assign('doamList', Doam::read([Doam::ORG_ID => $_SESSION['organization']->id],[0=>['property'=>Doam::ID,'sort'=>'DESC']]));
        }catch (DoamException $e){}

        if($_SESSION['s_doam_id']){

            try{

                $currentDoam = Doam::readID((int)$_SESSION['s_doam_id']);
                $this->Smarty->assign('currentDoam', $currentDoam);

            }catch (DoamException $e){
                $this->Smarty->assign('currentDoam', []);
            }

            if($currentDoam){

                try{

                    $this->Smarty->assign('wrdiahList', Wrdiah::read([
                        Wrdiah::ORG_ID => $currentDoam->org_id,
                        Wrdiah::DOAM_ID => $currentDoam->id
                    ],[0=>[
                        'property'=>Wrdiah::ORDER,
                        'sort'=>'ASC'
                    ]]));

                }catch (WrdiahException $e){}

            }

        }

        $this->Smarty->assign('weekDays',Setting::getList(25));
        $_SESSION['s_hrdoamwrdiah_token'] = md5(rand(0, 10000000));

    }

    public function add($parm, $post)
    {
        try{
            $this->Smarty->assign('week_days', Setting::getList(25));
        }catch (SectionException $e){
            $this->Smarty->assign('week_days',[]);
        }

        $this->Smarty->assign('notCompleteFingerprintOptions',Setting::getList(252));
        $this->Smarty->assign('extraTimeOptions',Setting::getList(253));
        $this->Smarty->assign('outOfRangeFingerprintOptions',Setting::getList(254));
        $_SESSION['s_hrdoamwrdiah_token'] = md5(rand(0, 10000000));
    }

    public function edit($parm, $post)
    {
        try {
            $this->Smarty->assign('row', Doam::readID(((int) $parm[0] ?? 0)));
        } catch (DoamException $e) {
            $this->Smarty->assign('row',[]);
        }

        try{
            $this->Smarty->assign('week_days', Setting::getList(25));
        }catch (SectionException $e){
            $this->Smarty->assign('week_days',[]);
        }

        $this->Smarty->assign('notCompleteFingerprintOptions',Setting::getList(252));
        $this->Smarty->assign('extraTimeOptions',Setting::getList(253));
        $this->Smarty->assign('outOfRangeFingerprintOptions',Setting::getList(254));
        $_SESSION['s_hrdoamwrdiah_token'] = md5(rand(0, 10000000));
    }

    public function confirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', Doam::readID(((int) $parm[0] ?? 0)));
        } catch (DoamException $e) {
            $this->Smarty->assign('row',[]);
        }
    }

    public function addwrdiah($parm, $post)
    {
        try{
            $this->Smarty->assign('week_days', Setting::getList(25));
        }catch (SectionException $e){
            $this->Smarty->assign('week_days',[]);
        }

        $_SESSION['s_hrdoamwrdiah_token'] = md5(rand(0, 10000000));
    }

    public function editwrdiah($parm, $post)
    {
        try {
            $this->Smarty->assign('row', Wrdiah::readID((int) $parm[0] ?? 0));
        } catch (WrdiahException $e) {
            $this->Smarty->assign('row', []);
        }

        try{
            $this->Smarty->assign('week_days', Setting::getList(25));
        }catch (SectionException $e){
            $this->Smarty->assign('week_days',[]);
        }

    }

    public function confirmwrdiah($parm, $post)
    {
        try {
            $this->Smarty->assign('row', Wrdiah::readID((int) $parm[0] ?? 0));
        } catch (WrdiahException $e) {
            $this->Smarty->assign('row', []);
        }
    }

    public function employees($parm, $post)
    {
        
        $doam = Doam::readID((int)$parm[0]);
        $employees = Vacant::getAllEmployeesOfFingerPrintDevice();
        // $employees = Vacant::getEmployeesOfDoamMontlyType();

        $this->Smarty->assign('doam', $doam); // make variable $doam available in templateEmployees view
        $this->Smarty->assign('employees', $employees);
    }

    public function saveEmployees($parm,$post)
    {
        foreach ($post['employees'] as $id) {
            $employee = Vacant::readID((int)$id);
            $employee->att_doam_id = $parm[0];
            $employee->save();
        }
        alert(translate('message_updated_successfully'));
        redirect('hrdoamwrdiah/show');
    }

    public function RegisteredEmployees($parm)
    {
        $doam = Doam::readID((int)$parm[0]);
        $employees = Vacant::getAllEmployeesOfFingerPrintDevice();
        $this->Smarty->assign('doam', $doam); // make variable $device available in templateEmployees view
        $this->Smarty->assign('employees', $employees);
    }

    public function information($parm, $post)
    {

    }

}