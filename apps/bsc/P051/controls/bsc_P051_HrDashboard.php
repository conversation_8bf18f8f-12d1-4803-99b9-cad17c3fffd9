<?php

use SNSO\Core\Reporter\ChartBuilder;
use Models\User;
use User as UserResource;
use Carbon\Carbon;
use Models\HumanResource\Job;

/**
 * Created by PhpStorm.
 * User: altaif
 * Date: 1/14/19
 * Time: 11:40 AM
 */
class bsc_P051_HrDashboard extends Controller
{
    /**
     * Fetch sum of job user per country
     *
     * @param $countries
     * @return array
     */
    public function getData($countries)
    {
        $other_country_count = 0;
        foreach ($countries as $country){
            if($country['country_code'] != "SA"){
                $other_country_count += $country['count'];
            }else{
                $sudai_count = $country['count'];
            }
        }

        return [$other_country_count, $sudai_count];
    }

    /**
     * @param $parm
     * @param $post
     */
    public function show($parm, $post)
    {
        $this->getDowams();
        $this->getEmployeesDashboardRequestsDetails();
        $this->getGenaralRequestsDetails();

        $occupiedJobs = Job::occupiedJobsCount();
        $vacantJobs = Job::vacantJobsCount();

        $employeesByGender = User::has('basicJob')->get()->groupBy(UserResource::GENDER);
        $malesEmployees = $employeesByGender[UserResource::SETTING_MALE] ?
            $employeesByGender[UserResource::SETTING_MALE]->count() : 0;
        $femalesEmployees = $employeesByGender[UserResource::SETTING_FEMALE] ?
            $employeesByGender[UserResource::SETTING_FEMALE]->count() : 0;

        $employeesCountries = User::has('basicJob')->get()->groupBy(function (User $user) {
            return $user->nationality == 56 ? 'SA' : 'NON_SA';
        });


        $this->Smarty->assign([
            'countries_values' => [
                $employeesCountries['NON_SA'] ? $employeesCountries['NON_SA']->count(): 0,
                $employeesCountries['SA'] ? $employeesCountries['SA']->count(): 0,
            ],
            'gender_values'    => [$malesEmployees, $femalesEmployees],
            'jobs_values'      => [$occupiedJobs, $vacantJobs]
        ]);

    }

    /**
     *It's assign the  available Dowams for this organization
     *
     */
    public function getDowams()
    {
        try {
            $this->Smarty->assign('dowams', Doam::read([Doam::ORG_ID => $_SESSION['organization']->id], [0 => ['property' => Doam::ID, 'sort' => 'DESC']]));
        } catch (DoamException $e) {
        }
        $this->Smarty->assign('weekDays', Setting::getList(25));
    }

    /**
     * Gather HR requests data and assign it
     *
     */
    public function getEmployeesDashboardRequestsDetails()
    {
        $EmployeesDashboardTables = [
            Request::REQUEST_TYPE_HR_LEAVE,
            Request::REQUEST_TYPE_HR_PERMISSION,
            Request::REQUEST_TYPE_HR_MANDATE,
            Request::REQUEST_TYPE_HR_MANDATE_FEES,
            Request::REQUEST_TYPE_HR_OUTWORK,
            Request::REQUEST_TYPE_HR_OUTWORK_FEES,
            Request::REQUEST_TYPE_HR_ADVANCE_REQUEST,
        ];

        $employeesRequestsDetails = DB::table(wf_request::class)
            ->where(Request::ORG_ID, $_SESSION['organization']->id)
            ->whereIn(Request::TABLE_NAME, $EmployeesDashboardTables)
            ->get();

        $unsentRequestsCount = $employeesRequestsDetails->where(Request::SEND_STATUS, Request::REQUEST_IS_NOT_SEND)->count();
        $underProcessRequestsCount = $employeesRequestsDetails->where(Request::SEND_STATUS, Request::REQUEST_IS_UNDER_PROCESS)->count();
        $sentRequestsCount = $employeesRequestsDetails->where(Request::SEND_STATUS, Request::REQUEST_REACH_END)->count();

        $this->Smarty->assign([
            'employeesRequestsTotal' => $employeesRequestsDetails->count(),
            'employeesRequestsDetails' => $employeesRequestsDetails->groupBy(Request::TABLE_NAME),
            'unsentEmployeesRequestsCount' => $unsentRequestsCount,
            'underProcessEmployeesRequestsCount' => $underProcessRequestsCount,
            'sentEmployeesRequestsCount' => $sentRequestsCount,
        ]);

        $graphData = [];
        $graphLabels = [];
        foreach ($employeesRequestsDetails->groupBy(Request::TABLE_NAME) as $key => $data) {
            $graphData[] = $data->count();
            $graphLabels[] = Translation::translate($_SESSION['program'], $key);
        }
        $this->Smarty->assign('graphData',$graphData);
        $this->Smarty->assign('graphLabels',$graphData);
//        $this->buildGraph('requestsGraph', $graphData, $graphLabels);

    }

    public function getGenaralRequestsDetails()
    {
        $GeneralHrTables = [
            Request::REQUEST_TYPE_HR_DEDUCT_ADDITION,
            Request::REQUEST_TYPE_HR_ATTENDANCE,
            Request::REQUEST_TYPE_HR_LATENCY,
        ];

        $generalHrRequestsDetails = DB::table(wf_request::class)
            ->where(Request::ORG_ID, $_SESSION['organization']->id)
            ->whereIn(Request::TABLE_NAME, $GeneralHrTables)
            ->get();

        $unsentRequestsCount = $generalHrRequestsDetails->where(Request::SEND_STATUS, Request::REQUEST_IS_NOT_SEND)->count();
        $underProcessRequestsCount = $generalHrRequestsDetails->where(Request::SEND_STATUS, Request::REQUEST_IS_UNDER_PROCESS)->count();
        $sentRequestsCount = $generalHrRequestsDetails->where(Request::SEND_STATUS, Request::REQUEST_REACH_END)->count();

        $this->Smarty->assign('generalHrRequestsTotal', $generalHrRequestsDetails->count());
        $this->Smarty->assign('generalHrRequestsDetails', $generalHrRequestsDetails->groupBy(Request::TABLE_NAME));
        $this->Smarty->assign('unsentGeneralRequestsCount', $unsentRequestsCount);
        $this->Smarty->assign('underProcessGeneralRequestsCount', $underProcessRequestsCount);
        $this->Smarty->assign('sentGeneralRequestsCount', $sentRequestsCount);
    }

    public function buildGraph($name, $data, $labels, $type = "bar")
    {


        $chart = new ChartBuilder();

        if ($type == "bar") {
            foreach (range(0, count($data) - 1) as $index) {
                $randomColor = ChartBuilder::getColor();
                $randomColor = Helper::hexColorToRgb($randomColor, .5);
                $dataSets[] = [
                    'label' => [$labels[$index]],
                    'backgroundColor' => $randomColor,
                    'hoverBackgroundColor' => $randomColor,
                    'data' => [$data[$index]]
                ];
            };
        } else {
            $dataSets = [
                'backgroundColor' => ['aqua', 'gray', 'orange'],
                'hoverBackgroundColor' => ['aqua', 'gray', 'orange'],
                'data' => $data,
            ];
        }


        $chart->name($name)
            ->type($type)
            ->size(['width' => 400, 'height' => 200])
            ->options([
                "responsive" => true,
            ])
            ->datasets($dataSets);

        $this->Smarty->assign($name, $chart->build());
    }


}
