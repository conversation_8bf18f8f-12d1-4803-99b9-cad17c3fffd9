<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');
use \Models\User;
// TODO Documentation Missing
class bsc_P051_orguntmanager extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]){

            case 'update':

                if($_SESSION['s_orguntmanager_token'] === $parm[1]){

                    try{

                        $unit = Unit::readID((int) $parm[2] ?? 0);
                        $lastUnitManager = $unit->manager->full_name;
                        $unit->manager_id = (int) $post['manager_id'] ?? 0;
                        $unit->save();
                        $newUnitManager = User::find($post['manager_id'])->sh_user_full_name;
                        event('event.UnitMangerUpdated',[$lastUnitManager,$newUnitManager,$unit->name]);
                        try{

                            $projects = Project::read([
                                Project::ORG_ID => $unit->org_id,
                                Project::UNIT_ID => $unit->id
                            ]);

                            foreach ($projects as $project) {

                                try{
                                    $project->unit_manager_id = $unit->manager_id;
                                    $project->save();
                                }catch (ProjectException $e){

                                }

                            }

                        }catch (ProjectException $e){

                        }

                        Notification::updatedAlert();

                    }catch (UnitException $e){

                    }
                }


                break;

        }

        try{

            $this->Smarty->assign('units',Unit::read([Unit::ORG_ID => $_SESSION['organization']->id]));

        }catch (UnitException $e){

            $this->Smarty->assign('units',[]);

        }

        $_SESSION['s_orguntmanager_token'] = md5(rand(0000,9999));

    }

    public function browse($parm, $post)
    {

        try{

            $this->Smarty->assign('unit', Unit::readID((int) $parm[0] ?? 0));

        }catch (UnitException $e){

            $this->Smarty->assign('unit',[]);

        }

        $_SESSION['s_orguntmanager_token'] = md5(rand(0000,9999));

    }


    public function edit($parm, $post)
    {

        $this->Smarty->assign('employees', Vacant::getEmployeeListByEntity($_SESSION['organization'],Vacant::EMPLOYEE_ALL_VACANT));

        try{

            $this->Smarty->assign('unit', Unit::readID((int) $parm[0] ?? 0));

        }catch (UnitException $e){

            $this->Smarty->assign('unit',[]);

        }

        $_SESSION['s_orguntmanager_token'] = md5(rand(0000,9999));

    }

}
