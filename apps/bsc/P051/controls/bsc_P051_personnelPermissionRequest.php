<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bsc_P051_personnelPermissionRequest extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case Operation::INSERT:

                if ($_SESSION['s_personnelPermissionRequest_token'] == $parm[1]) {

                    $permission = new HRPermissionRequest();
                    $permission->bindProperties($post);
                    $permission->org_id = $_SESSION['organization']->id;
                    $permission->show_type = 0;
                    $permission->date = $this->Date->get_date('ad', $post['date']);
                    $permission->created_by = $_SESSION['user']->id;
                    $permission->created_date = date('Y-m-d');
                    if ($permission->create(HRPermissionRequest::HR_PERMISSION_REQUEST_FROM_PERSONNEL)) {
                        Notification::createdAlert();
                    }

                }

                break;

            case Operation::UPDATE:

                if ($_SESSION['s_personnelPermissionRequest_token'] == $parm[1]) {

                    try {

                        $permission = HRPermissionRequest::readID($parm[2] ?? 0);
                        $permission->bindProperties($post);
                        $permission->date = $this->Date->get_date('ad', $post['date']);
                        if ($permission->update()) {
                            Notification::updatedAlert();
                        }

                    } catch (HRPermissionRequestException $e) {

                    }
                }

                break;

            case Operation::DELETE:

                if ($_SESSION['s_personnelPermissionRequest_token'] == $parm[1]) {

                    try {
                        $permission = HRPermissionRequest::readID($parm[2] ?? 0);
                    } catch (HRPermissionRequestException $e) {
                        $permission = null;
                    }

                    if ($permission) {

                        try {

                            Request::deleteWFRequest(
                                $_SESSION['organization']->id,
                                $_SESSION['program']->id,
                                $_SESSION['user']->id,
                                'permission',
                                'hr_perm',
                                $permission->id);

                            Notification::deletedAlert();

                        } catch (RequestException $e) {

                        }
                    }

                }

                break;

        }

        $this->Smarty->assign('requests', Request::read([
            Request::TABLE_NAME => hr_perm::class,
            Request::ORG_ID => $_SESSION['organization']->id,
            Request::PRG_ID => $_SESSION['program']->id,
        ]));

        $_SESSION['s_personnelPermissionRequest_token'] = md5(rand(0, 10000000));

    }


    public function add($parm, $post)
    {

        try {
            $employees = Vacant::getEmployeeList($_SESSION['organization']);
        } catch (VacantException $e) {
            $employees = [];
        }

        $this->Smarty->assign('employees', $employees);


        try {
            $employee = Vacant::getEmployeeJobs($_SESSION['organization']->id, $_SESSION['user']->id, Vacant::EMPLOYEE_BASIC_VACANT);
        } catch (VacantException $e) {
            $employee = [];
        }

        $this->Smarty->assign('employee', $employee);

        try {

            $this->Smarty->assign('wrdias', hr_wrdiah::simpleReadByProperty([
                Wrdiah::ORG_ID => $employee->org_id,
                Wrdiah::DOAM_ID => $employee->att_doam_id
            ]));

        } catch (ModelException $e) {

            $this->Smarty->assign('wrdias', []);

        }

        try {
            $this->Smarty->assign('grfExistenceNum', Graph::count([Graph::STATUS => 1, Graph::OPR_CODE => 'permission']));
        } catch (ModelException $e) {
            $this->Smarty->assign('grfExistenceNum', 0);
        }

        $this->Smarty->assign('perm_type_list', Setting::getList(29));

        $_SESSION['s_personnelPermissionRequest_token'] = md5(rand(0, 10000000));

    }

    public function edit($parm, $post)
    {

        try {
            $employees = Vacant::getEmployeeList($_SESSION['organization']);
        } catch (VacantException $e) {
            $employees = [];
        }

        $this->Smarty->assign('employees', $employees);

        try {
            $employee = Vacant::getEmployeeJobs($_SESSION['organization']->id, $_SESSION['user']->id, Vacant::EMPLOYEE_BASIC_VACANT);
            $this->Smarty->assign('employee', $employee);
        } catch (VacantException $e) {
            $employee = [];
            $this->Smarty->assign('employee', []);
        }

        try {

            $this->Smarty->assign('wrdias', hr_wrdiah::simpleReadByProperty([
                Wrdiah::ORG_ID => $employee->org_id,
                Wrdiah::DOAM_ID => $employee->att_doam_id
            ]));

        } catch (ModelException $e) {

            $this->Smarty->assign('wrdias', []);

        }

        try {
            $this->Smarty->assign('perm_row', HRPermissionRequest::readID((int)$parm[0]));
        } catch (HRPermissionRequestException $e) {
            $this->Smarty->assign('perm_row', []);
        }

        $this->Smarty->assign('perm_type_list', Setting::getList(29));

        $_SESSION['s_personnelPermissionRequest_token'] = md5(rand(0, 10000000));

    }

    public function confirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', HRPermissionRequest::readID((int)$parm[0]));
        } catch (HRPermissionRequestException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_personnelPermissionRequest_token'] = md5(rand(0, 10000000));

    }
}