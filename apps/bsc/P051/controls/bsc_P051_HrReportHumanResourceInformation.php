<?php

use SNSO\Core\Reporter\ChartBuilder;

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class bsc_P051_HrReportHumanResourceInformation extends Controller
{

    public function show($parm, $post)
    {

        // Education
        $eData = \HRReportsBuilder::EducationReport();

        // Gender
        $gData = \HRReportsBuilder::GenderReport();

        // Jobs classification
        $jData = \HRReportsBuilder::JobReport();

        // Nationality
        $nData = \HRReportsBuilder::NationReport();

        // Doam type
        $dData = \HRReportsBuilder::DoamReport();

        // Unit type
        $uData = \HRReportsBuilder::UnitReport();

        // Unit type
        $sData = \HRReportsBuilder::SocialStatusReport();

        $this->Smarty->assign('eData', $eData);

        $this->Smarty->assign('gData', $gData);

        $this->Smarty->assign('jData', $jData);

        $this->Smarty->assign('nData', $nData);

        $this->Smarty->assign('dData', $dData);

        $this->Smarty->assign('uData', $uData);

        $this->Smarty->assign('sData', $sData);

        $lookup = [
            "educationChart" => $eData,
            "genderChart" => $gData,
            "jobChart" => $jData,
            "nationalityChart" => $nData,
            "dowmChart" => $dData,
            "unitChart" => $uData,
            "socialStatusChart" => $sData,

        ];

        foreach ($lookup as $key => $value) {
            $this->prepareChartsData($value, $key);
        }

    }

    private function prepareChart($name, $data, $labels)
    {

        foreach (range(0, count($data) - 1) as $index) {
            $randomColor = ChartBuilder::getColor();
            $randomColor = Helper::hexColorToRgb($randomColor, .4);
            $randomColors[] = $randomColor;
        };


        $chart = new ChartBuilder();
        $chart->name($name)
            ->type('pie')
            ->size(['width' => 500, 'height' => 300])
            ->labels($labels)
            ->options([
                "legend" => [
                    "position" =>
                        'left'
                ]
            ])
            ->datasets(
                [
                    [
                        'backgroundColor' => $randomColors,
                        'hoverBackgroundColor' => $randomColors,
                        'data' => $data,
                    ]
                ]
            );

        $this->Smarty->assign($name, $chart->build());
    }

    private function prepareChartsData($hrData, $charName)
    {
        $Data = [];
        $Labels = [];

        foreach ($hrData["EmployeesCount"] as $socialState => $count) {
            if ($count > 0) {
                $Data[] = $count;
                $Labels[] = $socialState;
            }
        }
        $this->prepareChart("{$charName}",
            $Data,
            $Labels
        );
    }
}