<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bsc_P051_personnel extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case 'menu':

                $_SESSION['s_personnel_tab'] = "absence";

                break;

            case 'tabulated':

                switch ($parm[1]) {

                    case 'insert':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            try {

                                $schedual = new LeaveScheduled();
                                $schedual->bindProperties($post);
                                $schedual->org_id = $_SESSION['organization']->id;
                                $schedual->created_by = $_SESSION['user']->id;
                                $schedual->start_date = $this->Date->get_date('ad', $post['start_date']);
                                $schedual->circulate_status = 0;
                                $dateArray = LeaveScheduled::circulateLeaveEndDate($_SESSION['organization']->id, $schedual->start_date, $post['duration']);
                                $schedual->end_date = $dateArray['date'];
                                $schedual->created_date = date('Y-m-d');
                                $schedual->save();

                                Notification::createdAlert();

                            } catch (LeaveScheduledException $e) {

                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

                        }

                        break;

                    case 'update':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            try {

                                $schedual = LeaveScheduled::readID($parm[3] ?? 0);
                                $schedual->bindProperties($post);
                                $schedual->start_date = $this->Date->get_date('ad', $post['start_date']);
                                $schedual->circulate_status = 0;
                                $dateArray = LeaveScheduled::circulateLeaveEndDate($_SESSION['organization']->id, $schedual->start_date, $post['duration']);
                                $schedual->end_date = $dateArray['date'];
                                $schedual->save();

                                Notification::updatedAlert();

                            } catch (LeaveScheduledException $e) {
                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

                        }

                        break;

                    case 'delete':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            try {
                                $schedual = LeaveScheduled::readID($parm[3] ?? 0);
                                $schedual->delete();

                                Notification::deletedAlert();

                            } catch (LeaveScheduledException $E) {
                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

                        }

                        break;

                    case 'circulate':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            LeaveScheduled::circulateTabulated($parm[4], $parm[3]);

                            Notification::updatedAlert();

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

                        }

                        break;

                }

                $_SESSION['s_personnel_tab'] = 'tabulated';
                break;

            case 'absence':

                switch ($parm[1]) {

                    case 'insert':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            $attendance = new HRAttendanceRequest();
                            $attendance->bindProperties($post);
                            $attendance->org_id = $_SESSION['organization']->id;
                            $attendance->user_id = $post['user_id'];
                            $attendance->start_intervals = implode(',', $post['wardiah_ids1']);
                            $attendance->end_intervals = implode(',', $post['wardiah_ids2']);
                            $attendance->start_date = $this->Date->get_date('ad', $post['start_date']);
                            $attendance->end_date = $this->Date->get_date('ad', $post['end_date']);
                            $attendance->manipulation_type = 0;
                            $attendance->created_by = $_SESSION['user']->id;
                            $attendance->created_date = date('Y-m-d');

                            if ($attendance->record_type == HRAttendanceRequest::ATTENDANCE_UPON_DOAM_RECORDS) {
                                $attendance->detail = HRAttendanceRequest::AbsenceByDoamDetail($_SESSION['organization']->id, $attendance->user_id, $attendance->start_date, $attendance->end_date);
                                $attendance->duration = HRAttendanceRequest::calculateAbsenceDuration($attendance->detail);

                            }

                            if ($attendance->save()) {

                                if ($attendance->record_type == HRAttendanceRequest::ATTENDANCE_UPON_FINGER_PRINTS_RECORDS) {

                                    try {
                                        HRAttendanceRequest::AbsenceByFingerPrintDetail(hr_attendance::readByID((int)$attendance->id));
                                    } catch (ModelException $e) {

                                    }
                                }

                                Notification::createdAlert();
                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

                        }

                        break;

                    case 'update':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            try {

                                $attendance = HRAttendanceRequest::readID((int)$parm[3] ?? 0);
                                $attendance->bindProperties($post);
                                $attendance->start_intervals = implode(',', $post['wardiah_ids1']);
                                $attendance->end_intervals = implode(',', $post['wardiah_ids2']);
                                $attendance->start_date = $this->Date->get_date('ad', $post['start_date']);
                                $attendance->end_date = $this->Date->get_date('ad', $post['end_date']);

                                switch ($attendance->record_type) {

                                    case HRAttendanceRequest::ATTENDANCE_UPON_DOAM_RECORDS:

                                        $attendance->detail = HRAttendanceRequest::AbsenceByDoamDetail($_SESSION['organization']->id, $attendance->user_id, $attendance->start_date, $attendance->end_date);
                                        $attendance->duration = HRAttendanceRequest::calculateAbsenceDuration($attendance->detail);

                                        break;

                                    case HRAttendanceRequest::ATTENDANCE_UPON_FINGER_PRINTS_RECORDS:

                                        HRAttendanceRequest::AbsenceByFingerPrintDetail(hr_attendance::readByID((int)$attendance->id));

                                        break;

                                }

                                if ($attendance->save()) {

                                    Notification::updatedAlert();

                                }

                            } catch
                            (HRAttendanceRequestException $e) {

                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

                        }

                        break;

                    case 'delete':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            try {

                                HRAttendanceRequest::readID((int)$parm[3] ?? 0)->delete();
                                Notification::deletedAlert();

                            } catch (HRAttendanceRequestException $e) {

                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

                        }

                        break;

                    case 'structure':
                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            HRAttendanceRequest::updateAbsenceStructure($post, $parm[3]);

                            Notification::updatedAlert();

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));
                        }

                        break;

                    case 'archive':
                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            try {

                                $attendance = HRAttendanceRequest::readID((int)$parm[3] ?? 0);
                                Notification::updatedAlert();

                            } catch (HRAttendanceRequestException $e) {

                            }
                            try {
                                $request = Request::readID((int)$attendance->requestEntity->wf_request_id);

                                $request->send_status = Request::REQUEST_IS_ARCHIVED;

                                $request->update();
                            } catch (RequestException $e) {
                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));
                        }

                        break;
                }
                $_SESSION['s_personnel_tab'] = "absence";
                break;

            case 'latency':

                switch ($parm[1]) {

                    case 'insert':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            $latency = new HRLatencyRequest();
                            $latency->bindProperties($post);
                            $latency->org_id = $_SESSION['organization']->id;
                            $latency->user_id = $post['user_id'];
                            $latency->start_date = $this->Date->get_date('ad', $post['start_date']);
                            $latency->end_date = $this->Date->get_date('ad', $post['end_date']);
                            $latency->manipulation_type = 0;
                            $latency->created_by = $_SESSION['user']->id;
                            $latency->created_date = date('Y-m-d');

                            if ($latency->save()) {

                                HRLatencyRequest::latencyDetails($latency);
                                Notification::createdAlert();

                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

                        }

                        break;

                    case 'update':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            try {

                                $latency = HRLatencyRequest::readID((int)$parm[3] ?? 0);
                                $latency->bindProperties($post);
                                $latency->start_date = $this->Date->get_date('ad', $post['start_date']);
                                $latency->end_date = $this->Date->get_date('ad', $post['end_date']);

                                if ($latency->save()) {

                                    Notification::updatedAlert();

                                }

                            } catch (HRLatencyRequestException $e) {

                            }

                            if ($latency) {

                                HRLatencyRequest::latencyDetails($latency);

                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

                        }

                        break;

                    case 'delete':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            try {

                                HRLatencyRequest::readID((int)$parm[3] ?? 0)->delete();
                                Notification::deletedAlert();

                            } catch (HRLatencyRequestException $e) {

                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

                        }

                        break;

                    case 'archive':
                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            try {

                                $latency = HRLatencyRequest::readID((int)$parm[3] ?? 0);
                                Notification::updatedAlert();

                            } catch (HRLatencyRequestException $e) {

                            }
                            try {
                                $request = Request::readID((int)$latency->requestEntity->wf_request_id);

                                $request->send_status = Request::REQUEST_IS_ARCHIVED;

                                $request->update();
                            } catch (RequestException $e) {
                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));
                        }

                        break;

                }

                $_SESSION['s_personnel_tab'] = "latency";

                break;

            case 'deductaddition':

                switch ($parm[1]) {

                    case 'insert':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            try {

                                $deductAddition = new HRDeductAdditionRequest();
                                $deductAddition->bindProperties($post);
                                $deductAddition->user_id = $post['user_id'];
                                $deductAddition->org_id = $_SESSION['organization']->id;
                                $deductAddition->created_by = $_SESSION['user']->id;
                                $deductAddition->created_date = date('Y-m-d');
                                $deductAddition->save();

                                Notification::createdAlert();

                            } catch (HRDeductAdditionRequestException $e) {

                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));
                        }

                        break;

                    case 'update':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            $deductAddition = null;

                            try {

                                $deductAddition = HRDeductAdditionRequest::readID((int)$parm[3]);
                                $deductAddition->bindProperties($post);
                                $deductAddition->save();

                                Notification::updatedAlert();

                            } catch (HRDeductAdditionRequestException $exception) {

                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

                        }

                        break;

                    case 'delete':

                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            try {

                                $deductAddition = HRDeductAdditionRequest::readID((int)$parm[3]);

                            } catch (HRDeductAdditionRequestException $exception) {

                            }

                            if ($deductAddition) {

                                try {

                                    Request::deleteWFRequest(
                                        $_SESSION['organization']->id,
                                        $_SESSION['program']->id,
                                        $deductAddition->user_id,
                                        'deductaddition',
                                        'hr_deductaddition',
                                        $parm[3]);

                                    Notification::deletedAlert();

                                } catch (Exception $e) {

                                }
                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

                        }

                        break;

                    case 'archive':
                        if ($_SESSION['s_personnel_token'] == $parm[2]) {

                            try {

                                $deductAddition = HRDeductAdditionRequest::readID((int)$parm[3] ?? 0);
                                Notification::updatedAlert();

                            } catch (HRDeductAdditionRequestException $e) {

                            }
                            try {
                                $request = Request::readID((int)$deductAddition->requestEntity->wf_request_id);

                                $request->send_status = Request::REQUEST_IS_ARCHIVED;

                                $request->update();
                            } catch (RequestException $e) {
                            }

                            $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));
                        }

                        break;

                }

                $_SESSION['s_personnel_tab'] = "deductaddition";

                break;

        }

        /*
         * Data Preparation
         */

        try {

            $this->Smarty->assign('hr_schedholid_list', LeaveScheduled::read([LeaveScheduled::ORG_ID => $_SESSION['organization']->id],
                [0 => ['property' => LeaveScheduled::ID,
                    'sort' => 'DESC']]));

        } catch
        (LeaveScheduledException $e) {

            $this->Smarty->assign('hr_schedholid_list', []);

        }

        // attendance
        $this->Smarty->assign('attendanceRequests',Request::getRequests($_SESSION['organization'], hr_attendance::class, [
            Request::REQUEST_IS_NOT_SEND,
            Request::REQUEST_IS_UNDER_PROCESS,
            Request::REQUEST_REACH_END]));


        // Latencies

        $this->Smarty->assign('latenciesRequests',Request::getRequests($_SESSION['organization'], hr_latency::class, [
            Request::REQUEST_IS_NOT_SEND,
            Request::REQUEST_IS_UNDER_PROCESS,
            Request::REQUEST_REACH_END]));


        // deductaddition

        $this->Smarty->assign('deductadditionRequests',Request::getRequests($_SESSION['organization'], hr_deductaddition::class, [
            Request::REQUEST_IS_NOT_SEND,
            Request::REQUEST_IS_UNDER_PROCESS,
            Request::REQUEST_REACH_END]));

        $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));

    }
}