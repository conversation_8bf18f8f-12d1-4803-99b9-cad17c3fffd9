<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bsc_P051_PersonnelArchive extends Controller
{

    public function show($parm, $post)
    {
        
        switch ($parm[0]) {
            case 'menu':

                $_SESSION['s_personnel_tab'] = "absence";

                break;
            case 'latency':
                if ($_SESSION['s_personnel_archive_token'] == $parm[1]) {
                    try {
                        $latencyRequest = Request::readID((int)$parm[2]);
                        $latencyRequest->send_status = Request::REQUEST_REACH_END;
                        $latencyRequest->update();
                    } catch (RequestException $e) {

                    }
                }

                $_SESSION['s_personnel_tab'] = "latency";
                break;
            case 'absence':
                if ($_SESSION['s_personnel_archive_token'] == $parm[1]) {
                    try {
                        $latencyRequest = Request::readID((int)$parm[2]);
                        $latencyRequest->send_status = Request::REQUEST_REACH_END;
                        $latencyRequest->update();
                    } catch (RequestException $e) {

                    }
                }
                $_SESSION['s_personnel_tab'] = "absence";
                break;
            case 'deductaddition':
                if ($_SESSION['s_personnel_archive_token'] == $parm[1]) {
                    try {
                        $deductAddition = HRDeductAdditionRequest::readID((int)$parm[2]);
                        if ($deductAddition) {
                            $deductAddition->request_success = 2;
                            $deductAddition->save();
                            Notification::updatedAlert();
                        }
                    } catch (HRDeductAdditionRequestException $e) {
                        Notification::failAlert($e->getMessage());
                    }
                }
                $_SESSION['s_personnel_tab'] = "deductaddition";
                break;
        }

        // attendance
        try {
            $attendanceRequests = DB::table('hr_attendance as attendance')
                ->select(
                    'attendance.*',
                    'user.sh_user_full_name as user_name'
                )
                ->leftjoin('sh_user as user', 'attendance.hr_attendance_user_id', '=', 'user.sh_user_id')
                ->where('attendance.hr_attendance_org_id', $_SESSION['organization']->id)
                ->where('attendance.hr_attendance_request_success', 2) // الطلبات المؤرشفة فقط
                ->orderByDesc('attendance.hr_attendance_id')
                ->paginate(15, ['*'], 'page', $_SESSION['pagn']);
            
            $this->Smarty->assign('attendanceRequests', $attendanceRequests);
        } catch (Exception $e) {
            $this->Smarty->assign('attendanceRequests', []);
        }

        // Latencies
        try {
            $latenciesRequests = DB::table('hr_latency as latency')
                ->select(
                    'latency.*',
                    'user.sh_user_full_name as user_name'
                )
                ->leftjoin('sh_user as user', 'latency.hr_latency_user_id', '=', 'user.sh_user_id')
                ->where('latency.hr_latency_org_id', $_SESSION['organization']->id)
                ->where('latency.hr_latency_request_success', 2) // الطلبات المؤرشفة فقط
                ->orderByDesc('latency.hr_latency_id')
                ->paginate(15, ['*'], 'page', $_SESSION['pagn']);
            
            $this->Smarty->assign('latenciesRequests', $latenciesRequests);
        } catch (Exception $e) {
            $this->Smarty->assign('latenciesRequests', []);
        }

        // deductaddition
        try {
            $deductadditionRequests = DB::table('hr_deductaddition as deductaddition')
                ->select(
                    'deductaddition.*',
                    'user.sh_user_full_name as user_name'
                )
                ->leftjoin('sh_user as user', 'deductaddition.hr_deductaddition_user_id', '=', 'user.sh_user_id')
                ->where('deductaddition.hr_deductaddition_org_id', $_SESSION['organization']->id)
                ->where('deductaddition.hr_deductaddition_request_success', 2) // الطلبات المؤرشفة فقط
                ->orderByDesc('deductaddition.hr_deductaddition_id')
                ->paginate(30, ['*'], 'page', $_SESSION['pagn']);
            
            // تحميل بيانات المستخدمين وبيانات المسير لكل طلب
            foreach ($deductadditionRequests as $request) {
                try {
                    // تحميل بيانات المستخدم
                    $request->userObject = User::readID((int)$request->hr_deductaddition_user_id);
                    
                    // تحميل بيانات المسير
                    if ($request->hr_deductaddition_manipulation_batche_id) {
                        try {
                            $batch = DB::table('payroll_batch')
                                ->where('payroll_batch_id', (int)$request->hr_deductaddition_manipulation_batche_id)
                                ->first();
                            
                            if ($batch) {
                                $request->batch_name = $batch->payroll_batch_name;
                                $request->batchObject = $batch;
                            } else {
                                $request->batch_name = 'غير محدد';
                                $request->batchObject = null;
                            }
                        } catch (Exception $e) {
                            $request->batch_name = 'غير محدد';
                            $request->batchObject = null;
                        }
                    }
                } catch (UserException $e) {
                    $request->userObject = null;
                    $request->batch_name = 'غير محدد';
                    $request->batchObject = null;
                }
            }
            
            $this->Smarty->assign('deductadditionRequests', $deductadditionRequests);
        } catch (Exception $e) {
            $this->Smarty->assign('deductadditionRequests', []);
        }

        $_SESSION['s_personnel_archive_token'] = md5(rand(0000, 9999));
    }

    public function confirmDeductaddition($parm, $post)
    {
        // التحقق من وجود الـ ID
        $deductadditionId = (int)$parm[0];
        if (!$deductadditionId) {
            $this->Smarty->assign('deductadditionRequest', []);
            return;
        }

        try {
            // البحث عن السجل باستخدام نفس طريقة دالة show
            $deductadditionRecord = DB::table('hr_deductaddition')
                ->where('hr_deductaddition_id', $deductadditionId)
                ->where('hr_deductaddition_org_id', $_SESSION['organization']->id)
                ->where('hr_deductaddition_request_success', 2) // التأكد أنه مؤرشف
                ->first();

            if ($deductadditionRecord) {
                $this->Smarty->assign('deductadditionRequest', $deductadditionRecord);
            } else {
                $this->Smarty->assign('deductadditionRequest', []);
            }
            
        } catch (Exception $e) {
            $this->Smarty->assign('deductadditionRequest', []);
        }
    }

    public function executeDeductadditionRestore($parm, $post)
    {
        // التحقق من وجود الـ ID
        $deductadditionId = (int)$parm[0];
        if (!$deductadditionId) {
            Notification::failAlert('معرف الطلب غير صحيح');
            redirect('PersonnelArchive/show', ['deductaddition']);
            return;
        }

        try {
            // البحث عن السجل باستخدام نفس طريقة دالة show
            $deductadditionRecord = DB::table('hr_deductaddition')
                ->where('hr_deductaddition_id', $deductadditionId)
                ->where('hr_deductaddition_org_id', $_SESSION['organization']->id)
                ->where('hr_deductaddition_request_success', 2) // التأكد أنه مؤرشف
                ->first();

            if (!$deductadditionRecord) {
                Notification::failAlert('الطلب غير موجود أو غير مؤرشف');
                redirect('PersonnelArchive/show', ['deductaddition']);
                return;
            }

            // تحديث حالة الطلب إلى مستعاد (1)
            DB::table('hr_deductaddition')
                ->where('hr_deductaddition_id', $deductadditionId)
                ->update(['hr_deductaddition_request_success' => 1]);

            Notification::updatedAlert();
        } catch (Exception $e) {
            Notification::failAlert('حدث خطأ أثناء استعادة الطلب');
        }

        redirect('PersonnelArchive/show', ['deductaddition']);
    }

    public function confirmAbsence($parm, $post)
    {
        try {
            $this->Smarty->assign('absenceRequest', HRAttendanceRequest::readID((int)$parm[0]));
        } catch (HRAttendanceRequestException $e) {
            $this->Smarty->assign('absenceRequest', []);
        }
    }

    public function confirmLatency($parm, $post)
    {

        try {
            $this->Smarty->assign('latencyRequest', HRLatencyRequest::readID((int)$parm[0]));
        } catch (HRLatencyRequestException $e) {
            $this->Smarty->assign('latencyRequest', []);
        }
    }
}