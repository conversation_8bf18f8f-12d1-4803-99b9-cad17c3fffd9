<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bsc_P051_leavesettings extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case 'menu':

                $_SESSION['s_leave_setting_tab'] = 'templates';
                $_SESSION['s_tabulated_active_tab'] = 'tabulatedleaves';
                $_SESSION['s_leave_active_tab'] = 'employeelist';

                break;

            case 'weekdays':

                try {

                    $organization = Organization::readID($_SESSION['organization']->id);
                    $organization->weekleaves_ids = implode(",", $post['weekleaves_ids']);;
                    $organization->update();

                    Notification::updatedAlert();

                } catch (OrganizationException $e) {
                }

                $_SESSION['s_leave_setting_tab'] = 'weekdays';

                break;

            case 'templates':

                switch ($parm[1]) {

                    case 'insert':

                        if (!empty($parm[2]) && $parm[2] == $_SESSION['s_empleaves_token']) {

                            $leave = new LeaveType();
                            $post['annual_intrvl_lngth'] = (int)$post['annual_intrvl_lngth'];
                            $post['transfer_max_length'] = (int)$post['transfer_max_length'];
                            $post['activation'] = (int)$post['activation'];
                            $leave->bindProperties($post);
                            $leave->org_id = $_SESSION['organization']->id;
                            $leave->leav_type = 640;
                            $leave->created_by = $_SESSION['user']->id;
                            $leave->created_date = date('Y-m-d');
                            $leave->save();

                            Notification::createdAlert();
                        }

                        break;

                    case 'update':

                        if (!empty($parm[2]) && $parm[2] == $_SESSION['s_empleaves_token']) {

                            try {

                                $leave = LeaveType::readID((int)$parm[3] ?? 0);
                                $leave->bindProperties($post);
                                $leave->leav_type = 640;
                                $leave->update();

                                try {

                                    $allowedLeaves = LeaveAllowed::read([
                                        LeaveAllowed::ORG_ID => $leave->org_id,
                                        LeaveAllowed::LEAVE_ID => $leave->id
                                    ]);

                                    foreach ($allowedLeaves as $allowedLeave) {
                                        if ($allowedLeave->max_credit < $leave->transfer_max_length) {
                                            try {

                                                $allowedLeave->max_credit = $leave->transfer_max_length;
                                                $allowedLeave->update();

                                            } catch (LeaveAllowedException $e) {

                                            }
                                        }
                                    }
                                } catch (LeaveAllowedException $e) {
                                }

                            } catch (LeaveTypeException $e) {
                            }

                            Notification::updatedAlert();

                        }

                        break;

                    case 'updatesimple':

                        if (!empty($parm[2]) && $parm[2] == $_SESSION['s_empleaves_token']) {

                            try {

                                $leave = LeaveType::readID((int)$parm[3] ?? 0);
                                $leave->bindProperties($post);
                                $leave->update();

                                Notification::updatedAlert();

                            } catch (LeaveTypeException $e) {
                            }
                        }

                        break;

                    case 'delete':

                        if (!empty($parm[2]) && $parm[2] == $_SESSION['s_empleaves_token']) {

                            try {

                                $leave = LeaveType::readID((int)$parm[3] ?? 0);
                                $leave->delete();

                                Notification::deletedAlert();

                            } catch (LeaveTypeException $e) {
                            }

                        }

                        break;

                }

                $_SESSION['s_leave_setting_tab'] = 'templates';

                break;

            case 'tabtemplates':

                switch ($parm[1]) {

                    case 'insert':

                        if (isset($parm[2]) && $parm[2] == $_SESSION['s_empleaves_token']) {

                            $leave = new LeaveType();
                            $leave->bindProperties($post);
                            $leave->org_id = $_SESSION['organization']->id;
                            $leave->leav_type = 641;
                            $leave->created_by = $_SESSION['user']->id;
                            $leave->created_date = date('Y-m-d');
                            $leave->save();

                            Notification::updatedAlert();

                        }

                        break;

                    case 'update':

                        if (isset($parm[2]) && $parm[2] == $_SESSION['s_empleaves_token']) {

                            try {

                                $leave = LeaveType::readID((int)$parm[3] ?? 0);
                                $leave->bindProperties($post);
                                $leave->update();

                                Notification::updatedAlert();

                            } catch (LeaveTypeException $e) {
                            }

                        }

                        break;

                    case 'delete':

                        if (isset($parm[2]) && $parm[2] == $_SESSION['s_empleaves_token']) {

                            try {

                                $leave = LeaveType::readID((int)$parm[3] ?? 0);
                                $leave->delete();

                                Notification::deletedAlert();

                            } catch (LeaveTypeException $e) {
                            }

                        }

                        break;
                }

                $_SESSION['s_leave_setting_tab'] = 'tabulated';
                $_SESSION['s_tabulated_active_tab'] = 'tabulatedsetting';

                break;
        }

        try {
            $this->Smarty->assign('LeaveTypesList', LeaveType::read([
                LeaveType::ORG_ID => $_SESSION['organization']->id,
                LeaveType::LEAV_TYPE => 640
            ]));
        } catch (LeaveTypeException $e) {
        }

        try {
            $this->Smarty->assign('LeaveSchedualedList', LeaveType::read([
                LeaveType::ORG_ID => $_SESSION['organization']->id,
                LeaveType::LEAV_TYPE => 641
            ]));
        } catch (LeaveTypeException $e) {
        }

        $this->Smarty->assign('EditPrivilege',Privilege::isAnEmployeeHasAPrivilegeOnOperation(
            $_SESSION['organization'],
            $_SESSION['user'],
            Operation::readByCode('leavesettings'),
            '',
            Privilege::UPDATE));

        $this->Smarty->assign('weekDaysList', Setting::getList(25));
        $_SESSION['s_empleaves_token'] = md5(rand(00000, 99999));
    }

    public function templateadd($parm, $post)
    {

        $this->Smarty->assign('lev_activation', Setting::getList(19));
        $this->Smarty->assign('gender_list', Setting::getList(27));
        $this->Smarty->assign('transfer_credit_options', Setting::getList(179));
        $this->Smarty->assign('monthList', Setting::getList(59));

        $_SESSION['s_leave_setting_tab'] = 'templates';
        $_SESSION['s_empleaves_token'] = md5(rand(00000, 99999));

    }

    public function templateedit($parm, $post)
    {
        try {

            $row = LeaveType::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('row', $row);

            switch ($row->annual_intrvl_type) {
                case 830:
                    $this->Smarty->assign('jsCode9',
                        ' <script type="text/javascript"> $(document).ready(function () { $("#extraInput").hide("fast"); }); </script> ');
                    break;

                case 831:
                    $this->Smarty->assign('jsCode1',
                        ' <script type="text/javascript"> $(document).ready(function () { $("#extraInput").show("fast");$("#extraTransfere").show("fast");$("#divdaysnumber").show("fast");}); </script> ');
                    break;

                case 837:
                    $this->Smarty->assign('jsCode10',
                        ' <script type="text/javascript"> $(document).ready(function () { $("#extraInput").show("fast"); $("#extraTransfere").show("fast"); $("#divdaysnumber").hide("fast");}); </script> ');
                    if ($row->transfer_no == 838) {
                        $this->Smarty->assign('jsCode50',
                            ' <script type="text/javascript"> $(document).ready(function () { $("#firstUse").show("fast");}); </script> ');
                    }
                    break;

                case 894:
                    $this->Smarty->assign('jsCode51',
                        ' <script type="text/javascript"> $(document).ready(function () {$("#extraInput").show("fast"); $("#extraTransfere").hide("fast");$("#divzSomeDaySomeMonthYearly").hide("fast");}); </script> ');
                    break;

                case 884:
                    $this->Smarty->assign('jsCode9',
                        ' <script type="text/javascript"> $(document).ready(function () { $("#extraInput").hide("fast"); }); </script> ');
                    break;
            }


            switch ($row->transfer_type) {

                case 808:
                    $this->Smarty->assign('jsCode2', ' <script type="text/javascript"> $(document).ready(function () { 
                    $("#divzSomeDaySomeMonthYearly").hide("fast");                    
                }); </script> ');
                    break;

                case 809:
                    $this->Smarty->assign('jsCode8', ' <script type="text/javascript"> $(document).ready(function () { 
                    $("#divzSomeDaySomeMonthYearly").show("fast");
                    $("#divdaysnumber").show("fast");
            }); </script> ');
                    break;

                case 838:
                    $this->Smarty->assign('jsCode3', ' <script type="text/javascript"> $(document).ready(function () { 
                    $("#divzSomeDaySomeMonthYearly").show("fast");
                    $("#divdaysnumber").hide("fast");
                }); </script> ');

                    if ($row->annual_intrvl_type == 837) {
                        $this->Smarty->assign('jsCode50',
                            ' <script type="text/javascript"> $(document).ready(function () { $("#firstUse").show("fast");}); </script> ');
                    }
                    break;
            }


            switch ($row->transfer_no) {
                case 814:
                    $this->Smarty->assign('jsCode6', ' <script type="text/javascript"> $(document).ready(function () { 
                        $("#divzSomeDaySomeMonthYearly").show("fast");}); </script> ');
                    break;

                case 810:
                case 811:
                case 812:
                case 813:
                    $this->Smarty->assign('jsCode52', ' <script type="text/javascript"> $(document).ready(function () { 
                    $("#divTSomeDaySomeMonthYearly").hide("fast");    
            }); </script> ');
                    break;

                default:
                    $this->Smarty->assign('jsCode52', ' <script type="text/javascript"> $(document).ready(function () { 
                    $("#divTSomeDaySomeMonthYearly").show("fast");    
            }); </script> ');
                    break;
            }

        } catch (LeaveTypeException $e) {
            $this->Smarty->assign('row', []);
        }

        $this->Smarty->assign('lev_activation', Setting::getList(19));
        $this->Smarty->assign('gender_list', Setting::getList(27));
        $this->Smarty->assign('monthList', Setting::getList(59));
        $this->Smarty->assign('transfer_credit_options', Setting::getList(179));

        $_SESSION['s_leave_setting_tab'] = 'templates';
        $_SESSION['s_empleaves_token'] = md5(rand(00000, 99999));
    }

    public function templateconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', LeaveType::readID((int)$parm[0] ?? 0));
        } catch (LeaveTypeException $e) {
            $this->Smarty->assign('row',[]);
        }
        $_SESSION['s_leave_setting_tab'] = 'templates';
        $_SESSION['s_empleaves_token'] = md5(rand(00000, 99999));
    }

    /*
    * Tabulated Templates
    */
    public function tabtemplateadd($parm, $post)
    {
        $this->Smarty->assign('lev_activation', Setting::getList(19));
        $_SESSION['s_leave_setting_tab'] = 'templates';
        $_SESSION['s_empleaves_token'] = md5(rand(00000, 99999));
    }

    public function tabtemplateedit($parm, $post)
    {
        try {
            $this->Smarty->assign('row', LeaveType::readID((int)$parm[0] ?? 0));
        } catch (LeaveTypeException $e) {
        }

        $this->Smarty->assign('lev_activation', Setting::getList(19));
        $_SESSION['s_leave_setting_tab'] = 'templates';
        $_SESSION['s_empleaves_token'] = md5(rand(00000, 99999));
    }

    public function templateeditsimple($parm, $post)
    {
        try {
            $this->Smarty->assign('row', LeaveType::readID((int)$parm[0] ?? 0));
        } catch (LeaveTypeException $e) {
        }

        $this->Smarty->assign('lev_activation', Setting::getList(19));
        $_SESSION['s_leave_setting_tab'] = 'templates';
        $_SESSION['s_empleaves_token'] = md5(rand(00000, 99999));
    }

    public function tabtemplateconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', LeaveType::readID((int)$parm[0] ?? 0));
        } catch (LeaveTypeException $e) {
        }
        $_SESSION['s_leave_setting_tab'] = 'templates';
        $_SESSION['s_empleaves_token'] = md5(rand(00000, 99999));
    }

    public function information($parm, $post)
    {
        $this->Smarty->assign('infoType', $parm['0']);
    }
}