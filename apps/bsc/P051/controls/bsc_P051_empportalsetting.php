<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class bsc_P051_empportalsetting extends Controller
{
    function show($parm, $post)
    {

        switch ($parm[0]) {

            case 'updateInformation':

                if ($_SESSION['s_empportal_token'] === $parm[1]) {

                    try {

                        $clientConfiguration = ClientConfiguration::read([ClientConfiguration::ORG_ID => $_SESSION['organization']->id, ClientConfiguration::OPR_ID => ClientConfiguration::CLIENT_CONFIGURATION_EMPLOYEE_DASHBOARD_INFORMATION,])[0];

                    } catch (ClientConfigurationException $e) {
                        $clientConfiguration = new ClientConfiguration();
                    }

                    if ($clientConfiguration) {

                        try {

                            $clientConfiguration->org_id = $_SESSION['organization']->id;
                            $clientConfiguration->opr_id = ClientConfiguration::CLIENT_CONFIGURATION_EMPLOYEE_DASHBOARD_INFORMATION;
                            $clientConfiguration->ids = implode(',', $post['information']);
                            $clientConfiguration->save();

                            Notification::updatedAlert();

                        } catch (ClientConfigurationException $e) {

                        }
                    }
                }

                break;

            case 'updateRequest':

                if ($_SESSION['s_empportal_token'] === $parm[1]) {

                    try {

                        $clientConfiguration = ClientConfiguration::read([ClientConfiguration::ORG_ID => $_SESSION['organization']->id, ClientConfiguration::OPR_ID => ClientConfiguration::CLIENT_CONFIGURATION_EMPLOYEE_DASHBOARD_REQUESTS,])[0];

                    } catch (ClientConfigurationException $e) {
                        $clientConfiguration = new ClientConfiguration();
                    }

                    if ($clientConfiguration) {

                        try {

                            $clientConfiguration->org_id = $_SESSION['organization']->id;
                            $clientConfiguration->opr_id = ClientConfiguration::CLIENT_CONFIGURATION_EMPLOYEE_DASHBOARD_REQUESTS;
                            $clientConfiguration->ids = implode(',', $post['requests']);
                            $clientConfiguration->save();

                            Notification::updatedAlert();

                        } catch (ClientConfigurationException $e) {

                        }
                    }
                }

                break;

            case 'insertCDS':

                if ($_SESSION['s_empportal_token'] === $parm[1]) {

                    try {

                        $cd = new st_contactdata();
                        $cd->st_contactdata_org_id = CLIENT_ID;
                        $cd->st_contactdata_user_id = $post['st_contactdata_user_id'];
                        $cd->st_contactdata_sex_available = $post['st_contactdata_sex_available'];
                        $cd->st_contactdata_units_available = $post['st_contactdata_units_available'];
                        $cd->st_contactdata_created_by = $post['st_contactdata_created_by'];
                        $cd->st_contactdata_created_date = $post['st_contactdata_created_date'];
                        $cd->save();

                        Notification::createdAlert();

                    } catch (ModelException $e) {

                    }
                }
                break;

            case 'updateCDS':

                if ($_SESSION['s_empportal_token'] === $parm[1]) {

                    try {

                        $cd = st_contactdata::readByID((int)$parm[2] ?? 0);
                        $cd->st_contactdata_sex_available = $post['st_contactdata_sex_available'];
                        $cd->st_contactdata_units_available = $post['st_contactdata_units_available'];
                        $cd->st_contactdata_created_by = $post['st_contactdata_created_by'];
                        $cd->st_contactdata_created_date = $post['st_contactdata_created_date'];
                        $cd->save();

                        Notification::updatedAlert();

                    } catch (ModelException $e) {

                    }
                }

                break;

            case 'deleteCDS':

                if ($_SESSION['s_empportal_token'] === $parm[1]) {

                    try {

                        $cd = st_contactdata::readByID((int)$parm[2] ?? 0);
                        $cd->delete();

                        Notification::deletedAlert();

                    } catch (ModelException $e) {

                    }

                }

                break;
        }

        try {
            $this->Smarty->assign('clientConfigurationArray', ClientConfiguration::getClientConfigurationArray((int)$_SESSION['organization']->id));
        } catch (ClientConfigurationException $e) {
            $this->Smarty->assign('clientConfigurationArray', []);
        }

        $this->Smarty->assign("requests", Setting::getEmployeeDashboardRequestsList());
        $this->Smarty->assign("informations", Setting::getEmployeeDashboardInformationList());

        try {
            $this->Smarty->assign('ContactSettingList', st_contactdata::readAll());
        } catch (ModelException $e) {
            $this->Smarty->assign('ErrorMessage', 1);
        }

        $this->Smarty->assign('EditPrivilege', Privilege::isAnEmployeeHasAPrivilegeOnOperation($_SESSION['organization'], $_SESSION['user'], Operation::readByCode('empportalsetting'), '', Privilege::UPDATE));

        try {
            $this->Smarty->assign('documents', Document::read([Document::OPERATION_CODE => $_SESSION['oprname'],]));
        } catch (DocumentException $e) {

        }
        $_SESSION['s_empportal_token'] = md5(rand(0, 99999));

    }

    function addCDS($parm, $post)
    {

        try {
            $this->Smarty->assign('employees', Vacant::getEmployeeListPermitToAccessContactData($_SESSION['organization']));
        } catch (VacantException $e) {
            $this->Smarty->assign('employees', []);
        }

        try {
            $this->Smarty->assign('SexList', Setting::getList(217));
        } catch (ModelException $e) {
            $this->Smarty->assign('ErrorMessage', []);
        }

        try {
            $this->Smarty->assign('UnitsList', Setting::getList(218));
        } catch (ModelException $e) {
            $this->Smarty->assign('ErrorMessage', 1);
        }

        $_SESSION['s_empportal_token'] = md5(rand(0, 99999));
    }

    function editCDS($parm, $post)
    {

        try {
            $this->Smarty->assign('ContactDataRow', st_contactdata::readByID((int)$parm[0] ?? 0));
        } catch (ModelException $e) {
            $this->Smarty->assign('ErrorMessage', 1);
        }

        try {
            $this->Smarty->assign('SexList', Setting::getList(217));
        } catch (ModelException $e) {
            $this->Smarty->assign('ErrorMessage', 1);
        }

        try {
            $this->Smarty->assign('UnitsList', Setting::getList(218));
        } catch (ModelException $e) {
            $this->Smarty->assign('ErrorMessage', 1);
        }

        $_SESSION['s_empportal_token'] = md5(rand(0, 99999));
    }

    function confirmCDS($parm, $post)
    {
        try {
            $this->Smarty->assign('ContactDataRow', st_contactdata::readByID((int)$parm[0] ?? 0));
        } catch (ModelException $e) {
            $this->Smarty->assign('ErrorMessage', 1);
        }

        $_SESSION['s_empportal_token'] = md5(rand(0, 99999));
    }

    function add($parm, $post, $files)
    {

        try {

            $document = new Document();
            $post['permission'] = isset($post['permission']) ? json_encode($post['permission']) : null;
            $document->bindProperties($post);
            $document->fileArray = $files['fileArray'];
            $document->client_id = $_SESSION['organization']->id;
            $document->operation_code = $_SESSION['oprname'];
            $document->created_by = $_SESSION['user']->id;
            $document->created_date = date('Y-m-d');
            if (empty($post['name'])) {
                $documentName = explode('.', $files['fileArray']['name']);
                $document->name = $documentName[0];
            }
            if ($document->save()) {
                Notification::createdAlert();
                redirect('empportalsetting/show');
            }

        } catch (DocumentException $e) {
            /**
             * @TODO handling other errors and display some feed back for end user
             */
            $feedback = ($e->getCode() === self::CODE_FILE_TYPE_NOT_ALLOWED) ? self::MESSAGE_GNR_FILE_TYPE_NOT_ALLOWED : '';
            Notification::alertMessage(Notification::ERROR, $feedback);
        }

        $_SESSION['s_empportal_token'] = md5(rand(0, 99999));

    }

    public function edit($parm, $post)
    {

        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }


        $_SESSION['s_documents_token'] = md5(rand(0, 10000000));

    }

    public function update($parm, $post)
    {

        try {

            $document = Document::readID((int)$parm[1] ?? 0);
            $post['permission'] = isset($post['permission']) ? json_encode($post['permission']) : null;
            $document->bindProperties($post);
            $document->update();

            redirect('empportalsetting/show');
            Notification::updatedAlert();

        } catch (DocumentException $e) {

        }


        $_SESSION['s_documents_token'] = md5(rand(0, 10000000));
    }

    public function confirm($parm, $post)
    {
        if ($parm[1]) {

            try {
                $document = Document::readID((int)$parm[1] ?? 0);
                $document->delete();
                redirect('empportalsetting/show');
                Notification::deletedAlert();

            } catch (DocumentException $e) {
            }

        }
        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

        $_SESSION['s_documents_token'] = md5(rand(0, 10000000));

    }



}
