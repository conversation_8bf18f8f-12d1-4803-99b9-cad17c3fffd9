<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bsc_P051_committeebuilding extends Controller
{

    public function show($parm, $post)
    {

        switch ($parm[0]) {
            case 'menu':
                $_SESSION['s_active_tab'] = "committee";
                break;

            case 'tab':
                $_SESSION['s_active_tab'] = $parm[1];
                break;

            case 'save_session':
                $_SESSION['s_committee_id'] = $parm[1];
                $_SESSION['s_active_tab'] = "members";
                break;

            case 'insert':

                if ($_SESSION['s_committeebuilding_token'] == $parm[1]) {

                    try {

                        $committee = new Committee();
                        $committee->bindProperties($post);
                        $committee->org_id = $_SESSION['organization']->id;
                        $committee->prg_id = $_SESSION['program']->id;
                        $committee->start_date = $this->Date->get_date('ad', $post["start_date"]);
                        $committee->end_date = $this->Date->get_date('ad', $post["end_date"]);
                        $committee->created_by = $_SESSION['user']->id;
                        $committee->created_date = date('Y-m-d');
                        $committee->save();

                        Notification::createdAlert();

                    } catch (CommitteeException $e) {

                    }

                }

                $_SESSION['s_active_tab'] = "committee";
                break;

            // update existed record
            case 'update':

                if ($_SESSION['s_committeebuilding_token'] == $parm[1]) {

                    try {

                        $committee = Committee::readID((int) $parm[2] ?? 0);
                        $committee->bindProperties($post);
                        $committee->start_date = $this->Date->get_date('ad', $post["start_date"]);
                        $committee->end_date = $this->Date->get_date('ad', $post["end_date"]);
                        $committee->save();

                        Notification::updatedAlert();

                    } catch (CommitteeException $e) {

                    }

                }

                $_SESSION['s_active_tab'] = "committee";
                break;

            case 'delete':

                if ($_SESSION['s_committeebuilding_token'] == $parm[1]) {

                    try {

                        $committee = Committee::readID((int) $parm[2] ?? 0);
                        $committee->delete();

                        Notification::deletedAlert();

                    }catch (CommitteeException $e){
                    }

                }

                $_SESSION['s_active_tab'] = "committee";
                break;

            case 'member':

                switch ($parm[1]) {

                    case 'insert':

                        if ($_SESSION['s_committeebuilding_token'] == $parm[2]) {

                            try {

                                $member = new CommitteeMember();
                                $member->bindProperties($post);
                                $member->org_id = $_SESSION['organization']->id;
                                $member->created_by = $_SESSION['user']->id;
                                $member->committee_id = $_SESSION['s_committee_id'];
                                $member->notify_types = implode(',', $post["notify_types"]);
                                $member->created_date = date('Y-m-d');
                                $member->save();

                                Notification::createdAlert();

                            }catch (CommitteeMemberException $e){
                            }
                        }

                        break;

                    case 'update':

                        if ($_SESSION['s_committeebuilding_token'] == $parm[2]) {

                            try {

                                $member = CommitteeMember::readID( (int) $parm[3] ?? 0);
                                $member->bindProperties($post);
                                $member->committee_id = $_SESSION['s_committee_id'];
                                $member->notify_types = implode(',', $post["notify_types"]);
                                $member->save();

                                Notification::updatedAlert();

                            }catch (CommitteeMemberException $e){
                            }
                        }

                        break;

                    case 'delete':

                        if ($_SESSION['s_committeebuilding_token'] == $parm[2]) {

                            try {

                                $member = CommitteeMember::readID( (int) $parm[3] ?? 0);
                                $member->delete();

                                Notification::deletedAlert();

                            }catch (CommitteeMemberException $e){
                            }
                        }

                        $_SESSION['s_active_tab'] = "members";

                        break;
                }

                $_SESSION['s_active_tab'] = "members";
                break;
        }

        try {

            $this->Smarty->assign('committees', Committee::read([
                Committee::ORG_ID => $_SESSION['organization']->id
            ]));

        } catch (CommitteeException $e) {

            $this->Smarty->assign('committees', []);

        }

        if(!empty($_SESSION['s_committee_id'])){

            try{

                $this->Smarty->assign('currentCommittee',Committee::readID((int)$_SESSION['s_committee_id']));

            }catch (CommitteeException $e){

                $this->Smarty->assign('currentCommittee', []);

            }

            try {

                $this->Smarty->assign('members', CommitteeMember::read([
                    CommitteeMember::COMMITTEE_ID => $_SESSION['s_committee_id']
                ]));

            } catch (CommitteeMemberException $e) {

                $this->Smarty->assign('members', []);

            }
        }

        $_SESSION['s_committeebuilding_token'] = md5(rand(0000, 9999));

    }

    public function add($parm, $post)
    {

        $_SESSION['s_committeebuilding_token'] = md5(rand(0000, 9999));
    }

    public function edit($parm, $post)
    {

        try {
            $this->Smarty->assign('committee', Committee::readID((int)$parm[0] ?? 0));
        } catch (CommitteeException $e) {
            $this->Smarty->assign('committee', []);
        }

        $_SESSION['s_committeebuilding_token'] = md5(rand(0000, 9999));
    }

    public function confirm($parm, $post)
    {

        try {
            $this->Smarty->assign('committee', Committee::readID((int)$parm[0] ?? 0));
        } catch (CommitteeException $e) {
            $this->Smarty->assign('committee', []);
        }

        $_SESSION['s_committeebuilding_token'] = md5(rand(0000, 9999));
    }

    public function addmember($parm, $post)
    {

        $this->Smarty->assign('employees', Vacant::getEmployeeListByEntity($_SESSION['organization']));

        $this->Smarty->assign('notifications', Setting::getList(199));
        $this->Smarty->assign('activations', Setting::getList(147));

        $_SESSION['s_committeebuilding_token'] = md5(rand(0000, 9999));
    }

    public function editmember($parm, $post)
    {

        try{
            $this->Smarty->assign('member', CommitteeMember::readID((int) $parm[0]  ?? 0));
        }catch (CommitteeMemberException $e){
            $this->Smarty->assign('member',[]);
        }

        $this->Smarty->assign('employees', Vacant::getEmployeeListByEntity($_SESSION['organization']));

        $this->Smarty->assign('notifications', Setting::getList(199));
        $this->Smarty->assign('activations', Setting::getList(147));

        $_SESSION['s_committeebuilding_token'] = md5(rand(0000, 9999));
    }

    public function confirmmember($parm, $post)
    {
        try{
            $this->Smarty->assign('member', CommitteeMember::readID((int) $parm[0]  ?? 0));
        }catch (CommitteeMemberException $e){
            $this->Smarty->assign('member',[]);
        }

        $_SESSION['s_committeebuilding_token'] = md5(rand(0000, 9999));

    }

}