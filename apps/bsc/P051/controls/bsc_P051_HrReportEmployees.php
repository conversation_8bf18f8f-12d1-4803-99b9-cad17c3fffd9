<?php

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');
//use Models\User;
use Models\HumanResource\Job as JobModel;
use Models\HumanResource\Request as RequestModel;

class bsc_P051_HrReportEmployees extends Controller
{

    /**
     * @param $param
     * @param $post
     */
    public function show($parm, $post)
    {
        switch ($parm[0]) {
            case 'menu':
                $_SESSION['report_data'] = null;
                break;

            case 'save_session':
                $_SESSION['report_data'] = $post;
                break;
        }

        try {
            $this->Smarty->assign('employees', Vacant::getEmployeeList($_SESSION['organization']));
        } catch (UserException $e) {
            $this->Smarty->assign('employees', []);
        }


        $_SESSION['emp_user_id'] = $post['employee_id'];
        try {
            $employee = User::readID((int)$_SESSION['emp_user_id'] ?? 0);
        } catch (UserException $e) {
            $employee = new Vacant();
        }

        $this->Smarty->assign('employee', $employee);

        $this->getEmployeeRequests($employee);
        $this->getEmployeeAccountabilties($employee);
        $this->getEmployeeCommittees();
        $this->getEmployeeJobs();

        try {
            $vacations = LeaveAllowed::read([
                LeaveAllowed::USER_ID => $employee->id
            ]);
        } catch (LeaveAllowedException $e) {
            $vacations = null;
        }

        $this->Smarty->assign('vacations', $vacations);

        $this->Smarty->assign('post', $post);


    }

    /**
     * @param $parm
     */
    public function jobCardHTML($parm)
    {
        $job = JobModel::find($parm[0]);
        $this->Smarty->assign('job', $job);
    }

    private function getEmployeeJobs()
    {
        try {
            if($_SESSION['emp_user_id'] != 0) {
                $employeeJobs = JobModel::userJobs($_SESSION['emp_user_id']);
                $this->Smarty->assign('employeeJobs', $employeeJobs);
            }
        } catch (Exception $e) {
            $this->Smarty->assign('employeeJobs', []);
        }

    }

    private function getEmployeeCommittees()
    {
        try {
            $committees = CommitteeMember::read([
                CommitteeMember::USER_ID => (int)$_SESSION['emp_user_id']
            ]);;

        } catch (CommitteeMemberException $e) {
            $committees = [];
        }
        try {
            $committees = collect($committees);
            $employeeCommittees = Committee::convertFromQB(DB::table('es_committee')
                ->whereIn(Committee::ID, $committees->pluck('committee_id'))
                ->get());

            foreach ($employeeCommittees as $committee) {
                $committee->memberships = $committees
                    ->where('committee_id', $committee->id);
            };

        } catch (CommitteeException $e) {
            $employeeCommittees = [];
        }
        $this->Smarty->assign('employeeCommittees', $employeeCommittees);
    }

    private function getEmployeeRequests($employee)
    {
        $configTypesArray = [];

        $request = RequestModel::withTableNames()
            ->where('wf_request_user_id', '=', (int)$employee->id)
            ->totalStatus()
            ->first();
        $this->Smarty->assign('request', $request);


        try {
            $configTypesArray = ClientConfiguration::getClientConfigurationArray((int)$_SESSION['organization']->id);
            $this->Smarty->assign('clientConfigurationArray', $configTypesArray);
        } catch (ClientConfigurationException $e) {
            $this->Smarty->assign('clientConfigurationArray', []);
        }

//        $this->Smarty->assign('employee', Vacant::getEmployeeJobs($_SESSION['organization']->id, $employee->userObject->id, Vacant::EMPLOYEE_BASIC_VACANT));

        if (in_array(Setting::EMPLOYEE_DASHBOARD_LEAVE_REQUEST, $configTypesArray)) {
//            $this->Smarty->assign('hr_lev_list', \Models\HumanResource\Request::getEmployeeFinishedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_LEAVE)->get());
            $this->Smarty->assign('hr_lev_list', Request::getEmployeeFinshedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_LEAVE));
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_RETREAT_LEAVE_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('hr_retreat_leave', Request::getEmployeeFinshedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_LEAVE_RETREAT, $_SESSION['prg']->id));
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_PERMISSION_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('hr_perm_list', Request::getEmployeeFinshedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_PERMISSION));
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_ADVANCE_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('hr_advancerequest_list', Request::getEmployeeFinshedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_ADVANCE_REQUEST));
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_MANDATE_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('hr_mndt_list', Request::getEmployeeFinshedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_MANDATE));
            $this->Smarty->assign('hr_mndtfees_list', Request::getEmployeeFinshedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_MANDATE_FEES));
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_ADDITIONAL_WORK_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('hr_otwrk_list', Request::getEmployeeFinshedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_OUTWORK));
            $this->Smarty->assign('hr_outworkfees_list', Request::getEmployeeFinshedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_OUTWORK_FEES));

        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_FINEXCH_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('fin_exch_request_list', Request::getEmployeeFinshedRequests((int) $employee->id, Request::REQUEST_TYPE_FIN_EXCHANGE, Program::PROGRAM_EMPLOYEE_DASHBOARD_P002));
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_LEAVE_CREDIT_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('leave_credit_request_list', Request::getEmployeeFinshedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_LEAVE_CREDIT_EDIT, Program::PROGRAM_EMPLOYEE_DASHBOARD_P002));
        }

    }

    private function getEmployeeAccountabilties($employee)
    {
        $this->Smarty->assign('attendanceRequests', Request::getEmployeeFinshedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_ATTENDANCE));
        $this->Smarty->assign('latenciesRequests', Request::getEmployeeFinshedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_LATENCY));
    }

    public function print($parm, $post)
    {

        $this->show($parm, session('report_data'));

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));

    }
}
