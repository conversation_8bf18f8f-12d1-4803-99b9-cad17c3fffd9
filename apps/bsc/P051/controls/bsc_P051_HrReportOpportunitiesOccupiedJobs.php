<?php

use <PERSON><PERSON><PERSON>\Core\Reporter\ChartBuilder;

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');
use Models\HumanResource\Unit as UnitModel;
class bsc_P051_HrReportOpportunitiesOccupied<PERSON><PERSON>s extends Controller
{
    /**
     * Show details of the unit
     *
     * @param $parm
     * @param $post
     */
    public function show($parm, $post)
    {
        $_SESSION["unit_id"] = $parm[1];
        session('unit_id', $post['unit_id']);

        $units = UnitModel::active()
            ->where('sh_unt_id', '=', session('unit_id'))
            ->get();

        $units->map(function ($unit) {

            $unit->vacantJobs = DB::table('sh_uao')
                ->join('sh_job', 'sh_uao.sh_uao_job_id', '=', 'sh_job.sh_job_id')
                ->where([
                    ['sh_uao_org_id', (int)$_SESSION['organization']->id],
                    ['sh_uao_job_unt_id', $unit->sh_unt_id],
                    ['sh_uao_quit', 0],
                    ['sh_uao_deleted', 0],
                ])
                ->whereRaw('(sh_uao_user_id IS NULL OR sh_uao_user_id = 0)')
                ->orderBy('sh_job_id')
                ->get();

            $unit->occupiedJobs = DB::table('sh_uao')
                ->join('sh_user', 'sh_uao.sh_uao_user_id', '=', 'sh_user.sh_user_id')
                ->join('sh_job', 'sh_uao.sh_uao_job_id', '=', 'sh_job.sh_job_id')
                ->where([
                    ['sh_uao_org_id', (int)$_SESSION['organization']->id],
                    ['sh_uao_job_unt_id', $unit->sh_unt_id],
                    ['sh_uao_quit', 0],
                    ['sh_uao_deleted', 0],
                ])
                ->whereRaw('sh_uao_user_id IS NOT NULL AND sh_uao_user_id > 0')
                ->orderBy('sh_job_id')
                ->get();

            $unit->jobsCount = DB::table('sh_job')
                ->where('sh_job_unit_id', $unit->sh_unt_id)
                ->count();
        });

        $this->Smarty->assign([
            "units" => UnitModel::active()->get(),
            'unit' => $units->first(),
            'title' => "نسبة الوظائف المشغولة والشاغرة",
            'vacant_values' => $this->getArrayVacantValues($units->first())
        ]);

    }

    /**
     * @param Models\HumanResource\Unit $unit
     * @return array
     */
    public function getArrayVacantValues($unit)
    {
        if(isset($unit)){
            return [
                $unit->vacantJobs->count(),
                $unit->occupiedJobs->count(),
            ];
        }

        return $unit;
    }

    /**
     * Print pdf files
     *
     * @param $parm
     * @param $post
     */
    public function print($parm, $post)
    {
        $this->show($parm, $post);
        generatePdf();
    }
}