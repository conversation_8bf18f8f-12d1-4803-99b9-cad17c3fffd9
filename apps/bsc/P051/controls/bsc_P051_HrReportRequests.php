<?php

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class bsc_P051_HrReportRequests extends Controller
{

    public function show($parm, $post)
    {

        switch ($parm[0]) {
            case 'menu':
                $_SESSION['report_parm'] = null;
                break;

            case 'save_session':
                $_SESSION['report_parm'] = $post;
                break;
        }

        $requests_types = Operation::operationsInPrograms([
            Program::PROGRAM_HUMAN_RESOURCE_P051,
            Program::PROGRAM_EMPLOYEE_DASHBOARD_P002,
        ]);

        $requests_types[] = Operation::readByCode(Operation::WF_OPERATION_FINEXCH);
        array_pull($requests_types, $salariesOperation = 4);
        $this->Smarty->assign('requests_types', $requests_types);

        $this->Smarty->assign('request_status', Setting::getList(99));
        $this->Smarty->assign('employees', Vacant::getEmployeeListByEntityWithBasicVacancies($_SESSION['organization'], null, null, null, null));

        if ($_SESSION['report_parm']) {

            try {
                $this->Smarty->assign('type', Operation::readID($_SESSION['report_parm']['type']));
            } catch (OperationException $e) {
                $this->Smarty->assign('type', null);
            }

            $this->Smarty->assign('requests', $this->prepareReportData(
                $_SESSION['report_parm']['type'],
                $_SESSION['report_parm']['status'],
                $_SESSION['report_parm']['created_by'],
                $_SESSION['report_parm']['user_id'],
                session('report_parm')['from'] ? $this->Date->get_date('ad', session('report_parm')['from']) : '',
                session('report_parm')['from'] ? $this->Date->get_date('ad', session('report_parm')['to']) : ''
            ));

        }

    }

    public function print($parm, $post)
    {

        $this->show($parm, $_SESSION['report_parm']);

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));

    }

    /**
     * @param      $type
     * @param      $status
     * @param null $created_by
     * @param null $user_id
     * @param null $from
     * @param null $to
     *
     * @return mixed
     */
    public function prepareReportData($type, $status, $created_by = null, $user_id = null, $from = null, $to = null)
    {

        $requests = DB::table(wf_request::class)
            ->leftJoin('sh_user as owner', 'wf_request.wf_request_user_id', '=', 'owner.sh_user_id')
            ->leftJoin('sh_user as creator', 'wf_request.wf_request_created_by', '=', 'creator.sh_user_id')
            ->leftJoin('wf_step', 'wf_request.wf_request_step_id', '=', 'wf_step.wf_step_id')
            ->where('wf_request_opr_id', $type)
            ->where('wf_request_send_status', $status)
            ->when($from != "" and $to != "", function ($query) use ($from, $to) {
                return $query->whereBetween('wf_request_created_date', [$from . ' 00:00:00', $to . ' 23:59:59']);
            })
            ->when($created_by, function ($query) use ($created_by) {
                return $query->where('wf_request_created_by', '=', $created_by);
            })
            ->when($user_id, function ($query) use ($user_id) {
                return $query->where('wf_request_user_id', '=', $user_id);
            })
            ->orderBy('wf_request_created_date', 'DESC')
            ->get([
                'wf_request_id',
                'wf_request_success',
                'wf_request_created_date',
                'wf_request_created_by',
                'wf_request_user_id',
                'owner.sh_user_full_name as owner_name',
                'creator.sh_user_full_name as creator_name'
            ]);

        foreach ($requests as $request) {
            $request->wf_request_success = $request->wf_request_success == 1
                ? Translation::translate(null, 'gnr_request_accepted')
                : Translation::translate(null, 'gnr_request_rejected');
        }

        return $requests ?? null;

    }

}
