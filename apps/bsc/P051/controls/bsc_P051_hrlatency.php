<?php

/**
 * Created by PhpStorm.
 * User: altaif
 * Date: 1/14/19
 * Time: 12:00 PM
 */
class bsc_P051_hrlatency extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case 'insert':

                if ($_SESSION['s_hrlatency_token'] == $parm[1]) {

                    $latency = new HRLatencyRequest();
                    $latency->bindProperties($post);
                    $latency->org_id = $_SESSION['organization']->id;
                    $latency->user_id = $post['user_id'];
                    $latency->start_date = $this->Date->get_date('ad', $post['start_date']);
                    $latency->end_date = $this->Date->get_date('ad', $post['end_date']);
                    $latency->manipulation_type = 0;
                    $latency->created_by = $_SESSION['user']->id;
                    $latency->created_date = date('Y-m-d');

                    if ($latency->save()) {

                        HRLatencyRequest::latencyDetails($latency);
                        if ($latency->duration) {
                            Notification::createdAlert();
                        } else {
                            $latency->delete();
                            Notification::alertMessage(Notification::WARNING, 'gnr_user_has_no_latency_duration');
                        }
                    }


                    $_SESSION['s_hrlatency_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'update':

                if ($_SESSION['s_hrlatency_token'] == $parm[1]) {

                    try {

                        $latency = HRLatencyRequest::readID((int)$parm[2] ?? 0);
                        $latency->bindProperties($post);
                        $latency->start_date = $this->Date->get_date('ad', $post['start_date']);
                        $latency->end_date = $this->Date->get_date('ad', $post['end_date']);

                        if ($latency->save()) {

                            Notification::updatedAlert();

                        }

                    } catch (HRLatencyRequestException $e) {

                    }

                    if ($latency) {

                        HRLatencyRequest::latencyDetails($latency);

                    }

                    $_SESSION['s_hrlatency_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'delete':

                if ($_SESSION['s_hrlatency_token'] == $parm[1]) {

                    try {

                        HRLatencyRequest::readID((int)$parm[2] ?? 0)->delete();
                        Notification::deletedAlert();

                    } catch (HRLatencyRequestException $e) {

                    }

                    $_SESSION['s_hrlatency_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'archive':
                if ($_SESSION['s_hrlatency_token'] == $parm[1]) {

                    try {

                        $latency = HRLatencyRequest::readID((int)$parm[2] ?? 0);
                        Notification::updatedAlert();

                    } catch (HRLatencyRequestException $e) {

                    }
                    try {
                        $request = Request::readID((int)$latency->requestEntity->wf_request_id);

                        $request->send_status = Request::REQUEST_IS_ARCHIVED;

                        $request->update();
                    } catch (RequestException $e) {
                    }

                    $_SESSION['s_hrlatency_token'] = md5(rand(0000, 9999));
                }

                break;

        }

        $_SESSION['s_personnel_tab'] = "latency";

        $this->Smarty->assign('latenciesRequests', Request::getRequests($_SESSION['organization'], hr_latency::class, [
            Request::REQUEST_IS_NOT_SEND,
            Request::REQUEST_IS_UNDER_PROCESS,
            Request::REQUEST_REACH_END]));

    }

    public function add($parm, $post)
    {
        $this->Smarty->assign('employees', Vacant::getEmployeeListByEntityWithBasicVacancies($_SESSION['organization'], Vacant::EMPLOYEE_ALL_VACANT));

        try {

            $this->Smarty->assign('grfExistenceNum', Graph::count([
                Graph::STATUS => 1,
                Graph::OPR_CODE => 'hrlatency'
            ]));

        } catch (GraphException $e) {

            $this->Smarty->assign('grfExistenceNum', 0);

        }

        $this->Smarty->assign('latencyRecordTypes', Setting::getList(250));

        $_SESSION['s_personnel_tab'] = "latency";
        $_SESSION['s_hrlatency_token'] = md5(rand(0000, 9999));
    }

    public function edit($parm, $post)
    {

        try {
            $this->Smarty->assign('row', HRLatencyRequest::readID((int)$parm[0] ?? 0));
        } catch (HRLatencyRequestException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_personnel_tab'] = "latency";
        $_SESSION['s_hrlatency_token'] = md5(rand(0000, 9999));
    }

    public function confirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', HRLatencyRequest::readID((int)$parm[0] ?? 0));
        } catch (HRLatencyRequestException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_hrlatency_token'] = md5(rand(0000, 9999));
    }

    public function manipulate($parm, $post)
    {
        switch ($parm[0]) {

            case 'save_session':
                $_SESSION['s_latency_row_id'] = $parm[1];
                break;

            case 'update':

                if ($_SESSION['s_hrlatency_token'] == $parm[1]) {

                    try {
                        $latency = hr_latency::readByID((int)$_SESSION['s_latency_row_id'] ?? 0);
                    } catch (ModelException $e) {

                    }

                    switch ($parm[1]) {

                        case Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_LEAVE_BALANCE:

                            try {
                                $allwedLeaveRow = LeaveAllowed::readID((int)$parm[2] ?? 0);
                            } catch (LeaveAllowedException $e) {
                            }

                            if ($allwedLeaveRow) {

                                try {

                                    $latency->hr_latency_manipulation_type = Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_LEAVE_BALANCE;
                                    $latency->hr_latency_manipulation_batche_id = 0;
                                    $latency->hr_latency_manipulation_trans_id = 0;
                                    $latency->hr_latency_manipulation_leave_id = $allwedLeaveRow->leaveType->id;
                                    $latency->save();

                                    LeaveTransaction::saveLeaveCreditTransaction(
                                        $latency->hr_latency_org_id,
                                        $latency->hr_latency_user_id,
                                        $allwedLeaveRow->leaveType,
                                        LeaveTransaction::LEAVE_TRANSACTION_DEDUCT_UPON_LATENCY,
                                        LeaveCreditRequest::LEAVE_CREDIT_DEDUCTION,
                                        $latency->hr_latency_duration,
                                        Request::REQUEST_TYPE_HR_LATENCY,
                                        $latency->hr_latency_id,
                                        $_SESSION['user']->id
                                    );

                                    Notification::alertMessage(Notification::SUCCESS, Notification::MESSAGE_UPDATED_SUCCESSFULLY);

                                } catch (ModelException $e) {
                                }

                            }

                            break;

                        case Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_SALARY:

                            try {

                                $latency->hr_latency_manipulation_type = Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_SALARY;
                                $latency->hr_latency_manipulation_leave_id = 0;
                                $latency->hr_latency_manipulation_batche_id = 0;
                                $latency->hr_latency_manipulation_trans_id = 0;
                                $latency->save();

                                Notification::alertMessage(Notification::SUCCESS, Notification::MESSAGE_UPDATED_SUCCESSFULLY);

                                LeaveTransaction::removeLeaveCreditTransaction(
                                    $_SESSION['organization']->id,
                                    $latency->hr_latency_user_id,
                                    LeaveTransaction::LEAVE_TRANSACTION_DEDUCT_UPON_LATENCY,
                                    Request::REQUEST_TYPE_HR_LATENCY,
                                    $latency->hr_latency_id
                                );


                            } catch (ModelException $e) {
                            }

                            break;

                        case Setting::PERSONNEL_ABSENCE_NO_DEDUCTED:

                            try {

                                $latency->hr_latency_manipulation_type = Setting::PERSONNEL_ABSENCE_NO_DEDUCTED;
                                $latency->hr_latency_manipulation_leave_id = 0;
                                $latency->hr_latency_manipulation_batche_id = 0;
                                $latency->hr_latency_manipulation_trans_id = 0;
                                $latency->save();

                                Notification::alertMessage(Notification::SUCCESS, Notification::MESSAGE_UPDATED_SUCCESSFULLY);

                                LeaveTransaction::removeLeaveCreditTransaction(
                                    $latency->hr_latency_org_id,
                                    $latency->hr_latency_user_id,
                                    LeaveTransaction::LEAVE_TRANSACTION_DEDUCT_UPON_LATENCY,
                                    Request::REQUEST_TYPE_HR_ATTENDANCE,
                                    $latency->hr_latency_id
                                );

                            } catch (ModelException $e) {
                            }

                            break;
                    }

                    if ($latency) {
                        LeaveData::updateEmployeeLeavesCredit($_SESSION['organization']->id, $latency->hr_latency_user_id);
                    }

                }

                break;
        }

        try {

            $latency = HRLatencyRequest::readID((int)$_SESSION['s_latency_row_id'] ?? 0);
            $this->Smarty->assign('latency', $latency);

        } catch (HRLatencyRequestException $e) {

            $this->Smarty->assign('latency', []);

        }

        if ($latency) {

            try {

                $days = FingerPrintDay::read([
                    FingerPrintDay::ORG_ID => $latency->org_id,
                    FingerPrintDay::LATENCY_ID => $latency->id
                ], [
                    0 => [
                        'property' => FingerPrintDay::DATE,
                        'sort' => 'ASC'
                    ]
                ]);

            } catch (FingerPrintDayException $e) {

            }

            $this->Smarty->assign('fingerPrintRecords', $days);

            try {
                $this->Smarty->assign('interval', FingerPrintInterval::readID((int)($days[0]->interval_id)));
            } catch (FingerPrintIntervalException $e) {

            }

            $this->Smarty->assign('employee', Vacant::getEmployeeJobs($latency->org_id, $latency->user_id, Vacant::EMPLOYEE_BASIC_VACANT));
        }


        $this->Smarty->assign('manipulateList', Setting::getList(79));

        $_SESSION['s_personnel_tab'] = "latency";
        $_SESSION['s_hrlatency_token'] = md5(rand(0000, 9999));
    }

    public function manipulateConfirm($parm, $post)
    {

        $this->Smarty->assign('type', (int)$parm[0] ?? 0);

        try {
            $latency = HRLatencyRequest::readID((int)$parm[1]);
            $this->Smarty->assign('latency', $latency);
        } catch (HRLatencyRequestException $e) {
            $this->Smarty->assign('latency', []);

        }

        try {
            $user = User::readID((int)$latency->user_id);
            $this->Smarty->assign('user', $user);
        } catch (UserException $e) {
            $this->Smarty->assign('user', []);
        }

        if ($latency && $user) {


            switch ($parm[0]) {

                case Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_LEAVE_BALANCE:

                    // update employee leaves credit
                    LeaveData::updateEmployeeLeavesCredit($latency->org_id, $user->id);

                    try {
                        $this->Smarty->assign('leaves', LeaveAllowed::read([LeaveAllowed::USER_ID => $user->id]));
                    } catch (LeaveAllowedException $e) {
                    } catch (LeaveTypeException $e) {
                    }

                    break;

                case Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_SALARY: // Deduct from salary ( Absence )
                    break;

                case Setting::PERSONNEL_ABSENCE_NO_DEDUCTED: // Not Deduct
                    break;
            }

        }

    }

    public function fingerPrintStructure($parm, $post)
    {

        try {
            $row = HRLatencyRequest::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('row', $row);
        } catch (HRLatencyRequestException $e) {
            $this->Smarty->assign('row', []);
        }

        if ($row) {

            try {

                $days = FingerPrintDay::read([
                    FingerPrintDay::ORG_ID => $row->org_id,
                    FingerPrintDay::LATENCY_ID => $row->id
                ], [
                    0 => [
                        'property' => FingerPrintDay::DATE,
                        'sort' => 'ASC'
                    ]
                ]);

            } catch (FingerPrintDayException $e) {

            }

            $this->Smarty->assign('fingerPrintRecords', $days);

            try {
                $this->Smarty->assign('interval', FingerPrintInterval::readID((int)($days[0]->interval_id)));
            } catch (FingerPrintIntervalException $e) {

            }

            $this->Smarty->assign('employee', Vacant::getEmployeeJobs($row->org_id, $row->user_id, Vacant::EMPLOYEE_BASIC_VACANT));
        }

        $_SESSION['s_personnel_tab'] = "latency";
        $_SESSION['s_hrlatency_token'] = md5(rand(0000, 9999));

    }

}