<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bsc_P051_evaluation extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {
            case 'insert':
                if ($parm[1] == $_SESSION['s_evaluation_token']) {
                    try {
                        $evaluation = new Evaluation();
                        $evaluation->bindProperties($post);
                        $evaluation->org_id = $_SESSION['organization']->id;
                        $evaluation->prg_id = $_SESSION['program']->id;
                        $evaluation->start_date = $this->Date->get_date('ad', $post['start_date']);
                        $evaluation->end_date = $this->Date->get_date('ad', $post['end_date']);
                        $evaluation->created_by = $_SESSION['user']->id;
                        $evaluation->created_date = date('Y-m-d');
                        $evaluation->updated_date = date('Y-m-d');
                        $evaluation->status = 0;
                        $evaluation->save();

                        Notification::createdAlert();

                    } catch (EvaluationException $e) {
                    }
                }
                break;
            case 'edit':
                try {
                    if ($parm[1] == $_SESSION['s_evaluation_token']) {

                        try {
                            $evaluation = Evaluation::readID((int)$parm[2] ?? 0);
                            $evaluation->bindProperties($post);
                            $evaluation->start_date = $this->Date->get_date('ad', $post['start_date']);
                            $evaluation->end_date = $this->Date->get_date('ad', $post['end_date']);
                            $evaluation->updated_date = date('Y-m-d');
                            $evaluation->save();

                            Notification::updatedAlert();

                        } catch (EvaluationException $e) {
                        }

                    }
                } catch (ModelException $exception) {
                }
                break;
            case 'delete':
                try {
                    if ($parm[1] == $_SESSION['s_evaluation_token']) {
                        try {
                            $evaluation = Evaluation::readID((int)$parm[2] ?? 0);
                            $evaluation->delete();

                            Notification::deletedAlert();

                        } catch (EvaluationException $e) {
                        }
                    }
                } catch (ModelException $exception) {
                }
                break;


            case 'updateStatus':
                if ($parm[1] == $_SESSION['s_evaluation_token']) {
                    switch ($parm[3]) {
                        case 'enable':
                            try {
                                $evaluation = Evaluation::readID((int)$parm[2] ?? 0);
                                $evaluation->status = Evaluation::ENABLED;
                                $evaluation->save();

                                Notification::updatedAlert();

                            } catch (EvaluationException $exception) {
                            } catch (SampleException $exception) {
                            }

                            try {
                                Sample::notifyQuestionnaireSamples((int)$evaluation->id ?? 0);
                            } catch (SampleException $e) {
                            }


                            break;
                        case 'disable':
                            try {
                                $evaluation = Evaluation::readID((int)$parm[2] ?? 0);
                                $evaluation->status = Evaluation::DISABLED;
                                $evaluation->save();

                                Notification::updatedAlert();

                            } catch (EvaluationException $exception) {
                            }
                            break;
                    }
                }
                break;
        }
        try {
            $this->Smarty->assign('EvaluationList', Evaluation::read([
                Evaluation::ORG_ID => $_SESSION['organization']->id,
                Evaluation::PRG_ID => $_SESSION['program']->id
            ]));
        } catch (EvaluationException $exception) {
        }

        $_SESSION['s_evaluation_token'] = md5(rand(0, 999));
        $_SESSION['s_back_url'] = 'bsc/P051/evaluation/show/0/' . $_SESSION['lang'];
    }

    public function add($parm, $post)
    {
    }

    public function edit($parm, $post, $files)
    {
        try {
            $this->Smarty->assign('row', Evaluation::readID((int)$parm[0] ?? 0));
        } catch (EvaluationException $e) {
        }
        $this->Smarty->assign('id', $parm[0]);
    }

    public function delete($parm, $post, $files)
    {
        $this->Smarty->assign('id', $parm[0]);
    }

    public function status($parm, $post, $files)
    {
        try {
            $this->Smarty->assign('eval', Evaluation::readID((int)($parm[0] ?? 0)));
        } catch (EvaluationException $e) {
        };
    }

    public function showQuiz($parm, $post, $files)
    {
        switch ($parm[0]) {
            case 'addFillersOutside':
                try {
                    $Emails = Sample::readSampleUsersEmails((int)$parm[1] ?? 0);
                } catch (SampleException $exception) {
                    $Emails = [];
                }
                $selectedEmails = $post['emails'];

                $newEmails = array_diff($selectedEmails, $Emails);
                $oldEmails = array_diff($Emails, $selectedEmails);

                foreach ($newEmails as $email) {
                    try {
                        $sample = new Sample();
                        $sample->bindProperties([]);
                        $sample->eva_id = $_SESSION['s_evaluation_id'];
                        $sample->questionnaire_id = (int)$parm[1] ?? 0;
                        $sample->type = Sample::OUTSIDE_SYSTEM;
                        $sample->user_id = 0;
                        $sample->email = $email;
                        $sample->save();
                    } catch (SampleException $e) {
                    }
                }

                foreach ($oldEmails as $email) {
                    try {
                        Sample::read([Sample::EMAIL => $email])[0]->delete();
                    } catch (SampleException $exception) {
                    }
                }

                break;
            case 'saveSession':
                $_SESSION['s_evaluation_id'] = $parm[1] ?? 0;
                break;
            case 'insert':
                if ($_SESSION['s_evaluation_token'] === $parm[1]) {
                    try {
                        $questionnaire = new Questionnaire();
                        $questionnaire->bindProperties($post);
                        $questionnaire->org_id = $_SESSION['organization']->id;
                        $questionnaire->prg_id = $_SESSION['program']->id;
                        $questionnaire->eva_id = $_SESSION['s_evaluation_id'];
                        $questionnaire->created_by = $_SESSION['user']->id;
                        $questionnaire->created_date = date('Y-m-d');
                        $questionnaire->save();
                        $this->Smarty->assign('Message', 'InsertedSuccessfull');
                    } catch (QuestionnaireException $e) {
                    }
                }
                break;
            case 'update':
                if ($_SESSION['s_evaluation_token'] === $parm[1]) {
                    try {
                        $questionnaire = Questionnaire::readID((int)$parm[2] ?? 0);
                        $questionnaire->bindProperties($post);
                        $questionnaire->save();
                        $this->Smarty->assign('Message', 'UpdatedSuccessfull');
                    } catch (QuestionnaireException $e) {
                    }
                }
                break;
            case 'delete':
                if ($_SESSION['s_evaluation_token'] === $parm[1]) {
                    try {
                        $questionnaire = new Questionnaire();
                        $questionnaire->id = $parm[2];
                        $questionnaire->delete();
                        $this->Smarty->assign('Message', 'DeletetedSuccessfull');
                    } catch (ModelException $e) {
                    }
                }
                break;
            case 'import':
                if ($_SESSION['s_evaluation_token'] == $parm[1] && is_uploaded_file($files['form']['tmp_name'])) {
                    /**
                     * @var $questionnaire Questionnaire
                     */
                    $questionnaire = unserialize(file_get_contents($files['form']['tmp_name']));
                    Questionnaire::import($questionnaire);
                }
                break;
        }

        try {
            $this->Smarty->assign('questionnaires', Questionnaire::read([
                Questionnaire::ORG_ID => $_SESSION['organization']->id,
                Questionnaire::PRG_ID => $_SESSION['program']->id,
                Questionnaire::EVA_ID => $_SESSION['s_evaluation_id']
            ], [
                0 => [
                    'property' => Questionnaire::CREATED_DATE,
                    'sort' => 'DESC'
                ]
            ]));

        } catch (QuestionnaireException $e) {
            $this->Smarty->assign('questionnaires', []);
        }

        $_SESSION['s_evaluation_token'] = md5(rand(0, 99999));
    }

    public function addQuiz($parm, $post)
    {
        try {
            $this->Smarty->assign('options', Setting::getList(Questionnaire::FILL_OPTIONS));
        } catch (SettingException $e) {
            $this->Smarty->assign('options', []);
        }
        $_SESSION['s_evaluation_token'] = md5(rand(0, 99999));
    }

    public function editQuiz($parm, $post)
    {
        try {
            $this->Smarty->assign('questionnaire', Questionnaire::readID((int)$parm[0] ?? 0));
        } catch (QuestionnaireException $e) {
        }

        $this->Smarty->assign('options', Setting::getList(Questionnaire::FILL_OPTIONS));
        $_SESSION['s_evaluation_token'] = md5(rand(0, 99999));
    }

    public function confirm($parm, $post)
    {
        try {
            $this->Smarty->assign('questionnaire', Questionnaire::readID((int)$parm[0] ?? 0));
        } catch (QuestionnaireException $e) {
        }
        $_SESSION['s_evaluation_token'] = md5(rand(0, 99999));
    }

    public function browse($parm, $post)
    {
        try {
                $this->Smarty->assign('questionnaire', Questionnaire::readID((int)$parm[0] ?? 0));
        } catch (QuestionnaireException $e) {
        }
    }

    public function showUsers($parm, $post)
    {
        $Users = [];
        $this->Smarty->assign('QuestionnaireID', $parm[0]);

        switch ($parm[1]) {

            case 'addFillers':


                if ($parm[2] == $_SESSION['s_evaluation_token']) {
                    try {
                        qs_sample::simpleDeleteByProperty([
                            Sample::QUESTIONNAIRE_ID => $parm[0] ?? 0,
                        ]);
                    } catch (ModelException $e) {
                    }

                    foreach ($post['selectedUsers'] as $userId) {

                        try {
                            Sample::read([
                                Sample::EVA_ID => $_SESSION['s_evaluation_id'],
                                Sample::QUESTIONNAIRE_ID => $parm[0] ?? 0,
                                Sample::TYPE => Sample::INSIDE_SYSTEM,
                                Sample::USER_ID => (int)$userId,
                            ]);
                        } catch (SampleException $e) {
                            $sample = new Sample();
                            $sample->bindProperties([]);
                            $sample->eva_id = $_SESSION['s_evaluation_id'];
                            $sample->questionnaire_id = $parm[0] ?? 0;
                            $sample->type = Sample::INSIDE_SYSTEM;
                            $sample->user_id = (int)$userId;
                            $sample->save();
                            $sample = Sample::readID((int)$sample->id);
                            if (
                                $sample->questionnaire->evaluationObject()->status == Evaluation::ENABLED
                            ) {
                                Notification::sendNotification(
                                    Operation::OPERATION_MY_QUESTIONNAIRES,
                                    0,
                                    Sample::getEntityName(),
                                    $sample->id,
                                    $_SESSION['user']->id,
                                    $sample->user_id,
                                    Setting::NEW_QUESTIONNAIRE_NOTIFICATION,
                                    []
                                );
                            }
                        }
                    }
                }

                break;
        }

        try {
            $this->Smarty->assign('UsersClassesList',
                UserClass::read([UserClass::ORG_ID => $_SESSION['organization']->id]));
        } catch (UserClassException $exception) {
            $this->Smarty->assign('UsersClassesList', []);
        }

        $this->Smarty->assign('UsersList', $Users);
        $fillers = [];
        try {
            $fillers = qs_sample::readSampleUsersIDs($parm[0]??0);
        } catch (ModelException $exception) {
        }
        $this->Smarty->assign('QuestionnaireArray', $fillers);
        $_SESSION['s_evaluation_token'] = md5(rand(0, 99999));

    }

    public function showUsersOutside($parm, $post, $files)
    {
        try {
            $this->Smarty->assign('oldEmails', Sample::read([
                Sample::PRG_ID => $_SESSION['program']->id,
                Sample::QUESTIONNAIRE_ID => $parm[0] ?? 0,
                Sample::TYPE => Sample::OUTSIDE_SYSTEM
            ]));
        } catch (SampleException $exception) {
        }
        $this->Smarty->assign('id', $parm[0]);
    }

    public function showFillers($parm, $post)
    {
        try {
            $this->Smarty->assign('questionnaire', Questionnaire::readID((int)$parm[0]));
        } catch (QuestionnaireException $e) {
            $this->Smarty->assign('questionnaire', null);
        }
    }
}
