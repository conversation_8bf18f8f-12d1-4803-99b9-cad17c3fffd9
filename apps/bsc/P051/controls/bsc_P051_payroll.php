<?php
// Deny Direct Script Access
defined('ENVIRONMENT') or exit('No Direct Access Allowed!');

use Application\Cache\SnsoCache;
use Carbon\Carbon;
use Models\EmployeePayrollTemplate;
use Models\HumanResource\PayrollTransaction;
use Models\HumanResource\PayrollBatch as PayrollBatchModel;
use Models\Request as RequestModel;
use Models\User as UserModel;

use function Symfony\Component\DependencyInjection\Loader\Configurator\ref;

class bsc_P051_payroll extends Controller
{
    public function show($parm, $post)
    {

        if (isset($parm[0]) && $parm[0] == 'menu') {
            $_SESSION['tab1'] = 'active';
            $_SESSION['tab0'] = '';
            $_SESSION['tab2'] = '';
            $_SESSION['tab3'] = '';
            $_SESSION['tab4'] = '';
            $_SESSION['s_tab_0_top_tab'] = 'payrollView';
            $_SESSION['s_tab_3_top_tab'] = 'monthly';
            $_SESSION['s_tab_2_top_tab'] = 'monthlyBatch';
            $_SESSION['rulestabone'] = 'active';
            $_SESSION['rulestabtow'] = '';
            $_SESSION['monthly'] = 'active';
            $_SESSION['daily'] = '';
            $_SESSION['payslibsheet'] = 'active';
            $_SESSION['payslipbrowse'] = '';
            $_SESSION['rulestabone'] = 'active';
            $_SESSION['rulestabtow'] = '';
            $_SESSION['batcheslist'] = 'active';
            $_SESSION['payslibsheet'] = '';
            $_SESSION['s_prl_templates_id'] = null;
            $this->deleteFormSessionFields();
        }

        if (isset($parm[0]) && $parm[0] == 'save_session') {
            switch ($parm[1]) {
                case 'tab0':
                    switch ($parm[2]) {
                        case 'browse':
                            if ($post['submit'] == 'browse') {
                                $this->deleteFormSessionFields();
                            }
                            $_SESSION['tab0'] = 'active';
                            $_SESSION['s_tab_0_top_tab'] = 'payrollView';
                            $_SESSION['payroll_name'] = $post['name'];
                            $_SESSION['rev_user_id'] = $post['rev_user_id'];
                            $_SESSION['prov_user_id'] = $post['prov_user_id'];
                            $_SESSION['unit_id'] = $post['unit_id'];
                            $_SESSION['payroll_type'] = $this->getPayrollType($post) ?? $_SESSION['payroll_type'];
                            $_SESSION['templates_id'] = $post['templates_id'] ?? 0;
                            $_SESSION['parameters']['from_date'] = $post['from_date'];
                            $_SESSION['parameters']['to_date'] = $post['to_date'];
                            $_SESSION['tab1'] = $_SESSION['tab2'] = $_SESSION['tab3'] = $_SESSION['tab4'] = '';
                            if (isset($post['browse'])) {

                                $this->deleteCachedEmployeesExtraDetails();
                                SnsoCache::delete('reveiwing_status_comment');
                                if (isset($_SESSION['batch_to_edit_id'])) {
                                    $this->deleteFormSessionFields();
                                }

                            } elseif (isset($post['submit']) && $post['submit'] = 'save') {

                                try {
                                    $this->rollBatchCreateOrUpdate($post);
                                    $_SESSION['s_tab_0_top_tab'] = 'payrollSave';
                                    $_SESSION['tab0'] = 'active';
                                } catch (PayrollBatchException $e) {
                                    Notification::alertMessage(Notification::ERROR, 'ErrorInBatchCreation');
                                }
                            }
                            break;
                        case 'calculateUserExtraValue':
                            $post['unit_id'] = $_SESSION['unit_id'];
                            $post['templates_id'] = $_SESSION['templates_id'];
                            $post['payroll_type'] = $_SESSION['payroll_type'];
                            // $post['payroll_type_all'] = $_SESSION['payroll_type_all']; //تم إلغاءها لأن إذا حددت وحدة معينة أو قالب معين، ومن ثم رحت لتعديل على حسومات موظف ما، فإذا ضغطت على حفظ فإنه يحفظ تعديلات الخصومات وأيضا يجيب لي كل الموظفين وليس الموظفين اللي حددتهم سواء بناء على الوحدة أو القالب
                            $this->calculateUserExtraValue($post, $parm);
                            // dd($post,$parm,$_SESSION);
                            $_SESSION['tab0'] = 'active';
                            $_SESSION['tab1'] = $_SESSION['tab2'] = $_SESSION['tab3'] = $_SESSION['tab4'] = '';
                            break;


                        case 'calculateUserDeductionsAddition':
                            // إعداد متغيرات الجلسة
                            $post['unit_id'] = $_SESSION['unit_id'] ?? null;
                            $post['templates_id'] = $_SESSION['templates_id'] ?? null;
                            $post['payroll_type'] = $_SESSION['payroll_type'] ?? null;
                            $_SESSION['tab0'] = 'browse';
                            $_SESSION['s_tab_0_top_tab'] = 'payrollView';
                            $_SESSION['tab1'] = $_SESSION['tab2'] = $_SESSION['tab3'] = $_SESSION['tab4'] = '';

                            if (isset($post['calculateExtraValue'])) {
                                try {
                                    // التحقق من البيانات الواردة
                                    $additions_list = isset($post['additions_list']) && is_array($post['additions_list']) ? $post['additions_list'] : [];
                                    $deduction_list_amount = 0;

                                    // حالة التعديل
                                    if (isset($_SESSION['batch_to_edit_id'])) {
                                        $payrollBatch = PayrollBatchModel::find($_SESSION['batch_to_edit_id']);
                                        if (!$payrollBatch) {
                                            throw new Exception('Batch not found for ID: ' . $_SESSION['batch_to_edit_id']);
                                        }
                                        /** @var PayrollTransaction $extraD */
                                        $extraD = PayrollTransaction::forBatch($payrollBatch)
                                            ->where(PayrollTrans::USER_ID, $parm[3])
                                            ->first();

                                        if (!$extraD) {
                                            throw new Exception('Payroll transaction not found for user ID: ' . $parm[3]);
                                        }

                                        // حساب إجمالي الإضافات
                                        if (!empty($additions_list)) {
                                            foreach ($additions_list as $deductionId) {
                                                try {
                                                    $additionRequest = HRDeductAdditionRequest::readID($deductionId);
                                                    $deduction_list_amount += $additionRequest->amount;


                                                } catch (HRDeductAdditionRequestException $e) {

                                                }
                                            }
                                        }

                                        // تحديث السجل في قاعدة البيانات
                                        $extraD->additions_list = json_encode($additions_list);
                                        $extraD->extra_addition = $deduction_list_amount;

                                        $updated = $extraD->update();

                                        if (!$updated) {
                                            throw new Exception('Failed to update PayrollTransaction for user ID: ' . $parm[3]);
                                        }

                                        // تحديث الكاش لضمان التوافق
                                        $cacheKey = $parm[3] . '_extraDetails';
                                        $cachedAdditions = SnsoCache::get($cacheKey) ?: new stdClass();
                                        $cachedAdditions->additions_list = $additions_list;
                                        $cachedAdditions->additions_list_amount = $deduction_list_amount;
                                        SnsoCache::update($cacheKey, $cachedAdditions, 10800);

                                        // تسجيل نجاح التحديث
                                        error_log('Updated additions for user ID: ' . $parm[3] . ', Amount: ' . $deduction_list_amount . ', List: ' . json_encode($additions_list));
                                        error_log('Database updated: prl_trans_additions_list=' . $extraD->prl_trans_additions_list . ', prl_trans_extra_addition=' . $extraD->prl_trans_extra_addition);

                                        // إشعار للمستخدم
                                        $_SESSION['notification'] = [
                                            'type' => 'success',
                                            'message' => 'تم تحديث الإضافات بنجاح للموظف #' . $parm[3]
                                        ];
                                    } else {
                                        // حالة الإنشاء
                                        $cacheKey = $parm[3] . '_extraDetails';
                                        $extraD = SnsoCache::get($cacheKey) ?: new stdClass();

                                        // حساب إجمالي الإضافات
                                        if (!empty($additions_list)) {
                                            foreach ($additions_list as $deductionId) {
                                                try {
                                                    $additionRequest = HRDeductAdditionRequest::readID($deductionId);
                                                    $deduction_list_amount += $additionRequest->amount;
                                                } catch (HRDeductAdditionRequestException $e) {
                                                    error_log('Error fetching deduction ID ' . $deductionId . ': ' . $e->getMessage());
                                                    continue;
                                                }
                                            }
                                        }

                                        // تحديث الكائن في الكاش
                                        $extraD->additions_list = $additions_list;
                                        $extraD->additions_list_amount = $deduction_list_amount;
                                        SnsoCache::update($cacheKey, $extraD, 10800);

                                        // تسجيل نجاح التخزين
                                        error_log('Cached additions for user ID: ' . $parm[3] . ', Amount: ' . $deduction_list_amount . ', List: ' . json_encode($additions_list));

                                        // إشعار للمستخدم
                                        $_SESSION['notification'] = [
                                            'type' => 'success',
                                            'message' => 'تم تسجيل الإضافات بنجاح للموظف #' . $parm[3] . ' (سيتم حفظها عند حفظ الدفعة)'
                                        ];
                                    }

                                    // إعداد الجلسة لإعادة التوجيه
                                    $post['unit_id'] = $_SESSION['unit_id'] ?? null;
                                    $post['templates_id'] = $_SESSION['templates_id'] ?? null;
                                    $post['payroll_type'] = $_SESSION['payroll_type'] ?? null;
                                    $_SESSION['tab0'] = 'browse';
                                    $_SESSION['s_tab_0_top_tab'] = 'payrollView';
                                    $_SESSION['tab1'] = $_SESSION['tab2'] = $_SESSION['tab3'] = $_SESSION['tab4'] = '';

                                } catch (Exception $e) {
                                    error_log('Error in calculateUserDeductionsAddition: ' . $e->getMessage());
                                    // إشعار خطأ للمستخدم
                                    $_SESSION['notification'] = [
                                        'type' => 'error',
                                        'message' => 'فشل في تحديث الإضافات: ' . $e->getMessage()
                                    ];
                                }

                                redirect('payroll/show', $parm);
                            }
                            break;



                        case 'calculatePaidUnpaid':
                            if (isset($post['amount_paid']) && isset($post['amount_not_paid']) && isset($post['confirm_status'])) {
                                try {
                                    if (!isset($_SESSION['batch_to_edit_id'])) {
                                        $extraD = SnsoCache::get($parm[3] . '_extraDetails');
                                        if (!$extraD) {
                                            SnsoCache::set($parm[3] . '_extraDetails', new stdClass(), 10800);
                                            $extraD = SnsoCache::get($parm[3] . '_extraDetails');
                                        }
                                        $extraD->paid = $post['amount_paid'];
                                        $extraD->not_paid = $post['amount_not_paid'];
                                        $extraD->confirm_status = $post['confirm_status'];
                                        $extraD->amount_comment = $post['amount_comment'];
                                        SnsoCache::update($parm[3] . '_extraDetails', $extraD, 10800);
                                    } else {
                                        $trans = PayrollTrans::read([
                                            PayrollTrans::USER_ID => $parm[3],
                                            PayrollTrans::BATCH_ID => session('batch_to_edit_id')
                                        ])[0];
                                        $trans->confirm_status = $post['confirm_status'];
                                        $trans->amount_paid = $post['amount_paid'];
                                        $trans->amount_not_paid = $post['amount_not_paid'];
                                        $trans->amount_comment = $post['amount_comment'];
                                        try {
                                            $trans->update();
                                        } catch (PayrollTransException $e) {}
                                        // تحديث الكاش بعد التحديث في قاعدة البيانات
                                        $extraD = SnsoCache::get($parm[3] . '_extraDetails') ?: new stdClass();
                                        $extraD->confirm_status = $post['confirm_status'];
                                        SnsoCache::update($parm[3] . '_extraDetails', $extraD, 10800);
                                    }
                                    $post['unit_id'] = $_SESSION['unit_id'];
                                    $post['templates_id'] = $_SESSION['templates_id'];
                                    $post['payroll_type'] = $_SESSION['payroll_type'];
                                    // $post['payroll_type_all'] = $_SESSION['payroll_type_all']; //تم إلغاءها لأن إذا حددت وحدة معينة أو قالب معين، ومن ثم رحت لتعديل على حسومات موظف ما، فإذا ضغطت على حفظ فإنه يحفظ تعديلات الخصومات وأيضا يجيب لي كل الموظفين وليس الموظفين اللي حددتهم سواء بناء على الوحدة أو القالب
                                    $_SESSION['tab0'] = 'browse';
                                    $_SESSION['s_tab_0_top_tab'] = 'payrollView';
                                    $_SESSION['tab1'] = $_SESSION['tab2'] = $_SESSION['tab3'] = $_SESSION['tab4'] = '';
                                } catch (Exception $e) {
                                }
                            }
                            break;
                        case 'editPayRollBatch':
                            $batchDayNumber = 30; // مدير الموارد البشرية يقول: بأن الشهر عندنا هو 30 يوم حتى لو كان الشهر 29 أو 28 يوم
                            $this->Smarty->assign('batchDayNumber', $batchDayNumber);
                            $this->deleteFormSessionFields();
                            $_SESSION['tab0'] = 'active';
                            $_SESSION['tab1'] = '';
                            $_SESSION['s_tab_0_top_tab'] = 'payrollView';
                            $_SESSION['batch_to_edit_id'] = $parm[3];
                            break;
                        case 'deletePayRollBatch':
                            $this->deletePayrollBatch($parm[3] ?? 0);
                            unset($post);
                            break;
                        case 'newConfirmAllPayslips':
                            $employees = SnsoCache::get('payroll_employees');

                            SnsoCache::update('reveiwing_status_comment', $post['reveiwing_status_comment'], 10800);
                            SnsoCache::update('reveiwing_status', $post['status'], 10800);
                            foreach ($employees as $employee) {
                                if ($_SESSION['batch_to_edit_id']) {
                                    // Check if employee is excluded in prl_trans
                                    $trans = DB::table('prl_trans')
                                        ->where(PayrollTrans::USER_ID, $employee->user_id)
                                        ->where(PayrollTrans::BATCH_ID, session('batch_to_edit_id'))
                                        ->first();
                                        
                                    if ($trans && $trans->prl_trans_confirm_status == PayrollTrans::PAYSLIP_EXCLUDED) {
                                        continue; // Skip excluded employees
                                    }

                                    DB::table('prl_trans')
                                        ->where(PayrollTrans::USER_ID, $employee->user_id)
                                        ->where(PayrollTrans::BATCH_ID, session('batch_to_edit_id'))
                                        ->update(['prl_trans_confirm_status' => $post['status']]);
                                }

                                $extraDetails = SnsoCache::get($employee->user_id . '_extraDetails');
                                if (!$extraDetails) {
                                    SnsoCache::set($employee->user_id . '_extraDetails', new stdClass(), 10800);
                                    $extraDetails = SnsoCache::get($employee->user_id . '_extraDetails');
                                }

                                // Only update if not excluded in extraDetails
                                if (!isset($extraDetails->confirm_status) || $extraDetails->confirm_status != PayrollTrans::PAYSLIP_EXCLUDED) {
                                    $newData = $extraDetails;
                                    $newData->amount_comment = $extraDetails->amount_comment;
                                    $newData->confirm_status = $post['status'];
                                    SnsoCache::update($employee->user_id . '_extraDetails', $newData, 10800);
                                }
                            }

                            $post['unit_id'] = $_SESSION['unit_id'];
                            $post['templates_id'] = $_SESSION['templates_id'];
                            $post['payroll_type'] = $_SESSION['payroll_type'];
                            // $post['payroll_type_all'] = $_SESSION['payroll_type_all']; //تم إلغاءها لأن إذا حددت وحدة معينة أو قالب معين، ومن ثم رحت لتعديل على حسومات موظف ما، فإذا ضغطت على حفظ فإنه يحفظ تعديلات الخصومات وأيضا يجيب لي كل الموظفين وليس الموظفين اللي حددتهم سواء بناء على الوحدة أو القالب
                            break;
                    }
                    break;
                case 'tab1':
                    switch ($parm[2]) {
                        case 'read':
                            $this->BatchRead($parm[2]);
                            break;

                        case 'payslibsheet':
                            $_SESSION['s_payslibsheet_id'] = $parm[3];
                            $_SESSION['batcheslist'] = '';
                            $_SESSION['payslibsheet'] = 'active';
                            break;

                        case 'FinalConfirmBatch':
                            $this->FinalConfirmBatch($parm[3], $parm[4]);
                            break;
                    }
                    $_SESSION['tab0'] = '';
                    $_SESSION['tab1'] = 'active';
                    $_SESSION['tab2'] = '';
                    $_SESSION['tab3'] = '';
                    $_SESSION['tab4'] = '';
                    $_SESSION['batchtabone'] = 'active';
                    $_SESSION['batchtabtow'] = '';
                    break;
                case 'tab2':
                    switch ($parm[2]) {

                        case 'insert':

                            if ($_SESSION['s_rand_trans_num'] == $parm[3] && !empty($post['rev_user_id'] && !empty($post['prov_user_id']))) {
                                $this->BatchCreate($post, $parm[4]);
                                $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
                            } else {

                                Notification::alertMessage(Notification::WARNING, 'RevProvAreMissing');

                            }

                            $_SESSION['s_tab_2_top_tab'] = $parm[4];

                            break;

                        case 'update':

                            if ($_SESSION['s_rand_trans_num'] == $parm[4] && !empty($post['rev_user_id'] && !empty($post['prov_user_id']))) {

                                $this->BatchUpdate($post, $parm[3]);

                                Notification::updatedAlert();
                                $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));

                            } else {
                                Notification::alertMessage(Notification::WARNING, 'RevProvAreMissing');
                            }

                            $_SESSION['s_tab_2_top_tab'] = $parm[5];

                            break;

                        case 'delete':

                            if ($_SESSION['s_rand_trans_num'] == $parm[4]) {

                                try {

                                    $batch = PayrollBatch::readID((int) $parm[3]);
                                    $batch->delete();

                                    Notification::deletedAlert();

                                } catch (PayrollBatchException $e) {

                                }

                                $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));

                            }

                            break;

                        case 'updatestructure':

                            PayrollBatch::BatchFreeConnectedTransactions((int) $_SESSION['organization']->id, (int) $parm[3]);
                            PayrollBatch::BatchSetRulesStructure((int) $parm[3]);
                            PayrollBatch::BatchSetEmployeeList((int) $parm[3]);
                            Notification::updatedAlert();

                            break;
                    }
                    $_SESSION['tab0'] = '';
                    $_SESSION['tab1'] = '';
                    $_SESSION['tab2'] = 'active';
                    $_SESSION['tab3'] = '';
                    $_SESSION['tab4'] = '';
                    break;
                case 'tab3':
                    switch ($parm[2]) {

                        case 'updatetemp':
                            if ($_SESSION['s_payroll_token'])
                                try {

                                    $vacant = sh_uao::readByID((int) $parm[3]);
                                    $vacant->sh_uao_payroll_template_id = $post['payroll_template_id'];
                                    $vacant->save();
                                    $employeeSpecificTemplate = EmployeePayrollTemplate::where('user_id', (int) $parm[5] ?? 0)
                                        ->get();
                                    foreach ($employeeSpecificTemplate as $temp) {
                                        $temp->template_id = $post['payroll_template_id'];
                                        $temp->template->template_id = $post['payroll_template_id'];
                                        $temp->update();
                                    }

                                    Notification::updatedAlert();

                                } catch (ModelException $e) {

                                }

                            if ($parm[4] == 227) {
                                $_SESSION['s_tab_3_top_tab'] = 'monthly';
                            }

                            if ($parm[4] == 861) {
                                $_SESSION['s_tab_3_top_tab'] = 'daily';
                            }

                            break;

                    }
                    $_SESSION['tab0'] = '';
                    $_SESSION['tab1'] = '';
                    $_SESSION['tab2'] = '';
                    $_SESSION['tab3'] = 'active';
                    $_SESSION['tab4'] = '';
                    break;
                case 'tab4':

                    switch ($parm[2]) {

                        case 'insert':

                            if ($_SESSION['s_rand_trans_num'] == $parm[3]) {

                                $temp = new PayrollTemplate();
                                $temp->bindProperties($post);
                                $temp->org_id = $_SESSION['organization']->id;
                                $temp->created_by = $_SESSION['user']->id;
                                $temp->created_date = $this->Date->get_date('ad', date('Y-m-d'));
                                $temp->save();

                                Notification::createdAlert();
                            }

                            $_SESSION['rulestabone'] = 'active';
                            $_SESSION['rulestabtow'] = '';
                            $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));


                            break;

                        case 'update':

                            if ($_SESSION['s_rand_trans_num'] == $parm[4]) {

                                try {

                                    $temp = PayrollTemplate::readID((int) $parm[3]);
                                    $temp->bindProperties($post);
                                    $temp->save();

                                    Notification::updatedAlert();

                                } catch (PayrollTemplateException $e) {
                                }

                            }

                            $_SESSION['rulestabone'] = 'active';
                            $_SESSION['rulestabtow'] = '';
                            $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));

                            break;

                        case 'delete':

                            if ($_SESSION['s_rand_trans_num'] == $parm[4]) {
                                try {

                                    $template = PayrollTemplate::readID((int) $parm[3]);
                                    $template->delete();

                                    Notification::deletedAlert();

                                } catch (PayrollTemplateException $e) {
                                }
                            }

                            $_SESSION['rulestabone'] = 'active';
                            $_SESSION['rulestabtow'] = '';
                            $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));

                            break;

                        case 'buildtemplate':

                            $_SESSION['s_prl_templates_id'] = $parm[3];
                            $num = PayrollRule::count([
                                PayrollRule::TEMPLATE_ID => $parm[3],
                                PayrollRule::CATEGORY_ID => 644,
                                PayrollRule::TYPE => 1
                            ]);

                            switch ($num) {

                                case 0:

                                    $rule = new PayrollRule();
                                    $rule->org_id = $_SESSION['organization']->id;
                                    $rule->template_id = $parm[3];
                                    try {
                                        $rule->name = (Setting::readID(644))->translatedName;
                                    } catch (SettingException $e) {

                                    }
                                    $rule->type = 1;
                                    $rule->code = '';
                                    $rule->category_id = 644;
                                    $rule->order = 1;
                                    $rule->counter = 0;
                                    $rule->condition = '';
                                    $rule->range_based_on = '';
                                    $rule->range_min = 0;
                                    $rule->range_max = 0;
                                    $rule->amount_type = 650;
                                    $rule->amount_fixed_value = 0;
                                    $rule->amount_percentage_value = 0;
                                    $rule->amount_percentage_based_on = '';
                                    $rule->effect_in_day_cost = 526;
                                    $rule->created_by = $_SESSION['user']->id;
                                    $rule->created_date = $this->Date->get_date('ad', date('Y-m-d'));
                                    $rule->save();

                                    Notification::updatedAlert();

                                    break;

                                case 1:

                                    try {

                                        $rule = PayrollRule::read([
                                            PayrollRule::TEMPLATE_ID => $parm[3],
                                            PayrollRule::CATEGORY_ID => 644,
                                            PayrollRule::TYPE => 1
                                        ])[0];
                                        $rule->effect_in_day_cost = 526;
                                        $rule->save();

                                        Notification::updatedAlert();

                                    } catch (PayrollRuleException $e) {

                                    }

                                    break;
                            }

                            $_SESSION['rulestabone'] = '';
                            $_SESSION['rulestabtow'] = 'active';
                            $_SESSION['tab0'] = '';
                            $_SESSION['tab1'] = '';
                            $_SESSION['tab2'] = '';
                            $_SESSION['tab3'] = '';
                            $_SESSION['tab4'] = 'active';
                            $_SESSION['tab5'] = '';
                            break;

                        case 'employees':

                            try {
                                $template = PayrollTemplate::readID((int) $parm[3]);
                            } catch (PayrollTemplateException $e) {
                                $template = [];
                            }

                            $employees = Vacant::getEmployeesOfSalaryDeliveryType($_SESSION['organization']->id, 227);

                            foreach ($employees as $employee) {
                                if (in_array($employee->sh_uao_user_id, $post['employees'])) {
                                    $employee->sh_uao_payroll_template_id = $template->id;
                                } else {
                                    if ($employee->sh_uao_payroll_template_id == $template->id) {
                                        $employee->sh_uao_payroll_template_id = 0;
                                    }
                                }
                                try {
                                    $employee->update();
                                } catch (ModelException $e) {

                                }
                            }

                            $_SESSION['rulestabone'] = 'active';
                            $_SESSION['rulestabtow'] = '';
                            $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
                            break;
                    }
                    $_SESSION['tab0'] = '';
                    $_SESSION['tab1'] = '';
                    $_SESSION['tab2'] = '';
                    $_SESSION['tab3'] = '';
                    $_SESSION['tab4'] = 'active';
                    break;
                case 'tab5':
                    switch ($parm[2]) {
                        case 'insert':

                            if ($_SESSION['s_rand_trans_num'] == $parm[4]) {
                                $order = DB::table('prl_rules')->latest(PayrollRule::ID)->first()->prl_rules_order + 1;
                                $rule = new PayrollRule();
                                $rule->bindProperties($post);
                                $rule->org_id = $_SESSION['organization']->id;
                                $rule->name = !empty($post['name']) ? $post['name'] : $post['predefined_terms'];
                                $rule->template_id = $parm[3];
                                $rule->type = $_SESSION['new_rule_type'];
                                $rule->condition = 648; // $post['condition'];
                                $rule->range_based_on = ''; // implode(',', $post['range_based_on']);
                                $rule->range_min = 0; // $post['range_min'];
                                $rule->order = $order;
                                $rule->range_max = 0; // $post['range_max'];
                                $rule->amount_percentage_based_on = implode(',', $post['amount_percentage_based_on']);
                                $rule->amount_percentage_value = isset($post['amount_percentage_value']) ? $post['amount_percentage_value'] : 0.0;
                                $rule->created_by = $_SESSION['user']->id;
                                $rule->created_date = $this->Date->get_date('ad', date('Y-m-d'));
                                $rule->save();
                                $_SESSION['rulestabone'] = 'active';
                                $_SESSION['rulestabtow'] = '';
                                Notification::createdAlert();
                            }
                            $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));

                            break;

                        case 'update':
                            
                            if ($_SESSION['s_rand_trans_num'] == $parm[4]) {

                                try {

                                    $rule = PayrollRule::readID((int) $parm[3]);
                                    $rule->bindProperties($post);
                                    $rule->name = (!empty($post['use_predefined_terms']) && !empty($post['predefined_terms'])) ? $post['predefined_terms'] : (!empty($post['name']) ? $post['name'] : '');
                                    $rule->condition = 648; // $post['condition'];
                                    $rule->range_based_on = ''; // implode(',', $post['range_based_on']);
                                    $rule->range_min = 0; // $post['range_min'];
                                    $rule->range_max = 0; // $post['range_max'];
                                    $rule->amount_percentage_based_on = implode(',', $post['amount_percentage_based_on']);
                                    
                                    $rule->save();

                                    Notification::updatedAlert();

                                } catch (PayrollRuleException $e) {

                                }
                            }

                            $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));

                            break;

                        case 'delete':

                            if (isset($_SESSION['s_rand_trans_num']) && $_SESSION['s_rand_trans_num'] == $parm[4]) {

                                try {

                                    $rule = PayrollRule::readID((int) $parm[3]);
                                    $rule->delete();

                                    Notification::deletedAlert();

                                } catch (PayrollRuleException $e) {
                                }
                            }

                            break;
                    }
                    $_SESSION['tab0'] = '';
                    $_SESSION['tab1'] = '';
                    $_SESSION['tab2'] = '';
                    $_SESSION['tab3'] = '';
                    $_SESSION['tab4'] = 'active';
                    $_SESSION['rulestabone'] = '';
                    $_SESSION['rulestabtow'] = 'active';
                    break;
            }
        }

        if (isset($post['payroll_type']) || isset($_SESSION['payroll_type']) || isset($_SESSION['batch_to_edit_id'])) {
            try {
                $batch = PayrollBatchModel::find(session('batch_to_edit_id'));
                
                if ($batch) {
                    $this->Smarty->assign('batch', $batch);
                    $this->Smarty->assign('templates_ids', $batch->payroll_batch_templates_ids);
                    $this->Smarty->assign('unit_id', $batch->payroll_batch_unit_id);
                    $_SESSION['payroll_name'] = $batch->payroll_batch_name;
                    $_SESSION['parameters']['from_date'] = $batch->payroll_batch_from_date;
                    $_SESSION['parameters']['to_date'] = $batch->payroll_batch_to_date;
                    $_SESSION['rev_user_id'] = $batch->rev_user_id;
                    $_SESSION['prov_user_id'] = $batch->prov_user_id;
                    if (json_decode($batch->payroll_batch_unit_id) != null) {
                        $_SESSION['unit_id'] = $post['unit_id'] = json_decode($batch->payroll_batch_unit_id);
                    } else if (json_decode($batch->payroll_batch_templates_ids) != null) {
                        $_SESSION['templates_id'] = $post['templates_id'] = json_decode($batch->payroll_batch_templates_ids);
                    }
                    $_SESSION['payroll_type'] = $post['payroll_type'] = $batch->payroll_batch_type;

                } else {
                    $_SESSION['parameters']['from_date'] = $post['from_date'] ?? $_SESSION['parameters']['from_date'];
                    $_SESSION['parameters']['to_date'] = $post['to_date'] ?? $_SESSION['parameters']['to_date'];
                    $_SESSION['payroll_type'] = $_SESSION['payroll_type'] ?? $this->getPayrollType($post);

                }


                // منطق جلب الموظفين حسب حالة التعديل أو الإنشاء
                if (isset($_SESSION['batch_to_edit_id']) && $_SESSION['batch_to_edit_id']) {
                    // تعديل: جلب الموظفين من جدول prl_trans فقط
                    // $employees = $this->getEmployeesFromPayrollTrans($_SESSION['batch_to_edit_id']);
                    $employees = $this->getEmployeesBasedOnPayrollType($post);

                } else {
                    // إنشاء جديد: جلب الموظفين بناءً على نوع الرواتب
                    $employees = $this->getEmployeesBasedOnPayrollType($post);
                }
                $this->Smarty->assign('employees', $employees);
                SnsoCache::update('payroll_employees', $employees, 10800);

                $extraD = SnsoCache::get($parm[3] . '_extraDetails');


                $allEmployeesAreConfirmed = collect([]);
                
                foreach ($employees as $employee) {
                    if (isset($batch->payroll_batch_id)) {
                        $result = Vacant::getEmployeePayrollDetails($employee->user_id, $batch->payroll_batch_id);
                    } else {
                        $result = Vacant::getEmployeePayrollDetails($employee->user_id);
                    }
                    // return $result;
                    $extraD = $this->getUserExtraD($employee->user_id);
                    
                    $employee->salary = $result['salary'];
                    $employee->employee_total_salary = $result['net'];
                    $employee->allowances = $result['allowances'];
                    $employee->deductions = $result['deductions'];
                    $employee->additions_list_amount = $result['additions_list_amount'];
                    $employee->extraValue = $result['extraValue'];
                    $employee->allowancesDetails = $result['allowancesDetails'];
                    $employee->deductionDetails = $result['deductionDetails'];
                    $employee->extraString = $result['extraString'];
                    $employee->employeeAbsenceDays = $result['extraString']->employeeAbsenceDays;
                    $employee->amount_comment = $result['amount_comment'];
                    $employee->has_payroll_template = $result['has_payroll_template'];
                    $employee->confirm_status = isset($result['confirm_status']) && $result['confirm_status'] !== null ? $result['confirm_status'] : 357;
                    $employee->salary_overall = $result['total_salary'];
                    $allEmployeesAreConfirmed->push($result['confirm_status']);
                    $employee->paid = $result['paid'] ?? 0;
                    $employee->not_paid = $result['not_paid'] ?? 0;
                    $employee->batch_id = isset($batch) ? $batch->id : 0;
                    
                    // Compute extraValueWithDetails for this employee
                    $postForEmployee = [
                        'employeeAbsenceDays' => $employee->extraString->employeeAbsenceDays ?? 0,
                        'deductions_list' => $employee->extraString->deductions_list ?? [],
                        'attendance_list' => $employee->extraString->attendance_list ?? [],
                        'latency_list' => $employee->extraString->latency_list ?? [],
                        'installments_list' => $employee->extraString->installments_list ?? [],
                        'calculateExtraValue' => true,
                    ];
                    $employee->extraValueWithDetails = $this->calculateUserExtraValue($postForEmployee, [3 => $employee->user_id]);
                }
                //return $employees;
                $allConfirmed = true;
                foreach ($allEmployeesAreConfirmed as $employeeStatus) {
                    if ($employeeStatus == 357 || is_null($employeeStatus)) {
                        $allConfirmed = false;
                        break;
                    }
                }
                // return ($employees);

                $this->Smarty->assign('allConfirmed', $allConfirmed);
                $this->Smarty->assign('extraD', $extraD);

                if ($_SESSION['tab1'] == '' && $_SESSION['tab2'] == '' && $_SESSION['tab3'] == '' && $_SESSION['tab4'] == '') {
                    $_SESSION['tab0'] = 'active';
                }


            } catch (VacantException $e) {

                $this->Smarty->assign('employees', []);

            }
        }

        try {
            $this->Smarty->assign('employeesList', Vacant::getEmployeeListByEntity($_SESSION['organization']));
        } catch (VacantException $e) {
            $this->Smarty->assign('employeesList', []);
        }


        // DATA PREPARATION ********************************************************************************************

        //[0] units
        try {
            $this->Smarty->assign('units', sh_unt::simpleReadByProperty([
                Unit::ORG_ID => $_SESSION['organization']->id,
                Unit::ACTIVATION => Unit::UNIT_IS_ACTIVATED
            ]));

        } catch (ModelException $e) {
            $this->Smarty->assign('units', []);
        }
        //templates
        try {
            $this->Smarty->assign('templates', PayrollTemplate::read([
                PayrollTemplate::ORG_ID => $_SESSION['organization']->id,
            ], [0 => ['property' => PayrollTemplate::ID, 'sort' => 'DESC']]));
        } catch (PayrollTemplateException $e) {

        }

        // [0] new Mosair List
        try {
            $mosairList = PayrollBatchModel::where([
                'payroll_batch_org_id' => $_SESSION['organization']->id,
                'payroll_batch_prov_status' => 1
            ])->get()->reverse();

            $this->Smarty->assign('mosairsList', $mosairList);

        } catch (PayrollBatchException $e) {

            $this->Smarty->assign('mosairsList', []);

        }
        // [1] Mosair List
        try {

            $this->Smarty->assign('MosairsList', PayrollBatch::read([
                PayrollBatch::ORG_ID => $_SESSION['organization']->id,
                PayrollBatch::BUILT_STATUS => 1
            ], [0 => ['property' => PayrollBatch::ID, 'sort' => 'DESC']]));

        } catch (PayrollBatchException $e) {

            $this->Smarty->assign('MosairsList', []);

        }

        // [2] monthlyBatches
        try {

            $this->Smarty->assign('monthlyBatches', PayrollBatch::read([
                PayrollBatch::ORG_ID => $_SESSION['organization']->id,
                PayrollBatch::TYPE => 865
            ], [0 => ['property' => PayrollBatch::ID, 'sort' => 'DESC']]));

        } catch (PayrollBatchException $e) {

        }

        try {

            $this->Smarty->assign('dailyBatches', PayrollBatch::read([
                PayrollBatch::ORG_ID => $_SESSION['organization']->id,
                PayrollBatch::TYPE => 866
            ], [0 => ['property' => PayrollBatch::ID, 'sort' => 'DESC']]));

        } catch (PayrollBatchException $e) {

        }

        // [3] Employees settings
        $this->Smarty->assign('monthly_users_list', Vacant::getEmployeesOfSalaryDeliveryType($_SESSION['organization']->id, 227));
        $this->Smarty->assign('daily_users_list', Vacant::getEmployeesOfSalaryDeliveryType($_SESSION['organization']->id, 861));

        // [4] Templates settings
        try {

            $this->Smarty->assign('templates', PayrollTemplate::read([
                PayrollTemplate::ORG_ID => $_SESSION['organization']->id
            ], [0 => ['property' => PayrollTemplate::ID, 'sort' => 'DESC']]));

        } catch (PayrollTemplateException $e) {
        }

        if (isset($_SESSION['s_prl_templates_id'])) {

            try {
                $template = PayrollTemplate::readID((int) $_SESSION['s_prl_templates_id'] ?? 0);
                $this->Smarty->assign('row', $template);
            } catch (PayrollTemplateException $e) {

            }

            if ($template) {
                $this->Smarty->assign('temp_rules', PayrollRule::read([
                    PayrollRule::ORG_ID => $_SESSION['organization']->id,
                    PayrollRule::TYPE => 1,
                    PayrollRule::TEMPLATE_ID => $template->id
                ], [0 => ['property' => PayrollRule::ORDER, 'sort' => 'DESC']]));

            }

        }
        $_SESSION['parameters']['from_date'] = $_SESSION['parameters']['from_date'] ?? Carbon::now()->startOfMonth()->toDateString();
        $_SESSION['parameters']['to_date'] = $_SESSION['parameters']['to_date'] ?? Carbon::now()->endOfMonth()->toDateString();

        $_SESSION['s_payroll_token'] = Helper::generateToken();
    }

    private function deleteFormSessionFields()
    {
        $this->deleteCachedEmployeesExtraDetails();
        SnsoCache::delete('reveiwing_status');
        SnsoCache::delete('reveiwing_status_comment');
        unset($_SESSION['payroll_name'], $_SESSION['batch_to_edit_id'], $_SESSION['templates_id'], $_SESSION['parameters']['to_date'], $_SESSION['parameters']['from_date'], $_SESSION['template_id'], $_SESSION['payroll_type'], $_SESSION['rev_user_id'], $_SESSION['prov_user_id'], $_SESSION['s_unit_id'], $_SESSION['batch_to_edit_id']);
    }

    private function deleteCachedEmployeesExtraDetails()
    {
        if (SnsoCache::have('payroll_employees')) {
            $employees = SnsoCache::get('payroll_employees');
            foreach ($employees as $employee) {
                SnsoCache::delete($employee->user_id . '_extraDetails');
            }


        }
    }

    private function getPayrollType($post)
    {

        $type = null;
        if (isset($post['payroll_type_all'])) {
            $type = $_SESSION['payroll_type_all'] = $post['payroll_type_all'];
            $post['unit_id'] = $_SESSION['unit_id'] = $post['templates_id'] = $_SESSION['templates_id'] = null;
        } else {
            switch ($post['payroll_type']) {
                case 'units':
                    $type = $_SESSION['payroll_type'] = 'units';
                    $_SESSION['s_unit_id'] = $post['unit_id'];
                    break;
                case 'templates':
                    $type = $_SESSION['payroll_type'] = 'templates';
                    $_SESSION['templates_id'] = $post['templates_id'];
                    break;
            }
        }

        return $type ?? $_SESSION['payroll_type'];
    }

    /**
     * @param $post
     * @return mixed
     */
    public function rollBatchCreateOrUpdate($post)
    {
        $from = Carbon::parse($post['form_date'] ?? $_SESSION['parameters']['from_date']);
        $to = Carbon::parse($post['to_date'] ?? $_SESSION['parameters']['to_date']);

        $batch = $this->getPayroll($post, $from, $to);

        if (!$batch) {
            $batch = new PayrollBatchModel();
        }

        $batch->name = $post['name'];
        $batch->oprCode = 'NewPayrollBatch';
        $batch->templates_ids = json_encode($post['templates_id']) ?? 0;
        $batch->type = $this->getPayrollType($post) ?? $_SESSION['payroll_type'];
        $batch->org_id = $_SESSION['organization']->id;
        $batch->unit_id = json_encode($post['unit_id']);
        $batch->created_date = date('Y-m-d');
        $batch->days = 30; // مدير الموارد البشرية يقول: بأن الشهر عندنا هو 30 يوم حتى لو كان الشهر 29 أو 28 يوم
        $batch->confirm_status = PayrollBatch::PAYSLIP_UN_CONFIRMED;
        $batch->created_by = $_SESSION['user']->id;
        $batch->from_date = $from->toDateString();
        $batch->to_date = $to->toDateString();
        $batch->rev_user_id = $_SESSION['rev_user_id'] ? $_SESSION['rev_user_id'] : $post['rev_user_id'];
        $batch->prov_user_id = $_SESSION['prov_user_id'] ? $_SESSION['prov_user_id'] : $post['prov_user_id'];
        $batch->prov_status = 1;
        $batch->save();

        // منظومة الإجراءات
        Request::createWFRequest(
            $batch->payroll_batch_org_id,
            Program::PROGRAM_HUMAN_RESOURCE_P051,
            $batch->payroll_batch_oprCode,
            $_SESSION['user']->id,
            $batch->getTable(),
            $batch->payroll_batch_id,
            $_SESSION['user']->id
        );

        $employees = $this->getEmployeesBasedOnPayrollType($post);
        foreach ($employees as $employee) {
            $result = Vacant::getEmployeePayrollDetails($employee->user_id, session('batch_to_edit_id'));
            $employee->employee_total_salary = $result['total_salary'] - $result['extraValue'];
            $employee->salary = $result['salary'];
            $employee->deduction = $result['deductions'];
            $employee->allowances = $result['allowances'];
            $employee->extraValue = $result['extraValue'];
            $employee->allowancesDetails = $result['allowancesDetails'];
            $employee->deductionDetails = $result['deductionDetails'];

            // Compute extraValueWithDetails
            $postForEmployee = [
                'employeeAbsenceDays' => $result['extraString']->employeeAbsenceDays ?? 0,
                'deductions_list' => $result['extraString']->deductions_list ?? [],
                'attendance_list' => $result['extraString']->attendance_list ?? [],
                'latency_list' => $result['extraString']->latency_list ?? [],
                'installments_list' => $result['extraString']->installments_list ?? [],
                'calculateExtraValue' => true,
            ];
            $extraValueWithDetails = $this->calculateUserExtraValue($postForEmployee, [3 => $employee->user_id]);

            // Merge other deductions into deductionDetails
            $deductionDetails = $result['deductionDetails'] ?? [];
            if (is_array($extraValueWithDetails)) {
                foreach ($extraValueWithDetails as $deduction) {
                    if ($deduction['table'] === 'hr_deductaddition' && $deduction['is_absent'] == 0 && ($deduction['inout'] === 'out' || ($deduction['inout'] === 'in' && $deduction['id'] === '17'))) {
                        $deductionDetails[$deduction['desc']] = $deduction['value'];
                    }
                }
            }

            try {
                $payrollTrans = PayrollTrans::read([
                    PayrollTrans::USER_ID => $employee->user_id,
                    PayrollTrans::BATCH_ID => $batch->payroll_batch_id,
                    PayrollTrans::UPDATE_STATUS => 1
                ])[0];
            } catch (PayrollTransException $ex) {
                $payrollTrans = new PayrollTransaction();
            }

            $payrollTrans->org_id = $_SESSION['organization']->id;
            $payrollTrans->created_by = $_SESSION['user']->id;
            $payrollTrans->user_id = $employee->user_id;
            $payrollTrans->uao_id = $employee->id;
            $payrollTrans->template_id = $employee->id;
            $payrollTrans->allowances = $result['allowances'];
            $payrollTrans->deductions = $result['deductions'];
            $payrollTrans->basic_salary = $result['salary'];
            $payrollTrans->batch_id = $batch->payroll_batch_id;
            $payrollTrans->net_after_additions = $result['net'];
            $payrollTrans->net = $result['net'];
            $payrollTrans->amount_not_paid = $result['not_paid'];
            $payrollTrans->amount_paid = $result['paid'];
            $payrollTrans->amount_comment = $result['amount_comment'];

            // Initialize empty arrays for all lists
            $payrollTrans->deductions_list = json_encode([]);
            $payrollTrans->additions_list = json_encode([]);
            $payrollTrans->attendance_list = json_encode([]);
            $payrollTrans->latency_list = json_encode([]);
            $payrollTrans->installments_list = json_encode([]);
            $payrollTrans->employeeAbsenceDays = 0;
            $payrollTrans->extravalue = 0;
            $payrollTrans->extra_addition = $result['extra_addition'] ?? 0;
            $payrollTrans->update_status = 1;
            
            // Explicitly set confirm_status from result or default to 357 (unconfirmed)
            $payrollTrans->confirm_status = isset($result['confirm_status']) && !is_null($result['confirm_status']) ? 
                                          $result['confirm_status'] : 357;
                                          
            $payrollTrans->created_date = date('Y-m-d');
            $payrollTrans->allowancesDetails = json_encode($employee->allowancesDetails ?? []);
            $payrollTrans->deductionDetails = json_encode($deductionDetails ?? []);

            // Save the transaction first
            $payrollTrans->save();

            // Now update with the actual values
            if (isset($result['extraString'])) {
                $payrollTrans->deductions_list = json_encode($result['extraString']->deductions_list ?? []);
                $payrollTrans->additions_list = json_encode($result['extraString']->additions_list ?? []);
                $payrollTrans->attendance_list = json_encode($result['extraString']->attendance_list ?? []);
                $payrollTrans->latency_list = json_encode($result['extraString']->latency_list ?? []);
                $payrollTrans->installments_list = json_encode($result['extraString']->installments_list ?? []);
                $payrollTrans->employeeAbsenceDays = $result['extraString']->employeeAbsenceDays ?? 0;
                $payrollTrans->extravalue = $result['extraValue'] ?? 0;
                $payrollTrans->extrastring = json_encode(array_merge((array) $result['extraString'], ['extraValueWithDetails' => $extraValueWithDetails]));
                
                // Make sure confirm_status persists in update
                $payrollTrans->confirm_status = isset($result['confirm_status']) && !is_null($result['confirm_status']) ?
                                              $result['confirm_status'] : 357;
                                              
                $payrollTrans->update();
            }

            try {
                $payrollTrans->save();
            } catch (PayrollTransException $e) {
                // Notification::alertMessage(Notification::ERROR, 'ErrorInSavingTransaction');
            }
            $_SESSION['tab0'] = 'active';
            $_SESSION['tab1'] = '';
            $_SESSION['s_tab_0_top_tab'] = 'payrollSave';
        }

        $this->deleteFormSessionFields();
        redirect('payroll/show', []);
    }
    /**
     * @param $post
     * @param $from
     * @param $to
     * @return \Illuminate\Database\Query\Builder
     */
    private function getPayroll($post, $from, $to)
    {

        return PayrollBatchModel::where('payroll_batch_org_id', $_SESSION['organization']->id)
            ->where('payroll_batch_name', $post['name'])
            ->where('payroll_batch_request_success', 0)
            ->where('payroll_batch_rev_user_id', $post['rev_user_id'])
            ->where('payroll_batch_prov_user_id', $post['prov_user_id'])
            ->where('payroll_batch_type', $this->getPayrollType($post))
            ->whereDate('payroll_batch_from_date', $from)
            ->whereDate('payroll_batch_to_date', $to)
            ->first();
    }

    private function getEmployeesBasedOnPayrollType($post)
    {
        if (is_null($post['payroll_type']) or is_null($post['payroll_type']) or is_null($_SESSION['payroll_type']))
            return Vacant::getEmployeeList(organization());
        $employees = [];
        switch ($post['payroll_type'] ?? $_SESSION['payroll_type']) {
            case 'units':
            case 'all_units':
                if ($post['payroll_type_all'] == 'all_units' || $_SESSION['payroll_type'] == 'all_units') {
                    $employees = Vacant::getEmployeeList(organization());
                } else {
                    $employees = Vacant::getEmployeesBasicInUnits(collect($post['unit_id'] ?? $_SESSION['unit_id'])->toArray());
                }
                break;
            case 'templates':
            case 'all_templates':
                if ($post['payroll_type_all'] == 'all_templates' || $_SESSION['payroll_type'] == 'all_templates') {
                    $templates = DB::table('prl_templates')
                        ->get(['prl_templates_id'])
                        ->pluck('prl_templates_id')
                        ->toArray();
                    $employees = Vacant::getEmployeesByTemplateId(organization('id'), $templates);
                } else {
                    $employees = Vacant::getEmployeesByTemplateId($_SESSION['organization']->id, collect($post['templates_id'] ?? $_SESSION['templates_id'])->toArray());
                }
                break;
        }
        // get only employees (with only basic job ) have payrollTemplate associated to
        $employeesHasPayrollTemplate = collect($employees)->filter(function ($employee) {
            return $employee->payroll_template_id != null && $employee->basic == 1;
        });
        return $employeesHasPayrollTemplate;
    }

    private function calculateUserExtraValue($post, $parm)
    {
        if (isset($post['calculateExtraValue'])) {
            try {
                $employeeSpecificTemplate = EmployeePayrollTemplate::where('user_id', (int) $parm[3] ?? 0)
                    ->get();

                $employee = Vacant::getEmployeeBasicVacantEntity($parm[3]);
                $template = PayrollTemplate::readID((int) $employee->sh_uao_payroll_template_id ?? 0);

                $templateItemsArray = collect([]);

                foreach ($template->rules as $temp) {
                    $_template = [];
                    $_template['effectInDay'] = $temp->effect_in_day_cost;
                    $_template['type'] = $temp->category_id;
                    if ($temp->category_id == PayrollRule::SALARY_RULE_BASIC) {
                        $_template['value'] = $employee->sh_uao_salary_basic;
                    } else {
                        if ($temp->amount_type == 650)
                            $_template['value'] = $temp->amount_fixed_value;
                        else if ($temp->amount_type == 651) {
                            $_template['value'] = $temp->getParentPayrollValue($employee->sh_uao_salary_basic);
                        }
                    }
                    $templateItemsArray->push($_template);
                }

                foreach ($employeeSpecificTemplate as $temp) {
                    $_template = [];
                    $_template['effectInDay'] = $temp->template->effect_in_day_cost;
                    $_template['type'] = $temp->template->category_id;
                    if ($temp->template->amount_type == 650)
                        $_template['value'] = $temp->template->amount_fixed_value;
                    else if ($temp->template->amount_type == 651) {
                        $_template['value'] = $temp->getParentPayrollValue($employee->sh_uao_salary_basic);
                    }
                    $templateItemsArray->push($_template);
                }
                $from = Carbon::parse($_SESSION['parameters']['from_date']);
                $to = Carbon::parse($_SESSION['parameters']['to_date']);

                $batchDayNumber = 30;
                $this->Smarty->assign('batchDayNumber', $batchDayNumber);
                $dayCost = PayrollBatch::calculateDayCost($templateItemsArray, $batchDayNumber);
                $extraValue = PayrollTrans::calculateExtraValue($post, $dayCost)['extraPayrollTransValue'];
                $extraValueWithDetails = PayrollTrans::calculateExtraValue($post, $dayCost)['extraArray'];

                if (isset($_SESSION['batch_to_edit_id'])) {
                    $payrollBatch = PayrollBatchModel::find(session('batch_to_edit_id'));
                    $transaction = PayrollTransaction::forBatch($payrollBatch)
                        ->where(PayrollTrans::USER_ID, $parm[3])
                        ->first();

                    if ($transaction) {
                        if (!isset($post['additions_list']) && $transaction->additions_list) {
                            $post['additions_list'] = json_decode($transaction->additions_list, true);
                        }
                        if (!isset($post['additions_list_amount']) && $transaction->additions_list_amount) {
                            $post['additions_list_amount'] = $transaction->additions_list_amount;
                        }

                        $transaction->extravalue = $extraValue;
                        $transaction->deductions_list = json_encode($post['deductions_list'] ?? []);
                        $transaction->attendance_list = json_encode($post['attendance_list'] ?? []);
                        $transaction->latency_list = json_encode($post['latency_list'] ?? []);
                        $transaction->installments_list = json_encode($post['installments_list'] ?? []);
                        $transaction->employeeAbsenceDays = $post['employeeAbsenceDays'];

                        if (isset($post['additions_list'])) {
                            $transaction->additions_list = json_encode($post['additions_list']);
                        }
                        if (isset($post['additions_list_amount'])) {
                            $transaction->additions_list_amount = $post['additions_list_amount'];
                        }

                        $extraString = new stdClass();
                        $extraString->deductions_list = json_decode($transaction->deductions_list, true);
                        $extraString->attendance_list = json_decode($transaction->attendance_list, true);
                        $extraString->latency_list = json_decode($transaction->latency_list, true);
                        $extraString->installments_list = json_decode($transaction->installments_list, true);
                        $extraString->employeeAbsenceDays = $transaction->employeeAbsenceDays;
                        $extraString->extraValueWithDetails = $extraValueWithDetails;
                        $transaction->extrastring = json_encode($extraString);
                        $transaction->update();
                    }
                } else {
                    $extraD = SnsoCache::get($parm[3] . '_extraDetails') ?? new stdClass();

                    if (!isset($post['additions_list']) && isset($extraD->additions_list)) {
                        $post['additions_list'] = $extraD->additions_list;
                    }
                    if (!isset($post['additions_list_amount']) && isset($extraD->additions_list_amount)) {
                        $post['additions_list_amount'] = $extraD->additions_list_amount;
                    }

                    $extraD->attendance_list = $post['attendance_list'];
                    $extraD->latency_list = $post['latency_list'];
                    $extraD->installments_list = $post['installments_list'];
                    $extraD->deductions_list = $post['deductions_list'];
                    $extraD->employeeAbsenceDays = $post['employeeAbsenceDays'];
                    $extraD->extraValue = $extraValue;
                    $extraD->extraValueWithDetails = $extraValueWithDetails;

                    $extraString = new stdClass();
                    $extraString->deductions_list = $extraD->deductions_list;
                    $extraString->attendance_list = $extraD->attendance_list;
                    $extraString->latency_list = $extraD->latency_list;
                    $extraString->installments_list = $extraD->installments_list;
                    $extraString->employeeAbsenceDays = $extraD->employeeAbsenceDays;
                    $extraString->extraValueWithDetails = $extraD->extraValueWithDetails;
                    $extraD->prl_trans_extrastring = json_encode($extraString);

                    SnsoCache::update($parm[3] . '_extraDetails', $extraD, 10800);
                }

                return $extraValueWithDetails;

            } catch (Exception $e) {
                return [];
            }
        }
        return [];
    }
    // private function calculateUserExtraValue($post, $parm)
    // {
    //     if (isset($post['calculateExtraValue'])) {
    //         try {
    //             // استرجاع قالب الرواتب الخاص بالموظف
    //             $employeeSpecificTemplate = EmployeePayrollTemplate::where('user_id', (int) $parm[3] ?? 0)->get();

    //             // استرجاع بيانات الموظف
    //             $employee = Vacant::getEmployeeBasicVacantEntity($parm[3]);
    //             $template = PayrollTemplate::readID((int) $employee->sh_uao_payroll_template_id ?? 0);

    //             // إنشاء مصفوفة لتخزين عناصر القالب
    //             $templateItemsArray = collect([]);

    //             // معالجة قواعد القالب العام
    //             foreach ($template->rules as $temp) {
    //                 $_template = [];
    //                 $_template['effectInDay'] = $temp->effect_in_day_cost;
    //                 $_template['type'] = $temp->category_id;
    //                 if ($temp->category_id == PayrollRule::SALARY_RULE_BASIC) {
    //                     $_template['value'] = $employee->sh_uao_salary_basic;
    //                 } else {
    //                     if ($temp->amount_type == 650) {
    //                         $_template['value'] = $temp->amount_fixed_value;
    //                     } else if ($temp->amount_type == 651) {
    //                         $_template['value'] = $temp->getParentPayrollValue($employee->sh_uao_salary_basic);
    //                     }
    //                 }
    //                 $templateItemsArray->push($_template);
    //             }

    //             // معالجة القوالب الخاصة بالموظف
    //             foreach ($employeeSpecificTemplate as $temp) {
    //                 $_template = [];
    //                 $_template['effectInDay'] = $temp->template->effect_in_day_cost;
    //                 $_template['type'] = $temp->template->category_id;
    //                 if ($temp->template->amount_type == 650) {
    //                     $_template['value'] = $temp->template->amount_fixed_value;
    //                 } else if ($temp->template->amount_type == 651) {
    //                     $_template['value'] = $temp->getParentPayrollValue($employee->sh_uao_salary_basic);
    //                 }
    //                 $templateItemsArray->push($_template);
    //             }

    //             // حساب عدد الأيام بين التواريخ
    //             $from = Carbon::parse($_SESSION['parameters']['from_date']);
    //             $to = Carbon::parse($_SESSION['parameters']['to_date']);
    //             $batchDayNumber = $from->diffInDays($to);
    //             $this->Smarty->assign('batchDayNumber', $batchDayNumber);

    //             // حساب تكلفة اليوم والقيم الإضافية
    //             $dayCost = PayrollBatch::calculateDayCost($templateItemsArray, $batchDayNumber);
    //             $extraValue = PayrollTrans::calculateExtraValue($post, $dayCost)['extraPayrollTransValue'];
    //             $extraValueWithDetails = PayrollTrans::calculateExtraValue($post, $dayCost)['extraArray'];

    //             // التحقق من وجود batch_to_edit_id
    //             if (isset($_SESSION['batch_to_edit_id'])) {
    //                 $payrollBatch = PayrollBatchModel::find(session('batch_to_edit_id'));

    //                 if (!$payrollBatch) {
    //                     throw new Exception("Payroll batch not found for ID: " . session('batch_to_edit_id'));
    //                 }

    //                 // البحث عن المعاملة
    //                 $transaction = PayrollTransaction::forBatch($payrollBatch)
    //                     ->where(PayrollTrans::USER_ID, $parm[3])
    //                     ->first();

    //                 // إنشاء كائن extraString
    //                 $extraString = new stdClass();
    //                 $extraString->deductions_list = $post['deductions_list'];
    //                 $extraString->attendance_list = $post['attendance_list'];
    //                 $extraString->latency_list = $post['latency_list'];
    //                 $extraString->installments_list = $post['installments_list'];
    //                 $extraString->employeeAbsenceDays = $post['employeeAbsenceDays'];
    //                 $extraString->extraValueWithDetails = $extraValueWithDetails;

    //                 // تحديث أو إنشاء المعاملة
    //                 if ($transaction) {
    //                     // تحديث المعاملة الموجودة

    //                     $transaction->update([
    //                         'prl_trans_extravalue' => $extraValue,
    //                         'prl_trans_extraValueWithDetails' => json_encode($extraValueWithDetails), // تحويل إلى JSON
    //                         'prl_trans_deductions_list' => json_encode($post['deductions_list']),
    //                         'prl_trans_attendance_list' => json_encode($post['attendance_list']),
    //                         'prl_trans_latency_list' => json_encode($post['latency_list']),
    //                         'prl_trans_installments_list' => json_encode($post['installments_list']),
    //                         'prl_trans_employeeAbsenceDays' => $post['employeeAbsenceDays'],
    //                         'prl_trans_extrastring' => json_encode($extraString),
    //                     ]);
    //                 } else {
    //                     // إنشاء معاملة جديدة
    //                     $transaction = new PayrollTransaction();
    //                     $transaction->prl_trans_batch_id = $payrollBatch->id;
    //                     $transaction->prl_trans_user_id = $parm[3];
    //                     $transaction->prl_trans_extravalue = $extraValue;
    //                     $transaction->prl_trans_extraValueWithDetails = json_encode($extraValueWithDetails);
    //                     $transaction->prl_trans_deductions_list = json_encode($post['deductions_list']);
    //                     $transaction->prl_trans_attendance_list = json_encode($post['attendance_list']);
    //                     $transaction->prl_trans_latency_list = json_encode($post['latency_list']);
    //                     $transaction->prl_trans_installments_list = json_encode($post['installments_list']);
    //                     $transaction->prl_trans_employeeAbsenceDays = $post['employeeAbsenceDays'];
    //                     $transaction->prl_trans_extrastring = json_encode($extraString);
    //                     $transaction->prl_trans_created_by = auth()->id() ?? 0; // المستخدم الحالي
    //                     $transaction->prl_trans_created_date = now();
    //                     $transaction->save();
    //                 }
    //             } else {
    //                 // إذا لم يكن هناك batch_to_edit_id، يتم تخزين البيانات في الكاش
    //                 $extraD = new stdClass();
    //                 $extraD->attendance_list = $post['attendance_list'];
    //                 $extraD->latency_list = $post['latency_list'];
    //                 $extraD->installments_list = $post['installments_list'];
    //                 $extraD->deductions_list = $post['deductions_list'];
    //                 $extraD->employeeAbsenceDays = $post['employeeAbsenceDays'];
    //                 $extraD->extraValue = $extraValue;
    //                 $extraD->prl_trans_extraValueWithDetails = $extraValueWithDetails;

    //                 $extraString = new stdClass();
    //                 $extraString->deductions_list = $extraD->deductions_list;
    //                 $extraString->attendance_list = $extraD->attendance_list;
    //                 $extraString->latency_list = $extraD->latency_list;
    //                 $extraString->installments_list = $extraD->installments_list;
    //                 $extraString->employeeAbsenceDays = $extraD->employeeAbsenceDays;
    //                 $extraString->extraValueWithDetails = $extraD->prl_trans_extraValueWithDetails;

    //                 $extraD->prl_trans_extrastring = json_encode($extraString);

    //                 SnsoCache::update($parm[3] . '_extraDetails', $extraD, 10800);
    //             }
    //         } catch (Exception $e) {
    //             // تسجيل الخطأ للتحليل
    //             \Log::error('Error in calculateUserExtraValue: ' . $e->getMessage());
    //             throw $e; // إعادة رمي الخطأ للتعامل معه في مكان آخر إذا لزم الأمر
    //         }
    //     }
    // }

    public function getUserExtraD($userId)
    {
        $extraD = SnsoCache::get($userId . '_extraDetails');

        return $extraD ?? null;
    }

    private function deletePayrollBatch($id)
    {
        $batch = PayrollBatchModel::find($id ?? 0);

        if ($batch) {
            $batch->delete();
            DB::table('prl_trans')
                ->where('prl_trans_batch_id', $batch->id)
                ->delete();
            RequestModel::where([
                Request::ORG_ID => $_SESSION['organization']->id,
                Request::TABLE_NAME => 'payroll_batch',
                Request::ROW_ID => $batch->id
            ])->delete();
        }
        $_SESSION['tab0'] = 'active';
        $_SESSION['tab1'] = '';
        $_SESSION['s_tab_0_top_tab'] = 'payrollSave';
    }

    public function BatchRead($batchid)
    {
        try {
            $this->Smarty->assign('batch', PayrollBatch::readID((int) $batchid ?? 0));
        } catch (PayrollBatchException $e) {
        }
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function FinalConfirmBatch($BatchId, $Token)
    {
        if (isset($_SESSION['s_rand_trans_num']) && $_SESSION['s_rand_trans_num'] == $Token) {

            try {

                $row = PayrollBatch::readID((int) $BatchId ?? 0);
                $row->confirm_status = 356;
                $row->save();
                $this->Smarty->assign('row', $row);
                $_SESSION['batcheslist'] = 'active';
                $_SESSION['payslibsheet'] = '';

            } catch (PayrollBatchException $e) {

                $this->Smarty->assign('row', []);

            }

        }
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function BatchCreate($post, $batchType)
    {

        $duration = $post['days'] - 1;
        $batch = new PayrollBatch();
        $batch->bindProperties($post);
        $batch->org_id = $_SESSION['organization']->id;
        $batch->from_date = $this->Date->get_date('ad', $post['from_date']);
        $batch->to_date = date("Y-m-d", strtotime("+ {$duration} days", strtotime($this->Date->get_date('ad', $post['from_date']))));
        $batch->created_date = date('Y-m-d');
        $batch->templates_based_on = implode(',', $post['templates_based_on']);
        $batch->type = $batchType == 'monthlyBatch' ? 865 : 866;
        $batch->confirm_status = PayrollBatch::PAYSLIP_UN_CONFIRMED;
        $batch->created_by = $_SESSION['user']->id;
        $batch->save();

        Request::createWFRequest(
            $_SESSION['organization']->id,
            Program::PROGRAM_HUMAN_RESOURCE_P051,
            'payroll',
            $_SESSION['user']->id,
            'prl_batches',
            $batch->id,
            $_SESSION['user']->id
        );
        $this->deleteFormSessionFields();

    }

    public function BatchUpdate($post, $id)
    {
        $duration = $post['days'] - 1;
        $from_date = $this->Date->get_date('ad', $post['from_date']);
        $to_date = date("Y-m-d", strtotime("+ {$duration} days", strtotime($from_date)));

        try {

            $batch = PayrollBatch::readID((int) $id);
            $batch->bindProperties($post);
            $batch->from_date = $from_date;
            $batch->to_date = $to_date;
            $batch->templates_based_on = implode(',', $post['templates_based_on']);
            $batch->rules_structure_json = '';
            $batch->save();

        } catch (PayrollBatchException $e) {

        }
    }

    public function updatePayRollBatch($parm, $post)
    {
        $batch = PayrollBatchModel::find($parm[0] ?? 0);
        $this->Smarty->assign('batch', $batch);
        $this->Smarty->assign('type', $parm[1]);
    }

    public function payslip($parm, $post)
    {
        try {

            $this->Smarty->assign('row', PayrollTrans::readID((int) $parm[0] ?? 0));

        } catch (PayrollTransException $e) {

            $this->Smarty->assign('row', []);
        }

        $this->Smarty->assign('confirm_list', Setting::getList(80));
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function payslipbrowse($parm, $post)
    {
        try {

            $this->Smarty->assign('row', PayrollTrans::readID((int) $parm[0] ?? 0));

        } catch (PayrollTransException $e) {

            $this->Smarty->assign('row', []);
        }

        $this->Smarty->assign('confirm_list', Setting::getList(80));
    }

    public function payslipsheetbrowse($parm, $post)
    {

        $emp_arr = [];
        $add_list = [];
        $sub_list = [];

        try {
            $batch = PayrollBatch::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('batch', $batch);
            $this->Smarty->assign('tempid', $parm[1] ?? 0);
        } catch (PayrollBatchException $e) {
        }

        try {

            $add_list = PayrollRule::read([
                PayrollRule::TEMPLATE_ID => $parm[1],
                PayrollRule::CATEGORY_ID => 645
            ], [0 => ['property' => PayrollRule::ORDER, 'sort' => 'ASC']]);

            $this->Smarty->assign('add_rule_list', $add_list);

        } catch (PayrollRuleException $e) {

        }

        try {

            $sub_list = PayrollRule::read([
                PayrollRule::TEMPLATE_ID => $parm[1],
                PayrollRule::CATEGORY_ID => 646
            ], [0 => ['property' => PayrollRule::ORDER, 'sort' => 'ASC']]);

            $this->Smarty->assign('sub_rule_list', $sub_list);

        } catch (PayrollRuleException $e) {

        }

        try {
            $emp_list = PayrollTrans::read([
                PayrollTrans::BATCH_ID => $batch->id,
                PayrollTrans::TEMPLATE_ID => (int) $parm[1],
                PayrollTrans::CONFIRM_STATUS => 356
            ], [0 => ['property' => PayrollTrans::USER_ID, 'sort' => 'ASC']]);

        } catch (PayrollTransException $e) {

        }

        if ($emp_list) {

            $i = 0;
            foreach ($emp_list as $emp) {
                $emp_arr[$i]['uao_id'] = $emp->uao_id;
                $emp_arr[$i]['user_id'] = $emp->user_id;
                $emp_arr[$i]['payslip_structure'] = $emp->payslip_structure;
                $emp_arr[$i]['basic_salary'] = $emp->basic_salary;
                $emp_arr[$i]['allowances'] = $emp->allowances;
                $emp_arr[$i]['deductions'] = $emp->deductions;
                $emp_arr[$i]['net'] = $emp->net;
                $emp_arr[$i]['extravalue'] = $emp->extravalue;
                $emp_arr[$i]['net_after_additions'] = $emp->net_after_additions;
                $emp_arr[$i]['amount_paid'] = $emp->amount_paid;
                $emp_arr[$i]['amount_not_paid'] = $emp->amount_not_paid;
                $emp_arr[$i]['amount_comment'] = $emp->amount_comment;
                $emp_arr[$i]['confirm_status'] = $emp->confirm_status;
                $adds_array = [];
                foreach ($add_list as $add) {
                    $adds_array[] = PayrollTrans::payrollTransValue($add->id, $emp->payslip_structure);
                }
                $emp_arr[$i]['adds'] = $adds_array;
                $subs_array = [];
                foreach ($sub_list as $sub) {
                    $subs_array[] = PayrollTrans::payrollTransValue($sub->id, $emp->payslip_structure);
                }
                $emp_arr[$i]['subs'] = $subs_array;
                $i++;
            }

            $this->Smarty->assign('emp_list', $emp_arr);
            $this->Smarty->assign('num', $i);

        }

    }

    public function payslipExludeList($parm, $post)
    {

        try {

            $batch = PayrollBatch::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('batch', $batch);

        } catch (PayrollBatchException $e) {
        }

        try {
            $this->Smarty->assign('emp_list', PayrollTrans::read([
                PayrollTrans::BATCH_ID => $parm[0],
                PayrollTrans::CONFIRM_STATUS => 872
            ], [0 => ['property' => PayrollTrans::USER_ID, 'sort' => 'ASC']]));

        } catch (PayrollTransException $e) {

            $this->Smarty->assign('emp_list', []);

        }

        $this->Smarty->assign('type', $parm[1]);
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function secondPayslipExludeList($parm, $post)
    {
        try {
            $batch = PayrollBatchModel::find((int) $parm[0] ?? 0);
            $this->Smarty->assign('batch', $batch);

        } catch (PayrollBatchException $e) {
        }

        try {
            $this->Smarty->assign('emp_list', PayrollTrans::read([
                PayrollTrans::BATCH_ID => $parm[0],
                PayrollTrans::CONFIRM_STATUS => 872
            ], [0 => ['property' => PayrollTrans::USER_ID, 'sort' => 'ASC']]));

        } catch (PayrollTransException $e) {
            $this->Smarty->assign('emp_list', []);
        }
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function payrollsheetbrowse($parm, $post)
    {
        try {

            $batch = PayrollBatch::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('tempsArray', explode(',', $batch->templates_based_on));
            $this->Smarty->assign('batch', $batch);

        } catch (PayrollBatchException $e) {

        }

        if ($batch) {

            try {

                $emp_list = PayrollTrans::read([
                    PayrollTrans::BATCH_ID => $batch->id
                ], [0 => ['property' => PayrollTrans::ID, 'sort' => 'ASC']]);

                $this->Smarty->assign('emp_list', $emp_list);
                $this->Smarty->assign('num', count($emp_list));

            } catch (PayrollTransException $e) {
            }

        }

    }

    private function getEmployeesFromPayrollTrans($batchId = null)
    {
        // جلب جميع user_id من جدول prl_trans (مع إمكانية التصفية حسب batchId)
        $query = PayrollTransaction::query();
        
        if ($batchId) {
            $query->where('prl_trans_batch_id', $batchId);
        }
        $userIds = $query->where('prl_trans_batch_id', $batchId)->pluck('prl_trans_user_id')->unique()->toArray();
        $employees = [];
        foreach ($userIds as $userId) {
            // جلب بيانات الوظيفة الأساسية للموظف
            $vacant = \Vacant::getEmployeeJobs(
                $_SESSION['organization']->id,
                $userId,
                \Vacant::EMPLOYEE_BASIC_VACANT
            );
            if ($vacant) {
                $employees[] = $vacant;
            }
        }
        
        return $employees;
    }

    // دالة مشتركة لإعداد بيانات الرواتب
    private function prepareSecondPayrollData($parm, $post, $batchId = null)
    {
        $batchId = $batchId ?? (int) ($parm[0] ?? 0);
        $batch = PayrollBatchModel::find($batchId);

        if (!$batch) {
            $_SESSION['notification'] = [
                'type' => 'error',
                'message' => 'الدفعة غير موجودة'
            ];
            redirect('payroll/index', []);
        }

        $_SESSION['s_payslipPreparation_batch_id'] = $batchId;

        // جلب الموظفين بناءً على نوع الرواتب
        // $employees = $this->getEmployeesBasedOnPayrollType($post);
        // جلب الموظفين بناءً على الدفعات المحفوظة في جدول prl_trans
        $employees = $this->getEmployeesFromPayrollTrans($batchId);
        
        $payrollEmployees = [];

        foreach ($employees as $employee) {
            $userId = $employee->user_id;

            // جلب تفاصيل الوظيفة
            $job = Vacant::getEmployeeJobs(241, $userId, Vacant::EMPLOYEE_BASIC_VACANT);

            // جلب بيانات الرواتب
            $result = Vacant::getEmployeePayrollDetails($userId, $batch->payroll_batch_id);

            // جلب بيانات المعاملة من قاعدة البيانات إذا كانت الدفعة محفوظة
            $transaction = null;
            if (isset($batch->payroll_batch_id)) {
                $transaction = PayrollTransaction::where('prl_trans_batch_id', $batch->payroll_batch_id)
                    ->where(PayrollTrans::USER_ID, $userId)
                    ->first();
            }

            // إنشاء كائن الموظف
            $payrollEmployee = new stdClass();
            $payrollEmployee->prl_trans_user_id = $userId;
            $payrollEmployee->prl_trans_uao_id = $employee->id;
            $payrollEmployee->prl_trans_template_id = $employee->id;
            $payrollEmployee->prl_trans_basic_salary = $result['salary'];
            $payrollEmployee->prl_trans_allowances = $result['allowances'];
            $payrollEmployee->prl_trans_deductions = $result['deductions'];
            $payrollEmployee->prl_trans_net = $result['net'];
            $payrollEmployee->prl_trans_net_after_additions = $result['net'];
            $payrollEmployee->prl_trans_amount_paid = $result['paid'];
            $payrollEmployee->prl_trans_amount_not_paid = $result['not_paid'];
            $payrollEmployee->prl_trans_amount_comment = $result['amount_comment'];

            // استخدام البيانات من المعاملة المحفوظة إذا وجدت
            if ($transaction) {
                $payrollEmployee->prl_trans_deductions_list = $transaction->prl_trans_deductions_list ?? json_encode([]);
                $payrollEmployee->prl_trans_attendance_list = $transaction->prl_trans_attendance_list ?? json_encode([]);
                $payrollEmployee->prl_trans_latency_list = $transaction->prl_trans_latency_list ?? json_encode([]);
                $payrollEmployee->prl_trans_installments_list = $transaction->prl_trans_installments_list ?? json_encode([]);
                $payrollEmployee->prl_trans_employeeAbsenceDays = $transaction->prl_trans_employeeAbsenceDays ?? 0;
                $payrollEmployee->prl_trans_extravalue = $transaction->prl_trans_extravalue ?? 0;
                $payrollEmployee->prl_trans_confirm_status = $transaction->prl_trans_confirm_status ?? $result['confirm_status'];
            } else {
                $payrollEmployee->prl_trans_deductions_list = json_encode($result['extraString']->deductions_list ?? []);
                $payrollEmployee->prl_trans_attendance_list = json_encode($result['extraString']->attendance_list ?? []);
                $payrollEmployee->prl_trans_latency_list = json_encode($result['extraString']->latency_list ?? []);
                $payrollEmployee->prl_trans_installments_list = json_encode($result['extraString']->installments_list ?? []);
                $payrollEmployee->prl_trans_employeeAbsenceDays = $result['extraString']->employeeAbsenceDays ?? 0;
                $payrollEmployee->prl_trans_extravalue = $result['extraValue'];
                $payrollEmployee->prl_trans_confirm_status = $result['confirm_status'];
            }

            $payrollEmployee->prl_trans_batch_id = $batch->payroll_batch_id;
            $payrollEmployee->prl_trans_org_id = $_SESSION['organization']->id;
            $payrollEmployee->prl_trans_created_by = $_SESSION['user']->id;
            $payrollEmployee->prl_trans_created_date = date('Y-m-d');
            $payrollEmployee->prl_trans_update_status = 1;
            $payrollEmployee->prl_trans_allowancesDetails = json_encode($result['allowancesDetails']);
            $payrollEmployee->prl_trans_deductionDetails = json_encode($result['deductionDetails']);
            $payrollEmployee->prl_trans_extra_addition = $transaction ? $transaction->prl_trans_extra_addition : 0;
            $payrollEmployee->jobObject = $job->jobObject;
            $payrollEmployee->job_name = $job->jobObject->sh_job_name ?? 'غير محدد';
            
            // Get fingerprint device number - try multiple methods to ensure we get it
            $deviceNum = '';
            if (isset($employee->sh_uao_att_device_num) && !empty($employee->sh_uao_att_device_num)) {
                $deviceNum = $employee->sh_uao_att_device_num;
            } else {
                // Fallback: get from database directly using user ID
                try {
                    $vacantRecord = Vacant::getEmployeeBasicVacantEntity($userId);
                    if ($vacantRecord && isset($vacantRecord->sh_uao_att_device_num)) {
                        $deviceNum = $vacantRecord->sh_uao_att_device_num;
                    }
                } catch (Exception $e) {
                    // If all fails, keep empty
                    $deviceNum = '';
                }
            }
            $payrollEmployee->att_device_num = $deviceNum;

            // جلب الإضافات من الكاش أو قاعدة البيانات
            $cacheKey = $userId . '_extraDetails';
            $cachedAdditions = SnsoCache::get($cacheKey);
            if ($cachedAdditions) {
                $payrollEmployee->additions_list = json_encode($cachedAdditions->additions_list ?? []);
                $payrollEmployee->extra_addition = $cachedAdditions->additions_list_amount ?? 0;
            } elseif ($transaction) {
                $payrollEmployee->additions_list = $transaction->additions_list ?? json_encode([]);
                $payrollEmployee->extra_addition = $transaction->extra_addition ?? 0;
            } else {
                $payrollEmployee->additions_list = json_encode($result['extraString']->additions_list ?? []);
                $payrollEmployee->extra_addition = $result['extra_addition'] ?? 0;
            }

            // إعداد extraValueWithDetails بناءً على additions_list
            $additions_list = json_decode($payrollEmployee->additions_list, true) ?? [];
            $extraValueWithDetails = [];
            if (!empty($additions_list)) {
                foreach ($additions_list as $deductionId) {
                    try {
                        $addition = HRDeductAdditionRequest::readID($deductionId);
                        $extraValueWithDetails[] = [
                            'table' => 'hr_deductaddition',
                            'is_absent' => 0,
                            'desc' => $addition->reasons ?? 'إضافة',
                            'value' => $addition->amount,
                            'inout' => 'in',
                            'id' => $deductionId
                        ];
                    } catch (HRDeductAdditionRequestException $e) {
                        error_log('Error fetching addition ID ' . $deductionId . ': ' . $e->getMessage());
                    }
                }
            }

            // إضافة الخصومات من extraValueWithDetails الأصلي
            $originalExtraValueWithDetails = $this->calculateUserExtraValue([
                'employeeAbsenceDays' => $result['extraString']->employeeAbsenceDays ?? 0,
                'deductions_list' => $result['extraString']->deductions_list ?? [],
                'attendance_list' => $result['extraString']->attendance_list ?? [],
                'latency_list' => $result['extraString']->latency_list ?? [],
                'installments_list' => $result['extraString']->installments_list ?? [],
                'calculateExtraValue' => true
            ], [3 => $userId]);

            if (is_array($originalExtraValueWithDetails)) {
                foreach ($originalExtraValueWithDetails as $deduction) {
                    if ($deduction['table'] === 'hr_deductaddition' && $deduction['is_absent'] == 0 && ($deduction['inout'] === 'out' || ($deduction['inout'] === 'in' && $deduction['id'] === '17'))) {
                        $extraValueWithDetails[] = $deduction;
                    }
                }
            }

            $payrollEmployee->extraValueWithDetails = $extraValueWithDetails;
            $payrollEmployee->prl_trans_extrastring = json_encode(array_merge(
                (array) $result['extraString'],
                ['extraValueWithDetails' => $extraValueWithDetails]
            ));

            // إضافة الحقول المحسوبة
            $payrollEmployee->employee_total_salary = $result['total_salary'] - $result['extraValue'];
            $payrollEmployee->salary = $result['salary'];
            $payrollEmployee->deduction = $result['deductions'];
            $payrollEmployee->allowances = $result['allowances'];
            $payrollEmployee->extraValue = $result['extraValue'];
            $payrollEmployee->allowancesDetails = $result['allowancesDetails'];
            $payrollEmployee->deductionDetails = $result['deductionDetails'];

            // إضافة الكائن إلى المصفوفة
            $payrollEmployees[] = $payrollEmployee;
        }

        // تصحيح: تسجيل عدد الموظفين
        error_log('preparePayrollData Number of payrollEmployees: ' . count($payrollEmployees));
        error_log('preparePayrollData Sample payrollEmployee: ' . json_encode($payrollEmployees[0] ?? []));

        return [
            'payrollEmployees' => $payrollEmployees,
            'batch' => $batch,
            'tempsArray' => explode(',', $batch->templates_based_on)
        ];
    }

    // تعديل دالة printall لاستخدام preparePayrollData
    public function printall($parm, $post, $file)
    {
        if (!empty($parm[0]) && $parm[0] == 'save_session') {
            $_SESSION['s_payslipPreparation_batch_id'] = $parm[1];
        }

        if (isset($_SESSION['s_payslipPreparation_batch_id'])) {
            // استدعاء الدالة المشتركة
            $data = $this->prepareSecondPayrollData($parm, $post, $_SESSION['s_payslipPreparation_batch_id']);
            // return ($data);
            // تعيين البيانات إلى القالب
            $this->Smarty->assign('payrollEmployees', $data['payrollEmployees']);
            $this->Smarty->assign('batch', $data['batch']);
            $this->Smarty->assign('tempsArray', $data['tempsArray']);
        } else {
            $this->Smarty->assign('payrollEmployees', []);
        }

        // إنشاء ملف PDF
        generatePdf(true);
    }

    // تعديل دالة secondPayrollsheetbrowse لاستخدام preparePayrollData
    public function secondPayrollsheetbrowse($parm, $post)
    {
        // استدعاء الدالة المشتركة
        $data = $this->prepareSecondPayrollData($parm, $post);
        // return ($data);
        // تعيين البيانات إلى القالب
        $this->Smarty->assign('payrollEmployees', $data['payrollEmployees']);
        $this->Smarty->assign('batch', $data['batch']);
        $this->Smarty->assign('tempsArray', $data['tempsArray']);
    }

    // public function secondPayrollsheetbrowse($parm, $post)
    // {
    //     $batch = PayrollBatchModel::find((int) $parm[0] ?? 0);

    //     if (!$batch) {
    //         $_SESSION['notification'] = [
    //             'type' => 'error',
    //             'message' => 'الدفعة غير موجودة'
    //         ];
    //         redirect('payroll/index', []);
    //     }

    //     $this->Smarty->assign('tempsArray', []);
    //     $this->Smarty->assign('batch', $batch);
    //     $_SESSION['s_payslipPreparation_batch_id'] = $parm[0];

    //     // جلب الموظفين بناءً على نوع الرواتب
    //     $employees = $this->getEmployeesBasedOnPayrollType($post);
    //     $payrollEmployees = [];

    //     foreach ($employees as $employee) {
    //         $userId = $employee->user_id;

    //         // جلب تفاصيل الوظيفة
    //         $job = Vacant::getEmployeeJobs(241, $userId, Vacant::EMPLOYEE_BASIC_VACANT);
    //         // جلب بيانات الرواتب

    //         $result = Vacant::getEmployeePayrollDetails($userId, session('batch_to_edit_id'));

    //         // جلب بيانات المعاملة من قاعدة البيانات إذا كانت الدفعة محفوظة
    //         $transaction = null;
    //         if (isset($_SESSION['batch_to_edit_id'])) {
    //             $transaction = PayrollTransaction::forBatch($batch)
    //                 ->where(PayrollTrans::USER_ID, $userId)
    //                 ->first();
    //         }

    //         // إنشاء كائن الموظف
    //         $payrollEmployee = new stdClass();
    //         $payrollEmployee->prl_trans_user_id = $userId;
    //         $payrollEmployee->prl_trans_uao_id = $employee->id;
    //         $payrollEmployee->prl_trans_template_id = $employee->id;
    //         $payrollEmployee->prl_trans_basic_salary = $result['salary'];
    //         $payrollEmployee->prl_trans_allowances = $result['allowances'];
    //         $payrollEmployee->prl_trans_deductions = $result['deductions'];
    //         $payrollEmployee->prl_trans_net = $result['net'];
    //         $payrollEmployee->prl_trans_net_after_additions = $result['net'];
    //         $payrollEmployee->prl_trans_amount_paid = $result['paid'];
    //         $payrollEmployee->prl_trans_amount_not_paid = $result['not_paid'];
    //         $payrollEmployee->prl_trans_amount_comment = $result['amount_comment'];
    //         $payrollEmployee->prl_trans_deductions_list = json_encode($result['extraString']->deductions_list ?? []);
    //         $payrollEmployee->prl_trans_attendance_list = json_encode($result['extraString']->attendance_list ?? []);
    //         $payrollEmployee->prl_trans_latency_list = json_encode($result['extraString']->latency_list ?? []);
    //         $payrollEmployee->prl_trans_installments_list = json_encode($result['extraString']->installments_list ?? []);
    //         $payrollEmployee->prl_trans_employeeAbsenceDays = $result['extraString']->employeeAbsenceDays ?? 0;
    //         $payrollEmployee->prl_trans_extravalue = $result['extraValue'];
    //         $payrollEmployee->prl_trans_confirm_status = $result['confirm_status'];
    //         $payrollEmployee->prl_trans_batch_id = $batch->payroll_batch_id;
    //         $payrollEmployee->prl_trans_org_id = $_SESSION['organization']->id;
    //         $payrollEmployee->prl_trans_created_by = $_SESSION['user']->id;
    //         $payrollEmployee->prl_trans_created_date = date('Y-m-d');
    //         $payrollEmployee->prl_trans_update_status = 1;
    //         $payrollEmployee->prl_trans_allowancesDetails = json_encode($result['allowancesDetails']);
    //         $payrollEmployee->prl_trans_deductionDetails = json_encode($result['deductionDetails']);
    //         $payrollEmployee->prl_trans_extra_addition = $transaction->prl_trans_extra_addition;
    //         $payrollEmployee->jobObject = $job->jobObject;
    //     $payrollEmployee->att_device_num = $employee->att_device_num ?? '';

    //         // جلب الإضافات من الكاش أو قاعدة البيانات
    //         $cacheKey = $userId . '_extraDetails';
    //         $cachedAdditions = SnsoCache::get($cacheKey);
    //         if ($cachedAdditions) {
    //             $payrollEmployee->additions_list = json_encode($cachedAdditions->additions_list ?? []);
    //             $payrollEmployee->extra_addition = $cachedAdditions->additions_list_amount ?? 0;
    //         } elseif ($transaction) {
    //             $payrollEmployee->additions_list = $transaction->additions_list ?? json_encode([]);
    //             $payrollEmployee->extra_addition = $transaction->extra_addition ?? 0;
    //         } else {
    //             $payrollEmployee->additions_list = json_encode($result['extraString']->additions_list ?? []);
    //             $payrollEmployee->extra_addition = $result['extra_addition'] ?? 0;
    //         }

    //         // إعداد extraValueWithDetails بناءً على prl_trans_additions_list
    //         $additions_list = json_decode($payrollEmployee->additions_list, true) ?? [];

    //         $extraValueWithDetails = [];
    //         if (!empty($additions_list)) {
    //             foreach ($additions_list as $deductionId) {
    //                 try {
    //                     $addition = HRDeductAdditionRequest::readID($deductionId);
    //             $extraValueWithDetails[] = [
    //                         'table' => 'hr_deductaddition',
    //                         'is_absent' => 0,
    //                         'desc' => $addition->reasons ?? 'إضافة',
    //                         'value' => $addition->amount,
    //                         'inout' => 'in',
    //                         'id' => $deductionId
    //                     ];
    //                 } catch (HRDeductAdditionRequestException $e) {
    //                     error_log('Error fetching deduction ID ' . $deductionId . ': ' . $e->getMessage());
    //                 }
    //             }
    //         }

    //         // إضافة الخصومات من extraValueWithDetails الأصلي
    //         $originalExtraValueWithDetails = $this->calculateUserExtraValue([
    //             'employeeAbsenceDays' => $result['extraString']->employeeAbsenceDays ?? 0,
    //             'deductions_list' => $result['extraString']->deductions_list ?? [],
    //             'attendance_list' => $result['extraString']->attendance_list ?? [],
    //             'latency_list' => $result['extraString']->latency_list ?? [],
    //             'installments_list' => $result['extraString']->installments_list ?? [],
    //             'calculateExtraValue' => true
    //         ], [3 => $userId]);

    //         if (is_array($originalExtraValueWithDetails)) {
    //             foreach ($originalExtraValueWithDetails as $deduction) {
    //                 if ($deduction['table'] === 'hr_deductaddition' && $deduction['is_absent'] == 0 && ($deduction['inout'] === 'out' || ($deduction['inout'] === 'in' && $deduction['id'] === '17'))) {
    //                     $extraValueWithDetails[] = $deduction;
    //                 }
    //             }
    //         }

    //         $payrollEmployee->extraValueWithDetails = $extraValueWithDetails;
    //         $payrollEmployee->prl_trans_extrastring = json_encode(array_merge(
    //             (array) $result['extraString'],
    //             ['extraValueWithDetails' => $extraValueWithDetails]
    //         ));

    //         // إضافة الحقول المحسوبة
    //         $payrollEmployee->employee_total_salary = $result['total_salary'] - $result['extraValue'];
    //         $payrollEmployee->salary = $result['salary'];
    //         $payrollEmployee->deduction = $result['deductions'];
    //         $payrollEmployee->allowances = $result['allowances'];
    //         $payrollEmployee->extraValue = $result['extraValue'];
    //         $payrollEmployee->allowancesDetails = $result['allowancesDetails'];
    //         $payrollEmployee->deductionDetails = $result['deductionDetails'];

    //         // إضافة الكائن إلى المصفوفة
    //         $payrollEmployees[] = $payrollEmployee;
    //     }
    //     // return ($payrollEmployees); // todo
    //     // تصحيح: تسجيل عدد الموظفين
    //     error_log('Number of payrollEmployees: ' . count($payrollEmployees));
    //     error_log('Sample payrollEmployee: ' . json_encode($payrollEmployees[0] ?? []));

    //     // تعيين البيانات إلى القالب
    //     $this->Smarty->assign('payrollEmployees', $payrollEmployees);
    //     $this->Smarty->assign('batch', $batch);

    //     // عرض القالب
    //     // $this->render('payroll/secondPayrollsheetbrowse', [
    //     //     'payrollEmployees' => $payrollEmployees,
    //     //     'batch' => $batch
    //     // ]);

    //     // return $payrollEmployees;
    // }



    public function secondPayslip($parm, $post)
    {
        try {
            $result = Vacant::getEmployeePayrollDetails((int) $parm[0], session('batch_to_edit_id'));
            $row = new stdClass();
            $row->user_id = $parm[0];
            $row->salary = $result['salary'];
            $row->employee_total_salary = $result['total_salary'] - $result['extraValue'];
            $row->allowances = $result['allowances'];
            $row->deductions = $result['deductions'];
            $row->extraValue = $result['extraValue'];
            $row->confirm_status = $result['confirm_status'] ?? SnsoCache::get($parm[0] . '_extraDetails')->confirm_status ?? 357;
            $row->amount_comment = $result['amount_comment'];
            $row->paid = $result['paid'];
            $row->not_paid = $result['not_paid'];
            $this->Smarty->assign('row', $row);
            $_SESSION['payroll_batch_unit_id'] = $parm[1];
        } catch (PayrollTransException $e) {
            $this->Smarty->assign('row', []);
        }

        $this->Smarty->assign('confirm_list', Setting::getList(80));
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function printSecondPayslip($parm, $post)
    {
        try {
            $result = Vacant::getEmployeePayrollDetails((int) $parm[0]);
            $row = new stdClass();
            $row->user_id = $parm[0];
            $row->salary = $result['salary'];
            $row->employee_total_salary = $result['total_salary'] - $result['extraValue'];
            $row->allowances = $result['allowances'];
            $row->deductions = $result['deductions'];
            $row->extraValue = $result['extraValue'];
            $row->confirm_status = $result['confirm_status'];
            $row->paid = $result['paid'];
            $row->not_paid = $result['not_paid'];
            $this->Smarty->assign('row', $row);
        } catch (PayrollTransException $e) {
            $this->Smarty->assign('row', []);
        }

        $this->Smarty->assign('confirm_list', Setting::getList(80));
        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()), "", "", "", true);
    }

    /*
     * Monthly & Daily Batch add,edit,confirm,build
     */

    public function payrollSheetPrint($parm, $post)
    {

        $add_list = [];
        $sub_list = [];

        try {

            $batch = PayrollBatch::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('batch', $batch);
            $this->Smarty->assign('tempid', $parm[1] ?? 0);

        } catch (PayrollBatchException $e) {

        }

        try {

            $add_list = PayrollRule::read([
                PayrollRule::TEMPLATE_ID => $parm[1],
                PayrollRule::CATEGORY_ID => 645
            ], [0 => ['property' => PayrollRule::ORDER, 'sort' => 'ASC']]);

            $this->Smarty->assign('add_rule_list', $add_list);

        } catch (PayrollRuleException $e) {

            $this->Smarty->assign('add_rule_list', []);

        }

        try {

            $sub_list = PayrollRule::read([
                PayrollRule::TEMPLATE_ID => $parm[1],
                PayrollRule::CATEGORY_ID => 646
            ], [0 => ['property' => PayrollRule::ORDER, 'sort' => 'ASC']]);

            $this->Smarty->assign('sub_rule_list', $sub_list);

        } catch (PayrollRuleException $e) {
            $this->Smarty->assign('sub_rule_list', []);
        }


        try {

            $emp_list = PayrollTrans::read([
                PayrollTrans::BATCH_ID => $parm[0],
                PayrollTrans::TEMPLATE_ID => $parm[1],
                PayrollTrans::CONFIRM_STATUS => 356
            ], [0 => ['property' => PayrollTrans::USER_ID, 'sort' => 'ASC']]);

        } catch (PayrollTransException $e) {

        }

        if ($emp_list) {

            $i = 0;
            $emp_arr = [];
            foreach ($emp_list as $emp) {

                try {
                    $user = User::readID((int) $emp->user_id ?? 0);
                    $emp_arr[$i]['uao_id'] = $emp->uao_id;
                    $emp_arr[$i]['user_id'] = $emp->user_id;
                    $emp_arr[$i]['identity_number'] = $user->identity_number;
                    $emp_arr[$i]['bank_name'] = $user->bank_name;
                    $emp_arr[$i]['bank_number'] = $user->bank_number;
                } catch (UserException $e) {
                }

                $emp_arr[$i]['payslip_structure'] = $emp->payslip_structure;
                $emp_arr[$i]['basic_salary'] = $emp->basic_salary;
                $emp_arr[$i]['allowances'] = $emp->allowances;
                $emp_arr[$i]['deductions'] = $emp->deductions;
                $emp_arr[$i]['net'] = $emp->net;
                $emp_arr[$i]['extravalue'] = $emp->extravalue;
                $emp_arr[$i]['net_after_additions'] = $emp->net_after_additions;
                $emp_arr[$i]['amount_paid'] = $emp->amount_paid;
                $emp_arr[$i]['amount_not_paid'] = $emp->amount_not_paid;
                $emp_arr[$i]['amount_comment'] = $emp->amount_comment;
                $emp_arr[$i]['confirm_status'] = $emp->confirm_status;
                $adds_array = [];
                foreach ($add_list as $add) {
                    $adds_array[] = PayrollTrans::payrollTransValue($add->id, $emp->payslip_structure);
                }
                $emp_arr[$i]['adds'] = $adds_array;
                $subs_array = [];
                foreach ($sub_list as $sub) {
                    $subs_array[] = PayrollTrans::payrollTransValue($sub->id, $emp->payslip_structure);
                }
                $emp_arr[$i]['subs'] = $subs_array;
                $i++;
            }

            $this->Smarty->assign('emp_list', $emp_arr);
            $this->Smarty->assign('num', $i);

        }

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()), "", "", "", true);
    }

    public function confirm($parm, $post)
    {
        switch ($parm[1]) {

            case 'tab1':
                break;

            case 'tab2':
                try {
                    $this->Smarty->assign('batch', PayrollBatch::readID((int) $parm[0] ?? 0));
                } catch (PayrollBatchException $e) {
                }
                $_SESSION['s_tab_2_top_tab'] = $parm[2];
                break;

            case 'tab3':
                break;

            case 'tab4':
                try {
                    $this->Smarty->assign('template', PayrollTemplate::readID((int) $parm[0] ?? 0));
                } catch (PayrollTemplateException $e) {
                }
                break;

            case 'tab5':

                try {
                    $this->Smarty->assign('rule', PayrollRule::readID((int) $parm[0] ?? 0));
                } catch (PayrollRuleException $e) {
                }
                if (isset($parm[2]) && $parm[2] == 'rulestabone') {
                    $_SESSION['rulestabone'] = 'active';
                    $_SESSION['rulestabtow'] = '';
                }
                if (isset($parm[2]) && $parm[2] == 'rulestabtow') {
                    $_SESSION['rulestabone'] = '';
                    $_SESSION['rulestabtow'] = 'active';
                }
                break;
        }

        $this->Smarty->assign('tab', $parm[1]);
        $this->Smarty->assign('batchType', $parm[2]);
    }

    public function payrollInfo($parm, $post)
    {
        try {

            $batch = PayrollBatch::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('row', $batch);

        } catch (PayrollBatchException $e) {

            $this->Smarty->assign('row', []);
            $this->Smarty->assign('templates', []);

        }

        if ($batch) {

            $i = 0;
            $tempArr = [];

            $tempList = explode(',', $batch->templates_based_on);
            foreach ($tempList as $temp) {

                $tempArr[$i]['tempID'] = $temp;
                $tempArr[$i]['tempEmployeeNum'] = PayrollTrans::count([
                    PayrollTrans::ORG_ID => $batch->org_id,
                    PayrollTrans::BATCH_ID => $batch->id,
                    PayrollTrans::TEMPLATE_ID => $temp,
                    PayrollTrans::CONFIRM_STATUS => 356
                ]);
                $i++;

            }

            $this->Smarty->assign('templates', $tempArr);

        }
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function secondPayrollInfo($parm, $post)
    {
        try {

            $batch = PayrollBatchModel::find((int) $parm[0] ?? 0);
            $this->Smarty->assign('row', $batch);

        } catch (PayrollBatchException $e) {

            $this->Smarty->assign('row', []);
            $this->Smarty->assign('templates', []);

        }

        if ($batch) {

            $i = 0;
            $tempArr = [];

            $tempList = explode(',', $batch->templates_based_on);
            foreach ($tempList as $temp) {

                $tempArr[$i]['tempID'] = $temp;
                $tempArr[$i]['tempEmployeeNum'] = PayrollTrans::count([
                    PayrollTrans::ORG_ID => $batch->org_id,
                    PayrollTrans::BATCH_ID => $batch->id,
                    PayrollTrans::TEMPLATE_ID => $temp,
                    PayrollTrans::CONFIRM_STATUS => 356
                ]);
                $i++;

            }

            $this->Smarty->assign('templates', $tempArr);

        }
        $this->Smarty->assign('units', Unit::read([
            Unit::ORG_ID => organization('id'),
            Unit::ACTIVATION => Unit::UNIT_IS_ACTIVATED,
        ]));
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function confirmtemplate($parm, $post)
    {
        try {

            $this->Smarty->assign('row', PayrollTemplate::readID((int) $parm[0] ?? 0));

        } catch (PayrollTemplateException $e) {

            $this->Smarty->assign('row', []);

        }

        $this->Smarty->assign('batchType', $parm[2]);
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function addrule($parm, $post)
    {
        
        try {

            $this->Smarty->assign('row', PayrollTemplate::readID((int) $parm[1] ?? 0));

        } catch (PayrollTemplateException $e) {

            $this->Smarty->assign('row', []);

        }

        $this->Smarty->assign('yesNoList', Setting::getList(119));
        $this->Smarty->assign('rule_type_list', Setting::getList(144));
        $this->Smarty->assign('rule_condition_list', Setting::getList(145));
        $this->Smarty->assign('rule_active_list', Setting::getList(147));
        $this->Smarty->assign('rule_amount_list', Setting::getList(146));
        
        try {
            $this->Smarty->assign('rules_list', collect(PayrollRule::read([
                PayrollRule::ORG_ID => $_SESSION['organization']->id,
                PayrollRule::TEMPLATE_ID => $_SESSION['s_prl_templates_id']
            ]))->filter(function ($rule) {
                return $rule->category_id != PayrollRule::SALARY_RULE_DEDUCTION;
            })->values());
        } catch (PayrollRuleException $e) {
        }

        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));

        if (isset($parm[0]) && $parm[0] == 'rulestabone') {
            $_SESSION['rulestabone'] = 'active';
            $_SESSION['rulestabtow'] = '';
            $_SESSION['new_rule_type'] = 1;
        }
        if (isset($parm[0]) && $parm[0] == 'rulestabtow') {
            $_SESSION['rulestabone'] = '';
            $_SESSION['rulestabtow'] = 'active';
            $_SESSION['new_rule_type'] = 2;
        }
    }

    public function editrule($parm, $post)
    {
        try {
            $this->Smarty->assign('row', PayrollRule::readID((int) $parm[0] ?? 0));
        } catch (PayrollRuleException $e) {
        }
        try {
            $this->Smarty->assign('rules_list', PayrollRule::read([
                PayrollRule::ORG_ID => $_SESSION['organization']->id,
                PayrollRule::TEMPLATE_ID => $_SESSION['s_prl_templates_id']
            ]));
        } catch (PayrollRuleException $e) {
        }

        $this->Smarty->assign('yesNoList', Setting::getList(119));
        $this->Smarty->assign('rule_types_list', Setting::getList(144));
        $this->Smarty->assign('rule_amount_list', Setting::getList(146));

        $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';

        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));

        if (isset($parm[0]) && $parm[1] == 'rulestabone') {
            $_SESSION['rulestabone'] = 'active';
            $_SESSION['rulestabtow'] = '';
        }
        if (isset($parm[0]) && $parm[1] == 'rulestabtow') {
            $_SESSION['rulestabone'] = '';
            $_SESSION['rulestabtow'] = 'active';
        }
    }

    public function addtemp($parm, $post)
    {
        try {
            $this->Smarty->assign('rules_list', PayrollRule::read([
                PayrollRule::ORG_ID => $_SESSION['organization']->id,
                PayrollRule::TYPE => 1
            ], [0 => ['property' => PayrollRule::ORDER, 'sort' => 'DESC']]));

        } catch (PayrollRuleException $e) {

        }

        $this->Smarty->assign('salary_types', Setting::getList(196));
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function edittemp($parm, $post)
    {
        try {

            $row = PayrollTemplate::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('row', $row);

        } catch (PayrollTemplateException $e) {

            $this->Smarty->assign('row', []);

        }

        try {

            $this->Smarty->assign('rules_list', PayrollRule::read([
                PayrollRule::ORG_ID => $_SESSION['organization']->id,
                PayrollRule::TYPE => 1
            ], [0 => ['property' => PayrollRule::ORDER, 'sort' => 'DESC']]));

        } catch (PayrollRuleException $e) {
            $this->Smarty->assign('rules_list', []);
        }

        $this->Smarty->assign('salary_types', Setting::getList(196));
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function selecttemp($parm, $post)
    {
        try {

            $this->Smarty->assign('templates', PayrollTemplate::read([
                PayrollTemplate::ORG_ID => $_SESSION['organization']->id,
            ]));

        } catch (PayrollTemplateException $e) {
            $this->Smarty->assign('templates', []);
        }

        try {
            $this->Smarty->assign('vacant', Vacant::readID((int) $parm[0] ?? 0));

        } catch (VacantException $e) {

            $this->Smarty->assign('vacant', []);

        }

        if (isset($parm[2]) && $parm[2] == "monthly") {
            $_SESSION['monthly'] = 'active';
            $_SESSION['daily'] = '';
        }
        if (isset($parm[2]) && $parm[2] == "daily") {
            $_SESSION['monthly'] = '';
            $_SESSION['daily'] = 'active';
        }

        $this->Smarty->assign('type', $parm[2]);

        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function payslipPreparation($parm, $post)
    {

        switch ($parm[0]) {
            case 'save_session':
                $_SESSION['s_payslipPreparation_batch_id'] = $parm[1];
                break;

            case 'updatepayslip':

                if ($_SESSION['s_rand_trans_num'] == $parm[2]) {

                    try {

                        $trans = PayrollTrans::readID((int) $parm[1]);
                        $trans->amount_comment = $post['amount_comment'];
                        $trans->confirm_status = $post['confirm_status'];
                        $trans->save();

                    } catch (PayrollTransException $e) {

                    }

                }
                break;

            case 'updateaddition':

                if ($_SESSION['s_rand_trans_num'] == $parm[2]) {
                    PayrollTrans::AdditionsUpdate($post, $parm[1]);
                    $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
                }
                break;

            case 'getEmployeePayslipBack':

                if ($_SESSION['s_rand_trans_num'] == $parm[1]) {

                    try {

                        $transaction = PayrollTrans::readID((int) $parm[2]);
                        $transaction->confirm_status = PayrollTrans::PAYSLIP_CONFIRMED;
                        $transaction->save();

                    } catch (PayrollTransException $e) {

                    }

                    $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
                }
                break;

            case 'confirmAllPayslips':

                if ($_SESSION['s_rand_trans_num'] == $parm[1]) {

                    try {
                        $transactions = PayrollTrans::read([
                            PayrollTrans::BATCH_ID => (int) $parm[2]
                        ]);

                        foreach ($transactions as $transaction) {

                            try {

                                $transaction->confirm_status = (string) $parm[3];
                                $transaction->save();

                            } catch (PayrollTransException $e) {

                            }

                        }

                    } catch (PayrollTransException $e) {

                    }

                    $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
                }
                break;
        }

        if (isset($_SESSION['s_payslipPreparation_batch_id'])) {

            try {

                $batch = PayrollBatch::readID((int) $_SESSION['s_payslipPreparation_batch_id'] ?? 0);
                $this->Smarty->assign('batch', $batch);
                $this->Smarty->assign('tempsArray', explode(',', $batch->templates_based_on));

            } catch (PayrollBatchException $e) {
            }

            if ($batch) {

                try {
                    $this->Smarty->assign('emp_list', PayrollTrans::read([PayrollTrans::BATCH_ID => $batch->id]));
                } catch (PayrollTransException $e) {
                }

            }

        } else {
            $this->Smarty->assign('emp_list', []);
        }

    }

    public function payslipadditions($parm, $post)
    {

        $this->Smarty->assign('userID', (int) $parm[1] ?? 0);

        try {

            $transRow = PayrollTrans::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('row', $transRow);

        } catch (PayrollTransException $e) {
            $this->Smarty->assign('row', []);
        }

        if ($transRow) {

            try {
                /**
                 * @var $userBasicJob Vacant
                 */
                $userBasicJob = Vacant::getEmployeeJobs($transRow->org_id, $transRow->user_id, Vacant::EMPLOYEE_BASIC_VACANT);
                $this->Smarty->assign('UserJobDayNumber', $userBasicJob->days_in_month);
            } catch (VacantException $e) {

            }

            $this->Smarty->assign('confirm_list', Setting::getList(80));

            if (!empty($parm[1]) && !empty($parm[0])) {

                /*
                 * Absence list ************************************************************************************************
                 */
                $absencesArray = [];

                try {

                    $absences = HRAttendanceRequest::read([
                        HRAttendanceRequest::ORG_ID => $transRow->org_id,
                        HRAttendanceRequest::USER_ID => $transRow->user_id,
                        HRAttendanceRequest::MANIPULATION_TYPE => 355,
                        HRAttendanceRequest::REQUEST_SUCCESS => 1
                    ], [0 => ['property' => HRAttendanceRequest::ID, 'sort' => 'DESC']]);

                } catch (HRAttendanceRequestException $e) {
                }

                if ($absences) {

                    foreach ($absences as $aKey => $absence) {
                        if (empty($absence->manipulation_trans_id) || $absence->manipulation_trans_id == $transRow->id) {
                            $absencesArray[] = $absence;
                        }
                    }

                }

                $this->Smarty->assign('absencesNum', count($absencesArray));
                $this->Smarty->assign('absences', $absencesArray);

                /*
                 * Latency list ************************************************************************************************
                 */
                $latencyArray = [];

                try {

                    $latencies = HRLatencyRequest::read([
                        HRLatencyRequest::ORG_ID => $transRow->org_id,
                        HRLatencyRequest::USER_ID => $transRow->user_id,
                        HRLatencyRequest::MANIPULATION_TYPE => 355,
                        HRLatencyRequest::REQUEST_SUCCESS => 1
                    ], [0 => ['property' => HRLatencyRequest::ID, 'sort' => 'DESC']]);

                } catch (HRLatencyRequestException $e) {
                }

                if ($latencies) {

                    foreach ($latencies as $aKey => $latency) {
                        if (empty($latency->manipulation_trans_id) || $latency->manipulation_trans_id == $transRow->id) {
                            $latencyArray[] = $latency;
                        }
                    }

                }

                $this->Smarty->assign('latencyNum', count($latencyArray));
                $this->Smarty->assign('latencies', $latencyArray);

                /*
                 * Advance list ************************************************************************************************
                 */

                try {

                    $advanceRequests = HRAdvanceRequest::read([
                        HRAdvanceRequest::ORG_ID => $transRow->org_id,
                        HRAdvanceRequest::USER_ID => (int) $transRow->user_id ?? 0,
                        HRAdvanceRequest::REQUEST_SUCCESS => 1
                    ], [0 => ['property' => HRAdvanceRequest::ID, 'sort' => 'DESC']]);

                    $arr = [];
                    foreach ($advanceRequests as $key => $advanceRequest) {
                        $arr[] = $advanceRequest->id;
                    }

                    if (count($arr) >= 1) {
                        $advanceRequestIds = implode(',', $arr);
                    } else {
                        $advanceRequestIds = implode(',', [0]);
                    }

                    $advanceInstallmentsArray = [];

                    /**
                     * @var $advanceInstallmentsList HRAdvanceInstallment[]
                     */
                    $advanceInstallmentsList = HRAdvanceInstallment::getInstallmentsinRange($advanceRequestIds, (int) $transRow->user_id);

                    foreach ($advanceInstallmentsList as $installment) {
                        if (empty($installment->manipulation_batche_trans_id) || $installment->manipulation_batche_trans_id == $transRow->id) {
                            $advanceInstallmentsArray[] = $installment;
                        }
                    }

                    $this->Smarty->assign('advanceInstallmentsNum', count($advanceInstallmentsArray));
                    $this->Smarty->assign('advanceInstallments', $advanceInstallmentsArray);

                } catch (HRAdvanceRequestException $e) {
                    $this->Smarty->assign('advanceInstallments', 0);
                }


                /*
                 * deduct request ************************************************************************************************
                 */

                $deductAdditionsArray = [];

                try {

                    $deductAdditions = HRDeductAdditionRequest::read([
                        HRDeductAdditionRequest::ORG_ID => (int) $transRow->org_id,
                        HRDeductAdditionRequest::USER_ID => (int) $transRow->user_id ?? 0,
                        HRDeductAdditionRequest::TYPE => 916,
                        HRDeductAdditionRequest::REQUEST_SUCCESS => 1
                    ], [0 => ['property' => HRDeductAdditionRequest::ID, 'sort' => 'DESC']]);

                    foreach ($deductAdditions as $dKey => $deduct) {
                        if (empty($deduct->manipulation_batche_trans_id) || $deduct->manipulation_batche_trans_id == $transRow->id) {
                            $deductAdditionsArray[] = $deduct;
                        }
                    }

                } catch (HRDeductAdditionRequestException $e) {
                }

                $this->Smarty->assign('deductAdditionsNum', count($deductAdditionsArray));
                $this->Smarty->assign('deductAdditions', $deductAdditionsArray);

                /*
                 * Other Deductions
                 */

                /**
                 * @var $EmployeeBasicJob Vacant
                 */
                $EmployeeBasicJob = Vacant::getEmployeeJobs(
                    $transRow->org_id,
                    (int) $transRow->user_id ?? 0,
                    Vacant::EMPLOYEE_BASIC_VACANT
                );

                if ($EmployeeBasicJob->salary_delivery == 227) {

                    $this->Smarty->assign('employeeWorkDays', 30);

                } else {

                    $this->Smarty->assign('employeeWorkDays', $EmployeeBasicJob->days_in_month);

                }

                $OtherDeductArray = json_decode($transRow->extrastring, true);
                foreach ($OtherDeductArray as $deduct) {
                    if ($deduct['table'] == 'employeeAbsenceDays' && $deduct['id'] == 0) {
                        $this->Smarty->assign('employeeAbsenceDays', $deduct['desc']);
                        $this->Smarty->assign('employeeAbsenceValue', $deduct['value']);
                        break;
                    }
                }

            }

        }

        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    /**
     * @param $parm
     * @param $post
     */
    public function payslipAdditionsPrint($parm, $post)
    {
        $this->Smarty->assign('userID', (int) $parm[1] ?? 0);

        try {

            $transRow = PayrollTrans::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('row', $transRow);

        } catch (PayrollTransException $e) {
            $this->Smarty->assign('row', []);
        }

        if ($transRow) {

            try {
                /**
                 * @var $userBasicJob Vacant
                 */
                $userBasicJob = Vacant::getEmployeeJobs($transRow->org_id, $transRow->user_id, Vacant::EMPLOYEE_BASIC_VACANT);
                $this->Smarty->assign('UserJobDayNumber', $userBasicJob->days_in_month);
            } catch (VacantException $e) {

            }

            $this->Smarty->assign('confirm_list', Setting::getList(80));

            if (!empty($parm[1]) && !empty($parm[0])) {

                /*
                 * Absence list ************************************************************************************************
                 */
                $absencesArray = [];

                try {

                    $absences = HRAttendanceRequest::read([
                        HRAttendanceRequest::ORG_ID => $transRow->org_id,
                        HRAttendanceRequest::USER_ID => $transRow->user_id,
                        HRAttendanceRequest::MANIPULATION_TYPE => 355,
                        HRAttendanceRequest::REQUEST_SUCCESS => 1
                    ], [0 => ['property' => HRAttendanceRequest::ID, 'sort' => 'DESC']]);

                } catch (HRAttendanceRequestException $e) {
                }

                if ($absences) {

                    foreach ($absences as $aKey => $absence) {
                        if (empty($absence->manipulation_trans_id) || $absence->manipulation_trans_id == $transRow->id) {
                            $absencesArray[] = $absence;
                        }
                    }

                }

                $this->Smarty->assign('absencesNum', count($absencesArray));
                $this->Smarty->assign('absences', $absencesArray);

                /*
                 * Latency list ************************************************************************************************
                 */
                $latencyArray = [];

                try {

                    $latencies = HRLatencyRequest::read([
                        HRLatencyRequest::ORG_ID => $transRow->org_id,
                        HRLatencyRequest::USER_ID => $transRow->user_id,
                        HRLatencyRequest::MANIPULATION_TYPE => 355,
                        HRLatencyRequest::REQUEST_SUCCESS => 1
                    ], [0 => ['property' => HRLatencyRequest::ID, 'sort' => 'DESC']]);

                } catch (HRLatencyRequestException $e) {
                }

                if ($latencies) {

                    foreach ($latencies as $aKey => $latency) {
                        if (empty($latency->manipulation_trans_id) || $latency->manipulation_trans_id == $transRow->id) {
                            $latencyArray[] = $latency;
                        }
                    }

                }

                $this->Smarty->assign('latencyNum', count($latencyArray));
                $this->Smarty->assign('latencies', $latencyArray);

                /*
                 * Advance list ************************************************************************************************
                 */

                try {

                    $advanceRequests = HRAdvanceRequest::read([
                        HRAdvanceRequest::ORG_ID => $transRow->org_id,
                        HRAdvanceRequest::USER_ID => (int) $transRow->user_id ?? 0,
                        HRAdvanceRequest::REQUEST_SUCCESS => 1
                    ], [0 => ['property' => HRAdvanceRequest::ID, 'sort' => 'DESC']]);

                    $arr = [];
                    foreach ($advanceRequests as $key => $advanceRequest) {
                        $arr[] = $advanceRequest->id;
                    }

                    if (count($arr) >= 1) {
                        $advanceRequestIds = implode(',', $arr);
                    } else {
                        $advanceRequestIds = implode(',', [0]);
                    }

                    $advanceInstallmentsArray = [];

                    /**
                     * @var $advanceInstallmentsList HRAdvanceInstallment[]
                     */
                    $advanceInstallmentsList = HRAdvanceInstallment::getInstallmentsinRange($advanceRequestIds, (int) $transRow->user_id);

                    foreach ($advanceInstallmentsList as $installment) {
                        if (empty($installment->manipulation_batche_trans_id) || $installment->manipulation_batche_trans_id == $transRow->id) {
                            $advanceInstallmentsArray[] = $installment;
                        }
                    }

                    $this->Smarty->assign('advanceInstallmentsNum', count($advanceInstallmentsArray));
                    $this->Smarty->assign('advanceInstallments', $advanceInstallmentsArray);

                } catch (HRAdvanceRequestException $e) {
                    $this->Smarty->assign('advanceInstallments', 0);
                }


                /*
                 * deduct request ************************************************************************************************
                 */

                $deductAdditionsArray = [];

                try {

                    $deductAdditions = HRDeductAdditionRequest::read([
                        HRDeductAdditionRequest::ORG_ID => (int) $transRow->org_id,
                        HRDeductAdditionRequest::USER_ID => (int) $transRow->user_id ?? 0,
                        HRDeductAdditionRequest::TYPE => 916,
                        HRDeductAdditionRequest::REQUEST_SUCCESS => 1
                    ], [0 => ['property' => HRDeductAdditionRequest::ID, 'sort' => 'DESC']]);

                    foreach ($deductAdditions as $dKey => $deduct) {
                        if (empty($deduct->manipulation_batche_trans_id) || $deduct->manipulation_batche_trans_id == $transRow->id) {
                            $deductAdditionsArray[] = $deduct;
                        }
                    }

                } catch (HRDeductAdditionRequestException $e) {
                }

                $this->Smarty->assign('deductAdditionsNum', count($deductAdditionsArray));
                $this->Smarty->assign('deductAdditions', $deductAdditionsArray);

                /*
                 * Other Deductions
                 */

                /**
                 * @var $EmployeeBasicJob Vacant
                 */
                $EmployeeBasicJob = Vacant::getEmployeeJobs(
                    $transRow->org_id,
                    (int) $transRow->user_id ?? 0,
                    Vacant::EMPLOYEE_BASIC_VACANT
                );

                if ($EmployeeBasicJob->salary_delivery == 227) {

                    $this->Smarty->assign('employeeWorkDays', 30);

                } else {

                    $this->Smarty->assign('employeeWorkDays', $EmployeeBasicJob->days_in_month);

                }

                $OtherDeductArray = json_decode($transRow->extrastring, true);
                foreach ($OtherDeductArray as $deduct) {
                    if ($deduct['table'] == 'employeeAbsenceDays' && $deduct['id'] == 0) {
                        $this->Smarty->assign('employeeAbsenceDays', $deduct['desc']);
                        $this->Smarty->assign('employeeAbsenceValue', $deduct['value']);
                        break;
                    }
                }

            }
            DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()), "", "", "", true);

        }

        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function secondPayslipDeductions($parm, $post)
    {

        $this->Smarty->assign('user', UserModel::find($parm[0] ?? 0));

        $this->Smarty->assign('batch_id', $parm[1] ?? 0);
        try {
            /**
             * @var $userBasicJob Vacant
             */
            $userBasicJob = Vacant::getEmployeeJobs($_SESSION['organization']->id, $parm[0], Vacant::EMPLOYEE_BASIC_VACANT);
            $this->Smarty->assign('vacant', $userBasicJob);

            $this->Smarty->assign('UserJobDayNumber', $userBasicJob->days_in_month);
        } catch (VacantException $e) {

        }

        $this->Smarty->assign('confirm_list', Setting::getList(80));


        if (!empty($parm[0])) {


            /*
             * Absence list ************************************************************************************************
             */
            $absencesArray = [];


            try {
                $absencesArray = HRAttendanceRequest::convertFromQB(
                    DB::table('hr_attendance')->Where(HRAttendanceRequest::USER_ID, $parm[0])
                        ->where(HRAttendanceRequest::MANIPULATION_TYPE, 355)
                        ->where(HRAttendanceRequest::REQUEST_SUCCESS, 1)
                        ->where(HRAttendanceRequest::MANIPULATION_BATCHE_ID, 0)
                        ->where(HRAttendanceRequest::MANIPULATION_TRANS_ID, 0)
                        ->get()
                );


            } catch (HRAttendanceRequestException $e) {
            }

            $this->Smarty->assign('absencesNum', count($absencesArray));
            $this->Smarty->assign('absences', $absencesArray);

            /*
             * Latency list ************************************************************************************************
             */
            $latencyArray = [];

            try {

                $latencyArray = HRLatencyRequest::convertFromQB(
                    DB::table('hr_latency')->Where(HRLatencyRequest::USER_ID, $parm[0])
                        ->where(HRLatencyRequest::MANIPULATION_TYPE, 0)
                        ->where(HRLatencyRequest::REQUEST_SUCCESS, 1)
                        ->whereNull(HRLatencyRequest::MANIPULATION_BATCHE_ID)
                        ->whereNull(HRLatencyRequest::MANIPULATION_TRANS_ID)
                        ->get()
                );


            } catch (HRLatencyRequestException $e) {
            }

            $this->Smarty->assign('latencyNum', count($latencyArray));
            $this->Smarty->assign('latencies', $latencyArray);

            /*
             * Advance list ************************************************************************************************
             */

            try {

                $advanceRequests = HRAdvanceRequest::convertFromQB(
                    DB::table('hr_advancerequest')
                        ->Where(HRAdvanceRequest::USER_ID, $parm[0])
                        ->where(HRAdvanceRequest::REQUEST_SUCCESS, 1)
                        ->where(HRAdvanceRequest::ENTRY_ID, '!=', 0)
                        ->get()
                );

                $arr = [];
                foreach ($advanceRequests as $key => $advanceRequest) {
                    $arr[] = $advanceRequest->id;
                }

                if (count($arr) >= 1) {
                    $advanceRequestIds = implode(',', $arr);
                } else {
                    $advanceRequestIds = implode(',', [0]);
                }

                $advanceInstallmentsArray = [];


                /**
                 * @var $advanceInstallmentsList HRAdvanceInstallment[]
                 */

                $advanceInstallmentsList = HRAdvanceInstallment::getInstallmentsinRange($advanceRequestIds, (int) $parm[0] ?? 0);

                foreach ($advanceInstallmentsList as $advanceInstallment) {
                    if ($advanceInstallment->manipulation_batche_id == 0 && $advanceInstallment->manipulation_batche_trans_id == 0) {
                        $advanceInstallmentsArray[] = $advanceInstallment;
                    }
                }


                $this->Smarty->assign('advanceInstallmentsNum', count($advanceInstallmentsArray));
                $this->Smarty->assign('advanceInstallments', $advanceInstallmentsArray);

            } catch (HRAdvanceRequestException $e) {
                $this->Smarty->assign('advanceInstallments', 0);
            }


            /*
             * deduct request ************************************************************************************************
             */

            $deductionsArray = [];

            try {

                $deductionsArray = HRDeductAdditionRequest::convertFromQB(
                    DB::table('hr_deductaddition')->Where(HRDeductAdditionRequest::USER_ID, $parm[0])
                        ->where(HRDeductAdditionRequest::TYPE, HRDeductAdditionRequest::SETTINGS_DEDUCTION)
                        ->where(HRDeductAdditionRequest::REQUEST_SUCCESS, 1)
                        ->whereNull(HRDeductAdditionRequest::MANIPULATION_BATCHE_ID)
                        ->whereNull(HRDeductAdditionRequest::MANIPULATION_BATCHE_TRANS_ID)
                        ->get()
                );

            } catch (HRDeductAdditionRequestException $e) {
            }

            $this->Smarty->assign('deductionsNum', count($deductionsArray));
            $this->Smarty->assign('deductions', $deductionsArray);


            /*
             * Other Deductions
             */

            /**
             * @var $EmployeeBasicJob Vacant
             */
            $EmployeeBasicJob = Vacant::getEmployeeJobs(
                $_SESSION['organization']->id,
                (int) $parm[0] ?? 0,
                Vacant::EMPLOYEE_BASIC_VACANT
            );

            if ($EmployeeBasicJob->salary_delivery == 227) {

                $this->Smarty->assign('employeeWorkDays', 30);

            } else {

                $this->Smarty->assign('employeeWorkDays', $EmployeeBasicJob->days_in_month);

            }
        }
        if (session('batch_to_edit_id')) {
            $this->Smarty->assign('batch_id', session('batch_to_edit_id'));
            $payrollBatch = PayrollBatchModel::find(session('batch_to_edit_id'));
            /** @var PayrollTrans $trans */
            $transaction = PayrollTransaction::forBatch($payrollBatch)
                ->where(PayrollTrans::USER_ID, $parm[0])
                ->first();
            $extraString = json_decode($transaction->extrastring, true);
            $transaction->deductions_list = $extraString['deductions_list'];
            $transaction->attendance_list = $extraString['attendance_list'];
            $transaction->latency_list = $extraString['latency_list'];
            $transaction->installments_list = $extraString['installments_list'];
            $transaction->employeeAbsenceDays = $extraString['employeeAbsenceDays'];

            $this->Smarty->assign('extraDetails', $transaction);
        } else {
            $extraDetails = SnsoCache::get($parm[0] . '_extraDetails');
            $this->Smarty->assign('extraDetails', $extraDetails);
            $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
        }
    }

    public function secondPayslipAdditions($parm, $post)
    {
        $additionsArray = [];
        try {
            $this->Smarty->assign('user', UserModel::find($parm[0] ?? 0));
            $additionsArray = DB::table('hr_deductaddition')->where(HRDeductAdditionRequest::USER_ID, $parm[0])
                ->where(HRDeductAdditionRequest::TYPE, HRDeductAdditionRequest::SETTINGS_ADDITION)
                ->where(HRDeductAdditionRequest::REQUEST_SUCCESS, 1)
                ->whereNull(HRDeductAdditionRequest::MANIPULATION_BATCHE_ID)
                ->whereNull(HRDeductAdditionRequest::MANIPULATION_BATCHE_TRANS_ID)
                ->get();
            $additionsArray = HRDeductAdditionRequest::convertFromQB($additionsArray);

        } catch (HRDeductAdditionRequestException $e) {
        }

        if (session('batch_to_edit_id')) {
            $this->Smarty->assign('batch_id', session('batch_to_edit_id'));
            $trans = PayrollTrans::convertFromQB(DB::table('prl_trans')->where(PayrollTrans::USER_ID, $parm[0])
                ->where(PayrollTrans::BATCH_ID, session('batch_to_edit_id'))
                ->first());
            $trans->additions_list = json_decode($trans->extrastring, true)['additions_list'];
            $this->Smarty->assign('extraDetails', $trans);
            $this->Smarty->assign('additionsNum', count($additionsArray));
            $this->Smarty->assign('additions', $additionsArray);
        } else {
            $extraDetails = SnsoCache::get($parm[0] . '_extraDetails');
            $this->Smarty->assign('extraDetails', $extraDetails);
            $this->Smarty->assign('additionsNum', count($additionsArray));
            $this->Smarty->assign('additions', $additionsArray);
            try {
                $from = Carbon::parse($_SESSION['parameters']['from_date']);
                $to = Carbon::parse($_SESSION['parameters']['to_date']);
                $batch = PayrollBatchModel::where('payroll_batch_org_id', $_SESSION['organization']->id)
                    ->where('payroll_batch_unit_id', $post['unit_id'])
                    ->where('payroll_batch_request_success', 0)
                    ->where('payroll_batch_rev_user_id', $post['rev_user_id'])
                    ->where('payroll_batch_prov_user_id', $post['prov_user_id'])
                    ->whereDate('payroll_batch_from_date', $from->toDateString())
                    ->whereDate('payroll_batch_to_date', $to->toDateString())
                    ->first();
                $this->Smarty->assign('batch_id', $batch->id);
            } catch (PayrollBatchException $e) {
            }
        }

    }

    public function addbatch($parm, $post)
    {
        switch ($parm[0]) {
            case 'monthlyBatch':
                $batchType = 865;
                break;
            case 'dailyBatch':
                $batchType = 866;
                break;
            default:
                $batchType = 865;
                break;
        }

        try {

            $this->Smarty->assign('templates', PayrollTemplate::read([
                PayrollTemplate::ORG_ID => $_SESSION['organization']->id,
                PayrollTemplate::TYPE => $batchType
            ], [0 => ['property' => PayrollTemplate::TYPE, 'sort' => 'DESC']]));

        } catch (PayrollTemplateException $e) {
            $this->Smarty->assign('templates', []);
        }

        try {
            $this->Smarty->assign('employees', Vacant::getEmployeeListByEntity($_SESSION['organization']));
        } catch (VacantException $e) {
            $this->Smarty->assign('employees', []);
        }

        $this->Smarty->assign('batchType', $parm[0]);
        $this->Smarty->assign(
            'grfExistenceNum',
            $this->DB->num_rows_as_int('wf_graph', array('wf_graph_status' => 1, 'wf_graph_opr_code' => 'payroll'))
        );

        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function addSecondbatch($parm, $post)
    {
        switch ($parm[0]) {
            case 'monthlyBatch':
                $batchType = 865;
                break;
            case 'dailyBatch':
                $batchType = 866;
                break;
            default:
                $batchType = 865;
                break;
        }

        try {

            $this->Smarty->assign('units', sh_unt::simpleReadByProperty([
                Unit::ORG_ID => $_SESSION['organization']->id,
                Unit::ACTIVATION => Unit::UNIT_IS_ACTIVATED
            ]));

        } catch (ModelException $e) {
        }

        try {
            $this->Smarty->assign('employees', Vacant::getEmployeeListByEntity($_SESSION['organization']));
        } catch (VacantException $e) {
            $this->Smarty->assign('employees', []);
        }

        $this->Smarty->assign('batchType', $parm[0]);
        $this->Smarty->assign(
            'grfExistenceNum',
            $this->DB->num_rows_as_int('wf_graph', array('wf_graph_status' => 1, 'wf_graph_opr_code' => 'payroll'))
        );

        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function editbatch($parm, $post)
    {
        switch ($parm[1]) {
            case 'monthlyBatch':
                $batchType = 865;
                break;
            case 'dailyBatch':
                $batchType = 866;
                break;
            default:
                $batchType = 865;
                break;
        }

        try {

            $row = PayrollBatch::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('row', $row);

        } catch (PayrollBatchException $e) {

            $this->Smarty->assign('row', []);

        }

        try {

            $this->Smarty->assign('templates', PayrollTemplate::read([
                PayrollTemplate::ORG_ID => $_SESSION['organization']->id,
                PayrollTemplate::TYPE => $batchType
            ], [0 => ['property' => PayrollTemplate::TYPE, 'sort' => 'DESC']]));

        } catch (PayrollTemplateException $e) {
            $this->Smarty->assign('templates', []);
        }

        $this->Smarty->assign('users_list', Vacant::getEmployeeListByEntity($_SESSION['organization']));

        $this->Smarty->assign('batchType', $parm[1]);
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function buildbatch($parm, $post)
    {
        try {

            $batch = PayrollBatch::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('row', $batch);

        } catch (PayrollBatchException $e) {

            $this->Smarty->assign('row', []);
            $this->Smarty->assign('templates', []);

        }

        if ($batch) {

            $i = 0;
            $tempArr = [];
            $tempList = explode(',', $batch->templates_based_on);
            foreach ($tempList as $temp) {
                $tempArr[$i]['tempID'] = $temp;
                $tempArr[$i]['tempEmployeeNum'] = Vacant::count([
                    Vacant::ORG_ID => $batch->org_id,
                    Vacant::PAYROLL_TEMPLATE_ID => $temp,
                    Vacant::B_ACCEPTANCE => 1,
                    Vacant::B_TYPE => 2,
                    Vacant::QUIT => 0,
                    Vacant::DELETED => 0
                ]);
                $i++;
            }

            $this->Smarty->assign('templates', $tempArr);
            $_SESSION['s_tab_2_top_tab'] = $parm[1];

        }

        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function confirmbatch($parm, $post)
    {

        try {

            $batch = PayrollBatch::readID((int) $parm[0] ?? 0);
            $num = PayrollTrans::count([
                PayrollTrans::BATCH_ID => $batch->id,
                PayrollTrans::CONFIRM_STATUS => 357
            ]);
            $this->Smarty->assign('row', $batch);
            $this->Smarty->assign('UnConfirmedTransactionNum', $num);

        } catch (PayrollBatchException $e) {

            $this->Smarty->assign('row', []);

        }

        $this->Smarty->assign('batchType', $parm[2]);
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }




    // public function printall($parm, $post, $file)
// {
//     if (!empty($parm[0]) && $parm[0] == 'save_session') {
//         $_SESSION['s_payslipPreparation_batch_id'] = $parm[1];
//     }

    //     if (isset($_SESSION['s_payslipPreparation_batch_id'])) {
//         $batch = PayrollBatchModel::find($parm[1]);
//         if (!$batch) {
//             Notification::alertMessage(Notification::ERROR, 'Batch not found');
//             redirect('payroll/show', []);
//         }

    //         $this->Smarty->assign('batch', $batch);
//         $this->Smarty->assign('tempsArray', explode(',', $batch->templates_based_on));

    //         // جلب الموظفين بناءً على نوع الرواتب
//         $employees = $this->getEmployeesBasedOnPayrollType($post);
//         $payrollEmployees = [];

    //         foreach ($employees as $employee) {
//             $userId = $employee->user_id;

    //             // جلب تفاصيل الوظيفة
//             $job = Vacant::getEmployeeJobs(241, $userId, Vacant::EMPLOYEE_BASIC_VACANT);

    //             // جلب بيانات الرواتب
//             $result = Vacant::getEmployeePayrollDetails($userId, session('batch_to_edit_id'));

    //             // جلب بيانات المعاملة من قاعدة البيانات إذا كانت الدفعة محفوظة
//             $transaction = null;
//             if (isset($_SESSION['batch_to_edit_id'])) {
//                 // استبدال forBatch باستعلام مباشر
//                 $transaction = PayrollTransaction::where('prl_trans_batch_id', $batch->payroll_batch_id)
//                     ->where(PayrollTrans::USER_ID, $userId)
//                     ->first();
//             }

    //             // إنشاء كائن الموظف
//             $payrollEmployee = new stdClass();
//             $payrollEmployee->prl_trans_user_id = $userId;
//             $payrollEmployee->prl_trans_uao_id = $employee->id;
//             $payrollEmployee->prl_trans_template_id = $employee->id;
//             $payrollEmployee->prl_trans_basic_salary = $result['salary'];
//             $payrollEmployee->prl_trans_allowances = $result['allowances'];
//             $payrollEmployee->prl_trans_deductions = $result['deductions'];
//             $payrollEmployee->prl_trans_net = $result['net'];
//             $payrollEmployee->prl_trans_net_after_additions = $result['net'];
//             $payrollEmployee->prl_trans_amount_paid = $result['paid'];
//             $payrollEmployee->prl_trans_amount_not_paid = $result['not_paid'];
//             $payrollEmployee->prl_trans_amount_comment = $result['amount_comment'];
//             $payrollEmployee->prl_trans_deductions_list = json_encode($result['extraString']->deductions_list ?? []);
//             $payrollEmployee->prl_trans_attendance_list = json_encode($result['extraString']->attendance_list ?? []);
//             $payrollEmployee->prl_trans_latency_list = json_encode($result['extraString']->latency_list ?? []);
//             $payrollEmployee->prl_trans_installments_list = json_encode($result['extraString']->installments_list ?? []);
//             $payrollEmployee->prl_trans_employeeAbsenceDays = $result['extraString']->employeeAbsenceDays ?? 0;
//             $payrollEmployee->prl_trans_extravalue = $result['extraValue'];
//             $payrollEmployee->prl_trans_confirm_status = $result['confirm_status'];
//             $payrollEmployee->prl_trans_batch_id = $batch->payroll_batch_id;
//             $payrollEmployee->prl_trans_org_id = $_SESSION['organization']->id;
//             $payrollEmployee->prl_trans_created_by = $_SESSION['user']->id;
//             $payrollEmployee->prl_trans_created_date = date('Y-m-d');
//             $payrollEmployee->prl_trans_update_status = 1;
//             $payrollEmployee->prl_trans_allowancesDetails = json_encode($result['allowancesDetails']);
//             $payrollEmployee->prl_trans_deductionDetails = json_encode($result['deductionDetails']);
//             $payrollEmployee->prl_trans_extra_addition = $transaction ? $transaction->prl_trans_extra_addition : 0;
//             $payrollEmployee->jobObject = $job->jobObject;
//             // إضافة job_name للتوافق مع القالب
//             $payrollEmployee->job_name = $job->jobObject->sh_job_name ?? 'غير محدد';
//             $payrollEmployee->att_device_num = $employee->att_device_num ?? '';

    //             // جلب الإضافات من الكاش أو قاعدة البيانات
//             $cacheKey = $userId . '_extraDetails';
//             $cachedAdditions = SnsoCache::get($cacheKey);
//             if ($cachedAdditions) {
//                 $payrollEmployee->additions_list = json_encode($cachedAdditions->additions_list ?? []);
//                 $payrollEmployee->extra_addition = $cachedAdditions->additions_list_amount ?? 0;
//             } elseif ($transaction) {
//                 $payrollEmployee->additions_list = $transaction->additions_list ?? json_encode([]);
//                 $payrollEmployee->extra_addition = $transaction->extra_addition ?? 0;
//             } else {
//                 $payrollEmployee->additions_list = json_encode($result['extraString']->additions_list ?? []);
//                 $payrollEmployee->extra_addition = $result['extra_addition'] ?? 0;
//             }

    //             // إعداد extraValueWithDetails بناءً على additions_list
//             $additions_list = json_decode($payrollEmployee->additions_list, true) ?? [];
//             $extraValueWithDetails = [];
//             if (!empty($additions_list)) {
//                 foreach ($additions_list as $deductionId) {
//                     try {
//                         $addition = HRDeductAdditionRequest::readID($deductionId);
//                         $extraValueWithDetails[] = [
//                             'table' => 'hr_deductaddition',
//                             'is_absent' => 0,
//                             'desc' => $addition->reasons ?? 'إضافة',
//                             'value' => $addition->amount,
//                             'inout' => 'in',
//                             'id' => $deductionId
//                         ];
//                     } catch (HRDeductAdditionRequestException $e) {
//                         error_log('Error fetching addition ID ' . $deductionId . ': ' . $e->getMessage());
//                     }
//                 }
//             }

    //             // إضافة الخصومات من extraValueWithDetails الأصلي
//             $originalExtraValueWithDetails = $this->calculateUserExtraValue([
//                 'employeeAbsenceDays' => $result['extraString']->employeeAbsenceDays ?? 0,
//                 'deductions_list' => $result['extraString']->deductions_list ?? [],
//                 'attendance_list' => $result['extraString']->attendance_list ?? [],
//                 'latency_list' => $result['extraString']->latency_list ?? [],
//                 'installments_list' => $result['extraString']->installments_list ?? [],
//                 'calculateExtraValue' => true
//             ], [3 => $userId]);

    //             if (is_array($originalExtraValueWithDetails)) {
//                 foreach ($originalExtraValueWithDetails as $deduction) {
//                     if ($deduction['table'] === 'hr_deductaddition' && $deduction['is_absent'] == 0 && ($deduction['inout'] === 'out' || ($deduction['inout'] === 'in' && $deduction['id'] === '17'))) {
//                         $extraValueWithDetails[] = $deduction;
//                     }
//                 }
//             }

    //             $payrollEmployee->extraValueWithDetails = $extraValueWithDetails;
//             $payrollEmployee->prl_trans_extrastring = json_encode(array_merge(
//                 (array) $result['extraString'],
//                 ['extraValueWithDetails' => $extraValueWithDetails]
//             ));

    //             // إضافة الحقول المحسوبة
//             $payrollEmployee->employee_total_salary = $result['total_salary'] - $result['extraValue'];
//             $payrollEmployee->salary = $result['salary'];
//             $payrollEmployee->deduction = $result['deductions'];
//             $payrollEmployee->allowances = $result['allowances'];
//             $payrollEmployee->extraValue = $result['extraValue'];
//             $payrollEmployee->allowancesDetails = $result['allowancesDetails'];
//             $payrollEmployee->deductionDetails = $result['deductionDetails'];

    //             // تسجيل التصحيح
//             error_log('Printall User ID: ' . $userId . ', Job Name: ' . $payrollEmployee->job_name);
//             error_log('Printall User ID: ' . $userId . ', Deductions List: ' . $payrollEmployee->prl_trans_deductions_list);
//             error_log('Printall User ID: ' . $userId . ', Additions List: ' . $payrollEmployee->additions_list);
//             error_log('Printall User ID: ' . $userId . ', Extra Value With Details: ' . json_encode($extraValueWithDetails));

    //             // إضافة الكائن إلى المصفوفة
//             $payrollEmployees[] = $payrollEmployee;
//         }

    //         // تصحيح: تسجيل عدد الموظفين
//         error_log('Printall Number of payrollEmployees: ' . count($payrollEmployees));
//         error_log('Printall Sample payrollEmployee: ' . json_encode($payrollEmployees[0] ?? []));

    //         // تعيين البيانات إلى القالب
//         $this->Smarty->assign('payrollEmployees', $payrollEmployees);
//         $this->Smarty->assign('batch', $batch);
//     } else {
//         $this->Smarty->assign('payrollEmployees', []);
//     }

    //     // إنشاء ملف PDF
//     generatePdf(true);
// }








    public function printpayslip($parm, $post, $file)
    {
        try {
            $this->Smarty->assign('row', PayrollTrans::readID((int) $parm[0] ?? 0));
        } catch (PayrollTransException $e) {

            $this->Smarty->assign('row', []);
        }

        $this->Smarty->assign('confirm_list', Setting::getList(80));
        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));
    }

    public function export($parm, $post)
    {

        $emp_arr = [];
        try {

            $emp_list = PayrollTrans::read([
                PayrollTrans::BATCH_ID => (int) $parm[0],
                PayrollTrans::TEMPLATE_ID => (int) $parm[1],
                PayrollTrans::CONFIRM_STATUS => 356
            ]);

        } catch (PayrollTransException $e) {
        }

        if ($emp_list) {

            $i = 0;
            foreach ($emp_list as $emp) {

                try {
                    $emp_arr[$i]['user_id'] = (User::readID((int) $emp->user_id))->full_name;
                } catch (UserException $e) {

                }

                try {

                    $emp_arr[$i]['uao_id'] = (Vacant::readID((int) $emp->uao_id))->jobObject->sh_job_name;

                } catch (VacantException $e) {

                }

                $emp_arr[$i]['basic_salary'] = number_format($emp->basic_salary, 2, '.', ',');
                $emp_arr[$i]['allowances'] = number_format($emp->allowances, 2, '.', ',');
                $emp_arr[$i]['deductions'] = number_format($emp->deductions, 2, '.', ',');
                $emp_arr[$i]['extravalue'] = number_format($emp->extravalue, 2, '.', ',');
                $emp_arr[$i]['net_after_additions'] = number_format($emp->net_after_additions, 2, '.', ',');
                $i++;
            }
            $this->Smarty->assign('emp_list', $emp_arr);

        }


        try {
            $columns_names = $this->DocumentProcessor->prepare_parameters(
                "all",
                [
                    "ttt" => "الموظف",
                    "zzz" => "الوظيفة",
                    "ccc" => "الأساسي",
                    "aaa" => "الإضافات",
                    "xxx" => "الخصومات",
                    "jjj" => "الخصومات الإضافية",
                    "eee" => "الصافي",
                ],
                "",
                $fileName = 'الموارد_البشرية-المرتبات-مسير_الرواتب',
                "",
                $emp_arr
            );

            try {
                $this->DocumentProcessor->create_csv_document($columns_names, "الموارد_البشرية-المرتبات-مسير_الرواتب");
            } catch (ErrorException $e) {

            }

        } catch (PDOException $e) {

        }

    }

    public function dayCostStructure($parm, $post)
    {
        if (isset($_SESSION['s_prl_templates_id'])) {

            try {
                $this->Smarty->assign('template', PayrollTemplate::readID((int) $parm[0] ?? 0));
            } catch (PayrollTemplateException $e) {
                $this->Smarty->assign('template', []);
            }

            try {

                $this->Smarty->assign('rules', PayrollRule::read([
                    PayrollRule::ORG_ID => $_SESSION['organization']->id,
                    PayrollRule::TYPE => 1,
                    PayrollRule::TEMPLATE_ID => (int) $parm[0] ?? 0
                ], [0 => ['property' => PayrollRule::ORDER, 'sort' => 'ASC']]));

            } catch (PayrollRuleException $e) {
                $this->Smarty->assign('rules', []);
            }
        }

    }

    public function ConfirmAll($parm, $post)
    {
        try {

            $i = 0;
            $batch = PayrollBatch::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('batch', $batch);

        } catch (PayrollBatchException $e) {

        }

        if ($batch) {

            $tempArr = [];
            $tempList = explode(',', $batch->templates_based_on);
            foreach ($tempList as $temp) {
                $tempArr[$i]['tempID'] = $temp;
                $tempArr[$i]['tempEmployeeNum'] = Vacant::count([
                    Vacant::ORG_ID => $batch->org_id,
                    Vacant::PAYROLL_TEMPLATE_ID => $temp,
                    Vacant::B_ACCEPTANCE => 1,
                    Vacant::B_TYPE => 2,
                    Vacant::QUIT => 0,
                    Vacant::DELETED => 0
                ]);
                $i++;
            }
            $this->Smarty->assign('templates', $tempArr);

        }

        $_SESSION['s_tab_2_top_tab'] = $parm[1];
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function newConfirmAll($parm, $post)
    {
        try {
            $row = new stdClass();
            $row->type = session('payroll_type');
            $row->payroll_batch_unit_id = json_encode(session('s_unit_id') ?? session('unit_id'));
            $row->payroll_batch_templates_ids = json_encode(session('payroll_batch_templates_ids'));
            $this->Smarty->assign('name', session('payroll_name'));
            $this->Smarty->assign('row', $row);
            $this->Smarty->assign('type', session('payroll_type'));
            $this->Smarty->assign('from', $_SESSION['parameters']['from_date']);
            $this->Smarty->assign('to', $_SESSION['parameters']['to_date']);
            $this->Smarty->assign('units', session('unit_id'));
            $this->Smarty->assign('templates', session('templates_id'));
            $this->Smarty->assign('reveiwing_status_comment', SnsoCache::get('reveiwing_status_comment'));


        } catch (PayrollBatchException $e) {

        }
        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
    }

    public function templateEmployees($parm, $post)
    {
        try {

            $template = PayrollTemplate::readID((int) $parm[0]);

        } catch (PayrollTemplateException $e) {
            $template = [];
        }


        $employees = Vacant::getEmployeesOfSalaryDeliveryType($_SESSION['organization']->id, 227);

        $this->Smarty->assign('template', $template);
        $this->Smarty->assign('employees', $employees);
    }

    public function unExcludeEmployeeFromPayroll($parm, $post)
    {
        try {
            $transaction = PayrollTrans::readID((int) $parm[1]);
            $transaction->confirm_status = PayrollTrans::PAYSLIP_UN_CONFIRMED;
            $transaction->save();
            redirect('payroll/secondPayrollsheetbrowse', [$parm[0]]);
        } catch (PayrollTransException $e) {
        }

    }
}