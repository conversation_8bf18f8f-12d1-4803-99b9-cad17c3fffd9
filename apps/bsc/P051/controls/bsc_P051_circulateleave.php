<?php

/**
 * Created by PhpStorm.
 * User: altaif
 * Date: 1/14/19
 * Time: 11:55 AM
 */
class bsc_P051_circulateleave extends Controller
{
    public function show($parm, $post)
    {

        switch ($parm[0]) {

            case 'insert':

                if ($_SESSION['s_circulateleave_token'] == $parm[1]) {

                    try {

                        $schedual = new LeaveScheduled();
                        $schedual->bindProperties($post);
                        $schedual->org_id = $_SESSION['organization']->id;
                        $schedual->created_by = $_SESSION['user']->id;
                        $schedual->start_date = $this->Date->get_date('ad', $post['start_date']);
                        $schedual->circulate_status = 0;
                        $dateArray = LeaveScheduled::circulateLeaveEndDate($_SESSION['organization']->id, $schedual->start_date, $post['duration']);
                        $schedual->end_date = $dateArray['date'];
                        $schedual->created_date = date('Y-m-d');
                        $schedual->save();

                        Notification::createdAlert();

                    } catch (LeaveScheduledException $e) {

                    }

                    $_SESSION['s_circulateleave_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'update':

                if ($_SESSION['s_circulateleave_token'] == $parm[1]) {

                    try {

                        $schedual = LeaveScheduled::readID($parm[2] ?? 0);
                        $schedual->bindProperties($post);
                        $schedual->start_date = $this->Date->get_date('ad', $post['start_date']);
                        $schedual->circulate_status = 0;
                        $dateArray = LeaveScheduled::circulateLeaveEndDate($_SESSION['organization']->id, $schedual->start_date, $post['duration']);
                        $schedual->end_date = $dateArray['date'];
                        $schedual->save();

                        Notification::updatedAlert();

                    } catch (LeaveScheduledException $e) {
                    }

                    $_SESSION['s_circulateleave_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'delete':

                if ($_SESSION['s_circulateleave_token'] == $parm[1]) {

                    try {
                        $schedual = LeaveScheduled::readID($parm[2] ?? 0);
                        $schedual->delete();

                        Notification::deletedAlert();

                    } catch (LeaveScheduledException $E) {
                    }

                    $_SESSION['s_circulateleave_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'circulate':

                if ($_SESSION['s_circulateleave_token'] == $parm[1]) {

                    LeaveScheduled::circulateTabulated($parm[3], $parm[2]);

                    Notification::updatedAlert();

                    $_SESSION['s_circulateleave_token'] = md5(rand(0000, 9999));

                }

                break;

        }




        try {

            $this->Smarty->assign('hr_schedholid_list', LeaveScheduled::read([LeaveScheduled::ORG_ID => $_SESSION['organization']->id],
                [0 => ['property' => LeaveScheduled::ID,
                    'sort' => 'DESC']]));

        } catch
        (LeaveScheduledException $e) {

            $this->Smarty->assign('hr_schedholid_list', []);

        }
    }

    public function scheduledleave($parm, $post)
    {

        try {

            $this->Smarty->assign('row', LeaveScheduled::readID((int)$parm[0] ?? 0));

        } catch (LeaveScheduledException $e) {

        }

        $this->Smarty->assign('type', $parm[1] ?? 0);
        $_SESSION['s_circulateleave_token'] = md5(rand(0000, 9999));

    }

    /*
    * Tabulated
    */
    public function add($parm, $post)
    {

        try {

            $this->Smarty->assign('hr_levst_schedholid_list', LeaveType::read([
                LeaveType::ORG_ID => $_SESSION['organization']->id,
                LeaveType::LEAV_TYPE => 641,
                LeaveType::ACTIVATION => 23
            ], [
                0 => [
                    'property' => LeaveType::ID,
                    'sort' => 'DESC'
                ]
            ]));

        } catch (LeaveTypeException $e) {

        }

        $this->Smarty->assign('selected_tab', "tab2");
        $_SESSION['s_circulateleave_token'] = md5(rand(0000, 9999));

    }

    public function edit($parm, $post)
    {

        try {

            $this->Smarty->assign('row', LeaveScheduled::readID((int)$parm[0] ?? 0));

        } catch (LeaveScheduledException $e) {

        }

        try {

            $this->Smarty->assign('hr_levst_schedholid_list', LeaveType::read([
                LeaveType::ORG_ID => $_SESSION['organization']->id,
                LeaveType::LEAV_TYPE => 641,
                LeaveType::ACTIVATION => 23
            ], [
                0 => [
                    'property' => LeaveType::ID,
                    'sort' => 'DESC'
                ]
            ]));

        } catch (LeaveTypeException $e) {

        }

        $this->Smarty->assign('selected_tab', "tab2");
        $_SESSION['s_circulateleave_token'] = md5(rand(0000, 9999));

    }

    public function confirm($parm, $post)
    {

        try {

            $this->Smarty->assign('row', LeaveScheduled::readID((int)$parm[0] ?? 0));

        } catch (LeaveScheduledException $e) {

        }

        $_SESSION['s_circulateleave_token'] = md5(rand(0000, 9999));

    }
}