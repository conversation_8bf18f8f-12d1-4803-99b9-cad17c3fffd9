<?php

use Carbon\Carbon;

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class bsc_P051_HrAttendanceDepartureRecords extends Controller
{

    public function show($parm, $post)
    {

        $units = DB::table('sh_unt')
            ->where(Unit::ORG_ID, $_SESSION['organization']->id)
            ->get();

        $doams = DB::table('hr_doam')
            ->where(Doam::ORG_ID, $_SESSION['organization']->id)
            ->get();

        $fingerprintDevices = DB::table('fp_dev')
            ->where(FingerPrintDevice::ORG_ID, $_SESSION['organization']->id)
            ->get();

        $this->Smarty->assign('units', $units);
        $this->Smarty->assign('doams', $doams);
        $this->Smarty->assign('fingerprintDevices', $fingerprintDevices);
    }

    public function employeesList($parm, $post)
    {

        $employees = DB::table('sh_uao')
            ->join('sh_user', 'sh_user.sh_user_id', '=', 'sh_uao_user_id')
            ->join('sh_job', 'sh_job.sh_job_id', '=', 'sh_uao_job_id')
            ->join('sh_unt', 'sh_unt.sh_unt_id', '=', 'sh_uao_job_unt_id')
            ->join('fp_dev', 'fp_dev.fp_dev_id', '=', 'sh_uao_att_device_id')
            ->join('hr_doam', 'hr_doam.hr_doam_id', '=', 'sh_uao_att_doam_id')
            ->where('sh_uao_org_id', $_SESSION['organization']->id)
            ->when($parm[0] == 'UNIT', function ($query) use ($parm) {
                return $query->where('sh_unt_id', (int)$parm[1]);
            })
            ->when($parm[0] == 'SHIFT', function ($query) use ($parm) {
                return $query->where('hr_doam_id', (int)$parm[1]);
            })
            ->when($parm[0] == 'FINGERPRINT', function ($query) use ($parm) {
                return $query->where('fp_dev_id', (int)$parm[1]);
            })
            ->where('sh_uao_user_id', '>=', 0)
            ->where('sh_uao_b_acceptance', 1)
            ->where('sh_uao_b_type', 2)
            ->where('sh_uao_basic', 1)
            ->where('sh_uao_quit', 0)
            ->where('sh_uao_deleted', 0)
            ->get();

        $modalTitle = null;
        $lookup = [
            "UNIT" => "sh_unt_name",
            'SHIFT' => "hr_doam_name",
            "FINGERPRINT" => "fp_dev_name"
        ];

        $modalTitle = $employees[0]->{$lookup[$parm[0]]};

        $this->Smarty->assign('modalTitle', $modalTitle);

        if ($employees) {
            $this->Smarty->assign('employees', $employees);
        }

    }

    public function records($parm, $post)
    {

        switch ($parm[0]) {

            case 'menu':

                $_SESSION['report_parms']['doam_id'] = $parm[1];
                $_SESSION['report_parms']['emp_id'] = $parm[2];

                $_SESSION['intervals'] = null;

                break;

            case 'save_session':

                $_SESSION['intervals'] = $post;

                break;
        }

        $data = $this->prepareReportData(
            $_SESSION['report_parms']['doam_id'],
            $_SESSION['report_parms']['emp_id'],
            $_SESSION['intervals']['from_date'],
            $_SESSION['intervals']['to_date']
        );

        $this->Smarty->assign('fingerprintRecords', $data['fingerprintRecords']);
        $this->Smarty->assign('employee', $data['employee']);

    }

    /**
     * @param $doamID
     * @param $employeeID
     * @param null $fromDate
     * @param null $toDate
     * @return array
     */
    public function prepareReportData($doamID, $employeeID, $fromDate = null, $toDate = null)
    {
        $fromDate = $fromDate == null ? Carbon::now()->startOfMonth()->toDateString() : $fromDate;
        $toDate = $toDate == null ? Carbon::now()->endOfMonth()->toDateString() : $toDate;

        try {
            $doam = Doam::readID((int)$doamID);
        } catch (DoamException $e) {
            $doam = null;
        }

        try {
            $employee = Vacant::readID((int)$employeeID);
        } catch (VacantException $e) {
            $employee = null;
        }

        if ($doam and $employee) {

            $fingerprintRecords = FingerPrintDay::convertFromQB(DB::table('fp_day')
                ->where('fp_day_org_id', $_SESSION['organization']->id)
                ->where('fp_day_doam_id', $doam->id)
                ->where('fp_day_user_id', $employee->user_id)
                ->whereBetween("fp_day_date", [$fromDate, $toDate])
                ->orderBy('fp_day_date')
                ->get());

            $data = [
                'employee' => $employee,
                'fingerprintRecords' => $fingerprintRecords ?? null,
            ];

        }

        return $data ?? [];

    }

    public function print($parm, $post)
    {

        $this->records($parm, $post);

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));

    }

}