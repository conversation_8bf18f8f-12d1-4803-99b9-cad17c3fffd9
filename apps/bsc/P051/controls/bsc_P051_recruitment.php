<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bsc_P051_recruitment extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {
            case 'menu':
                $_SESSION['s_recruitment_unit_id'] = null;
                break;

            case 'save_session':
                $_SESSION['s_recruitment_unit_id'] = $post['unitId'];
                break;

        }

        try {
            $this->Smarty->assign('units', Unit::toggleNestedRetrieve(false)::read([Unit::ORG_ID => $_SESSION['organization']->id]));
        } catch (UnitException $e) {
            $this->Smarty->assign('units', []);
        }

        if($_SESSION['s_recruitment_unit_id']){

            try{

                $this->Smarty->assign('vacancies', Vacant::read([
                    Vacant::ORG_ID => $_SESSION['organization']->id,
                    Vacant::JOB_UNT_ID => $_SESSION['s_recruitment_unit_id'],
                    Vacant::JOB_ACTIVATION => 23,
                    Vacant::QUIT => 0,
                    Vacant::DELETED => 0
                ]));

            }catch (VacantException $e){

                $this->Smarty->assign('vacancies', []);

            }

            try{
                $this->Smarty->assign('currentUnit',sh_unt::readByID((int)$_SESSION['s_recruitment_unit_id']));
            }catch (UnitException $e){
                $this->Smarty->assign('currentUnit', []);
            }

        }

    }

    public function recruit($parm, $post)
    {
        switch ($parm[0]) {

            case 'save_session':

                $_SESSION['s_vacant_id'] = $parm[1];
                $_SESSION['s_main_tab_activation'] = 'tab1';
                $_SESSION['s_user_active_tabe'] = 'employee';

                break;

            case 'recruit':

                if ($_SESSION['s_recruitment_token'] === $parm[1]) {

                    if(Vacant::recruitVacant((int) $parm[2] ?? 0, (int) $parm[3] ?? 0)){

                        try{
                            $vacantRecruited = Vacant::readID((int)$parm[3]);
                        }catch (VacantException $e){

                        }

                        if($vacantRecruited){
                            try {
                                Notification::sendNotification(
                                    848,
                                    0,
                                    'sh_uao',
                                    $vacantRecruited->id,
                                    $_SESSION['user']->id,
                                    $vacantRecruited->user_id,
                                    1008,
                                    []);
                            }catch (NotificationException $e){

                            }
                            $templateVars = [
                                'username'=> $vacantRecruited->userObject->full_name,
                                'jobName'=> $vacantRecruited->jobObject->sh_job_name,
                                'jobCode'=> $vacantRecruited->job_code,
                                'recruitmentDate'=> $vacantRecruited->job_date_of_appointment,
                            ];
                            event('event.EmployeeGetRecruited',[$vacantRecruited->job_unt_id,$vacantRecruited->jobObject->sh_job_name,$vacantRecruited->userObject->full_name]);
                            $template = new RecruitUserTemplate($templateVars);
                            try{
                                $mailer = new UserMailer(User::readID((int)$vacantRecruited->user_id), $template);
                                $mailer->send();
                            }catch (UserException $e){

                            }
                        }
                    }
                }
                break;
            case 'recruitRequest':

                if ($_SESSION['s_recruitment_token'] === $parm[1]) {

                    try{

                        $recruit = new HRRecruitment();
                        $recruit->bindProperties($post);
                        $recruit->org_id = $_SESSION['organization']->id;
                        $recruit->user_id = $parm[2];
                        $recruit->uao_id = $parm[3];
                        $recruit->ask_date = date('Y-m-d');
                        $recruit->ask_comment = $post['ask_comment'];
                        $recruit->replay_is = 49;
                        $recruit->created_by = $_SESSION['user']->id;
                        $recruit->save();

                        try {

                            Notification::sendNotification(
                                848,
                                0,
                                'sh_belong',
                                $recruit->id,
                                $recruit->created_by,
                                $recruit->user_id,
                                1009,
                                []);

                        } catch (NotificationException $e) {

                        }

                        $this->Smarty->assign('requestSentStatus','Success');
                    }catch (HRRecruitmentException $e){

                    }

                }

                break;

            case 'search':


                try{
                    $this->Smarty->assign('searchResult', User::search(User::SEARCH_BY_USER_NAME,$post['user_show_name']));
                }catch (UserException $e){

                }

                $_SESSION['s_main_tab_activation'] = 'tab2';
                break;
        }

        try{
            $this->Smarty->assign('vacant',Vacant::readID((int) $_SESSION['s_vacant_id'] ?? 0));
        }catch (VacantException $e){
            $this->Smarty->assign('vacant',[]);
        }

//        try{
//            $this->Smarty->assign('employees', Vacant::getEmployeeList($_SESSION['organization']));
//        }catch (VacantException $e){
//            $this->Smarty->assign('employees',[]);
//        }
//
//        try{
//            $this->Smarty->assign('classifications', UserClass::read([UserClass::ORG_ID => $_SESSION['organization']->id,]));
//        }catch (UserClassException $e){
//            $this->Smarty->assign('classifications',[]);
//        }
//
//        try{
//            $this->Smarty->assign('unclassifiedUsers', User::getUnClassifiedUsers($_SESSION['organization']));
//        }catch (UserClassException $e){
//            $this->Smarty->assign('unclassifiedUsers',[]);
//        }

        $_SESSION['s_recruitment_token'] = md5(rand(0000,9999));

    }

    public function confirm($parm, $post)
    {
        try{
            $this->Smarty->assign('user', User::readID((int) $parm[0] ?? 0));
        }catch (UserException $e){
            $this->Smarty->assign('user',[]);
        }

        try{
            $this->Smarty->assign('vacant', Vacant::readID((int) $parm[1] ?? 0));
        }catch (VacantException $e){
            $this->Smarty->assign('vacant',[]);
        }

        $this->Smarty->assign('backTab',$parm[2].'/'.$parm[3]);
        $_SESSION['s_recruitment_token'] = md5(rand(0000,9999));
    }

    public function browseRecruitRequestSent($parm, $post)
    {
        $user = [];
        $vacant = [];

        try{
            $user = User::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('user', $user);
        }catch (UserException $e){
            $this->Smarty->assign('user',[]);
        }

        try{
            $vacant = Vacant::readID((int) $parm[1] ?? 0);
            $this->Smarty->assign('vacant', $vacant);
        }catch (VacantException $e){
            $this->Smarty->assign('vacant',[]);
        }

        try{

            $this->Smarty->assign('requests', HRRecruitment::read([
                HRRecruitment::ORG_ID => $_SESSION['organization']->id,
                HRRecruitment::UAO_ID => $vacant->id,
                HRRecruitment::USER_ID => $user->id
            ]));
        }catch (HRRecruitmentException $e) {
            $this->Smarty->assign('requests', []);
        }

        $this->Smarty->assign('backTab',$parm[2].'/'.$parm[3]);
        $_SESSION['s_recruitment_token'] = md5(rand(0000,9999));
    }

    public function sendRecruitmentRequest($parm, $post)
    {
        try{
            $this->Smarty->assign('user', User::readID((int) $parm[0] ?? 0));
        }catch (UserException $e){
            $this->Smarty->assign('user',[]);
        }

        try{
            $this->Smarty->assign('vacant', Vacant::readID((int) $parm[1] ?? 0));
        }catch (VacantException $e){
            $this->Smarty->assign('vacant',[]);
        }

        $this->Smarty->assign('backTab',$parm[2].'/'.$parm[3]);
        $_SESSION['s_recruitment_token'] = md5(rand(0000,9999));
    }

    public function answer($parm, $post)
    {

        try{

            $vacant = Vacant::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('vacant', $vacant);

            try{

                $this->Smarty->assign('recruitRequest', HRRecruitment::read([
                    HRRecruitment::ORG_ID => $_SESSION['organization']->id,
                    HRRecruitment::UAO_ID => $vacant->id,
                    HRRecruitment::USER_ID => $vacant->userObject->id
                ],[0=>['property'=>HRRecruitment::ID,'sort'=>'DESC']],[1])[0]);

            }catch (HRRecruitmentException $e){
                $this->Smarty->assign('recruitRequest',[]);
            }

        }catch (VacantException $e){
            $this->Smarty->assign('vacant',[]);
        }

    }

    public function recruitmentComplete($parm,$post){

        try{

            $notification = Notification::readID((int) $parm[0] ?? 0);
            $notification->status = 0;
            $notification->save();

            try{

                $request = HRRecruitment::readID($notification->row_id);
                $this->Smarty->assign('request',$request);

                try{

                    $this->Smarty->assign('vacant',Vacant::readID($request->uao_id));

                }catch (VacantException $e){
                    $this->Smarty->assign('vacant',[]);
                }

            }catch (HRRecruitmentException $e){

            }

        }catch (NotificationException $e){
        }

    }

    public function dismissComplete($parm,$post){

        try{

            $notification = Notification::readID((int) $parm[0] ?? 0);
            $notification->status = 0;
            $notification->save();

            try{

                $dismiss = Dismiss::readID($notification->row_id);
                $this->Smarty->assign('request',$dismiss);

                try{

                    $this->Smarty->assign('vacant',Vacant::readID($dismiss->uao_id));

                }catch (VacantException $e){
                    $this->Smarty->assign('vacant',[]);
                }

            }catch (DismissException $e){

            }

        }catch (NotificationException $e){

        }

    }
}
