<?php

/**
 * Created by PhpStorm.
 * User: altaif
 * Date: 1/14/19
 * Time: 11:40 AM
 */
class bsc_P051_attendance extends Controller
{

    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case 'insert':

                if ($_SESSION['s_attendance_token'] == $parm[1]) {

                    $attendance = new HRAttendanceRequest();
                    $attendance->bindProperties($post);
                    $attendance->org_id = $_SESSION['organization']->id;
                    $attendance->user_id = $post['user_id'];
                    $attendance->start_intervals = implode(',', $post['wardiah_ids1']);
                    $attendance->end_intervals = implode(',', $post['wardiah_ids2']);
                    $attendance->start_date = $this->Date->get_date('ad', $post['start_date']);
                    $attendance->end_date = $this->Date->get_date('ad', $post['end_date']);
                    $attendance->manipulation_type = 0;
                    $attendance->created_by = $_SESSION['user']->id;
                    $attendance->created_date = date('Y-m-d');

                    if ($attendance->record_type == HRAttendanceRequest::ATTENDANCE_UPON_DOAM_RECORDS) {
                        $attendance->detail = HRAttendanceRequest::AbsenceByDoamDetail($_SESSION['organization']->id, $attendance->user_id, $attendance->start_date, $attendance->end_date);
                        $attendance->duration = HRAttendanceRequest::calculateAbsenceDuration($attendance->detail);

                    }

                    $attendanceRequestOverlap = (bool)DB::table(hr_attendance::class)->where(HRAttendanceRequest::USER_ID, $attendance->user_id)
                        ->whereBetween(HRAttendanceRequest::START_DATE, [$attendance->start_date, $attendance->end_date])
                        ->whereBetween(HRAttendanceRequest::END_DATE, [$attendance->start_date, $attendance->end_date])
                        ->count();
//                    return $attendanceRequestOverlap;
                    if (!$attendanceRequestOverlap) {
                        if ($attendance->save()) {

                            if ($attendance->record_type == HRAttendanceRequest::ATTENDANCE_UPON_FINGER_PRINTS_RECORDS) {

                                try {
                                    HRAttendanceRequest::AbsenceByFingerPrintDetail(hr_attendance::readByID((int)$attendance->id));
                                } catch (ModelException $e) {

                                }
                            }

                            Notification::createdAlert();
                        }
                    } else {
                        Notification::alertMessage(Notification::WARNING, 'gnr_absence_record_is_overlaping');
                    }
                    $_SESSION['s_attendance_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'update':

                if ($_SESSION['s_attendance_token'] == $parm[1]) {

                    try {

                        $attendance = HRAttendanceRequest::readID((int)$parm[2] ?? 0);
                        $attendance->bindProperties($post);
                        $attendance->start_intervals = implode(',', $post['wardiah_ids1']);
                        $attendance->end_intervals = implode(',', $post['wardiah_ids2']);
                        $attendance->start_date = $this->Date->get_date('ad', $post['start_date']);
                        $attendance->end_date = $this->Date->get_date('ad', $post['end_date']);

                        switch ($attendance->record_type) {

                            case HRAttendanceRequest::ATTENDANCE_UPON_DOAM_RECORDS:

                                $attendance->detail = HRAttendanceRequest::AbsenceByDoamDetail($_SESSION['organization']->id, $attendance->user_id, $attendance->start_date, $attendance->end_date);
                                $attendance->duration = HRAttendanceRequest::calculateAbsenceDuration($attendance->detail);

                                break;

                            case HRAttendanceRequest::ATTENDANCE_UPON_FINGER_PRINTS_RECORDS:
                                HRAttendanceRequest::AbsenceByFingerPrintDetail(hr_attendance::readByID((int)$attendance->id));

                                break;

                        }

                        if ($attendance->save()) {

                            Notification::updatedAlert();

                        }

                    } catch
                    (HRAttendanceRequestException $e) {

                    }

                    $_SESSION['s_attendance_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'delete':

                if ($_SESSION['s_attendance_token'] == $parm[1]) {

                    try {

                        HRAttendanceRequest::readID((int)$parm[2] ?? 0)->delete();
                        Notification::deletedAlert();

                    } catch (HRAttendanceRequestException $e) {

                    }

                    $_SESSION['s_attendance_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'structure':
                if ($_SESSION['s_attendance_token'] == $parm[1]) {

                    HRAttendanceRequest::updateAbsenceStructure($post, $parm[2]);

                    Notification::updatedAlert();

                    $_SESSION['s_attendance_token'] = md5(rand(0000, 9999));
                }

                break;

            case 'archive':
                if ($_SESSION['s_attendance_token'] == $parm[1]) {

                    try {

                        $attendance = HRAttendanceRequest::readID((int)$parm[2] ?? 0);
                        Notification::updatedAlert();

                    } catch (HRAttendanceRequestException $e) {

                    }
                    try {
                        $request = Request::readID((int)$attendance->requestEntity->wf_request_id);

                        $request->send_status = Request::REQUEST_IS_ARCHIVED;

                        $request->update();
                    } catch (RequestException $e) {
                    }

                    $_SESSION['s_attendance_token'] = md5(rand(0000, 9999));
                }

                break;
        }
        $_SESSION['s_personnel_tab'] = "absence";
        // attendance
        $attendances = \Models\Request::with([
            'requestable:hr_attendance_id,hr_attendance_user_id,hr_attendance_duration,hr_attendance_start_date,hr_attendance_end_date',
            'requestable.user:sh_user_full_name,sh_user_id'

        ])
            ->where(Request::TABLE_NAME , hr_attendance::class)
            ->whereIn(Request::SEND_STATUS , [
                Request::REQUEST_IS_NOT_SEND,
                Request::REQUEST_IS_UNDER_PROCESS,
                Request::REQUEST_REACH_END
            ])->orderBy(Request::ID, 'desc')
            ->paginated(15);


        $this->Smarty->assign('attendanceRequests', $attendances);
//        $this->Smarty->assign('attendanceRequests', Request::getRequests($_SESSION['organization'], hr_attendance::class, [
//            Request::REQUEST_IS_NOT_SEND,
//            Request::REQUEST_IS_UNDER_PROCESS,
//            Request::REQUEST_REACH_END]));

    }

    public function add($parm, $post)
    {

        $this->Smarty->assign('employees', Vacant::getEmployeeListByEntityWithBasicVacancies($_SESSION['organization'], Vacant::EMPLOYEE_ALL_VACANT));

        try {

            $this->Smarty->assign('grfExistenceNum', Graph::count([
                Graph::STATUS => 1,
                Graph::OPR_CODE => 'attendance'
            ]));

        } catch (GraphException $e) {

            $this->Smarty->assign('grfExistenceNum', 0);

        }

        $this->Smarty->assign('absenceRecordTypes', Setting::getList(250));

        $_SESSION['s_personnel_tab'] = "absence";
        $_SESSION['s_attendance_token'] = md5(rand(0000, 9999));
    }

    public function edit($parm, $post)
    {

        try {
            $attendance = HRAttendanceRequest::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('row', $attendance);
        } catch (HRAttendanceRequestException $e) {
            $this->Smarty->assign('row', []);
        }

        $this->Smarty->assign('employees', Vacant::getEmployeeListByEntityWithBasicVacancies($_SESSION['organization'], Vacant::EMPLOYEE_ALL_VACANT));

        $this->Smarty->assign('absenceRecordTypes', Setting::getList(250));
        $_SESSION['s_personnel_tab'] = "absence";
        $_SESSION['s_attendance_token'] = md5(rand(0000, 9999));
    }

    public function confirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', HRAttendanceRequest::readID((int)$parm[0] ?? 0));
        } catch (HRAttendanceRequestException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_attendance_token'] = md5(rand(0000, 9999));
    }

    public function manipulate($parm, $post)
    {
        switch ($parm[0]) {

            case 'save_session':
                $_SESSION['s_attendance_row_id'] = $parm[1];
                break;

            case 'update':

                if ($_SESSION['s_attendance_token'] == $parm[1]) {

                    $attendance = \Models\HumanResource\Attendance::find((int)$_SESSION['s_attendance_row_id']);

//                    try {
//                        $attendance = hr_attendance::readByID((int)$_SESSION['s_attendance_row_id'] ?? 0);
//                    } catch (ModelException $e) {
//                        $attendance = null;
//                    }

//                    if ($attendance) {

                        switch ($parm[2]) {

                            case Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_LEAVE_BALANCE:
                                $allwedLeaveRow = \Models\HumanResource\LeaveAllowed::find((int)$parm[3]);
                                $allwedLeaveRow->load('leave_type');
//                                try {
//                                    $allwedLeaveRow = LeaveAllowed::readID((int)$parm[3]);
//                                } catch (LeaveAllowedException $e) {
//                                    $allwedLeaveRow = null;
//                                }

//                                if ($allwedLeaveRow) {

//                                    try {

                                        $attendance->manipulation_type = Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_LEAVE_BALANCE;
                                        $attendance->manipulation_batche_id = 0;
                                        $attendance->manipulation_trans_id = 0;
                                        $attendance->manipulation_leave_id = $allwedLeaveRow->leave_type->id;
                                        $attendance->save();

                                        $leaveTransaction = \Models\HumanResource\LeaveTransaction::where(LeaveTransaction::USER_ID , $attendance->user_id)
                                            ->where(LeaveTransaction::LEAVE_ID , $allwedLeaveRow->leave_type->id)
                                            ->where(LeaveTransaction::TYPE , LeaveTransaction::LEAVE_TRANSACTION_DEDUCT_UPON_ABSENCE)
                                            ->where(LeaveTransaction::TABLE_NAME , Request::REQUEST_TYPE_HR_ATTENDANCE)
                                            ->where(LeaveTransaction::ROW_ID , Request::REQUEST_TYPE_HR_ATTENDANCE)
                                            ->where(LeaveTransaction::ROW_ID , $attendance->id)
                                            ->update([
                                                LeaveTransaction::CREDIT => $attendance->duration
                                            ]);
//                                        $leaveTransaction = LeaveTransaction::read([
//                                            LeaveTransaction::ORG_ID => $organizationId,
//                                            LeaveTransaction::USER_ID => $userId,
//                                            LeaveTransaction::LEAVE_ID => $leave->id,
//                                            LeaveTransaction::TYPE => $type,
//                                            LeaveTransaction::TABLE_NAME => $tableName,
//                                            LeaveTransaction::ROW_ID => $rowId
//                                        ])[0];

//                                        LeaveTransaction::saveLeaveCreditTransaction(
//                                            $_SESSION['organization']->id,
//                                            $attendance->hr_attendance_user_id,
//                                            $allwedLeaveRow->leaveType,
//                                            LeaveTransaction::LEAVE_TRANSACTION_DEDUCT_UPON_ABSENCE,
//                                            LeaveCreditRequest::LEAVE_CREDIT_DEDUCTION,
//                                            $attendance->hr_attendance_duration,
//                                            Request::REQUEST_TYPE_HR_ATTENDANCE,
//                                            $attendance->hr_attendance_id,
//                                            $_SESSION['user']->id
//                                        );

                                        Notification::alertMessage(Notification::SUCCESS, Notification::MESSAGE_UPDATED_SUCCESSFULLY);

//                                    } catch (ModelException $e) {
//                                    }

//                                }

                                break;

                            case Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_SALARY:

//                                try {

                                    $attendance->manipulation_type = Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_SALARY;
                                    $attendance->manipulation_leave_id = 0;
                                    $attendance->manipulation_batche_id = 0;
                                    $attendance->manipulation_trans_id = 0;
                                    $attendance->save();

                                    Notification::alertMessage(Notification::SUCCESS, Notification::MESSAGE_UPDATED_SUCCESSFULLY);

                                    $leaveTransaction = \Models\HumanResource\LeaveTransaction::where(LeaveTransaction::USER_ID , $attendance->user_id)
                                        ->where(LeaveTransaction::TYPE , LeaveTransaction::LEAVE_TRANSACTION_DEDUCT_UPON_ABSENCE)
                                        ->where(LeaveTransaction::TABLE_NAME , Request::REQUEST_TYPE_HR_ATTENDANCE)
                                        ->where(LeaveTransaction::ROW_ID , $attendance->id)
                                        ->delete();

//                                    $existTransaction = LeaveTransaction::read([
//                                        LeaveTransaction::ORG_ID => $organizationId,
//                                        LeaveTransaction::USER_ID => $userId,
//                                        LeaveTransaction::TYPE => $type,
//                                        LeaveTransaction::TABLE_NAME => $tableName,
//                                        LeaveTransaction::ROW_ID => $rowId
//                                    ]);
//
//
//                                    LeaveTransaction::removeLeaveCreditTransaction(
//                                        $_SESSION['organization']->id,
//                                        $attendance->hr_attendance_user_id,
//                                        LeaveTransaction::LEAVE_TRANSACTION_DEDUCT_UPON_ABSENCE,
//                                        Request::REQUEST_TYPE_HR_ATTENDANCE,
//                                        $attendance->hr_attendance_id
//                                    );


//                                } catch (ModelException $e) {
//                                }

                                break;

                            case Setting::PERSONNEL_ABSENCE_NO_DEDUCTED:

//                                try {

                                    $attendance->manipulation_type = Setting::PERSONNEL_ABSENCE_NO_DEDUCTED;
                                    $attendance->manipulation_leave_id = 0;
                                    $attendance->manipulation_batche_id = 0;
                                    $attendance->manipulation_trans_id = 0;
                                    $attendance->save();

                                    Notification::alertMessage(Notification::SUCCESS, Notification::MESSAGE_UPDATED_SUCCESSFULLY);

                                    $leaveTransaction = \Models\HumanResource\LeaveTransaction::where(LeaveTransaction::USER_ID , $attendance->user_id)
                                        ->where(LeaveTransaction::TYPE , LeaveTransaction::LEAVE_TRANSACTION_DEDUCT_UPON_ABSENCE)
                                        ->where(LeaveTransaction::TABLE_NAME , Request::REQUEST_TYPE_HR_ATTENDANCE)
                                        ->where(LeaveTransaction::ROW_ID , $attendance->id)
                                        ->delete();

//                                    LeaveTransaction::removeLeaveCreditTransaction(
//                                        $_SESSION['organization']->id,
//                                        $attendance->hr_attendance_user_id,
//                                        LeaveTransaction::LEAVE_TRANSACTION_DEDUCT_UPON_ABSENCE,
//                                        Request::REQUEST_TYPE_HR_ATTENDANCE,
//                                        $attendance->hr_attendance_id
//                                    );

//                                } catch (ModelException $e) {
//                                }

                                break;
                        }

//                        LeaveData::updateEmployeeLeavesCredit($_SESSION['organization']->id, $attendance->hr_attendance_user_id);
//                    }

                }

                break;
        }

        try {

            $attendance = HRAttendanceRequest::readID((int)$_SESSION['s_attendance_row_id'] ?? 0);
            $this->Smarty->assign('attendance', $attendance);
            $this->Smarty->assign('details', json_decode($attendance->detail, true));

        } catch (HRAttendanceRequestException $e) {

            $this->Smarty->assign('attendance', []);
            $this->Smarty->assign('details', []);

        }

        if ($attendance) {

            try {

                $days = FingerPrintDay::read([
                    FingerPrintDay::ORG_ID => $attendance->org_id,
                    FingerPrintDay::ATTENDANCE_ID => $attendance->id
                ], [
                    0 => [
                        'property' => FingerPrintDay::DATE,
                        'sort' => 'ASC'
                    ]
                ]);

            } catch (FingerPrintDayException $e) {

            }

            $this->Smarty->assign('fingerPrintRecords', $days);

            try {
                $this->Smarty->assign('interval', FingerPrintInterval::readID((int)($days[0]->interval_id)));
            } catch (FingerPrintIntervalException $e) {

            }

            $this->Smarty->assign('employee', Vacant::getEmployeeJobs($attendance->org_id, $attendance->user_id, Vacant::EMPLOYEE_BASIC_VACANT));
        }


        $this->Smarty->assign('manipulateList', Setting::getList(79));

        $_SESSION['s_personnel_tab'] = "absence";
        $_SESSION['s_attendance_token'] = md5(rand(0000, 9999));
    }

    public function manipulateConfirm($parm, $post)
    {

        $this->Smarty->assign('type', (int)$parm[0] ?? 0);

        try {
            $attendance = HRAttendanceRequest::readID((int)$parm[1]);
            $this->Smarty->assign('attendance', $attendance);
        } catch (HRAttendanceRequestException $e) {
            $this->Smarty->assign('attendance', []);

        }

        try {
            $user = User::readID((int)$attendance->user_id);
            $this->Smarty->assign('user', $user);
        } catch (UserException $e) {
            $this->Smarty->assign('user', []);
        }

        if ($attendance && $user) {


            switch ($parm[0]) {

                case Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_LEAVE_BALANCE:

                    // update employee leaves credit
                    LeaveData::updateEmployeeLeavesCredit($attendance->org_id, $user->id);

                    try {
                        $this->Smarty->assign('leaves', LeaveAllowed::read([LeaveAllowed::USER_ID => $user->id]));
                    } catch (LeaveAllowedException $e) {
                    } catch (LeaveTypeException $e) {
                    }

                    break;

                case Setting::PERSONNEL_ABSENCE_DEDUCTED_FROM_SALARY: // Deduct from salary ( Absence )
                    break;

                case Setting::PERSONNEL_ABSENCE_NO_DEDUCTED: // Not Deduct
                    break;
            }

        }

        $_SESSION['s_attendance_token'] = Helper::generateToken();

    }

    public function doamStructure($parm, $post)
    {
        try {
            $row = HRAttendanceRequest::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('row', $row);
            $this->Smarty->assign('details', json_decode($row->detail, true));
        } catch (HRAttendanceRequestException $e) {
            $this->Smarty->assign('row', []);
            $this->Smarty->assign('details', []);
        }

        $_SESSION['s_personnel_tab'] = "absence";
        $_SESSION['s_attendance_token'] = md5(rand(0000, 9999));
    }

    public function fingerPrintStructure($parm, $post)
    {
        try {
            $row = HRAttendanceRequest::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('row', $row);
        } catch (HRAttendanceRequestException $e) {
            $this->Smarty->assign('row', []);
        }

        if ($row) {

            try {

                $days = FingerPrintDay::read([
                    FingerPrintDay::ORG_ID => $row->org_id,
                    FingerPrintDay::ATTENDANCE_ID => $row->id
                ], [
                    0 => [
                        'property' => FingerPrintDay::DATE,
                        'sort' => 'ASC'
                    ]
                ]);

            } catch (FingerPrintDayException $e) {

            }

            $this->Smarty->assign('fingerPrintRecords', $days);

            try {
                $this->Smarty->assign('interval', FingerPrintInterval::readID((int)($days[0]->interval_id)));
            } catch (FingerPrintIntervalException $e) {

            }

            $this->Smarty->assign('employee', Vacant::getEmployeeJobs($row->org_id, $row->user_id, Vacant::EMPLOYEE_BASIC_VACANT));
        }

        $_SESSION['s_personnel_tab'] = "absence";
        $_SESSION['s_attendance_token'] = md5(rand(0000, 9999));
    }

}