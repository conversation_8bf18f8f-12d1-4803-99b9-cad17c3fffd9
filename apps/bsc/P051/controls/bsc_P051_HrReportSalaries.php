<?php

use Illuminate\Database\Query\Builder;

class bsc_P051_HrReportSalaries extends Controller{

    public function show($parm, $post)
    {

        switch ($parm[0]){
            case 'menu':
                $_SESSION['report_data'] = null;
                break;

            case 'save_session':
                $_SESSION['report_data'] = $post;
                break;
        }

        $batches = collect(DB::table('prl_batches')
            ->where('prl_batches_org_id', $_SESSION['organization']->id)
            ->get());

        $templates = collect(DB::table('prl_templates')
            ->where('prl_templates_org_id', $_SESSION['organization']->id)
            ->get());

        $employees = collect(DB::table('sh_uao')
            ->join('sh_user', 'sh_uao.sh_uao_user_id', '=', 'sh_user.sh_user_id')
            ->where([
                ['sh_uao_org_id', '=', $_SESSION['organization']->id],
                ['sh_uao_b_type', '=', 2],
                ['sh_uao_quit', '=', 0],
                ['sh_uao_b_acceptance', '=', 1],
                ['sh_uao_user_id', '>=', 1],
                ['sh_uao_deleted', '=', 0]
            ])
        ->get());

        $this->Smarty->assign('batches', $batches);
        $this->Smarty->assign('templates', $templates);
        $this->Smarty->assign('employees', $employees);

        $salaries = null;
        $employee = null;
        $template = null;
        $batch = null;

        if ($_SESSION['report_data']){

            $employee = $employees->where('sh_user_id', '=', $_SESSION['report_data']['employee'])->first();
            $batch = $batches->where('prl_batches_id', '=', $_SESSION['report_data']['batch'])->first();
            $template = $templates->where('prl_templates_id', '=', $_SESSION['report_data']['template'])->first();

            $this->Smarty->assign('employee', $employee);
            $this->Smarty->assign('template', $template);
            $this->Smarty->assign('batch', $batch);

            $salaries = $this->prepareReportData($batch, $template, $employee);
            $this->Smarty->assign('salaries', $salaries);

        }

    }

    public function print($parm, $post)
    {

        $this->show($parm, $_SESSION['report_data']);

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));

    }

    /**
     * @param null $batch
     * @param null $template
     * @param null $employee
     * @return mixed
     */
    public function prepareReportData($batch = null, $template = null, $employee = null)
    {

        return DB::table('prl_trans')
            ->where('prl_trans_org_id', $_SESSION['organization']->id)
            ->when($batch, function (Builder $query) use ($batch){
                return $query->where('prl_trans_batch_id', $batch->prl_batches_id);
            })
            ->when($template, function (Builder $query) use ($template){
                return $query->where('prl_trans_template_id', $template->prl_templates_id);
            })
            ->when($employee, function (Builder $query) use ($employee){
                return $query->where('prl_trans_user_id', $employee->sh_uao_user_id);
            })
        ->get();

    }

}