<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bsc_P051_fingerPrintDevices extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]){

            case Operation::INSERT:

                if($_SESSION['s_fingerPrintDevices_token'] == $parm[1]){

                    $device = new FingerPrintDevice();
                    $device->bindProperties($post);
                    $device->org_id = $_SESSION['organization']->id;
                    $device->created_by = $_SESSION['user']->id;
                    $device->created_date = date('Y-m-d');
                    $device->save();

                    Notification::createdAlert();

                }

                break;

            case Operation::UPDATE:

                if($_SESSION['s_fingerPrintDevices_token'] == $parm[1]){

                    try{

                        $device = FingerPrintDevice::readID((int)$parm[2]);
                        $device->bindProperties($post);
                        $device->save();

                        Notification::updatedAlert();

                    }catch (FingerPrintDeviceException $e){

                    }

                }

                break;

            case Operation::DELETE:

                if($_SESSION['s_fingerPrintDevices_token'] == $parm[1]){

                    try{

                        FingerPrintDevice::readID((int)$parm[2])->delete();

                    }catch (FingerPrintDeviceException $e){

                    }

                    $this->DB->delete('fp_dev', (int)$parm[1] ?? 0);

                }

                break;

        }

        try {

            $this->Smarty->assign('devices', FingerPrintDevice::read([FingerPrintDevice::ORG_ID => $_SESSION['organization']->id]));

        } catch (FingerPrintDeviceException $e) {

            $this->Smarty->assign('devices', []);

        }

        $_SESSION['s_fingerPrintDevices_token'] = Helper::generateToken();

    }

    public function add($parm, $post)
    {
        $this->Smarty->assign('deviceTypes', Setting::getList(58));
        $_SESSION['s_fingerPrintDevices_token'] = Helper::generateToken();
    }

    public function edit($parm, $post)
    {

        try{
            $this->Smarty->assign('device', FingerPrintDevice::readID((int) $parm[0] ?? 0));
        }catch (FingerPrintDeviceException $e){
            $this->Smarty->assign('device',[]);
        }

        $this->Smarty->assign('deviceTypes',Setting::getList(58));
        $_SESSION['s_fingerPrintDevices_token'] = Helper::generateToken();

    }

    public function confirm($parm, $post)
    {

        try{
            $this->Smarty->assign('device', FingerPrintDevice::readID((int) $parm[0] ?? 0));
        }catch (FingerPrintDeviceException $e){
            $this->Smarty->assign('device',[]);
        }

        $_SESSION['s_fingerPrintDevices_token'] = Helper::generateToken();

    }

    public function employees($parm)
    {
        
        $device = FingerPrintDevice::readID((int)$parm[0]);
        $employees = Vacant::getAllEmployeesOfFingerPrintDevice();
        // dd($employees);
        $this->Smarty->assign('device', $device); // make variable $device available in templateEmployees view
        $this->Smarty->assign('employees', $employees);
    }

    public function RegisteredEmployees($parm)
    {
        $device = FingerPrintDevice::readID((int)$parm[0]);
        $employees = Vacant::getAllEmployeesOfFingerPrintDevice();
        $this->Smarty->assign('device', $device); // make variable $device available in templateEmployees view
        $this->Smarty->assign('employees', $employees);
    }

    public function saveEmployees($parm, $post)
    {
        foreach ($post['employees'] as $id) {
            $employee = Vacant::readID((int)$id);
            $employee->att_device_id = $parm[0];
            $employee->save();
        }
        alert(translate('message_updated_successfully'));
        redirect('fingerPrintDevices/show');
    }
    

    public function information($parm, $post)
    {

    }
}