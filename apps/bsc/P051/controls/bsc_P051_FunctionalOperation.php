<?php

/**
 * Created by PhpStorm.
 * User: Omda
 * Date: 22/07/20
 * Time: 03:20 PM
 */
use Application\Cache\SnsoCache;
use Carbon\Carbon;
use Domain\ProcessDataService;
use Illuminate\Database\Eloquent\Builder;
use Models\EmployeePayrollTemplate;
use Models\HumanResource\Job as JobModel;
use Models\HumanResource\Request as RequestModel;
use Models\HumanResource\Unit as UnitModel;
use Models\User as UserModel;

class bsc_P051_FunctionalOperation extends Controller
{

    /**
     * @param $parm
     * @param $post
     */
    public function show2($parameters, $post)
    {
        if (!is_null($parameters[0])) {

            $_SESSION['unit_id'] = $parameters[1];
            $employee = UserModel::has('basicJob')
                ->find($parameters[0]);

            $this->Smarty->assign([
                "user_employee" => $employee,
                "sh_uao_id" => $employee->basicVacant->id,
            ]);

        }

        $_SESSION['unit_id'] = (int)$post['unit_id'] ? (int)$post['unit_id'] : $parameters[1];

        $employees = UserModel::has('basicJob.unit')
            ->whereHas('basicJob', function (Builder $query) {
                $query->where(Job::UNIT_ID, $_SESSION['unit_id']);
            })->get();


        $this->Smarty->assign([
            "employees" => $employees,
            "units" => UnitModel::active()->get(),
            "unit_id" => $_SESSION['unit_id']
        ]);

    }

    public function show($parm, $post)
    {
        switch ($parm[0]) {
            case 'menu':
                $_SESSION['search_employees'] = null;
                $_SESSION['unit_id'] = null;
                break;
            case 'save_session':
                $_SESSION['unit_id']  = $post['unit_id'] ?? 0;
                break;
            case 'search':
                $_SESSION['search_employees'] = $post;
                $_SESSION['unit_id']  = $post['unit_id'] ?? 0;
                break;
            case 'cancel_search':
                $_SESSION['search_employees'] = null;
                $_SESSION['unit_id'] = null;
                break;
        }



        $unitQuery = UnitModel::query();

        if(session('unit_id')){
            $unit = $unitQuery->with('vacancies.job')->find(session('unit_id'));
            $vacancies = collect($unit->vacancies)->map(function ($vacant){
                return $vacant->job;
            })->unique('sh_job_id');
        }else{
            $units = $unitQuery->with('vacancies.job')->get();
            $vacancies = collect($units)->map(function ($vacant){
                return collect($vacant->vacancies)->map(function($vacant){
                    return $vacant->job;
                })->unique('sh_job_id');
            })->flatten();
        }

        // Search & Filter *********************************************************************************************
        if ($_SESSION['search_employees']['name'] || $_SESSION['search_employees']['vacancyId']
            || $_SESSION['search_employees']['filters']) {

            if($_SESSION['search_employees']['name']){
                $trimmedName = str_replace(' ', '', $_SESSION['search_employees']['name']);
                $employees = UserModel::filterByFullName($trimmedName);

                $_SESSION['unit_id'] = null;
                $_SESSION['search_employees']['vacancyId'] = null;

            }

            if(session('search_employees.vacancyId')){
                $unit_id   = $_SESSION['unit_id'];
                $vacant_id = $_SESSION['search_employees']['vacancyId'];
                $employees = UserModel::filterByVacantAndUnit($unit_id, $vacant_id);
            }

            if($_SESSION['search_employees']['filters']){
                $genders = $_SESSION['search_employees']['filters'];
                $employees = UserModel::filterByGender($genders);
            }


        } else {
            $employees  = UserModel::filterByUnit($_SESSION['unit_id']);
        }

        // filters using only vacant
        if($post['unit_id'] == null && $post['vacancyId'] != ""){
            if($genders = $_SESSION['search_employees']['filters']){
                $employees = UserModel::filterByVacantAndGender($vacant_id, $genders);
            }else{
                $vacant_id = (int) $post['vacancyId'];
                $employees = UserModel::filterByVacantOnly($vacant_id );
            }
        }

        $this->Smarty->assign([
            'employees' => $employees,
            'units' => UnitModel::active()->get(),
            'vacancies' => $vacancies
        ]);

        $this->Smarty->assign('sexList', Setting::getList(27));

    }

    /**
     * @param $parm
     * @param $post
     */
    public function getEmployee($parm, $post)
    {
        redirect('FunctionalOperation/show', [$post['user_id'], $parm[0]]);
    }

    /**
     * @param $parm
     */
    public function jobCardHTML($parm)
    {
        $job = JobModel::find($parm[0]);
        $this->Smarty->assign('job', $job);
    }

    public function printEmployeeReport($parm, $post)
    {
        $this->printEmployee($parm, $post);

        DocumentProcessor::outputPDF(
            $this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig())
        );
    }

    /**
     * @param $parm
     */
    public function printEmployee($parm)
    {
        $this->Smarty->assign(
            'breadcrumb_sub_title', translate('gnr_employee_record_data')
        );

        $employee = UserModel::find($parm[0]);
        $_SESSION['emp_user_id'] = $parm[0];
        $this->Smarty->assign('employee', $employee);

        $this->getEmployeeRequests($employee);
        $this->getEmployeeAccountabilties($employee);
        $this->getEmployeeCommittees();
        $this->getEmployeeJobs();

        try {
            $vacations = LeaveAllowed::read([
                LeaveAllowed::USER_ID => $employee->id
            ]);
        } catch (LeaveAllowedException $e) {
            $vacations = null;
        }

        $this->Smarty->assign('vacations', $vacations);

    }

    private function getEmployeeRequests($employee)
    {
        $configTypesArray = [];

        $request = RequestModel::withTableNames()
            ->where('wf_request_user_id', '=', (int)$employee->id)
            ->totalStatus()
            ->first();
        $this->Smarty->assign('request', $request);


        try {
            $configTypesArray = ClientConfiguration::getClientConfigurationArray((int)$_SESSION['organization']->id);
            $this->Smarty->assign('clientConfigurationArray', $configTypesArray);
        } catch (ClientConfigurationException $e) {
            $this->Smarty->assign('clientConfigurationArray', []);
        }

//        $this->Smarty->assign('employee', Vacant::getEmployeeJobs($_SESSION['organization']->id, $employee->userObject->id, Vacant::EMPLOYEE_BASIC_VACANT));

        if (in_array(Setting::EMPLOYEE_DASHBOARD_LEAVE_REQUEST, $configTypesArray)) {
//            $this->Smarty->assign('hr_lev_list', \Models\HumanResource\Request::getEmployeeFinishedRequests((int) $employee->id, Request::REQUEST_TYPE_HR_LEAVE)->get());
            $this->Smarty->assign('hr_lev_list', Request::getEmployeeFinshedRequests((int)$employee->id, Request::REQUEST_TYPE_HR_LEAVE));
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_RETREAT_LEAVE_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('hr_retreat_leave', Request::getEmployeeFinshedRequests((int)$employee->id, Request::REQUEST_TYPE_HR_LEAVE_RETREAT, $_SESSION['prg']->id));
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_PERMISSION_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('hr_perm_list', Request::getEmployeeFinshedRequests((int)$employee->id, Request::REQUEST_TYPE_HR_PERMISSION));
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_ADVANCE_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('hr_advancerequest_list', Request::getEmployeeFinshedRequests((int)$employee->id, Request::REQUEST_TYPE_HR_ADVANCE_REQUEST));
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_MANDATE_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('hr_mndt_list', Request::getEmployeeFinshedRequests((int)$employee->id, Request::REQUEST_TYPE_HR_MANDATE));
            $this->Smarty->assign('hr_mndtfees_list', Request::getEmployeeFinshedRequests((int)$employee->id, Request::REQUEST_TYPE_HR_MANDATE_FEES));
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_ADDITIONAL_WORK_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('hr_otwrk_list', Request::getEmployeeFinshedRequests((int)$employee->id, Request::REQUEST_TYPE_HR_OUTWORK));
            $this->Smarty->assign('hr_outworkfees_list', Request::getEmployeeFinshedRequests((int)$employee->id, Request::REQUEST_TYPE_HR_OUTWORK_FEES));

        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_FINEXCH_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('fin_exch_request_list', Request::getEmployeeFinshedRequests((int)$employee->id, Request::REQUEST_TYPE_FIN_EXCHANGE, Program::PROGRAM_EMPLOYEE_DASHBOARD_P002));
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_LEAVE_CREDIT_REQUEST, $configTypesArray)) {
            $this->Smarty->assign('leave_credit_request_list', Request::getEmployeeFinshedRequests((int)$employee->id, Request::REQUEST_TYPE_HR_LEAVE_CREDIT_EDIT, Program::PROGRAM_EMPLOYEE_DASHBOARD_P002));
        }

    }

    private function getEmployeeAccountabilties($employee)
    {
        $this->Smarty->assign('attendanceRequests', Request::getEmployeeFinshedRequests((int)$employee->id, Request::REQUEST_TYPE_HR_ATTENDANCE));
        $this->Smarty->assign('latenciesRequests', Request::getEmployeeFinshedRequests((int)$employee->id, Request::REQUEST_TYPE_HR_LATENCY));
    }

    private function getEmployeeCommittees()
    {
        try {
            $committees = CommitteeMember::read([
                CommitteeMember::USER_ID => (int)$_SESSION['emp_user_id']
            ]);;

        } catch (CommitteeMemberException $e) {
            $committees = [];
        }
        try {
            $committees = collect($committees);
            $employeeCommittees = Committee::convertFromQB(DB::table('es_committee')
                ->whereIn(Committee::ID, $committees->pluck('committee_id'))
                ->get());

            foreach ($employeeCommittees as $committee) {
                $committee->memberships = $committees
                    ->where('committee_id', $committee->id);
            };

        } catch (CommitteeException $e) {
            $employeeCommittees = [];
        }
        $this->Smarty->assign('employeeCommittees', $employeeCommittees);
    }

    private function getEmployeeJobs()
    {
        try {
            if ($_SESSION['emp_user_id']) {
                $employeeJobs = JobModel::userJobs($_SESSION['emp_user_id']);

                $this->Smarty->assign('employeeJobs', $employeeJobs);
            }
        } catch (Exception $e) {
            $this->Smarty->assign('employeeJobs', []);
        }

    }

    //================== resume =================

    public function eduAdd($parm, $post)
    {
        $this->Smarty->assign('edu_type_list', Setting::getList(2));
        $this->Smarty->assign('edu_field_list', Setting::getList(61));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function payrollDetails($parm, $post)
    {
        try {
            $temp = EmployeePayrollTemplate::find($parm[0] ?? 0);
            $this->Smarty->assign('payroll', $temp);
            $basedOnPayrollNames = '';
            foreach (collect(json_decode($temp->template->amount_percentage_based_on)) as $tempId) {
                $basedOnPayrollNames .= PayrollRule::getPayrollRuleName($tempId) . '<br>';
            }
            $this->Smarty->assign('basedOnPayrollNames', $basedOnPayrollNames);
        } catch (PayrollTemplateException $e) {
            $this->Smarty->assign('payroll', null);
        }
    }

    public function vacancies($parm, $post)
    {
        $this->Smarty->assign('breadcrumb_sub_title', translate('gnr_jobs'));

        switch ($parm[0]) {

            case 'save_session':

                $_SESSION['s_current_employee_user_id'] = $parm[1];
                $_SESSION['s_empdata_uao_tab'] = $parm[2];
                break;
            case 'update':

                if ($_SESSION['s_empdata_token'] === $parm[1]) {

                    // Set the validation rules
                    Validation::rules($post, [
                        'sh_uao_job_date_of_appointment' => 'date',
                        'salary_basic' => 'required_if:salary_delivery,227',
                        'days_in_month' => 'required_if:salary_delivery,861',
                        'days_salary' => 'required_if:salary_delivery,861',
                        'hour_salary' => 'required_if:salary_delivery,228',
                        'att_doam_id' => 'required_if:att_status,1',
                        'att_device_id' => 'required_if:att_status,1,2|numeric|min:0',
                        'att_device_num' => 'required_if:att_status,1,2|numeric|min:0',
                    ]);

                    if (Validation::check()) {

                        try {

                            $vacant = Vacant::readID((int)$parm[2] ?? 0);
                            $lastBoss = $vacant->direct_boss_emp;
                            $post['sh_uao_payroll_template_id'] = $vacant->sh_uao_payroll_template_id;
                            $vacant->bindProperties($post);


                            $vacant->job_date_of_appointment = $this->Date->get_date('ad', $post['sh_uao_job_date_of_appointment']);

                            $vacant->job_end_date_of_appointment = $this->Date->get_date('ad',
                                $post['sh_uao_job_end_date_of_appointment']);

                            if ($post['att_status'] == 0) {
                                $vacant->att_doam_id = 0;
                                $vacant->att_device_id = 0;
                            }

                            $vacant->save();
                            if ($lastBoss != $vacant->direct_boss_emp)
                                event('event.DirectEmployeeBossChanged', [$lastBoss, $vacant->direct_boss_emp, $vacant->job_unt_id, $vacant->job_name]);

                            Notification::updatedAlert();

                        } catch (VacantException $e) {
                        }

                    }

                }

                $_SESSION['s_empdata_uao_tab'] = $parm[2];

                break;
            case 'insertPayrollTemplate':
                if ($_SESSION['s_rand_trans_num'] == $parm[2]) {

                    try {
                        $rule = new EmployeePayrollTemplate();

                        $template = new stdClass();
                        
                        // تحديد اسم البند بناءً على الاختيار
                        if (!empty($post['use_predefined_terms']) && !empty($post['predefined_terms'])) {
                            // إذا كان المستخدم اختار الخيارات الجاهزة
                            $template->name = $post['predefined_terms'];
                        } else {
                            // إذا كان المستخدم يستخدم الإدخال اليدوي
                            $template->name = !empty($post['name']) ? $post['name'] : '';
                        }
                        
                        foreach ($post as $propertyName => $propertyValue) {
                            if ($propertyName == 'user_id') {
                                $template->user_id = $post['user_id'];
                            } else {
                                $template->{$propertyName} = $propertyValue;
                            }
                        }
                        $template->template_id = $parm[1];
                        $template->type = $_SESSION['new_rule_type'];
                        $template->condition = 648; // $post['condition'];
                        $template->range_based_on = ''; // implode(',', $post['range_based_on']);
                        $template->range_min = 0; // $post['range_min'];
                        $template->range_max = 0; // $post['range_max'];
                        $template->amount_percentage_based_on = json_encode($post['amount_percentage_based_on']);
                        $rule->template_id = $parm[1];
                        $rule->template = json_encode($template);
                        $rule->user_id = $post['user_id'];
                        $rule->save();

                        $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';

                        Notification::createdAlert();
                    } catch (Exception $e) {

                    }
                }
                try {
                    $template = PayrollTemplate::readID((int)$_SESSION['s_prl_templates_id'] ?? 0);
                    $this->Smarty->assign('row', $template);
                    $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
                    $this->Smarty->assign('row', $template);
                    if ($template) {
                        $this->Smarty->assign('temp_rules', PayrollRule::getPayrollRule($template));
                    }
                    $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
                } catch (PayrollTemplateException $exception) {

                }

                break;
            case 'editPayrollTemplate':
                if ($_SESSION['s_rand_trans_num'] == $parm[2]) {

                    try {
                        $rule = EmployeePayrollTemplate::find($parm[1]);
                        $template = new stdClass();
                        
                        // تحديد اسم البند بناءً على الاختيار
                        if (!empty($post['use_predefined_terms']) && !empty($post['predefined_terms'])) {
                            // إذا كان المستخدم اختار الخيارات الجاهزة
                            $template->name = $post['predefined_terms'];
                        } else {
                            // إذا كان المستخدم يستخدم الإدخال اليدوي
                            $template->name = !empty($post['name']) ? $post['name'] : '';
                        }
                        
                        foreach ($post as $propertyName => $propertyValue) {
                            if ($propertyName == 'user_id') {
                                $template->user_id = $_SESSION['s_current_employee_user_id'];
                            } else {
                                $template->{$propertyName} = $propertyValue;
                            }
                        }
                        $template->template_id = $_SESSION['s_prl_templates_id'];
                        $template->type = $_SESSION['new_rule_type'];
                        $template->condition = 648;
                        $template->range_based_on = $post['range_based_on'];
                        $template->effect_in_day_cost = $post['effect_in_day_cost'];
                        $template->range_min = 0;
                        $template->range_max = 0;
                        $template->amount_percentage_based_on = json_encode($post['amount_percentage_based_on']);
                        $rule->template_id = $_SESSION['s_prl_templates_id'];
                        $rule->template = json_encode($template);
                        $rule->update();
                        $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
                        Notification::updatedAlert();
                        redirect('FunctionalOperation/vacancies');
                    } catch (Exception $e) {
                    }
                }
                break;
            case 'dismiss':

                try {

                    $quitedVacant = Vacant::readID((int)$parm[2]);

                } catch (VacantException $e) {

                }

                if ($quitedVacant) {

                    //TODO check if vacant is it manager ?
                    if ((int)$quitedVacant->user_id === (int)$_SESSION['organization']->it_manager_id and $quitedVacant->basic) {
                        Notification::alertMessage(Notification::ERROR, 'please_change_it_manager_to_another_vacant');
                    } else {
                        try{
                            $dismiss = Vacant::dismissalVacant($post, $quitedVacant);
                        }catch (VacantException $e ){

                        }
                        Notification::sendNotification(
                            848,
                            0,  
                            'hr_dismiss',
                            $dismiss->id,
                            $dismiss->created_by,
                            $dismiss->user_id,
                            1010,
                            []);

                        $templateVars = [
                            'username' => $quitedVacant->userObject->full_name,
                            'jobName' => $quitedVacant->jobObject->sh_job_name,
                            'dismissalReason' => $post['reasons'],
                            'disMissalDate' => date('Y-m-d')
                        ];
                        $template = new DismissEmployeeEmailTemplate($templateVars);
                        try {
                            $mailer = new UserMailer($quitedVacant->userObject, $template);
                            $mailer->send();
                        } catch (UserException $e) {

                        }

                        Notification::alertMessage(Notification::SUCCESS, 'EmployeeDismissedSuccessfully');
                    }

                    $_SESSION['tab1'] = '';
                    $_SESSION['tab2'] = 'active';
                    $_SESSION['tab3'] = '';
                    $_SESSION['tab4'] = '';
                    $_SESSION['tab5'] = '';
                    $_SESSION['tab6'] = '';
                    $_SESSION['tab7'] = '';
                }

                break;
            case 'settobasic':

                if ($_SESSION['s_empdata_token'] === $parm[1]) {

                    Vacant::setVacantToBasic(Vacant::readID((int)$parm[2]));
                    Notification::updatedAlert();

                }

                break;
            case 'PayrollTemplate':
                if ($_SESSION['s_empdata_token'] === $parm[1]) {
                    if ($post['submit'] === 'update') {
                        try {
                            $template = PayrollTemplate::readID((int)$post['template'] ?? 0);
                            $this->deleteEmployeeSpecificPayroll($template->id, $post['user_id']);
                            $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
                            $_SESSION['s_prl_templates_id'] = $post['template'];
                            $this->Smarty->assign('row', $template);
                            if ($template) {
                                $this->Smarty->assign('temp_rules', PayrollRule::getPayrollRule($template));
                            }
                        } catch (PayrollTemplateException $e) {
                        }
                    } else {
                        $template = PayrollTemplate::readID((int)$post['template']);
                        $this->Smarty->assign('temp_rules2', PayrollRule::getPayrollRule($template));
                        $this->Smarty->assign('temp_template_id2', $post['template']);
                        $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
                    }
                }
                break;
            case 'deletePayrollTemplate':
                if ($_SESSION['s_empdata_token'] == $parm[2]) {
                    $rule = EmployeePayrollTemplate::find((int)$parm[1]);
                    $rule->delete();

                    Notification::deletedAlert();
                    $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
                    $_SESSION['s_prl_templates_id'] = $parm[3];
                    $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));

                }
                break;
        }

        try {

            /** @var sh_uao $vacant */
            $vacant = Vacant::getEmployeeBasicVacantEntity((int)$_SESSION['s_current_employee_user_id'] ?? 0);
            $employee = UserModel::has('basicJob')
                ->find((int)$_SESSION['s_current_employee_user_id']);
            SnsoCache::set('vacancies_vacant', $vacant, 21600);

            $this->Smarty->assign('basicVacant', $vacant);
            $this->Smarty->assign('user_employee', $employee);

            if (!isset($_SESSION['s_empdata_uao_tab']))
                $_SESSION['s_empdata_uao_tab'] = $vacant->sh_uao_id;

            try {
                $this->Smarty->assign('UserVacanciesList',
                    sh_uao::simpleReadByProperty([
                        Vacant::ORG_ID => $_SESSION['organization']->id,
                        Vacant::USER_ID => $vacant->sh_user_id,
                        Vacant::B_TYPE => 2,
                        Vacant::B_ACCEPTANCE => 1,
                        Vacant::QUIT => 0,
                        Vacant::DELETED => 0
                    ], Vacant::BASIC, 'DESC'));


            } catch (ModelException $e) {
                $this->Smarty->assign('UserVacanciesList', []);
            }


            $jscode1 = '';
            switch ($vacant->sh_uao_salary_delivery) {
                case 227:
                    $jscode1 = '<script> $(document).ready(function(){ $("#BasicSalary").show("fast"); $("#MonthTypeDays").show("fast"); $("#DaysTypeDays").hide("fast"); $("#hourTypeCost").hide("fast");}); </script>';
                    break;
                case 228:
                    $jscode1 = '<script> $(document).ready(function(){ $("#BasicSalary").hide("fast");$("#MonthTypeDays").hide("fast"); $("#DaysTypeDays").hide("fast");$("#hourTypeCost").show("fast");}); </script>';
                    break;
                case 861:
                    $jscode1 = '<script> $(document).ready(function(){ $("#BasicSalary").hide("fast"); $("#MonthTypeDays").hide("fast"); $("#DaysTypeDays").show("fast"); $("#hourTypeCost").hide("fast"); }); </script>';
                    break;
                case 826:
                    $jscode1 = '<script> $(document).ready(function(){ $("#BasicSalary").hide("fast"); $("#MonthTypeDays").hide("fast"); $("#DaysTypeDays").hide("fast"); $("#hourTypeCost").hide("fast"); }); </script>';
                    break;
            }

            $jscode = '';
            switch ($vacant->sh_uao_att_status) {
                case 0:
                    $jscode = '<script> $(document).ready(function(){ $("#doam_div").hide("fast"); $("#fingerprint_div").hide("fast"); }); </script>';
                    break;

                case 1:
                    $jscode = '<script> $(document).ready(function(){ $("#doam_div").show("fast"); $("#fingerprint_div").show("fast"); } ); </script>';
                    break;

                case 2:
                    $jscode = '<script> $(document).ready(function(){ $("#doam_div").hide("fast"); $("#fingerprint_div").show("fast"); } ); </script>';
                    break;
            }

            $this->Smarty->assign('jscode1', $jscode1);
            $this->Smarty->assign('jscode', $jscode);


        } catch (VacantException $e) {
            $this->Smarty->assign('basicVacant', []);
        }

        try {
            $this->Smarty->assign('salaryDeliveryList', Setting::getSalarydeliveryTypes());
        } catch (SettingException $e) {
            $this->Smarty->assign('salaryDeliveryList', []);
        }

        try {
            $this->Smarty->assign('directManagersList', Vacant::getEmployeeListByEntity($_SESSION['organization']));
        } catch (VacantException $e) {
            $this->Smarty->assign('directManagersList', []);
        }

        try {
            $this->Smarty->assign('doamList', Doam::read([Doam::ORG_ID => $_SESSION['organization']->id]));
        } catch (DoamException $e) {
            $this->Smarty->assign('doamList', []);
        }

        try {
            $this->Smarty->assign('AttendanceDevicesList',
                FingerPrintDevice::read([FingerPrintDevice::ORG_ID => $_SESSION['organization']->id]));
        } catch (FingerPrintDeviceException $e) {
            $this->Smarty->assign('AttendanceDevicesList', []);
        }

        try {
            $this->Smarty->assign('templates', PayrollTemplate::read([
                PayrollTemplate::ORG_ID => $_SESSION['organization']->id,
            ]));
        } catch (PayrollTemplateException $e) {

        }

        try {
            $template = PayrollTemplate::readID((int)SnsoCache::get('vacancies_vacant')->sh_uao_payroll_template_id);
            $this->Smarty->assign('row', $template);
            $this->Smarty->assign('temp_rules', PayrollRule::getPayrollRule($template));
            $this->Smarty->assign('basic_template_id', $_SESSION['s_prl_templates_id']);

            // get employee basic templates
            $employeeSpecificTemplate = EmployeePayrollTemplate::where('user_id', (int)SnsoCache::get('vacancies_vacant')->sh_user_id ?? 0)
                ->where('template_id', $template->id)
                ->whereNotNull('template')
                ->get();


            $this->Smarty->assign('employeeSpecificTemplate', $employeeSpecificTemplate);

            $vacant = SnsoCache::get('vacancies_vacant');
            $_SESSION['s_prl_templates_id'] = $vacant->sh_uao_payroll_template_id;
            $result = Vacant::getEmployeePayrollDetails((int)$vacant->sh_user_id ?? 0);

            // get employee basic salary
            $this->Smarty->assign('employee_salary', $vacant->sh_uao_salary_basic);
            $employeeTotalSalary = $result['salary'] + $result['allowances'] - $result['deductions'];
            $this->Smarty->assign('employee_total_salary',  $employeeTotalSalary);

        } catch (PayrollTemplateException $exception) {
        }

        $_SESSION['s_empdata_token'] = Helper::generateToken();
    }

    private function deleteEmployeeSpecificPayroll($templateId, $userId)
    {
        try {
            $vacant = Vacant::getEmployeeBasicVacantEntity((int)$userId ?? 0);
            if ($vacant->sh_uao_payroll_template_id != $templateId) {
                $vacant->sh_uao_payroll_template_id = $templateId;
                $vacant->save();
            }
            $lastTempaId = SnsoCache::get('vacancies_vacant')->sh_uao_payroll_template_id;
            EmployeePayrollTemplate::where('user_id', (int)$userId ?? 0)
                ->where('template_id', $lastTempaId)
                ->delete();
            Notification::updatedAlert();
        } catch (VacantException $e) {

        }
    }


    public function addrule($parm, $post)
    {
        try {

            $this->Smarty->assign('row', PayrollTemplate::readID((int)$parm[1] ?? 0));
            $this->Smarty->assign('user_id', (int)$parm[2] ?? 0);

        } catch (PayrollTemplateException $e) {

            $this->Smarty->assign('row', []);

        }

        $this->Smarty->assign('yesNoList', Setting::getList(119));
        $this->Smarty->assign('rule_types_list', Setting::getList(144));
        $this->Smarty->assign('rule_condition_list', Setting::getList(145));
        $this->Smarty->assign('rule_active_list', Setting::getList(147));
        $this->Smarty->assign('rule_amount_list', Setting::getList(146));

        // get all PayrollRule except SALARY_RULE_DEDUCTION
        $rules = collect(PayrollRule::read([
            PayrollRule::ORG_ID => $_SESSION['organization']->id,
            PayrollRule::TEMPLATE_ID => $_SESSION['s_prl_templates_id']
        ]))->filter(function ($rule) {
            return $rule->category_id != PayrollRule::SALARY_RULE_DEDUCTION;
        })->values();

        try {
            $this->Smarty->assign('rules_list', $rules);
        } catch (PayrollRuleException $e) {
        }


        $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));

        $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
    }


    public function confirmDocument($parm, $post)
    {

        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

        $_SESSION['s_documents_token'] = md5(rand(0, 10000000));

    }

    public function ruleConfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('rule', EmployeePayrollTemplate::find((int)$parm[0] ?? 0));
        } catch (PayrollRuleException $e) {
        }
        $_SESSION['s_prl_templates_id'] = $parm[1];
        $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
    }

    public function EditRule($parm, $post)
    {
        try {
            $this->Smarty->assign('rule', EmployeePayrollTemplate::find((int)$parm[0] ?? 0));
        } catch (PayrollRuleException $e) {
        }
        try {
            $this->Smarty->assign('rules_list', PayrollRule::read([
                PayrollRule::ORG_ID => $_SESSION['organization']->id,
                PayrollRule::TEMPLATE_ID => $_SESSION['s_prl_templates_id']
            ]));
        } catch (PayrollRuleException $e) {
        }

        $this->Smarty->assign('yesNoList', Setting::getList(119));
        $this->Smarty->assign('rule_types_list', Setting::getList(144));
        $this->Smarty->assign('rule_amount_list', Setting::getList(146));
        $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
    }

    public function dismiss($parm, $post)
    {
        try {
            $this->Smarty->assign('vacant', Vacant::readID((int)$parm[0] ?? 0));
        } catch (VacantException $e) {
            $this->Smarty->assign('vacant', []);
        }

        $_SESSION['s_empdata_token'] = md5(rand(0, 99999));

    }

    public function moveSpecifiedVacant($parm, $post)
    {

        $this->Smarty->assign('oldVacant', (int)$parm[0]);
        try {
            $this->Smarty->assign('vacant', Vacant::readID((int)$parm[0] ?? 0));
//            dd(Vacant::readID((int)$parm[0] ?? 0));
        } catch (VacantException $e) {
            // $this->Smarty->assign('vacant', []);
        }

        // $_SESSION['s_empdata_token'] = md5(rand(0, 99999));

        try {
            $this->Smarty->assign('units', Unit::toggleNestedRetrieve(false)::read([Unit::ORG_ID => $_SESSION['organization']->id]));
        } catch (UnitException $e) {
            // $this->Smarty->assign('units', []);
        }

        $_SESSION['s_recruitment_unit_id'] = $post['unitId'] ?? '';

        if($_SESSION['s_recruitment_unit_id']){

            try{

                $this->Smarty->assign('vacancies', Vacant::read([
                    Vacant::ORG_ID => $_SESSION['organization']->id,
                    Vacant::JOB_UNT_ID => $_SESSION['s_recruitment_unit_id'],
                    Vacant::JOB_ACTIVATION => 23,
                    Vacant::QUIT => 0,
                    Vacant::DELETED => 0
                ]));

            }catch (VacantException $e){

                $this->Smarty->assign('vacancies', []);

            }

            try{
                $this->Smarty->assign('currentUnit',sh_unt::readByID((int)$_SESSION['s_recruitment_unit_id']));
            }catch (UnitException $e){
                $this->Smarty->assign('currentUnit', []);
            }

        }


    }

    public function ConfirmRecruimentForSpecifiedVacant($parm, $post)
    {
        $this->Smarty->assign('user', User::readID((int) $parm[0] ?? 0));

        $this->Smarty->assign('vacant', Vacant::readID((int) $parm[1] ?? 0));

        $this->Smarty->assign('backTab',$parm[2].'/'.$parm[3]);

        $_SESSION['s_recruitment_token'] = md5(rand(0000,9999));
    }

    public function RecruitSpecifiedVacant($parm, $post)
    {
        switch ($parm[0]) {

            case 'save_session':

                $_SESSION['s_vacant_id'] = $parm[1];
                $_SESSION['s_main_tab_activation'] = 'tab1';
                $_SESSION['s_user_active_tabe'] = 'employee';

                break;

            case 'recruit':

                if ($_SESSION['s_recruitment_token'] === $parm[1]) {

                    if(Vacant::moveEmbloyeeToNewVacant((int) $parm[2] ?? 0, (int) $parm[3] ?? 0 , (int) $parm[4] )){

                        $vacantRecruited = Vacant::readID((int)$parm[3]);

//                        if($vacantRecruited){
//                            try {
//                                Notification::sendNotification(
//                                    848,
//                                    0,
//                                    'sh_uao',
//                                    $vacantRecruited->id,
//                                    $_SESSION['user']->id,
//                                    $vacantRecruited->user_id,
//                                    1008,
//                                    []);
//                            }catch (NotificationException $e){
//
//                            }
//
//                            $templateVars = [
//                                'username'=> $vacantRecruited->userObject->full_name,
//                                'jobName'=> $vacantRecruited->jobObject->sh_job_name,
//                                'jobCode'=> $vacantRecruited->job_code,
//                                'recruitmentDate'=> $vacantRecruited->job_date_of_appointment,
//                            ];
//                            event('event.EmployeeGetRecruited',[$vacantRecruited->job_unt_id,$vacantRecruited->jobObject->sh_job_name,$vacantRecruited->userObject->full_name]);
//                            $template = new RecruitUserTemplate($templateVars);
//                            try{
//                                    $mailer = new UserMailer(User::readID((int)$vacantRecruited->user_id), $template);
//                                    $mailer->send();
//                            }catch (UserException $e){
//
//                            }
//                        }
                    }
                }
                alert(translate('employee_moved_successfully'));
                redirect('FunctionalOperation/vacancies',['save_session',(int) $parm[2],(int) $parm[3]]);
                break;

        }

        try{
            $this->Smarty->assign('vacant',Vacant::readID((int) $_SESSION['s_vacant_id'] ?? 0));
        }catch (VacantException $e){
            $this->Smarty->assign('vacant',[]);
        }

        $_SESSION['s_recruitment_token'] = md5(rand(0000,9999));

    }

    public function settobasic($parm, $post)
    {
        try {

            $this->Smarty->assign('vacant', Vacant::readID((int)$parm[0] ?? 0));

        } catch (VacantException $e) {
            $this->Smarty->assign('vacant', []);
        }

        $_SESSION['s_empdata_token'] = md5(rand(0, 99999));
    }


    public function templates($parm, $post)
    {
        try {
            $userRow = User::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('UserRow', $userRow);
        } catch (UserException $e) {
            $this->Smarty->assign('UserRow', []);
        }

        if ($userRow) {

            $this->Smarty->assign('leaves', LeaveType::getEmployeeLeaves($_SESSION['organization'], $userRow));
            $this->Smarty->assign('selected_array',
                $this->getSelectedUserLeaves($userRow)
            );

        }


        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    /**
     * @param $userRow
     * @return array
     */
    private function getSelectedUserLeaves($userRow): array
    {
        $SelectedArray = [];
        try {

            $user_leaves_types = LeaveAllowed::read([
                LeaveAllowed::ORG_ID => $_SESSION['organization']->id,
                LeaveAllowed::USER_ID => $userRow->id
            ]);

        } catch (LeaveAllowedException $e) {
        }

        if ($user_leaves_types)
            foreach ($user_leaves_types as $item) {
                $SelectedArray[] = $item->leave_id;
            }

        return $SelectedArray;
    }

    public function leavesetting($parm, $post)
    {
        $this->Smarty->assign('breadcrumb_sub_title', translate('gnr_leaves'));

        switch ($parm[0]) {

            case 'save_session':
                $_SESSION['s_leave_user_id'] = $parm[1];
                $_SESSION['s_leave_uao_id'] = $parm[2];
                break;

            case 'leave':

                switch ($parm[1]) {

                    case 'credit':

                        $_SESSION['s_leave_user_id'] = $parm[2];

                        break;

                    case 'insert':

                        if ($_SESSION['s_employees_token'] == $parm[2]) {

                            try {
                                $allowedLeave = LeaveAllowed::readID($post['allowedleave_id'] ?? 0);
                            } catch (LeaveAllowedException $e) {
                            }

                            if ($allowedLeave) {

                                $creditRequest = new LeaveCreditRequest();
                                $creditRequest->bindProperties($post);
                                $creditRequest->leave_id = $allowedLeave->leave_id;
                                $creditRequest->org_id = $_SESSION['organization']->id;
                                $creditRequest->user_id = $_SESSION['s_leave_user_id'];
                                $creditRequest->take_effect_date = date('Y-m-d');
                                $creditRequest->created_by = $_SESSION['user']->id;

                                if ($creditRequest->credit_opr_type == Setting::LEAVE_CREDIT_ADD_BALANCE) {
                                    if ($this->balanceHitLimit($allowedLeave, $post['credit']) || $allowedLeave->leaveType->transfer_type != LeaveType::TRANSFER_WITH_LIMIT) {
                                        $creditRequest->create();
                                        Notification::createdAlert();
                                    } else {
                                        alert(translate('gnr_maximum_leave_credit_hit'), 'warning');
                                    }
                                }

                                if ($creditRequest->credit_opr_type == Setting::LEAVE_CREDIT_DISCOUNT_FROM_BALANCE) {
                                    if ($allowedLeave->employeeLeaveCredit >= $creditRequest->credit) {
                                        $creditRequest->create();
                                        Notification::createdAlert();
                                    } else {
                                        Notification::alertMessage(Notification::WARNING, 'p_employee_leave_credit_less_than_discounted_credit');
                                    }
                                }

                            }

                        }

                        break;

                    case 'update':

                        if ($_SESSION['s_employees_token'] == $parm[2]) {

                            try {
                                $allowedLeave = LeaveAllowed::readID($post['allowedleave_id'] ?? 0);
                            } catch (LeaveAllowedException $e) {
                            }

                            if ($allowedLeave) {

                                try {

                                    $creditRequest = LeaveCreditRequest::readID($parm[3] ?? 0);
                                    $creditRequest->bindProperties($post);
                                    $creditRequest->leave_id = $allowedLeave->leave_id;
                                    $creditRequest->take_effect_date = date('Y-m-d');
                                    $creditRequest->save();

                                    Notification::updatedAlert();

                                } catch (LeaveCreditRequestException $e) {
                                }
                            }

                        }

                        break;

                    case 'delete':

                        if ($_SESSION['s_employees_token'] == $parm[2]) {

                            try {

                                $creditRequest = LeaveCreditRequest::readID($parm[3] ?? 0);
                                $creditRequest->delete();

                                Notification::deletedAlert();

                            } catch (LeaveCreditRequestException $e) {
                            }

                        }

                        break;
                }

                $this->Smarty->assign('creditList', 1);
                $_SESSION['s_empleaves_tab'] = "leave";
                $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
                break;

            case 'updatecredit':

                if ($_SESSION['s_employees_token'] == $parm[1]) {

                    try {

                        $leaveAllowed = LeaveAllowed::readID($parm[2] ?? 0);
                        $leaveAllowed->bindProperties($post);
                        $leaveAllowed->update();

                        Notification::updatedAlert();

                    } catch (LeaveAllowedException $e) {
                    }

                    $_SESSION['s_empleaves_tab'] = "leave";
                    $_SESSION['s_employees_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'template':

                if ($_SESSION['s_employees_token'] == $parm[1]) {
                    $user = User::readID((int)$parm[2] ?? 0);
                    $allowedLeaves = LeaveType::getEmployeeLeaves($_SESSION['organization'], $user);
                    $lastSelectedLeaves = $this->getSelectedUserLeaves($user);
                    LeaveAllowed::updateUserLeaves($post, $user->id);
                    $newSelectedLeaves = $this->getSelectedUserLeaves($user);
                    event('event.AllowedLeavesTemplateUpdated', [$allowedLeaves, $lastSelectedLeaves, $newSelectedLeaves, $user->full_name, $user->id]);
                    $_SESSION['s_empleaves_tab'] = "leave";
                    $_SESSION['s_employees_token'] = md5(rand(0000, 9999));

                }

                break;
        }

        LeaveData::updateEmployeeLeavesCredit($_SESSION['organization']->id, (int)$_SESSION['s_leave_user_id']);

        // Credit
        try {
            $this->Smarty->assign('crediteditList', Request::read([
                Request::ORG_ID => $_SESSION['organization']->id,
                Request::USER_ID => (int)$_SESSION['s_leave_user_id'],
                Request::PRG_ID => Program::PROGRAM_HUMAN_RESOURCE_P051,
                Request::TABLE_NAME => hr_creditedit::class
            ],
                [
                    0 => [
                        'property' => Request::ID,
                        'sort' => 'DESC'
                    ]
                ]));
        } catch (RequestException $e) {
        }

        try {
            $this->Smarty->assign('editcreditrequest_grf', wf_graph::count([Graph::OPR_ID => 650, Graph::STATUS => 1]));
        } catch (ModelException $e) {
            $this->Smarty->assign('editcreditrequest_grf', 0);
        }

        try {
            $this->Smarty->assign('Allowedleaves', LeaveAllowed::read([
                LeaveAllowed::ORG_ID => (int)$_SESSION['organization']->id,
                LeaveAllowed::USER_ID => (int)$_SESSION['s_leave_user_id'] ?? 0
            ]));
        } catch (LeaveAllowedException $e) {
        }

        try {
            $uaoRow = Vacant::readID($_SESSION['s_leave_uao_id'] ?? 0);
            $this->Smarty->assign('uaoRow', $uaoRow);
        } catch (VacantException $e) {
            $this->Smarty->assign('uaoRow', []);
        }

        try {
            $this->Smarty->assign('userRow', UserModel::has('basicJob')->find((int)$uaoRow->user_id));
        } catch (UserException $e) {
            $this->Smarty->assign('userRow', []);
        }

        if (!empty($uaoRow->att_doam_id)) {

            try {

                $employeeDoam = Doam::readID((int)$uaoRow->att_doam_id ?? 0);
                $workDays = explode(',', $employeeDoam->workdays);
                $this->Smarty->assign('workDays', $workDays ? $workDays : []);

            } catch (DoamException $e) {

                $this->Smarty->assign('workDays', []);

            }

        }

        $this->Smarty->assign('week_days', Setting::getList(25));

    }

    /**
     * @param $allowedLeave
     * @param $newCredit
     * @return bool
     * @internal param $post
     */
    private function balanceHitLimit($allowedLeave, $newCredit)
    {
        return ($allowedLeave->employeeLeaveCredit + $newCredit) <= $allowedLeave->max_credit;
    }


    public function Workadd($parm, $post)
    {
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Workedit($parm, $post)
    {

        try {

            $row = DBWork::readID((int)$parm[0]);

            if ($row->onwork == 'on') {
                $this->Smarty->assign('jscode',
                    "<script>$(document).ready(function(){ $('#search1').css('display','none'); });</script>");
            }

            $this->Smarty->assign('row', $row);

        } catch (DBWorkException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Workconfirm($parm, $post)
    {
        try {

            $this->Smarty->assign('row', DBWork::readID((int)$parm[0] ?? 0));

        } catch (DBWorkException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function eduConfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', DBEducation::readID((int)$parm[0] ?? 0));
        } catch (DBEducationException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }


    public function trainAdd($parm, $post)
    {
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function trainEdit($parm, $post)
    {

        try {
            $this->Smarty->assign('row', DBTraining::readID((int)$parm[0]));
        } catch (DBTrainingException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function trainConfirm($parm, $post)
    {

        try {
            $this->Smarty->assign('row', DBTraining::readID((int)$parm[0] ?? 0));
        } catch (DBTrainingException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }


    public function houseAdd($parm, $post)
    {
        $this->Smarty->assign('housing_type_list', Setting::getList(83));
        $this->Smarty->assign('owning_type_list', Setting::getList(84));
        $this->Smarty->assign('housing_size_list', Setting::getList(85));
        $this->Smarty->assign('housing_kind_list', Setting::getList(86));
        $this->Smarty->assign('userId', $parm[0]);

        $isFirstHouse = DB::table(db_houses::class)->where(DBHouses::USER_ID, $parm[0])->doesntExist();
        $this->Smarty->assign('isFirstHouse', $isFirstHouse);

    }

    public function housesEdit($parm, $post)
    {
        try {

            $house = DBHouses::readID((int)$parm[0]);

            $this->Smarty->assign('house', $house);

        } catch (DBHousesException $e) {

            $this->Smarty->assign('house', []);

        }

        $this->Smarty->assign('housing_type_list', Setting::getList(83));
        $this->Smarty->assign('owning_type_list', Setting::getList(84));
        $this->Smarty->assign('housing_size_list', Setting::getList(85));
        $this->Smarty->assign('housing_kind_list', Setting::getList(86));
        $this->Smarty->assign('health_status_list', Setting::getList(4));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));

        $isFirstHouse = DB::table(db_houses::class)
                ->where(DBHouses::USER_ID, $house->user_id)
                ->count() == 1;
        $this->Smarty->assign('isFirstHouse', $isFirstHouse);
    }

    public function housesConfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('house', DBHouses::readID((int)$parm[0] ?? 0));
        } catch (DBHousesException $e) {
            $this->Smarty->assign('house', []);
        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function incomeAdd($parm, $post)
    {
        $this->Smarty->assign('work_list', Setting::getList(81));
        $this->Smarty->assign('inc_montyly_list', Setting::getList(82));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function incomeEdit($parm, $post)
    {

        try {

            $row = DBIncome::readID((int)$parm[0]);

            if ($row->monthly_income_type == 365) {
                $this->Smarty->assign('codeone',
                    '<script>$(document).ready(function(){$("#365div").show("fast");$("#366div").hide("fast");});</script>');
            }
            if ($row->monthly_income_type == 366) {
                $this->Smarty->assign('codeone',
                    '<script>$(document).ready(function(){$("#365div").hide("fast");$("#366div").show("fast");});</script>');
            }

            $this->Smarty->assign('row', $row);

        } catch (DBIncomeException $e) {

            $this->Smarty->assign('row', []);

        }

        $this->Smarty->assign('work_list', Setting::getList(81));
        $this->Smarty->assign('inc_montyly_list', Setting::getList(82));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function incomeConfirm($parm, $post)
    {

        try {
            $this->Smarty->assign('row', DBIncome::readID((int)$parm[0] ?? 0));
        } catch (DBIncomeException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }


    public function vehicleAdd($parm, $post)
    {
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function vehicleEdit($parm, $post)
    {
        try {

            $row = DBVehical::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBVehicalException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function vehicleConfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', DBVehical::readID((int)$parm[0] ?? 0));
        } catch (DBVehicalException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }


    public function depitEdit($parm, $post)
    {
        try {
            $depit = DBDebt::readID((int)$parm[0]);
            $this->Smarty->assign('row', $depit);
        } catch (DBDeptException $e) {
            $this->Smarty->assign('row', []);
        }

        switch ($depit->type) {

            case 1206:
                $this->Smarty->assign('codeone', '<script>$(document).ready(function(){$("#loanTypes").show("fast");$("#InstallmentTypes").hide("fast");});</script>');
                break;

            case 1207:
                $this->Smarty->assign('codeone', '<script>$(document).ready(function(){$("#loanTypes").hide("fast");$("#InstallmentTypes").show("fast");});</script>');
                break;

        }

        $this->Smarty->assign('LoanAdvanceTypes', Setting::getList(273));
        $this->Smarty->assign('LoanTypes', Setting::getList(274));
        $this->Smarty->assign('InstallmentsTypes', Setting::getList(275));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function depitConfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', DBDebt::readID((int)$parm[0]));
        } catch (DBDeptException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }


    public function liveStockAdd($parm, $post)
    {
        $this->Smarty->assign('livestock_type_list', Setting::getList(271));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }


    public function liveStockEdit($parm, $post)
    {
        try {

            $row = DBLiveStock::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBLiveStockException $e) {

            $this->Smarty->assign('row', []);

        }
        $this->Smarty->assign('livestock_type_list', Setting::getList(271));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function workerAdd($parm, $post)
    {
        $this->Smarty->assign('country_list', Country::read([]));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function workerEdit($parm, $post)
    {
        try {

            $row = DBWorker::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBWorkerException $e) {

            $this->Smarty->assign('row', []);

        }
        $this->Smarty->assign('country_list', Country::read([]));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }


    public function workerConfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', DBWorker::readID((int)$parm[0] ?? 0));
        } catch (DBWorkerException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function addProfilePicture($parm, $post)
    {

        try {
            $this->Smarty->assign('operation', Operation::readByCode('UserProfilePicture'));
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        try {
            $this->Smarty->assign('document_max_size_limit', number_format((((int)(new ConfigurationParser(CLIENT_CONFIG))->getSection('document')['document_max_size_limit']) / 1024) / 1024, 2, '.', ','));
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('document_max_size_limit', 0);
        }

        $_SESSION['s_resume_token'] = md5(rand(0, 10000000));
    }


    public function editProfilePicture($parm, $post)
    {

        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

    }

    public function deleteProfilePicture($parm, $post)
    {

        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

    }

    public function depitAdd($parm, $post)
    {
        $this->Smarty->assign('LoanAdvanceTypes', Setting::getList(273));
        $this->Smarty->assign('LoanTypes', Setting::getList(274));
        $this->Smarty->assign('InstallmentsTypes', Setting::getList(275));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function confirmcredit($parm, $post)
    {
        try {
            $this->Smarty->assign('row', LeaveCreditRequest::readID((int)$parm[0] ?? 0));
        } catch (LeaveCreditRequestException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_empleaves_tab'] = "credit";
        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function editcredit($parm, $post)
    {

        try {
            $this->Smarty->assign('row', LeaveCreditRequest::readID((int)$parm[0] ?? 0));
        } catch (LeaveCreditRequestException $e) {
            $this->Smarty->assign('row', []);
        }

        try {

            $this->Smarty->assign('levstList', LeaveAllowed::read([
                LeaveAllowed::ORG_ID => $_SESSION['organization']->id,
                LeaveAllowed::USER_ID => $_SESSION['s_leave_user_id'] ?? 0
            ]));

        } catch (LeaveAllowedException $e) {
            $this->Smarty->assign('levstList', []);
        }

        $this->Smarty->assign('creditoperations', Setting::getList(182));
        $_SESSION['s_empleaves_tab'] = "credit";
        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }


    public function employeeleavesetting($parm, $post)
    {
        try {
            $this->Smarty->assign('row', LeaveAllowed::readID((int)$parm[0] ?? 0));
        } catch (LeaveAllowedException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function documents($parm, $post, $files)
    {
        $this->Smarty->assign('breadcrumb_sub_title', translate('gnr_documents'));

        switch ($parm[0]) {

            case 'save_session':
                $_SESSION['s_doc_employee_user_id'] = $parm[1];
                break;

            case 'insert':

                if ($_SESSION['s_employees_token'] === $parm[1]) {

                    try {

                        $document = new Document();
                        $post['permission'] = isset($post['permission']) ? json_encode($post['permission']) : null;
                        $document->bindProperties($post);
                        $document->fileArray = $files['fileArray'];
                        $document->client_id = $_SESSION['organization']->id;
                        $document->user_id = $_SESSION['user']->id;
                        $document->operation_code = 'userdata';
                        $document->table_name = 'sh_user';
                        $document->row_id = (int)$_SESSION['s_doc_employee_user_id'] ?? 0;
                        $document->created_by = $_SESSION['user']->id;
                        $document->created_date = date('Y-m-d');
                        $document->save();

                    } catch (DocumentException $e) {

                    }

                }
                break;

            case 'update':

                if ($_SESSION['s_employees_token'] === $parm[1]) {

                    try {

                        $document = Document::readID((int)$parm[2] ?? 0);
                        $post['permission'] = isset($post['permission']) ? json_encode($post['permission']) : null;
                        $document->bindProperties($post);
                        $document->update();

                    } catch (DocumentException $e) {

                    }

                    $this->Smarty->assign('alertmessage', 'update');

                }

                break;

            case 'delete':

                if ($_SESSION['s_employees_token'] === $parm[1]) {

                    try {

                        $document = Document::readID((int)$parm[2] ?? 0);
                        $document->delete();

                    } catch (DocumentException $e) {

                    }

                    $this->Smarty->assign('alertmessage', 'delete');
                }

                break;
        }

        try {
            $this->Smarty->assign('user', UserModel::has('basicJob')->find((int)$_SESSION['s_doc_employee_user_id']));
        } catch (UserException $e) {
            $this->Smarty->assign('user', []);
        }

        try {

            $this->Smarty->assign('documents', Document::read([
                Document::CLIENT_ID => $_SESSION['organization']->id,
                Document::OPERATION_CODE => 'userdata',
                Document::ROW_ID => $_SESSION['s_doc_employee_user_id']
            ]));

        } catch (DocumentException $e) {
            $this->Smarty->assign('documents', []);
        }

    }

    public function showResume($parm, $post)
    {

        $this->Smarty->assign('breadcrumb_sub_title', translate('p_data'));

        $_SESSION['s_active_cv_tab'] = 'PrimaryData';

        try {

            $user = User::readID((int)$parm[0]);
            $this->Smarty->assign('user', $user);

            /*
         * Education Data
         */
            try {
                $this->Smarty->assign('db_edu_list', DBEducation::read([
                    DBEducation::USER_ID => $user->id
                ], [
                    0 => [
                        'property' => DBEducation::DATE,
                        'sort' => 'ASC'
                    ]
                ]));
            } catch (DBEducationException $e) {
            }

            /*
             * Work Data
             */
            try {
                $this->Smarty->assign('db_work_list', DBWork::read([
                    DBWork::USER_ID => $user->id
                ], [
                    0 => [
                        'property' => DBWork::FROM,
                        'sort' => 'ASC'
                    ]
                ]));
            } catch (DBWorkException $e) {
            }

            /*
             * Family Data
             */
            try {

                $this->Smarty->assign('db_family_list', DBFamily::read([
                    DBFamily::USER_ID => $user->id
                ], [
                    0 => [
                        'property' => DBFamily::BIRTH_DATE,
                        'sort' => 'ASC'
                    ]
                ]));

            } catch (DBFamilyException $e) {

            }

            /*
             * Train Data
             */
            try {
                $this->Smarty->assign('db_train_list', DBTraining::read([
                    DBTraining::USER_ID => $user->id
                ]));
            } catch (DBTrainingException $e) {
            }

            /*
             * Income Data
             */
            try {
                $this->Smarty->assign('db_inc_list', DBIncome::read([
                    DBIncome::USER_ID => $user->id
                ]));
            } catch (DBIncomeException $e) {
            }

            try {
                $this->Smarty->assign('houses_list', DBHouses::read([
                    DBHouses::USER_ID => $user->id
                ]));
            } catch (DBHousesException $e) {
            }

            /*
             * Vehical Data
             */
            try {
                $this->Smarty->assign('db_vehicle_list', DBVehical::read([
                    DBVehical::USER_ID => $user->id
                ]));
            } catch (DBVehicalException $e) {
                $this->Smarty->assign('db_vehicle_list', []);
            }

            /*
             * Worker Data
             */
            try {
                $this->Smarty->assign('db_worker_list', DBWorker::read([
                    DBWorker::USER_ID => $user->id
                ]));
            } catch (DBWorkerException $e) {
            }

            /*
             * Debt Data
             */
            try {
                $this->Smarty->assign('db_dept_list', DBDebt::read([
                    DBDebt::USER_ID => $user->id
                ]));
            } catch (DBDeptException $e) {
            }

            /*
             * LiveStock Data
             */
            try {
                $this->Smarty->assign('db_livestock_list', DBLiveStock::read([
                    DBLiveStock::USER_ID => $user->id
                ]));
            } catch (DBLiveStockException $e) {
            }

            /*
             * Housing Data
             */

            $this->Smarty->assign('userclassifications', explode(',', $user->classification));

            $userTabArray = array();
            foreach ($user->classification as $class) {

                if (!empty($class)) {

                    try {

                        $classRow = UserClass::readID((int)$class);
                        $classTabsArray = explode(',', $classRow->userdata_ids);
                        foreach ($classTabsArray as $tab) {
                            if (!empty($tab) && !in_array($tab, $userTabArray)) {
                                $userTabArray[] = $tab;
                            }
                        }

                    } catch (UserClassException $e) {
                    }

                }
            }
            if (count($userTabArray) >= 1) {
                $this->Smarty->assign('allowedUserClasses', Setting::settingsInArray(167, $userTabArray));
            }


            if ($user->housing_type == 367) {
                $this->Smarty->assign('codeone',
                    '<script>$(document).ready(function(){$("#367div").show("fast");$("#368div").hide("fast");});</script>');
            }

            if ($user->housing_type == 368) {
                $this->Smarty->assign('codeone',
                    '<script>$(document).ready(function(){$("#367div").hide("fast");$("#368div").show("fast");});</script>');
            }

        } catch (UserException $e) {
            $this->Smarty->assign('user', []);
        }


    }

    public function eduEdit($parm, $post)
    {

        try {
            $this->Smarty->assign('row', DBEducation::readID((int)$parm[0]));
        } catch (DBEducationException $e) {
        }

        $this->Smarty->assign('edu_type_list', Setting::getList(2));
        $this->Smarty->assign('edu_field_list', Setting::getList(61));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function resumeDocuments($parm, $post, $files)
    {

        $this->Smarty->assign('breadcrumb_sub_title',
            translate('data') . ' / ' . translate('gnr_documents')
        );

        switch ($parm[0]) {
            case 'save_session':
                $_SESSION['s_document_operation_code'] = $parm[1];
                $_SESSION['s_document_table_name'] = $parm[2];
                $_SESSION['s_document_row_id'] = $parm[3];
                $_SESSION['s_document_user_id'] = $parm[4];
                if ($parm[5]) {
                    $_SESSION['s_document_back_path'] = implode(
                        '/',
                        array_slice(explode('/', $this->URL->currentUrlPath()), 11)
                    );
                } else {
                    $_SESSION['s_document_back_path'] = null;
                }
                break;

            case 'insert':
                if ($_SESSION['s_documents_token'] === $parm[1]) {

                    try {

                        $document = new Document();
                        $post['permission'] = isset($post['permission']) ? json_encode($post['permission']) : null;
                        $document->bindProperties($post);
                        $document->fileArray = $files['fileArray'];
                        $document->client_id = $_SESSION['organization']->id;
                        $document->user_id = $_SESSION['s_document_user_id'];
                        $document->operation_code = $_SESSION['s_document_operation_code'];
                        $document->table_name = $_SESSION['s_document_table_name'];
                        $document->row_id = $_SESSION['s_document_row_id'];
                        $document->created_by = $_SESSION['user']->id;
                        $document->created_date = date('Y-m-d');
                        if (empty($post['name'])) {
                            $documentName = explode('.', $files['fileArray']['name']);
                            $document->name = $documentName[0];
                        }
                        if ($document->save()) {
                            Notification::createdAlert();
                        }

                    } catch (DocumentException $e) {
                        /**
                         * @TODO handling other errors and display some feed back for end user
                         */
                        $feedback = ($e->getCode() === self::CODE_FILE_TYPE_NOT_ALLOWED) ? self::MESSAGE_GNR_FILE_TYPE_NOT_ALLOWED : '';
                        Notification::alertMessage(Notification::ERROR, $feedback);
                    }

                }

                break;

            case 'update':

                if ($_SESSION['s_documents_token'] === $parm[1]) {

                    try {

                        $document = Document::readID((int)$parm[2] ?? 0);
                        $post['permission'] = isset($post['permission']) ? json_encode($post['permission']) : null;
                        $document->bindProperties($post);
                        $document->update();

                        Notification::updatedAlert();

                    } catch (DocumentException $e) {
                        /**
                         * @TODO handling other errors and display some feed back for end user
                         */
                        $feedback = ($e->getCode() === self::CODE_FILE_TYPE_NOT_ALLOWED) ? self::MESSAGE_GNR_FILE_TYPE_NOT_ALLOWED : '';
                        Notification::alertMessage(Notification::ERROR, $feedback);
                    }

                }

                break;

            case 'delete':

                if ($_SESSION['s_documents_token'] === $parm[1]) {

                    try {

                        $document = Document::readID((int)$parm[2] ?? 0);
                        $document->delete();

                        Notification::deletedAlert();

                    } catch (DocumentException $e) {

                    }
                }

                break;
        }

        try {
            $this->Smarty->assign(
                'documents',
                Document::read(
                    [
                        Document::OPERATION_CODE => $_SESSION['s_document_operation_code'],
                        Document::TABLE_NAME => $_SESSION['s_document_table_name'],
                        Document::ROW_ID => $_SESSION['s_document_row_id'],
                    ]
                )
            );
        } catch (DocumentException $e) {

        }

        try {
            $this->Smarty->assign(
                'operation',
                Operation::readByCode((string)$_SESSION['s_document_operation_code'] ?? '')
            );
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        $_SESSION['s_documents_token'] = md5(rand(0, 10000000));
    }

    //========= Documents ==========
    public function createDocument($parm, $post)
    {

        try {
            $this->Smarty->assign(
                'operation',
                Operation::readByCode((string)$_SESSION['s_document_operation_code'] ?? '')
            );
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        try {
            $this->Smarty->assign(
                'document_max_size_limit',
                number_format(
                    (((int)(new ConfigurationParser(CLIENT_CONFIG))->getSection(
                            'document'
                        )['document_max_size_limit']) / 1024) / 1024,
                    2,
                    '.',
                    ','
                )
            );
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('document_max_size_limit', 0);
        }

        $_SESSION['s_documents_token'] = md5(rand(0, 10000000));
    }

    public function settoBasicVacant($parm, $post)
    {
        try {

            $this->Smarty->assign('vacant', Vacant::readID((int)$parm[0] ?? 0));

        } catch (VacantException $e) {
            $this->Smarty->assign('vacant', []);
        }

        $_SESSION['s_empdata_token'] = md5(rand(0, 99999));
    }

    public function addDocument($parm, $post)
    {
        $this->Smarty->assign('user', User::readID((int)$_SESSION['s_doc_employee_user_id'] ?? 0));

        try {
            $this->Smarty->assign('operation', Operation::readByCode((string)$parm[0] ?? ''));
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        try {
            $this->Smarty->assign('document_max_size_limit',
                number_format((((int)(new ConfigurationParser(CLIENT_CONFIG))->getSection('document')['document_max_size_limit']) / 1024) / 1024,
                    2, '.', ','));
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('document_max_size_limit', 0);
        }

        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function documentdit($parm, $post)
    {
        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

        try {
            $this->Smarty->assign('operation', Operation::readByCode((string)$parm[1] ?? ''));
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        try {
            $this->Smarty->assign('document_max_size_limit',
                number_format((((int)(new ConfigurationParser(CLIENT_CONFIG))->getSection('document')['document_max_size_limit']) / 1024) / 1024,
                    2, '.', ','));
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('document_max_size_limit', 0);
        }


        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function documentconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function documentDownload($parm, $post, $files)
    {
        try {

            $document = Document::readID((int)$parm[0] ?? 0);
            $document->forceDownload();

        } catch (DocumentException $e) {

        }

    }

    public function editDocument($parm, $post)
    {

        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

        try {
            $this->Smarty->assign(
                'operation',
                Operation::readByCode((string)$_SESSION['s_document_operation_code'] ?? '')
            );
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        try {
            $this->Smarty->assign(
                'document_max_size_limit',
                number_format(
                    (((int)(new ConfigurationParser(CLIENT_CONFIG))->getSection(
                            'document'
                        )['document_max_size_limit']) / 1024) / 1024,
                    2,
                    '.',
                    ','
                )
            );
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('document_max_size_limit', 0);
        }

        $_SESSION['s_documents_token'] = md5(rand(0, 10000000));

    }

    public function preview($parm, $post, $files)
    {
        try {
            $documentId = isset($parm[0]) ? (int)$parm[0] : 0;
            $document = Document::readID($documentId);
            $document->forcePreview();
        } catch (DocumentException $e) {
            redirect('page403/show');
        }
    }
    public function addcredit($parm, $post)
    {

        if (!empty($_SESSION['s_leave_user_id'])) {

            try {

                $this->Smarty->assign('levstList', LeaveAllowed::read([
                    LeaveAllowed::ORG_ID => $_SESSION['organization']->id,
                    LeaveAllowed::USER_ID => $_SESSION['s_leave_user_id']
                ]));

            } catch (LeaveAllowedException $e) {

                $this->Smarty->assign('levstList', []);

            }

        }

        $this->Smarty->assign('creditoperations', Setting::getList(182));
        $_SESSION['s_empleaves_tab'] = "credit";
        $_SESSION['s_employees_token'] = md5(rand(0000, 9999));
    }

    public function resume($parm, $post, $files)
    {

        $this->Smarty->assign('breadcrumb_sub_title', translate('gnr_data'));

        global $snso;

        switch ($parm[0]) {

            case 'save_session':
                $_SESSION['s_current_user_id'] = (int)$parm[1] ?? 0;
                $_SESSION['s_back_bnd'] = $parm[2];
                $_SESSION['s_back_prg'] = $parm[3];
                $_SESSION['s_back_opr'] = $parm[4];
                $_SESSION['s_back_sub'] = $parm[5];
                $_SESSION['pagn'] = $parm[6];
                $_SESSION['lang'] = $parm[7];
                $_SESSION['extra'] = $parm[8];
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'PrimaryData';
                $_SESSION['s_active_debt_type_tab'] = 'debt';
                $_SESSION['s_active_debt_data_tab'] = 'person';
                break;

            case 'PrimaryData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'PrimaryData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            $this->Smarty->assign('alertmessage', 'insert');
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $user = User::readID((int)$_SESSION['s_current_user_id']);
                                $user->bindProperties($post);
                                $user->bank_name = ProcessDataService::getBankName(Setting::getList(291), $post);
                                $user->bank_number = str_replace(' ', '', $post['bank_number']);
                                $user->identity_essue_date = $this->Date->get_date('ad', $post['identity_essue_date']);
                                $user->identity_expairy_date = $this->Date->get_date('ad', $post['identity_expairy_date']);

                                if ($user->address_neighborhood) {

                                    try {
                                        $neighborhood = st_neighborhood::readByID((int)$user->address_neighborhood);
                                    } catch (ModelException $e) {
                                        $neighborhood = null;
                                    }

                                    if ($neighborhood) {
                                        $user->country = $neighborhood->st_neighborhood_country_id;
                                        $user->address_region = $neighborhood->st_neighborhood_region_id;
                                        $user->address_city = $neighborhood->st_neighborhood_city_id;
                                    }

                                }

                                if (empty($post['bank_code'])) {
                                    $user->bank_name = $post['bank_name'];
                                    $user->bank_code = "";
                                }
                                $user->birth_date = $this->Date->get_date('ad', $post['birth_date']);
                                $user->age = Helper::calculateAge($user);
                                $user->save();
                                Notification::updatedAlert();

                            } catch (UserException $e) {
                            }

                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            Notification::deletedAlert();
                        }

                        break;

                }
                break;

            case 'EducationalData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'EducationalData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $edu = new DBEducation();
                                $edu->bindProperties($post);
                                $edu->user_id = $_SESSION['s_current_user_id'];
                                $edu->date = $this->Date->get_date('ad', $post['date']);
                                $edu->save();

                                Notification::createdAlert();
                            } catch (DBEducationException $e) {

                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));

                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $edu = DBEducation::readID((int)$parm[3] ?? 0);
                                $edu->bindProperties($post);
                                $edu->date = $this->Date->get_date('ad', $post['date']);
                                $edu->save();
                                Notification::updatedAlert();
                            } catch (DBEducationException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBEducation::readID((int)$parm[3] ?? 0)->delete();
                                $this->DB->delete('db_edu', $parm[3]);
                                Notification::deletedAlert();
                            } catch (DBEducationException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'ExperianceData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'ExperianceData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            $work = new DBWork();
                            $work->bindProperties($post);
                            $work->from = $this->Date->get_date('ad', $post['from']);
                            $work->to = $this->Date->get_date(1, $post['to']);
                            $work->user_id = $_SESSION['s_current_user_id'];
                            if ($post['onwork'] == 'on') {
                                $work->onwork = 'on';
                            } else {
                                $work->onwork = '';
                            }
                            $work->save();
                            Notification::createdAlert();
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $work = DBWork::readID((int)$parm[3]);
                                $work->bindProperties($post);
                                $work->from = $this->Date->get_date('ad', $post['from']);
                                $work->to = $this->Date->get_date('ad', $post['to']);
                                if ($post['onwork'] == 'on') {
                                    $work->onwork = 'on';
                                } else {
                                    $work->onwork = '';
                                }
                                $work->save();
                                Notification::updatedAlert();
                            } catch (DBWorkException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBWork::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBWorkException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'FamilyData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'FamilyData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $family_member = new DBFamily();
                                $family_member->bindProperties($post);
                                $family_member->user_id = $_SESSION['s_current_user_id'];
                                $family_member->birth_date = $this->Date->get_date('ad', $post['birth_date']);
                                $family_member->save();
                                Notification::createdAlert();
                            } catch (DBFamilyException $e) {

                            }

                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $family_member = DBFamily::readID((int)$parm[3]);
                                $family_member->bindProperties($post);
                                $family_member->birth_date = $this->Date->get_date('ad', $post['birth_date']);
                                $family_member->save();
                                Notification::updatedAlert();
                            } catch (DBFamilyException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBFamily::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBFamilyException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'TrainingData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'TrainingData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            $training = new DBTraining();
                            $training->bindProperties($post);
                            $training->user_id = $_SESSION['s_current_user_id'];
                            $training->days = (int)($post['days'] ?? 0);
                            $training->hours = (int)($post['hours'] ?? 0);
                            $training->issuedate = $this->Date->get_date('ad', $post['issuedate']);
                            $training->save();
                            Notification::createdAlert();
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $training = DBTraining::readID((int)$parm[3]);
                                $training->bindProperties($post);
                                $training->issuedate = $this->Date->get_date('ad', $post['issuedate']);
                                $training->save();
                                Notification::updatedAlert();
                            } catch (DBTrainingException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBTraining::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBTrainingException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'Houses':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'HomeAddress';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {

                                $house = new DBHouses();
                                $house->bindProperties($post);
                                $house->user_id = $parm[3];
                                $house->contract_from = $this->Date->get_date('ad', $post['contract_from']);
                                $house->contract_to = $this->Date->get_date('ad', $post['contract_to']);

                                if ($post['status'] == 1) {
                                    $house->status = 1;
                                } else {
                                    $house->status = 0;
                                }

                                $house->save();

                                Notification::createdAlert();

                            } catch (DBHousesException $e) {

                            }

                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));

                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $house = DBHouses::readID((int)$parm[3]);
                                $house->bindProperties($post);
                                $house->contract_from = $this->Date->get_date('ad', $post['contract_from']);
                                $house->contract_to = $this->Date->get_date('ad', $post['contract_to']);

                                if ($post['status'] == 1) {
                                    $house->status = 1;
                                } else {
                                    $house->status = 0;
                                }

                                $house->save();
                                Notification::updatedAlert();
                            } catch (DBHousesException $e) {
                            }

                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBHouses::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBHousesException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'IncomeData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'IncomeData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $income = new DBIncome();
                                $income->bindProperties($post);
                                $income->monthly_incole_vfrom = $this->Date->get_date('ad', $post['monthly_incole_vfrom']);
                                $income->monthly_incole_vto = $this->Date->get_date('ad', $post['monthly_incole_vto']);
                                $income->user_id = $_SESSION['s_current_user_id'];
                                $income->created_date = date('Y-m-d');
                                $income->save();
                                Notification::createdAlert();
                            } catch (DBIncomeException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $income = DBIncome::readID((int)$parm[3]);
                                $income->bindProperties($post);
                                $income->monthly_incole_vfrom = $this->Date->get_date('ad', $post['monthly_incole_vfrom']);
                                $income->monthly_incole_vto = $this->Date->get_date('ad', $post['monthly_incole_vto']);
                                $income->save();
                                Notification::updatedAlert();
                            } catch (DBIncomeException $e) {
                            }

                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBIncome::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBIncomeException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'VehicleData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'VehicleData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $vehicle = new DBVehical();
                                $vehicle->user_id = $_SESSION['s_current_user_id'];
                                $vehicle->bindProperties($post);

                                if ($post['type']) {
                                    $vehicle->type = $post['type'];
                                }
                                if ($post['model']) {
                                    $vehicle->model = $post['model'];
                                }
                                $vehicle->created_date = date('Y-m-d');

                                $vehicle->save();

                                Notification::createdAlert();
                            } catch (DBVehicalException $e) {

                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $vehicle = DBVehical::readID((int)$parm[3]);
                                if ($post['type']) {
                                    $vehicle->type = $post['type'];
                                }
                                if ($post['model']) {
                                    $vehicle->model = $post['model'];
                                }
                                $vehicle->save();

                                Notification::updatedAlert();
                            } catch (DBVehicalException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBVehical::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBVehicalException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'WorkerData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'WorkerData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $worker = new DBWorker();
                                $worker->user_id = $_SESSION['s_current_user_id'];
                                $worker->bindProperties($post);

                                if ($post['stillworking']) {
                                    $worker->stillworking = $post['stillworking'];
                                }
                                if ($post['type']) {
                                    $worker->type = $post['type'];
                                }
                                if ($post['nationality']) {
                                    $worker->nationality = $post['nationality'];
                                }
                                $worker->created_date = date('Y-m-d');

                                $worker->save();

                                Notification::createdAlert();
                            } catch (DBWorkerException $e) {

                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $worker = DBWorker::readID((int)$parm[3]);

                                if ($post['stillworking']) {
                                    $worker->stillworking = $post['stillworking'];
                                }
                                if ($post['type']) {
                                    $worker->type = $post['type'];
                                }
                                if ($post['nationality']) {
                                    $worker->nationality = $post['nationality'];
                                }
                                $worker->save();

                                Notification::updatedAlert();
                            } catch (DBWorkerException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBWorker::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBWorkerException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'DebtData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'DebtData';
                $_SESSION['s_active_debt_type_tab'] = 'debt';
                $_SESSION['s_active_debt_data_tab'] = 'person';
                switch ($parm[1]) {
                    case 'insert':

                        $fromDate = Carbon::parse($post['from_date']);
                        $toDate = Carbon::parse($post['to_date']);

                        if ($_SESSION['s_resume_token'] == $parm[2]) {

                            if ($fromDate->lt($toDate)) {
                                try {
                                    $debt = new DBDebt();
                                    $debt->bindProperties($post);
                                    $debt->user_id = $_SESSION['s_current_user_id'];
                                    $debt->created_by = $_SESSION['s_current_user_id'];
                                    $debt->created_date = date('Y-m-d');
                                    $debt->save();

                                    Notification::createdAlert();
                                } catch (DBDeptException $e) {

                                }
                            } else {
                                Notification::alertMessage(Notification::WARNING, "p_to_date_must_be_greater_than_to_date");
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {

                            $fromDate = Carbon::parse($post['from_date']);
                            $toDate = Carbon::parse($post['to_date']);

                            if ($fromDate->lt($toDate)) {
                                try {
                                    $debt = DBDebt::readID((int)$parm[3]);
                                    $debt->bindProperties($post);
                                    switch ((int)$post['type']) {
                                        case 1206:
                                            $debt->installment_type = null;
                                            break;
                                        case 1207:
                                            $debt->loan_type = null;
                                            break;
                                    }
                                    $debt->save();
                                    Notification::updatedAlert();
                                } catch (DBDeptException $e) {
                                }
                            } else {
                                Notification::alertMessage(Notification::WARNING, "p_to_date_must_be_greater_than_to_date");
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBDebt::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBDeptException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'LiveStockData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'LiveStockData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $liveStock = new DBLiveStock();
                                $liveStock->user_id = $_SESSION['s_current_user_id'];
                                $liveStock->bindProperties($post);

                                if ($post['amount']) {
                                    $liveStock->amount = $post['amount'];
                                }
                                if ($post['type']) {
                                    $liveStock->type = $post['type'];
                                }
                                $liveStock->created_date = date('Y-m-d');

                                $liveStock->save();

                                Notification::createdAlert();
                            } catch (DBLiveStockException $e) {

                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $liveStock = DBLiveStock::readID((int)$parm[3]);
                                if ($post['amount']) {
                                    $liveStock->amount = $post['amount'];
                                }
                                if ($post['type']) {
                                    $liveStock->type = $post['type'];
                                }
                                $liveStock->save();

                                Notification::updatedAlert();
                            } catch (DBLiveStockException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBLiveStock::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBLiveStockException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'tab':
                $_SESSION['s_active_userdata_tab'] = 'userImage';
                $_SESSION['s_active_cv_tab'] = 'PrimaryData';

                break;

            case 'Classifications':
                if ($_SESSION['s_resume_token'] == $parm[1]) {

                    try {

                        $user = User::readID((int)$_SESSION['s_current_user_id']);
                        $user->classification = implode(',', $post['classification']);
                        $user->save();

                        Notification::updatedAlert();

                    } catch (UserException $e) {
                    }

                    $_SESSION['s_active_userdata_tab'] = 'userClassification';
                    $_SESSION['s_resume_token'] = md5(rand(0000, 9999));

                }
                break;

            case 'documents':

                switch ($parm[1]) {

                    case 'addProfilePicture':

                        if ($_SESSION['s_resume_token'] === $parm[2]) {

                            try {

                                $document = new Document();
                                $document->bindProperties($post);
                                $document->fileArray = $files['fileArray'];
                                $document->client_id = $_SESSION['organization']->id;
                                $document->user_id = $_SESSION['s_current_user_id'];
                                $document->operation_code = 'UserProfilePicture';
                                $document->table_name = 'sh_user';
                                $document->row_id = $_SESSION['s_current_user_id'];
                                $document->created_by = $_SESSION['s_current_user_id'];
                                $document->created_date = date('Y-m-d');
                                $document->save();

                                Notification::createdAlert();

                            } catch (DocumentException $e) {

                            }

                        }

                        $_SESSION['s_active_userdata_tab'] = 'userImage';
                        break;

                    case 'editProfilePicture':

                        if ($_SESSION['s_resume_token'] === $parm[2]) {

                            try {

                                $document = Document::readID((int)$parm[3] ?? 0);
                                $document->bindProperties($post);
                                $document->update();

                                Notification::updatedAlert();

                            } catch (DocumentException $e) {

                            }

                        }
                        $_SESSION['s_active_userdata_tab'] = 'userImage';
                        break;

                    case 'deleteProfilePicture':

                        if ($_SESSION['s_resume_token'] === $parm[2]) {

                            try {
                                $document = Document::readID((int)$parm[3] ?? 0);
                                $document->delete();

                                Notification::deletedAlert();
                            } catch (DocumentException $e) {

                            }

                        }
                        $_SESSION['s_active_userdata_tab'] = 'userImage';
                        break;

                    case 'activationProfilePicture':

                        if ($_SESSION['s_resume_token'] === $parm[2]) {

                            try {

                                $document = Document::readID((int)$parm[3] ?? 0);
                                $document->activate();

                                Notification::updatedAlert();

                            } catch (DocumentException $e) {

                            }

                            try {

                                User::updateCurrentUserSessionObject();

                            } catch (UserException $e) {

                            }

                        }
                        $_SESSION['s_active_userdata_tab'] = 'userImage';
                        break;
                }
                break;

            default:
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'PrimaryData';
                $_SESSION['s_current_user_id'] = (int)$parm[0] ?? 0;

                break;
        }

        /*
         * PrimaryData Data
         */
        try {

            $user = User::readID((int)$_SESSION['s_current_user_id']);
            $this->Smarty->assign('user', $user);
            $this->Smarty->assign('userclassifications', explode(',', $user->classification));

        } catch (UserException $e) {

            $this->Smarty->assign('user', []);

        }

        $this->Smarty->assign('social_list', Setting::getList(5));

        try {
            $this->Smarty->assign('nationality_list', Country::read());
        } catch (CountryException $e) {
            $this->Smarty->assign('nationality_list', []);
        }

        $this->Smarty->assign('identity_list', Setting::getList(6));

        $this->Smarty->assign('bank_list', Setting::getList(291));

        $this->Smarty->assign('work_status_list', Setting::getList(280));

        $this->Smarty->assign('educational_levels_list', Setting::getList(2));

        try {
            $this->Smarty->assign('neighborhoods', Neighborhood::read([Neighborhood::ORG_ID => $_SESSION['organization']->id, Neighborhood::ACTIVATION => Setting::ACTIVE]));
        } catch (NeighborhoodException $e) {
            $this->Smarty->assign('neighborhoods', []);
        }

        /*
         * Education Data
         */
        try {
            $this->Smarty->assign('db_edu_list', DBEducation::read([
                DBEducation::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBEducationException $e) {
            $this->Smarty->assign('db_edu_list', []);
        }

        /*
         * Work Data
         */
        try {
            $this->Smarty->assign('db_work_list', DBWork::read([
                DBWork::USER_ID => $_SESSION['s_current_user_id']
            ], [
                0 => [
                    'property' => DBWork::FROM,
                    'sort' => 'ASC'
                ]
            ]));
        } catch (DBWorkException $e) {
            $this->Smarty->assign('db_work_list', []);
        }

        /*
         * Family Data
         */
        try {
            $this->Smarty->assign('db_family_list', DBFamily::read([
                DBFamily::USER_ID => $_SESSION['s_current_user_id']
            ], [
                0 => [
                    'property' => DBFamily::BIRTH_DATE,
                    'sort' => 'ASC'
                ]
            ]));
        } catch (DBFamilyException $e) {
            $this->Smarty->assign('db_family_list', []);
        }

        /*
         * Train Data
         */
        try {
            $this->Smarty->assign('db_train_list', DBTraining::read([
                DBTraining::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBTrainingException $e) {
            $this->Smarty->assign('db_train_list', []);
        }

        /*
         * Income Data
         */
        try {
            $this->Smarty->assign('db_inc_list', DBIncome::read([
                DBIncome::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBIncomeException $e) {
            $this->Smarty->assign('db_inc_list', []);
        }

        /*
         * Houses Data
         */
        try {
            $this->Smarty->assign('houses_list', DBHouses::read([
                DBHouses::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBHousesException $e) {
            $this->Smarty->assign('houses_list', []);
        }

        /*
         * Vehical Data
         */
        try {
            $this->Smarty->assign('db_vehicle_list', DBVehical::read([
                DBVehical::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBVehicalException $e) {
            $this->Smarty->assign('db_vehicle_list', []);
        }

        /*
        * Worker Data
        */
        try {
            $this->Smarty->assign('db_worker_list', DBWorker::read([
                DBWorker::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBWorkerException $e) {
            $this->Smarty->assign('db_worker_list', []);
        }

        /*
        * Debt Data
        */
        try {
            $this->Smarty->assign('db_debt_list', DBDebt::read([
                DBDebt::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBDeptException $e) {
            $this->Smarty->assign('db_debt_list', []);
        }

        /*
        * LiveStock Data
        */
        try {
            $this->Smarty->assign('db_livestock_list', DBLiveStock::read([
                DBLiveStock::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBLiveStockException $e) {
            $this->Smarty->assign('db_livestock_list', []);
        }

        $classes_id = explode(',', $user->classification);
        $userTabArray = array();
        foreach ($classes_id as $class) {
            try {

                $classRow = UserClass::readID((int)$class);
                $classTabsArray = explode(',', $classRow->userdata_ids);
                foreach ($classTabsArray as $tab) {
                    if (!empty($tab) && !in_array($tab, $userTabArray)) {
                        $userTabArray[] = $tab;
                    }
                }

            } catch (UserClassException $e) {

            }
        }

        if (count($userTabArray) >= 1) {
            $this->Smarty->assign('allowedUserClasses', Setting::settingsInArray(167, $userTabArray));
        }

        // change profile picture
        try {
            $this->Smarty->assign('userProfilePictures', Document::read([
                Document::CLIENT_ID => $_SESSION['organization']->id,
                Document::USER_ID => $_SESSION['s_current_user_id'],
                Document::OPERATION_CODE => 'UserProfilePicture'
            ]));
        } catch (DocumentException $e) {
            $this->Smarty->assign('userProfilePictures', []);
        }

        ///////////////////////////////////////////////////////////////

        $this->Smarty->assign('housing_type_list', Setting::getList(83));
        $this->Smarty->assign('owning_type_list', Setting::getList(84));
        $this->Smarty->assign('housing_size_list', Setting::getList(85));
        $this->Smarty->assign('housing_kind_list', Setting::getList(86));
        $this->Smarty->assign('health_status_list', Setting::getList(269));

        switch ($user->housing_type) {
            case 367:
                $this->Smarty->assign('codeone',
                    '<script>$(document).ready(function(){$("#367div").show("fast");$("#368div").hide("fast");});</script>');
                break;
            case 368:
                $this->Smarty->assign('codeone',
                    '<script>$(document).ready(function(){$("#367div").hide("fast");$("#368div").show("fast");});</script>');
                break;
            default:

                break;
        }

        try {
            $this->Smarty->assign('User_Type_List', UserClass::read([
                UserClass::ORG_ID => $_SESSION['organization']->id
            ]));
        } catch (UserClassException $e) {
        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function DismissalProcedures($parm, $post)
    {
        $this->Smarty->assign('breadcrumb_sub_title', translate('dismissal_process'));
        $employee = Vacant::getDeviceOfFingerPrintDevice($parm[0]);
        if($parm[1] === 'FingerPrintAndDoamTab'){
            $this->Smarty->assign('tab','FingerPrintAndDoamTab');
            $device = FingerPrintDevice::readID((int)$employee[0]->sh_uao_att_device_id);
            $this->Smarty->assign('device',$device);
            $doam = Doam::readID((int)$employee[0]->sh_uao_att_doam_id);
            $this->Smarty->assign('weekDays',Setting::getList(25));
            $this->Smarty->assign('doam',$doam);
        }elseif ($parm[1] === 'Requests'){
            $EmployeesByDirectBoss = Vacant::getEmployeeListByDirectBoss($_SESSION['organization'],(int)$employee[0]->sh_uao_user_id);
            if($post['new_boss']){
                foreach ($EmployeesByDirectBoss as $employee) {
                    $employee->sh_uao_direct_boss_emp = $post['new_boss'];
                    $employee->save();
                }
                $this->Smarty->assign('DirectBossId',$employee->sh_uao_user_id);
                $this->Smarty->assign('EmployeesByDirectBoss', []);
            }else{
                $this->Smarty->assign('EmployeesByDirectBoss', $EmployeesByDirectBoss);
                $this->Smarty->assign('DirectBossId',$employee[0]->sh_uao_user_id);
            }
            $this->Smarty->assign('tab','Requests');
            $this->Smarty->assign('directManagersList', Vacant::getEmployeeListByEntity($_SESSION['organization']));
        }elseif ($parm[1] === 'CovenantManagementTab'){
            $this->Smarty->assign('tab','CovenantManagementTab');
        }elseif ($parm[1] === 'EndOfServiceCalculationTab'){
            $this->Smarty->assign('tab','EndOfServiceCalculationTab');
        }else{
            $this->Smarty->assign('tab','projects');
        }

        switch ($parm[0]) {

            case 'save_session':

                $_SESSION['s_current_employee_user_id'] = $parm[1];
                $_SESSION['s_empdata_uao_tab'] = $parm[2];
                break;
            case 'update':

                if ($_SESSION['s_empdata_token'] === $parm[1]) {

                    // Set the validation rules
                    Validation::rules($post, [
                        'sh_uao_job_date_of_appointment' => 'date',
                        'salary_basic' => 'required_if:salary_delivery,227',
                        'days_in_month' => 'required_if:salary_delivery,861',
                        'days_salary' => 'required_if:salary_delivery,861',
                        'hour_salary' => 'required_if:salary_delivery,228',
                        'att_doam_id' => 'required_if:att_status,1',
                        'att_device_id' => 'required_if:att_status,1,2|numeric|min:0',
                        'att_device_num' => 'required_if:att_status,1,2|numeric|min:0',
                    ]);

                    if (Validation::check()) {

                        try {

                            $vacant = Vacant::readID((int)$parm[2] ?? 0);
                            $lastBoss = $vacant->direct_boss_emp;
                            $post['sh_uao_payroll_template_id'] = $vacant->sh_uao_payroll_template_id;
                            $vacant->bindProperties($post);


                            $vacant->job_date_of_appointment = $this->Date->get_date('ad', $post['sh_uao_job_date_of_appointment']);

                            $vacant->job_end_date_of_appointment = $this->Date->get_date('ad',
                                $post['sh_uao_job_end_date_of_appointment']);

                            if ($post['att_status'] == 0) {
                                $vacant->att_doam_id = 0;
                                $vacant->att_device_id = 0;
                            }

                            $vacant->save();
                            if ($lastBoss != $vacant->direct_boss_emp)
                                event('event.DirectEmployeeBossChanged', [$lastBoss, $vacant->direct_boss_emp, $vacant->job_unt_id, $vacant->job_name]);

                            Notification::updatedAlert();

                        } catch (VacantException $e) {
                        }

                    }

                }

                $_SESSION['s_empdata_uao_tab'] = $parm[2];

                break;
            case 'insertPayrollTemplate':
                if ($_SESSION['s_rand_trans_num'] == $parm[2]) {

                    try {
                        $rule = new EmployeePayrollTemplate();

                        $template = new stdClass();
                        $template->name = $post['name'];
                        foreach ($post as $propertyName => $propertyValue) {
                            if ($propertyName == 'user_id') {
                                $template->user_id = $post['user_id'];
                            } else {
                                $template->{$propertyName} = $propertyValue;
                            }
                        }
                        $template->template_id = $parm[1];
                        $template->type = $_SESSION['new_rule_type'];
                        $template->condition = 648; // $post['condition'];
                        $template->range_based_on = ''; // implode(',', $post['range_based_on']);
                        $template->range_min = 0; // $post['range_min'];
                        $template->range_max = 0; // $post['range_max'];
                        $template->amount_percentage_based_on = json_encode($post['amount_percentage_based_on']);
                        $rule->template_id = $parm[1];
                        $rule->template = json_encode($template);
                        $rule->user_id = $post['user_id'];
                        $rule->save();

                        $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';

                        Notification::createdAlert();
                    } catch (Exception $e) {

                    }
                }
                try {
                    $template = PayrollTemplate::readID((int)$_SESSION['s_prl_templates_id'] ?? 0);
                    $this->Smarty->assign('row', $template);
                    $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
                    $this->Smarty->assign('row', $template);
                    if ($template) {
                        $this->Smarty->assign('temp_rules', PayrollRule::getPayrollRule($template));
                    }
                    $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));
                } catch (PayrollTemplateException $exception) {

                }

                break;
            case 'editPayrollTemplate':
                if ($_SESSION['s_rand_trans_num'] == $parm[2]) {

                    try {
                        $rule = EmployeePayrollTemplate::find($parm[1]);
                        $template = new stdClass();
                        $template->name = $post['name'];
                        foreach ($post as $propertyName => $propertyValue) {
                            if ($propertyName == 'user_id') {
                                $template->user_id = $_SESSION['s_current_employee_user_id'];
                            } else {
                                $template->{$propertyName} = $propertyValue;
                            }
                        }
                        $template->template_id = $_SESSION['s_prl_templates_id'];
                        $template->type = $_SESSION['new_rule_type'];
                        $template->condition = 648;
                        $template->range_based_on = $post['range_based_on'];
                        $template->effect_in_day_cost = $post['effect_in_day_cost'];
                        $template->range_min = 0;
                        $template->range_max = 0;
                        $template->amount_percentage_based_on = json_encode($post['amount_percentage_based_on']);
                        $rule->template_id = $_SESSION['s_prl_templates_id'];
                        $rule->template = json_encode($template);
                        $rule->update();
                        $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
                        Notification::updatedAlert();
                        redirect('FunctionalOperation/vacancies');
                    } catch (Exception $e) {
                    }
                }
                break;
            case 'dismiss':

                try {

                    $quitedVacant = Vacant::readID((int)$parm[2]);

                } catch (VacantException $e) {

                }

                if ($quitedVacant) {

                    //TODO check if vacant is it manager ?
                    if ((int)$quitedVacant->user_id === (int)$_SESSION['organization']->it_manager_id and $quitedVacant->basic) {
                        Notification::alertMessage(Notification::ERROR, 'please_change_it_manager_to_another_vacant');
                    } else {
                        try{
                            $dismiss = Vacant::dismissalVacant($post, $quitedVacant);
                        }catch (VacantException $e ){

                        }
                        Notification::sendNotification(
                            848,
                            0,
                            'hr_dismiss',
                            $dismiss->id,
                            $dismiss->created_by,
                            $dismiss->user_id,
                            1010,
                            []);

                        $templateVars = [
                            'username' => $quitedVacant->userObject->full_name,
                            'jobName' => $quitedVacant->jobObject->sh_job_name,
                            'dismissalReason' => $post['reasons'],
                            'disMissalDate' => date('Y-m-d')
                        ];
                        $template = new DismissEmployeeEmailTemplate($templateVars);
                        try {
                            $mailer = new UserMailer($quitedVacant->userObject, $template);
                            $mailer->send();
                        } catch (UserException $e) {

                        }

                        Notification::alertMessage(Notification::SUCCESS, 'EmployeeDismissedSuccessfully');
                    }

                    $_SESSION['tab1'] = '';
                    $_SESSION['tab2'] = 'active';
                    $_SESSION['tab3'] = '';
                    $_SESSION['tab4'] = '';
                    $_SESSION['tab5'] = '';
                    $_SESSION['tab6'] = '';
                    $_SESSION['tab7'] = '';
                }

                break;
            case 'settobasic':

                if ($_SESSION['s_empdata_token'] === $parm[1]) {

                    Vacant::setVacantToBasic(Vacant::readID((int)$parm[2]));
                    Notification::updatedAlert();

                }

                break;
            case 'PayrollTemplate':
                if ($_SESSION['s_empdata_token'] === $parm[1]) {
                    if ($post['submit'] === 'update') {
                        try {
                            $template = PayrollTemplate::readID((int)$post['template'] ?? 0);
                            $this->deleteEmployeeSpecificPayroll($template->id, $post['user_id']);
                            $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
                            $_SESSION['s_prl_templates_id'] = $post['template'];
                            $this->Smarty->assign('row', $template);
                            if ($template) {
                                $this->Smarty->assign('temp_rules', PayrollRule::getPayrollRule($template));
                            }
                        } catch (PayrollTemplateException $e) {
                        }
                    } else {
                        $template = PayrollTemplate::readID((int)$post['template']);
                        $this->Smarty->assign('temp_rules2', PayrollRule::getPayrollRule($template));
                        $this->Smarty->assign('temp_template_id2', $post['template']);
                        $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
                    }
                }
                break;
            case 'deletePayrollTemplate':
                if ($_SESSION['s_empdata_token'] == $parm[2]) {
                    $rule = EmployeePayrollTemplate::find((int)$parm[1]);
                    $rule->delete();

                    Notification::deletedAlert();
                    $_SESSION['s_empdata_uao_tab'] = 'PayrollTemplate';
                    $_SESSION['s_prl_templates_id'] = $parm[3];
                    $_SESSION['s_rand_trans_num'] = md5(rand(0000, 9999));

                }
                break;
        }

        try {

            /** @var sh_uao $vacant */
            $vacant = Vacant::getEmployeeBasicVacantEntity((int)$_SESSION['s_current_employee_user_id'] ?? 0);
            $employee = UserModel::has('basicJob')
                ->find((int)$_SESSION['s_current_employee_user_id']);
            SnsoCache::set('vacancies_vacant', $vacant, 21600);

            $this->Smarty->assign('basicVacant', $vacant);
            $this->Smarty->assign('user_employee', $employee);

            if (!isset($_SESSION['s_empdata_uao_tab']))
                $_SESSION['s_empdata_uao_tab'] = $vacant->sh_uao_id;

            try {
                $this->Smarty->assign('UserVacanciesList',
                    sh_uao::simpleReadByProperty([
                        Vacant::ORG_ID => $_SESSION['organization']->id,
                        Vacant::USER_ID => $vacant->sh_user_id,
                        Vacant::B_TYPE => 2,
                        Vacant::B_ACCEPTANCE => 1,
                        Vacant::QUIT => 0,
                        Vacant::DELETED => 0
                    ], Vacant::BASIC, 'DESC'));


            } catch (ModelException $e) {
                $this->Smarty->assign('UserVacanciesList', []);
            }


            $jscode1 = '';
            switch ($vacant->sh_uao_salary_delivery) {
                case 227:
                    $jscode1 = '<script> $(document).ready(function(){ $("#BasicSalary").show("fast"); $("#MonthTypeDays").show("fast"); $("#DaysTypeDays").hide("fast"); $("#hourTypeCost").hide("fast");}); </script>';
                    break;
                case 228:
                    $jscode1 = '<script> $(document).ready(function(){ $("#BasicSalary").hide("fast");$("#MonthTypeDays").hide("fast"); $("#DaysTypeDays").hide("fast");$("#hourTypeCost").show("fast");}); </script>';
                    break;
                case 861:
                    $jscode1 = '<script> $(document).ready(function(){ $("#BasicSalary").hide("fast"); $("#MonthTypeDays").hide("fast"); $("#DaysTypeDays").show("fast"); $("#hourTypeCost").hide("fast"); }); </script>';
                    break;
                case 826:
                    $jscode1 = '<script> $(document).ready(function(){ $("#BasicSalary").hide("fast"); $("#MonthTypeDays").hide("fast"); $("#DaysTypeDays").hide("fast"); $("#hourTypeCost").hide("fast"); }); </script>';
                    break;
            }

            $jscode = '';
            switch ($vacant->sh_uao_att_status) {
                case 0:
                    $jscode = '<script> $(document).ready(function(){ $("#doam_div").hide("fast"); $("#fingerprint_div").hide("fast"); }); </script>';
                    break;

                case 1:
                    $jscode = '<script> $(document).ready(function(){ $("#doam_div").show("fast"); $("#fingerprint_div").show("fast"); } ); </script>';
                    break;

                case 2:
                    $jscode = '<script> $(document).ready(function(){ $("#doam_div").hide("fast"); $("#fingerprint_div").show("fast"); } ); </script>';
                    break;
            }

            $this->Smarty->assign('jscode1', $jscode1);
            $this->Smarty->assign('jscode', $jscode);


        } catch (VacantException $e) {
            $this->Smarty->assign('basicVacant', []);
        }

        try {
            $this->Smarty->assign('salaryDeliveryList', Setting::getSalarydeliveryTypes());
        } catch (SettingException $e) {
            $this->Smarty->assign('salaryDeliveryList', []);
        }

        try {
            $this->Smarty->assign('directManagersList', Vacant::getEmployeeListByEntity($_SESSION['organization']));
        } catch (VacantException $e) {
            $this->Smarty->assign('directManagersList', []);
        }

        try {
            $this->Smarty->assign('doamList', Doam::read([Doam::ORG_ID => $_SESSION['organization']->id]));
        } catch (DoamException $e) {
            $this->Smarty->assign('doamList', []);
        }

        try {
            $this->Smarty->assign('AttendanceDevicesList',
                FingerPrintDevice::read([FingerPrintDevice::ORG_ID => $_SESSION['organization']->id]));
        } catch (FingerPrintDeviceException $e) {
            $this->Smarty->assign('AttendanceDevicesList', []);
        }

        try {
            $this->Smarty->assign('templates', PayrollTemplate::read([
                PayrollTemplate::ORG_ID => $_SESSION['organization']->id,
            ]));
        } catch (PayrollTemplateException $e) {

        }

        try {
            $template = PayrollTemplate::readID((int)SnsoCache::get('vacancies_vacant')->sh_uao_payroll_template_id);
            $this->Smarty->assign('row', $template);
            $this->Smarty->assign('temp_rules', PayrollRule::getPayrollRule($template));
            $this->Smarty->assign('basic_template_id', $_SESSION['s_prl_templates_id']);

            // get employee basic templates
            $employeeSpecificTemplate = EmployeePayrollTemplate::where('user_id', (int)SnsoCache::get('vacancies_vacant')->sh_user_id ?? 0)
                ->where('template_id', $template->id)
                ->whereNotNull('template')
                ->get();


            $this->Smarty->assign('employeeSpecificTemplate', $employeeSpecificTemplate);

            $vacant = SnsoCache::get('vacancies_vacant');
            $_SESSION['s_prl_templates_id'] = $vacant->sh_uao_payroll_template_id;
            $result = Vacant::getEmployeePayrollDetails((int)$vacant->sh_user_id ?? 0);

            // get employee basic salary
            $this->Smarty->assign('employee_salary', $vacant->sh_uao_salary_basic);
            $employeeTotalSalary = $result['salary'] + $result['allowances'] - $result['deductions'];
            $this->Smarty->assign('employee_total_salary',  $employeeTotalSalary);

        } catch (PayrollTemplateException $exception) {
        }

        $_SESSION['s_empdata_token'] = Helper::generateToken();
    }
}