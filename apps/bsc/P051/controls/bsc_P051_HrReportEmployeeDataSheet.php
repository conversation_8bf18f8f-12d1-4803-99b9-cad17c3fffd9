<?php

class bsc_P051_HrReportEmployeeDataSheet extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {
            case 'menu':
                $_SESSION['report_data'] = null;
                break;

            case 'save_session':
                $_SESSION['report_data'] = $post;
                break;
        }

        $units = DB::table('sh_unt')
            ->where(Unit::ORG_ID, $_SESSION['organization']->id)
            ->get();

        $this->Smarty->assign('units', $units);


        if ($_SESSION['report_data']) {

            $unit = $units->where('sh_unt_id', $_SESSION['report_data']['unit'])->first();
            $this->Smarty->assign('unit', $unit);

            $this->Smarty->assign('users', $this->prepareReportData($unit->sh_unt_id));
        }

    }

    public function print($parm, $post)
    {

        $this->show($parm, $_SESSION['report_data']);

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()), "", "", "", true);

    }

    /**
     * @param int $unit
     * @return \Illuminate\Support\Collection
     */
    public function prepareReportData(int $unit)
    {

        $transactions = DB::table('sh_uao')
            ->where('sh_uao_org_id', $_SESSION['organization']->id)
            ->join('sh_user', 'sh_uao_user_id', '=', 'sh_user_id')
            ->leftJoin('sh_unt', 'sh_uao_job_unt_id', '=', 'sh_unt_id')
            ->leftJoin('sh_job', 'sh_uao_job_id', '=', 'sh_job_id')
            ->leftJoin('prl_templates', 'sh_uao_payroll_template_id', '=', 'prl_templates_id')
            ->where('sh_unt_id', $unit)
            ->where('sh_uao_basic', 1)
            ->get();

        return $transactions;
    }

}