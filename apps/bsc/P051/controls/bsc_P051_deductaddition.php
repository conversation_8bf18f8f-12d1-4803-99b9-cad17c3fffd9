<?php

/**
 * Created by PhpStorm.
 * User: altaif
 * Date: 1/14/19
 * Time: 11:50 AM
 */
class bsc_P051_deductaddition extends Controller
{

    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case 'insert':

                if ($_SESSION['s_deductaddition_token'] == $parm[1]) {

                    try {

                        $deductAddition = new HRDeductAdditionRequest();
                        $deductAddition->bindProperties($post);
                        $deductAddition->user_id = $post['user_id'];
                        $deductAddition->org_id = $_SESSION['organization']->id;
                        $deductAddition->created_by = $_SESSION['user']->id;
                        $deductAddition->created_date = date('Y-m-d');
                        $deductAddition->type = (int)$post['type'];
                        $deductAddition->request_success = 1;
                        $deductAddition->save();

                        Notification::createdAlert();

                    } catch (HRDeductAdditionRequestException $e) {
                        Notification::errorAlert();
                    }
                }

                break;

            case 'update':

                if ($_SESSION['s_deductaddition_token'] == $parm[1]) {

                    $deductAddition = null;

                    try {

                        $deductAddition = HRDeductAdditionRequest::readID((int)$parm[2]);
                        $deductAddition->bindProperties($post);
                        $deductAddition->save();

                        Notification::updatedAlert();

                    } catch (HRDeductAdditionRequestException $exception) {

                    }

                    $_SESSION['s_deductaddition_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'delete':

                if ($_SESSION['s_deductaddition_token'] == $parm[1]) {

                    try {

                        $deductAddition = HRDeductAdditionRequest::readID((int)$parm[2]);

                    } catch (HRDeductAdditionRequestException $exception) {

                    }

                    if ($deductAddition) {

                        try {

                            Request::deleteWFRequest(
                                $_SESSION['organization']->id,
                                $_SESSION['program']->id,
                                $deductAddition->user_id,
                                'deductaddition',
                                'hr_deductaddition',
                                $parm[2]);

                            Notification::deletedAlert();

                        } catch (Exception $e) {

                        }
                    }

                    $_SESSION['s_deductaddition_token'] = md5(rand(0000, 9999));

                }

                break;

            case 'archive':
                if ($_SESSION['s_deductaddition_token'] == $parm[1]) {
                    try {
                        $deductAddition = HRDeductAdditionRequest::readID((int)$parm[2] ?? 0);
                        if ($deductAddition) {
                            $deductAddition->request_success = 2;
                            $deductAddition->save();
                            Notification::updatedAlert();
                        }
                    } catch (HRDeductAdditionRequestException $e) {
                        Notification::failAlert($e->getMessage());
                    }

                    $_SESSION['s_deductaddition_token'] = md5(rand(0000, 9999));
                }
                break;

        }

        $_SESSION['s_personnel_tab'] = "deductaddition";

        // عرض جميع الطلبات المعتمدة مباشرة من قاعدة البيانات
        try {
            $deductadditionRequests = HRDeductAdditionRequest::read([
                HRDeductAdditionRequest::ORG_ID => $_SESSION['organization']->id,
                HRDeductAdditionRequest::REQUEST_SUCCESS => 1
            ], [0 => ['property' => HRDeductAdditionRequest::ID, 'sort' => 'DESC']]);
            
            // تحميل بيانات المستخدمين وبيانات المسير لكل طلب
            foreach ($deductadditionRequests as $request) {
                try {
                    // تحميل بيانات المستخدم
                    $request->userObject = User::readID((int)$request->user_id);
                    
                    // تحميل بيانات المسير
                    if ($request->manipulation_batche_id) {
                        try {
                            $batch = DB::table('payroll_batch')
                                ->where('payroll_batch_id', (int)$request->manipulation_batche_id)
                                ->first();
                            
                            if ($batch) {
                                $request->batch_name = $batch->prl_batches_name;
                                $request->batchObject = $batch;
                            } else {
                                $request->batch_name = 'غير محدد';
                                $request->batchObject = null;
                            }
                        } catch (Exception $e) {
                            $request->batch_name = 'غير محدد';
                            $request->batchObject = null;
                        }
                    }
                } catch (UserException $e) {
                    $request->userObject = null;
                    $request->batch_name = 'غير محدد';
                    $request->batchObject = null;
                }
            }
            // return $deductadditionRequests;
            $this->Smarty->assign('deductadditionRequests', $deductadditionRequests);
        } catch (HRDeductAdditionRequestException $e) {
            $this->Smarty->assign('deductadditionRequests', []);
        }

        $_SESSION['s_deductaddition_token'] = Helper::generateToken();

    }

    public function add($parm, $post)
    {
        $this->Smarty->assign('employees', Vacant::getEmployeeListByEntityWithBasicVacancies($_SESSION['organization'], Vacant::EMPLOYEE_ALL_VACANT));

        $this->Smarty->assign('deductaddition_options_list', Setting::getList(207));

        try {
            $this->Smarty->assign('grfExistenceNum', Graph::count([
                Graph::STATUS => 1,
                Graph::OPR_CODE => 'deductaddition'
            ]));
        } catch (GraphException $e) {
            $this->Smarty->assign('grfExistenceNum', 0);
        }

        $_SESSION['s_personnel_tab'] = "deductaddition";
        $_SESSION['s_deductaddition_token'] = Helper::generateToken();
    }

    public function edit($parm, $post)
    {
        $this->Smarty->assign('employees', Vacant::getEmployeeListByEntityWithBasicVacancies($_SESSION['organization'], Vacant::EMPLOYEE_ALL_VACANT));

        try {
            $row = HRDeductAdditionRequest::readID((int)$parm[0]);
            $this->Smarty->assign('row', $row);
        } catch (HRDeductAdditionRequestException $exception) {
            $this->Smarty->assign('row', []);
        }

        $this->Smarty->assign('deductaddition_options_list', Setting::getList(207));

        $_SESSION['s_personnel_tab'] = "deductaddition";
        $_SESSION['s_deductaddition_token'] = Helper::generateToken();
    }

    public function confirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', HRDeductAdditionRequest::readID((int)$parm[0] ?? 0));
        } catch (HRDeductAdditionRequestException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_deductaddition_token'] = Helper::generateToken();
    }

    public function read($request) {
        try {
            // تحميل بيانات المستخدم
            $request->userObject = User::readID((int)$request->user_id);
            
            // تحميل بيانات المسير
            if ($request->manipulation_batche_id) {
                try {
                    $batch = DB::table('prl_batches')
                        ->where('prl_batches_id', (int)$request->manipulation_batche_id)
                        ->first();
                    
                    if ($batch) {
                        $this->batch_name = $batch->prl_batches_name;
                        $this->batchObject = $batch;
                    } else {
                        $this->batch_name = 'غير محدد';
                        $this->batchObject = null;
                    }
                } catch (Exception $e) {
                    $this->batch_name = 'غير محدد';
                    $this->batchObject = null;
                }
            }
            
            return $this;
        } catch (UserException $e) {
            $request->userObject = null;
            throw new Exception($e->getMessage());
        }
    }

}