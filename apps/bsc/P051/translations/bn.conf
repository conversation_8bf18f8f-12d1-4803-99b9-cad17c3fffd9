
[Setting]
direction = "ltr"
alignment = "left"
valignment = "right"

[Language]
code = "bn"

[Program]
name = "মানব সম্পদ"
about = "অবকাশ, secondment এবং অনুমোদন এবং বরখাস্ত, এবং যে তথ্য ও কর্মচারী উপস্থিতি এবং তার বেতন পথ লিঙ্ক: হিউম্যান রিসোর্স প্রোগ্রাম বিষয় পার্সোনেল সভা ও মিছিলের বেতন ও উপস্থিতি প্রস্তুতি ঠিকানাগুলি, প্রোগ্রাম নিয়োগ এবং যেমন ব্যবহারকারী অপারেশন পরিচালনার পরিচালনা করা সহজ হয়। প্রোগ্রাম সংগঠনের বিভিন্ন ধরনের অনুসারে নির্মিত হয়েছিল, উভয় কাজের প্রকৃতি বা ব্যবহারকারীদের এবং সেটিংস প্রোগ্রামের তালিকা মাধ্যমে সংখ্যা সংগঠন বৈশিষ্ট্য সঙ্গে সেইসাথে ক্ষমতা সামঞ্জস্য করে অবিকল মাপসই অনুসারে প্রোগ্রাম কাস্টমাইজ করতে পারেন ফাংশন এবং সেটিংস যে প্রোগ্রাম কর্মক্ষমতা সঙ্গে সম্পর্কযুক্ত অনেক নিয়ন্ত্রণ করতে পারেন পরিপ্রেক্ষিতে এটি বিভিন্ন কাজের সকল ব্যবহারকারীর। হিউম্যান রিসোর্স প্রোগ্রাম নিয়ন্ত্রক প্রোগ্রাম গাইড উপর নিয়োগ করে এবং ব্যবহারকারীদের পরিচালনার উপর নির্ভর করে তাই আপনি সাংগঠনিক প্রোগ্রাম গাইড প্রথম এবং ইউনিট কাঠামো এবং ফাংশন প্রস্তুতি, যার মাধ্যমে অর্ডার মানব সম্পদ প্রোগ্রামের মধ্যে ব্যবহারের জন্য উপলব্ধ করা সক্রিয় সক্রিয় করতে হবে"

[Menus]
HumanResource = "কর্মী বিষয়"
HumanResourceReports = ""
HumanResourceSettings = ""
newMenu = ""

[Operations]
recruitment = "এপয়েন্টমেন্ট"
employees = "লাঠি"
payroll = "বেতনের"
fingerPrintIntervals = ""
committeebuilding = ""
editcreditrequest = ""
attendance = ""
deductaddition = ""
circulateleave = ""
HumanResourceReportDashboard = ""
orguntmanager = ""
leavesettings = ""
fingerPrintDevices = ""
hrdoamwrdiah = ""
empportalsetting = ""
transferaccounts = ""

[recruitment]
p_recruite = "এপয়েন্টমেন্ট"
p_appointment_report = "নিয়োগের রিপোর্ট"
p_recruite_success = "সফলভাবে নিয়োগ করা হয়েছে"
p_request_sent_successfully = "পি অনুরোধ সফলভাবে পাঠানো হয়েছে"
p_send_belonging_request = "একটি অ্যাপয়েন্টমেন্ট অনুরোধ পাঠান"
p_browse_recruit_requests_sent_to_user = "কর্মসংস্থান অ্যাপ্লিকেশন পর্যালোচনা"
p_recruit_request_ask_date = "নিয়োগ আবেদন জমা দেওয়ার তারিখ"
p_recruit_request_ask_comment = "কর্মসংস্থান আবেদন নেভিগেশন মন্তব্য"
p_recruit_request_replay_date = "কর্মসংস্থান জন্য আবেদন প্রতিক্রিয়া তারিখ"
p_recruit_request_replay_comment = "কর্মসংস্থান জন্য আবেদন প্রতিক্রিয়া মন্তব্য"
p_messages_notes = "নোট"
p_recruite_in_job_report = "চাকরির রিপোর্টে পি নিয়োগ"
p_you_are_recruit_on_this_vacant = "আপনি এই ফাংশন বরাদ্দ করা হয়েছে"
p_update_this = "আপডেট করা হয়েছে"
p_the_human_resource = "কর্মী বিষয়"
p_you_are_dismissed_from_vacant = "আপনি এই পোস্ট থেকে সরানো হয়েছে"

[employees]
p_convert_job_to_primary = "কার্যকারিতা মৌলিক রূপান্তর করুন"
p_dismiss_from_job = "অফিস থেকে খারিজ"
p_date_of_appointment = "অ্যাপয়েন্টমেন্টের তারিখ"
p_direct_chief = "সরাসরি প্রেসিডেন্ট"
p_job_salary_type = "চাকরিটা দাও"
p_base_salary = "বেসিক বেতন"
p_subject_to_presense_and_leave = "উপস্থিতি এবং প্রস্থান বিষয়"
p_not_subjected = "বিষয় না"
p_subject_to_presense_and_leave_based_on_job_time = "উপস্থিতিতে এবং দায়িত্ব পালনে বিষয়"
p_subject_to_presense_and_leave_based_on_hour = "উপস্থিতি এবং প্রস্থান ঘন্টা বিষয়"
p_fingerprint_device = "ফিঙ্গারপ্রিন্ট ডিভাইস"
p_no_on_fingerprint_device = "ফিঙ্গারপ্রিন্টের সাথে সংখ্যা"
p_user_no_in_presense_and_leave_device = "উপস্থিতি এবং প্রস্থান মেশিন ব্যবহারকারীর সংখ্যা"
p_hired_employee_name = "মনোনীত কর্মচারীর নাম"
p_attach_title = "পি জোড়া শিরোনাম"
p_dismissal_reasons = "বরখাস্তের কারণ"
p_dismissal_reason = "বরখাস্তের কারণ"
p_dismiss_employee_alert = "পপি কর্মচারী সতর্কতা বরখাস্ত"
p_dismissal_implementation = "বরখাস্তের নির্বাহণ"
p_converting_job_to_brimary_make_all_others_secondary = ""
p_adjust_leave_templates = "একটি কোর মধ্যে পোস্ট রূপান্তর প্রয়োজন যে কর্মচারী অন্যান্য ফাংশন দ্বিতীয় ফাংশন রূপান্তরিত করা উচিত"
p_employees_available_leaves = "কর্মচারী উপলব্ধ অবকাশ"
p_leave_name = "ছুটির নাম"
p_credit_type = "ব্যালেন্স টাইপ"
p_credit_max_limit_when_transfer = "পোস্টিংয়ের সর্বোচ্চ ব্যালেন্স"
p_employee_available_credit = "উপলব্ধ স্টাফ ব্যালেন্স"
p_untransferable_leave = "স্থানান্তর করা যাবে না"
p_unlimited = "একটি উচ্চ সীমা ছাড়াই"
p_the_record = "রেজিস্ট্রি"
p_add_deduct_leave_credit = "যোগ করুন / ছেড়ে বামে ব্যালেন্স"
p_adjust_employee_leave = "কর্মচারী জন্য ছুটির সমন্বয়"
p_leave_type = "ছুটির ধরন"
p_leave_credit = "ছুটি ব্যালেন্স"
p_max_credit_limit = "সর্বোচ্চ ব্যালেন্স"
p_allowedleave_max_credit = "উপরের সীমা"
p_start_counting_leave_date = "ছুটির হিসাব শুরু"
p_types_of_available_leaves = "উপলব্ধ অবকাশ"
p_need_to_determine_gender_first = "ওহো, আপনাকে প্রথমবারের কর্মচারীর লিঙ্গটি নির্দিষ্ট করতে হবে"
p_num_of_required_days = "প্রয়োজনীয় সংখ্যা সংখ্যা"
p_duration_by_days = "দিনের সময়কাল"
p_annual_leave_credit = "বার্ষিক ব্যালেন্স"
p_monthly_credit = "মাসের ব্যালেন্স"
p_added_credit = "যোগ ব্যালেন্স যোগ করা"
p_deducted_credit = "ছাড়ের ভারসাম্য"
p_transfer_credit = "ব্যালান্স ট্রান্সফার"
p_max_size = "শীর্ষ আকার"
p_max_number = "শীর্ষ সংখ্যা"
p_extensions = "প্রসার"

[payroll]
p_payslip_updated_successfully = "বেতন ট্র্যাক সফলভাবে আপডেট"
p_should_determine_payslip_auditor_and_approver = "ওহো, আপনাকে রেফারেন্সগুলি নির্দিষ্ট করতে হবে এবং পাথটি অনুমোদন করতে হবে"
p_payslips_preparation = "মার্চের প্রস্তুতি"
p_payslip_creation = "পাথ তৈরি করুন"
p_adjust_employees = "স্টাফ সমন্বয় করুন"
p_salaries_template = "পেরোল টেমপ্লেট"
p_payslip = "এডমিন"
p_there_are_not_reveiwed_records = "দুঃখিত, সেখানে অডিওড্রাইভে রেকর্ড রয়েছে"
p_monthly_salary_payslips = "মাসিক বেতন রেখা"
p_daily_salary_payslips = "দৈনিক মজুরি সমাবেশ"
p_monthly_salary_employees = "মাসিক বেতন স্টাফ"
p_daily_salary_employees = "দৈনিক বেতন স্টাফ"
p_salary_template = "বেতন টেমপ্লেট"
p_template_create = "টেমপ্লেট তৈরি করুন"
p_template_type = "টেমপ্লেট টাইপ"
p_estimate_day_price = "দিনের মূল্য হিসাব করুন"
p_affecting_day_price_estimation = "দিন মান গণনা প্রভাবিত করে"
p_reveiwing_status = "চেক অবস্থা"
p_salary_net_after_additions_must_not_be_less_than_zero = "সংযোজন পরে নেট বেতন অন্তত শূন্য হতে হবে"
p_salary_record = "বেতনটি সনাক্ত করুন"
p_additions_total = "মোট সংযোজন"
p_sorry_no_approved_salary_records = "দুঃখিত, কোন অনুমোদিত বেতন রেকর্ড আছে"
p_excluded_employees = "বহিষ্কৃত কর্মচারী"
p_deleting_payslip_consequences = "এটির সাথে যুক্ত সমস্ত ডেটা মুছে ফেলার একটি বেতন ট্র্যাক ফলাফল মুছে ফেলছে, এবং এটি অনুপস্থিতি রিলিজ, অগ্রগতি এবং এটি সঙ্গে সম্পর্কিত রেকর্ড রেকর্ড"
p_salaries_recoed_information = ""
p_payslip_name = ""
p_delete_template = ""
p_add_new_term = ""
p_term_name = ""
p_edit_term = ""
p_add_new_salary_template = ""
p_template_name = ""
p_edit_salary_template = ""
p_salary_type = ""
p_choose_salary_template = ""
p_should_bulid_monthly_salary_templates_first = ""
p_should_bulid_daily_salary_templates_first = ""
p_adjust_reveiwing_status = ""
p_bonuses_overall = ""
p_net_salary = ""
p_payment_record = ""
p_reveiwed = ""
p_not_reveiwed = ""
p_absence = ""
p_no_absence_records = ""
p_advances = ""
p_no_advances_requests = ""
p_deducting_requests = ""
p_no_deducting_requests = ""
p_the_absence = ""
p_number_of_duty_days = ""
p_number_of_absence_days = ""
p_deduction_value = ""
p_add_payslip = ""
p_from_salary = ""
p_auditor = ""
p_approved_by = ""
p_reveiw_and_confirm_payslip_fully_depend_on_it_manager_workflow_should_bulid_workflow_with_auditor_and_approver_determined_from_record = ""
p_edit_payslip = ""
approved_by = ""
p_build_payslip = ""
p_building_payslip_describtion = ""
p_tamplate_or_extra_or_deductions_alteration_not_affecting_payslip_after_build = ""
p_rebulid_payslip_to_apply_tamplate_or_extra_or_deductions_alteration = ""
p_delete_payslip = ""
p_there_are_not_approved_payslips = ""
p_approve_payslip_and_send_it_to_financial_accounting = ""
p_net_after_deductions = ""
p_review_all_records = ""
p_important_information_about_payslip = ""
p_convert_all_records_to_reviewed = ""
p_convert_all_records_to_not_reviewed = ""

[fingerPrintIntervals]
p_record_name = ""
p_records = ""
p_add_attendance_record = ""
p_period_name = ""
p_edit_attendance_record = ""
p_record_confirm_delete_data = ""
p_record_delete_note = ""
p_attach_attendance_file = ""
p_data_file = ""
p_record_create = ""
p_finger_print_records = ""
p_attendance_and_leave_records = ""
p_original_finger_print_records = ""
p_edited_finger_print_records = ""
p_work_duration = ""
p_delay_duration = ""
p_extra_duration = ""
p_absence_duration = ""
p_early_entry_start = ""
p_doam_starting_time = ""
gp_start_deduction_time = ""
p_start_absent_time = ""
p_end_absent_time = ""
p_start_early_logout_time = ""
p_doam_ending_time = ""
p_late_exit_time = ""

[committeebuilding]
p_commitee_building = ""
p_commitee_name = ""
p_committee_purpose = ""
p_committee_members = ""
p_member_name = ""
p_committeemember_role = ""
p_add_new_committee = ""
p_edit_committee = ""
p_delete_commitee_will_delete_related_data_in_other_programs = ""
p_add_new_member = ""
p_choose_member = ""
p_edit_member = ""
p_delete_member = ""

[orguntmanager]
p_unitcode = ""
p_add_unit_emp_sex = ""
p_unit_card = ""
p_unit_name = ""
p_unit_code = ""
p_unit_employees_sex = ""
p_unit_duties = ""
p_unit_goals = ""
p_edit_unit_data = ""

[leavesettings]
p_leaves_templates = ""
p_schediuled_leaves = ""
p_weekend_leaves = ""
p_leave_name = ""
p_leave_type = ""
p_leave_duration = ""
p_leave_days = ""
p_leave_description = ""
p_leave_entitled_sex = ""
p_leave_conditions = ""
p_annual_leave_credit = ""
p_credit_num_of_days = ""
p_transfer_credit = ""
p_credit_max_limit_when_transfer = ""
p_end_of_leave_year = ""
p_edit_job_type_details = ""
p_helping_guide = ""
p_cant_delete_this_type_of_leave_because_its_in_use = ""
p_cant_delete_this_type_of_scheduled_leave_because_its_in_use_u_can_deactivate_it_and_create_new_one = ""

[fingerPrintDevices]
p_add_new_device = ""
p_edit_presence_and_parting_device = ""
p_delete_presence_and_parting_device = ""
p_confirm_delete_presence_and_parting_device = ""
p_delete_job_time = ""
p_helping_guide = ""
p_cant_delete_in_use_device_unless_all_employees_using_this_device_transfered_to_another_one = ""

[hrdoamwrdiah]
p_no_weekend = ""
p_period_name = ""
p_from_to = ""
p_add_new_doam = ""
p_doam_name = ""
p_edit_doam = ""
p_add_new_period = ""
p_early_login_time = ""
p_doam_starting_time = ""
p_start_deduction_time = ""
p_start_absent_time = ""
p_end_absent_time = ""
p_start_early_logout_time = ""
p_doam_ending_time = ""
p_late_exit_time = ""
p_edit_period = ""
p_delete_period = ""

[empportalsetting]
p_contact_data_setting = ""
by_gender_access_to = ""
by_unit_access_to = ""
p_add_new_user = ""
p_update_user_data = ""
p_delete_user = ""

[transferaccounts]
p_to = ""
