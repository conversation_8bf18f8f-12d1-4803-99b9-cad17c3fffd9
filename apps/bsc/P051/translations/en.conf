
[Setting]
direction = "ltr"
alignment = "left"
valignment = "right"

[Language]
code = "en"

[Program]
name = "Human Resources"
about = ""

[Menus]
HumanResource = "Human Resource"
HumanResourceReports = "Reports"
HumanResourceSettings = "Settings"
newMenu = "New menu"

[Operations]
recruitment = " Appointment"
employees = " employees"
payroll = "Payroll"
fingerPrintIntervals = "Attendance and departure data"
committeebuilding = "compose a committee"
editcreditrequest = "Request a balance adjustment"
attendance = "Accountability of absence"
deductaddition = " Request for addition / deduction"
circulateleave = "vacation schedule"
HumanResourceReportDashboard = " Human Resources Reports"
orguntmanager = "Adjust Unit Managers"
leavesettings = "vacation settings"
fingerPrintDevices = "Adjust fingerprint devices"
hrdoamwrdiah = "settings of work duties"
empportalsetting = "employee portal setting"
transferaccounts = "setting transfer account"

[recruitment]
p_recruite = " Appointment"
p_appointment_report = " Appointment report"
p_recruite_success = " Successfully assigned"
p_request_sent_successfully = "request sent successfully"
p_send_belonging_request = " Send an appointment request"
p_browse_recruit_requests_sent_to_user = "Review of employment applications"
p_recruit_request_ask_date = "Date of submission of employment application"
p_recruit_request_ask_comment = "Comment on the employment application"
p_recruit_request_replay_date = "Date of response of the application  employment"
p_recruit_request_replay_comment = "Comment in response to the application for employment"
p_messages_notes = "Ovservations"
p_recruite_in_job_report = "recruite_in_job_report"
p_you_are_recruit_on_this_vacant = "You have been assigned this function"
p_update_this = "update done"
p_the_human_resource = "Human Resources"
p_you_are_dismissed_from_vacant = "You have been removed from this post"

[employees]
p_convert_job_to_primary = "Convert the functionality to Basic"
p_dismiss_from_job = "dismiss from job"
p_date_of_appointment = "date of appointment"
p_direct_chief = "direct chief"
p_job_salary_type = "employees reward"
p_base_salary = "Basic salary"
p_subject_to_presense_and_leave = " Subject to attendance and departure"
p_not_subjected = " Not subject to"
p_subject_to_presense_and_leave_based_on_job_time = " Subject to attendance and leave on duty"
p_subject_to_presense_and_leave_based_on_hour = " Subject to attendance and departure hours"
p_fingerprint_device = " Fingerprint Device"
p_no_on_fingerprint_device = "number with the fingerprint"
p_user_no_in_presense_and_leave_device = "The number of the user in the attendance and departure machine"
p_hired_employee_name = "Name of Designated Employee"
p_attach_title = "attach title"
p_dismissal_reasons = "dismissal reasons"
p_dismissal_reason = "dismissal reason"
p_dismiss_employee_alert = "dismissal employee alert"
p_dismissal_implementation = "dismissal implementation"
p_converting_job_to_brimary_make_all_others_secondary = "Transforming the post into a core requires that the other functions of the employee be converted to secondary functions"
p_adjust_leave_templates = "Adjust the holiday templates"
p_employees_available_leaves = "Vacations available to the employee"
p_leave_name = " Name of leave"
p_credit_type = "Balance type"
p_credit_max_limit_when_transfer = "The maximum balance on the posting"
p_employee_available_credit = "employees available credit"
p_untransferable_leave = "Available staff balance"
p_unlimited = "Unlimited"
p_the_record = "record"
p_add_deduct_leave_credit = "Discount vacation balance"
p_adjust_employee_leave = "Adjustment of leave for employee"
p_leave_type = "type of leave"
p_leave_credit = "credit of leave"
p_max_credit_limit = "Maximum credit"
p_allowedleave_max_credit = "the highest rate"
p_start_counting_leave_date = "Beginning of leave calculation"
p_types_of_available_leaves = "Available Vacations"
p_need_to_determine_gender_first = "Oops, you must specify the gender of the employee first"
p_num_of_required_days = "Number of days required"
p_duration_by_days = "Duration of days"
p_annual_leave_credit = "Annual Balance"
p_monthly_credit = "Balance of the month"
p_added_credit = "Added balance"
p_deducted_credit = "Discounted balance"
p_transfer_credit = "Balance transfer"
p_max_size = "maximum size"
p_max_number = "Maximum number"
p_extensions = "extension"

[payroll]
p_payslip_updated_successfully = "Salary track updated successfully"
p_should_determine_payslip_auditor_and_approver = "Oops, you must specify references and approve the path"
p_payslips_preparation = "Prepare the marches"
p_payslip_creation = "Create paths"
p_adjust_employees = "Adjust the staff"
p_salaries_template = "Payroll templates"
p_payslip = "Path"
p_there_are_not_reveiwed_records = "Sorry, there are unaudited records"
p_monthly_salary_payslips = "Monthly wage rallies"
p_daily_salary_payslips = "Daily wage rallies"
p_monthly_salary_employees = "Monthly pay staff"
p_daily_salary_employees = "Daily pay staff"
p_salary_template = "Salary template"
p_template_create = "Build the template"
p_template_type = "Template type"
p_estimate_day_price = "Calculate the value of the day"
p_affecting_day_price_estimation = "Affects the day value calculation"
p_reveiwing_status = "Check status"
p_salary_net_after_additions_must_not_be_less_than_zero = "Net salary after additions must be at least zero"
p_salary_record = "salary record"
p_additions_total = "Total additions"
p_sorry_no_approved_salary_records = "Sorry, there are no approved salary records"
p_excluded_employees = "The excluded employees"
p_deleting_payslip_consequences = "Deleting a salary track results in the deletion of all data associated with it, and it releases the absence, advances and discount records associated with it"
p_salaries_recoed_information = "Payroll information"
p_payslip_name = "payslip name"
p_delete_template = "Delete Template"
p_add_new_term = "Add a new item"
p_term_name = "Item name"
p_edit_term = "Edit Item"
p_add_new_salary_template = "Add a new salary template"
p_template_name = "Template name"
p_edit_salary_template = " Modify Salary Template"
p_salary_type = "Type of pay"
p_choose_salary_template = "Select a ordered template"
p_should_bulid_monthly_salary_templates_first = "should build monthly salary templates first"
p_should_bulid_daily_salary_templates_first = "Salary templates must be built daily first"
p_adjust_reveiwing_status = "Adjust the audit status"
p_bonuses_overall = " Total bonuses"
p_net_salary = "Net salary"
p_payment_record = " Payment history"
p_reveiwed = " Checker"
p_not_reveiwed = " Unchecked"
p_absence = " Accountability of absence"
p_no_absence_records = " There are no records of absence"
p_advances = "advances"
p_no_advances_requests = "no advances requests"
p_deducting_requests = "deducting requests"
p_no_deducting_requests = "no deducting requests"
p_the_absence = " Absence"
p_number_of_duty_days = "number of duty days"
p_number_of_absence_days = "number of absence's Day"
p_deduction_value = "deduction value"
p_add_payslip = "add payslip"
p_from_salary = "from salary"
p_auditor = "References"
p_approved_by = "المعتمد "
p_reveiw_and_confirm_payslip_fully_depend_on_it_manager_workflow_should_bulid_workflow_with_auditor_and_approver_determined_from_record = " The process review and approval depends mainly on the procedure of the technology management program. The schema must be structured so that the auditor and auditor are identified from the registry"
p_edit_payslip = "edit payslip"
approved_by = "approved by"
p_build_payslip = "build payslip"
p_building_payslip_describtion = " Path construction is the process by which the system calculates payroll templates for all staff of salary templates involved in the path configuration"
p_tamplate_or_extra_or_deductions_alteration_not_affecting_payslip_after_build = " Any modification to the templates, allowances, and rebates does not affect the path after construction"
p_rebulid_payslip_to_apply_tamplate_or_extra_or_deductions_alteration = " You must rebuild the path each time you modify templates, allowances, and rebates and want to include those changes in the path"
p_delete_payslip = "Delete a path"
p_there_are_not_approved_payslips = " Sorry, there are unsupported staff paths"
p_approve_payslip_and_send_it_to_financial_accounting = " Approving the path and sending it to the financial resources"
p_net_after_deductions = "net after deductions"
p_review_all_records = " Check all records"
p_important_information_about_payslip = "It is important to go through each payroll record separately and review it and verify the amounts in each track. Records can not be modified after the route is approved and sent to the financial accounting program. If the amounts are confirmed based on the result of previous paychecks, To summarize"
p_convert_all_records_to_reviewed = " Convert all records to a checker"
p_convert_all_records_to_not_reviewed = "Convert all records to un audited"

[fingerPrintIntervals]
p_record_name = "record name"
p_records = "records"
p_add_attendance_record = " Add an attendance and log out record"
p_period_name = "إسم الفتره"
p_edit_attendance_record = "edit attendance record"
p_record_confirm_delete_data = "? Are you sure you want to delete attendance and departure data for the month below"
p_record_delete_note = " When a month is deleted, all attendance and exit data associated with that month are also deleted"
p_attach_attendance_file = "Attach the attendance and exit file"
p_data_file = " Data file"
p_record_create = "record create"
p_finger_print_records = " Fingerprint records"
p_attendance_and_leave_records = "Attendance and departure records"
p_original_finger_print_records = "original finger print records"
p_edited_finger_print_records = "edited finger print records"
p_work_duration = "work duration"
p_delay_duration = "deduction duration"
p_extra_duration = "Overtime Duration"
p_absence_duration = "Absent Duration"
p_early_entry_start = "early entry start"
p_doam_starting_time = "work beginning"
gp_start_deduction_time = "start deduction time"
p_start_absent_time = "absent starting time"
p_end_absent_time = "end absent time"
p_start_early_logout_time = "start early logout time"
p_doam_ending_time = "End of duty"
p_late_exit_time = "overtime starting"

[committeebuilding]
p_commitee_building = "build a committee"
p_commitee_name = "committee name"
p_committee_purpose = " General objective of the Committee"
p_committee_members = " Committee members"
p_member_name = "member name"
p_committeemember_role = "Mission within the Committee"
p_add_new_committee = " Add a new committee"
p_edit_committee = "Modify the data committee"
p_delete_commitee_will_delete_related_data_in_other_programs = "warning:When this committee is deleted, all related data will be deleted in other programs"
p_add_new_member = "Add a new member"
p_choose_member = "Select the employee"
p_edit_member = " Modify member data"
p_delete_member = "Delete member"

[orguntmanager]
p_unitcode = "unit code"
p_add_unit_emp_sex = "representative unit gender"
p_unit_card = "unit card"
p_unit_name = "unit name"
p_unit_code = "unit sign"
p_unit_employees_sex = "representative unit gender"
p_unit_duties = "unit duties"
p_unit_goals = "unit goals"
p_edit_unit_data = "edit unit data"

[leavesettings]
p_leaves_templates = "vacation templates"
p_schediuled_leaves = "vacation scheduled"
p_weekend_leaves = "weekend vacation"
p_leave_name = "vacation's name"
p_leave_type = "type of vacation"
p_leave_duration = "vacation duration"
p_leave_days = "days of vacation"
p_leave_description = "vacation decription"
p_leave_entitled_sex = "eligible leave gender"
p_leave_conditions = "vacation's condition"
p_annual_leave_credit = "annual credit"
p_credit_num_of_days = "number of credit days"
p_transfer_credit = "transfer credit"
p_credit_max_limit_when_transfer = "credit max limit when transfer"
p_end_of_leave_year = "end of leave year"
p_edit_job_type_details = "edit types of leave"
p_helping_guide = "Help guide"
p_cant_delete_this_type_of_leave_because_its_in_use = "You can not delete a user leave type in advance, deactivating will prevent the use of this type again"
p_cant_delete_this_type_of_scheduled_leave_because_its_in_use_u_can_deactivate_it_and_create_new_one = " A scheduled leave type can not be deleted in advance, you can switch the leave type to inactive and create a new scheduled leave type"

[fingerPrintDevices]
p_add_new_device = " Add a new device"
p_edit_presence_and_parting_device = "edit device data log in and log out"
p_delete_presence_and_parting_device = "delete device log in and log out"
p_confirm_delete_presence_and_parting_device = "are you sure you want to delete log in device?"
p_delete_job_time = "deleting duty"
p_helping_guide = " Help guide"
p_cant_delete_in_use_device_unless_all_employees_using_this_device_transfered_to_another_one = " A device can not be deleted with a user ID. First, employees using this device must first be switched to another device, and you can then delete the device"

[hrdoamwrdiah]
p_no_weekend = " There are no"
p_period_name = "name of the period"
p_from_to = "from/to"
p_add_new_doam = " Add new time"
p_doam_name = "duty name"
p_edit_doam = "edit data duty"
p_add_new_period = "add new period"
p_early_login_time = "early log in time"
p_doam_starting_time = "duty start"
p_start_deduction_time = "beginning of time deduction"
p_start_absent_time = "beginning of absent time"
p_end_absent_time = "end of absent time"
p_start_early_logout_time = "start permission to log out early"
p_doam_ending_time = " End of duty"
p_late_exit_time = "overtime starting"
p_edit_period = "Modify period data"
p_delete_period = "Delete period"

[empportalsetting]
p_contact_data_setting = "contact data setting"
by_gender_access_to = "gender / reviews on"
by_unit_access_to = " Units / reviews on"
p_add_new_user = "Add new user"
p_update_user_data = "Update user data"
p_delete_user = "delete user"

[transferaccounts]
p_to = "to"
