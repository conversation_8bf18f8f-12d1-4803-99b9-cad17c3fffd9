
[Setting]
direction = "rtl"
alignment = "right"
valignment = "left"

[Language]
code = "ar"

[Program]
name = "الموارد البشرية"
about = "برنامج الموارد البشرية يعالج مواضيع شؤون العاملين وإعداد مسيرات الرواتب والحضور والإنصراف كما يدير البرنامج بصورة سهله عمليات توظيف وإدارة المستخدمين مثل : الاجازات والانتداب والاستئذان والإقالة وربط ذلك ببيانات الحضور والانصراف للموظف ومسير الرواتب.
تم بناء البرنامج ليتناسب مع مختلف أنواع المنظمات سواء من ناحية طبيعة العمل أو عدد المستخدمين و من خلال قائمة الضبط الخاصة بالبرنامج يمكن التحكم في كثير من المهام والإعدادات التي تتعلق بأداء البرنامج ليتناسب بصورة دقيقه مع خصائص المنظمه كذلك من خلال ضبط الصلاحيات يمكن تخصيص البرنامج ليتناسب مع جميع المستخدمين بمختلف مهامهم.
يعتمد برنامج الموارد البشرية في عمليات التوظيف وإدارة المستخدمين على برنامج الدليل التنظيمي لذا يجب تفعيل برنامج الدليل التنظيمي أولاً وإعداد هياكل الوحدات والوظائف من خلاله وتفعيلها حتى تكون متاحه للإستخدام داخل برنامج الموارد البشرية."

[Menus]
HumanResource = "شؤون العاملين"
personnel = "المعاملات الإدارية"
HumanResourcesArchive = " الإرشيف"
HumanResourceReports = "التقارير"
HumanResourceSettings = "الضبط"

[Operations]
HrDashboard = "بوابة الموارد البشرية"
recruitment = "التعيين"
employees = "تقرير الموظفون"
editcreditrequest = "طلب تعديل رصيد"
payroll = "المرتبات"
fingerPrintIntervals = "بيانات الحضور والإنصراف"
committeebuilding = "تكوين اللجان"
attendance = "مساءلة غياب"
hrlatency = "مساءلة تأخر"
deductaddition = "طلبات الإضافة والخصم"
circulateleave = "الإجازات المجدولة"
PersonnelArchive = "إرشيف المعاملات الإدارية"
HrReportHumanResourceInformation = "معلومات الموارد البشريه"
HrReportEmployees = "تقرير البيانات والسجل الوظيفي للموظف"
HrReportRequests = "تقرير الطلبات العام"
HrAttendanceDepartureRecords = "تقرير بيانات الحضور والانصراف"
HrEmployeesLeavesCredit = "تقرير أرصدة الإجازات"
HrReportSalaries = "تقرير المرتبات"
HrReportOpportunitiesOccupiedJobs = "تقرير الوظائف"
HrReportEmployeeDataSheet = "تقرير كشف بيانات الموظفين"
orguntmanager = "ضبط مديرو الوحدات"
leavesettings = "ضبط الإجازات"
fingerPrintDevices = "ضبط أجهزة البصمة"
hrdoamwrdiah = "ضبط دوامات العمل"
empportalsetting = "ضبط بوابة الموظف"
transferaccounts = "ضبط حسابات التحويل"
NewPayrollBatch = "مسير الرواتب"
FunctionalOperation = "العمليات الوظيفية"
IdentityReport = "تقرير الهوية"

[HrDashboard]
p_requests_un_sent = "غير مرسل"
p_requests_under_process = "تحت المعالجه"
p_requests_sent = "منتهي"
p_outbox = ""
p_dowams = "دوامات العمل"
p_work_days = "ايام الدوام"
P_weekends = "ايام العطل"
p_there_is_no_dowams = "لا توجد دوامات عمل"
p_hr_requests = "طلبات الموارد البشرية"
p_requests_type = "نوع الطلب"
p_requests_total = "المجموع"
no_recent_unimported_transactions = ""
p_employees_requests = "طلبات بوابة الموظف"
p_unemployeed = "غير المعينين"
p_confirmed_leaves = "الاجازات المعتمده"
p_employees_details = "كشف بيانات الموظفين"
p_financial_detailes = "البيانات المالية"
p_deduct_add_analysis = "الإضافات و الحسومات"
p_hr_details = "معلومات الموارد البشرية"

[recruitment]
p_recruite = "التعيين"
p_appointment_report = "تقرير التعيين"
p_request_sent_successfully = "تم إرسال الطلب بنجاح"
p_send_belonging_request = "إرسال طلب تعيين"
p_recruite_success = "تم التعيين بنجاح"
p_browse_recruit_requests_sent_to_user = "إستعراض طلبات التوظيف"
p_recruit_request_ask_date = "تاريخ إرسال طلب التوظيف"
p_recruit_request_ask_comment = "التعليق في طلب التوظيف"
p_recruit_request_replay_date = "تاريخ الرد على طلب التوظيف"
p_recruit_request_replay_comment = "التعليق في الرد على طلب التوظيف"
p_messages_notes = "ملاحظات"
p_recruite_in_job_report = "التعيين"
p_update_this = "تم تحديث "
p_the_human_resource = "شئون العاملين"
p_you_are_dismissed_from_vacant = "تمت إقالتك من هذه الوظيفة"

[employees]
p_employee_report = "تقرير الموظفون"
p_job_code = "رمز الوظيفة"
p_show_hr = "صلاحية "
p_employee_record = "السجل الوظيفي"
p_convert_job_to_primary = "تحويل الوظيفة إلى أساسية"
p_dismiss_from_job = "الإقالة من الوظيفة"
p_date_of_appointment = "تاريخ التعيين"
p_end_date_of_appointment = "تاريخ انتهاء العقد"
p_direct_chief = "الرئيس المباشر"
p_job_salary_type = "أجر الوظيفة"
p_base_salary = "المرتب الأساسي"
p_salary = "المرتب"
p_payroll_template = ""
p_subject_to_presense_and_leave = "خاضع للحضور والإنصراف"
p_not_subjected = "غير خاضع"
p_subject_to_presense_and_leave_based_on_job_time = "خاضع للحضور والإنصراف بالدوام"
p_subject_to_presense_and_leave_based_on_hour = "خاضع للحضور والإنصراف بالساعه"
p_fingerprint_device = "جهاز البصمه"
p_no_on_fingerprint_device = "الرقم بجهاز البصمه"
p_user_no_in_presense_and_leave_device = "الرقم الخاص بالمستخدم في جهاز الحضور والإنصراف"
p_hired_employee_name = "إسم الموظف المُعَيََّن"
p_attach_title = "إرفاق ملف"
p_employement_record_for = "السجل الوظيفي لـ"
p_employement_reocrd_report = "تقرير السجل الوظيفي"
p_dismissal_date = "تاريخ الإقالة"
p_dismissal_reasons = "أسباب الإقاله"
p_dismissal_reason = "سبب الإقالة"
p_dismiss_employee_alert = "تنبيه!، عملية الإقالة نهائية ولا يمكن التراجع عنها إلا بإعادة تعيين الموظف مره أخرى في نفس الوظيفة"
p_dismissal_implementation = "تنفيذ الإقالة"
p_converting_job_to_brimary_make_all_others_secondary = " تحويل الوظيفة إلى أساسية يقتضى تحول الوظائف الأخرى للموظف إلى وظائف ثانوية"
p_adjust_leave_templates = "ضبط قوالب الإجازات"
p_employees_available_leaves = "الإجازات المتاحه للموظف"
p_leave_name = "إسم الإجازة"
p_credit_type = "نوع الرصيد"
p_credit_max_limit_when_transfer = "الحد الأعلى للرصيد عند الترحيل"
p_employee_available_credit = "الرصيد المتاح للموظف"
p_untransferable_leave = "لا يمكن ترحيلها"
p_unlimited = "بدون حد أعلى"
p_the_record = "السجل"
p_add_deduct_leave_credit = "إضافة / خصم رصيد الإجازات"
p_adjust_employee_leave = "ضبط الإجازة للموظف"
p_leave_type = "نوع الإجازة"
p_leave_credit = "رصيد الإجازة"
p_max_credit_limit = "الحد الأعلى للرصيد"
p_allowedleave_max_credit = "الحد الأعلى"
p_start_counting_leave_date = "بداية إحتساب الإجازة"
p_types_of_available_leaves = "أنواع الإجازات المتاحة"
p_need_to_determine_gender_first = "عفواً يجب تحديد جنس الموظف أولاً"
p_num_of_required_days = "عدد الأيام المطلوبة"
p_duration_by_days = "المدة بالأيام"
p_annual_leave_credit = "الرصيد السنوي"
p_monthly_credit = "رصيد الشهر"
p_added_credit = "الرصيد المضاف"
p_deducted_credit = "الرصيد المخصوم"
p_transfer_credit = "ترحيل الرصيد"
p_max_size = "أعلى حجم"
p_max_number = "أعلى رقم"
p_extensions = "إمتداد"
p_affecting_day_price_estimation = "يؤثر في إحتساب قيمة اليوم"
p_add_new_term = "إضافة بند جديد"
p_term_name = "إسم البند"
p_deleting_payslip_consequences = "حذف مسير رواتب يؤدي إلى حذف جميع البيانات المرتبطه به، ويقوم بتحرير سجلات الغياب والسلف والحسم المرتبطه به."
p_advance_installment = "قسط السلفة"
p_installment_discounted = "تم خصم القسط"
p_installment_not_discounted = "لم يتم خصم القسط"
p_absence = "الغياب"
p_no_absence_records = "لا توجد سجلات غياب"
p_advances = "السلف"
p_no_advances_requests = "لا توجد سجلات سلف"
p_deducting_requests = "طلبات الخصم"
p_no_deducting_requests = "لا توجد طلبات خصم"
p_the_absence = "الغياب"
p_number_of_duty_days = "عدد أيام العمل"
p_number_of_absence_days = "عدد أيام الغياب"
p_deduction_value = "قيمة الخصم"
p_set_date_and_lang = "ضبط التاريخ واللغة"
p_latency = "مساءلة التأخر"
p_choose_unit = "اختر الوحدة/القسم"
p_unit_or_depart = "الوحدة/القسم"
p_job_type = "نوع الوظيفة"

[payroll]
p_payslips_preparation = "إعداد المسيرات"
p_payslips_preparation_new = "إعداد المسيرات"
p_payslip_creation = "إنشاء المسيرات"
p_adjust_employees = "ضبط الموظفين"
p_salaries_template = "قوالب المرتبات"
p_payslip = "المسير"
p_there_are_not_reveiwed_records = "عفواً توجد سجلات غير مدققة"
p_monthly_salary_payslips = "مسيرات الأجر الشهري"
p_daily_salary_payslips = "مسيرات الأجر اليومي"
p_monthly_salary_employees = "موظفو الأجر الشهري"
p_daily_salary_employees = "موظفو الأجر اليومي"
p_salary_template = "قالب المرتبات"
p_template_create = "بناء القالب"
p_template_type = "نوع القالب"
p_estimate_day_price = "إحتساب قيمة اليوم"
p_affecting_day_price_estimation = "يؤثر في إحتساب قيمة اليوم"
p_reveiwing_status = "حالة التدقيق"
p_salary_net_after_additions_must_not_be_less_than_zero = " صافي المرتب بعد الإضافات يجب أن لا يقل عن الصفر."
p_salary_record = "كشف مرتب"
p_additions_total = "مجموع الإضافات"
p_sorry_no_approved_salary_records = "عفواً لا توجد سجلات مرتب معتمدة"
p_excluded_employees = "الموظفون المستبعدون"
p_deleting_payslip_consequences = "حذف مسير رواتب يؤدي إلى حذف جميع البيانات المرتبطه به، ويقوم بتحرير سجلات الغياب والسلف والحسم المرتبطه به."
p_salaries_recoed_information = "معلومات كشف المرتبات"
p_payslip_name = "إسم المسير"
p_delete_template = "حذف قالب"
p_add_new_term = "إضافة بند جديد"
p_term_name = "إسم البند"
p_edit_term = "تعديل بند"
p_add_new_salary_template = "إضافة قالب مرتبات جديد"
p_template_name = "إسم القالب"
p_edit_salary_template = "تعديل قالب مرتبات"
p_salary_type = "نوع الأجر"
p_choose_salary_template = "إختيار قالب مرتب"
p_should_bulid_monthly_salary_templates_first = "يجب إعادة بناء المسير من جديد"
p_should_bulid_daily_salary_templates_first = "يجب بناء قوالب مرتبات الأجر اليومي أولاً"
p_adjust_reveiwing_status = "ضبط حالة التدقيق"
p_bonuses_overall = "إجمالي العلاوات"
p_net_salary = "صافي المرتب"
p_payment_record = "سجل الدفع"
p_reveiwed = "مدقق"
p_edit_payrollBatch = "تعديل المسيير"
p_delete_payrollBatch = "حذف المسيير "
p_not_reveiwed = "غير مدقق"
p_absence = "مساءلة غياب"
p_no_absence_records = "لا توجد سجلات غياب"
p_latency = "مساءلة التأخر"
p_advances = "السلف"
p_no_advances_requests = "لا توجد سجلات سلف"
p_deducting_requests = "طلبات الحسم"
p_no_deducting_requests = "لا توجد طلبات حسم"
p_addition_requests = "طلبات الإضافة"
p_no_addition_requests = " لا توجد طلبات إضافة"
p_the_absence = "الغياب"
p_number_of_duty_days = "عدد أيام العمل"
p_number_of_absence_days = "عدد أيام الغياب"
p_deduction_value = "قيمة الخصم"
p_add_payslip = "إضافة مسير"
p_from_salary = "من المرتب"
p_auditor = "المُراجع"
p_approved_by = "المعتمد"
p_reveiw_and_confirm_payslip_fully_depend_on_it_manager_workflow_should_bulid_workflow_with_auditor_and_approver_determined_from_record = "مراجعة وإعتماد المسير تعتمد بشكل أساسي على مخطط الإجراء ببرنامج إدارة التقنية، يجب بناء المخطط بحيث يحدد فيه المراجع والمعتمد من السجل."
p_edit_payslip = "تعديل مسير"
approved_by = "أعتمد بواسطة"
p_build_payslip = "بناء مسير رواتب"
p_building_payslip_describtion = "بناء المسير هي العملية التي يقوم فيها النظام بإحتساب قوالب المرتبات لجميع الموظفين التابعين لقوالب المرتبات الداخلة في تكوين المسير"
p_tamplate_or_extra_or_deductions_alteration_not_affecting_payslip_after_build = "أي تعديل على القوالب أو البدلات والحسومات التابعة لها لا تؤثر على المسير بعد بنائه."
p_rebulid_payslip_to_apply_tamplate_or_extra_or_deductions_alteration = "يجب عليك إعادة بناء المسير في كل مره أحدثت فيها تعديل على القوالب والبدلات والحسومات وأردت تضمين تلك التعديلات في المسير"
p_delete_payslip = "حذف مسير"
p_there_are_not_approved_payslips = "عفواً توجد مسيرات موظفين غير معتمدة"
p_approve_payslip_and_send_it_to_financial_accounting = "إعتماد المسير وإرساله للموارد المالية"
p_net_after_deductions = "الصافي بعد الخصومات الأخرى"
p_review_all_records = "تدقيق كل السجلات"
p_important_information_about_payslip = "                    من المهم المرور على سجلات المرتبات كل على حده ومراجعتها والتأكد من صحة المبالغ الوارده في كل مسير، لا يمكن تعديل السجلات بعد إعتماد المسير وإرساله لبرنامج المحاسبة المالية، وفي حال التأكد من المبالغ بناءاً على نتيجة مسيرات رواتب سابقة بإمكانك تحويل جميع المسيرات إلى حالة تم التدقيق للإختصار."
p_convert_all_records_to_reviewed = "تحويل جميع السجلات إلى مدققة"
p_convert_all_records_to_not_reviewed = "تحويل جميع السجلات إلى غير مدققة"
p_payroll_batch_view = "إنشاء مسيرات الرواتب"
p_payroll_batch_save = "تعديل مسيرات الرواتب"

[fingerPrintIntervals]
p_record_name = "إسم السجل"
p_records = "السجلات"
p_add_attendance_record = "إضافة سجل حضور وإنصراف"
p_period_name = "إسم الفتره"
p_edit_attendance_record = "تعديل سجل حضور وإنصراف"
p_record_confirm_delete_data = "هل تريد بالتأكيد حذف بيانات الحضور والإنصراف للشهر أدناه؟"
p_record_delete_note = "عند حذف شهر فإن جميع بيانات الحضور والإنصراف المرتبطه بهذا الشهر يتم حذفها كذلك."
p_attach_attendance_file = "إرفاق ملف الحضور والإنصراف"
p_fingerPrint_edit = "تعديل بصمة"
p_fingerPrint_origin = "البصمات الأصلية"
p_data_file = "ملف البيانات"
p_record_create = "بناء السجل"
p_finger_print_records = "سجلات البصمه"
p_attendance_and_leave_records = "سجلات الحضور والإنصراف"
p_original_finger_print_records = "سجلات البصمة الأصلية"
p_edited_finger_print_records = "سجلات البصمة المعدلة"
p_work_duration = "مدة العمل"
p_delay_duration = "مدة الخصم"
p_extra_duration = "مدة الإضافي"
p_absence_duration = "مدة الغياب"
p_no_compound_request = "المشفوعات"
p_doam_information = "بيانات الدوام"
p_not_complete_fingerprint_options = "السجل غير مكتمل"
p_extra_time_options = "الوقت الإضافي"
p_out_of_range_fingerprint_options = "البصمة قبل/بعد وقت الدخول/الخروج"
p_early_entry_start = "بداية الدخول المبكر"
p_doam_starting_time = "بداية الدوام"
gp_start_deduction_time = "بداية وقت الخصم"
p_start_absent_time = "بداية وقت الغياب"
p_end_absent_time = "نهاية وقت الغياب"
p_start_early_logout_time = "بداية السماح بالخروج المبكر"
p_doam_ending_time = "نهاية الدوام"
p_late_exit_time = "نهاية وقت الخروج المتأخر"
p_finger_print_in_time = "بصمة الدخول"
p_finger_print_out_time = "بصمة الخروج"
p_finger_print_work_duration = "مدة العمل"
p_finger_print_latency_duration = "مدة التأخر"
p_finger_print_extra_duration = "مدة العمل الإضافي"
p_finger_print_absence_duration = "مدة الغياب"
p_finger_print_record_status = "حالة سجل الفترة"
p_browse_or_edit_finger_print_records = "إستعراض/تعديل سجل البصمات"
p_finger_print_edit_conditions = "- يجب كتابة الوقت بالساعة والدقيقة<br> - كل وقت في سطر منفصل بدون مسافات بيضاء<br> - يجب أن يكون العدد الكلي للمواقيت زوجي"

[committeebuilding]
p_commitee_building = "بناء اللجان"
p_commitee_name = "إسم اللجنة"
p_committee_purpose = "الهدف العام للَّجنة"
p_committee_members = "أعضاء اللجنة"
p_member_name = "إسم العضو"
p_committeemember_role = "المهمة داخل اللجنة"
p_add_new_committee = "إضافة لجنة جديدة"
p_edit_committee = "تعديل بيانات لجنة"
p_delete_commitee_will_delete_related_data_in_other_programs = " تنبيه : عند حذف هذه اللجنة سيتم حذف كل البيانات المتعلقة بها في البرامج الأخرى"
p_add_new_member = "إضافة عضو جديد"
p_choose_member = "إختر الموظف"
p_edit_member = "تعديل بيانات عضو"
p_delete_member = "حذف عضو"

[attendance]
p_salary_deducted = ""
p_leave_credit_deducted = ""
p_not_belong_to_doam = ""
p_create_absence_record = ""
record_type = ""
p_original_finger_print_records = ""
p_edited_finger_print_records = ""
p_work_duration = ""
p_delay_duration = ""
p_extra_duration = ""
p_absence_duration = ""
p_no_compound_request = ""
p_you_can_choose_one_of_leaves_below_to_deduct_absence_days_from_it = ""
p_cannot_deduct_from_this_leave_type = ""
p_cannot_deduct_from_this_leave_type_note = ""
p_you_will_be_able_to_deduct_absence_amount_from_user_salary_when_prepare_payslip = ""
p_when_choose_not_to_deduct_absence_will_not_affect_employee_leave_credit_or_his_salary = ""
p_absence_details = ""
p_work_periods = ""

[hrlatency]
p_salary_deducted = ""
p_leave_credit_deducted = ""
p_not_belong_to_doam = ""
p_create_latency_record = ""
p_original_finger_print_records = ""
p_edited_finger_print_records = ""
p_work_duration = ""
p_delay_duration = ""
p_extra_duration = ""
p_no_compound_request = ""
p_you_can_choose_one_of_leaves_below_to_deduct_latency_days_from_it = ""
p_you_will_be_able_to_deduct_latency_amount_from_user_salary_when_prepare_payslip = ""
p_when_choose_not_to_deduct_latency_will_not_affect_employee_leave_credit_or_his_salary = ""
p_absence_duration = ""

[circulateleave]
p_not_populated = "غير معممه"
p_populate_leave = "تعميم"
p_populated = "تم التعميم"
p_cancel_population = "إلغاء التعميم"
p_populate_scheduled_leave = "تعميم الإجازة المجدولة"
p_cancel_leave_populatiton = "إلغاء تعميم الإجازة المجدولة"
p_are_you_really_wants_to_populate_this_schedule_leave_all_employees_day_record_will_be_affected = "هل تريد بالتأكيد تعميم هذه الإجازة المجدولة، تعميمها سيؤثر على سجلات جميع الموظفين"
p_are_you_really_wants_to_cancel_populating_this_leave_all_employees_day_record_will_be_affected = "هل تريد بالتأكيد إلغاء تعميم هذه الإجازة المجدولة، إلغاء تعميمها سيؤثر على سجلات جميع الموظفين"
p_leave_name = "الإسم"
p_choose_scheduled_leave = "إختر إجازة مجدولة"

[PersonnelArchive]
p_absence = "مساءلة غياب"
p_latency = "مساءلة تأخر"
p_addition_deduction_request = "طلب إضافة/خصم"
p_undo_archiving = "إستعاده"

[HrReportHumanResourceInformation]
p_qualifications = "حسب المؤهل"
p_jobs = "الوظائف"
p_nationalities = "الجنسيات"
p_doam = "الدوام"

[HrReportEmployees]
p_attandace_accountabilites = "مساءلات الغياب"
p_latency_accountabilites = "مساءلات التأخر"
p_commitee_name = "إسم اللجنة"
p_committee_purpose = "الغرض من اللجنة"
p_committee_roles = "المهام في اللجنة"
p_no_committees = "الموظف ليس عضوا في اي لجنة"
p_job_title = "المسمى الوظيفي"
p_job_type = "نوع الوظيفة"
p_contract_start_data = "تاريخ بداية العقد"
p_contract_end_data = "تاريخ نهاية العقد"
p_primary_data = "المعلومات الأساسية"
p_license = "الإتفاقية"
p_days = "الأيام"
p_job_name = "إسم الوظيفة"
p_job_code = "كود الوظيفة"
p_job_unit = "الوحدة"
p_job_def = "تعريف"
p_emp_sex = "جنس الموظف"
p_doam_type = "نوع الدوام"
p_job_gnr_def = "تعريف عام"
p_desc = "وصف"
p_res_tasks = "مهام ومسؤوليات الوظيفة"
p_relations = "العلاقات "
p_direct_boss = "الرئيس المباشر"
p_emp_supervising = "يشرف على"
p_in_org_contact = "داخل المنظمة يتواصل مع"
p_out_org_contact = "خارج المنظمة يتواصل مع"
p_emp_power = "سلطات الوظيفة"
p_job_invironment = "بيئة الوظيفة"
p_job_places = "أمكنة الوظيفة"
p_eidt_job_invorn = "بيئة الوظيفة"
p_job_condition = "شروط الوظيفة"
p_job_coalif = "مؤهلات الوظيفة"
p_work_exep = "الخبرات العملية"
p_maarif = "معارف الوظيفة"
p_rep_job_name_ar = "إسم الوظيفة"
p_rep_job_unit = "وحدة الوظيفة"
p_job_card_report = "تقرير بطاقة الوظيفة"
p_edit_job_code = "تعديل كود الوظيفة"
p_edit_job_def = "تعريف الوظيفة"
p_edit_job_type = "نوع الوظيفة"
p_edit_emp_sex = "جنس الموظف"
p_edit_doam_type = "نوع الدوام"
p_edit_job_gnr_def = "التعريف العام للوظيفة"
p_edit_res_tasks = "مسؤوليات الوظيفة"
p_edit_relations = "علاقات الوظيفة"
p_edit_direct_boss = "الرئيس المباشر"
p_edit_emp_supervising = "يشرف على"
p_edit_in_org_contact = "يتواصل داخل المنشأة مع"
p_edit_out_org_contact = "يتواصل خارج المنشأة مع"
p_edit_emp_power = "سلطات الوظيفة"
p_edit_job_invironment = "بيئة الوظيفة"
p_edit_job_places = "أماكن الوظيفة"
p_edit_job_condition = "شروط الوظيفة"
p_edit_job_coalif = "مؤهلات شاغل الوظيفة"
p_edit_work_exep = "الخبرات العملية لشاغل الوظيفة"
p_edit_maarif = "الخبرات العلمية لشاغل الوظيفة"
p_advance_installment = "قسط السلفة"
p_installment_discounted = "تم خصم القسط"
p_installment_not_discounted = "لم يتم خصم القسط"
p_absence = "الغياب"
p_no_absence_records = "لا توجد سجلات غياب"
p_advances = "السلف"
p_no_advances_requests = "لا توجد سجلات سلف"
p_deducting_requests = "طلبات الخصم"
p_no_deducting_requests = "لا توجد طلبات خصم"
p_the_absence = "الغياب"
p_number_of_duty_days = "عدد أيام العمل"
p_number_of_absence_days = "عدد أيام الغياب"
p_deduction_value = "قيمة الخصم"
p_set_date_and_lang = "ضبط التاريخ واللغة"
p_display_lang = "لغة العرض"
p_browse_leave_details = "إستعراض تفاصيل الإجازة"
p_leave_name = "مسمَّى الإجازة"
p_leave_description = "وصف الإجازة"
p_leave_entitled_sex = "الإجازة مسموحة لـ"
p_leave_conditions = "شروط الإجازة"
p_annual_leave_credit = "رصيد الإجازة السنوي"
p_credit_num_of_days = "عدد أيام الرصيد"
p_transfer_credit = "ترحيل الرصيد"
p_end_of_leave_year = "نهاية السنة"
p_credit_max_limit_when_transfer = "حد ترحيل الرصيد الأعلى"
p_type = "النوع"
p_balance = "الرصيد"
p_year_balance = "الرصيد السنوي"
p_max_balance = "حد الرصيد الأعلى"
p_holidays_record = "سجل الإجازات"
p_holidays = "الإجازات"
p_circulate_leave = "إجازة مجدولة"
p_leave_type = "نوع الإجازة"
p_monthly_credit = "رصيد الشهر"
p_added_credit = "الرصيد المضاف"
p_deducted_credit = "الرصيد المخصوم"
p_relax_day = "أيام الراحة"
p_recruite = "التعيين"
p_you_are_recruit_on_this_vacant = "مرحباً، تم تعيينك في هذه الوظيفة"
p_transaction_type = "نوع الحركة"
p_transaction_calculation_type = "إضافة/خصم"
p_transaction_credit = "الرصيد"
p_transaction_created_by = "منشئ الحركة"
p_transaction_created_date = "تاريخ الحركة"
p_transaction_details = "تفاصيل الحركة"
p_income_data = "بيانات الدخل"
p_income_type = "نوع  الدخل"
p_income_value = "قيمة الدخل"
p_income_status = "الحالة"
p_loan_data = "بيانات الديون"
p_loan_type = "نوع  الدين"
p_installment_type = "نوع القسط"
p_loan_value = "قيمة الدين"
p_installment_value = "قيمة القسط"
p_rent_data = "بيانات الإستئجار"
p_house_type = "نوع السكن"
p_building_type = "نوع البناء"
p_house_space = "مساحة المنزل"
p_rent_value = "قيمة الإيجار"
p_current_house = "السكن الحالي؟"
p_term_of_selection = "شرط الإختيار"
p_criteria = "المعايير"
p_history_records = "السجل"
p_beneficiary_history = "سجل المستفيد"
p_materials = "الأعيان"
p_family_history = "سجل الأسرة"
p_indevidual_history = "سجل الفرد"
p_constraint = "الشرط"
p_identity_number = "رقم الهوية"
p_family = "الأسرة"
p_file_number = "رقم الملف"
p_visit_date = "تاريخ الزيارة"
p_committee_comment = "ملاحظات اللجنة"
p_records = "السجلات"
p_constraints = "الاشتراطات"
p_add_new_beneficiary = "اضافة مستفيد جديد"
p_fr_name = "الاسم الاول"
p_sec_name = "الاسم الثاني"
p_thrd_name = "الاسم الثالث"
p_fm_name = "اسم العائلة"
p_classification = "التصنيف"
p_gender = "الجنس"
p_email = "البريد الالكتروني"
p_ayban_number = "رقم الايبان"
p_phone_number = "رقم الجوال"
p_birth_date = "تاريخ الميلاد"
p_sponsorship_number = "رقم الكفالة"
p_live_status = "حالة الحياة"
p_user_live = "حي"
p_user_dead = "متوفي"
p_recommendations = "التوصيات"
p_execution_status = "حالة التنفيذ"
p_id_type = "نوع الهوية"
p_id_no = "رقم الهوية"
p_request_perm = "طلب إذن"
p_request_leave = "طلب إجازة"
p_request_retreat_Leave = "طلب تراجع عن اجازة"
p_request_edit_credit_request = "طلب تغذية رصيد إجازة"
p_request_adv = "طلب سلفة"
p_request_out_work = "طلب عمل إضافي"
p_request_out_work_salary = "طلب أجرة عمل إضافي"
p_request_mandate = "طلب إنتداب"
p_request_mandate_salary = "طلب أجرة إنتداب"
p_finexch_request = "طلب صرف"
p_request = "طلب"
p_not_send_requests = "غير مرسل"
p_sent_requests = "قيد الاجراء"
p_finish_requests = "منتهي"
p_leave_credit = "رصيد الإجازة"
p_permission_type = "نوع الإذن"
p_job_period = "فترة العمل"
p_permission_date = "تاريخ الإذن"
p_emp_name = "إسم الموظف"
p_outwork_period = "الفترة"
p_daily_rate = "المعدل اليومي"
p_hour = "ساعة/ساعات"
p_assign_from = "مسنده من"
p_hours = "عدد الساعات"
p_mandate_days_number = "عدد أيام الإنتداب"
p_mandate_period = "فترة الإنتداب"
p_mandate_reason = "سبب الانتداب"
p_outwork_reason = "السبب"
p_mandate_report = "تقرير الإنتداب"
p_adv_amount = "قيمة السلفة"
p_months = "الشهور"
p_request_status = "حالة الطلب"
p_amount_as_number = "القيمة"
p_choose_leave = "اختر نوع الإجازة"
p_available_credit = "الرصيد المتاح"
p_specify_credit = "حدد الرصيد المطلوب"
p_leave_duration = "مدة الإجازة"
p_required_credit = "الرصيد المطلوب"
p_leave_start_date = "تاريخ بداية الإجازة"
p_choose_alternative = "اختر البديل"
p_back_to_leaves = "العودة لأنواع الإجازات المتاحة"
p_confirm_request = "تأكيد الطلب"
p_leave_end_date = "تاريخ نهاية الإجازة"
p_create_request = "إنشاء الطلب"
p_update_request = "تحديث الطلب"
p_back_to_credit = "العودة لتحديد الرصيد المطلوب"
p_no_more_than_one_leave = "عفواً لا يمكن أخذ أكثر من إجازة في نفس اليوم، لديك إجازة في الأيام التالية:"
p_sorry_your_alternative_in_leave_in_same_duration = "عفواً لدى البديل أدناه أيام إجازة معتمده أثناء الفترة المحدده"
p_no_past_leave = "عفواً لا يمكن أخذ إجازة بتاريخ سابق"
p_chart_first = "عفواً ... يجب تحديد مخطط تنفيذ العملية أولاً"
p_specify_chart = "إضغط هنا للإنتقال لعملية تحديد مخطط التنفيذ : "
p_need_perm = "يظهر زر تحديد المخطط لمن لديه صلاحية الوصول للعملية فقط."
p_add_perm = "إضافة طلب إذن"
p_permission_reason = "سبب الإذن"
p_edit_perm = "تعديل بيانات طلب إذن"
p_add_outwork_request = "طلب عمل إضافي"
p_choose_emp = "اختر الموظف"
p_with_rate = "بمعدل"
p_rate = "المعدل"
p_reason_for_overtime = "سبب العمل الإضافي"
p_edit_out_work = "تعديل بيانات عمل إضافي"
p_edit_out_work_salary = "تعديل بيانات طلب أجرة عمل إضافي"
p_mission_execution_status = "حالة تنفيذ المهمة"
p_job_hours = "ساعات العمل"
p_add_mandate_request = "طلب إنتداب"
p_mandate_days = "عدد أيام الإنتداب"
p_mandate_city = "مدينة الإنتداب"
p_edit_mandate = "تعديل بيانات طلب انتداب"
p_edit_mandate_salary = "تعديل بيانات طلب أجرة انتداب"
p_mission_status = "حالة المهمة"
p_mission_report = "تقرير المهمة"
p_mission_transportation = "هل تم توفير وسيلة مواصلات؟"
p_mission_accommodation = "هل تم توفير السكن؟"
p_mission_expenses = "هل تم توفير تكاليف المعيشة؟"
p_add_advance_request = "إضافة طلب سلفة"
p_amount = "المبلغ الكلي"
p_adv_months = "عدد شهور الاستقطاع"
p_adv_date = "تاريخ بدء الاستقطاع"
p_add_fin_exch = "إضافة طلب صرف"
p_receipt_beneficiary = "المستفيد"
p_amount_written = "المبلغ كتابة"
p_receipt_purpose = "الغرض"
p_edit_fin_exch = "تعديل طلب صرف"
p_delete_exchange_request = "حذف طلب صرف"
p_confirm_delete_exchange_request = "هل تريد بالتأكيد حذف طلب الصرف هذا؟"
p_num_of_required_days = "عدد الأيام المطلوبة"
p_duration_by_days = "المدة بالأيام"
p_no_leave_retrive_requests = "لا توجد طلبات تراجع عن إجازة"
p_no_perm_requests = "لا توجد طلبات اذونات"
p_no_out_work_requests = "لا توجد طلبات عمل إضافي"
p_no_leave_requests = "لا توجد طلبات إجازة"
p_no_outwork_fees_requests = "لا توجد طلبات أجرة عمل إضافي"
p_no_mndtfees_requests = "لا توجد طلبات إجرة إنتداب"
p_no_mandate_requests = "لا توجد طلبات إنتداب"
p_no_advance_requests = "لا توجد طلبات سلف"
p_no_leave_credit_requests = "لا توجد طلبات رصيد إجازات"
p_no_finexch_requests = "لا توجد طلبات صرف"
p_no_attandace_accountabilites = "لا توجد مساءلات غياب"
p_no_latency_accountabilites = "لا توجد مساءلات تأخر"
p_vacation_name = "مسمي الإجازة"
p_avalible_credit = "الرصيد المتاح"
p_yearly_credit = "الرصيد السنوي"
p_yearly_credit_total = "مجموع الرصيد السنوي"
p_available_credit_total = "مجموع الرصيد المتاح"
p_no_vacations = "لا توجد إجازات"
p_fin_sheet = "كشف حساب"
p_fin_year = "السنة المالية"
p_dail_account = "الحساب"
p_fin_from_date = "من"
p_fin_to_date = "إلى"
p_entry_number = "رقم القيد"
p_fin_bian = "البيان"
p_fin_date = "التاريخ"
p_fin_depit = "مدين"
p_fin_credit = "دائن"
p_fin_balance = "الرصيد"
p_fin_comulated_balance = "الرصيد المتراكم"
p_opening_balance = "رصيد افتتاحي للفترة"
p_summary = "الملخص"
p_summary_opening_balance = "رصيد افتتاحي أول الفترة"
p_summary_closing_balance = "رصيد آخر الفترة"
p_summary_debit_transactions_total = "مجموع الحركات المدينة"
p_summary_credit_transactions_total = "مجموع الحركات الدائنة"
p_fin_box_sig = "توقيع أمين الصندوق"
p_fin_acc_sig = "توقيع المحاسب"
p_fin_manag_sig = "توقيع المدير المالي"
p_add_new_transaction = "إنشاء وثيقة جديدة"
p_main_topic = "الموضوع"
p_comment = "الرسالة"
p_language = "لغة الوثيقة"
p_transaction_tags = "تصنيف الوثيقة"
p_privacy_status = "حالة الخصوصية"
p_referral_assignment_type = "النوع"
p_referral_execution_start_date = "تاريخ البداية"
p_referral_execution_duration = "المدة"
p_referral_priority_status = "الأولوية"
p_edit_transaction_data = "تعديل بيانات الوثيقة"
p_document_data = "بيانات الوثيقة"
p_transaction_folder = "المجلد"
p_transaction_mediator = "إستلمنا الوثيقة من"
p_mediator_name = "الإسم"
p_mediator_phone = "رقم الجوال"
p_mediator_email = "البريد الإلكتروني"
p_mediator_date = "تاريخ التوريد"
p_mediator_comment = "ملاحظات التسليم"
p_transaction_carrier = "أرسلنا الوثيقة عبر"
p_carrier_name = "الإسم"
p_carrier_phone = "رقم الجوال"
p_carrier_email = "البريد الإلكتروني"
p_carrier_date = "تاريخ التصدير"
p_carrier_comment = "ملاحظات التصدير"
p_tags = "التصنيف"
p_referral_folder = "المجلد"
p_export_or_import_information = "معلومات التصدير / التوريد"
p_export_number = "رقم الصادر"
p_export_date = "تاريخ الصادر"
p_export_attachments_count = "عدد المشفوعات"
p_import = "توريد"
p_import_number = "رقم الوارد"
p_import_date = "تاريخ الوارد"
p_import_attachments_count = "عدد المشفوعات"
p_transaction_information = "معلومات الوثيقة"
p_referral_information = "بيانات الإحالة"
p_action_needed = "الإجراء المطلوب"
p_my_referral_setting = "الضبط"
p_transaction_referrals = "الإحالات"
p_edit_privilege = "التعديل"
p_refer_privilege = "الإحالة"
p_attach_privilege = "إرفاق الملفات"
p_client_outbox = "صندوق صادر المنشأة"
p_transaction_append_and_comment = "الإلحاق والتعقيب"
p_comment_creator = "منشئ التعقيب"
p_message = "الرسالة"
p_comment_date = "تاريخ التعقيب"
p_sub_topic = "الموضوع الفرعي"
p_referrals = "الإحالات"
p_referral_to = "إحالة إلى"
p_referral_title = "العنوان"
p_title = "العنوان"
p_attach_title = "إرفاق ملف"
p_max_size = "الحجم الأعلى"
p_max_number = "الحد الأعلى"
p_documents = "مستندات"
p_extensions = "الإمتداد"
p_add_referral = "إضافة إحالة"
p_subject = "الموضوع"
p_referral_type = "نوع الإحالة"
p_referral_privileges = "الصلاحيات"
p_referral_to_client_outbox = "تصدير عبر صندوق صادر المنشأة"
p_referral_destination = "وجهة الإحالة"
p_publication_according_to = "تعميم حسب"
p_unit_managers = "مدراء الوحدات"
p_user_classes = "التصانيف"
p_edit_referral = "تعديل إحاله"
p_view_referral = "استعراض الإحالة"
p_referral_execution_type = "التنفيذ"
p_referral_transaction_execution_status = "حالة التنفيذ"
p_delete_referral = "حذف إحالة"
p_referral_deletion_confirm_message = "هل تريد التأكيد حذف هذه الإحالة؟"
p_add_append_comment = "إضافة إلحاق/تعقيب"
p_edit_append_comment = "تعديل إلحاق/تعقيب"
p_delete_append_comment = "حذف إلحاق/تعقيب"
p_edit_referral_status = "تعديل حالة تنفيذ الإحالة"
p_transaction_execution_types = "خيارات التنفيذ"
p_comment_from = "التعقيب من"
p_attachments_count = "عدد المشفوعات"
p_serial_number = "الرقم التسلسلي"
p_manual = "يدوي"
p_automatic = "تلقائي"
p_year = "السنة"
p_year_and_month = "السنة والشهر"
p_year_and_month_and_day = "السنة والشهر واليوم"
p_delivered_by = "تم التوصيل عن طريق"
p_date_must_be_change_from_it = "التاريخ يتم تغييره بواسطة مدير التقنية"
p_direct_master_job = "الرئيس المباشر"
p_view_job_card = "إستعراض البطاقة"

[HrReportRequests]
p_choose_request_type = "اختر نوع الطلب"
p_creator = "منشيء الطلب"
p_owner = "المستفيد"
p_request_status = "حالة الطلب"
p_human_resource_information = "تقرير الطلبات العام"

[HrAttendanceDepartureRecords]
p_employees_list = "قائمه الموظفين"
p_original_finger_print_records = "سجلات البصمة الأصلية"
p_edited_finger_print_records = "سجلات البصمة المعدلة"
p_work_duration = "مدة العمل"
p_delay_duration = "مدة الخصم"
p_extra_duration = "مدة الإضافي"
p_absence_duration = "مدة الغياب"
p_human_resource_attendance_departure_records = "تقرير الحضور والانصراف"
p_employee_name = "اسم الموظف"

[HrEmployeesLeavesCredit]
p_leave_name = "إسم الإجازة"
p_credit_type = "نوع الرصيد"
p_credit_max_limit_when_transfer = "الحد الأعلى للرصيد عند الترحيل"
p_employee_available_credit = "الرصيد المتاح للموظف"
p_untransferable_leave = ""
p_unlimited = ""
p_the_record = ""
p_annual_leave_credit = ""
p_monthly_credit = ""
p_added_credit = ""
p_deducted_credit = ""
p_transfer_credit = ""
p_employement_leave_reocrd_report = "تقرير ارصدة الإجازات"

[HrReportSalaries]
p_with_slip = "المسير"
p_slip = "المسير"
p_salaries_report = "تقرير المرتبات"

[HrReportOpportunitiesOccupiedJobs]
p_total_count_of_jobs_in_units = "العدد الكلي للوظائف في الوحده"
p_total_digits = "العدد الكلي للخانات"
p_count_of_occupied_jobs = "عدد الخانات المشغولة"
p_count_of_opportunities_jobs = "عدد الخانات الشاغره"
p_couupied_jobs = "الوظائف المشغولة"
p_job_type = "نوع الوظيفة"
p_opportunities_jobs = "الوظائف الشاغرة"
p_human_resource_opportunities_occupied_job = "تقرير الوظائف المشغولة والشاغرة"
p_choose_unit = "إختر الوحدة/القسم"

[HrReportEmployeeDataSheet]
p_build_report = "بناء التقرير"
p_basic_job = "الوظيفة"
p_start_working_with_organization_date = "بداية العقد"
p_end_working_with_organization_date = "نهاية العقد"
p_slip = "المسير"
p_template = "القالب"
p_basic_salary = "الراتب الأساسي"
p_allowances = "العلاوة"
p_deductions = "الحصومات"
p_other_deductions = "خصومات أخرى"
p_paid = "المدفوع"
p_not_paid = "غير المدفوع"
p_net_salary = "الصافي"
p_bank_account_name = "اسم البنك"
p_ayban_number = "رقم الايبان"
p_identity_number = "رقم الهوية"
p_employee_data_sheet_reprot = "تقرير كشف الموظفين"

[orguntmanager]
p_unitcode = "كود الوحدة"
p_add_unit_emp_sex = "جنس منسوبي الوحده"
p_unit_card = "بطاقة وحده"
p_unit_name = "إسم الوحده"
p_unit_code = "رمز الوحده"
p_unit_employees_sex = "جنس منسوبي الوحده"
p_unit_duties = "مهام الوحده"
p_edit_unit_data = "تعديل بيانات وحده"

[leavesettings]
p_leaves_templates = "قوالب الإجازات"
p_schediuled_leaves = "الإجازات المجدولة"
p_weekend_leaves = "الإجازة الأسبوعية"
p_leave_name = "إسم الإجازة"
p_leave_name_add_row = "إنشاء إجازة"
p_leave_type = "نوع الإجازة"
p_leave_duration = "مدة الإجازة"
p_leave_days = "أيام الإجازة"
p_leave_description = "وصف الإجازة"
p_leave_entitled_sex = "جنس مستحقي الإجازة"
p_leave_conditions = "شروط الإجازة"
p_annual_leave_credit = "الرصيد السنوي"
p_credit_num_of_days = "عدد أيام الرصيد"
p_transfer_credit = "ترحيل الرصيد"
p_credit_max_limit_when_transfer = "الحد الأعلى للرصيد عند الترحيل"
p_end_of_leave_year = "نهاية سنة الإجازة"
p_edit_job_type_details = "تعديل بيانات نوع إجازة"
p_helping_guide = "دليل المساعده"
p_cant_delete_this_type_of_leave_because_its_in_use = "لا يمكن حذف نوع إجازة مستخدم سلفا ً، إلغاء التفعيل سيمنع إستخدام هذا النوع مجدداً"
p_cant_delete_this_type_of_scheduled_leave_because_its_in_use_u_can_deactivate_it_and_create_new_one = "لا يمكن حذف نوع إجازة مجدولة مستخدم سلفاً، بإمكانك تحويل نوع الإجازة إلى غير فعال وإنشاء نوع إجازة مجدول جديد."

[fingerPrintDevices]
p_add_new_device = "إضافة جهاز جديد"
p_edit_presence_and_parting_device = "تعديل بيانات جهاز حضور وإنصراف"
p_delete_presence_and_parting_device = "حذف جهاز حضور وإنصراف"
p_confirm_delete_presence_and_parting_device = "هل تريد بالتأكيد حذف جهاز والحضور هذا؟"
p_delete_job_time = "حذف دوام"
p_helping_guide = "دليل المساعده"
p_cant_delete_in_use_device_unless_all_employees_using_this_device_transfered_to_another_one = "لا يمكن حذف جهاز بصمه مستخدم، يجب أولاً تحويل الموظفون الذين يستخدمون هذا الجهاز إلى جهاز آخر، ومن ثم يمكنك حذف الجهاز."

[hrdoamwrdiah]
p_period_name = "إسم الفتره"
p_from_to = "من/إلى"
p_add_new_doam = "إضافة دوام جديد"
p_doam_name = "إسم الدوام"
p_out_of_range_fingerprint_options = "البصمة قبل/بعد وقت الدخول/الخروج"
p_not_complete_fingerprint_options = "السجل غير المكتمل"
p_extra_time_options = "الوقت الإضافي"
p_edit_doam = "تعديل بيانات دوام"
p_add_new_period = "إضافة فترة جديدة"
p_early_login_time = "بداية الدخول المبكر"
p_doam_starting_time = "بداية الدوام"
p_start_deduction_time = "بداية وقت الخصم"
p_start_absent_time = "بداية وقت الغياب"
p_end_absent_time = "نهاية وقت الغياب"
p_start_early_logout_time = "بداية السماح بالخروج المبكر"
p_doam_ending_time = "نهاية الدوام"
p_late_exit_time = "نهاية وقت الخروج المتأخر"
p_edit_period = "تعديل بيانات فترة"
p_delete_period = "حذف فترة"
p_cant_delete_in_use_doam_unless_all_employees_using_this_doam_transfered_to_another_one = "لا يمكن حذف دوام مستخدم، يجب أولاً تحويل الموظفون الذين يستخدمون هذا الدوام إلى دوام آخر، ومن ثم يمكنك حذف الدوام."

[empportalsetting]
p_contact_data_setting = "ضبط دليل الإتصالات"
p_regulations_library = "مكتبة لوائح العمل"
by_gender_access_to = "الجنس/يطلع على"
by_unit_access_to = "الوحدات/يطلع على"
p_add_new_user = "إضافة مستخدم جديد"
p_update_user_data = "تحديث بيانات المستخدم"
p_delete_user = "حذف المستخدم"

[transferaccounts]
p_to = " إلى"

[Impeded]
employees_job_date_of_appointment_date_msg = "يجب أن يكون تاريخ التعيين صحيحاً"
employees_direct_boss_emp_numeric_msg = "يجب أن يتم إختيار الرئيس المباشر بصورة صحيحه"
employees_salary_basic_required_if_msg = "يجب تحديد قيمة المرتب الأساسي"
employees_days_in_month_required_if_msg = "يجب تحديد عدد الأيام في الشهر"
employees_days_salary_required_if_msg = "يجب تحديد أجر اليوم"
employees_hour_salary_required_if_msg = "يجب تحديد أجر الساعة"
employees_att_doam_id_required_if_msg = "يجب تحديد دوام العمل للموظف"
employees_att_doam_id_numeric_msg = "يجب أن يتم إختيار دوام العمل بصورة صحيحة"
employees_att_device_id_required_if_msg = "يجب تحديد جهاز الحضور والإنصراف صحيحه"
employees_att_device_id_numeric_msg = "يجب تحديد جهاز الحضور والإنصراف صحيحه"
employees_att_device_num_required_if_msg = "يجب كتابة رقم الموظف بجهاز الحضور والإنصراف بصورة صحيحة"
employees_att_device_num_numeric_msg = "يجب كتابة رقم الموظف بجهاز الحضور والإنصراف بصورة صحيحة"
employees_att_device_num_min_msg = "يجب كتابة رقم الموظف بجهاز الحضور والإنصراف بصورة صحيحة"
employees_att_doam_id_min_msg = "يجب تحديد دوام الموظف بصورة صحيحة"
employees_att_device_id_min_msg = "يجب كتابة رقم الموظف بجهاز الحضور والإنصراف بصورة صحيحة"
empportalsetting_job_date_of_appointment_date_msg = "يجب تحديد تاريخ التعيين"
empportalsetting_salary_basic_required_if_msg = "يجب تحديد المرتب الأساسي"
empportalsetting_days_in_month_required_if_msg = "يجب تحديد عدد الأيام في الشهر"
empportalsetting_days_salary_required_if_msg = "يجب تحديد عدد أيام المرتب"
empportalsetting_hour_salary_required_if_msg = "يجب تحديد عدد ساعات دوام العمل"
empportalsetting_att_doam_id_required_if_msg = "يجب تحديد دوام العمل"
empportalsetting_att_doam_id_min_msg = "يجب إختيار دوام العمل من القائمة"
empportalsetting_att_device_id_required_if_msg = "يجب تحديد جهاز البصمة"
empportalsetting_att_device_id_numeric_msg = "يجب إختيار جهاز البصمة من القائمة"
empportalsetting_att_device_id_min_msg = "يجب إختيار جهاز البصمة من القائمة"
empportalsetting_att_device_num_required_if_msg = "يجب تحديد رقم الموظف بجهاز البصمة"
empportalsetting_att_device_num_numeric_msg = "يجب تحديد رقم الموظف بجهاز البصمة"
empportalsetting_att_device_num_min_msg = "يجب تحديد رقم الموظف بجهاز البصمة"
payrollSheet = "مسير الرواتب"
from_date = "من تاريخ"
duration = "المدة"
payrollSheetTotal = "مجموع مسير الرواتب"
gnr_created_by = "إنشئ بواسطة"
p_permission_type = "نوع الإذن"
p_permission_date = "تاريخ الإذن"
p_permission_work_interval = "فترة عمل الإذن"
p_permission_reason = "سبب الإذن"
gnr_employee = "الموظف"
gnr_request_type = "نوع الطلب"
gnr_amount = "القيمة"
gnr_reasons = "الاسباب"
p_credit_request_Leave_type = "نوع الإجازة"
p_request_type = "نوع الطلب"
p_credit_request_days_number_demand = "أيام الرصيد المطلوبة"
p_credit_request_from_date = "من تارخ"
p_credit_request_reasons = "المبررات"
p_absented_employee = "الموظف الغائب"
p_absence_start_date = "تاريخ بداية الغياب"
p_absence_end_date = "تاريخ نهاية الغياب"
personnel_end_date_required_msg = "يجب تعبئة تاريخ النهاية"
personnel_end_date_date_msg = "يجب أن يكون تاريخ النهاية تاريخاً صحيحاً"
personnel_end_date_before_msg = "يجب أن لا يكون تاريخ النهاية تاريخ مستقبلي"
personnel_start_date_required_msg = "يجب تعبئة تاريخ البداية"
personnel_start_date_date_msg = "يجب أن يكون تاريخ البداية تاريخاً صحيحاً"
personnel_start_date_after_msg = "يجب أن يكون تاريخ البداية سابقاً لتاريخ النهاية"
personnel_start_date_before_msg = "يجب أن يكون تاريخ البداية سابقاً لتاريخ النهاية"
personnel_start_date_before_or_equal_msg = "يجب أن يكون تاريخ البداية صحيحاً وسابقاً لتاريخ النهاية"
personnel_end_date_before_or_equal_msg = "يجب أن يكون تاريخ النهاية صحيحاً وسابقاً لتاريخ اليوم أو مساو ٍ له"
p_credit_request_user_name = "الموظف المعدل له الرصيد"
p_late_employee = "الموظف المتأخر"
p_latency_start_date = "تاريخ البداية"
p_latency_end_date = "تاريخ النهاية"
p_duration = "المدة"
employees_sh_uao_job_date_of_appointment_date_msg = "عفواً يجب تعبئة تاريخ التعيين"
employees_sh_uao_job_end_date_of_appointment_date_msg = "عفواً يجب تعبئة تاريخ إنتهاء العقد"
p_retreate_leave = "الإجازة"
p_retreat_leave_days_number = "الأيام المتراجع عنها"
p_record_type = ""
attendance_start_date_before_or_equal_msg = "تاريخ البداية لا يمكن أن يكون بعد تاريخ اليوم"
attendance_end_date_required_msg = "تاريخ النهاية مطلوب"
attendance_end_date_before_or_equal_msg = "تاريخ النهاية لا يمكن أن يكون بعد تاريخ اليوم "
hrlatency_start_date_required_msg = "تاريخ البداية مطلوب"
hrlatency_start_date_date_msg = ""
hrlatency_start_date_before_or_equal_msg = "تاريخ البداية لا يمكن أن يكون بعد تاريخ اليوم"
hrlatency_end_date_required_msg = "تاريخ النهاية مطلوب"
hrlatency_end_date_date_msg = ""
hrlatency_end_date_before_or_equal_msg = "تاريخ النهاية لا يمكن أن يكون بعد تاريخ اليوم"
p_permission_time = "وقت الإذن"
personnelPermissionRequest_date_required_msg = ""
personnelPermissionRequest_date_date_msg = ""
personnelMandateRequest_start_date_required_msg = ""
personnelMandateRequest_start_date_date_msg = ""
personnelMandateRequest_start_date_before_or_equal_msg = ""
personnelMandateRequest_start_date_after_or_equal_msg = ""
personnelMandateRequest_end_date_required_msg = ""
personnelMandateRequest_end_date_date_msg = ""
hr_lev = "طلب إجازه"
hr_advancerequest = "طلب سلفة"
hr_prem = "طلب إذن"
hr_otwrk = "طلب عمل إضافي"
hr_outworkfees = "طلب اجرة عمل إضافي"
hr_mndt = "طلب إنتداب"
hr_mndtfees = "طلب اجرة إنتداب"
hr_perm = "طلب إذن"
occupide_jobs_count = "الوظائف المشغولة"
nonoccupide_jobs_count = "الوظائف الشاغره"
hr_attendance = "طلب مساءلة غياب"
hr_deductaddition = "طلب اضافة وخصم"
hr_latency = "طلب مساءلة تأخر"
p_month_milady_1 = "يناير"
p_month_milady_2 = "فبراير"
p_month_milady_3 = "مارس"
p_month_milady_4 = "أبريل"
p_month_milady_5 = "مايو"
p_month_milady_6 = "يونيو"
p_month_milady_7 = "يوليو"
p_month_milady_8 = "أغسطس"
p_month_milady_9 = "سبتمبر"
p_month_milady_10 = "أكتوبر"
p_month_milady_11 = "نوفمبر"
p_month_milady_12 = "ديسمبر"
p_month_hijri_1 = "محرم"
p_month_hijri_2 = "صفر"
p_month_hijri_3 = "ربيع الأول"
p_month_hijri_4 = "ربيع الثاني"
p_month_hijri_5 = "جمادى اﻷول"
p_month_hijri_6 = "جمادى الثاني"
p_month_hijri_7 = "رجب"
p_month_hijri_8 = "شعبان"
p_month_hijri_9 = "رمضان"
p_month_hijri_10 = "شوال"
p_month_hijri_11 = "ذو القعدة"
p_month_hijri_12 = "ذو الحجة"
attendance_start_date_required_msg = ""
attendance_start_date_date_msg = ""
attendance_end_date_date_msg = ""
FunctionalOperation_sh_uao_job_date_of_appointment_date_msg = ""
FunctionalOperation_salary_basic_required_if_msg = ""
FunctionalOperation_days_in_month_required_if_msg = ""
FunctionalOperation_days_salary_required_if_msg = ""
FunctionalOperation_hour_salary_required_if_msg = ""
FunctionalOperation_att_doam_id_required_if_msg = ""
FunctionalOperation_att_device_id_required_if_msg = ""
FunctionalOperation_att_device_id_numeric_msg = ""
FunctionalOperation_att_device_id_min_msg = ""
FunctionalOperation_att_device_num_required_if_msg = ""
FunctionalOperation_att_device_num_numeric_msg = ""
FunctionalOperation_att_device_num_min_msg = ""

[FunctionalOperation]
p_employee_record = "السجل الوظيفي"
p_primary_data = "المعلومات الأساسية"
p_job_unit_name = "القسم"
p_job_name = "إسم الوظيفة"
p_direct_boss = "الرئيس المباشر"
p_employee_record_data = "تقرير البيانات والسجل الوظيفي للموظف"
p_attandace_accountabilites = "مساءلات الغياب"
p_latency_accountabilites = "مساءلات التأخر"
p_commitee_name = "إسم اللجنة"
p_committee_purpose = "الغرض من اللجنة"
p_committee_roles = "المهام في اللجنة"
p_no_committees = "الموظف ليس عضوا في اي لجنة"
p_job_title = "المسمى الوظيفي"
p_job_type = "نوع الوظيفة"
p_contract_start_data = "تاريخ بداية العقد"
p_contract_end_data = "تاريخ نهاية العقد"
p_license = "الإتفاقية"
p_days = "الأيام"
p_job_code = "رمز الوظيفة"
p_job_unit = "الوحدة"
p_job_def = "تعريف"
p_emp_sex = "جنس الموظف"
p_doam_type = "نوع الدوام"
p_job_gnr_def = "تعريف عام"
p_desc = "وصف"
p_res_tasks = "مهام ومسؤوليات الوظيفة"
p_relations = "العلاقات "
p_emp_supervising = "يشرف على"
p_in_org_contact = "داخل المنظمة يتواصل مع"
p_out_org_contact = "خارج المنظمة يتواصل مع"
p_emp_power = "سلطات الوظيفة"
p_job_invironment = "بيئة الوظيفة"
p_job_places = "أمكنة الوظيفة"
p_eidt_job_invorn = "بيئة الوظيفة"
p_job_condition = "شروط الوظيفة"
p_job_coalif = "مؤهلات الوظيفة"
p_work_exep = "الخبرات العملية"
p_maarif = "معارف الوظيفة"
p_rep_job_name_ar = "إسم الوظيفة"
p_rep_job_unit = "وحدة الوظيفة"
p_job_card_report = "تقرير بطاقة الوظيفة"
p_edit_job_code = "تعديل كود الوظيفة"
p_edit_job_def = "تعريف الوظيفة"
p_edit_job_type = "نوع الوظيفة"
p_edit_emp_sex = "جنس الموظف"
p_edit_doam_type = "نوع الدوام"
p_edit_job_gnr_def = "التعريف العام للوظيفة"
p_edit_res_tasks = "مسؤوليات الوظيفة"
p_edit_relations = "علاقات الوظيفة"
p_edit_direct_boss = "الرئيس المباشر"
p_edit_emp_supervising = "يشرف على"
p_edit_in_org_contact = "يتواصل داخل المنشأة مع"
p_edit_out_org_contact = "يتواصل خارج المنشأة مع"
p_edit_emp_power = "سلطات الوظيفة"
p_edit_job_invironment = "بيئة الوظيفة"
p_edit_job_places = "أماكن الوظيفة"
p_edit_job_condition = "شروط الوظيفة"
p_edit_job_coalif = "مؤهلات شاغل الوظيفة"
p_edit_work_exep = "الخبرات العملية لشاغل الوظيفة"
p_edit_maarif = "الخبرات العلمية لشاغل الوظيفة"
p_advance_installment = "قسط السلفة"
p_installment_discounted = "تم خصم القسط"
p_installment_not_discounted = "لم يتم خصم القسط"
p_absence = "مساءلة غياب"
p_no_absence_records = "لا توجد سجلات غياب"
p_advances = "السلف"
p_no_advances_requests = "لا توجد سجلات سلف"
p_deducting_requests = "طلبات الحسم"
p_no_deducting_requests = "لا توجد طلبات حسم"
p_the_absence = "الغياب"
p_number_of_duty_days = "عدد أيام العمل"
p_number_of_absence_days = "عدد أيام الغياب"
p_deduction_value = "قيمة الخصم"
p_set_date_and_lang = "ضبط التاريخ واللغة"
p_display_lang = "لغة العرض"
p_browse_leave_details = "إستعراض تفاصيل الإجازة"
p_leave_name = "إسم الإجازة"
p_leave_description = "وصف الإجازة"
p_leave_entitled_sex = "الإجازة مسموحة لـ"
p_leave_conditions = "شروط الإجازة"
p_annual_leave_credit = "الرصيد السنوي"
p_credit_num_of_days = "عدد أيام الرصيد"
p_transfer_credit = "ترحيل الرصيد"
p_end_of_leave_year = "نهاية السنة"
p_credit_max_limit_when_transfer = "الحد الأعلى للرصيد عند الترحيل"
p_type = "النوع"
p_balance = "الرصيد"
p_year_balance = "الرصيد السنوي"
p_max_balance = "حد الرصيد الأعلى"
p_holidays_record = "سجل الإجازات"
p_holidays = "الإجازات"
p_circulate_leave = "إجازة مجدولة"
p_leave_type = "نوع الإجازة"
p_monthly_credit = "رصيد الشهر"
p_added_credit = "الرصيد المضاف"
p_deducted_credit = "الرصيد المخصوم"
p_relax_day = "أيام الراحة"
p_recruite = "التعيين"
p_you_are_recruit_on_this_vacant = "مرحباً، تم تعيينك في هذه الوظيفة"
p_transaction_type = "نوع الحركة"
p_transaction_calculation_type = "إضافة/خصم"
p_transaction_credit = "الرصيد"
p_transaction_created_by = "منشئ الحركة"
p_transaction_created_date = "تاريخ الحركة"
p_transaction_details = "تفاصيل الحركة"
p_income_data = "معومات الدخل"
p_income_type = "نوع الدخل"
p_income_value = "قيمة الدخل"
p_income_status = "الحالة"
p_loan_data = "بيانات الديون"
p_loan_type = "نوع  الدين"
p_installment_type = "نوع القسط"
p_loan_value = "قيمة الدين"
p_installment_value = "قيمة القسط"
p_rent_data = "بيانات الإستئجار"
p_house_type = "نوع السكن"
p_building_type = "نوع البناء"
p_house_space = "حجم السكن"
p_rent_value = "قيمة الإجار"
p_current_house = "السكن الحالي"
p_term_of_selection = "شرط الإختيار"
p_criteria = "المعايير"
p_history_records = "السجل"
p_beneficiary_history = "سجل المستفيد"
p_materials = "الأعيان"
p_family_history = "سجل الأسرة"
p_indevidual_history = "سجل الفرد"
p_constraint = "الشرط"
p_identity_number = "رقم الهوية"
p_family = "الأسرة"
p_file_number = "رقم الملف"
p_visit_date = "تاريخ الزيارة"
p_committee_comment = "ملاحظات اللجنة"
p_records = "السجلات"
p_constraints = "الاشتراطات"
p_add_new_beneficiary = "اضافة مستفيد جديد"
p_fr_name = "الاسم الاول"
p_sec_name = "الاسم الثاني"
p_thrd_name = "الاسم الثالث"
p_fm_name = "اسم العائلة"
p_classification = "التصنيف"
p_gender = "الجنس"
p_email = "البريد الالكتروني"
p_ayban_number = "رقم الايبان"
p_phone_number = "رقم الجوال"
p_birth_date = "تاريخ الميلاد"
p_sponsorship_number = "رقم الكفالة"
p_live_status = "حالة الحياة"
p_user_live = "حي"
p_user_dead = "متوفي"
p_recommendations = "التوصيات"
p_execution_status = "حالة التنفيذ"
p_id_type = "نوع الهويه"
p_id_no = "رقم الهويه"
p_request_perm = "طلب إذن"
p_request_leave = "طلب إجازة"
p_request_retreat_Leave = "طلب تراجع عن اجازة"
p_request_edit_credit_request = "طلب تغذية رصيد إجازة"
p_request_adv = "طلب سلفة"
p_request_out_work = "طلب عمل إضافي"
p_request_out_work_salary = "طلب أجرة عمل إضافي"
p_request_mandate = "طلب إنتداب"
p_request_mandate_salary = "طلب أجرة إنتداب"
p_finexch_request = "طلب صرف"
p_request = "طلب"
p_not_send_requests = "غير مرسل"
p_sent_requests = "قيد الاجراء"
p_finish_requests = "منتهي"
p_leave_credit = "رصيد الإجازة"
p_permission_type = "نوع الإذن"
p_job_period = "فترة العمل"
p_permission_date = "تاريخ الإذن"
p_emp_name = "إسم الموظف"
p_outwork_period = "الفترة"
p_daily_rate = "المعدل اليومي"
p_hour = "ساعة/ساعات"
p_assign_from = "مسنده من"
p_hours = "عدد الساعات"
p_mandate_days_number = "عدد أيام الإنتداب"
p_mandate_period = "فترة الإنتداب"
p_mandate_reason = "سبب الانتداب"
p_outwork_reason = "السبب"
p_mandate_report = "تقرير الإنتداب"
p_adv_amount = "قيمة السلفة"
p_months = "الشهور"
p_request_status = "حالة الطلب"
p_amount_as_number = "القيمة"
p_choose_leave = "اختر نوع الإجازة"
p_available_credit = "الرصيد المتاح"
p_specify_credit = "حدد الرصيد المطلوب"
p_leave_duration = "مدة الإجازة"
p_required_credit = "الرصيد المطلوب"
p_leave_start_date = "تاريخ بداية الإجازة"
p_choose_alternative = "اختر البديل"
p_back_to_leaves = "العودة لأنواع الإجازات المتاحة"
p_confirm_request = "تأكيد الطلب"
p_leave_end_date = "تاريخ نهاية الإجازة"
p_create_request = "إنشاء الطلب"
p_update_request = "تحديث الطلب"
p_back_to_credit = "العودة لتحديد الرصيد المطلوب"
p_no_more_than_one_leave = "عفواً لا يمكن أخذ أكثر من إجازة في نفس اليوم، لديك إجازة في الأيام التالية:"
p_sorry_your_alternative_in_leave_in_same_duration = "عفواً لدى البديل أدناه أيام إجازة معتمده أثناء الفترة المحدده"
p_no_past_leave = "عفواً لا يمكن أخذ إجازة بتاريخ سابق"
p_chart_first = "عفواً ... يجب تحديد مخطط تنفيذ العملية أولاً"
p_specify_chart = "إضغط هنا للإنتقال لعملية تحديد مخطط التنفيذ : "
p_need_perm = "يظهر زر تحديد المخطط لمن لديه صلاحية الوصول للعملية فقط."
p_add_perm = "إضافة طلب إذن"
p_permission_reason = "سبب الإذن"
p_edit_perm = "تعديل بيانات طلب إذن"
p_add_outwork_request = "طلب عمل إضافي"
p_choose_emp = "اختر الموظف"
p_with_rate = "بمعدل"
p_rate = "المعدل"
p_reason_for_overtime = "سبب العمل الإضافي"
p_edit_out_work = "تعديل بيانات عمل إضافي"
p_edit_out_work_salary = "تعديل بيانات طلب أجرة عمل إضافي"
p_mission_execution_status = "حالة تنفيذ المهمة"
p_job_hours = "ساعات العمل"
p_add_mandate_request = "طلب إنتداب"
p_mandate_days = "عدد أيام الإنتداب"
p_mandate_city = "مدينة الإنتداب"
p_edit_mandate = "تعديل بيانات طلب انتداب"
p_edit_mandate_salary = "تعديل بيانات طلب أجرة انتداب"
p_mission_status = "حالة المهمة"
p_mission_report = "تقرير المهمة"
p_mission_transportation = "هل تم توفير وسيلة مواصلات؟"
p_mission_accommodation = "هل تم توفير السكن؟"
p_mission_expenses = "هل تم توفير تكاليف المعيشة؟"
p_add_advance_request = "إضافة طلب سلفة"
p_amount = "المبلغ الكلي"
p_adv_months = "عدد شهور الاستقطاع"
p_adv_date = "تاريخ بدء الاستقطاع"
p_add_fin_exch = "إضافة طلب صرف"
p_receipt_beneficiary = "المستفيد"
p_amount_written = "المبلغ كتابة"
p_receipt_purpose = "الغرض"
p_edit_fin_exch = "تعديل طلب صرف"
p_delete_exchange_request = "حذف طلب صرف"
p_confirm_delete_exchange_request = "هل تريد بالتأكيد حذف طلب الصرف هذا؟"
p_num_of_required_days = "عدد الأيام المطلوبة"
p_duration_by_days = "المدة بالأيام"
p_no_leave_retrive_requests = "لا توجد طلبات تراجع عن إجازة"
p_no_perm_requests = "لا توجد طلبات اذونات"
p_no_out_work_requests = "لا توجد طلبات عمل إضافي"
p_no_leave_requests = "لا توجد طلبات إجازة"
p_no_outwork_fees_requests = "لا توجد طلبات أجرة عمل إضافي"
p_no_mndtfees_requests = "لا توجد طلبات إجرة إنتداب"
p_no_mandate_requests = "لا توجد طلبات إنتداب"
p_no_advance_requests = "لا توجد طلبات سلف"
p_no_leave_credit_requests = "لا توجد طلبات رصيد إجازات"
p_no_finexch_requests = "لا توجد طلبات صرف"
p_no_attandace_accountabilites = "لا توجد مساءلات غياب"
p_no_latency_accountabilites = "لا توجد مساءلات تأخر"
p_vacation_name = "مسمي الإجازة"
p_avalible_credit = "الرصيد المتاح"
p_yearly_credit = "الرصيد السنوي"
p_yearly_credit_total = "مجموع الرصيد السنوي"
p_available_credit_total = "مجموع الرصيد المتاح"
p_no_vacations = "لا توجد إجازات"
p_fin_sheet = "كشف حساب"
p_fin_year = "السنة المالية"
p_dail_account = "الحساب"
p_fin_from_date = "من"
p_fin_to_date = "إلى"
p_entry_number = "رقم القيد"
p_fin_bian = "البيان"
p_fin_date = "التاريخ"
p_fin_depit = "مدين"
p_fin_credit = "دائن"
p_fin_balance = "الرصيد"
p_fin_comulated_balance = "الرصيد المتراكم"
p_opening_balance = "رصيد افتتاحي للفترة"
p_summary = "الملخص"
p_summary_opening_balance = "رصيد افتتاحي أول الفترة"
p_summary_closing_balance = "رصيد آخر الفترة"
p_summary_debit_transactions_total = "مجموع الحركات المدينة"
p_summary_credit_transactions_total = "مجموع الحركات الدائنة"
p_fin_box_sig = "توقيع أمين الصندوق"
p_fin_acc_sig = "توقيع المحاسب"
p_fin_manag_sig = "توقيع المدير المالي"
p_add_new_transaction = "إنشاء وثيقة جديدة"
p_main_topic = "الموضوع"
p_comment = "الرسالة"
p_language = "لغة الوثيقة"
p_transaction_tags = "تصنيف الوثيقة"
p_privacy_status = "حالة الخصوصية"
p_referral_assignment_type = "النوع"
p_referral_execution_start_date = "تاريخ البداية"
p_referral_execution_duration = "المدة"
p_referral_priority_status = "الأولوية"
p_edit_transaction_data = "تعديل بيانات الوثيقة"
p_document_data = "بيانات الوثيقة"
p_transaction_folder = "المجلد"
p_transaction_mediator = "إستلمنا الوثيقة من"
p_mediator_name = "الإسم"
p_mediator_phone = "رقم الجوال"
p_mediator_email = "البريد الإلكتروني"
p_mediator_date = "تاريخ التوريد"
p_mediator_comment = "ملاحظات التسليم"
p_transaction_carrier = "أرسلنا الوثيقة عبر"
p_carrier_name = "الإسم"
p_carrier_phone = "رقم الجوال"
p_carrier_email = "البريد الإلكتروني"
p_carrier_date = "تاريخ التصدير"
p_carrier_comment = "ملاحظات التصدير"
p_tags = "التصنيف"
p_referral_folder = "المجلد"
p_export_or_import_information = "معلومات التصدير / التوريد"
p_export_number = "رقم الصادر"
p_export_date = "تاريخ الصادر"
p_export_attachments_count = "عدد المشفوعات"
p_import = "توريد"
p_import_number = "رقم الوارد"
p_import_date = "تاريخ الوارد"
p_import_attachments_count = "عدد المشفوعات"
p_transaction_information = "معلومات الوثيقة"
p_referral_information = "بيانات الإحالة"
p_action_needed = "الإجراء المطلوب"
p_my_referral_setting = "الضبط"
p_transaction_referrals = "الإحالات"
p_edit_privilege = "التعديل"
p_refer_privilege = "الإحالة"
p_attach_privilege = "إرفاق الملفات"
p_client_outbox = "صندوق صادر المنشأة"
p_transaction_append_and_comment = "الإلحاق والتعقيب"
p_comment_creator = "منشئ التعقيب"
p_message = "الرسالة"
p_comment_date = "تاريخ التعقيب"
p_sub_topic = "الموضوع الفرعي"
p_referrals = "الإحالات"
p_referral_to = "إحالة إلى"
p_referral_title = "العنوان"
p_title = "العنوان"
p_attach_title = "إرفاق ملف"
p_max_size = "الحجم الأقصى"
p_max_number = "العدد الأقصى"
p_documents = "مستندات"
p_extensions = "الصيغ"
p_add_referral = "إضافة إحالة"
p_subject = "الموضوع"
p_referral_type = "نوع الإحالة"
p_referral_privileges = "الصلاحيات"
p_referral_to_client_outbox = "تصدير عبر صندوق صادر المنشأة"
p_referral_destination = "وجهة الإحالة"
p_publication_according_to = "تعميم حسب"
p_unit_managers = "مدراء الوحدات"
p_user_classes = "التصانيف"
p_edit_referral = "تعديل إحاله"
p_view_referral = "استعراض الإحالة"
p_referral_execution_type = "التنفيذ"
p_referral_transaction_execution_status = "حالة التنفيذ"
p_delete_referral = "حذف إحالة"
p_referral_deletion_confirm_message = "هل تريد التأكيد حذف هذه الإحالة؟"
p_add_append_comment = "إضافة إلحاق/تعقيب"
p_edit_append_comment = "تعديل إلحاق/تعقيب"
p_delete_append_comment = "حذف إلحاق/تعقيب"
p_edit_referral_status = "تعديل حالة تنفيذ الإحالة"
p_transaction_execution_types = "خيارات التنفيذ"
p_comment_from = "التعقيب من"
p_attachments_count = "عدد المشفوعات"
p_serial_number = "الرقم التسلسلي"
p_manual = "يدوي"
p_automatic = "تلقائي"
p_year = "السنة"
p_year_and_month = "السنة والشهر"
p_year_and_month_and_day = "السنة والشهر واليوم"
p_delivered_by = "تم التوصيل عن طريق"
p_date_must_be_change_from_it = "التاريخ يتم تغييره بواسطة مدير التقنية"
p_direct_master_job = "الرئيس المباشر"
p_view_jobcard = "إستعراض البطاقة"
p_choose_unit = "اختر الوحدة/القسم"
p_choose_employee = "إختر الموظف"
p_employee_report = "تقرير الموظفون"
p_show_hr = "صلاحية "
p_convert_job_to_primary = "تحويل الوظيفة إلى أساسية"
p_dismiss_from_job = "الإقالة من الوظيفة"
p_move_to_another_job = "نقل لوظيفة أخرى"
p_move = "نقل الموظف: "
p_to_another_job = "لوظيفة أخرى"
p_date_of_appointment = "تاريخ التعيين"
p_end_date_of_appointment = "تاريخ انتهاء العقد"
p_direct_chief = "الرئيس المباشر"
p_job_salary_type = "أجر الوظيفة"
p_base_salary = "المرتب الأساسي"
p_salary = "المرتب"
p_payroll_template = ""
p_subject_to_presense_and_leave = "خاضع للحضور والإنصراف"
p_not_subjected = "غير خاضع"
p_subject_to_presense_and_leave_based_on_job_time = "خاضع للحضور والإنصراف بالدوام"
p_subject_to_presense_and_leave_based_on_hour = "خاضع للحضور والإنصراف بالساعه"
p_fingerprint_device = "جهاز البصمه"
p_no_on_fingerprint_device = "الرقم بجهاز البصمه"
p_user_no_in_presense_and_leave_device = "الرقم الخاص بالمستخدم في جهاز الحضور والإنصراف"
p_hired_employee_name = "إسم الموظف المُعَيََّن"
p_employement_record_for = "السجل الوظيفي لـ"
p_employement_reocrd_report = "تقرير السجل الوظيفي"
p_dismissal_date = "تاريخ الإقالة"
p_dismissal_reasons = "أسباب الإقاله"
p_dismissal_reason = "سبب الإقالة"
p_dismiss_employee_alert = "تنبيه!، عملية الإقالة نهائية ولا يمكن التراجع عنها إلا بإعادة تعيين الموظف مره أخرى في نفس الوظيفة"
changing_will_delete_all_specfic_rules_alert = "تنبيه! عند تغير قالب مرتبات للموظف سيتم حذف كل القوالب الخاصة بالموظف "
p_dismissal_implementation = "تنفيذ الإقالة"
p_converting_job_to_brimary_make_all_others_secondary = " تحويل الوظيفة إلى أساسية يقتضى تحول الوظائف الأخرى للموظف إلى وظائف ثانوية"
p_adjust_leave_templates = "ضبط قوالب الإجازات"
p_employees_available_leaves = "الإجازات المتاحه للموظف"
p_credit_type = "نوع الرصيد"
p_employee_available_credit = "الرصيد المتاح للموظف"
p_untransferable_leave = "لا يمكن ترحيلها"
p_unlimited = "بدون حد أعلى"
p_the_record = "السجل"
p_add_deduct_leave_credit = "إضافة / خصم رصيد الإجازات"
p_adjust_employee_leave = "ضبط الإجازة للموظف"
p_max_credit_limit = "الحد الأعلى للرصيد"
p_allowedleave_max_credit = "الحد الأعلى"
p_start_counting_leave_date = "بداية إحتساب الإجازة"
p_types_of_available_leaves = "أنواع الإجازات المتاحة"
p_need_to_determine_gender_first = "عفواً يجب تحديد جنس الموظف أولاً"
p_affecting_day_price_estimation = "يؤثر في إحتساب قيمة اليوم"
p_add_new_term = "إضافة بند جديد"
p_term_name = "إسم البند"
p_deleting_payslip_consequences = "حذف مسير رواتب يؤدي إلى حذف جميع البيانات المرتبطه به، ويقوم بتحرير سجلات الغياب والسلف والحسم المرتبطه به."
p_latency = "مساءلة التأخر"
p_unit_or_depart = "الوحدة/القسم"
p_payslips_preparation = "إعداد المسيرات"
p_payslips_preparation_new = "إعداد المسيرات 2"
p_payslip_creation = "إنشاء المسيرات"
p_adjust_employees = "ضبط الموظفين"
p_salaries_template = "قوالب المرتبات"
p_payslip = "المسير"
p_there_are_not_reveiwed_records = "عفواً توجد سجلات غير مدققة"
p_monthly_salary_payslips = "مسيرات الأجر الشهري"
p_daily_salary_payslips = "مسيرات الأجر اليومي"
p_monthly_salary_employees = "موظفو الأجر الشهري"
p_daily_salary_employees = "موظفو الأجر اليومي"
p_salary_template = "قالب المرتبات"
p_template_create = "بناء القالب"
p_template_type = "نوع القالب"
p_estimate_day_price = "إحتساب قيمة اليوم"
p_reveiwing_status = "حالة التدقيق"
p_salary_net_after_additions_must_not_be_less_than_zero = " صافي المرتب بعد الإضافات يجب أن لا يقل عن الصفر."
p_salary_record = "كشف مرتب"
p_additions_total = "مجموع الإضافات"
p_sorry_no_approved_salary_records = "عفواً لا توجد سجلات مرتب معتمدة"
p_excluded_employees = "الموظفون المستبعدون"
p_salaries_recoed_information = "معلومات كشف المرتبات"
p_payslip_name = "إسم المسير"
p_delete_template = "حذف قالب"
p_edit_term = "تعديل بند"
p_add_new_salary_template = "إضافة قالب مرتبات جديد"
p_template_name = "إسم القالب"
p_edit_salary_template = "تعديل قالب مرتبات"
p_salary_type = "نوع الأجر"
p_choose_salary_template = "إختيار قالب مرتب"
p_should_bulid_monthly_salary_templates_first = "يجب إعادة بناء المسير من جديد"
p_should_bulid_daily_salary_templates_first = "يجب بناء قوالب مرتبات الأجر اليومي أولاً"
p_adjust_reveiwing_status = "ضبط حالة التدقيق"
p_bonuses_overall = "إجمالي العلاوات"
p_net_salary = "صافي المرتب"
p_payment_record = "سجل الدفع"
p_reveiwed = "مدقق"
p_not_reveiwed = "غير مدقق"
p_addition_requests = "طلبات الإضافة"
p_no_addition_requests = " لا توجد طلبات إضافة"
p_add_payslip = "إضافة مسير"
p_from_salary = "من المرتب"
p_auditor = "المُراجع"
p_approved_by = "المعتمد"
p_reveiw_and_confirm_payslip_fully_depend_on_it_manager_workflow_should_bulid_workflow_with_auditor_and_approver_determined_from_record = "مراجعة وإعتماد المسير تعتمد بشكل أساسي على مخطط الإجراء ببرنامج إدارة التقنية، يجب بناء المخطط بحيث يحدد فيه المراجع والمعتمد من السجل."
p_edit_payslip = "تعديل مسير"
approved_by = "أعتمد بواسطة"
p_build_payslip = "بناء مسير رواتب"
p_building_payslip_describtion = "بناء المسير هي العملية التي يقوم فيها النظام بإحتساب قوالب المرتبات لجميع الموظفين التابعين لقوالب المرتبات الداخلة في تكوين المسير"
p_tamplate_or_extra_or_deductions_alteration_not_affecting_payslip_after_build = "أي تعديل على القوالب أو البدلات والحسومات التابعة لها لا تؤثر على المسير بعد بنائه."
p_rebulid_payslip_to_apply_tamplate_or_extra_or_deductions_alteration = "يجب عليك إعادة بناء المسير في كل مره أحدثت فيها تعديل على القوالب والبدلات والحسومات وأردت تضمين تلك التعديلات في المسير"
p_delete_payslip = "حذف مسير"
p_there_are_not_approved_payslips = "عفواً توجد مسيرات موظفين غير معتمدة"
p_approve_payslip_and_send_it_to_financial_accounting = "إعتماد المسير وإرساله للموارد المالية"
p_net_after_deductions = "الصافي بعد الخصومات الأخرى"
p_review_all_records = "تدقيق كل السجلات"
p_important_information_about_payslip = "                    من المهم المرور على سجلات المرتبات كل على حده ومراجعتها والتأكد من صحة المبالغ الوارده في كل مسير، لا يمكن تعديل السجلات بعد إعتماد المسير وإرساله لبرنامج المحاسبة المالية، وفي حال التأكد من المبالغ بناءاً على نتيجة مسيرات رواتب سابقة بإمكانك تحويل جميع المسيرات إلى حالة تم التدقيق للإختصار."
p_convert_all_records_to_reviewed = "تحويل جميع السجلات إلى مدققة"
p_convert_all_records_to_not_reviewed = "تحويل جميع السجلات إلى غير مدققة"
p_payroll_batch_view = "إنشاء مسيرات الرواتب"
p_payroll_batch_save = "مسيرات الرواتب"
p_user_data = "بيانات مستخدم"
p_data = "البيانات"
p_image = "الصورة"
p_address = "عنوان السكن"
p_on_the_job = "على رأس العمل"
p_course_name = "إسم الدوره"
p_train_foundation = "إسم المركز"
p_housing_type = "نوع السكن"
p_ownership_type = "نوع الملك"
p_accommodation_size = "حجم السكن"
p_build_type = "نوع البناء"
p_work_type = "نوع العمل"
p_vehical_type = "نوع  المركبة"
p_vehical_model = "موديل المركبة"
p_worker_type = "طبيعة العمل"
p_worker_nationality = " الجنسية"
p_worker_stillworking = "ما زال على رأس العمل"
p_debt_amount = "قيمة القسط الشهري"
p_debt_creditor = "إسم الدائن"
p_livestock_amount = "عدد الماشية"
p_livestock_type = "نوع الماشية"
p_name_in_english = "الإسم باللغه الإنجليزية"
p_work_status = "حالة العمل"
p_education_level = "المستوى التعليمي"
p_employment_id_number = "رقم المعرف"
p_iBan_number = "رقم الآيبان"
p_health_status_desc = "وصف الحالة الصحية"
p_talk_about_your_self = "رؤيتي / رسالتي"
p_floor_number = "رقم الدور"
p_national_id_number = "رقم العنوان الوطني"
p_vehicle_type = "نوع المركبة"
p_vehicle_model = "الموديل"
p_profile_picture = "الصورة الشخصية"
p_choose = "اختر الملف"
p_profile_picture_deleteion_confirm = "هل أنت متأكد بأنك تريد حذف هذه الصورة؟"
p_delete_confirmation_message = "هل تريد بالتأكيد حذف هذا السجل؟"
p_name_of_work_or_employment = "مسمى العمل أو الوظيفة"
birthday = "تاريخ الميلاد"
p_course_to = "جهة التدريب"
p_course_location = "جهة إعتماد الشهادة"
p_select_from_menu = "اختر من القائمه"
p_from = "من"
p_to = " إلى"
p_debt_creditor_person = "إسم صاحب الدين"
p_debt_value = "إجمالي الدين"
p_debt_begin_date = "تاريخ بداية القسط"
p_debt_end_date = "تاريخ نهاية القسط"
p_debt_creditor_bank = "إسم البنك"
p_debt_car_type = "نوع السيارة"
p_debt_house_type = "نوع المنزل"
p_debt_other_type = "أخرى"
p_add_new_house = ""
p_contract_from = "تاريخ بداية العقد"
p_contract_to = "تاريخ نهاية العقد"
p_educational_data = "معلومات التعليم"
p_experiances_data = "معومات الخبرة"
p_no_records = "لا توجد سجلات"
p_family_data = "معلومات العائلة"
p_train_data = "معلومات التدريب"
p_housing_data = "معومات السكن"
p_vehicles_data = "معلومات المركبات"
p_worker_data = "معلومات العمل"
p_debt_data = "معلومات الديون"
p_live_stock_data = "معلومات المواشي"
