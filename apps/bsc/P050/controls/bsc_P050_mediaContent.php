<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class bsc_P050_mediaContent extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case 'menu':

                $_SESSION['s_active_tabe'] = 602;

                break;

            case 'tab':

                $_SESSION['s_active_tabe'] = $parm[1];

                break;

            case 'insert':

                if ($parm[1] === $_SESSION['s_rand_num']) {

                    $mediacenter = new MediaCenter();
                    $mediacenter->bindProperties($post);
                    $mediacenter->org_id = $_SESSION['organization']->id;
                    $mediacenter->counter = 0;
                    $mediacenter->type = (int)$parm[2] ?? 0;
                    $mediacenter->created_by = $_SESSION['user']->id;
                    $mediacenter->created_date = date('y-m-d', time());
                    $mediacenter->save();

                    Notification::createdAlert();
                    $_SESSION['s_active_tabe'] = $parm[2];

                }

                break;

            case 'update':

                if ($parm[1] === $_SESSION['s_rand_num']) {

                    try {

                        $mediacenter = MediaCenter::readID((int)$parm[2]);
                        $mediacenter->bindProperties($post);
                        $mediacenter->last_update_date = date('Y-m-d', time());
                        $mediacenter->save();

                        Notification::updatedAlert();
                        $_SESSION['s_active_tabe'] = $parm[3];

                    } catch (MediaCenterException $e) {
                    }

                }

                break;

            case 'delete':

                if ($parm[1] === $_SESSION['s_rand_num']) {

                    try {

                        MediaCenter::readID((int)$parm[2])->delete();

                        Notification::deletedAlert();
                        $_SESSION['s_active_tabe'] = $parm[3];

                    } catch (MediaCenterException $e) {
                    }
                }

                break;

            case 'publish':

                if ($parm[1] === $_SESSION['s_rand_num']) {

                    try {

                        $mediacenter = MediaCenter::readID((int)$parm[2]);
                        $mediacenter->bindProperties($post);
                        $mediacenter->is_published = isset($post['is_published']) ? $post['is_published'] : 0;
                        $mediacenter->save();

                        Notification::updatedAlert();
                        $_SESSION['s_active_tabe'] = $parm[3];

                    } catch (MediaCenterException $e) {
                    }

                }

                break;

            case 'show_section':

                try {
                    $settings = Setting::readID((int)$parm[1]);
                    $settings->bindProperties($post);
                    $settings->save();
                } catch (SettingException $e) {
                }
                break;
        }

        $this->Smarty->assign("sh_mediacenter_types", [602,603]);

        foreach ([602,603] as $id) {

            try {
                $this->Smarty->assign('type_' . $id, MediaCenter::read([
                    MediaCenter::ORG_ID => $_SESSION['organization']->id,
                    MediaCenter::TYPE => $id
                ], [
                    0 => [
                        'property' => MediaCenter::ID,
                        'sort' => 'DESC'
                    ]
                ]));
            } catch (MediaCenterException $e) {
            }
        }

        // pass sh_mediacenter list to view
        try {

            $this->Smarty->assign('sh_mediacenter_list', MediaCenter::read([
                MediaCenter::ORG_ID => $_SESSION['organization']->id
            ], [
                0 => [
                    'property' => MediaCenter::TYPE,
                    'sort' => 'ASC'
                ]
            ]));

        } catch (MediaCenterException $e) {

        }

        $_SESSION['s_rand_num'] = md5(rand(0, 10000000));
    }

    public function add($parm, $post)
    {
        $this->Smarty->assign('st_setting_id', $parm[0]);
        $_SESSION['s_active_tabe'] = $parm[0];
        $_SESSION['s_rand_num'] = md5(rand(0, 10000000));
    }

    public function edit($parm, $post)
    {
        try {

            $sh_mediacenter_row = MediaCenter::readID((int)$parm[0]);
            $this->Smarty->assign('sh_mediacenter_row', $sh_mediacenter_row);

        } catch (MediaCenterException $e) {
        }

        $_SESSION['s_active_tabe'] = $parm[1];
        $_SESSION['s_rand_num'] = md5(rand(0, 10000000));

    }

    public function publish($parm, $post)
    {

        try {

            $this->Smarty->assign('sh_mediacenter_row', MediaCenter::readID((int)$parm[0]));

        } catch (MediaCenterException $e) {

            $this->Smarty->assign('sh_mediacenter_row',[]);

        }

        $_SESSION['s_active_tabe'] = $parm[1];
        $_SESSION['s_rand_num'] = md5(rand(0, 10000000));
    }

    public function confirm($parm, $post)
    {
        try {

            $this->Smarty->assign('record', MediaCenter::readID((int)$parm[0]));

        } catch (MediaCenterException $e) {

            $this->Smarty->assign('record', []);

        }

        $_SESSION['s_active_tabe'] = $parm[1];
    }

    public function browse($parm, $post)
    {

        if ($parm[1] == 'back') {
            $_SESSION['s_media_back_page'] = $parm[2];
        }

        try {
            $this->Smarty->assign('media', MediaCenter::readID((int)$parm[0] ?? 0));
        } catch (MediaCenterException $e) {
            $this->Smarty->assign('media', []);
        }

    }

    public function prnt($parm, $post)
    {

        if (!isset($_SESSION['s_sh_mediacenter_id'])) {
            $_SESSION['s_sh_mediacenter_id'] = 0;
        }

        $this->Smarty->assign('record_id', $_SESSION['s_sh_mediacenter_id']);
        try{
            $this->Smarty->assign('row', MediaCenter::readID((int)$_SESSION['s_sh_mediacenter_id']));
        }catch (MediaCenterException $e){

        }

//        // check news_article number of stored pictures
//        $pic_num = ODocs::count([
//            ODocs::ORG_ID => $_SESSION['organization']->id,
//            ODocs::OPR_CODE => MediaCenter::OPERATION_CODE,
//            ODocs::ROW_ID => $_SESSION['s_sh_mediacenter_id']
//        ]);
//
//        // check if news_article have picture saved
//        if ($pic_num === 1) {
//            $this->Smarty->assign('picrow', ODocs::read([
//                ODocs::ORG_ID => $_SESSION['organization']->id,
//                ODocs::OPR_CODE => MediaCenter::OPERATION_CODE,
//                ODocs::ROW_ID => $_SESSION['s_sh_mediacenter_id']
//            ])[0]);
//            $this->Smarty->assign('pic_num',
//                $pic_num);                                                      // pass picture number to view
//        }

    }
}
