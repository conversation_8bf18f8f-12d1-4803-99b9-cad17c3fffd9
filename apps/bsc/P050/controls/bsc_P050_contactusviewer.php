<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class bsc_P050_contactusviewer extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case Operation::DELETE:

                if ($_SESSION['s_contactusviewer_token'] == $parm[1]) {

                    try {

                        $contactdata = ContactData::readID((int)$parm[2] ?? 0);
                        $contactdata->delete();

                        Notification::deletedAlert();

                    } catch (ContactDataException $e) {}

                }

                break;

        }

        try {

            $this->Smarty->assign('contact_us_list', ContactData::read([
                ContactData::ORG_ID => $_SESSION['organization']->id
            ], [0 => ['property' => ContactData::ID, 'sort' => 'DESC']]));

        } catch (ContactDataException $e) {
            $this->Smarty->assign('contact_us_list', []);
        }

        $_SESSION['s_contactusviewer_token'] = md5(rand(0000, 9999));

    }

    public function browse($parm, $post)
    {

        try {
            $this->Smarty->assign('row', ContactData::readID((int)$parm[0] ?? 0));
        } catch (ContactDataException $e) {

        }

    }

    public function confirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', ContactData::readID((int)$parm[0] ?? 0));
        } catch (ContactDataException $e) {

        }

        $_SESSION['s_contactusviewer_token'] = md5(rand(0000, 9999));
    }
}
