<?php
use SNSO\Core\Reporter\ChartBuilder;

/**
 * Created by PhpStorm.
 * User: altaif
 * Date: 1/15/19
 * Time: 12:44 PM
 */
class bsc_P050_MediaActivitySummary extends Controller
{
    public function show($parm, $post)
    {
       $contentLabels = [
            602 => [
                Translation::translate(null, 'gnr_published_news'),
                Translation::translate(null, 'gnr_not_published_news')
            ],
            603 => [
                Translation::translate(null, 'gnr_published_advertisements'),
                Translation::translate(null, 'gnr_not_published_advertisement'),
            ]
        ];

        foreach ([602, 603] as $key => $contentCode) {

            try {
                $contentTypes[$key]['code'] = $contentCode;
                $contentTypes[$key]['labels'] = $contentLabels[$contentCode];
                $contentTypes[$key]['published'] = MediaCenter::count([MediaCenter::TYPE => $contentCode, MediaCenter::IS_PUBLISHED => 1]);
                $contentTypes[$key]['not_published'] = DB::table(sh_mediacenter::class)->where(MediaCenter::TYPE, $contentCode)
                    ->where(MediaCenter::IS_PUBLISHED, NULL)->count();
                $contentTypes[$key]['graph'] = self::getGraph($contentTypes[$key])->build();
            } catch (MediaCenterException $e) {
                $contentTypes = [];
            }
            $this->Smarty->assign('contentTypes', $contentTypes);
        }
    }

    public function print($parm, $post)
    {
        $this->show($parm, $post);

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));
    }

    public static function getGraph($content)
    {
        $chart = new ChartBuilder();
        $chart->name('type' . $content['code'])
            ->type('pie')
            ->size(['width' => 400, 'height' => 200])
            ->labels($content['labels'])
            ->datasets(
                [
                    [
                        'backgroundColor' => ['green', 'red', 'orange'],
                        'hoverBackgroundColor' => ['green', 'red', 'orange'],
                        'data' => [
                            $content['published'],
                            $content['not_published'],
                        ],
                    ]
                ]
            );

        return $chart;
    }

}