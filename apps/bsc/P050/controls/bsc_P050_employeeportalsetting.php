<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class bsc_P050_employeeportalsetting extends Controller
{
    public function show($parm, $post)
    {

        switch ($parm[0]) {

            case 'updateDataSections':

                if ($_SESSION['s_employeeportalsetting_token'] === $parm[1]) {

                    try {

                        $clientDataSections = ClientConfiguration::read([
                            ClientConfiguration::ORG_ID => $_SESSION['organization']->id,
                            ClientConfiguration::OPR_ID => ClientConfiguration::CLIENT_CONFIGURATION_EMPLOYEE_DASHBOARD_CLIENT_SECTION
                        ])[0];

                        $clientDataSections->ids = !empty($post['dataSections']) ? implode(',', $post['dataSections']) : [];
                        $clientDataSections->save();


                        Notification::updated<PERSON>lert();

                    } catch (ClientConfigurationException $e) {

                    }


                    try {
                        $maxAdsOnDashboards = ClientList::read([
                            ClientList::CLIENT_ID => $_SESSION['organization']->id,
                            ClientList::TYPE => ClientList::MAX_ADS_ON_DASBOARD
                        ])[0];

                        $maxAdsOnDashboards->name = $post['maxVisibleAds'];

                        $maxAdsOnDashboards->save();
                    } catch (ClientListException $e) {
                        $maxAdsOnDashboards = new ClientList();

                        $maxAdsOnDashboards->type = ClientList::MAX_ADS_ON_DASBOARD;
                        $maxAdsOnDashboards->client_id = $_SESSION['organization']->id;
                        $maxAdsOnDashboards->name = $post['maxVisibleAds'];

                        $maxAdsOnDashboards->save();
                    }
                }

                break;

        }

        try {
            $this->Smarty->assign("currentClientConfiguration", ClientConfiguration::getClientConfigurationArray((int)$_SESSION['organization']->id));
        } catch (ClientConfigurationException $e) {
            $this->Smarty->assign("currentClientConfiguration", []);
        }


        try {
            $maxAdsOnDashboards = ClientList::read([
                ClientList::CLIENT_ID => $_SESSION['organization']->id,
                ClientList::TYPE => ClientList::MAX_ADS_ON_DASBOARD
            ])[0]->name;

        } catch (ClientListException $e) {
        }

        $this->Smarty->assign("clientDataSections", Setting::getList(ClientConfiguration::CLIENT_CONFIGURATION_EMPLOYEE_DASHBOARD_CLIENT_SECTION));
        $this->Smarty->assign("maxAdsOnDashboards", $maxAdsOnDashboards);
        $_SESSION['s_employeeportalsetting_token'] = md5(rand(0000, 9999));

    }
}
