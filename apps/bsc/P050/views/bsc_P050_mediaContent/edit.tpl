{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_edit_row#}  ({getname table=st_setting id=$smarty.session.s_active_tabe})</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-lg-12">
                <form  method="post" action='{url urltype="path" url_string="bsc/P050/mediaContent/show/0/{$smarty.session.lang}/update/{$smarty.session.s_rand_num}/{$sh_mediacenter_row->id}/{$sh_mediacenter_row->type}"}'>


                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_address#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="name" placeholder="{#p_post_title#}" value="{$sh_mediacenter_row->name}" required></div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_text#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><textarea rows="15" class="form-control" name="text" placeholder="">{$sh_mediacenter_row->text}</textarea></div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_status#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <div class="radio">
                                <label>
                                    <input name="is_published" type="radio" value=1 {if $sh_mediacenter_row->is_published eq 1} checked="checked" {/if}>
                                    <span class="text">{#p_published#}</span>
                                </label>
                            </div>
                            <div class="radio">
                                <label>
                                    <input name="is_published" type="radio" value=0 {if $sh_mediacenter_row->is_published eq 0} checked="checked" {/if}>
                                    <span class="text">{#p_not_published#}</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success">{#gnr_update#}</button></div>
                </form>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}