

{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}

    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_delete_row#} ({getname table=st_setting id=$smarty.session.s_active_tabe})</h4>
    </div>
    <div class="modal-body">
        <div class="row">

            <div class="snsowraper  text-center">
                {#gnr_delete_row_confirm#}<br><br>
                {$record->name}<br><br>
                {url check=1 urltype="delete" opr_code='mediaContent' url_string="bsc/P050/mediaContent/show/0/{$smarty.session.lang}/delete/{$smarty.session.s_rand_num}/{$record->id}/{$record->type}"}

            </div>

        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>

{/block}