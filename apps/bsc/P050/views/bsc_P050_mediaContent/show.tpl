{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}

{block name=title}المركز الإعلامي{/block}
{block name=page_body}
    <style>
        input.checkbox-slider[type="checkbox"] ~ .text::before {
            content: "نعم            لا"
        }
    </style>
    <div class="widget flat radius-bordered">

        <div class="tabbable tabs-left">
            <ul class="nav nav-tabs" id="myTab3">
                {$i = 1}
                {foreach $sh_mediacenter_types as $row}
                    <li class="tab-sky {if $smarty.session.s_active_tabe eq $row} active {/if} ">
                        <a aria-expanded="false" data-toggle="tab" href="#{$row}"><span
                                    class="badge badge-sky badge-square">{$i}</span> {getname table=st_setting id=$row}
                        </a>
                    </li>
                    {$i = $i +1}
                {/foreach}

            </ul>

            <div class="tab-content">

                {foreach $sh_mediacenter_types as $row}
                    <div id="{$row}" class="tab-pane {if $smarty.session.s_active_tabe eq $row} active {/if}">

                        <div class="table-responsive snsoinput" data-pattern="priority-columns">

                            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                <thead>
                                <tr>
                                    <th width="5%"
                                        style="background-color: #A0D468 !important;">{url check=0 urltype="madd" url_string="bsc/P050/mediaContent/add/0/{$smarty.session.lang}/{$row}"}</th>
                                    <th width="35%" style="background-color: #A0D468 !important;">{#p_title#}</th>
                                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_status#}</th>
                                    <th width="15%" style="background-color: #A0D468 !important;"
                                        data-priority="1">{#p_add_date#}</th>
                                    <th width="10%" style="background-color: #A0D468 !important;"
                                        data-priority="2">{#p_post_view#}</th>
                                    <th width="10%" style="background-color: #A0D468 !important;"
                                        data-priority="3">{#p_post_images#}</th>
                                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_settings#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i=1}
                                {foreach $type_{$row} as $article_row}
                                    <tr>
                                        <td align="center">{$i++}</td>
                                        <td>{$article_row->name}</td>
                                        <td align="center">{if $article_row->is_published eq 1}{#p_published#}{else}{#p_not_published#}{/if}</td>
                                        <td align="center">{getdate type=show row=$article_row col=created_date}</td>
                                        <td align="center">{url check=0 urltype="mbutton" url_string="bsc/P050/mediaContent/browse/0/{$smarty.session.lang}/{$article_row->id}/back/mediacenter" text_value="&nbsp;&nbsp;<i class='fa fa-newspaper-o'></i>&nbsp;&nbsp;"}</td>
                                        <td align="center">{url check=0 urltype="attach" url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/mediaContent/sh_mediacenter/{$article_row->id}/{$smarty.session.user->id}/bsc/P050/mediaContent/show/0/{$smarty.session.lang}/{$row}"}</td>
                                        <td align="center" nowrap>
                                            {url check=0 urltype="medit" url_string="bsc/P050/mediaContent/edit/0/{$smarty.session.lang}/{$article_row->id}/{$row}"}
                                            {url check=0 urltype="mdelete" url_string="bsc/P050/mediaContent/confirm/0/{$smarty.session.lang}/{$article_row->id}/{$row}"}
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>
                        </div>

                    </div>
                {/foreach}
            </div>

        </div>
    </div>
{/block}

{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
        function exportTo(ID, type) {
            $('#table' + ID).css('display', '').tableExport({type: type, escape: 'false'});
            $('#table' + ID).css('display', 'none');
        }
        {/literal}
    </script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "السابق",
                                "sNext": "التالي"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}
