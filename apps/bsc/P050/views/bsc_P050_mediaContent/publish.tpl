{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <style>

        input.checkbox-slider[type="checkbox"] ~ .text::before {
            content:"{#gnr_yes#}            {#gnr_no#}"
        }
    </style>
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_publish_edit#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url urltype="path" url_string="bsc/P050/mediaContent/show/0/{$smarty.session.lang}/publish/{$smarty.session.s_rand_num}/{$sh_mediacenter_row->id}/{$sh_mediacenter_row->type}"}'>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_address#}</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">{$sh_mediacenter_row->name}</div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_text#}</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">{$sh_mediacenter_row->text|nl2br}</div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_publish#}</div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput">
                    <label>
                        <input id="discussable" class="discussable checkbox-slider slider-icon colored-palegreen"
                               name="is_published" value="1" style="opacity: 0;position: absolute;" type="checkbox"
                                {if $sh_mediacenter_row->is_published eq 1} checked="checked"{/if}>

                        <span id="discussable" class="text"></span>
                    </label>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success">{#gnr_update#}</button></div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}