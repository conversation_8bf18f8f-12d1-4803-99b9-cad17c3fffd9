{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />{/block}

{block name=page_body}

    {if count($contact_us_list) gt 0 }
    <div class="table-responsive" data-pattern="priority-columns">
        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
            <thead>
            <tr>
                 <th style="background-color: #A0D468 !important;" width="5%" >&nbsp;</th>
                 <th style="background-color: #A0D468 !important;" width="10%" data-priority="1">{#gnr_date#}</th>
                 <th style="background-color: #A0D468 !important;" width="15%" data-priority="1">{#p_full_name#}</th>
                 <th style="background-color: #A0D468 !important;" width="15%" data-priority="2">{#p_email_address#}</th>
                 <th style="background-color: #A0D468 !important;" width="15%" data-priority="3">{#p_phone_number#}</th>
                 <th style="background-color: #A0D468 !important;" width="30%" data-priority="4">{#p_message_subject#}</th>
                 <th style="background-color: #A0D468 !important;" width="10%" data-priority="5">{#gnr_settings#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $contact_us_list as $row}
                <tr>
                    <td align="center">{$i++}</td>
                    <td>{getdate type=show row=$row col=created_date}</td>
                    <td>{$row->full_name}</td>
                    <td align="center"><a href="mailto:{$row->email}" target="_top">{$row->email}</a></td>
                    <td align="center">{$row->phone_number}</td>
                    <td>{$row->subject}</td>
                    <td align="center" nowrap="nowrap">
                        {url check=0 urltype="mbutton" url_string="bsc/P050/contactusviewer/browse/0/{$smarty.session.lang}/{$row->id}" text_value="<li class='fa fa-folder-open-o'></li>"}
                        {url check=0 urltype="mdelete" url_string="bsc/P050/contactusviewer/confirm/0/{$smarty.session.lang}/{$row->id}"}
                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>
    </div>
    {else}
        <div class="alert alert-warning margin-bottom-30">
            <p>{#p_no_visitor_message#}</p>
        </div>
    {/if}

{/block}
{block name=back}{url urltype="path" url_string="usr/P050/index/show/0/{$smarty.session.lang}"}{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}