{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}
    <div class="row snsowraper">

        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="widget">
                <div class="widget-header">
                    <span class="widget-caption">{$smarty.session.organization->name}</span>
                </div>
                <div class="widget-body bordered-top bordered-sky">
                    <div class="row snsowraper">
                        <form method="post"
                              action='{url urltype="path" url_string="bsc/P050/employeeportalsetting/show/0/{$smarty.session.lang}/updateDataSections/{$smarty.session.s_employeeportalsetting_token}"}'>

                            <div class="row snsowraper">

                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_display_data#}</div>
                                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                                    {foreach $clientDataSections as $row}
                                        {if in_array($row->id, [911,912,913,914])}
                                            <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsolabel">
                                                <label>
                                                    <input name="dataSections[]" value="{$row->id}"
                                                           type="checkbox" {if in_array($row->id,$currentClientConfiguration)} checked="checked" {/if} >
                                                    <span class="text">&nbsp;&nbsp;{$row->translatedName}
                                                        &nbsp;&nbsp;</span>
                                                </label>
                                            </div>
                                        {/if}
                                    {/foreach}
                                </div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_max_visable_add_on_dashboard#}</div>
                                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsolabel">
                                        <input name="maxVisibleAds" value="{$maxAdsOnDashboards}"
                                               type="number" min="1"/>

                                    </div>
                                </div>

                                <div class=" col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">
                                </div>
                                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                                    <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

    </div>
{/block}

