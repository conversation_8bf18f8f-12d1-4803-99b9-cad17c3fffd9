{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name='head_style'}
    <script src="/templates/assets/js/Chart.bundle.min.js"></script>
{/block}

{block name=body}
    <div class="row snsowraper">
        <div class="widget">
            <div class="widget-body bordered-left bordered-palegreen">
                <table class="table table-striped table-bordered table-hover no-footer mb-4">
                    <thead>
                    <tr>
                        <th>
                            {#p_content_type#}
                        </th>
                        <th>
                            {#p_published#}
                        </th>
                        <th>
                            {#p_not_published#}
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $contentTypes as $contentType}
                        <tr>
                            <td>{getname table=st_setting id=$contentType['code']}</td>
                            <td>{$contentType['published']}</td>
                            <td>{$contentType['not_published']}</td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
                <div class="row">
                    {foreach $contentTypes as $contentType}
                        <div class="col-lg-6">
                            {chart config=$contentType['graph']}
                        </div>
                    {/foreach}
                </div>
            </div>
        </div>
    </div>
{/block}

