{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=body}
    <div class="widget">
        <div class="widget-header bg-blue">
            <i class="widget-icon fa fa-arrow-left"></i>
            <span class="widget-caption">{#gnr_search#}</span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                </a>
            </div>
        </div>
        <div class="widget-body">

            <form method="post"
                  action='{url urltype="path" url_string="bsc/P060/taxesReport/show/0/{$smarty.session.lang}/search"}'>
                <div class="row">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_from_date#}</div>
                    <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=datefrom type=edit row=$smarty.session.searchArray required=true}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_to_date#}</div>
                    <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=dateto type=edit row=$smarty.session.searchArray required=true}</div>

                </div>
                <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>
                {if $smarty.session.searchArray}
                    {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P060/taxesReport/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
                {/if}
            </form>
        </div>
    </div>
    {if $data}
        <div class="row snsowraper">
            {if count($data) neq 0}
                <a style="height: 30px;margin-bottom: 5px" target="_blank" href="{url check=0 urltype="path" url_string="bsc/P060/taxesReport/print/0/{$smarty.session.lang}"}" class="btn btn-default btn-sm"><i class='fa fa-print fa-fw fa-lg'></i>{#gnr_print#}</a>
            {else}
                <div class="alert alert-warning fade in"><i class="fa-fw fa fa-warning"></i>{#p_no_data_to_print#}</div>
            {/if}
            <div class="table-responsive" data-pattern="priority-columns">
                <table id="snsotable-1" class="table table-striped table-bordered dataTable sortable-table">
                    <thead>
                    <tr>
                        <th width="1%">#</th>
                        <th width="10%">{#p_output#}</th>
                        <th width="10%">{#p_input#}</th>
                        <th width="10%">{#p_reference#}</th>
                        <th width="10%">{#p_date#}</th>
                        <th width="20%">{#p_comment#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $data as $row}
                        <tr style="text-align: center">
                            <td>{$i++}</td>
                            {if $row->outcome neq 0}
                                <td>{$row->outcome|number_format:2}</td>
                                {else}
                                <td>{$row->outcome|number_format:2}</td>
                            {/if}
                            {if $row->income neq 0}
                                <td>{$row->income|number_format:2}</td>
                            {else}
                                <td>{$row->income|number_format:2}</td>
                            {/if}
                            <td>{$row.entry->ref_num}</td>
                            <td>{$row.entry->date}</td>
                            <td>
                                {if $row.entry->ref_num|substr:0:2 eq 'SI'}
                                    {#p_sales#}
                                {elseif $row.entry->ref_num|substr:0:2 eq 'RI'}
                                    {#p_returned#}
                                {else}
                                    {#p_entry#}
                                {/if}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th width="10%"></th>
                            <th width="10%">{#p_output#}</th>
                            <th width="10%">{#p_input#}</th>
                            <th width="10%">{#p_tax_all#}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="text-align: center ;background: #8cc474; color:white;">
                            <td><b>{#p_total#}</b></td>
                            <td>{$data->sum('fin_trans_outcome')|number_format:2}</td>
                            <td>{$data->sum('fin_trans_income')|number_format:2}</td>
                            <td>{($data->sum('fin_trans_outcome') - $data->sum('fin_trans_income'))|number_format:2}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    {/if}

{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 10,
                        "oTableTools": {
                            "aButtons": [
                                //"copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}
