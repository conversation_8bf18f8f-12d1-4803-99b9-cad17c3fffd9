{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=title}

{/block}

{block name=body}
    <div class="text-center">
        <h2>
            {#p_taxes_report#}
        </h2>
        {if $smarty.session.searchArray.datefrom}
            {#p_from_date#}:{$smarty.session.searchArray.datefrom}
        {/if}
        {if $smarty.session.searchArray.dateto}
            {#p_to_date#}:{$smarty.session.searchArray.dateto}
        {/if}
    </div>
    <hr>
    <div class="table-responsive" data-pattern="priority-columns">
        <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer">
            <thead>
            <tr>
                <th width="3px">#</th>
                <th width="10%">{#p_output#}</th>
                <th width="10%">{#p_input#}</th>
                <th width="10%">{#p_reference#}</th>
                <th width="10%">{#p_date#}</th>
                <th width="20%">{#p_comment#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $data as $row}
                <tr>
                    <td>{$i++}</td>
                    {if $row->outcome neq 0}
                        <td>{$row->outcome|number_format:2}</td>
                    {else}
                        <td>{$row->outcome|number_format:2}</td>
                    {/if}
                    {if $row->income neq 0}
                        <td>{$row->income|number_format:2}</td>
                    {else}
                        <td>{$row->income|number_format:2}</td>
                    {/if}
                    <td>{$row.entry->ref_num}</td>
                    <td>{$row.entry->date}</td>
                    <td>
                        {if $row.entry->ref_num|substr:0:2 eq 'SI'}
                            {#p_sales#}
                        {elseif $row.entry->ref_num|substr:0:2 eq 'RI'}
                            {#p_returned#}
                        {else}
                            {#p_entry#}
                        {/if}
                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>
        <table class="table table-striped table-bordered">
            <thead>
            <tr>
                <th width="10%"></th>
                <th width="10%">{#p_output#}</th>
                <th width="10%">{#p_input#}</th>
                <th width="10%">{#p_tax_all#}</th>
            </tr>
            </thead>
            <tbody>
            <tr style="text-align: center ;background: #8cc474; color:white;">
                <td><b>{#p_total#}</b></td>
                <td>{$data->sum('fin_trans_outcome')|number_format:2}</td>
                <td>{$data->sum('fin_trans_income')|number_format:2}</td>
                <td>{($data->sum('fin_trans_outcome') - $data->sum('fin_trans_income'))|number_format:2}</td>
            </tr>>
            </tbody>
        </table>
    </div>
    <hr>
    {*{render view="/reports/signatures" signatures=$signatures}*}
    <div class="row snsowraper">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            {#gnr_printed_by_user#}: {$smarty.session.user->full_name}<br>
            {#gnr_on_date#}: {$smarty.now|date_format:"%d/%m/%Y"}
        </div>
    </div>
{/block}
