{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}

{block name=body}
<div class="row">
    <div class=" col-12 text-center">
        <h1><u>{#p_std_stock_deposit_print#}</u></h1>
    </div>
    <div class="modal-body">
        <table class="table p-2" >
            <thead>
            <tr>
                <th width="30%"><h2>{#p_std_invoice_no#}:</h2></th>
                <th width="20%"><h3>{$invoice->invoice_no}</h3></th>

                <th width="8%"></th>
                <th width="20%"></th>

                <th width="10%"></th>

                <th width="15%">
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">
                        <img src="/framework/core/functions/image.php?image={$baseUrl}/client/documents/qrcodes/{$qr_path}.png"
                             alt="qr-code" class="col-lg-12">
                    </div>
                </th>


            </tr>
            </thead>
        </table>
        <table class="table table-bordered " >
            <thead>

            <tr>
                <th width="20%" align="center"><h2>{#p_vendor#}:</h2></th>
                <th width="20%" align="center"><h2>{$origanization->name}</h2></th>

                <th width="35%" align="center"><h2>{#p_customer_vat_tax#}:</h2></th>
                <th width="10%" align="center"><h2>{$origanization->vat_tax}</h2></th>

                <th width="15%" align="center"><h2>{#p_std_stock#}:</h2></th>
                <th width="15%" align="center"><h2>{$invoice->stock->name}</h2></th>

            </tr>

            <tr>
                <th width="20%" align="center"><h2>{#p_customer#}:</h2></th>
                <th width="20%" align="center"><h2>{$customers->customer_name}</h2></th>

                <th width="35%" align="center"><h2>{#p_customer_vat_tax#}:</h2></th>
                <th width="10%" align="center"><h2>{$customers->Vat_tax}</h2></th>

                <th width="15%" align="center"><h2>{#p_std_date#}:</h2></th>
                <th width="15%" align="center"><h2>{$invoice->date}</h2></th>
            </tr>

            <tr>
                <th width="15%" align="center"><h2>{#p_pay#}:</h2></th>
                <th width="25%" align="center"><h2>{if $invoice->ispayed eq 0 }{#p_isnotpayed#}{else}{#p_ispayed#}{/if}</h2></th>


                <th width="15%" align="center"><h2>{#p_std_pay_date#}:</h2></th>
                <th width="25%" align="center" ><h2>{$invoice->pay_date }</h2></th>

            </tr>


            </thead>
        </table>

        <br>
        <div class="row mt-2">
            <div class="col-lg-12">
        <div class="table-responsive" data-pattern="priority-columns">
            <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th with="5%"></th>
                    <th width="15%">{#p_std_product#}</th>
                    <th width="15%">{#p_std_unit#}</th>
                    <th width="10%">{#p_std_qty#}</th>
                    <th width="10%">{#p_sale_price#}</th>
                    <th width="10%">{#p_discount#}</th>
                    <th width="10%">{#p_value_of_discount#}</th>
                    <th width="10%">{#p_sale_taxi#}</th>
                    <th width="10%">{#p_value_of_taxi#}</th>
                    <th width="10%">{#p_total_mount#}</th>
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $details as $row}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td>{$row->product->name}</td>
                        <td>{$row->productUnit->unit->name}</td>
                        <td>{$row->qty}</td>
                        <td>{$row->sale_price}</td>
                        <td>{$row->discount}</td>
                        <td>{$row->total_discount}</td>
                        <td>{$row->tax}</td>
                        <td>{$row->total_tax}</td>
                        <td>{$row->product_total}</td>
                    </tr>
                {/foreach}
                </tbody>
            </table>

           <br>
           <hr>
            <table class="table mb-1">
                <thead>
                <tr>

                    <th width="20%">{#p_actual_amount#}</th>

                    <th width="30%">
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">
                            <span>{$invoice->subProductTotal}</span>
                        </div>
                    </th>

                    <th width="20%">{#p_total_tax_amount#}</th>
                    <th width="30%">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 snsoinput">
                            <span>{$invoice->sumTotaltax}</span>
                        </div></th>

                </tr>

               <tr>
                   <th width="20%">{#p_total_discount_amount#}</th>
                   <th width="35%"><span>{$invoice->sumTotalDiscount}</span></th>

                   <th width="20%">{#p_actual_pay#}</th>
                   <th width="35%"><span>{$invoice->subTotal}</span></th>
               </tr>
                <tr>
                    <th width="20%">{#p_actual_pay_amount_without_discount#}</th>
                    <th width="35%"><span>{$invoice->subTotal_without_discount}</span></th>


                </tr>
                </thead>
            </table>




        </div>
            </div>
        </div>
        <hr>
    </div>
    <div class="row snsowraper">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            {#gnr_printed_by_user#}: {$smarty.session.user->full_name}<br>
            {#gnr_on_date#}: {$smarty.now|date_format:"%d/%m/%Y"}
        </div>
    </div>
    {/block}



























































    {extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
    {block name=head_style}
        <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
    {/block}

    {block name=body}
    <div class="row">
        <div class="col col-12 text-center">
            <h1><u>{#p_std_stock_deposit_print#}</u></h1>
        </div>

        <div class="col-12 modal-body">
            <table class="table ">
                <thead>
                <tr>
                    <th width="30%">
                        <div class="modal-body">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th >

                                        <img src="/framework/core/functions/image.php?image={$baseUrl}/client/documents/qrcodes/{$qr_path}.png"
                                             alt="qr-code" width="30%" height="30%" >

                                    </th>

                                </tr>

                            </table>
                        </div>
                    </th>
                    <th width="70% ">
                        <div class="modal-body m-1">
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <td class="text-right"><h4>{#p_std_invoice_no#}</h4></td>
                                    <td class="text-center"><h4>{$invoice->invoice_no}</h4></td>

                                    <td class="text-left"><h4>Invoice Number</h4></td>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <br>
                        <div class="modal-body m-1">
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <td class="text-right" >{#p_std_date#}</td>
                                    <td class="text-center">{$invoice->date}</td>
                                    <td class="text-left" ><h4>Invoice Issue Date</h4></td>
                                </tr>
                                <tr>
                                    <th class="text-right" >{#p_std_pay_date#}</th>
                                    <th class="text-center">{$invoice->pay_date}</th>
                                    <th class="text-left" >Date of supply</th>
                                </tr>
                                <tr>
                                    <th class="text-right" >{#p_pay#}</th>
                                    <th class="text-center">{if $invoice->ispayed eq 0 }{#p_isnotpayed#}{else}{#p_ispayed#}{/if}</th>
                                    <th class="text-left" >ٍStatus payment</th>
                                </tr>
                                {if $haveProducts > 0}
                                    <tr>
                                        <th class="text-right" >{#p_std_stock#}</th>
                                        <th class="text-center">{$invoice->stock->name} {$haveProducts}</th>
                                        <th class="text-left" >Stock Name</th>
                                    </tr>
                                {/if}
                                </thead>
                            </table>
                        </div>

                    </th>


                </tr>
            </table>
        </div>

        <div class="col-12 modal-body">
            <table class="table table-bordered">
                <thead>

                <tr>
                    <td class="text-right" >{#p_customer#}</td>
                    <td class="text-center"></td>
                    <td class="text-left" >Buyer</td>

                    <td class="text-right" >{#p_seller#}</td>
                    <td class="text-center"></td>
                    <td class="text-left" >Seller</td>
                </tr>



                <tr>
                    <td class="text-right" >{#p_customer_name#}</td>
                    <td class="text-center">{$customers->customer_name}</td>
                    <td class="text-left" >Name</td>

                    <td class="text-right" >{#p_customer_name#}</td>
                    <td class="text-center">{$origanization->name}</td>
                    <td class="text-left" >Name</td>
                </tr>
                د




                <tr>
                    <td class="text-right" >{#p_customer_address#}</td>
                    <td class="text-center">{$customers->customer_address}</td>
                    <td class="text-left" >ِِAddress</td>

                    <td class="text-right" >{#p_customer_address#}</td>
                    <td class="text-center">{$origanization->address}</td>
                    <td class="text-left" >ِِAddress</td>
                </tr>




                <tr>
                    <td class="text-right" >{#p_customer_phone#}</td>
                    <td class="text-center">{$customers->customer_phone}</td>
                    <td class="text-left" >phone</td>

                    <td class="text-right" >{#p_customer_phone#}</td>
                    <td class="text-center">{$origanization->phone}</td>
                    <td class="text-left" >phone</td>
                </tr>



                <tr>
                    <td class="text-right" >{#p_customer_vat_tax#}</td>
                    <td class="text-center">{$customers->Vat_tax}</td>
                    <td class="text-left" >VAT Number</td>

                    <td class="text-right" >{#p_customer_vat_tax#}</td>
                    <td class="text-center">{$origanization->vat_tax}</td>
                    <td class="text-left" >VAT Number</td>
                </tr>
                </thead>
            </table>
        </div>
        </th>


        </tr>
        </table>
    </div>

    <div class="col-lg-12">
        <div class="table-responsive" data-pattern="priority-columns">
            <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th with="5%"></th>
                    <th width="15%">{#p_std_product#}</th>
                    <th width="15%">{#p_std_unit#}</th>
                    <th width="10%">{#p_std_qty#}</th>
                    <th width="10%">{#p_sale_price#}</th>
                    <th width="10%">{#p_discount#}</th>
                    <th width="10%">{#p_value_of_discount#}</th>
                    <th width="10%">{#p_sale_taxi#}</th>
                    <th width="10%">{#p_value_of_taxi#}</th>
                    <th width="10%">{#p_total_mount#}</th>
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $details as $row}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td>{$row->product->name}</td>
                        <td>{$row->productUnit->unit->name}</td>
                        <td>{$row->qty}</td>
                        <td>{$row->sale_price}</td>
                        <td>{$row->discount}</td>
                        <td>{$row->total_discount}</td>
                        <td>{$row->tax}</td>
                        <td>{$row->total_tax}</td>
                        <td>{$row->product_total}</td>
                    </tr>
                {/foreach}
                </tbody>
            </table>
        </div>
    </div>

    <div class="col-12 modal-body">

        <div class="table-responsive" data-pattern="priority-columns">
            <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table ">
                <thead>
                <tr>
                    <th>إجمالي المبالغ: </th>

                    <th class="text-left" >:Total amounts</th>
                </tr>
                </thead>
            </table>
        </div>

        <div class="table-responsive" data-pattern="priority-columns">
            <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table ">

                <tbody>


                <tr>
                    <td class="text-center" style="direction: ltr" width="20%">{$invoice->subProductTotal} SAR</td>
                    <td class="text-right" >{#p_actual_amount#} ( {#p_total_exclude#}) </td>
                    <td class="text-left" style="direction: ltr" >Total (excluding VAT) </td>
                </tr>
                <tr>
                    <td class="text-center" width="20%" style="direction: ltr">{$invoice->sumTotalDiscount} SAR</td>
                    <td class="text-right" >{#p_total_discount_amount#} </td>
                    <td class="text-left" style="direction: ltr" >Discount</td>
                </tr>

                <tr>
                    <td class="text-center" width="20%" style="direction: ltr">{$invoice->subTotal_without_discount} SAR</td>
                    <td class="text-right" >{#p_actual_pay_amount_without_discount#}  ( {#p_total_exclude#}) </td>
                    <td class="text-left" style="direction: ltr" >Total Taxable Amount(excluding VAT) </td>
                </tr>
                <tr>
                    <td class="text-center" width="20%" style="direction: ltr">{$invoice->sumTotaltax} SAR</td>
                    <td class="text-right" >{#p_total_tax_amount#} </td>
                    <td class="text-left" style="direction: ltr" >Total VAT </td>
                </tr>
                <tr>
                    <td class="text-center" width="20%" style="direction: ltr">{$invoice->sumTotaltax}  SAR</td>
                    <td class="text-right" >{#p_actual_pay#} </td>
                    <td class="text-left" style="direction: ltr" >Total Amount Due </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>



    <div class="row snsowraper">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            {#gnr_printed_by_user#}: {$smarty.session.user->full_name}<br>
            {#gnr_on_date#}: {$smarty.now|date_format:"%d/%m/%Y"}
        </div>
    </div>

    </style>
    {/block}

