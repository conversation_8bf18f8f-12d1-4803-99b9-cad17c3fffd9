{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=body}

<div class="row">
    <div class="col-md-12">

        <div class="tabbable">
            <ul class="nav nav-tabs nav-justified">
                <li class="active">
                    <a aria-expanded="false" href="{url urltype="path" url_string="bsc/P060/Invoices/show/0/{$smarty.session.lang}"}" disabled="disabled">{#p_directInvoice#}</a>
                </li>

                <li class="">
                    <a aria-expanded="false" href="{url urltype="path" url_string="bsc/P060/Invoices/InvoiceFromQuotation/0/{$smarty.session.lang}" }">{#p_byQuotion_invoice#}</a>
                </li>

            </ul>

            <div class="tab-content p-2">
                <div class="tab-pane active" id="mainPane">
                    <div class="row">
                        <div class="widget {if !$smarty.session.filterParams} collapsed {/if}">
                            <div class="widget-header bg-blue">
                                <i class="widget-icon fa fa-arrow-left"></i>
                                <span class="widget-caption">{#gnr_search#}</span>
                                <div class="widget-buttons">
                                    <a href="#" data-toggle="collapse">
                                        <i class="fa fa-{if $smarty.session.filterParams}minus{else}plus{/if}"></i>
                                    </a>
                                </div><!--Widget Buttons-->
                            </div><!--Widget Header-->
                            <div class="widget-body">

                                <form method="post"
                                      action='{url urltype="path" url_string="bsc/P060/Invoices/show/0/{$smarty.session.lang}/filter"}'>

                                    <div class="row">

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_stock#}</div>
                                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                            <select name="stock" placeholder="{#gnr_select_from_list_bellow#}">
                                                <option></option>
                                                {foreach $stocks as $stock}
                                                    <option value="{$stock->id}" {if $smarty.session.filterParams.stock eq $stock->id} selected {/if}>{$stock->name}</option>
                                                {/foreach}
                                            </select>
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                            <select name="customer_id" placeholder="{#gnr_select_from_list_bellow#}">
                                                <option></option>
                                                {foreach $customers as $customer}
                                                    <option value="{$customer->id}" {if $smarty.session.filterParams.customer_id eq $customer->id} selected {/if}>{$customer->customer_name}</option>
                                                {/foreach}
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row">

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_from_date#}</div>
                                        <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=datefrom type=edit row=$smarty.session.filterParams required=true}</div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_to_date#}</div>
                                        <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=dateto type=edit row=$smarty.session.filterParams required=true}</div>

                                    </div>
                                    <div class="row" style="padding-top: 2px">
                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">مبلغ الفاتورة</div>
                                        <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">
                                            <input type="number" class="form-control" name="invoice_value" placeholder="مبلغ الفاتورة" value="{$smarty.session.filterParams.invoice_value}">
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">رقم الفاتورة</div>
                                        <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">
                                            <input type="text" class="form-control" name="invoice_no" placeholder="رقم الفاتورة" value="{$smarty.session.filterParams.invoice_no}">
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>
                                    {if count($stockDeposits)}
                                        {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P060/Invoices/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
                                    {/if}
                                </form>
                            </div>
                            {if ($smarty.session.filterParams)}
                                <div class="row snsowraper">
                                    <div class="col-lg-12 widget-body">
                                        <h5 class="text-center"><span
                                                    style="font-size: smaller;">{url check=0 urltype="alink" url_string="bsc/P060/Invoices/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}</span>
                                        </h5>
                                    </div>
                                </div>
                            {/if}
                        </div>
                        <div class="row snsowraper">

                            <div class="table-responsive" data-pattern="priority-columns">
                                <table id="snsotable-1" class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                    <thead>
                                    <tr>
                                        <th width="5%">
                                            {url check=1 style="btn btn-success" urltype="add" opr_code='Invoices' url_string="bsc/P060/Invoices/add/0/{$smarty.session.lang}"}
                                        </th>
                                        <th width="10%">{#p_std_invoice_no#}</th>
                                        <th width="10%">{#p_std_stock#}</th>
                                        <th width="10%">{#p_pay#}</th>
                                        <th width="10%">{#p_customer#}</th>
                                        <th width="10%">{#p_std_date#}</th>
                                        <th width="10%">{#p_returned#}</th>

                                        <th width="10%">{#p_total_invoice_no#}</th>
                                        <th width="10%">{#p_std_details#}</th>
                                        <th width="%" title="{#title1#}{#title2#}{#title3#}" >{#gnr_settings#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $invoices as $invoice}
                                        {if $invoice->status eq 1}
                                            <tr>
                                                <td align="center">{$i++}</td>
                                                <td>{$invoice->invoice_no}</td>
                                                <td>
                                                    {if $invoice->stock_id}
                                                        {$invoice->stock->name}
                                                    {else}
                                                        ---
                                                    {/if}
                                                </td>
                                                <td>{if $invoice->ispayed eq 0 }
                                                        {#p_isnotpayed#}
                                                    {else}
                                                        <a class="btn btn-success sharp" data-toggle="modal" data-target="#modal" href="index.php?bsc/P054/finenteries/browse/0/ar/save_session/{$invoice->entry_id}">{#p_ispayed#}</a>
                                                    {/if}

                                                </td>
                                                <td>{$invoice.customer.customer_name}</td>
                                                <td>{$invoice.date}</td>
                                                <td align="center">
                                                    {if $invoice->returnedInvoice()->first()  }
	                                                    {url  urltype="mbutton" text_value="{#P_gnr_details#}" style="btn btn-default shiny"  url_string="bsc/P060/InvoicesReturnIn/Rdetails/0/{$smarty.session.lang}/{$invoice->returnedInvoice->first()->id}"}
                                                    {else}
                                                        -

                                                    {/if}
                                                </td>
                                                <td>{$invoice->subTotal}</td>
                                                <td align="center">
                                                    {url  urltype="mbutton" text_value="{#gnr_details#}" style="btn btn-default shiny"  url_string="bsc/P060/Invoices/details/0/{$smarty.session.lang}/{$invoice->id}"}</td>
                                                <td align="center" title="{#title1#}{#title2#}{#title3#}">

                                                     {url urltype="print" opr_code='Invoices' url_string="bsc/P060/Invoices/print/0/{$smarty.session.lang}/{$invoice->id}"}
	                                                {if $invoice->created_by eq {$smarty.session.user->id} && $invoice->isdelivered eq {$isnotdelivered} }


                                                        {if $invoice->ispayed eq 0 }
                                                            {*{url check=1 urltype="edit" opr_code='Invoices' url_string="bsc/P060/Invoices/edit/0/{$smarty.session.lang}/{$invoice->id}/{$smarty.session.s_stock_invoices_token}"}*}
                                                            {*{url check=1 urltype="mdelete" opr_code='Invoices' url_string="bsc/P060/Invoices/confirm/0/{$smarty.session.lang}/{$invoice->id}"}*}
                                                        {/if}

                                                        {if $invoice->isproduct_invoice eq 1}
                                                            {url check=0  urltype="button" opr_code='Invoices' text_value="{#P_delivery_stock#}" style="btn btn-default shiny"  url_string="bsc/P060/Invoices/confrimDelivery/0/{$smarty.session.lang}/{$invoice->id}/{$smarty.session.s_stock_invoices_token}"}

                                                        {/if}
                                                    {/if}
                                                    {if $invoice->isdelivered eq {$isdelivered} }
                                                        {*<button class="btn btn-success sharp" disabled> {#p_isdelevered#} </button>*}
                                                        <a class="btn btn-success sharp" data-toggle="modal" data-target="#modal" href="index.php?bsc/P054/finenteries/browse/0/ar/save_session/{$invoice->stock_entry_id}">{#p_isdelevered#}</a>
	                                                    {if is_null($invoice->returnedInvoice()->first())    }
	                                                        {url  urltype="button" text_value="{#p_returninvoice#}"  opr_code='InvoicesReturnIn' url_string="bsc/P060/InvoicesReturnIn/edit/0/{$smarty.session.lang}/{$invoice->id}"}
                                                        {/if}
                                                    {/if}
	                                                {if $invoice->isproduct_invoice eq 0 }<buttom class="btn btn-success sharp" disabled> {#p_isproductservice#}</buttom>{/if}

                                                </td>
                                            </tr>
                                        {/if}
                                    {/foreach}
                                    </tbody>
                                </table>


                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>




{/block}

{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 10,
                        "oTableTools": {
                            "aButtons": [
//								"copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }
            };
        }();
        InitiateSimpleDataTable.init();
    </script>
{/block}
