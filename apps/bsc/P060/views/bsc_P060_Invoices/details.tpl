{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"> {#p_std_details#}</h4>
    </div>
    <div class="modal-body">
        <h5 class="row-title before-blue ml-8">
            {#p_std_invoice_no#} : {$invoice->invoice_no}
        </h5>
        <h5 class="row-title before-blue ml-8">
            {#p_std_stock#} : {$invoice->stock->name}
        </h5>
        <h5 class="row-title before-blue">
            {#p_std_date#} : {$invoice->date}
        </h5>
        <div class="row snsowraper">
            <div class="table-responsive" data-pattern="priority-columns">


                <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table">
                    <thead>
                    <tr>
                        <th with="5%"></th>
                        <th width="15%">{#p_std_product#}</th>
                        <th width="15%">{#p_std_unit#}</th>
                        <th width="10%">{#p_std_qty#}</th>
                        <th width="10%">{#p_sale_price#}</th>
                        <th width="10%">{#p_discount#}</th>
                        <th width="10%">{#p_value_of_discount#}</th>
                        <th width="10%">{#p_sale_taxi#}</th>
                        <th width="10%">{#p_value_of_taxi#}</th>
                        <th width="10%">{#p_total_mount#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $details as $row}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>{$row->product->name}</td>
                            <td>{$row->productUnit->unit->name}</td>
                            <td>{$row->qty}</td>
                            <td>{$row->sale_price}</td>
                            <td>{$row->discount}</td>
                            <td>{$row->total_discount}</td>
                            <td>{$row->tax}</td>
                            <td>{$row->total_tax}</td>
                            <td>{$row->product_total}</td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
        </div>
    </div>
{/block}
{block name=page_header}
    <script type="text/javascript" src="/templates/assets/js/loader.js"></script>
    {DrawChart Data=$Data title={#gnr_chart#} RandNumber="usersType-chart"}
    <!-- Chart Libraries -->
    <script src="/templates/assets/js/charts/morris/raphael-2.0.2.min.js"></script>
    <script src="/templates/assets/js/charts/morris/morris.js"></script>
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
        function exportTo(ID, type) {
            $('#table' + ID).css('display', '').tableExport({type: type, escape: 'false'});
            $('#table' + ID).css('display', 'none');
        }
        {/literal}
    </script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 10,
                        "oTableTools": {
                            "aButtons": [],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                    $("tfoot input").keyup(function () {
                        /* Filter on the column (the index) of this element */
                        oTable.fnFilter(this.value, $("tfoot input").index(this));
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}
