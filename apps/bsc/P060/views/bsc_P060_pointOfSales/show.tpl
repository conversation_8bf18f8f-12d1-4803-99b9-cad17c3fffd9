{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{$inValidColor = "danger"}
{$validColor = "success"}
{block name=head_style}
    <!--Page Related styles-->

    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />
    <style>

        
        .btn{
            position: initial;
        }
        
        .overflow-auto {
            overflow: auto;
        }

        .table-scrollable {
            /* set the height to enable overflow of the table */
            max-height: 350px;

            overflow-x: auto;
            overflow-y: auto;
            scrollbar-width: thin;
        }

        .table-scrollable thead th {
            /* Set header to stick to the top of the container. */
            position: relative;
            top: 0px;

            /* This is needed otherwise the sticky header will be transparent
            */
            background-color: white;

            /* Because bootstrap adds `border-collapse: collapse` to the
             * table, the header boarders aren't sticky.
             * So, we need to make some adjustments to cover up the actual
             * header borders and created fake borders instead
             */
            margin-top: -1px;
            margin-bottom: -1px;

            /* This is our fake border (see above comment) */
            box-shadow: inset 0 1px 0 #dee2e6,
            inset 0 -1px 0 #dee2e6;
        }
        hr {
            border: none;
            height: 15px;
            /* Set the hr color */
            color: #90c474; /* old IE */
            background-color: #90c474; /* Modern Browsers */
        }
        .button {
            background-color: forestgreen;
            border: none;
            color: white;
            padding: 10px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 20px;
            margin: 1px 2px;
            cursor: pointer;
        }

    </style>
{/block}


{block name=page_body}



    <form method="post" action='{url check=1 urltype="path" url_string="bsc/P060/pointOfSales/show/0/{$smarty.session.lang}/insert/{$smarty.session.pos_invoice_token}"}'>
       <div class="row">
            <sale-group groups='{$groups}'>
            </sale-group>
            <div class="col-lg-2"   style="height: 350px">
                <div class="col-lg-12 " >
                    <div class="table-scrollable" data-pattern="priority-columns">
                        <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer table-sortable">
                            <thead>
                            <tr>
                                <th width="100%">{#p_product_List#}</th>
                            </tr>
                            </thead>

                            <tbody is="sale-product" products='{$products}'>

                            </tbody>
                        </table>
                        {*<div is="bar-code" products='{$products}' inline-template>*}

                        {*<div>*}
                    </div>
                </div>
            </div>
            <div class="col-lg-6  " >
                <div data-pattern="priority-columns " class="table-scrollable" style="height: 350px">
                    <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer table-sortable">
                        <thead class="lg:sticky pin-top">
                        <tr>
                            <th width="20%">{#p_std_product#}</th>
                            <th width="25%">{#p_std_unit#}</th>
                            <th width="25%">{#p_std_qty#}</th>
                            <th width="10%">{#p_sale_price#}</th>
                            <th width="10%">{#p_total_mount#}</th>
                            <th width="10%">{#gnr_setting#}</th>
                        </tr>
                        </thead>


                        <tbody is="pos-unit" products='{$products}' >

                        </tbody>


                    </table>
                </div>

                <br/>
                <br/>

            </div>
       </div>

        <div is="total-invoice"  inline-template>
            <div class="row col-12">
                <hr class="col-12 mx-3">
                <div class="row col-12 mx-3 py-2" style="background-color: #90c474;color:white;">
                    <div class="form-inline align-content-center col-lg-10 mr-5">
                        <title_snso>{#p_actual_amount#}</title_snso>
                        <div class="col-lg-4 col-md-12 col-sm-12 col-xs-12 snsoinput " style="background-color: #90c474;color:white;">
                            <input type="text" name="Product_Total" class="from-control col-lg-12" v-model="Product_Total" readonly>
                        </div>
                        <title_snso>{#p_actual_pay#}</title_snso>
                        <div class="col-lg-4 col-md-5 col-sm-12 col-xs-12 snsoinput" style="background-color: #90c474;color:white;">
                            <input type="text" name="total_invoice" class="from-control col-lg-12" v-model="total_invoice" readonly>
                        </div>
                        <title_snso>{#p_actual_pay_amount_without_discount#}</title_snso>
                        <div class="col-lg-4 col-md-5 col-sm-12 col-xs-12 snsoinput" style="background-color: #90c474;color:white;">
                            <input type="text" name="subTotal_without_discount" class="from-control col-lg-12"
                                   v-model="subTotal_without_discount" >

                        </div>
                        <title_snso>{#p_total_tax_amount#}</title_snso>
                        <div class="col-lg-4 col-md-5 col-sm-12 col-xs-12 snsoinput" style="background-color: #90c474;color:white;">
                            <input type="hidden" v-model="tax_parsent" v-text="tax_parsent" >
                            <span><input type="text" v-model="tax_parsent"  class="from-control col-lg-4" @change="generate_taxt()"></span>
                            <input type="text" name="total_tax" class="from-control col-lg-8" v-model="total_tax"  />

                        </div>
                        <title_snso>{#p_total_discount_amount#}</title_snso>
                        <div class="col-lg-4 col-md-5 col-sm-12 col-xs-12  snsoinput" style="background-color: #90c474;color:white;">
                            <input type="hidden" v-model="discount_parsent" v-text="discount_parsent" >
                            <input type="text"  v-model="discount_parsent" class="from-control col-lg-4" v-text="discount_parsent"  @change="generate_discount()"  size="5" >
                            <input type="text" name="total_discount" class="from-control col-lg-8" v-model="total_discount" >
                        </div>

                    </div>
                    <div class="col-lg-1 ml-3">
                    <button class="btn btn-default col-lg-12 btn-lg shiny p-2" type="submit">
                        <i class="fa fa-shopping-cart fa-6" style="font-size: 3em;"></i>
                    </button>
                    </div>
                </div>
            </div>
        </div>

       </form>



{/block}
{block name=page_header}


{/block}