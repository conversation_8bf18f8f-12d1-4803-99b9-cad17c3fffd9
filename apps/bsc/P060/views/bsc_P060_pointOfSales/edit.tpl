{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=body}


<div class="tab-content p-2">
    <div class="tab-pane active" id="mainPane">
        <div class="row">
            <div class="widget {if !$smarty.session.search_groups} collapsed {/if}">
                <div class="widget-header bg-blue">
                    <i class="widget-icon fa fa-arrow-left"></i>
                    <span class="widget-caption">{#gnr_search#}</span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-{if $smarty.session.search_groups}minus{else}plus{/if}"></i>
                        </a>
                    </div><!--Widget Buttons-->
                </div><!--Widget Header-->
                <div class="widget-body">

                    <form method="post"
                            action='{url urltype="path" url_string="bsc/P060/pointOfSales/show/0/{$smarty.session.lang}/filter"}'>

                        <div class="row">

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_stock#}</div>
                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                <select name="stock" placeholder="{#gnr_select_from_list_bellow#}">
                                    <option></option>
                                    {foreach $stocks as $stock}
                                        <option value="{$stock->id}" {if $smarty.session.post_data.stock eq $stock->name} selected {/if}>{$stock->name}</option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                <select name="customer_id" placeholder="{#gnr_select_from_list_bellow#}">
                                    <option></option>
                                    {foreach $customers as $customer}
                                        <option value="{$customer->id}" {if $smarty.session.post_data.customer_id eq $customer->customer_name} selected {/if}>{$customer->customer_name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>

                        <div class="row">

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_from_date#}</div>
                            <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=datefrom type=edit row=$smarty.session.filterParams required=true}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_to_date#}</div>
                            <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=dateto type=edit row=$smarty.session.filterParams required=true}</div>

                        </div>
                        <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>
                        {if count($stockDeposits)}
                            {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P060/Invoices/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
                        {/if}
                    </form>
                </div>
            </div>
            <div class="row snsowraper">

                <div class="table-responsive" data-pattern="priority-columns">
                    <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer">
                        <thead>
                        <tr>
                            <th width="5%">
                                {url check=1 style="btn btn-success" urltype="add" opr_code='pointOfSales' url_string="bsc/P060/pointOfSales/add/0/{$smarty.session.lang}"}
                            </th>
                            <th width="10%">{#p_std_invoice_no#}</th>
                            <th width="10%">{#p_std_stock#}</th>
                            <th width="10%">{#p_std_date#}</th>
                            <th width="10%">{#p_customer#}</th>
                            <th width="10%">{#p_total_invoice_no#}</th>
                            <th width="10%">{#p_std_details#}</th>
                            <th width="20%">{#gnr_settings#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {$i=1}
                        {foreach $invoices as $invoice}
                            {if $invoice->status eq 1}
                                <tr>
                                    <td align="center">{$i++}</td>
                                    <td>{$invoice->invoice_no}</td>
                                    <td>{$invoice->stock->name}</td>
                                    <td>{$invoice->date}</td>
                                    <td>{$invoice.customer.customer_name}</td>
                                    <td>{$invoice->subTotal}</td>
                                    <td align="center">{url  urltype="mbutton" text_value="{#browse#}" style="btn btn-default shiny"  url_string="bsc/P060/Invoices/details/0/{$smarty.session.lang}/{$invoice->id}"}</td>
                                    <td align="center">
                                        {if $invoice->created_by eq {$smarty.session.user->id}}
                                            {url urltype="print" opr_code='pointOfSales' url_string="bsc/P060/pointOfSales/print/0/{$smarty.session.lang}/{$invoice->id}"}
                                            {url check=1 urltype="edit" opr_code='pointOfSales' url_string="bsc/P060/pointOfSales/edit/0/{$smarty.session.lang}/{$invoice->id}"}
                                            {url check=1 urltype="mdelete" opr_code='pointOfSales' url_string="bsc/P060/pointOfSales/confirm/0/{$smarty.session.lang}/{$invoice->id}"}
                                        {/if}

                                    </td>
                                </tr>
                            {/if}
                        {/foreach}
                        </tbody>
                    </table>


                </div>
            </div>

        </div>
    </div>
</div>





{/block}
{block name=page_header}
{/block}