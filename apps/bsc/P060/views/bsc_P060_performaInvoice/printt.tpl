{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}

{block name=body}
<div class="row">
    <div class=" col-12 text-center">
        <h1><u>{#p_std_stock_deposit_print#}</u></h1>
    </div>
    <div class="modal-body">
        <table class="table mb-1">
            <thead>
            <tr>
                <th width="10%"><h4>{#p_std_invoice_no#}:</h4></th>
                <th width="20%"><h3>{$invoice->invoice_no}</h3></th>

                <th width="8%"></th>
                <th width="35%"></th>

                <th width="8%"></th>

                <th width="10%">
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">
                        <img src="/framework/core/functions/image.php?image={$baseUrl}/client/documents/qrcodes/{$qr_path}.png"
                             alt="qr-code" class="col-lg-12">
                    </div>
                </th>


            </tr>

            <tr>
                <th width="15%"><h2>{#p_customer#}:</h2></th>
                <th width="25%"><h2>{$customers->customer_name}</h2></th>

                <th width="8%"><h2>{#p_std_stock#}:</h2></th>
                <th width="30%"><h2>{$invoice->stock->name}</h2></th>

                <th width="8%"><h2>{#p_std_date#}:</h2></th>
                <th width="15%"><h2>{$invoice->date}</h2></th>
            </tr>


            </thead>
        </table>

        <br>
        <div class="row mt-2">
            <div class="col-lg-12">
                <div class="table-responsive" data-pattern="priority-columns">
                    <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table">
                        <thead>
                        <tr>
                            <th with="5%"></th>
                            <th width="15%">{#p_std_product#}</th>
                            <th width="15%">{#p_std_unit#}</th>
                            <th width="10%">{#p_std_qty#}</th>
                            <th width="10%">{#p_sale_price#}</th>
                            <th width="10%">{#p_discount#}</th>
                            <th width="10%">{#p_value_of_discount#}</th>
                            <th width="10%">{#p_sale_taxi#}</th>
                            <th width="10%">{#p_value_of_taxi#}</th>
                            <th width="10%">{#p_total_mount#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {$i=1}
                        {foreach $details as $row}
                            <tr>
                                <td align="center">{$i++}</td>
                                <td>{$row->product->name}</td>
                                <td>{$row->productUnit->unit->name}</td>
                                <td>{$row->qty}</td>
                                <td>{$row->sale_price}</td>
                                <td>{$row->discount}</td>
                                <td>{$row->total_discount}</td>
                                <td>{$row->tax}</td>
                                <td>{$row->total_tax}</td>
                                <td>{$row->product_total}</td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>

                    <br>
                    <hr>
                    <table class="table mb-1">
                        <thead>
                        <tr>

                            <th width="20%">{#p_actual_amount#}</th>

                            <th width="30%">
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">
                                    <span>{$invoice->subProductTotal}</span>
                                </div>
                            </th>

                            <th width="20%">{#p_total_tax_amount#}</th>
                            <th width="30%">
                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 snsoinput">
                                    <span>{$invoice->sumTotaltax}</span>
                                </div></th>

                        </tr>

                        <tr>
                            <th width="20%">{#p_total_discount_amount#}</th>
                            <th width="35%"><span>{$invoice->sumTotalDiscount}</span></th>

                            <th width="20%">{#p_actual_pay#}</th>
                            <th width="35%"><span>{$invoice->subTotal}</span></th>
                        </tr>
                        <tr>
                            <th width="20%">{#p_actual_pay_amount_without_discount#}</th>
                            <th width="35%"><span>{$invoice->subTotal_without_discount}</span></th>

                        </tr>
                        </thead>
                    </table>




                </div>
            </div>
        </div>
        <hr>
    </div>
    <div class="row snsowraper">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            {#gnr_printed_by_user#}: {$smarty.session.user->full_name}<br>
            {#gnr_on_date#}: {$smarty.now|date_format:"%d/%m/%Y"}
        </div>
    </div>
    {/block}
