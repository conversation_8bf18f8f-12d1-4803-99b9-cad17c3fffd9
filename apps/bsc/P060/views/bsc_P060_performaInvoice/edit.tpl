{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}

{block name=page_body}


        <div class="row">
            <div class="row col-md-6">
                {if can('toSales')}
                    <div class="col-md-3">

                    {*<button type="button" class="btn btn-primary"><a href="{url check=1 urltype="path" url_string="bsc/P060/performaInvoice/show/0/{$smarty.session.lang}/makeInvoice/{$SalesGenerateTemInvoices.id}/{$smarty.session.s_stock_performaInvoice_token}"}">{#p_makeInvoice#}</a></button>*}

                        <span>{url check=0 urltype="button" style="btn btn-success sharp mb-1" url_string="bsc/P060/performaInvoice/perfomainvoices/0/{$smarty.session.lang}/makeInvoice/{$SalesGenerateTemInvoices.id}/{$smarty.session.s_stock_performaInvoice_token}" text_value="{#p_makeInvoice#}"}</span>


                    </div>
                {/if}
            <div class="col-md-3">
                <span>{url urltype="print" text_value="{#p_print#} " style="btn btn-success sharp mb-1" opr_code='performaInvoice'  url_string="bsc/P060/performaInvoice/print/0/{$smarty.session.lang}/{$invoice.id}"}</span>
                {*<button type="button" class="btn btn-primary"><a href="{url check=1 urltype="path" url_string="bsc/P060/performaInvoice/show/0/{$smarty.session.lang}/makeInvoice/{$SalesGenerateTemInvoices.id}/{$smarty.session.s_stock_performaInvoice_token}"}">{#p_makeInvoice#}</a></button>*}
                {*<span>{url check=0 urltype="button" style="btn btn-success shiny ml-10 mb-1" url_string="bsc/P060/performaInvoice/show/0/{$smarty.session.lang}/makeInvoice/{$SalesGenerateTemInvoices.id}/{$smarty.session.s_stock_performaInvoice_token}" text_value="{#gnr_cancel#} {#p_invoice_cancel#}"}</span>*}
            </div>
            </div>

        </div>

        <div class="row">
            <div class="col-md-12">

                <tem-invoices
                        stocks='{$stocks}'
                        products='{$products}'
                        sales-generate-tem-invoices='{$SalesGenerateTemInvoices}'
                        index="{$SalesGenerateTemInvoices.id}"
                        stock-type="{$stockType}"
                        inline-template>

                    <div>
                        <form method="post"
                              action='{url check=1 urltype="path" url_string="bsc/P060/performaInvoice/perfomainvoices/0/{$smarty.session.lang}/update/{$SalesGenerateTemInvoices.id}/{$smarty.session.s_stock_performaInvoice_token}"}'>
                            <div class="widget-body">

                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_std_invoice_no#}</div>
                                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput" >
                                            <h4><span>{$SalesGenerateTemInvoices.invoice_no}</span></h4>
                                            <input type="hidden" name="invoice_no" value="{$SalesGenerateTemInvoices.invoice_no}">
                                        </div>

                                        <div v-if="is_product_invoice" class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_std_stock#}</div>
                                        <div v-if="is_product_invoice" class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">


                                            <selectize name="stock_id"  v-model="id" class="form-control" required
                                                       data-live-search="true">
                                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                                <option v-for="stock in stocksData" :key="stock.id" :value="stock.id"
                                                        v-text="stock.name"></option>

                                            </selectize>
                                            <p class="snsolable" :value="text"></p>
                                        </div>




                                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                            <select name="customer_id" placeholder="{#gnr_select_from_list_bellow#}" required
                                                    class="form-control">

                                                <option></option>
                                                {foreach $customers as $customer}
                                                    <option value="{$customer->id}" {if $SalesGenerateTemInvoices.customer_id eq $customer->id} selected {/if}>{$customer->customer_name}</option>
                                                {/foreach}
                                            </select>
                                            <p class="snsolable" :value="text"></p>
                                        </div>





                                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_payment_method#}</div>
                                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput ">
                                            <select name="paymentType" placeholder="{#gnr_select_from_list_bellow#}" class="form-control" required>
                                                <option></option>
                                                <option value="1111" {if $theInvoice.paymentType eq 1111}selected{/if}> {#p_cash#}</option>
                                                <option value="2111" {if $theInvoice.paymentType eq 2111}selected{/if}> {#p_bank#}</option>
                                                <option value="41111" {if $theInvoice.paymentType eq 41111}selected{/if}> {#p_postment#} </option>


                                            </select>
                                            <p class="snsolable" :value="text"></p>
                                        </div>

                                    </div>
                                    <div class="col-lg-3">
                                            {*<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>*}
                                            {*<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput mb-3">*}

                                            {*</div>*}
                                           <div class="col-lg-12"></div>

                                            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 snsolabel">{#p_std_date#}</div>
                                            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 snsoinput mb-3">
                                                    {getdate table=sales_generate_invoice col=date  type=edit future=false row=$theInvoice required=true}
                                            </div>
                                            <br>


                                            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 snsolabel">{#p_std_enddate#}</div>
                                            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 snsoinput">
                                                {getdate table=sales_generate_invoice col=invoice_end type=edit past=false  row=$theInvoice required=true}</div>


                                    </div>
                                    <div class="col-lg-3">
                                        <img style="border: 1px #333 dotted;" src="/framework/core/functions/image.php?image={$baseUrl}/client/documents/qrcodes/{$qr_path}.png"
                                             alt="qr-code" class="col-lg-12">
                                    </div>

                                </div>
                                <div class="row mt-1">
                                    <div class="col-lg-12 pl-2">
                                        <div class="col-lg-1">
                                            <div class="snsolabel">{#p_std_comment#}</div>
                                        </div>
                                        <div class="col-lg-11">
                                            <textarea name="note" class="form-control mr-2"
                                                      placeholder="{#p_std_palce_holder_comment#}">{$SalesGenerateTemInvoices.note}</textarea>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="row mt-2 ">
                                <div class="col-lg-12">
                                    <div class="widget-body">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                        <tr>
                                            <th width="2%">
                                                <button @click.prevent="addProduct" class="btn btn-success btn-sm sharp icon-only">
                                                    <i class='fa fa-plus'></i>
                                                </button>
                                            </th>
                                            <th width="15%">{#p_std_product#}</th>
                                            <th width="7%">{#p_std_unit#}</th>
                                            <th width="5%">{#p_std_total_qty#}</th>
                                            <th width="7%">{#p_std_qty#}</th>
                                            <th width="5%">{#p_sale_price#}</th>
                                            <th width="5%">{#gnr_status#}</th>
                                            <th width="8%">{#p_discount#}</th>
                                            <th width="10%">{#p_value_of_discount#}</th>
                                            <th width="8%">{#p_sale_taxi#}</th>
                                            <th width="10%">{#p_value_of_taxi#}</th>
                                            <th width="10%">{#p_total_mount#}</th>
                                            <th width="10%">{#gnr_settings#}</th>

                                        </tr>
                                        </thead>
                                        <tbody>

                                        <tr
                                                is="sale-unit"
                                                v-for="(stock , index ) in productsData"
                                                :key="index"
                                                :index="index"
                                                :stock="stock"
                                                :products-unit="productsUnit"
                                                :id="id"
                                                @remove="removeProduct"
                                                @changed="changed"
                                        >

                                        </tr>


                                        </tbody>


                                    </table>
                                    </div>

                                    <div class="row mt-2">
                                        <div class=" row widget-body ml-1 mr-1">
                                            <div class="col-12">
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_actual_amount#}</div>
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                                    <input type="text" name="subProductTotal" v-model="subProductTotal" readonly>
                                                </div>
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_total_tax_amount#}</div>
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                                    <input type="text" name="sumTotaltax" v-model="sumTotaltax" readonly>
                                                </div>

                                            </div>
                                            <div class="col-12">
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_total_discount_amount#}</div>
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                                    <input type="text" name="sumTotalDiscount" v-model="sumTotalDiscount" readonly>
                                                </div>
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_actual_pay#}</div>
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                                    <input type="text" name="subTotal" v-model="subTotal" readonly>
                                                </div>
                                            </div>

                                            <div class="col-12">
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_actual_pay_amount_without_discount#}</div>
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                                    <input type="text" name="subTotal_without_discount"
                                                           v-model="subTotal_without_discount" readonly>
                                                </div>


                                            </div>
                                        </div>

                                    </div>


                                    <div class="snsoinput">
                                        <button type="submit" class="btn btn-success text-align-center sharp col-lg-2 mt-3 " :disabled="disabled"
                                                :title="disabled">{#gnr_save#}</button>
                                    </div>

                                </div>
                            </div>
                        </form>
                    </div>

                </tem-invoices>

            </div>

        </div>



{/block}
{block name=back}{url urltype="path" url_string="bsc/P060/performaInvoice/perfomainvoices/0/{$smarty.session.lang}"}{/block}

