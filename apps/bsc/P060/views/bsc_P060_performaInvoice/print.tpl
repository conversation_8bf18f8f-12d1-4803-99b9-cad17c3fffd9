{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
    <style>
        *{

        }
        th,td{
            padding:4px 6px;
            font-size: 10px !important;
        }
    </style>
{/block}

{block name=body}
<div class="row">
    <div class="col col-12 text-center">
        <h1><u>{#p_std_stock_deposit_print#}</u></h1>
    </div>



    <div class="col-12 modal-body">
        <table class="table ">
            <thead>
            <tr>
                <th width="30%">
                    <div class="modal-body">
                        <table class="table">
                            <thead>
                            <tr>
                                <th width="20">

                                    <img src="/framework/core/functions/image.php?image={$baseUrl}/client/documents/qrcodes/{$qr_path}.png"
                                         alt="qr-code" width="20%" height="20%" >

                                </th>

                            </tr>

                        </table>
                    </div>
                </th>
                <th width="70% ">
                    <div class="modal-body m-1">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <td class="text-right">{#p_std_invoice_quotation_no#}</td>
                                <td class="text-center">{$invoice->invoice_no}</td>

                                <td class="text-left">Quotion Number</td>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <br>
                    {*<div class="modal-body m-1">*}
                        {*<table class="table table-bordered">*}
                            {*<thead>*}
                            {*<tr>*}
                                {*<td class="text-right" >{#p_std_date#}</td>*}
                                {*<td class="text-center">{$invoice->date}</td>*}
                                {*<td class="text-left" >Invoice Issue Date</td>*}
                            {*</tr>*}
                            {*<tr>*}
                                {*<th class="text-right" >{#p_std_pay_date#}</th>*}
                                {*<th class="text-center">{$invoice->pay_date}</th>*}
                                {*<th class="text-left" >Date of supply</th>*}
                            {*</tr>*}
                            {*<tr>*}
                                {*<th class="text-right" >{#p_pay#}</th>*}
                                {*<th class="text-center">{if $invoice->ispayed eq 0 }{#p_isnotpayed#}{else}{#p_ispayed#}{/if}</th>*}
                                {*<th class="text-left" >ٍStatus payment</th>*}
                            {*</tr>*}
                            {*{if $haveProducts > 0}*}
                                {*<tr>*}
                                    {*<th class="text-right" >{#p_std_stock#}</th>*}
                                    {*<th class="text-center">{$invoice->stock->name} {$haveProducts}</th>*}
                                    {*<th class="text-left" >Stock Name</th>*}
                                {*</tr>*}
                            {*{/if}*}
                            {*</thead>*}
                        {*</table>*}
                    {*</div>*}
                    <div class="modal-body m-1">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <td class="text-right" >{#p_std_date#}</td>
                                <td class="text-center">{$invoice->date}</td>
                                <td class="text-left" ><h4>Quotion Issue Date</h4></td>
                            </tr>
                            <tr>
                                <th class="text-right" >{#p_std_enddate#}</th>
                                <th class="text-center">{$invoice->invoice_end}</th>
                                <th class="text-left" >End of invoice Quotion</th>
                            </tr>

                            {if $haveProducts > 0}
                                <tr>
                                    <th class="text-right" >{#p_std_stock#}</th>
                                    <th class="text-center">{$invoice->stock->name} {$haveProducts}</th>
                                    <th class="text-left" >Stock Name</th>
                                </tr>
                            {/if}
                            </thead>
                        </table>
                    </div>

                </th>


            </tr>
        </table>
    </div>

    <div class="col-12 modal-body">
        <table class="table table-bordered">
            <thead>

            <tr>
                <td class="text-right" style="background-color: #727272;color: white;border:none;" >{#p_customer#}</td>
                <td class="text-center" style="background-color: #727272;color: white;border:none;"></td>
                <td class="text-left" style="background-color: #727272;color: white;border-right:none;">Buyer</td>

                <td class="text-right" style="background-color: #727272;color: white;border:none;" >{#p_seller#}</td>
                <td class="text-center" style="background-color: #727272;color: white;border:none;"></td>
                <td class="text-left" style="background-color: #727272;color: white;border:none;">Seller</td>
            </tr>



            <tr >
                <td class="text-right" style="border-left:none;border-right:none">{#p_customer_name#}</td>
                <td class="text-center" style="border-left:none;border-right:none" width="20%">{$customers->customer_name}</td>
                <td class="text-left" style="border-right:none;border-bottom: thin">Name</td>

                <td class="text-right" style="border-left:none;border-right:none">{#p_customer_name#}</td>
                <td class="text-center" style="border-left:none;border-right:none" width="20%">{$origanization->name}</td>
                <td class="text-left" style="border-left:none;border-right:none">Name</td>
            </tr>
            د




            <tr style="border-bottom: thin">
                <td class="text-right" style="border-left:none;border-right:none">{#p_customer_address#}</td>
                <td class="text-center" width="20%" style="border-left:none;border-right:none">{$customers->customer_address}</td>
                <td class="text-left" style="border-right:none;">ِِAddress</td>

                <td class="text-right" style="border-left:none;border-right:none">{#p_customer_address#}</td>
                <td class="text-center" width="20%" style="border-left:none;border-right:none">{$origanization->address}</td>
                <td class="text-left" style="border-left:none;border-right:none">ِِAddress</td>
            </tr>




            <tr style="border-bottom: thin">
                <td class="text-right" style="border-left:none;border-right:none">{#p_customer_phone#}</td>
                <td class="text-center" width="20%" style="border-left:none;border-right:none">{$customers->customer_phone}</td>
                <td class="text-left" style="border-right:none;">phone</td>

                <td class="text-right" style="border-left:none;border-right:none">{#p_customer_phone#}</td>
                <td class="text-center" style="border-left:none;border-right:none">{$origanization->phone}</td>
                <td class="text-left" style="border-left:none;border-right:none">phone</td>
            </tr>



            <tr style="border-bottom: thin">
                <td class="text-right" style="border-left:none;border-right:none">{#p_customer_vat_tax#}</td>
                <td class="text-center" width="20%" style="border-left:none;border-right:none">{$customers->Vat_tax}</td>
                <td class="text-left" style="border-right:none;">VAT Number</td>

                <td class="text-right" style="border-left:none;border-right:none">{#p_customer_vat_tax#}</td>
                <td class="text-center" width="20%" style="border-left:none;border-right:none">{$origanization->vat_tax}</td>
                <td class="text-left" style="border-left:none;border-right:none">VAT Number</td>
            </tr>
            </thead>
        </table>
    </div>



    <div class="col-lg-12  modal-body">
        <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table ">
            <thead>
            <tr >
                <th style="background-color: #999999;color: white;border: none">توصيف السلعه او الخدمه: </th>

                <th class="text-left" style="background-color: #999999;color: white;border: none" >:Line items</th>
            </tr>
            </thead>
        </table>
        <table class="table table-bordered table-hover dataTable no-footer sortable-table" >
            <thead>
            <tr>
                <th width="5%" style="background-color: #727272;color: white"> Index</th>
                <th width="15%" style="background-color: #727272;color: white">Nature of goods
                    or services <br> {#p_std_product#}</th>
                <th width="15%" style="background-color: #727272;color: white"> Unit price <br> {#p_std_unit#}</th>
                <th width="10%" style="background-color: #727272;color: white"> Quantity <br> {#p_std_qty#}</th>
                <th width="10%" style="background-color: #727272;color: white"> Price <br> {#p_sale_price#}</th>
                <th width="10%" style="background-color: #727272;color: white"> Discount <br> {#p_discount#}</th>
                <th width="10%" style="background-color: #727272;color: white">Discount Amount <br> {#p_value_of_discount#}</th>
                <th width="10%" style="background-color: #727272;color: white">Tax Rate <br> {#p_sale_taxi#}</th>
                <th width="10%" style="background-color: #727272;color: white"> Tax Amount <br> {#p_value_of_taxi#}</th>
                <th width="10%" style="background-color: #727272;color: white;direction: ltr">Item Subtotal
                    (Including VAT) <br> {#p_total_mount#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $details as $row}
                <tr>
                    <td align="center">{$i++}</td>
                    <td>{$row->product->name}</td>
                    <td>{$row->productUnit->unit->name}</td>
                    <td>{$row->qty}</td>
                    <td>{$row->sale_price}</td>
                    <td>{$row->discount}</td>
                    <td>{$row->total_discount}</td>
                    <td>{$row->tax}</td>
                    <td style="direction: ltr">{$row->total_tax} SAR </td>
                    <td style="direction: ltr">{$row->product_total} SAR</td>
                </tr>
            {/foreach}
            </tbody>
        </table>

    </div>

    <div class="col-12 modal-body">


        <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table ">
            <thead>
            <tr>
                <th style="background-color: #727272;color: white;border: none">إجمالي المبالغ: </th>

                <th class="text-left" style="background-color: #727272;color: white;border: none" >:Total amounts</th>
            </tr>
            </thead>
        </table>



        <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table ">

            <tbody>


            <tr>
                <td class="text-center" style="direction: ltr" width="20%;">{$invoice->subProductTotal} SAR</td>
                <td class="text-right" >{#p_actual_amount#} ( {#p_total_exclude#}) </td>
                <td class="text-left" style="direction: ltr" >Total (excluding VAT) </td>
            </tr>
            <tr>
                <td class="text-center" width="20%" style="direction: ltr">{$invoice->sumTotalDiscount} SAR</td>
                <td class="text-right" >{#p_total_discount_amount#} </td>
                <td class="text-left" style="direction: ltr" >Discount</td>
            </tr>

            <tr>
                <td class="text-center" width="20%" style="direction: ltr">{$invoice->subTotal_without_discount} SAR</td>
                <td class="text-right" >{#p_actual_pay_amount_without_discount#}  ( {#p_total_exclude#}) </td>
                <td class="text-left" style="direction: ltr" >Total Taxable Amount(excluding VAT) </td>
            </tr>
            <tr>
                <td class="text-center" width="20%" style="direction: ltr">{$invoice->sumTotaltax} SAR</td>
                <td class="text-right" >{#p_total_tax_amount#} </td>
                <td class="text-left" style="direction: ltr" >Total VAT </td>
            </tr>
            <tr>
                <td class="text-center" width="20%" style="direction: ltr">{$invoice->subTotal}  SAR</td>
                <td class="text-right" >{#p_actual_pay#} </td>
                <td class="text-left" style="direction: ltr" >Total Amount Due </td>
            </tr>
            </tbody>
        </table>

    </div>



    {*<div class="row snsowraper">*}
        {*<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">*}
            {*{#gnr_printed_by_user#}: {$smarty.session.user->full_name}<br>*}
            {*{#gnr_on_date#}: {$smarty.now|date_format:"%d/%m/%Y"}*}
        {*</div>*}
    {*</div>*}

    </style>
    {/block}









