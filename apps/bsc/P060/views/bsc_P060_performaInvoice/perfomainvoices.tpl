{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{$inValidColor = "danger"}
{$validColor = "success"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />{/block}
{block name=pre}



    <div class="row">
        <div class="col-md-12">

            <div class="tabbable">
                <ul class="nav nav-tabs nav-justified">
                    <li class="active">
                        <a aria-expanded="false" href="{url urltype="path" url_string="bsc/P060/performaInvoice/perfomainvoices/0/{$smarty.session.lang}/menu"}" disabled="disabled">{#p_PerformaInvoices#}</a>
                    </li>
                    <li class="">
                        <a aria-expanded="false" href="{url urltype="path" url_string="bsc/P060/performaInvoice/invoices/0/{$smarty.session.lang}/menu" }">{#p_invoice#}</a>
                    </li>



                    <li class="">
                        <a aria-expanded="false" href="{url urltype="path" url_string="bsc/P060/performaInvoice/InvalidInvoice/0/{$smarty.session.lang}/menu" }">{#p_invlaid_invoice#}</a>
                    </li>

                </ul>

                <div class="tab-content p-2">
                    <div class="tab-pane active" id="mainPane">
                        <div class="row">

                            <div class="widget {if !$smarty.session.filterParams} collapsed {/if} ">
                                <div class="widget-header bg-blue">
                                    <i class="widget-icon fa fa-arrow-left"></i>
                                    <span class="widget-caption">{#gnr_search#}</span>
                                    <div class="widget-buttons">
                                        <a href="#" data-toggle="collapse">
                                            <i class="fa fa-{if $smarty.session.filterParams}minus{else}plus{/if}"></i>
                                        </a>
                                    </div><!--Widget Buttons-->
                                </div><!--Widget Header-->
                                <div class="widget-body">

                                    <form method="post"
                                          action='{url urltype="path" url_string="bsc/P060/performaInvoice/perfomainvoices/0/{$smarty.session.lang}/filter/2"}'>

                                        <div class="row">

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_stock#}</div>
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                                <select name="stock" placeholder="{#gnr_select_from_list_bellow#}">
                                                    <option></option>
                                                    {foreach $stocks as $stock}
                                                        <option value="{$stock->id}" {if $smarty.session.filterParams.stock eq $stock->id} selected {/if}>{$stock->name}</option>
                                                    {/foreach}
                                                </select>
                                            </div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <select name="customer_id" placeholder="{#gnr_select_from_list_bellow#}">
                                                    <option></option>
                                                    {foreach $customers as $customer}
                                                        <option value="{$customer->id}" {if $smarty.session.filterParams.customer_id eq $customer->id} selected {/if}>{$customer->customer_name}</option>
                                                    {/foreach}
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row">

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_from_date#}</div>
                                            <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=datefrom type=edit row=$smarty.session.filterParams required=true}</div>

                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_to_date#}</div>
                                            <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=dateto type=edit row=$smarty.session.filterParams required=true}</div>

                                        </div>
                                        <div class="row" style="padding-top: 2px">
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">مبلغ الفاتورة</div>
                                            <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">
                                                <input type="number" class="form-control" name="invoice_value" placeholder="مبلغ الفاتورة" value="{$smarty.session.filterParams.invoice_value}">
                                            </div>
                                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">رقم الفاتورة</div>
                                            <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">
                                                <input type="text" class="form-control" name="invoice_no" placeholder="رقم الفاتورة" value="{$smarty.session.filterParams.invoice_no}">
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>
                                        {if $smarty.session.filterParams}
                                            {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P060/performaInvoice/perfomainvoices/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
                                        {/if}
                                    </form>
                                </div>
                            </div>
                            <div class="row snsowraper">

                                <div class="table-responsive" data-pattern="priority-columns">
                                    <table id="snsotable-1" class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                        <thead>
                                        <tr>
                                            <th width="5%">
                                                {url check=1 style="btn btn-success" urltype="add" opr_code='performaInvoice' url_string="bsc/P060/performaInvoice/add/0/{$smarty.session.lang}"}
                                            </th>
                                            <th width="10%">{#p_std_invoice_no#}</th>
                                            <th width="10%">{#p_std_stock#}</th>
                                            <th width="10%">{#p_std_date#}</th>
                                            <th width="10%">{#p_invoice_end#}</th>
                                            <th width="10%">{#p_customer#}</th>
                                            <th width="10%">{#p_total_invoice_no#}</th>
                                            <th width="10%">{#p_std_details#}</th>
                                            <th width="10%">{#p_created_by#}</th>
                                            <th width="15%">{#gnr_settings#}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {$i=1}
                                        {foreach $invoices as $invoice}
                                            <tr>
                                                <td align="center">{$i++}</td>
                                                <td>{$invoice->invoice_no}</td>
                                                <td>
                                                    {if $invoice->stock_id}
                                                        {$invoice->stock->name}
                                                    {else}
                                                        ---
                                                    {/if}
                                                </td>
                                                <td>{$invoice->date}</td>
                                                <td>{$invoice->invoice_end}</td>
                                                <td>{$invoice.customer.customer_name}</td>
                                                <td>{$invoice->subTotal}</td>
                                                <td align="center">{url  urltype="mbutton" text_value="{#browse#}" style="btn btn-default shiny"  url_string="bsc/P060/performaInvoice/details/0/{$smarty.session.lang}/{$invoice->id}"}</td>
                                                <td>{$invoice->user->full_name}</td>
                                                <td align="center">
                                                    {if $invoice->created_by eq {$smarty.session.user->id}}
                                                        <span>{url check=0 urltype="button" style="btn btn-success sharp mb-1" url_string="bsc/P060/performaInvoice/perfomainvoices/0/{$smarty.session.lang}/makeInvoice/{$invoice->id}/0" text_value="{#p_makeInvoice#}"}</span>
                                                        <br>
                                                        {url urltype="print" opr_code='performaInvoice' url_string="bsc/P060/performaInvoice/print/0/{$smarty.session.lang}/{$invoice->id}"}
                                                        {url check=1 urltype="edit" opr_code='performaInvoice' url_string="bsc/P060/performaInvoice/edit/0/{$smarty.session.lang}/{$invoice->id}"}
                                                        {url check=1 urltype="mdelete" opr_code='performaInvoice' url_string="bsc/P060/performaInvoice/confirm/0/{$smarty.session.lang}/{$invoice->id}"}
                                                    {/if}

                                                </td>
                                            </tr>

                                        {/foreach}
                                        </tbody>
                                    </table>


                                </div>
                            </div>














                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}
{block name=page-header}

{/block}
{block name=body}

{/block}

{block name=page_header}
    <!--Page Related Scripts-->
    <script src="https://cdn.datatables.net/1.10.16/js/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/1.5.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/1.5.1/js/buttons.print.min.js"></script>

    <script>

        $(function () {

//            // Deploy DataTable
            InitiateSearchableDataTable.init();

            // Remove the body section
            $('div.widget > div.widget-body.bordered-left.bordered-palegreen').parent().hide();

            // Set as full width screen -- small sidebar --
//            fullWidthScreen();
        });

    </script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}