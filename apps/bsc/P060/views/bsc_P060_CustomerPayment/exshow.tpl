{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=body}
    <div class="widget">
        <div class="tabbable">
            <ul class="nav nav-tabs nav-justified">
                <li class="">
                    <a aria-expanded="false" href="{url urltype="path" url_string="bsc/P060/CustomerPayment/show/0/{$smarty.session.lang}/menu"}" disabled="disabled">{#p_due_payment#}</a>
                </li>

                <li class="active">
                    <a aria-expanded="false" href="{url urltype="path" url_string="bsc/P060/CustomerPayment/exshow/0/{$smarty.session.lang}/menu" }">{#p_Expense_Reimbursement_Invoice#}</a>
                </li>

            </ul>

            <div class="tab-content p-2">
                <div class="tab-pane active" id="mainPane">
                    <div class="row">

                        <div class="col-lg-12">
                            <div class="widget {if !$smarty.session.searchArrayEx} collapsed {/if}">
                                <div class="widget-header bg-blue">
                                    <i class="widget-icon fa fa-arrow-left"></i>
                                    <span class="widget-caption">{#gnr_search#}</span>
                                    <div class="widget-buttons">
                                        <a href="#" data-toggle="collapse">
                                            <i class="fa fa-{if $smarty.session.searchArrayEx}minus{else}plus{/if}"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="widget-body">
                                    <form action="{url urltype=path url_string="bsc/P060/CustomerPayment/exshow/0/{$smarty.session.lang}/search"}"
                                          method="post">
                                        <div class="row">
                                            <div class="col-lg-6">

                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                    <div class="control-group">
                                                        <select name="customer_id" placeholder="{#gnr_select_from_list_bellow#}">
                                                            <option value=""></option>
                                                            {foreach $customers as $customer}
                                                                <option value="{$customer->id}" {if $smarty.session.searchArrayEx.customer_id eq $customer->id} selected {/if}>{$customer->customer_name}</option>
                                                            {/foreach}
                                                        </select>
                                                    </div>
                                                </div>

                                            </div>

                                            <div class="col-lg-6">
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#P_invoice#}</div>
                                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                    <div class="control-group">
                                                        <input type="text" class="form-control" name="invoice_id" placeholder="رقم الفاتورة" value="{$smarty.session.searchArrayEx.invoice_id}">
                                                    </div>
                                                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">
                                                        <div class="control-group">
                                                            <div class="radio">
                                                                <label>
                                                                    <input type="radio" name="invoice_payment_status" value="1" {if $smarty.session.searchArrayEx.invoice_payment_status eq 1}checked{/if}>
                                                                    <span class="text">الفواتير المسددة</span>
                                                                </label>
                                                            </div>
                                                            <div class="radio">
                                                                <label>
                                                                    <input type="radio" name="invoice_payment_status" value="0" {if $smarty.session.searchArrayEx.invoice_payment_status eq 0}checked{/if}>
                                                                    <span class="text">الفواتير الغير مسددة</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                            <div class="col-lg-6">
                                                <button type="submit" class="btn btn-default shiny">{#gnr_search#}</button>
                                            </div>
                                        </div>
                                    </form>

                                    <br>
                                </div>
                            </div>

                            {if $smarty.session.searchArrayEx}
                                <div class="well text-center">
                                    {url urltype=alink url_string="bsc/P060/CustomerPayment/exshow/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#}"}
                                </div>
                            {/if}

                        </div>

                        <div class="col-lg-12">
                            <table class="table table-bordered table-hover dataTable no-footer sortable-table">
                                <thead>
                                <tr>
                                    <th>#</th>
                                    <th class="text-right" width="20%">{#p_customer#}</th>
                                    <th class="text-right" width="20%">{#P_invoice#}</th>
                                    <th class="text-right" width="20%">{#p_date#}</th>
                                    <th class="text-right" width="10%">{#p_real_invoice#}</th>
                                    <th class="text-right" width="10%">{#p_paid_amount#}</th>
                                    <th class="text-right" width="15%">{#P_balance#}</th>
                                    <th>{#p_return_invoice#}</th>
                                    <th>{#p_payment_status#}</th>



                                    <th class="text-center from-control" width="20%">{#gnr_settings#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i=1}
                                {foreach $payments as $payment}
                                    <tr>
                                        <td align="center">{$i++}</td>
                                        <td >{$payment->customer->customer_name}</td>
                                        <td>{$payment->invoice.invoice_no}</td>
                                        <td>{$payment->invoice.date}</td>
                                        <td>{$payment->invoice.realinvoice.subTotal}</td>
                                        <td>{$payment->invoice.realinvoice.payment->first()->total_amount}</td>
                                        <td>{$payment->invoice.realinvoice.payment->first()->finial_balance} </td>
                                        <td>{$payment->total_amount}</td>
                                        <td>{$payment->finial_balance}</td>
                                        <td>
                                            {if $payment->finial_balance eq 0 }
                                                {*<buttom class="btn btn-success sharp" disabled>مكتمل السداد</buttom>*}
                                                <a class="btn btn-success sharp" data-toggle="modal" data-target="#modal" href="index.php?bsc/P054/finenteries/browse/0/ar/save_session/{$payment->entry_id}">{#p_related_entry#}</a>
                                            {else}
                                                {url check=0 text_value="سداد الفاتوره" urltype="mbutton" style="btn btn-default sharp" opr_code='CustomerPayment' url_string="bsc/P060/CustomerPayment/exadd/0/{$smarty.session.lang}/{$payment->id}"}
                                            {/if}
                                            {url urltype="print" check=0 url_string="bsc/P060/CustomerPayment/exPrint/0/{$smarty.session.lang}/{$payment.invoice.id}" text_value="{#gnr_print#}" style="btn btn-default sharp"}
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>

                        </div>

                    </div>


                </div>
            </div>
        </div>
    </div>
{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 10,
                        "oTableTools": {
                            "aButtons": [
//								"copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }
            };
        }();
        InitiateSimpleDataTable.init();
	</script>
{/block}
