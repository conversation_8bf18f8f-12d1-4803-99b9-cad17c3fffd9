{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=body}

    <div class="row">
        <div class="col-md-12">

            <div class="tabbable">
                <ul class="nav nav-tabs nav-justified">
                    <li class="active">
                        <a aria-expanded="false" href="{url urltype="path" url_string="bsc/P060/InvoicesReturnIn/show/0/{$smarty.session.lang}"}" disabled="disabled">{#p_directInvoice#}</a>
                    </li>

                    <li class="">
                        <a aria-expanded="false" href="{url urltype="path" url_string="bsc/P060/InvoicesReturnIn/returnInShow/0/{$smarty.session.lang}" }">{#p_ReturnInvoice#}</a>
                    </li>

                </ul>

                <div class="tab-content p-2">
                    <div class="tab-pane active" id="mainPane">
                        <div class="row">
                            <div class="widget {if !$smarty.session.filterParams} collapsed {/if}">
                                <div class="widget-header bg-blue">
                                    <i class="widget-icon fa fa-arrow-left"></i>
                                    <span class="widget-caption">{#gnr_search#}</span>
                                    <div class="widget-buttons">
                                        <a href="#" data-toggle="collapse">
                                            <i class="fa fa-{if $smarty.session.filterParams}minus{else}plus{/if}"></i>
                                        </a>
                                    </div><!--Widget Buttons-->
                                </div><!--Widget Header-->
                                <div class="widget-body">

                                    <form method="post"
                                          action='{url urltype="path" url_string="bsc/P060/InvoicesReturnIn/show/0/{$smarty.session.lang}/filter"}'>

                                        <div class="row ml-2">
                                            <div class="col-lg-1 col-md-1 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                                            <div class="col-lg-5 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                                <select name="customer_id" placeholder="{#gnr_select_from_list_bellow#}">
                                                    <option></option>
													{foreach $customers as $customer}
                                                        <option value="{$customer->id}" {if $smarty.session.filterParams.customer_id eq $customer->id} selected {/if}>{$customer->customer_name}</option>
													{/foreach}
                                                </select>
                                            </div>


                                            <div class="col-lg-1 col-md-1 col-sm-12 col-xs-12 snsolabel">{#P_invoice#}</div>
                                            <div class="col-lg-5 col-md-5 col-sm-12 col-xs-12 snsoinput ">
                                                <div class="control-group">
                                                    <input type="text" class="form-control p-2" name="invoice_id" placeholder="رقم الفاتورة" value="{$smarty.session.filterParams.invoice_id}">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mt-2">

                                            <div class="col-lg-1 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_from_date#}</div>
                                            <div class="col-lg-5 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=datefrom type=edit row=$smarty.session.filterParams required=true}</div>

                                            <div class="col-lg-1 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_to_date#}</div>
                                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=dateto type=edit row=$smarty.session.filterParams required=true}</div>

                                        </div>
                                        <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>
                                        {if $smarty.session.filterParams}
                                            {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P060/InvoicesReturnIn/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
                                        {/if}
                                    </form>
                                </div>
                            </div>
                            <div class="row snsowraper">

                                <div class="table-responsive" data-pattern="priority-columns">
                                    <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer">
                                        <thead>
                                        <tr>
                                            <th width="5%">#</th>
                                            <th width="10%">{#p_std_invoice_no#}</th>
                                            <th width="10%">{#p_std_stock#}</th>
                                            <th width="10%">{#p_pay#}</th>
                                            <th width="10%">{#p_std_date#}</th>
                                            <th width="10%">{#p_customer#}</th>
                                            <th width="10%">{#p_total_invoice_no#}</th>
                                            <th width="10%">{#p_std_details#}</th>
                                            <th width="30%">{#gnr_settings#}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
										{$i=1}
										{foreach $invoices as $invoice}
											{if $invoice->status eq 1}
                                                <tr>
                                                    <td align="center">{$i++}</td>
                                                    <td>{$invoice->invoice_no}</td>
                                                    <td>{if {$invoice->stock->name}}{$invoice->stock->name} {else}-- {/if}</td>
                                                    <td>{if $invoice->ispayed eq 0 } لم يتم السداد{else} تم السداد{/if}</td>
                                                    <td>{$invoice->date}</td>
                                                    <td>{$invoice.customer.customer_name}</td>
                                                    <td>{$invoice->subTotal}</td>
                                                    <td align="center">
														{url  urltype="mbutton" text_value="{#browse#}" style="btn btn-default shiny"  url_string="bsc/P060/InvoicesReturnIn/details/0/{$smarty.session.lang}/{$invoice->id}"}</td>
                                                    <td align="center">
                                                        {url urltype="print" opr_code='Invoices' url_string="bsc/P060/Invoices/print/0/{$smarty.session.lang}/{$invoice->id}"}
                                                        {if  $invoice->hasRInvoice eq 0 and can('edit')}
														{url  urltype="button" text_value="{#P_returnIn#}"  opr_code='InvoicesReturnIn' url_string="bsc/P060/InvoicesReturnIn/edit/0/{$smarty.session.lang}/{$invoice->id}"}
                                                        {/if}
                                                    </td>
                                                </tr>
											{/if}
										{/foreach}
                                        </tbody>
                                    </table>


                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




{/block}
{block name=page_header}
{/block}