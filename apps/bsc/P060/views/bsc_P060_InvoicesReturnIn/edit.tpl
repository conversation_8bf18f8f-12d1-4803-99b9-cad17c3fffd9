{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}

{block name=page_body}
    <return-in
            stocks='{$stocks}'
            products='{$products}'
            sales-Generate-Invoices='{$Sales_generate_invoices}'
            index="{$Sales_generate_invoices.id}"
            stockType="{$stockType}"
            inline-template>

        <div>
            <form method="post"
                  action='{url check=1 urltype="path" url_string="bsc/P060/InvoicesReturnIn/confirm/0/{$smarty.session.lang}/{$Sales_generate_invoices.id}/{$smarty.session.s_InvoicesReturnIn_token}"}'>
                {*bsc/P060/InvoicesReturnIn/show/0/{$smarty.session.lang}/update//{$smarty.session.s_InvoicesReturnIn_token}"}*}
                <div class="widget-body">
                    <div class="row">
                        <div class="col-lg-9">
                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel mt-2">{#p_std_invoice_no#}</div>
                            <div class="col-lg-8 col-md-9 col-sm-12 col-xs-12 snsoinput m-1" >
                                <h4><span>{$Sales_generate_invoices.invoice_no}</span></h4>
                                <input type="hidden" name="invoice_no" value="{$Sales_generate_invoices.invoice_no}">
                            </div>


                            <div v-if="is_product_invoice" class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_std_stock#}</div>
                            <div v-if="is_product_invoice" class="col-lg-8 col-md-9 col-sm-12 col-xs-12 snsoinput m-1">
                                <selectize name="stock_id"  v-model="id" class="form-control" required data-live-search="true">
                                    <option value="">{#gnr_select_from_list_bellow#}</option>
                                    <option v-for="stock in stocksData" :key="stock.id" :value="stock.id"
                                            v-text="stock.name"></option>
                                </selectize>
                                <p class="snsolable" :value="text"></p>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_payment_method#}</div>
                            <div class="col-lg-8 col-md-9 col-sm-12 col-xs-12 snsoinput m-1">
                                <select name="paymentType" placeholder="{#gnr_select_from_list_bellow#}" data-live-search="true" class="form-control" required {if $Sales_generate_invoices.ispayed eq 0 }disabled{/if}>
                                    <option></option>
                                    <option value="1111" > {#p_cash#} </option>
                                    <option value="2111"> {#p_bank#}</option>
                                    <option value="41111" selected> {#p_postment#} </option>

                                </select>
                                {if $Sales_generate_invoices.ispayed eq 0 }
                                    <span class="text-danger">لا يمكن تغيير حالة السداد</span>
                                    <input type="hidden" name="paymentType" value="41111">
                                {/if}
                                <p class="snsolable" :value="text"></p>
                            </div>


                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                            <div class="col-lg-8 col-md-9 col-sm-12 col-xs-12 snsoinput m-1">
                                <input type="hidden" name="customer_id" value="{$Sales_generate_invoices.customer_id}">
                                <input type="text" class="form-control p-2"  value="{$Sales_generate_invoices.customer.customer_name}" disabled>
                                {*<select name="customer_id" placeholder="{#gnr_select_from_list_bellow#}"*}
                                        {*class="form-control">*}

                                    {*<option></option>*}
                                    {*{foreach $customers as $customer}*}
                                        {*<option value="{$customer->id}" {if $Sales_generate_invoices.customer_id eq $customer->id} selected {/if}>{$customer->customer_name}</option>*}
                                    {*{/foreach}*}
                                {*</select>*}
                                {*<p class="snsolable" :value="text"></p>*}
                            </div>

                        </div>

                        <div class="col-lg-3">
                            <img style="border: 1px #333 dotted;" src="/framework/core/functions/image.php?image={$baseUrl}/client/documents/qrcodes/{$qr_path}.png"
                                 alt="qr-code" class="col-lg-12">
                        </div>

                    </div>
                    <div class="row mt-1">
                        <div class="col-lg-12 pl-2">
                            <div class="col-lg-2">
                                <div class="snsolabel">{#p_std_comment#}</div>
                            </div>
                            <div class="col-lg-10">
                                            <textarea name="note" class="form-control mr-2"
                                                      placeholder="{#p_std_palce_holder_comment#}">{$Sales_generate_invoices.note}</textarea>
                            </div>
                        </div>
                    </div>

                </div>


                <div class="row mt-2">
                    <div class="col-lg-12">
                        <div class="widget-body">
                        <table class="table table-bordered table-hover">
                            <thead>
                            <tr>
                                <th width="2%">#</th>
                                <th width="10%">{#p_std_product#}</th>
                                <th width="5%">{#p_std_unit#}</th>
                                <th width="5%">{#p_std_total_qty#}</th>
                                <th width="5%">{#p_std_qty#}</th>
                                <th width="10%">{#p_sale_price#}</th>
                                {*<th width="5%">{#p_std_current_qty#}</th>*}

                                <th width="5%">{#gnr_status#}</th>
                                <th width="10%">{#p_discount#}</th>
                                <th width="10%">{#p_value_of_discount#}</th>
                                <th width="10%">{#p_sale_taxi#}</th>
                                <th width="10%">{#p_value_of_taxi#}</th>
                                <th width="10%">{#p_total_mount#}</th>


                            </tr>
                            </thead>
                            <tbody>

                            <tr
                                    is="return-unit"
                                    v-for="(stock , index ) in productsData"
                                    :key="index"
                                    :index="index"
                                    :stock="stock"
                                    :products-unit="productsUnit"
                                    :id="id"
                                    @remove="removeProduct"
                                    @changed="changed"
                            >

                            </tr>


                            </tbody>


                        </table>
                        </div>
                        <div class="row mt-2">
                            <div class=" row widget-body ml-1 mr-1">
                            <div class="col-12">
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_actual_amount#}</div>
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                    <input type="text" name="subProductTotal" v-model="subProductTotal" readonly>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_total_tax_amount#}</div>
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                    <input type="text" name="sumTotaltax" v-model="sumTotaltax" readonly>
                                </div>

                            </div>
                            <div class="col-12">
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_total_discount_amount#}</div>
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                    <input type="text" name="sumTotalDiscount" v-model="sumTotalDiscount" readonly>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_actual_pay#}</div>
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                    <input type="text" name="subTotal" v-model="subTotal" readonly>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_actual_pay_amount_without_discount#}</div>
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                    <input type="text" name="subTotal_without_discount"
                                           v-model="subTotal_without_discount" readonly>
                                </div>


                            </div>
                            </div>

                        </div>


                        <div class="snsoinput">
                            <button type="submit" class="btn btn-success text-align-center sharp col-lg-2 mt-3 " :disabled="disabled"
                                    :title="disabled">{#gnr_confirm#}</button>
                        </div>

                    </div>
                </div>
            </form>
        </div>
    </return-in>
{/block}
{block name=back}{url urltype="path" url_string="bsc/P060/InvoicesReturnIn/show/0/{$smarty.session.lang}"}{/block}
