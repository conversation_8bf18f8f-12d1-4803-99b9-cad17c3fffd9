{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}

{block name=page_body}


        <div>
            <form method="post"
                  action='{url check=1 urltype="path" url_string="bsc/P060/InvoicesReturnIn/show/0/{$smarty.session.lang}/update/{$id}/{$smarty.session.s_InvoicesReturnIn_token}"}'>
                <div class="widget-body">
                    <div class="row">
                        <div class="col-lg-9">
                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel mt-2">{#p_std_invoice_no#}</div>
                            <div class="col-lg-8 col-md-9 col-sm-12 col-xs-12 snsoinput m-1" >
                                <h4><span>{$smarty.session.filterParams.invoice_no}</span></h4>
                                <input type="hidden" name="invoice_no" value="{$smarty.session.filterParams.invoice_no}">
                            </div>


                            {*<div v-if="is_product_invoice" class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_std_stock#}</div>*}
                            {*<div v-if="is_product_invoice" class="col-lg-8 col-md-9 col-sm-12 col-xs-12 snsoinput m-1">*}
                                {*<selectize name="stock_id"  v-model="id" class="form-control" required data-live-search="true">*}
                                    {*<option value="">{#gnr_select_from_list_bellow#}</option>*}
                                    {*<option v-for="stock in stocksData" :key="stock.id" :value="stock.id"*}
                                            {*v-text="stock.name"></option>*}
                                {*</selectize>*}
                                {*<p class="snsolable" :value="text"></p>*}
                            {*</div>*}
                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_payment_method#}</div>
                            <div class="col-lg-8 col-md-9 col-sm-12 col-xs-12 snsoinput m-1">
                                <select name="paymentType" placeholder="{#gnr_select_from_list_bellow#}" data-live-search="true" class="form-control" required {if $smarty.session.filterParams.paymentType neq 0 }disabled{/if}>
                                    <option></option>
                                    <option value="1111" {if $smarty.session.filterParams.paymentType eq 1111} selected {/if}> {#p_cash#} </option>
                                    <option value="2111"{if $smarty.session.filterParams.paymentType eq 2111} selected {/if}> {#p_bank#}</option>
                                    <option value="41111" {if $smarty.session.filterParams.paymentType eq 41111} selected {/if}> {#p_postment#} </option>

                                </select>
                                {if $smarty.session.filterParams.paymentType neq 0 }
                                    <span class="text-danger">لا يمكن تغيير حالة السداد</span>
                                    <input type="hidden" name="paymentType" value="{$smarty.session.filterParams.paymentType}">

                                {/if}
                                {*<input type="hidden" name="ispayed" value="{$smarty.session.filterParams.ispayed}">*}

                                <p class="snsolable" :value="text"></p>
                            </div>


                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                            <div class="col-lg-8 col-md-9 col-sm-12 col-xs-12 snsoinput m-1">
                                <input type="hidden" name="customer_id" value="{$smarty.session.filterParams.customer_id}">
                                <input type="text" class="form-control p-2"  value="{getname table=sales_customers id=$smarty.session.filterParams.customer_id}" disabled>
                                {*<select name="customer_id" placeholder="{#gnr_select_from_list_bellow#}"*}
                                {*class="form-control">*}

                                {*<option></option>*}
                                {*{foreach $customers as $customer}*}
                                {*<option value="{$customer->id}" {if $Sales_generate_invoices.customer_id eq $customer->id} selected {/if}>{$customer->customer_name}</option>*}
                                {*{/foreach}*}
                                {*</select>*}
                                {*<p class="snsolable" :value="text"></p>*}
                            </div>

                        </div>

                        <div class="col-lg-3">
                            <img style="border: 1px #333 dotted;" src="/framework/core/functions/image.php?image={$baseUrl}/client/documents/qrcodes/{$qr_path}.png"
                                 alt="qr-code" class="col-lg-12">
                        </div>

                    </div>
                    <div class="row mt-1">
                        <div class="col-lg-12 pl-2">
                            <div class="col-lg-2">
                                <div class="snsolabel">{#p_std_comment#}</div>
                            </div>
                            <div class="col-lg-10">
                                            <textarea name="note" class="form-control mr-2"
                                                      placeholder="{#p_std_palce_holder_comment#}">{$smarty.session.filterParams.note}</textarea>
                            </div>
                        </div>
                    </div>

                </div>


                <div class="row mt-2">
                    <div class="col-lg-12">
                        <div class="widget-body">
                            <div class="text-danger text-center"> <b>تأكد ان عدد او كمية الاصناف صحيحة</b> </div>
                            <table class="table table-bordered table-hover mt-2">
                                <thead>
                                <tr>
                                    <th width="2%">#</th>
                                    <th width="10%">{#p_std_product#}</th>
                                    <th width="5%">{#p_std_unit#}</th>
                                    {*<th width="5%">{#p_std_total_qty#}</th>*}
                                    <th width="5%">{#p_std_qty#}</th>
                                    <th width="10%">{#p_sale_price#}</th>
                                    {*<th width="5%">{#p_std_current_qty#}</th>*}

                                    {*<th width="5%">{#gnr_status#}</th>*}
                                    <th width="10%">{#p_discount#}</th>
                                    <th width="10%">{#p_value_of_discount#}</th>
                                    <th width="10%">{#p_sale_taxi#}</th>
                                    <th width="10%">{#p_value_of_taxi#}</th>
                                    <th width="10%">{#p_total_mount#}</th>


                                </tr>
                                </thead>
                                <tbody>


                                <div class="hidden">{$i=1}</div>
                                     {foreach $smarty.session.filterParams.data as $index=>$row}
                                         <tr>

                                             {if $row.id neq 0}
                                                 <td>{$i}</td>

                                                 <td>{getname table=warehouse_product id=$row.product_id}</td>
                                                 <input type="hidden" name="data[{$index}][product_id]" value="{$row.product_id}">
                                                 <input type="hidden" name="data[{$index}][product_id]" value="{$row.product_id}">
                                                 <input type="hidden" value="{$row.product_unit_id}" name="data[{$index}][product_unit_id]">
                                                 <input type="hidden" value="{$row.qty}" name="data[{$index}][qty]">
                                                 <input type="hidden" value="{$row.sale_price}" name="data[{$index}][sale_price]">
                                                 <input type="hidden" value="{$row.discount}" name="data[{$index}][discount]">
                                                 <input type="hidden" value="{$row.total_discount}" name="data[{$index}][total_discount]">
                                                 <input type="hidden" value="{$row.tax}" name="data[{$index}][tax]">
                                                 <input type="hidden" value="{$row.total_tax}" name="data[{$index}][total_tax]">
                                                 <input type="hidden" value="{$row.product_total}" name="data[{$index}][product_total]">
                                                 <td>{getname table=warehouse_stockunit id=$row.product_unit_id}</td>
                                                 <td>{$row.qty}</td>
                                                 <td>{$row.sale_price}</td>
                                                 <td>{$row.discount}</td>
                                                 <td>{$row.total_discount}</td>
                                                 <td>{$row.tax}</td>
                                                 <td>{$row.total_tax}</td>
                                                 <td>{$row.product_total}</td>
                                                 {assign var=i value=$i+1}
                                             {/if}
                                         </tr>
                                     {/foreach}




                                </tbody>


                            </table>
                        </div>
                        <div class="row mt-2">
                            <div class=" row widget-body ml-1 mr-1">
                                <div class="col-12">
                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_actual_amount#}</div>
                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                        <input type="text" name="subProductTotal" value="{$smarty.session.filterParams.subProductTotal}" readonly>
                                    </div>
                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_total_tax_amount#}</div>
                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                        <input type="text" name="sumTotaltax" value="{$smarty.session.filterParams.sumTotaltax}" readonly>
                                    </div>

                                </div>
                                <div class="col-12">
                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_total_discount_amount#}</div>
                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                        <input type="text" name="sumTotalDiscount" value="{$smarty.session.filterParams.sumTotalDiscount}" readonly>
                                    </div>
                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_actual_pay#}</div>
                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                        <input type="text" name="subTotal" value="{$smarty.session.filterParams.subTotal}" readonly>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_actual_pay_amount_without_discount#}</div>
                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                                        <input type="text" name="subTotal_without_discount"
                                               value="{$smarty.session.filterParams.subTotal_without_discount}" readonly>
                                    </div>


                                </div>
                            </div>

                        </div>


                        <div class="snsoinput mt-2">
                            <button type="submit" class="btn btn-small btn-success text-align-center sharp px-3"
                                    >{#gnr_save#}</button>
                            {url urltype="button" url_string="bsc/P060/InvoicesReturnIn/edit/0/{$smarty.session.lang}/{$id}" style="my--1 mx-2 px-3 btn btn-small btn-danger" text_value="{#gnr_back#}"}
                        </div>



                    </div>
                </div>
            </form>
        </div>
{/block}
{block name=back}{url urltype="path" url_string="bsc/P060/InvoicesReturnIn/edit/0/{$smarty.session.lang}/{$id}"}{/block}
