{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add#} {#p_pro_product_unit#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url check=1 urltype="path" url_string="bsc/P060/ServiceProducts/setting/0/{$smarty.session.lang}/{$product->id}/insert/{$smarty.session.s_product_units_token}"}'>
                <div class="row snsowraper">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_parent_unit#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input value="{$last_unit->unit->name}" class="form-control" readonly></div>
                        <input type="hidden" name="parent_id" value="{$last_unit->id}">

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_unit#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="stock_unit_id" class="form-control" required
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $units as $unit}
                                    <option value="{$unit->id}">{$unit->name}</option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_qty#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" id="number"  name="number" class="form-control" placeholder="{#p_pro_palce_holder_qty#}"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_purch_price#}</div>
                        {if $last_unit}
                            <input type="hidden" id="found"  value="">
                            <input type="hidden" id="o_purchase_price"  value="{$last_unit->purchase_price}">
                            <input type="hidden" id="o_m_purchase_price"  value="{$last_unit->m_purchase_price}">
                            <input type="hidden" id="o_sale_price"  value="{$last_unit->sale_price}">
                        {/if}

                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="purchase_price" id="v_purchase_price" {if $last_unit} readonly  {/if} value="" class="form-control" placeholder="{#p_pro_palce_holder_pur_price#}"></div>


                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_m_purch_price#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" id="v_m_purchase_price" {if $last_unit} readonly  {/if} value="" name="m_purchase_price" class="form-control" placeholder="{#p_pro_palce_holder_m_pur_price#}"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_sale_price#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" id="v_sale_price" {if $last_unit} readonly {/if} value="" name="sale_price" class="form-control" placeholder="{#p_pro_palce_holder_sale_price#}"></div>


                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp" >{#gnr_add#}</button></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
    <script>
        //////////////// Here The Process of calculation the price according to the size of unit /////

        $(document).ready(function() {
            $("#number").change(function() {
                if ($('#found').length)
                {
                    var number =  $("#number").val();             ///// get unit size //////
                    var o_purchase = $("#o_purchase_price").val();   ///// get purchase price //////
                    var o_m_purchase = $("#o_m_purchase_price").val();  ///// get middle of purchase price //////
                    var o_sale = $("#o_sale_price").val();      ///// get sales price //////

                    $("#v_purchase_price").val((o_purchase/number).toFixed(2));  ///// calculate the purchase price and assign to id //////
                    $("#v_m_purchase_price").val((o_m_purchase/number).toFixed(2)); ///// calculate the middle purchase price and assign to id //////
                    $("#v_sale_price").val((o_sale/number).toFixed(2));                ///// calculate the sale  price and assign to id //////
                }


            });
        })
    </script>
{/block}

 {block name=back}{url urltype="path" url_string="bsc/P057/Product/show/0/{$smarty.session.lang}"}{/block}

