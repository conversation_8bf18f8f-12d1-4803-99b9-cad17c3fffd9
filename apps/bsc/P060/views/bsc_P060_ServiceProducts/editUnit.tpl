{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_edit#} {#p_pro_product#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url check=1 urltype="path" url_string="bsc/P060/ServiceProducts/setting/0/{$smarty.session.lang}/{$productUnit->id}/update/{$smarty.session.s_product_units_token}"}'>
                <div class="row snsowraper">
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_parent_unit#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input value="{$last_unit->unit->name}" class="form-control" readonly></div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_unit#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input value="{$productUnit->unit->name}" class="form-control" readonly></div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_qty#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="number" id="number" class="form-control" value="{$productUnit->number}"></div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_purch_price#}</div>
                    {if $last_unit and $has_other_unit}
                        <input type="hidden" id="found"  value="{$last_unit->number}">
                        <input type="hidden" id="o_purchase_price"  value="{$last_unit->purchase_price}">
                        <input type="hidden" id="o_m_purchase_price"  value="{$last_unit->m_purchase_price}">
                        <input type="hidden" id="o_sale_price"  value="{$last_unit->sale_price}">

                    {/if}
                    {if not $has_other_unit}
                        <input type="hidden" name="stock_unit_id"  value="{$productUnit->stock_unit_id}">
                    {/if}
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="purchase_price" id="v_purchase_price" {if $last_unit and $has_other_unit} readonly  {/if} class="form-control" value="{$productUnit->purchase_price}"></div>


                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_m_purch_price#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="m_purchase_price" id="v_m_purchase_price"{if $last_unit and $has_other_unit} readonly  {/if} class="form-control" value="{$productUnit->m_purchase_price}"></div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_sale_price#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="sale_price" id="sale_price" {if $last_unit and $has_other_unit} readonly  {/if} class="form-control" value="{$productUnit->sale_price}"></div>



                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>

    <script>
        $(document).ready(function() {
            $("#number").change(function() {
                if ($('#found').length)
                {
                    var total =  $("#found").val();
                    var number =  $("#number").val();
                    var o_purchase = $("#o_purchase_price").val();
                    var o_m_purchase = $("#o_m_purchase_price").val();
                    var o_sale = $("#o_sale_price").val();
                    $("#v_purchase_price").val(((o_purchase)/number).toFixed(2));
                    $("#v_m_purchase_price").val(((o_m_purchase)/number).toFixed(2));
                    $("#v_sale_price").val(((o_sale)/number).toFixed(2));
                }


            });
        })
    </script>

{/block}

