{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add#} {#p_customer#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form method="post"
                  action='{url check=1 urltype="path" url_string="bsc/P060/Customers/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_customer_token}"}'>
                <div class="row snsowraper">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                            name="customer_name"
                                                                                            class="form-control"
                                                                                            required
                                                                                            placeholder="{#p_cusomer_palce_holder_name#}">
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_address#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                            name="customer_address"
                                                                                            class="form-control"
                                                                                            required
                                                                                            placeholder="{#p_customer_palce_holder_address#}">
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_phone#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                            name="customer_phone"
                                                                                            class="form-control"
                                                                                            required
                                                                                            placeholder="{#p_customer_palce_holder_phone#}">
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_email#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="email"
                                                                                            name="customer_email"
                                                                                            class="form-control"
                                                                                            placeholder="{#p_customer_palce_holder_email#}">
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_responsible_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <input type="text" name="responsibly_persian_name" class="form-control"
                                   placeholder="{#p_customer_palce_holder_responsible_name#}">
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_responsible_phone#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <input type="text" name="responsibly_persian_phone" class="form-control"
                                   placeholder="{#p_customer_palce_holder_responsible_phone#}">
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_vat_tax#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <input type="text" name="Vat_tax" class="form-control"
                                   placeholder="{#p_customer_palce_holder_responsible_vat_tax#}">
                        </div>


                        {*<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_account#}</div>*}
                        {*<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">*}
                            {*<select name="account_id" class="form-control"*}
                                    {*data-live-search="true" required>*}
                                {*<option value="">{#gnr_select_from_list_bellow#}</option>*}
                                {*{foreach $accounts as $account}*}
                                    {*<option value="{$account.id}">{$account.name}-{$account.code}</option>*}
                                {*{/foreach}*}
                            {*</select>*}
                        {*</div>*}
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_status#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            {foreach $activationStatus as $key => $status}
                                <div class="radio">
                                    <label>
                                        <input name="status" value="{$status->id}"
                                               id="{$status->id}" {if $key eq 0} checked {/if} type="radio" required>
                                        <span class="text">{$status->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <button  id="submit" type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
    <script src="/templates/assets/js/jquery.validate.min.js"></script>

    <script>
        jQuery.validator.addMethod("lettersonly", function(value, element) {
            return this.optional(element) || /^[a-z\u0600-\u06FF\u0750-\u077F\s]+$/i.test(value);
        }, "Letters only please");
        $(document).ready(function(){

            // Code for the Validator
            var $validator = $('.modal-body form').validate({
                rules: {
                    customer_name: {
                        lettersonly: true,
                        required: true,
                    },
                    customer_address: {
                        lettersonly: true,
                        required: true,

                    },
                    customer_phone: {
                        required: true,
                        maxlength: 12,
                        minlength: 12,
                        digits: true
                    },
                    responsibly_persian_name: {
                        lettersonly: true,
                        required: true,
                    },
                    responsibly_persian_phone: {
                        maxlength: 12,
                        minlength: 12,
                        required: true,
                        digits: true
                    },
                    Vat_tax: {
                        maxlength: 15,
                        minlength: 15,
                        required: true,
                        digits: true
                    },
                    {*account_id:{*}
                        {*required: true,*}
                    {*},*}
                    customer_email:{
                        required: true,
                    }
                },

                messages: {
                    Vat_tax: {
                        required: "{#p_validation_filed_required#}",
                        maxlength: $.validator.format( "الحد الأقصى لعدد الحروف هو 15" ),
                        minlength: $.validator.format( "الحد الأدنى لعدد الحروف هو 15" ),
                    },
                },

            });

            $('input').on('blur', function() {
                if ($(".modal-body form").valid()) {
                    $('#submit').prop('disabled', false);
                } else {
                    $('#submit').prop('disabled', 'disabled');
                }
            });

        });
    </script>
    <script>
        $.extend( $.validator.messages, {
            required: "{#p_validation_filed_required#}",
            email: "{#p_validation_field_must_be_email#}",
            date: "رجاء إدخال تاريخ صحيح",
            number: "رجاء إدخال عدد بطريقة صحيحة",
            maxlength: $.validator.format( "الحد الأقصى لعدد الحروف هو {}" ),
            minlength: $.validator.format( "الحد الأدنى لعدد الحروف هو 12" ),
            max: $.validator.format( "رجاء إدخال عدد أقل من أو يساوي 16" ),
            min: $.validator.format( "رجاء إدخال عدد أكبر من أو يساوي 8}" ),
            digits: $.validator.format( "رجاء إدخال عدد أكبر من أو يساوي 8}" ),
        } );

    </script>
{/block}

