{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add#} {#p_stok_stock#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url check=1 urltype="path" url_string="bsc/P060/setting/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_sale_account_setting_token}"}'>
                <div class="row snsowraper">
                    <h5 class="modal-title mr-1">{#gnr_add#} {#p_accountSale#}</h5>
                    <br>
                    <div class="col-lg-12 well">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_accountSale#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_sale_id" class="form-control" required
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $accounts as $account}
                                    <option value="{$account.id}">{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>

                    </div>
                    <h5 class="modal-title mr-1">{#gnr_add#} {#p_accountBank#}</h5>
                    <br>
                    <div class="col-lg-12 well">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_accountBank#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_bank_id" class="form-control"
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $accounts as $account}
                                    <option value="{$account.id}">{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>


                    <h5 class="modal-title mr-1">{#gnr_add#} {#p_accountCash#}</h5>
                    <br>
                    <div class="col-lg-12 well">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_accountCash#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_cash_id" class="form-control" required
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $accounts as $account}
                                    <option value="{$account.id}">{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>

                    <h5 class="modal-title mr-1">{#gnr_add#} {#p_accountCustomer#}</h5>
                    <br>
                    <div class="col-lg-12 well">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_accountCustomer#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_customer_id" class="form-control"
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $accounts as $account}
                                    <option value="{$account.id}">{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>





                    <h5 class="modal-title mr-1">{#gnr_add#} {#p_accountTax#}</h5>
                    <br>
                    <div class="col-lg-12 well">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_accountTax#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_tax_id" class="form-control" required
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
				                {foreach $accounts as $account}
                                    <option value="{$account.id}">{$account.name}-{$account.code}</option>
				                {/foreach}
                            </select>
                        </div>
                    </div>


                    <h5 class="modal-title mr-1">{#gnr_add#} {#p_accountDiscount#}</h5>
                    <br>
                    <div class="col-lg-12 well">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_accountDiscount#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_discount_id" class="form-control" required
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
				                {foreach $accounts as $account}
                                    <option value="{$account.id}">{$account.name}-{$account.code}</option>
				                {/foreach}
                            </select>
                        </div>
                    </div>




                    <h5 class="modal-title mr-1">{#gnr_add#} {#p_accountReturn#}</h5>
                    <br>
                    <div class="col-lg-12 well">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_accountReturn#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_return_id" class="form-control" required
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $accounts as $account}
                                    <option value="{$account.id}">{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>


                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12"><button type="submit" class="btn btn-success sharp" >{#gnr_add#}</button></div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}

