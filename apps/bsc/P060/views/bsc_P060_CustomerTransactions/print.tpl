{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=title}

{/block}

{block name=body}
    <div class="text-center">
        <h2>
            {#p_customer_movement_trans#}
        </h2>
    </div>
    {if $smarty.session.searchArray.customer_id}
        {#p_customer#}:
        {getname table=sales_customers id=$smarty.session.searchArray.customer_id}
    {/if}
    <br>
    {if $smarty.session.searchArray.TransactionsType}
        {#p_type#}:
        {if $smarty.session.searchArray.TransactionsType eq 7777}
            {#p_sales_invoice#}
        {/if}
        {if $smarty.session.searchArray.TransactionsType eq 7778}
            {#p_return_sales_invoice#}
        {/if}
        {if $smarty.session.searchArray.TransactionsType eq 7779}
            {#p_invoice_pay#}
        {/if}
        {if $smarty.session.searchArray.TransactionsType eq 7781}
            {#p_return_invoice_pay#}
        {/if}
        {if $smarty.session.searchArray.TransactionsType eq 7782}
            {#p_return_invoice_outlay#}
        {/if}
    {/if}
    <div class="text-center">
        {if $smarty.session.searchArray.datefrom}
            {#p_from_date#}:{$smarty.session.searchArray.datefrom}
        {/if}
        {if $smarty.session.searchArray.dateto}
            {#p_to_date#}:{$smarty.session.searchArray.dateto}
        {/if}
    </div>
    <hr>
    <div class="table-responsive" data-pattern="priority-columns">
        <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer">
            <thead>
            <tr>
                <th width="3%">#</th>
                <th width="20%">{#p_customer#}</th>
                <th width="10%">{#p_date#}</th>
                <th width="20%">{#p_std_invoice_no#}</th>
                <th width="10%">{#p_debit#}</th>
                <th width="10%">{#p_credit#}</th>
                <th width="20%">{#p_type#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $transaction as $row}
                <tr>
                    <td>{$i++}</td>
                    <td width="20%">{$row.customer.customer_name}</td>
                    <td width="20%">{$row.created_at|date_format:"%G-%m-%d"}</td>
                    <td align="center">{if $row->type eq 7777 or  $row->type eq 7778 }{$row.invoice_no}{else } ---- {/if}</td>
                    <td align="center">{$row.debit|number_format:2}</td>
                    <td align="center">{$row.credit|number_format:2}</td>
                    <td>{$row->typeName}</td>
                </tr>

            {/foreach}
            <tr style="background: #8cc474; color:white;">
                <td colspan="4"><b>{#p_total#}</b></td>
                <td>{#p_debit#}</td>
                <td>{#p_credit#}</td>
                <td>{#p_difference#}</td>

            </tr>
            </tbody>
        </table>
    </div>
    <hr>
    {*{render view="/reports/signatures" signatures=$signatures}*}
    <div class="row snsowraper">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            {#gnr_printed_by_user#}: {$smarty.session.user->full_name}<br>
            {#gnr_on_date#}: {$smarty.now|date_format:"%d/%m/%Y"}
        </div>
    </div>
{/block}
