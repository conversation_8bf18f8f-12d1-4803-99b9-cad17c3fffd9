{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />
{/block}
{block name=body}
    <div class="widget">
        <div class="widget-header bg-blue">
            <i class="widget-icon fa fa-arrow-left"></i>
            <span class="widget-caption">{#gnr_search#}</span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                </a>
            </div>
        </div>
        <div class="widget-body">

            <form method="post"
                  action='{url urltype="path" url_string="bsc/P060/CustomerTransactions/show/0/{$smarty.session.lang}/search"}'>

                <div class="row">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_type#}</div>
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                        <select name="TransactionsType" placeholder="{#gnr_select_from_list_bellow#}">
                            <option></option>
                            <option value="7777" {if $smarty.session.searchArray.TransactionsType eq 7777} selected {/if}> {#p_invoice#} </option>
                            <option value="7778" {if $smarty.session.searchArray.TransactionsType eq 7778} selected {/if}> {#p_return_invoice#}</option>
                            <option value="7779" {if $smarty.session.searchArray.TransactionsType eq 7779} selected {/if}> {#p_customer_payment#} </option>
                            <option value="7782" {if $smarty.session.searchArray.TransactionsType eq 7782} selected {/if}> {#p_export_reimburse#} </option>

                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                        <select name="customer_id" placeholder="{#gnr_select_from_list_bellow#}">
                            <option></option>
                            {foreach $customers as $customer}
                                <option value="{$customer->id}" {if $smarty.session.searchArray.customer_id eq $customer->id} selected {/if}>{$customer->customer_name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>

                <div class="row">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_from_date#}</div>
                    <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=datefrom type=edit row=$smarty.session.searchArray required=true}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_to_date#}</div>
                    <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=dateto type=edit row=$smarty.session.searchArray required=true}</div>

                </div>
                <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>
                {if $smarty.session.searchArray}
                    {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P060/CustomerTransactions/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
                {/if}
            </form>
        </div>
    </div>
    {if $transaction}
    <div class="row snsowraper">
        {if count($transaction) neq 0}
            <a style="height: 30px;margin-bottom: 5px" target="_blank" href="{url check=0 urltype="path" url_string="bsc/P060/CustomerTransactions/print/0/{$smarty.session.lang}"}" class="btn btn-default btn-sm"><i class='fa fa-print fa-fw fa-lg'></i>{#gnr_print#}</a>
        {else}
            <div class="alert alert-warning fade in"><i class="fa-fw fa fa-warning"></i>{#p_no_data_to_print#}</div>
        {/if}
        <div class="table-responsive" data-pattern="priority-columns">
            <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th width="3%">#</th>
                    <th width="15%">{#p_customer#}</th>
                    <th width="15%">{#p_date#}</th>
                    <th width="15%">{#p_std_invoice_no#}</th>
                    <th width="10%">{#p_debit#}</th>
                    <th width="10%">{#p_credit#}</th>
                    <th width="5%">{#gnr_print#}</th>
                    <th width="20%">{#p_type#}</th>

                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $transaction as $row}
                    <tr>
                        <td>{$i++}</td>
                        <td>{$row.customer.customer_name}</td>
                        <td>{$row.created_at|date_format:"%G-%m-%d"}</td>
                        <td align="center">{if $row->type eq 7777 or  $row->type eq 7778 }{$row.invoice_no}{else } ---- {/if}</td>
                        <td align="center">{$row.debit|number_format:2}</td>
                        <td align="center">{$row.credit|number_format:2}</td>
                        <td align="center">
                            {if $row->type eq 7777}
                                {url urltype="print" opr_code='Invoices' url_string="bsc/P060/Invoices/print/0/{$smarty.session.lang}/{$row.model_id}"}
                            {/if}
                            {if $row->type eq 7778}
                                {url urltype="print" opr_code='InvoicesReturnIn' url_string="bsc/P060/InvoicesReturnIn/Rprint/0/{$smarty.session.lang}/{$row.model_id}"}
                            {/if}
                            {if $row->type eq 7779}
                                {url urltype="print" check=0 url_string="bsc/P060/CustomerPayment/print_single/0/{$smarty.session.lang}/{$row->model_id}" style="btn btn-default sharp"}
                            {/if}
                            {if $row->type eq 7782}
                                {url urltype="print" check=0 url_string="bsc/P060/CustomerPayment/toReimbursement/0/{$smarty.session.lang}/{$row->model_id}"  style="btn btn-default sharp"}
                            {/if}
                        </td>
                        <td>{$row->typeName}</td>

                    </tr>

                {/foreach}
                </tbody>
            </table>
            <table class="table table-striped table-bordered">
                <thead>
                <tr style="background: #8cc474; color:white;">
                    <td></td>
                    <td>{#p_debit#}</td>
                    <td>{#p_credit#}</td>
                    <td>{#p_difference#}</td>
                </tr>
                </thead>
                <tbody>
                <tr style="background: #8cc474; color:white;">
                    <td width="48%"><b>{#p_total#}</b></td>

                    <td width="10%">{$transaction->sum('debit')|number_format:2}</td>

                    <td width="9%">{$transaction->sum('credit')|number_format:2}</td>

                    <td width="26%">{($transaction->sum('debit') - $transaction->sum('credit'))|number_format:2}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    {/if}
{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 10,
                        "oTableTools": {
                            "aButtons": [
                                //"copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}
