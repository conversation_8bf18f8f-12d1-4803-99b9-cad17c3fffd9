{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=body}
    <div class="row snsowraper">
        <div class="table-responsive" data-pattern="priority-columns">
            <a style="height: 30px;margin-bottom: 5px" target="_blank" href="{url check=0 urltype="path" url_string="bsc/P060/CustomersReport/print/0/{$smarty.session.lang}"}" class="btn btn-default btn-sm"><i class='fa fa-print fa-fw fa-lg'></i>{#gnr_print#}</a>
            <table id="snsotable-1" class="table table-striped table-bordered dataTable sortable-table">
                <thead>
                <tr>
                    <th width="1%">#</th>
                    <th width="17%">{#p_customer_name#}</th>
                    <th width="10%">{#p_customer_address#}</th>
                    <th width="12%"> {#p_customer_phone#} </th>
                    <th width="10%">{#p_customer_vat_tax#}</th>
                    <th width="5%"> {#gnr_status#} </th>
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $customers as $customer}
                    <tr>
                        <td>{$i++}</td>
                        <td>{$customer->customer_name}</td>
                        <td>{$customer->customer_address}</td>
                        <td>{$customer->customer_phone}</td>
                        <td>{$customer->Vat_tax}</td>
                        <td>{getname table=st_setting id=$customer->status}</td>
                    </tr>
                {/foreach}
                </tbody>
            </table>
        </div>
    </div>
{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 10,
                        "oTableTools": {
                            "aButtons": [
                                //"copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}
