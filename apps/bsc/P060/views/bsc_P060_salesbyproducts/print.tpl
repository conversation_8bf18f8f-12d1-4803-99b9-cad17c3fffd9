{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=title}

{/block}

{block name=body}
    <div class="text-center">
        <h2>{#p_sales_by_products_report#}</h2>
    </div>
            {if $smarty.session.searchArray.customer_id}
                {#p_customer#}:
                {getname table=sales_customers id=$smarty.session.searchArray.customer_id}
            {/if}
    <br>
            {if $smarty.session.searchArray.product}
                {#p_product#}:
                <ul>
                    {foreach $smarty.session.searchArray.product as $product}
                        <li>{getname table=warehouse_product id=$product}</li>
                    {/foreach}
                </ul>
            {/if}
            <div class="text-center">
            {if $smarty.session.searchArray.datefrom}
                  {#p_from_date#}:{$smarty.session.searchArray.datefrom}
            {/if}
            {if $smarty.session.searchArray.dateto}
                  {#p_to_date#}:{$smarty.session.searchArray.dateto}
            {/if}
            </div>
    <hr>
    <div class="table-responsive" data-pattern="priority-columns">
        <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer">
            <thead>
            <tr>
                <th width="3%">#</th>
                <th width="8%">{#p_products#}</th>
                <th width="13%">{#p_customer#}</th>
                <th width="8%">{#p_date#}</th>
                <th width="5%">{#p_unit#}</th>
                <th width="5%">{#p_quantity#}</th>
                <th width="9%">{#p_sale_price#}</th>
                <th width="6%">{#p_discount_percent#}</th>
                <th width="10%">{#p_discount_value#}</th>
                <th width="12%">{#p_sum#}</th>
                <th width="10%">{#p_vat#}</th>
                <th width="10%">{#p_total_with_vat#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $data as $row}
                <tr>
                    <td>{$i++}</td>
                    <td>{$row.product.name}</td>
                    <td>{$row.invoice.customer.customer_name}</td>
                    <td>{$row->date}</td>
                    <td>{$row.productUnit.unit.name}</td>
                    <td>{$row->qty}</td>
                    <td>{$row->sale_price}</td>
                    <td>{$row->discount}%</td>
                    <td>{$row->discount_value|number_format:2}</td>
                    <td>{($row->price_to_vat - $row->discount_value)|number_format:2}</td>
                    <td>{$row->total_tax}</td>
                    <td>{$row->product_total}</td>
                </tr>
            {/foreach}
            <tr style="background: #8cc474; color:white;">
                <td colspan="8"><b>{#p_total#}</b></td>
                <td align="center">{$data->sum('discount_value')|number_format:2}</td>
                <td align="center">{($data->sum('price_to_vat') - $data->sum('discount_value'))|number_format:2}</td>
                <td>{$data->sum('total_tax')|number_format:2}</td>
                <td>{$data->sum('product_total')|number_format:2}</td>
            </tr>
            </tbody>
        </table>
    </div>
    <hr>
    {*{render view="/reports/signatures" signatures=$signatures}*}
    <div class="row snsowraper">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            {#gnr_printed_by_user#}: {$smarty.session.user->full_name}<br>
            {#gnr_on_date#}: {$smarty.now|date_format:"%d/%m/%Y"}
        </div>
    </div>
{/block}
