{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=body}
    <div class="widget">
            <div class="widget-header bg-blue">
                <i class="widget-icon fa fa-arrow-left"></i>
                <span class="widget-caption">{#gnr_search#}</span>
                <div class="widget-buttons">
                    <a href="#" data-toggle="collapse">
                    </a>
                </div>
            </div>
            <div class="widget-body">

                <form method="post"
                      action='{url urltype="path" url_string="bsc/P060/salesbyproducts/show/0/{$smarty.session.lang}/search"}'>

                    <div class="row">

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_product#}</div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                            <select name="product[]" placeholder="{#p_select_from_list_bellow_multiple#}" multiple="multiple">
                                <option></option>
                                {foreach $products as $product}
                                    <option value="{$product->id}" {if in_array($product->id, $smarty.session.searchArray.product) } selected {/if}>{$product->name}</option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            <select name="customer_id" placeholder="{#gnr_select_from_list_bellow#}">
                                <option></option>
                                {foreach $customers as $customer}
                                    <option value="{$customer->id}" {if $smarty.session.searchArray.customer_id eq $customer->id} selected {/if}>{$customer->customer_name}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>

                    <div class="row">

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_from_date#}</div>
                        <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=datefrom type=edit row=$smarty.session.searchArray required=true}</div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_to_date#}</div>
                        <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=dateto type=edit row=$smarty.session.searchArray required=true}</div>

                    </div>
                    <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>
                    {if $smarty.session.searchArray}
                        {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P060/salesbyproducts/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
                    {/if}
                </form>
            </div>
    </div>
    {if $data}
        <div class="row snsowraper">
            {if count($data) neq 0}
                <a style="height: 30px;margin-bottom: 5px" target="_blank" href="{url check=0 urltype="path" url_string="bsc/P060/salesbyproducts/print/0/{$smarty.session.lang}"}" class="btn btn-default btn-sm"><i class='fa fa-print fa-fw fa-lg'></i>{#gnr_print#}</a>
            {else}
                    <div class="alert alert-warning fade in"><i class="fa-fw fa fa-warning"></i>{#p_no_data_to_print#}</div>
            {/if}
            <div class="table-responsive" data-pattern="priority-columns">
                <table class="table table-striped table-bordered dataTable sortable-table">
                    <thead>
                    <tr>
                        <th width="3%">#</th>
                        <th width="10%">{#p_products#}</th>
                        <th width="10%">{#p_customer#}</th>
                        <th width="6%">{#p_date#}</th>
                        <th width="6%">{#p_unit#}</th>
                        <th width="3%">{#p_quantity#}</th>
                        <th width="7%">{#p_sale_price#}</th>
                        <th width="4%">{#p_discount_percent#}</th>
                        <th width="6%">{#p_discount_value#}</th>
                        <th width="7%">{#p_sum#}</th>
                        <th width="7%">{#p_vat#}</th>
                        <th width="7%">{#p_total#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $data as $row}
                        <tr>
                            <td>{$i++}</td>
                            <td>{$row.product.name}</td>
                            <td>{$row.invoice.customer.customer_name}</td>
                            <td>{$row->date}</td>
                            <td>{$row.productUnit.unit.name}</td>
                            <td>{$row->qty}</td>
                            <td>{$row->sale_price}</td>
                            <td>{$row->discount}%</td>
                            <td>{$row->discount_value|number_format:2}</td>
                            <td>{($row->price_to_vat - $row->discount_value)|number_format:2}</td>
                            <td>{$row->total_tax}</td>
                            <td>{$row->product_total}</td>
                        </tr>

                    {/foreach}
                    </tbody>
                </table>
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th width="7%"></th>
                            <th width="7%">{#p_discount#}</th>
                            <th width="7%">{#p_sum#}</th>
                            <th width="7%">{#p_vat#}</th>
                            <th width="7%">{#p_total_with_vat#}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="background: #8cc474; color:white;">
                            <td><b>{#p_total#}</b></td>
                            <td align="center">{$data->sum('discount_value')|number_format:2}</td>
                            <td align="center">{($data->sum('price_to_vat') - $data->sum('discount_value'))|number_format:2}</td>
                            <td align="center">{$data->sum('total_tax')|number_format:2}</td>
                            <td align="center">{$data->sum('product_total')|number_format:2}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    {/if}
{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 10,
                        "oTableTools": {
                            "aButtons": [
                                //"copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}