{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />
{/block}
{block name=body}
    <div class="widget">
        <div class="widget-header bg-blue">
            <i class="widget-icon fa fa-arrow-left"></i>
            <span class="widget-caption">{#gnr_search#}</span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                </a>
            </div>
        </div>
        <div class="widget-body">

            <form method="post"
                  action='{url urltype="path" url_string="bsc/P060/performaInvoiceReport/show/0/{$smarty.session.lang}/search"}'>

                <div class="row">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_status#}</div>
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                        <select name="status" placeholder="{#gnr_select_from_list_bellow#}">
                            <option></option>
                            {foreach $invoicesStatuses as $key => $status}
                                <option value="{$key}" {if $key eq $smarty.session.searchArray.status } selected {/if}>{$status}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                        <select name="customer_id" placeholder="{#gnr_select_from_list_bellow#}">
                            <option></option>
                            {foreach $customers as $customer}
                                <option value="{$customer->id}" {if $smarty.session.searchArray.customer_id eq $customer->id} selected {/if}>{$customer->customer_name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>

                <div class="row">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_from_date#}</div>
                    <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=datefrom type=edit row=$smarty.session.searchArray required=true}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_to_date#}</div>
                    <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=dateto type=edit row=$smarty.session.searchArray required=true}</div>

                </div>
                <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>
                {if $smarty.session.searchArray}
                    {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P060/performaInvoiceReport/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
                {/if}
            </form>
        </div>
    </div>
    {if $invoicesDetails}
    <div class="row snsowraper">
        {if count($invoicesDetails) neq 0}
            <a style="height: 30px;margin-bottom: 5px" target="_blank" href="{url check=0 urltype="path" url_string="bsc/P060/performaInvoiceReport/print/0/{$smarty.session.lang}"}" class="btn btn-default btn-sm"><i class='fa fa-print fa-fw fa-lg'></i>{#gnr_print#}</a>
        {else}
            <div class="alert alert-warning fade in"><i class="fa-fw fa fa-warning"></i>{#p_no_data_to_print#}</div>
        {/if}
        <div class="table-responsive" data-pattern="priority-columns">
            <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th width="7%">{#p_invoice_qutation_no#}</th>
                    <th width="7%">{#p_invoice_no#}</th>
                    <th width="10%">{#p_invoice_type#}</th>
                    <th width="15%">{#p_customer#}</th>
                    <th width="10%">{#p_date#}</th>
                    <th width="10%">{#p_sub_product_total#}</th>
                    <th width="10%">{#p_discount#}</th>
                    <th width="10%">{#p_sum_total_with_discount#}</th>
                    <th width="10%">{#p_vat#}</th>
                    <th width="10%">{#p_total#}</th>
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $invoicesDetails as $row}
                    <tr>
                        <td>
                            {if $row->status eq 1}
                                {$row->invoice_quotation_no}
                            {else}
                                {$row->invoice_no}
                            {/if}
                        </td>
                        <td>
                            {if $row->status eq 1}
                                {$row->invoice_no}
                            {else}
                                ---
                            {/if}


                        </td>
                        <td>
                            {if $row.status eq 1}
                                {#p_sales_invoice#}
                            {elseif $row.status eq 2}
                                {#p_performa_invoice#}
                            {elseif $row.status eq 3}
                                {#p_cancelled#}
                            {/if}
                        </td>
                        <td>{$row.customer.customer_name}</td>
                        <td>{$row->date}</td>
                        <td>{$row->subProductTotal}</td>
                        <td>{$row->sumTotalDiscount}</td>
                        <td>{$row->subTotal_without_discount}</td>
                        <td>{$row->sumTotaltax}</td>
                        <td>{$row->subTotal}</td>
                    </tr>

                {/foreach}
                </tbody>
            </table>
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th width="10%"></th>
                        <th width="10%">{#p_sub_product_total#}</th>
                        <th width="10%">{#p_discount#}</th>
                        <th width="10%">{#p_sum_total_with_discount#}</th>
                        <th width="10%">{#p_vat#}</th>
                        <th width="10%">{#p_total#}</th>
                    </tr>
                </thead>
                <tbody>
                <tr style="background: #8cc474; color:white;">
                    <td><b>{#p_total#}</b></td>
                    <td align="center">{$invoicesDetails->sum('subProductTotal')|number_format:2}</td>
                    <td align="center">{$invoicesDetails->sum('sumTotalDiscount')|number_format:2}</td>
                    <td align="center">{$invoicesDetails->sum('subTotal_without_discount')|number_format:2}</td>
                    <td align="center">{$invoicesDetails->sum('sumTotaltax')|number_format:2}</td>
                    <td align="center">{$invoicesDetails->sum('subTotal')|number_format:2}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    {/if}

{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 10,
                        "oTableTools": {
                            "aButtons": [
                                //"copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}
