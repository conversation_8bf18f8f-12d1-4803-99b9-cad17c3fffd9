{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=title}

{/block}

{block name=body}
<div class="text-center">
    <h2>
        {#p_performa_invoice_report#}
    </h2>
</div>
    {if $smarty.session.searchArray.customer_id}
        {#p_customer#}:
        {getname table=sales_customers id=$smarty.session.searchArray.customer_id}
    {/if}
<br>
    {if $smarty.session.searchArray.status}
        {#p_invoice_type#}:
        {if $smarty.session.searchArray.status eq 1}
            {#p_sales_invoice#}
        {/if}
        {if $smarty.session.searchArray.status eq 2}
            {#p_performa_invoice#}
        {/if}
        {if $smarty.session.searchArray.status eq 3}
            {#p_cancelled#}
        {/if}

    {/if}
    <div class="text-center">
        {if $smarty.session.searchArray.datefrom}
            {#p_from_date#}:{$smarty.session.searchArray.datefrom}
        {/if}
        {if $smarty.session.searchArray.dateto}
            {#p_to_date#}:{$smarty.session.searchArray.dateto}
        {/if}
    </div>
    <hr>
    <div class="row snsowraper">
        <div class="table-responsive" data-pattern="priority-columns">
            <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer">
                <thead>
                <tr>
                    <th width="7%">{#p_invoice_qutation_no#}</th>
                    <th width="7%">{#p_invoice_no#}</th>
                    <th width="10%">{#p_invoice_type#}</th>
                    <th width="15%">{#p_customer#}</th>
                    <th width="10%">{#p_date#}</th>
                    <th width="10%">{#p_sub_product_total#}</th>
                    <th width="10%">{#p_discount#}</th>
                    <th width="10%">{#p_sum_total_with_discount#}</th>
                    <th width="10%">{#p_vat#}</th>
                    <th width="10%">{#p_total#}</th>
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $invoicesDetails as $row}
                    <tr>
                        <td>
                            {if $row->status eq 1}
                                {$row->invoice_quotation_no}
                            {else}
                                {$row->invoice_no}
                            {/if}
                        </td>
                        <td>
                            {if $row->status eq 1}
                                {$row->invoice_no}
                            {else}
                                ---
                            {/if}


                        </td>
                        <td>
                            {if $row.status eq 1}
                                {#p_sales_invoice#}
                            {elseif $row.status eq 2}
                                {#p_performa_invoice#}
                            {elseif $row.status eq 3}
                                {#p_cancelled#}
                            {/if}
                        </td>
                        <td>{$row.customer.customer_name}</td>
                        <td>{$row->date}</td>
                        <td>{$row->subProductTotal}</td>
                        <td>{$row->sumTotalDiscount}</td>
                        <td>{$row->subTotal_without_discount}</td>
                        <td>{$row->sumTotaltax}</td>
                        <td>{$row->subTotal}</td>
                    </tr>

                {/foreach}
                <tr style="background: #8cc474; color:white;">
                    <td colspan="5"><b>{#p_total#}</b></td>
                    <td>{$invoicesDetails->sum('subProductTotal')|number_format:2}</td>
                    <td>{$invoicesDetails->sum('sumTotalDiscount')|number_format:2}</td>
                    <td>{$invoicesDetails->sum('subTotal_without_discount')|number_format:2}</td>
                    <td>{$invoicesDetails->sum('sumTotaltax')|number_format:2}</td>
                    <td>{$invoicesDetails->sum('subTotal')|number_format:2}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
<hr>
    <div class="row snsowraper">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            {#gnr_printed_by_user#}: {$smarty.session.user->full_name}<br>
            {#gnr_on_date#}: {$smarty.now|date_format:"%d/%m/%Y"}
        </div>
    </div>
{/block}
