<?php

/**
 * Created by PhpStorm.
 * User: developer
 * Date: 04/08/21
 * Time: 12:46 م
 */

use Models\sales\Model_processing;
use Models\sales\SalesGenerateInvoices;
use Models\sales\SalesGenerateTemInvoices;
use Models\sales\SalesTemInvoicesDetails;
use Models\sales\PosInvoice;
use Models\sales\PosInvoiceDetails;
use Models\Warehouse\Product;
use Models\Warehouse\Stock;

use Models\Warehouse\Group;
use Models\Warehouse\StockTransaction;

class bsc_P060_pointOfSales extends Controller
{
    
    public function show($parm, $post)
    {
        $Model_processing = new Model_processing();
        $this->staticData($Model_processing,$invoices,$post);
        return $this->switchedFunction($Model_processing,$parm,$post);


    }
    public function test(){
         // return $post;
         $Model_processing = new Model_processing();
//        $Model_processing->getInvoiceNotComeFromPerfoma();
        $_SESSION['filterParams'] = $post;
        $invoices = $Model_processing->getPosInvoice(4, 6,$post['datefrom'],$post['dateto']);
        // return $invoices;
        $this->staticData($Model_processing,$invoices,$post);
        return $this->switchedFunction($Model_processing,$parm, $post);
                  
                 
    }

    public function  staticData($Model_processing,$parm,$post){
        $products = Product::with('group')->get();
        $groups = Group::all();
        $this->Smarty->assign('groups', $groups);
        $this->Smarty->assign('products', $products);
        $_SESSION['group'] = Group::where('id', $post['group_id'])->select('name')->first()->name;
        $_SESSION['product'] = Product::where('id', $post['product_id'])->select('name')->first()->name;
        $_SESSION['pos_invoice_token'] = Helper::generateToken();

    }
    public function  switchedFunction($Model_processing,$parm, $post){
        switch ($parm[0]) {
            case 'menu';
                $_SESSION['filterParams'] = null;
                break;
            case 'filter';
                $invoices = $Model_processing->getPosInvoice(4, 6,$post['datefrom'],
                $post['dateto']);
                $this->Smarty->assign('invoices', $invoices);
                $_SESSION['filterParams'] = $post;
                return $invoices;
                break;
            case 'insert':
//                return $post;
                  $this->Add($parm, $post);
                break;
        }
    }
  
    public function Add($parm, $post)
    {
        $date = now();
        $year = date_format($date, "Y");
        $PosInvoice = new PosInvoice();
        $random = str_pad((int)$PosInvoice->id+1, 6, '0', STR_PAD_LEFT);
        $invoice_no = 'POS' . '-' . $year . '-' . $random;
        $PosInvoice->fill(["invoice_no" => $invoice_no,
//         'stock_id' => $post["stock_id"],
         'date' => $date,
         'note' =>"Point of sale", 
         'subProductTotal' => $post["Product_Total"],
         'sumTotaltax' => $post["total_tax"],
         'sumTotalDiscount' => $post["total_discount"],
         'subTotal' => $post["total_invoice"],
         'subTotal_without_discount' => $post["subTotal_without_discount"],
         'created_by' => $_SESSION['user']->id,

        ]);
        $PosInvoice->save();
        foreach ($post["data"] as $d) {
            $PosInvoiceDetails = new PosInvoiceDetails();
            $PosInvoiceDetails->fill(["invoice_sale_id" => $PosInvoice->id, 
            "product_id" => $d["product_id"], 
            "product_unit_id" => $d["product_unit_id"], 
            "sale_price" => $d["sale_price"],
            "discount" => $d["discount"],
            "tax" => $d["tax"],
            "date" => $PosInvoice->date, 
            "product_total" => $d["total"],
            "total_discount" => 0,
            "total_tax" => 0    ,
            "qty" => $d["qty"],]);
            $PosInvoiceDetails->save();


        }
        Notification::createdAlert();
      
        redirect('pointOfSales/show');

    }

}