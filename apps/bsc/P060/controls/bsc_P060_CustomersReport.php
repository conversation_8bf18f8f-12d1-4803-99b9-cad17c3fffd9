<?php

/**
 * Created by PhpStorm.
 * User: developer
 * Date: 09/06/21
 * Time: 07:32 م
 */
use Models\Finance\Account;
use Models\sales\PosInvoiceDetails;
use Models\sales\SalesCustomer;
use Models\sales\SalesInvoicesDetails;
use Models\Warehouse\Product;
use Models\sales\perfoma_operation;


class bsc_P060_CustomersReport extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0])
        {
            case 'menu':
                break;
        }
        $customers = SalesCustomer::getAllCustomer();
        $this->Smarty->assign('customers', $customers);
    }
    public function print()
    {
        $customers = SalesCustomer::getAllCustomer();
        $this->Smarty->assign('customers', $customers);
        generatePdf();
    }
}