<?php

/**
 * Created by PhpStorm.
 * User: developer
 * Date: 09/06/21
 * Time: 07:12 م
 */

use Models\sales\Check;
use Models\sales\invoice_Operation;
use Models\sales\Model_processing;
use Models\sales\payment;
use Models\sales\perfoma_operation;
use Models\sales\ProductOperation;
use Models\sales\Qr_operations;
use Models\sales\SaleCustomerPayment;
use Models\sales\Sales_Invoice_payment;
use Models\sales\SalesCustomer;
use Models\sales\SalesGenerateInvoices;
use Models\sales\SalesGenerateTemInvoices;
use Models\sales\SalesTemInvoicesDetails;
use Models\sales\TransactionsType;
use Models\Warehouse\Product;
use Models\Warehouse\Stock;

class bsc_P060_performaInvoice extends Controller
{
    public function show($parm, $post)
    {

        perfoma_operation::updateInvoiceDateValidation();
        $_SESSION['filterParams'] = $post;

         $this->switchedFunction($parm,$post , 'show');
        $this->staticData($post);
        $invoices = perfoma_operation::getPerformaInvoices($post);
        $this->Smarty->assign('invoices', $invoices);
    }
    public function invoices($parm, $post){
        $invoices=perfoma_operation::getAllInvoices($post);
        $this->Smarty->assign('invoices', $invoices);
        $this->staticData($post);
        perfoma_operation::updateInvoiceDateValidation();
        $this->switchedFunction($parm,$post , 'invoices');
    }
    public function perfomainvoices($parm, $post){
        $invoices = perfoma_operation::getPerformaInvoices($post);
        $this->Smarty->assign('invoices', $invoices);
        $this->staticData($post);
        perfoma_operation::updateInvoiceDateValidation();
        $this->switchedFunction($parm,$post ,'perfomainvoices');

    }
    public function InvalidInvoice($parm, $post){
        $invoices=perfoma_operation::getUnvalidInvoice($post);
        $this->Smarty->assign('invoices', $invoices);
        $this->staticData($post);
        perfoma_operation::updateInvoiceDateValidation();
        $this->switchedFunction($parm,$post , 'InvalidInvoice');
    }
    public function switchedFunction($parm,$post,$method){

        $Model_processing = new Model_processing();

        switch ($parm[0]) {
            case 'menu';
                if($method === 'show'){
                    $invoices = perfoma_operation::getPerformaInvoices($post);
                } else if($method === 'invoices'){
                    $invoices = perfoma_operation::getAllInvoices($post);
                } else if($method === 'perfomainvoices'){
                    $invoices = perfoma_operation::getPerformaInvoices($post);
                } else if($method === 'InvalidInvoice'){
                    $invoices = perfoma_operation::getUnvalidInvoice($post);
                }
                perfoma_operation::updateInvoiceDateValidation();
                $this->Smarty->assign('invoices', $invoices);
                $_SESSION['filterParams'] = null;
                break;
            case 'filter';
                $invoices = perfoma_operation::getCustomerTransaction($post,$parm[1]);
                perfoma_operation::updateInvoiceDateValidation();
                $this->Smarty->assign('invoices', $invoices);
                $_SESSION['filterParams'] = $post;
                break;
            case 'insert':
                if ($parm[1] === $_SESSION['s_stock_performaInvoice_token']) {
                    perfoma_operation::add($parm, $post);
                }
                break;
            case 'delete':
                    $Model_processing->delete_temInvoice($parm, $post);
                break;
            case 'details':
                $this->details($parm, $post);
                break;
            case 'update':
//                return $parm;
                if ($parm[2] === $_SESSION['s_stock_performaInvoice_token']) {
                    perfoma_operation::update($parm,$post);
                }
                break;
            case 'makeInvoice':
                $this->makeInvoice($parm,$post);
                break;
        }
    }
    public function staticData($post){
        $types = Setting::getList(295);
        $types = collect($types);
        $_SESSION['type'] = $types->where('id', $post['type'])->first()->translatedName;
        $_SESSION['stock'] =Stock::getStockName($post['stock_id']);
        $stocks = Stock::getStockLocationName();
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('types', $types);
        $post['product_id']='';
        $post['group_id']='';
        $products = Product::getProucts(23,1360);
        $_SESSION['customer_id'] = SalesCustomer::getCustomerName($post['customer']);
        $customers =  SalesCustomer::getAllCustomer();
        $this->Smarty->assign('customers', $customers);
        $this->Smarty->assign('products', $products);

    }
    public function details($parm)
    {
        $invoice = SalesGenerateTemInvoices::find($parm[0])->load('stock');
        $details = SalesTemInvoicesDetails::with('product')->with('productUnit.unit')->where('invoice_sale_id', $invoice->id)->get();
        $haveProducts=0;
        $details = collect($details);
        foreach ($details as $d )
        {
            if ($d['product']['type']==1360)
                $haveProducts ++;
        }
        foreach ($details as $index => $d) {
            $result = (int)perfoma_operation::getUnitNumber($d["product_unit_id"], $d["product_id"])["data"];
        }
        $qr = Qr_operations::generateQR($invoice);
        $customer_id=$invoice->customer_id;
        $customers = SalesCustomer::getAllCustomer()->where('id',$customer_id)->first();
        $this->Smarty->assign('haveProducts', $haveProducts);
        $this->Smarty->assign('customers', $customers);
        $baseUrl = ENVIRONMENT == "PRODUCTION" ? SNSO_URL :  str_replace('https', 'http', SNSO_URL);
        $qr = ENVIRONMENT == "PRODUCTION" ? $qr : str_replace('https', 'http', $qr)  ;
        $this->Smarty->assign('qr_path', $qr);
        $this->Smarty->assign('baseUrl', $baseUrl);
        $this->Smarty->assign('details', $details);
        $this->Smarty->assign('invoice', $invoice);
        $this->Smarty->assign('origanization', Organization::readID($_SESSION['organization']->id));
    }
    public function makeInvoice($parm, $post)
    {

        $payment = SaleCustomerPayment::where('invoice_id' , $parm[1])->first();
        $SalesGenerateInvoices = SalesGenerateInvoices::find($parm[1]);
        $SalesGenerateInvoices = ($SalesGenerateInvoices->id != null) ? $SalesGenerateInvoices : invoice_Operation::getSalesGenerateInvoices($post);
        $invoice_no = $SalesGenerateInvoices->invoice_no;
        $SalesGenerateInvoices->status = 1;
        $SalesGenerateInvoices->invoice_no = perfoma_operation::getUpdatedInvoiceSerial();
        $SalesGenerateInvoices->invoice_quotation_no = $invoice_no;
        $SalesGenerateInvoices->date = today();
        $SalesGenerateInvoices->pay_date=today();
        $SalesGenerateInvoices->save();


        $created=false;
        if($SalesGenerateInvoices->paymentType==payment::Cash){
            $created=Sales_Invoice_payment::DoFinanialEntryForCash($SalesGenerateInvoices);
            if($created){
                $SalesGenerateInvoices->ispayed=payment::payed;
                $SalesGenerateInvoices->status = 1;
                $SalesGenerateInvoices->save();
            }
        }elseif ($SalesGenerateInvoices->paymentType == payment::Bank){
            $created=Sales_Invoice_payment::DoFinanialEntryForBank($SalesGenerateInvoices);
            if($created){
                $SalesGenerateInvoices->ispayed=payment::payed;
                $SalesGenerateInvoices->status = 1;
                $SalesGenerateInvoices->save();
            }
        }elseif($SalesGenerateInvoices->paymentType == payment::PostPayment){
            $created=Sales_Invoice_payment::DoFinanialEntryForPostPayment($SalesGenerateInvoices);
            if($created){
                $SalesGenerateInvoices->ispayed=payment::unpayed;
                $SalesGenerateInvoices->save();
            }
        }
        if ($created){


            if (
                !empty($SalesGenerateInvoices->id) and
                !empty($SalesGenerateInvoices->date) and
                !empty($SalesGenerateInvoices->subTotal) and
                !empty($SalesGenerateInvoices->customer_id) and
                !empty($SalesGenerateInvoices->invoice_no)
            ) {
                ProductOperation::addCustomerTransaction(
                    $SalesGenerateInvoices->id,
                    $SalesGenerateInvoices->invoice_no,
                    $SalesGenerateInvoices->customer_id,
                    $SalesGenerateInvoices->subTotal,
                    $SalesGenerateInvoices->date,
                    TransactionsType::Invoice,
                    $SalesGenerateInvoices->entry_id
                );
            }
            Notification::updatedAlert();
            redirect('performaInvoice/invoices');

        }



    }
    public function confirm($parm, $post)
    {
//        return $parm[0];
        $this->Smarty->assign('row', SalesGenerateInvoices::find($parm[0]));
        $_SESSION['s_stock_performaInvoice_token'] = Helper::generateToken();
    }

    /**
     * @param $parm
     * @param $post
     */
    public function add($parm, $post)
    {
        $invoice_no = Check::findCheckInvoiceNo()->invoice_no;
        $invoice_id = Check::findCheckInvoiceNo()->id;
        $post["invoice_id"] = $invoice_id;
        if ($invoice_no == "") {
            $post['date'] = now();
            $post['invoice_no'] = 0;
            $post['stock_id'] = 1;
            $post['note'] = "creating invoice number";
            $invoice_no = perfoma_operation::createInvoiceSerial($post);
            $qr = Qr_operations::generateQR2($invoice_no);
            Check::addCheck($invoice_no);
            $stocks = Stock::getStockLocationName();
            $products = Product::getAllProduct();
            $haveProducts = $products->where('type' , 1361)->count();
            $customers = SalesCustomer::getAllCustomer();
            $invoice = invoice_Operation::findInvoices( Check::findCheckInvoiceNo()->invoice_no)->first();
            $this->Smarty->assign('invoice', $invoice);
            $this->Smarty->assign('products', $products);
            $this->Smarty->assign('haveProducts', $haveProducts);
            $this->Smarty->assign('stocks', $stocks);
            $this->Smarty->assign('customers', $customers);
            $this->Smarty->assign('invoice_no', $invoice_no);
//            $this->Smarty->assign('qr_path', $qr);
            $baseUrl = ENVIRONMENT == "PRODUCTION" ? SNSO_URL :  str_replace('https', 'http', SNSO_URL);
            $qr = ENVIRONMENT == "PRODUCTION" ? $qr : str_replace('https', 'http', $qr);
            $this->Smarty->assign('qr_path', $qr);
            $this->Smarty->assign('baseUrl', $baseUrl);
            $_SESSION['s_stock_performaInvoice_token'] = Helper::generateToken();
        } else {
            $qr = Qr_operations::generateQR2($invoice_no);
            $stocks = Stock::getStockLocationName();
            $products = Product::getAllProduct();
            $products = Product::getAllProduct();
            $haveProducts = $products->where('type' , 1361)->count();
            $customers = SalesCustomer::getAllCustomer();
            Check::updateCheckToPerformaInvoice();
            $invoice = invoice_Operation::findInvoices( Check::findCheckInvoiceNo()->invoice_no)->first();
            $invoice_no = Check::findCheckInvoiceNo()->invoice_no;
            $this->Smarty->assign('invoice', $invoice);
            $this->Smarty->assign('products', $products);
            $this->Smarty->assign('haveProducts', $haveProducts);
            $this->Smarty->assign('stocks', $stocks);
            $this->Smarty->assign('customers', $customers);
            $this->Smarty->assign('invoice_no', $invoice_no);
            $this->Smarty->assign('qr_path', $qr);
            $_SESSION['s_stock_performaInvoice_token'] = Helper::generateToken();
        }


    }


    public function print($parm)
    {
        $this->details($parm);
        generatePdf();

    }


    public function edit($parm, $post)
    {
        $SalesGenerateTemInvoices = SalesGenerateInvoices::find($parm[0])->load("InvoicesStockDetail.product.units.unit");
        $theInvoice=$SalesGenerateTemInvoices;
        if($SalesGenerateTemInvoices->stock_id){

            $type = Stock::findOrFail($SalesGenerateTemInvoices->stock_id)->type;
            $stockType = Setting::readID($type)->translatedName;
            $this->Smarty->assign('stockType', $stockType);
        }
        $stocks = Stock::getStockLocationName();
        $products = Product::getAllProduct();
        $customers = SalesCustomer::getAllCustomer();
        $customer_id = SalesCustomer::getCustomerName($SalesGenerateTemInvoices->customer_id);
        $qr = Qr_operations::generateQR($SalesGenerateTemInvoices);
        $SalesGenerateTemInvoices = collect($SalesGenerateTemInvoices);
        $data = [];
        $haveProducts=0;
        foreach ($SalesGenerateTemInvoices["invoices_stock_detail"] as $index => $d) {
            $result = (int)invoice_Operation::getUnitNumber($d["product_unit_id"], $d["product_id"])["data"];
            $data[] = $d;
            if ($d['product']['type']==1360)
                $haveProducts ++;
        }
        $SalesGenerateTemInvoices["invoices_stock_detail"] = $data;
        $invoice = $SalesGenerateTemInvoices;
        $this->Smarty->assign('haveProducts', $haveProducts);
        $this->Smarty->assign('invoice', $invoice);
        $this->Smarty->assign('theInvoice', $theInvoice);
        $baseUrl = ENVIRONMENT == "PRODUCTION" ? SNSO_URL :  str_replace('https', 'http', SNSO_URL);
        $qr = ENVIRONMENT == "PRODUCTION" ? $qr : str_replace('https', 'http', $qr)  ;
        $this->Smarty->assign('qr_path', $qr);
        $this->Smarty->assign('baseUrl', $baseUrl);
        $this->Smarty->assign('customer_id', $customer_id);
        $this->Smarty->assign('products', $products);
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('customers', $customers);
        $this->Smarty->assign('SalesGenerateTemInvoices', $SalesGenerateTemInvoices);
        $_SESSION['s_stock_performaInvoice_token'] = Helper::generateToken();

    }
}
