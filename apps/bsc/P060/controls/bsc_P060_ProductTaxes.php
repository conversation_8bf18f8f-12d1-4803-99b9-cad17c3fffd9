<?php
use Models\Warehouse\Tax;

/**
 * Created by PhpStorm.
 * User: mohammed kamal
 * Date: 04/10/21
 * Time: 07:02 م
 * ProductTaxes
 */
class bsc_P060_ProductTaxes extends Controller
{
    public function show($parm, $post)
    {

        switch ($parm[0]){
            case 'insert':

                if ($parm[1] === $_SESSION['s_Taxes_token']){
                    Validation::rules($post, [
                        'name' => 'required',
                    ]);
                    if(Validation::check()) {
                        $this->insert($parm, $post );
                    }
                }
                break;
            case 'delete':
                if ($parm[1] === $_SESSION['s_Taxes_token']){
                    $this->delete($parm, $post);
                }
                break;

            case 'update':

                if ($parm[1] === $_SESSION['s_Taxes_token']){
                    Validation::rules($post, [
                        'name' => 'required',


                    ]);
                    if(Validation::check()) {
                        $this->update($parm, $post );
                    }
                }
                break;
        }

        $taxes = Tax::where('type',1361)->get();
        $this->Smarty->assign('taxes', $taxes);

        $_SESSION['s_Taxes_token'] = Helper::generateToken();
    }

    public function add($parm, $post)
    {
        $_SESSION['s_Taxes_token'] = Helper::generateToken();
    }

    public function insert($parm, $post )
    {
        $post['type']=1361;
        $taxes= new Tax($post);
        $taxes->created_by=$_SESSION['user']->id;
        $taxes->save();
        Notification::createdAlert();
        redirect('ProductTaxes/show');

    }

    public function edit($parm, $post)
    {

        $this->Smarty->assign('taxes', Tax::find($parm[0]));
    }

    public function update($parm, $post )
    {
        $taxes = Tax::find($parm[2]);
        $taxes->fill($post);
        $taxes->created_by=$_SESSION['user']->id;
        $taxes->save();
        Notification::updatedAlert();
        redirect('ProductTaxes/show');

    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', Tax::find($parm[0]));

        $_SESSION['s_Taxes_token'] = Helper::generateToken();
    }

    public function delete($parm, $post)
    {
        $taxes = Tax::find($parm[2])->delete();

        Notification::deletedAlert();
    }

}