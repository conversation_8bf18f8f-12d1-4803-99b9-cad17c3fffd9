<?php
use Domain\Warehouse\Repositories\TransactionRepository;
use Domain\Warehouse\Services\StockDepositRepository;
use Domain\Warehouse\Services\StockWithDrawalRepository;
use Models\sales\invoice_Operation;
use Models\sales\payment;
use Models\sales\ProductOperation;
use Models\sales\Qr_operations;
use Models\sales\SaleReturnIn;
use Models\sales\SaleReturnInDetails;
use Models\sales\sales_Return_Payment;
use Models\sales\SalesCustomer;
use Models\sales\SalesGenerateInvoices;
use Models\sales\SalesInvoicesDetails;
use Models\sales\StockStatus;
use Models\sales\TransactionsType;
use Models\Warehouse\Product;
use Models\Warehouse\Stock;

/**
 * Created by PhpStorm.
 * User: mohammed kamal
 * Date: 26/10/21
 * Time: 03:38 م
 */
#InvoicesReturnIn
class bsc_P060_InvoicesReturnIn extends Controller
{
    private $performaOperations;
    private $transactionRepository;

    public function show($parm, $post)
    {

        $_SESSION['filterParams'] = $post;
        $invoices = invoice_Operation::SearchInvoice($post);
        $this->staticData($invoices, $post);
        return $this->switchedFunction($parm, $post,'show');


    }
    public function returnInShow($parm, $post){
        $_SESSION['filterParams'] = $post;
        $invoices = invoice_Operation::getReturnInvoice($post['customer_id'], $post['stock_id'], $post['datefrom'], $post['dateto']);
//        return $invoices;
        $this->staticData($invoices, $post);
         $this->switchedFunction($parm, $post,'returnInShow');
    }

    /**
     * @param $invoices
     * @param $post
     */
    public function staticData($invoices, $post)
    {
        $this->transactionRepository = app(TransactionRepository::class);
        $types = Setting::getList(295);
        $types = collect($types);
        $_SESSION['type'] = $types->where('id', $post['type'])->first()->translatedName;
        $_SESSION['stock_id'] = Stock::getStockName($post['stock_id']);
        $stocks = Stock::getStockLocationName();
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('types', $types);
        $post['product_id'] = '';
        $post['group_id'] = '';
        $products = Product::getAllProduct();
        $this->Smarty->assign('invoices', $invoices);
        $this->Smarty->assign('invoice_nos', $invoices);
        $_SESSION['customer_id'] = SalesCustomer::getCustomerName($post['customer']);
        $customers = SalesCustomer::getAllCustomer();
        $this->Smarty->assign('customers', $customers);
        $this->Smarty->assign('isdelivered', StockStatus::isDeliveried);
        $this->Smarty->assign('isnotdelivered', StockStatus::isNotDeliveried);
        $this->Smarty->assign('Cash', payment::Cash);
        $this->Smarty->assign('Bank', payment::Bank);
        $this->Smarty->assign('payed', payment::payed);
        $this->Smarty->assign('unpayed', payment::unpayed);
        $this->Smarty->assign('products', $products);


    }
    /**
     * @param $parm
     * @param $post
     */
    public function  switchedFunction($parm, $post,$method){

        switch ($parm[0]) {
            case 'menu';
                if($method == 'show')
                    $invoices = invoice_Operation::SearchInvoice($post);
                else {
                    $invoices = invoice_Operation::getReturnInvoice($post['customer_id'], $post['stock_id'], $post['datefrom'], $post['dateto']);
                }

                $this->Smarty->assign('invoices', $invoices);
                $this->Smarty->assign('invoice_nos', $invoices);
                $_SESSION['filterParams'] = $post;
                break;
            case 'filter';
                $post['invoice_id']=$post['invoice_id'].trim(' ');
//                return $post;
                $invoices = invoice_Operation::SearchInvoice($post);
                $this->Smarty->assign('invoices', $invoices);
//                $this->Smarty->assign('invoice_nos', $invoices);
                #invoice_id
                $_SESSION['filterParams'] = $post;
                break;
            case 'filterReturn';
                $invoices = invoice_Operation::getReturnInvoice($post['customer_id'], $post['stock'],$post['datefrom'],$post['dateto']);
                $this->Smarty->assign('invoices', $invoices);
                $_SESSION['filterParams'] = $post;
                break;
            case 'details':
                $this->details($parm, $post);
                break;
            case 'Rdetails':
                $this->Rdetails($parm, $post);
                break;
            case 'update':
                   $this->update($parm, $post);
                break;



        }
    }

    /**
     * @param $invoice
     */
    public function doDeliveryStock($invoice)
    {
        try {
            $StockDeposit=new StockDepositRepository();
            $invoice= SaleReturnIn::find($invoice->id)->load("InvoicesStockDetail.product.units.unit");
            $isCreated =$StockDeposit->addStockDeposit($invoice, 'RI');
            if ($isCreated) {

                Notification::createdAlert();

            }

        }
        catch(ValidationException $e)
        {
            Notification::failAlert();
        }



    }
    public function details($parm)
    {
        $invoice = SalesGenerateInvoices::find($parm[0])->load('stock');
        $details = SalesInvoicesDetails::with('product')->with('productUnit.unit')->where('invoice_sale_id', $invoice->id)->get();
        $details = collect($details);
        $qr = Qr_operations::generateQR($invoice);
        $customer_id=$invoice->customer_id;
        $customers = SalesCustomer::getAllCustomer()->where('id',$customer_id)->first();
        $this->Smarty->assign('customers', $customers);
        $baseUrl = ENVIRONMENT == "PRODUCTION" ? SNSO_URL :  str_replace('https', 'http', SNSO_URL);
        $qr = ENVIRONMENT == "PRODUCTION" ? $qr : str_replace('https', 'http', $qr)  ;
        $this->Smarty->assign('qr_path', $qr);
        $this->Smarty->assign('baseUrl', $baseUrl);
        $this->Smarty->assign('details', $details);
        $this->Smarty->assign('invoice', $invoice);
        $this->Smarty->assign('origanization', Organization::readID($_SESSION['organization']->id));
    }
    public function Rdetails($parm)
    {
        $invoice = SaleReturnIn::find($parm[0])->load('stock');
        $details = SaleReturnInDetails::with('product')->with('productUnit.unit')->where('invoice_sale_id', $invoice->id)->get();
        $details = collect($details);
        $qr = Qr_operations::generateQR($invoice);
        $customer_id=$invoice->customer_id;
        $customers = SalesCustomer::getAllCustomer()->where('id',$customer_id)->first();
        $this->Smarty->assign('customers', $customers);
        $baseUrl = ENVIRONMENT == "PRODUCTION" ? SNSO_URL :  str_replace('https', 'http', SNSO_URL);
        $qr = ENVIRONMENT == "PRODUCTION" ? $qr : str_replace('https', 'http', $qr)  ;
        $this->Smarty->assign('qr_path', $qr);
        $this->Smarty->assign('baseUrl', $baseUrl);
        $this->Smarty->assign('details', $details);
        $this->Smarty->assign('invoice', $invoice);
        $this->Smarty->assign('origanization', Organization::readID($_SESSION['organization']->id));
    }
    public function edit($parm, $post)
    {

        $Sales_generate_invoices = SalesGenerateInvoices::find($parm[0])->load("InvoicesStockDetail.product.units.unit");
        $theInvoice=$Sales_generate_invoices;
        if($Sales_generate_invoices->stock_id){
            $type = Stock::findOrFail($Sales_generate_invoices->stock_id)->type;
            $stockType = Setting::readID($type)->translatedName;
            $this->Smarty->assign('stockType', $stockType);
        }
//        $type = Stock::findOrFail($Sales_generate_invoices->stock_id)->type;
//        $stockType = Setting::readID($type)->translatedName;
        $stocks = Stock::getAllStock();
        $products =Product::getAllProduct();
        $customer_id = SalesCustomer::getCustomerName($Sales_generate_invoices->customer_id);
        $qr = Qr_operations::generateQR($Sales_generate_invoices);
        $customers = SalesCustomer::getAllCustomer();
        $invoice = $Sales_generate_invoices;
//        return $invoice;
        $this->Smarty->assign('invoice', $invoice);
        $this->Smarty->assign('theInvoice', $theInvoice);
        $baseUrl = ENVIRONMENT == "PRODUCTION" ? SNSO_URL :  str_replace('https', 'http', SNSO_URL);
        $qr = ENVIRONMENT == "PRODUCTION" ? $qr : str_replace('https', 'http', $qr)  ;
        $this->Smarty->assign('qr_path', $qr);
        $this->Smarty->assign('baseUrl', $baseUrl);
        $this->Smarty->assign('customer_id', $customer_id);
        $this->Smarty->assign('products', $products);
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('customers', $customers);
        $this->Smarty->assign('Sales_generate_invoices', $Sales_generate_invoices);
        $this->Smarty->assign('stockType', $stockType);
        $_SESSION['s_InvoicesReturnIn_token'] = Helper::generateToken();
    }

    public function print($parm)
    {
        $this->details($parm);
        generatePdf();

    }
    public function Rprint($parm)
    {
        $this->Rdetails($parm);
        generatePdf();

    }
    public function updateSalesInvoicesDetails($invoice_details,$index,$data){

        $invoice_details->qty = (int)$data['data'][$index]['qty'];
        $invoice_details->product_unit_id=(int)$data['data'][$index]['product_unit_id'];
        $invoice_details->sale_price = (float)$data['data'][$index]['sale_price'];
        $invoice_details->discount = (int)$data['data'][$index]['discount'];
        $invoice_details->total_discount = (float)$data['data'][$index]['total_discount'];
        $invoice_details->tax = (int)$data['data'][$index]['tax'];
        $invoice_details->total_tax = (float)$data['data'][$index]['total_tax'];
        $invoice_details->product_total = (float)$data['data'][$index]['product_total'];
        $invoice_details->save();



    }
    public function updateSalesInvoicesDetailsWithDifferentUnit($invoice_details,$index,$data){

        $invoice_details->qty = (float)$data['data'][$index]['qty'];
        $invoice_details->product_unit_id=(int)$data['data'][$index]['product_unit_id'];
        $invoice_details->sale_price = (float)$data['data'][$index]['sale_price'];
        $invoice_details->discount = (int)$data['data'][$index]['discount'];
        $invoice_details->total_discount = (float)$data['data'][$index]['total_discount'];
        $invoice_details->tax = (int)$data['data'][$index]['tax'];
        $invoice_details->total_tax = (float)$data['data'][$index]['total_tax'];
        $invoice_details->product_total = (float)$data['data'][$index]['product_total'];
        $invoice_details->save();



    }


    /**
     * @param $old
     * @param $new
     * @return mixed
     */
    public function different($old, $new){
        return $old-$new;
    }

    /**
     * @param $data
     */
    public function createReturnSale($data)
    {
//        return $data;
        $date = now();
        $yearFormat = date_format($date, "Y");
        $pieces = explode("-", $yearFormat);
        $year = $pieces[0];
        $invoice = SalesGenerateInvoices::find($data['invoice_id']);
        $returnedsale = new SaleReturnIn();
        $returnedsale->save();
        $random = str_pad((int)$returnedsale->id, 6, '0', STR_PAD_LEFT);
        $invoice_no = 'RI' . '-' . $year . '-' . $random;
        $returnedsale->fill([
            "invoice_no" => $invoice_no,
            'stock_id' => $invoice->stock_id,
            'note' => $invoice->note,
            'customer_id' => $invoice->customer_id,
            'invoice_id'=>$invoice->id,
            'subProductTotal' => $data['subProductTotal'],
            'sumTotaltax' => $data["sumTotaltax"],
            'sumTotalDiscount' => $data["sumTotalDiscount"],
            'subTotal' => $data["subTotal"],
            'subTotal_without_discount' => $data["subTotal_without_discount"],
            'created_by' => $_SESSION['user']->id,
        ]);
        $returnedsale->paymentType= $data['paymentType'];
        $returnedsale->date = $date;
        $returnedsale->save();
//        return $SalesGenerateInvoices;
        foreach ($data['data'] as $d) {
            $SalesInvoicesDetails = new SaleReturnInDetails();
            $SalesInvoicesDetails->fill(["invoice_sale_id" => $returnedsale->id,
                "product_id" => $d["product_id"],
                "product_unit_id" => $d["product_unit_id"],
                "sale_price" => $d["sale_price"],
                "discount" => $d["discount"],
                "tax" => $d["tax"],
                "date" => $returnedsale->date,
                "product_total" => $d["product_total"],
                "total_discount" => $d["total_discount"],
                "total_tax" => $d["total_tax"],
                "qty" => $d["qty"],]);
            $SalesInvoicesDetails->save();

        }
        $ispayed =0;
        if ($returnedsale->stock_id !=0)
            $isdelived=$this->doDeliveryStock($returnedsale);
        if ($data['paymentType']==payment::Bank){
            $ispayed = sales_Return_Payment::DoFinanialEntryForBank($returnedsale);
        }
        elseif($data['paymentType']==payment::Cash){
            $ispayed = sales_Return_Payment::DoFinanialEntryForCash($returnedsale);
        }
        else{
            $ispayed=sales_Return_Payment::DoFinanialEntryForPostPayment($returnedsale);
        }

        if($ispayed > 0){
            $invoice->hasRInvoice=1;
            $invoice->save();
            if (
                !empty($returnedsale->id) and
                !empty($returnedsale->date) and
                !empty($returnedsale->subTotal) and
                !empty($returnedsale->customer_id) and
                !empty($returnedsale->invoice_no)
            ) {
                $amount =0;
                $amount = $returnedsale->subTotal;
                ProductOperation::addCustomerTransaction(
                    $returnedsale->id,
                    $returnedsale->invoice_no,
                    $returnedsale->customer_id,
                    $amount,
                    $returnedsale->date,
                    TransactionsType::ReturnedInvoice,
                    $returnedsale->entry_id
                );
            }
            Notification::createdAlert();
            redirect('InvoicesReturnIn/returnInShow');
        }

    }

    /**
     * @param $parm
     * @param $post
     * @return array
     */
    public function update($parm, $post)
    {
        $data=$post;
        $data['invoice_id']=(int)$parm[1];
        $data['paymentType']=$post['paymentType'];
        $this->createReturnSale(collect($data));
    }
    public function confirm($parm,$post)
    {
        $this->Smarty->assign('id', $parm[0]);
        $_SESSION['filterParams'] = $post;

    }
}
