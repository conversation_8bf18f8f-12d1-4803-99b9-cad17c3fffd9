<?php

/**
 * Created by PhpStorm.
 * User: mohammed
 * Date: 22/11/21
 * Time: 12:37 م
 */
use Models\Warehouse\StockUnit;

class bsc_P060_ServiceUnits extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]){
            case 'insert':
                if ($parm[1] === $_SESSION['s_stockUnits_token']){
                    Validation::rules($post, [
                        'name' => 'required',
                    ]);
                    if(Validation::check()) {
                        $this->insert($parm, $post);
                    }
                }
                break;
            case 'delete':
                if ($parm[1] === $_SESSION['s_stockUnits_token']){
                    $this->delete($parm, $post);
                }
                break;

            case 'update':
                if ($parm[1] === $_SESSION['s_stockUnits_token']){
                    Validation::rules($post, [
                        'name' => 'required',
                    ]);
                    if(Validation::check()) {
                        $this->update($parm, $post);
                    }
                }
                break;
        }

        $ServiceUnits = StockUnit::with('productUnit')->where('type',1361)->orderBy('id','desc')->get();
        $this->Smarty->assign('ServiceUnits', $ServiceUnits);

        $_SESSION['s_StockUnits_token'] = Helper::generateToken();
    }

    public function add($parm, $post)
    {
        $_SESSION['s_stockUnits_token'] = Helper::generateToken();
    }

    public function insert($parm, $post)
    {
        $post['type']=1361;
        $stockUnit = new StockUnit($post);

        $stockUnit->save();
        Notification::createdAlert();
        redirect('ServiceUnits/show');
    }

    public function edit($parm, $post)
    {
        $this->Smarty->assign('stockUnit', StockUnit::find($parm[0]));
    }

    public function update($parm, $post)
    {
        $stockUnit = StockUnit::find($parm[2]);

        $stockUnit->fill($post);
        $stockUnit->save();

        Notification::updatedAlert();
        redirect('ServiceUnits/show');

    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', StockUnit::find($parm[0]));

        $_SESSION['s_stockUnits_token'] = Helper::generateToken();
    }

    public function delete($parm, $post)
    {
        $stockUnit = StockUnit::find($parm[2])->delete();

        Notification::deletedAlert();
    }

}