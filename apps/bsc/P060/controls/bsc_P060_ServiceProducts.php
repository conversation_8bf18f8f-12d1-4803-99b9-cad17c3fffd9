<?php

/**
 * Created by PhpStorm.
 * User: mohammed
 * Date: 22/11/21
 * Time: 12:48 م
 * ServiceProducts
 */
use Illuminate\Support\Facades\Validator;
use Models\Warehouse\Group;
use Models\Warehouse\ProductTax;
use Models\Warehouse\Supplier;
use Models\Warehouse\StockUnit;
use Models\Warehouse\Product;
use Models\Warehouse\ProductUnit;
use Models\Warehouse\Tax;
class bsc_P060_ServiceProducts extends Controller
{
    public function show($parm , $post)
    {



        switch ($parm[0]){
            case 'insert':
//                $post['barcode']=(int)$post['barcode'];
//                return $post;
                if ($parm[1] === $_SESSION['s_products_token']){

                    Validation::rules($post, [
                        'name' => 'required',

                    ]);
                    if(Validation::check()) {
                        return $this->insert($parm, $post);
                    }
                }
                break;
            case 'delete':
                if ($parm[1] === $_SESSION['s_products_token']){
                    $this->delete($parm, $post);
                }
                break;

            case 'update':
                if ($parm[1] === $_SESSION['s_products_token']){
                    Validation::rules($post, [
                        'name' => 'required',

                    ]);
                    if(Validation::check()) {
                        $this->update($parm, $post);
                    }
                }
                break;
        }
        $products = Product::with('stockUnit')->with('stockTransaction')->with('stockDepositDetail')->with('stockWithdrawalDetail')->with('supplier')->with('group')->where('type','1361')->orderBy('id','desc')->get();
        $this->Smarty->assign('products', $products);
        $taxes = Tax::where('type',1361)->get();

        $this->Smarty->assign('taxes', $taxes);

        $_SESSION['s_products_token'] = Helper::generateToken();
    }

    public function setting($parm , $post)
    {

        switch ($parm[1]) {

            case 'menu':
                $_SESSION['product_id'] = $parm[0];
                break;

            case 'insert':
                if ($parm[2] === $_SESSION['s_product_units_token']) {
                    $post['product_id'] =$_SESSION['product_id'];
                    $this->insertUnit($post);
                }
                break;
            case 'delete':
                if ($parm[2] === $_SESSION['s_product_units_token']) {
                    $post['id']= $parm[0];
                    $this->deleteUnit($post);
                }
                break;

            case 'update':
                if ($parm[2] === $_SESSION['s_product_units_token']) {
                    $post['id'] =$parm[0];
                    $this->updateUnit($post);
                }

                break;
        }
        $productUnits = ProductUnit::with('product')->with('unit')->where('product_id' , (int)$_SESSION['product_id'])->get();
        $this->Smarty->assign('productUnits', $productUnits);
        $taxes = Tax::all();
        $this->Smarty->assign('taxes', $taxes);
        $this->Smarty->assign('product', Product::find($_SESSION['product_id']));
        $_SESSION['s_product_units_token'] = Helper::generateToken();
    }

    public function add($parm , $post){
        $groups=Group::all();
        $suppliers=Supplier::where('status',23)->get();
        $units=StockUnit::where('type','1361')->get();
        $activationStatus = Setting::getList(19);
        $productType = Setting::getList(298);
        $this->Smarty->assign('activationStatus', $activationStatus);
        $this->Smarty->assign('productType', $productType);
        $this->Smarty->assign('groups',$groups);
        $this->Smarty->assign('units',$units);
        $this->Smarty->assign('suppliers',$suppliers);
        $taxes =  Tax::where('type',1361)->get();
        $this->Smarty->assign('taxes', $taxes);
        $_SESSION['s_products_token'] = Helper::generateToken();
    }

    public function addUnit($parm , $post){
        $all_unit=ProductUnit::where('product_id',$_SESSION['product_id'])->with('unit')->orderBy('created_at','desc')->latest()->get();
        $added_unit =  $all_unit->pluck('stock_unit_id')->toArray();
        $units=StockUnit::whereNotIn('id' , $added_unit)->where('type','1361')->get();
        $last_unit = $all_unit->first();
        $this->Smarty->assign('product', Product::find($_SESSION['product_id']));
        $this->Smarty->assign('last_unit',$last_unit);
        $this->Smarty->assign('units',$units);
        $_SESSION['s_product_units_token'] = Helper::generateToken();
    }

    public function insert($parm , $post){
//        return $post;
        $tax_id=$post['tax_id'];
        $price =$post['price'];
        unset($post['tax_id']);
        unset($post['price']);
        $post['type']=1361;
        $post['supplier_id']=1;
        $product = new Product($post);
        $post['product_no']=0;
        $post['barcode'] = null;
        $product->save();
        $product_no = str_pad((int)$product->id, 6, '0', STR_PAD_LEFT); //// generate 3 zeros before number like 000001
        $product->product_no = $product_no;
        $product->save();
        $product->units()->create([
            'product_id' => $product->id,
            'stock_unit_id' => $product->stock_unit_id,
            'number' => 1,
            'purchase_price' => 0,
            'm_purchase_price' =>0,
            'sale_price' => (float)$price ,
        ]);
//        if($post['barcode']== ''){
//            $post['barcode'] = null;
//
//            $product->save();
//            $product_no = str_pad((int)$product->id, 6, '0', STR_PAD_LEFT); //// generate 3 zeros before number like 000001
//            $product->product_no = $product_no;
//            $product->save();
//            $product->units()->create([
//                'product_id' => $product->id,
//                'stock_unit_id' => $product->stock_unit_id,
//                'number' => 1,
//                'purchase_price' => 1,
//                'm_purchase_price' => 1,
//                'sale_price' => 1,
//            ]);
//        }
//        else {
//            $product = new Product($post);
//            $product->save();
//            $product_no = str_pad((int)$product->id, 6, '0', STR_PAD_LEFT); //// generate 3 zeros before number like 000001
//            $product->product_no = $product_no;
//            $product->save();
//            $product->units()->create([
//                'product_id' => $product->id,
//                'stock_unit_id' => $product->stock_unit_id,
//                'number' => 1,
//                'purchase_price' => 1,
//                'm_purchase_price' => 1,
//                'sale_price' => 1,
//            ]);
//        }
        $this->addTaxes($tax_id,$product->id);
        Notification::createdAlert();
        redirect('ServiceProducts/show');
    }

    public function insertUnit($post){
        $product = new ProductUnit($post);
        $product->save();
        Notification::createdAlert();
    }

    public function edit($parm , $post)
    {
        $groups=Group::all();
        $units=StockUnit::where('type','1361')->get();
        $suppliers=Supplier::where('status',23)->get();
        $activationStatus = Setting::getList(19);
        $productType = Setting::getList(298);
        $this->Smarty->assign('productType', $productType);
        $this->Smarty->assign('activationStatus', $activationStatus);
        $this->Smarty->assign('groups',$groups);
        $this->Smarty->assign('units',$units);
        $this->Smarty->assign('suppliers',$suppliers);
        $taxes = Tax::where('type',1361)->get();
        $this->Smarty->assign('taxes', $taxes);
        $this->Smarty->assign('product',Product::with("productTaxes.tax")->where('id',$parm[0])->with('units')->first());
        $_SESSION['s_products_token'] = Helper::generateToken();


    }
    public function editUnit($parm ,$post)
    {
        $productUnit=ProductUnit::where('id',$parm[0])->with('unit')->first();
        $all_unit=ProductUnit::where('product_id',$_SESSION['product_id'])->where('id','<',$productUnit->id)->with('unit')->orderBy('number','desc')->latest()->get();
        $added_unit =  $all_unit->pluck('unit_id')->toArray();
        $units=StockUnit::whereNotIn('id' , $added_unit)->get();
        $last_unit = $all_unit->first();
        $has_other_unit = $all_unit->count() > 1;

        $this->Smarty->assign('product', Product::find($_SESSION['product_id']));
        $this->Smarty->assign('last_unit',$last_unit);
        $this->Smarty->assign('units',$units);
        $this->Smarty->assign('productUnit',$productUnit);
        $this->Smarty->assign('has_other_unit',$has_other_unit);
    }

    public function update($parm , $post){
//        return $post;
        $tax_id=$post['tax_id'];

        unset($post['tax_id']);
        $product = Product::find($parm[2]);
        $productUnit = ProductUnit::where('product_id',$product->id)->first();
        $product->fill($post);
        $product->save();
        $product->units()->where('product_id',$product->id)->where('stock_unit_id',$productUnit->stock_unit_id)->update([
            'product_id' => $product->id,
            'stock_unit_id' => $product->stock_unit_id,
        ]);
        $this->addTaxes($tax_id,$product->id);
        Notification::updatedAlert();
        redirect('ServiceProducts/show');
    }
    public function updateUnit($post){
        $productUnit = ProductUnit::find($post['id']);
        $productUnit->fill($post);
        $productUnit->save();
        Notification::updatedAlert();
    }
    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', Product::find($parm[0]));
        $_SESSION['s_products_token'] = Helper::generateToken();
    }
    public function confirmUnit($parm, $post)
    {
        $this->Smarty->assign('row', ProductUnit::where('id',$parm[0])->with('unit')->first());
        $_SESSION['s_product_units_token'] = Helper::generateToken();
    }

    public function delete($parm, $post)
    {
        $product = Product::find($parm[2])->delete();
        Notification::deletedAlert();
    }
    public function deleteUnit($post)
    {
        $product = ProductUnit::find($post['id'])->delete();
        Notification::deletedAlert();
    }

    /**
     * @param $taxes
     * @param $product_id
     */
    public function addTaxes($taxes, $product_id){
        $sum=0;
        ProductTax::where('product_id' , $product_id )->delete();
        foreach ($taxes as $tax){
            $sum=$sum+ (int)Tax::find($tax)->tax_rate;
            $productTax=ProductTax::updateOrCreate(['product_id'=>$product_id , 'tax_id'=>$tax,'type'=>1361],
                ['tax_id'=>$tax,'type'=>1361]);


        }
        $product=Product::find($product_id);
        $product->tax=$sum;
        $product->save();

    }
}