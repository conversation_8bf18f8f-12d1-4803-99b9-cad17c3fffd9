<?php

/**
 * Created by PhpStorm.
 * User: developer
 * Date: 28/06/21
 * Time: 03:50 م
 */

use Domain\Warehouse\Repositories\TransactionRepository;
//use Infrastructure\Warehouse\Repositories\EloquentStockWithDrawalRepository;
use Domain\Warehouse\Services\StockWithDrawalRepository;
use Domain\Warehouse\Services\WarehouseEntry;
use Illuminate\Validation\ValidationException;
//use Infrastructure\Warehouse\Repositories\EloquentStockWithDrawalRepository;
use Models\sales\Check;
use Models\sales\Model_processing;
use Models\sales\invoice_Operation;
use Models\sales\payment;
use Models\sales\perfoma_operation;
use Models\sales\PerformInvoiceOperation;
use Models\sales\Qr_operations;
use Models\sales\SalesCustomer;
use Models\sales\SalesGenerateInvoices;
use Models\sales\SalesInvoicesDetails;
use Models\sales\StockStatus;
use Models\Warehouse\Product;
use Models\Warehouse\Stock;
use Notification;

class bsc_P060_Invoices extends Controller
{
    private $performaOperations;
    private $transactionRepository;


    public function show($parm, $post)
    {


        $_SESSION['filterParams'] = $post;
        $invoices = invoice_Operation::getInvoiceNotComeFromPerfoma($post);
        $this->staticData($invoices, $post);
        $this->switchedFunction($parm, $post, 'show');


    }

    /**
     * @param $parm
     * @param $post
     */
    public function InvoiceFromQuotation($parm, $post)
    {

        $_SESSION['filterParams'] = $post;
//        $invoices = perfoma_operation::getPerformaInvoices($post['customer_id'], $post['stock_id'],$post['datefrom'],$post['dateto']);
        $invoices = invoice_Operation::getInvoiceComeFromPerfoma($post);
        $this->staticData($invoices, $post);
        $this->switchedFunction($parm, $post, 'InvoiceFromQuotation');


    }

    /**
     * @param $invoices
     * @param $post
     */
    public function staticData($invoices, $post)
    {
        $this->transactionRepository = app(TransactionRepository::class);
        $types = Setting::getList(295);
        $types = collect($types);
        $_SESSION['type'] = $types->where('id', $post['type'])->first()->translatedName;
        $_SESSION['stock_id'] = Stock::getStockName($post['stock_id']);
        $stocks = Stock::getStockLocationName();
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('types', $types);
        $post['product_id'] = '';
        $post['group_id'] = '';
        $products = Product::getAllProduct();
        $this->isProductInvoice($invoices);
        $this->Smarty->assign('invoices', $invoices);
        $_SESSION['customer_id'] = SalesCustomer::getCustomerName($post['customer']);
        $customers = SalesCustomer::getAllCustomer();
        $this->Smarty->assign('customers', $customers);
        $this->Smarty->assign('isdelivered', StockStatus::isDeliveried);
        $this->Smarty->assign('isnotdelivered', StockStatus::isNotDeliveried);
        $this->Smarty->assign('Cash', payment::Cash);
        $this->Smarty->assign('Bank', payment::Bank);
        $this->Smarty->assign('payed', payment::payed);
        $this->Smarty->assign('unpayed', payment::unpayed);
        $this->Smarty->assign('products', $products);


    }

    public function isProductInvoice($invoices)
    {


        foreach ($invoices as $inv) {
            $invoice = SalesGenerateInvoices::with('stock')->find($inv->id);
            $details = SalesInvoicesDetails::with('product')->with('productUnit.unit')->where('invoice_sale_id', $inv->id)->get();

            $details = collect($details);
            $check = 0;
            foreach ($details as $detl) {
                if ($detl->product->type === 1360) {
                    $check = 1;
                    break;
                }

            }
            if ($check === 1) {
                $invoice->isproduct_invoice = 1;
                $invoice->save();
            }
        }

    }

    /**
     * @param $parm
     * @param $post
     */
    public function switchedFunction($parm, $post, $method)
    {


        switch ($parm[0]) {

            case 'menu';
                if ($method == 'show')
                    $invoices = invoice_Operation::getInvoiceNotComeFromPerfoma($post);
                else {
                    $invoices = invoice_Operation::getInvoiceComeFromPerfoma($post);
                }
                $this->Smarty->assign('invoices', $invoices);
                $this->isProductInvoice($invoices);
                $_SESSION['filterParams'] = $post;

                break;
            case 'filter';
//                $post['customer_id'], $post['stock'],$post['datefrom'],$post['dateto']
                $invoices = invoice_Operation::getInvoiceNotComeFromPerfoma($post);
//                 $invoices;
                $this->Smarty->assign('invoices', $invoices);
                $_SESSION['filterParams'] = $post;

                break;
            case 'filter1';
//                $invoices = perfoma_operation::getPerformaInvoices($post['customer_id'], $post['stock_id'],$post['datefrom'],$post['dateto'],$post['invoice_no'],$post['invoice_value']);
                $invoices = invoice_Operation::getInvoiceComeFromPerfoma($post);
                $this->Smarty->assign('invoices', $invoices);
                $_SESSION['filterParams'] = $post;

                break;
            case 'insert':
//                return $post;
                if ($parm[1] == $_SESSION['s_stock_invoices_token']) {
                    invoice_Operation::Add($parm, $post);
                }
                break;
            case 'delete':
                if ($parm[1] == $_SESSION['s_stock_invoices_token']) {
                    invoice_Operation::delete($parm, $post);
                }
                break;
            case 'details':
                $this->details($parm, $post);
                break;

            case 'update':

                if ($parm[2] == $_SESSION['s_stock_invoices_token']) {
                    invoice_Operation::update($parm, $post);
                }
                break;
            case 'confrimDelivery':
                $this->confrimDelivery($parm, $post);
                break;
            case 'doDeliveryStock':
                $this->doDeliveryStock($parm, $post);
                break;
            case 'edit':
                $this->details($parm, $post);
                break;

        }
    }


    public function doDeliveryStock($parm, $post)
    {
        try {
            $invoice = SalesGenerateInvoices::with('stock')->find($parm[1]);
            $stockWithDrawal = new StockWithDrawalRepository();
            $stock = [];
            $stock['from'] = $invoice->id;
            $stock['created_by'] = $_SESSION['user']->id;
            $stock['stock_id'] = $invoice->stock_id;
            $stock['date'] = $invoice->date;
            $stock['note'] = $invoice->note;
            $stock['data'] = SalesInvoicesDetails::with(['product' => function ($query) {
                $query->where('type', 1360);
            }, 'productUnit.unit'])
                ->whereHas('product', function ($query) {
                    $query->where('type', 1360);
                })
                ->where('invoice_sale_id', $invoice->id)->get();

            $isCreated = $stockWithDrawal->addStockWithDrawal($stock, 'WH');
            if ($isCreated[0]) {

                $invoice->isdelivered = 1;
                $invoice->stock_entry_id = $isCreated[1]->id;
                $invoice->save();
                Notification::createdAlert();
                redirect('Invoices/show');
            }

        } catch (ValidationException $e) {
            Notification::failAlert();
        }


    }

    /**
     * @param $parm
     * @param $post
     */
    public function confrimDelivery($parm, $post)
    {
        $invoice = SalesGenerateInvoices::with('stock')->find($parm[0]);
        $details = SalesInvoicesDetails::with('product')->with('productUnit.unit')->where('invoice_sale_id', $invoice->id)->get();
        $details = collect($details);
        $qr = Qr_operations::generateQR($invoice);
        $customer_id = $invoice->customer_id;
        $customers = SalesCustomer::getAllCustomer()->where('id', $customer_id)->first();
        $this->Smarty->assign('customers', $customers);
        $this->Smarty->assign('qr_path', $qr);
        $this->Smarty->assign('details', $details);
        $this->Smarty->assign('invoice', $invoice);
        $this->Smarty->assign('origanization', Organization::readID($_SESSION['organization']->id));
        $_SESSION['s_stock_invoices_token'] = Helper::generateToken();

    }

    public function details($parm)
    {
        $invoice = SalesGenerateInvoices::with('stock')->find($parm[0]);
        $details = SalesInvoicesDetails::with('product')->with('productUnit.unit')->where('invoice_sale_id', $invoice->id)->get();
        $haveProducts = 0;
        $details = collect($details);
        foreach ($details as $d) {
            if ($d['product']['type'] == 1360)
                $haveProducts++;

        }

        $qr = Qr_operations::generateQR($invoice);
        $customer_id = $invoice->customer_id;
        $customers = SalesCustomer::getAllCustomer()->where('id', $customer_id)->first();


        $this->Smarty->assign('haveProducts', $haveProducts);
        $this->Smarty->assign('customers', $customers);
        $baseUrl = str_replace('https', 'http', SNSO_URL);
        $qr = ENVIRONMENT == "PRODUCTION" ? $qr : str_replace('https', 'http', $qr)  ;
        $this->Smarty->assign('qr_path', $qr);
        $this->Smarty->assign('baseUrl', $baseUrl);
        $this->Smarty->assign('details', $details);
        $this->Smarty->assign('invoice', $invoice);
        $this->Smarty->assign('origanization', Organization::readID($_SESSION['organization']->id));
    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', SalesGenerateInvoices::find($parm[0]));
        $_SESSION['s_stock_invoices_token'] = Helper::generateToken();
    }

    /**
     * @param $parm
     * @param $post
     */
    public function add($parm, $post)
    {

        $invoice_no = Check::findCheckInvoiceNo()->invoice_no;
        $invoice_id = Check::findCheckInvoiceNo()->id;
        $post["invoice_id"] = $invoice_id;
        if ($invoice_no == "") {
            $date = now();
            $yearFormat = date_format($date, "Y");
            $pieces = explode("-", $yearFormat);
            $year = $pieces[0];
            $invoice_no = 'HO' . '-' . $year;

            $post['date'] = now();
            $post['invoice_no'] = 0;
            $post['stock_id'] = 1;
            $post['note'] = "creating invoice number";
            $invoice_no = invoice_Operation::createInvoiceSerial($post);
            $qr = Qr_operations::generateQR2($invoice_no);
            Check::addCheck($invoice_no);
            $stocks = Stock::getStockLocationName();
            $products = Product::getAllProduct();
            $customers = SalesCustomer::getAllCustomer();
            $invoice = invoice_Operation::findInvoices(Check::findCheckInvoiceNo()->invoice_no)->first();
            $haveProducts = $products->where('type', 1361)->count();
            $this->Smarty->assign('haveProducts', $haveProducts);
            $this->Smarty->assign('invoice', $invoice);
            $this->Smarty->assign('products', $products);
            $this->Smarty->assign('stocks', $stocks);
            $this->Smarty->assign('customers', $customers);
            $this->Smarty->assign('invoice_no', $invoice_no);
            $baseUrl = ENVIRONMENT == "PRODUCTION" ? SNSO_URL :  str_replace('https', 'http', SNSO_URL);
            $qr = ENVIRONMENT == "PRODUCTION" ? $qr : str_replace('https', 'http', $qr)  ;
            $this->Smarty->assign('qr_path', $qr);
            $this->Smarty->assign('baseUrl', $baseUrl);


        } else {
            $qr = Qr_operations::generateQR2($invoice_no);
            $stocks = Stock::getStockLocationName();
            $products = Product::getAllProduct();
            $customers = SalesCustomer::getAllCustomer();
            Check::updateCheckToInvoice();
            $invoice = invoice_Operation::findInvoices(Check::findCheckInvoiceNo()->invoice_no)->first();
            $invoice_no = Check::findCheckInvoiceNo()->invoice_no;
            $haveProducts = $products->where('type', 1361)->count();
            $this->Smarty->assign('haveProducts', $haveProducts);
            $this->Smarty->assign('invoice', $invoice);
            $this->Smarty->assign('products', $products);
            $this->Smarty->assign('stocks', $stocks);
            $this->Smarty->assign('customers', $customers);
            $this->Smarty->assign('invoice_no', $invoice_no);
            $this->Smarty->assign('qr_path', $qr);

        }
        $_SESSION['s_stock_invoices_token'] = Helper::generateToken();


    }


    public function print($parm)
    {
        $this->details($parm);
        generatePdf();

    }


    public function edit($parm, $post)
    {
        $Sales_generate_invoices = SalesGenerateInvoices::find($parm[0])->load("InvoicesStockDetail.product.units.unit");
        $theInvoice = $Sales_generate_invoices;
        if ($Sales_generate_invoices->stock_id) {
            $type = Stock::findOrFail($Sales_generate_invoices->stock_id)->type;
            $stockType = Setting::readID($type)->translatedName;
            $this->Smarty->assign('stockType', $stockType);
        }
        $stocks = Stock::getAllStock();
        $customer_id = SalesCustomer::getCustomerName($Sales_generate_invoices->customer_id);
        $qr = Qr_operations::generateQR($Sales_generate_invoices);
        $Sales_generate_invoices = collect($Sales_generate_invoices);
        $products = Product::getAllProduct();
        $data = [];
        $haveProducts = 0;
        foreach ($Sales_generate_invoices["invoices_stock_detail"] as $index => $d) {
            $result = (int)invoice_Operation::getUnitNumber($d["product_unit_id"], $d["product_id"])["data"];
            $data[] = $d;
            if ($d['product']['type'] == 1360)
                $haveProducts++;

        }
        $Sales_generate_invoices["invoices_stock_detail"] = $data;
        $customers = SalesCustomer::getAllCustomer();
        $invoice = $Sales_generate_invoices;
        $this->Smarty->assign('haveProducts', $haveProducts);
        $this->Smarty->assign('invoice', $invoice);
        $this->Smarty->assign('theInvoice', $theInvoice);
        $baseUrl = ENVIRONMENT == "PRODUCTION" ? SNSO_URL :  str_replace('https', 'http', SNSO_URL);
        $qr = ENVIRONMENT == "PRODUCTION" ? $qr : str_replace('https', 'http', $qr)  ;
        $this->Smarty->assign('qr_path', $qr);
        $this->Smarty->assign('baseUrl', $baseUrl);
        $this->Smarty->assign('customer_id', $customer_id);
        $this->Smarty->assign('products', $products);
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('customers', $customers);
        $this->Smarty->assign('Sales_generate_invoices', $Sales_generate_invoices);

        $_SESSION['s_stock_invoices_token'] = Helper::generateToken();
    }

}