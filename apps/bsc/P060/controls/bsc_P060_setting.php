<?php
use Models\sales\SaleAccountSetting;
use Models\Finance\Account;
use Models\sales\Account as SaleAccoount;

/**
 * Created by PhpStorm.
 * User: mohammed kamal
 * Date: 11/10/21
 * Time: 12:20 م
 */
class bsc_P060_setting extends Controller
{
    public function show($parm, $post)
    {

        switch ($parm[0]){
            case 'insert':
                if ($parm[1] === $_SESSION['s_sale_account_setting_token']) {
                    Validation::rules($post, [
                        'account_sale_id' => 'required',
                        'account_cash_id' => 'required',
                        'account_bank_id' => 'required',
                        'account_customer_id' => 'required',
                        'account_tax_id' => 'required',
                        'account_discount_id' => 'required',
                        'account_return_id' => 'required',
                        'account_tax_income_id' => 'required',

                    ]);
                    if (Validation::check()) {
                        $this->insert($parm, $post);
                    }
                }
                break;
            case 'update':
                if ($parm[1] === $_SESSION['s_sale_account_setting_token']) {
                    $this->update($parm, $post);
                }
                break;
        }
        $saleAccountSetting = SaleAccountSetting::with(
            ['accountSale',
                'accountCash',
                'accountBank',
                'accountCustomer',
                'accountTax',
                'accountDiscount',
                'accountReturnIn'
                ])->first();
        $this->Smarty->assign('saleAccountSetting', $saleAccountSetting);
        $_SESSION['s_sale_account_setting_token'] = Helper::generateToken();
    }

    public function add($parm, $post)
    {
        $accounts = cache('accounts');
//        $accounts = Account::currentYear()
//            ->allAccounts()
////            ->where('fin_acc_code' ,'like' , '11%')
//            ->where('fin_acc_level' , '>=' , 4)
////            ->doesntHave('children')
//            ->get();

//        return $accounts;
        $accounts = $accounts->map(function ($account) {
            return collect($account->toArray())
                ->only(['id', 'name', 'code'])
                ->all();
        });
        $this->Smarty->assign('accounts',$accounts) ;
        $this->Smarty->assign('stockAccountSetting',$stockAccountSetting);
        $_SESSION['s_stock_account_setting_token'] = Helper::generateToken();
    }

    public function insert($parm , $post){
        $saleAccountSetting = new SaleAccountSetting();
        $saleAccountSetting->fill($post);
        $saleAccountSetting->save();

        $income = $post['account_tax_income_id'];
        $outcome = $post['account_tax_id'];

        $income_account = new SaleAccoount();
        $income_account->account_id = $income;
        $income_account->type = SaleAccoount::INCOME;
        $income_account->save();

        $outcome_account = new SaleAccoount();
        $outcome_account->account_id = $outcome;
        $outcome_account->type = SaleAccoount::OUTCOME;
        $outcome_account->save();
//        $income_account->type = SaleAccoount::OUTCOME;

        redirect('setting/show');
    }

    public function edit($parm , $post){
        $accounts = cache('accounts');

//        $accounts=Account::currentYear()
////            ->where('fin_acc_code' ,'like' , '11%')
//            ->where('fin_acc_level' , '>=' , 4)
////            ->doesntHave('children')
//            ->get();
        $accounts = $accounts->map(function ($account) {
            return collect($account->toArray())
                ->only(['id', 'name', 'code'])
                ->all();
        });
//        return $accounts;
        $this->Smarty->assign('accounts',$accounts) ;
//        return SaleAccountSetting::find($parm[0]);
        $this->Smarty->assign('saleAccountSetting',SaleAccountSetting::find($parm[0]));
    }

    public function update($parm, $post)
    {
        if($parm[0]){
            $saleAccountSetting = SaleAccountSetting::find($parm[0]);
            if (SaleAccoount::outcome()->where('account_id' , $saleAccountSetting->account_tax_id)->count() == 0 and $saleAccountSetting->account_tax_id != null){
                $outcome_account = new SaleAccoount();
                $outcome_account->account_id = $saleAccountSetting->account_tax_id;
                $outcome_account->type = SaleAccoount::OUTCOME;
                $outcome_account->save();
            }
            if (SaleAccoount::income()->where('account_id' , $saleAccountSetting->account_tax_income_id)->count() == 0 and $saleAccountSetting->account_tax_income_id != null){
                $income_account = new SaleAccoount();
                $income_account->account_id = $saleAccountSetting->account_tax_income_id;
                $income_account->type = SaleAccoount::INCOME;
                $income_account->save();
            }
            $saleAccountSetting->fill($post);
            $saleAccountSetting->save();
            $income = $post['account_tax_income_id'];
            $outcome = $post['account_tax_id'];
            $income_account = new SaleAccoount();
            $income_account->account_id = $income;
            $income_account->type = SaleAccoount::INCOME;
            $income_account->save();

            $outcome_account = new SaleAccoount();
            $outcome_account->account_id = $outcome;
            $outcome_account->type = SaleAccoount::OUTCOME;
            $outcome_account->save();
            Notification::updatedAlert();
        }
        else{
            $saleAccountSetting = new SaleAccountSetting();
            $saleAccountSetting->fill($post);
            $saleAccountSetting->save();
            $income = $post['account_tax_income_id'];
            $outcome = $post['account_tax_id'];

            $income_account = new SaleAccoount();
            $income_account->account_id = $income;
            $income_account->type = SaleAccoount::INCOME;
            $income_account->save();

            $income_account = new SaleAccoount();
            $income_account->account_id = $outcome;
            $income_account->type = SaleAccoount::OUTCOME;
            $income_account->save();
            Notification::updatedAlert();
        }

        redirect('setting/show');
    }
}