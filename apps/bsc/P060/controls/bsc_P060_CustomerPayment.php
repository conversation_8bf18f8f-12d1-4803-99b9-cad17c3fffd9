    <?php
use Domain\Warehouse\Services\WarehouseEntry;
    use Models\sales\ExpenseReimbursementInvoice;
    use Models\sales\ExpenseReimbursementOperation;
    use Models\sales\invoice_Operation;
    use Models\sales\payment;
    use Models\sales\ProductOperation;
    use Models\sales\Qr_operations;
    use Models\sales\SaleAccountSetting;
use Models\sales\SaleCustomerPayment;
    use Models\sales\SaleReturnIn;
    use Models\sales\SaleReturnInDetails;
    use Models\sales\SalesCustomer;
use Models\sales\SalesGenerateInvoices;
    use Models\sales\SalesInvoicesDetails;
    use Models\sales\TransactionsType;

    /**
 * Created by PhpStorm.
 * User: mohammed
 * Date: 24/10/21
 * Time: 11:11 ص
 */
class bsc_P060_CustomerPayment extends Controller
{
    /**
     * @param $parm
     * @param $post
     */
    public function show($parm, $post) {

        switch ($parm[0]) {
            case 'dopayment':
                $this->dopayment($parm,$post);
                break;

            case 'menu':
                $customers = SalesCustomer::getAllCustomer();
                $this->Smarty->assign('customers', $customers);
                $invoices = SaleCustomerPayment::all();
                $this->Smarty->assign('payments_transaction', $invoices);
                unset($_SESSION['searchArray']);
                break;

            case 'search':
                $customers = SalesCustomer::getAllCustomer();
                $this->Smarty->assign('customers', $customers);
                $invoices = SaleCustomerPayment::all();
                $this->Smarty->assign('payments_transaction', $invoices);
                $_SESSION['searchArray'] = $post;
                break;
        }

         $query = SaleCustomerPayment::when($_SESSION['searchArray']['customer_id'], function ($query){
             $query->where('customer_id', $_SESSION['searchArray']['customer_id']);
             })
             ->when($_SESSION['searchArray']['invoice_id'], function($query){
                 $query->where('invoice_id', 'LIKE','%'. SalesGenerateInvoices::where('invoice_no',$_SESSION['searchArray']['invoice_id'].trim())->first()->id  .'%');
             })
             ->when($_SESSION['searchArray']['invoice_payment_status'] != 0 and $_SESSION['searchArray']['invoice_payment_status'] != null , function($query){
                 $query->where('finial_balance',0);
             })
             ->when($_SESSION['searchArray']['invoice_payment_status'] == 0 and $_SESSION['searchArray']['invoice_payment_status'] != null , function($query){
                 $query->where('finial_balance','>',0);
             })
             ->get();
        $this->Smarty->assign('payments', $query);

        $_SESSION['s_CustomerPayment_token'] = Helper::generateToken();


    }


    /**
     * @param $parm
     * @param $post
     */
    public function exshow($parm, $post) {

        switch ($parm[0]) {
            case 'dopayment':
                ExpenseReimbursementOperation::dopayment($parm,$post,$this);
                break;

            case 'menu':
                $invoices = ExpenseReimbursementInvoice::with('Invoice')->get();
                $this->Smarty->assign('payments_transaction', $invoices);
                unset($_SESSION['searchArrayEx']);
                break;
            case 'add':
                ExpenseReimbursementOperation::add($parm,$post,$this);
                break;
            case 'search':
                $customers = SalesCustomer::getAllCustomer();
                $this->Smarty->assign('customers', $customers);
                $invoices = ExpenseReimbursementInvoice::with('Invoice')->get();
                $this->Smarty->assign('payments_transaction', $invoices);
                $_SESSION['searchArrayEx'] = $post;
                break;
        }

        $query = ExpenseReimbursementInvoice::with('Invoice.realinvoice.payment')->when($_SESSION['searchArrayEx']['customer_id'], function ($query){
            $query->where('customer_id', $_SESSION['searchArrayEx']['customer_id']);
        })
            ->when($_SESSION['searchArrayEx']['invoice_id'], function($query){
                $query->where('invoice_id', 'LIKE','%'.SaleReturnIn::where('invoice_no',$_SESSION['searchArrayEx']['invoice_id'].trim())->first()->id  .'%');
            })
            ->when($_SESSION['searchArrayEx']['invoice_payment_status'] != 0 and $_SESSION['searchArrayEx']['invoice_payment_status'] != null , function($query){
                $query->where('finial_balance',0);
            })
            ->when($_SESSION['searchArrayEx']['invoice_payment_status'] == 0 and $_SESSION['searchArrayEx']['invoice_payment_status'] != null , function($query){
                $query->where('finial_balance','>',0);
            })
            ->get();
//        return $query;
        $customers = SalesCustomer::getAllCustomer();
        $this->Smarty->assign('customers', $customers);
        $this->Smarty->assign('payments', $query);

        $_SESSION['s_CustomerPayment_token'] = Helper::generateToken();


    }


    public function  add($parm,$post){
        $payment = SaleCustomerPayment::find($parm[0]);
        $this->Smarty->assign('payment', $payment);
    }
    public function  exadd($parm,$post){
        $payment = ExpenseReimbursementInvoice::find($parm[0]);
        $this->Smarty->assign('payment', $payment);
    }
    public function print_single($parm, $post)
    {
        $payment = SaleCustomerPayment::find($parm[0]);
        $details = SalesInvoicesDetails::with('product')
            ->with('productUnit.unit')
            ->where('invoice_sale_id', $payment->Invoice()->first()->id)
            ->get();
        $qr = Qr_operations::generateQR($payment->Invoice()->first());
        $customers = SalesCustomer::getAllCustomer()->where('id',$payment->Invoice()->first()->customer_id)->first();
        $this->Smarty->assign('customers', $customers);
        $this->Smarty->assign('qr_path', $qr);
        $this->Smarty->assign('payment', $payment);
        $this->Smarty->assign('details', $details);
        $this->Smarty->assign('origanization', Organization::readID($_SESSION['organization']->id));
        $this->Smarty->assign('invoice', $payment->Invoice()->first());

        generatePdf();

    }

    public function exPrint($parm, $post)
    {

        $invoice = SaleReturnIn::find($parm[0]);
        $payment = ExpenseReimbursementInvoice::where('invoice_id', $parm[0])->first();
        $details = SaleReturnInDetails::with('product')
            ->with('productUnit.unit')
            ->where('invoice_sale_id',$parm[0])
            ->get();
        $qr = Qr_operations::generateQR($parm[0]);
        $customers = SalesCustomer::getAllCustomer()->where('id',$payment->customer_id)->first();
        $this->Smarty->assign('customers', $customers);
        $this->Smarty->assign('qr_path', $qr);
//        $this->Smarty->assign('payment', $payment);
        $this->Smarty->assign('details', $details);
        $this->Smarty->assign('origanization', Organization::readID($_SESSION['organization']->id));
//        return $details;
        $this->Smarty->assign('invoice', $invoice);
        $this->Smarty->assign('payment', $payment);

        generatePdf();

    }

    public function dopayment($parm, $post){

        $payment = SaleCustomerPayment::with('Invoice')->find($parm[1]);
        $paymentType=$post['paymentType'];
        if (payment::Bank==$paymentType){
            $this->cashpay($payment);

        }else{
            $this->BankPayment($payment);
        }

        $customers = SalesCustomer::getAllCustomer();
        $this->Smarty->assign('customers', $customers);
        $invoices = SalesGenerateInvoices::where('ispayed', 0)
            ->where('invoice_no', 'like', 'SI%')
            ->where('stock_id', "<>", 0)->get();
        $this->Smarty->assign('invoice', $invoices);



        Notification::createdAlert();
        redirect('CustomerPayment/show');

    }
    public function cashpay($payment){

        $cash_account=SaleAccountSetting::first()->account_cash_id;
        $customer_account=SaleAccountSetting::first()->account_customer_id;

        $narration="عملية سداد لفاتورة مبيعات اجلة";
        $warehouse = new WarehouseEntry();

        $entry =$warehouse->createEntry($payment->finial_balance,$payment->Invoice()->first()->invoice_no,$narration,user('id'));
        /* create cash transaction */
        $warehouse->createTransaction($entry,$cash_account,$payment->finial_balance,0,$narration,user('id'));
        $warehouse->createTransaction($entry,$customer_account,0,$payment->finial_balance,$narration,user('id'));

        $created = $warehouse->postEntry($entry);
        if($created){
            invoice_Operation::directPaymentTrans($payment,$entry);
            return true;
        }
        return false;
    }

    /**
     * @param $payment
     * @return bool
     */
    public function BankPayment($payment){
        $bank_account=SaleAccountSetting::first()->account_bank_id;
        $customer_account=SaleAccountSetting::first()->account_customer_id;

        $narration="عملية سداد لفاتورة مبيعات اجلة ";
        $warehouse = new WarehouseEntry();
        $entry =$warehouse->createEntry($payment->finial_balance,$payment->Invoice()->first()->invoice_no,$narration,user('id'));
        /* create cash transaction */
        $warehouse->createTransaction($entry,$bank_account,$payment->finial_balance,0,$narration,user('id'));
        $warehouse->createTransaction($entry,$customer_account,0,$payment->finial_balance,$narration,user('id'));
        $created = $warehouse->postEntry($entry);
        if($created){
            invoice_Operation::directPaymentTrans($payment,$entry);
            return true;
        }
        return false;

    }

    public function toReimbursement($parm,$post){
        $invoice_id = ExpenseReimbursementInvoice::find($parm[0])->invoice_id;
        redirect('CustomerPayment/exPrint' ,[$invoice_id]);

    }


}