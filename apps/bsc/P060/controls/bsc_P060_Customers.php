<?php

/**
 * Created by PhpStorm.
 * User: developer
 * Date: 09/06/21
 * Time: 07:32 م
 */
use Models\Finance\Account;
use Models\sales\SalesCustomer;

class bsc_P060_Customers extends Controller
{
    public function show($parm, $post)
    {

        switch ($parm[0]) {
            case 'insert':
                if ($parm[1] === $_SESSION['s_customer_token']) {
                    Validation::rules($post, [
//                        'customer_name' => 'required',
//                        'customer_address' => 'required',
//                        'customer_phone' => 'required',
//                        'Vat_tax'=>'required'

                    ]);
                    if (Validation::check()) {
                        $this->insert($parm, $post);
                    }
                }
                break;
            case 'delete':
                if ($parm[1] === $_SESSION['s_customer_token']) {
                    $this->delete($parm, $post);
                }
                break;

            case 'update':
                if ($parm[1] === $_SESSION['s_customer_token']) {
                    Validation::rules($post, [
//                        'customer_name' => 'required',
//                        'customer_address' => 'required',
//                        'customer_phone' => 'required|max_length:12',
//                        'Vat_tax'=>'required',
//                        'responsibly_persian_name'=>'required',
//                        'responsibly_persian_phone'=>'required|max_length:12'
                    ]);
                    if (Validation::check()) {
                        $this->update($parm, $post);
                    }
                }
                break;
        }
        $suppliers = SalesCustomer::all();
        $this->Smarty->assign('Customers', $suppliers);
        $_SESSION['s_customer_token'] = Helper::generateToken();
    }

    public function insert($parm, $post)
    {
//        return $post;
        $SalesCustomer = new SalesCustomer($post);
        $SalesCustomer->save();
        Notification::createdAlert();
        redirect('Customers/show');
    }

    public function delete($parm, $post)
    {
        $Customers = SalesCustomer::find($parm[2])->delete();
        Notification::deletedAlert();
    }

    public function update($parm, $post)
    {
        $SalesCustomer = SalesCustomer::find($parm[2]);
        $SalesCustomer->fill($post);
        $SalesCustomer->save();
        Notification::updatedAlert();

        redirect('Customers/show');
    }

    public function add($parm, $post)
    {
        $activationStatus = Setting::getList(19);
        $accounts = Account::currentYear()
//            ->where('fin_acc_code', 'like', '11%')
            ->where('fin_acc_level', '>=', 4)
//            ->doesntHave('children')
            ->get();
        $accounts = $accounts->map(function ($account) {
            return collect($account->toArray())->only(['id', 'name', 'code'])->all();
        });
        $this->Smarty->assign('accounts', $accounts);
        $this->Smarty->assign('activationStatus', $activationStatus);
        $_SESSION['s_customer_token'] = Helper::generateToken();
    }

    public function edit($parm, $post)
    {
        $activationStatus = Setting::getList(19);
        $accounts = Account::currentYear()
//            ->where('fin_acc_code', 'like', '11%')
            ->where('fin_acc_level', '>=', 4)
//            ->doesntHave('children')
            ->get();
        $accounts = $accounts->map(function ($account) {
            return collect($account->toArray())->only(['id', 'name', 'code'])->all();
        });

        $this->Smarty->assign('accounts', $accounts);
        $this->Smarty->assign('activationStatus', $activationStatus);
        $this->Smarty->assign('Customer', SalesCustomer::find($parm[0]));
    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', SalesCustomer::find($parm[0]));

        $_SESSION['s_customer_token'] = Helper::generateToken();
    }

}