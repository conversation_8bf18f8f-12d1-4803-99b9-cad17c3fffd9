<?php
use Models\sales\CustomerTransaction;
use Models\sales\SalesCustomer;
use Models\sales\SalesGenerateInvoices;
use Models\sales\TransactionsType;

/**
 * Created by PhpStorm.
 * User: mohammed kamal
 * Date: 27/12/21
 * Time: 02:50 م
 * CustomerTransactions
 */
class bsc_P060_CustomerTransactions extends Controller
{
   public function show($parm, $post){

       switch ($parm[0]) {
           case 'print':
               $this->print();
               break;
           case 'menu':
               $customers = SalesCustomer::getAllCustomer();
               $this->Smarty->assign('customers', $customers);
               unset($_SESSION['searchArray']);
               break;

           case 'search':
               $_SESSION['searchArray'] = $post;
               $query =CustomerTransaction::when($_SESSION['searchArray']['customer_id'], function ($query){
                   $query->where('customer_id', $_SESSION['searchArray']['customer_id']);
               })
                   ->when($_SESSION['searchArray']['TransactionsType'], function($query){
                       $query->where('type', $_SESSION['searchArray']['TransactionsType']);
                   })
                   ->when($_SESSION['searchArray']['datefrom'], function ($query)  {
                       $query->where('created_at','>=',$_SESSION['searchArray']['datefrom']);
                   })->when($post['dateto'], function ($query)  {
                       $query->where('created_at','<=',$_SESSION['searchArray']['dateto']);
                   })
                   ->get()->sortBy('created_at' , 'desc');
               $transaction= collect($query)->map(function ($transaction){
                   $transaction['typeName']='';
                   if($transaction->type==TransactionsType::Invoice){
                       $transaction['typeName']='فاتورة مبيعات';
                       $transaction['debit']=(float)$transaction->total_amount;
                       $transaction['credit']=0;
                   }
                   elseif ($transaction->type==TransactionsType::ReturnedInvoice){
                       $transaction['typeName']=' فاتورة مرتجعة';


                       $transaction['credit']=(float)$transaction->total_amount;
                       $transaction['debit']=0;
                   }
                   elseif ($transaction->type==TransactionsType::CustomerPayment){
                       $transaction['typeName']='سداد فاتورة';
                       $transaction['credit']=(float)abs($transaction->total_amount) ;
                       $transaction['debit']=0;
                   }
                   elseif ($transaction->type==TransactionsType::ExpenseReimbursementInvoice){
                       $transaction['typeName']='تسوية سداد فواتير مرتجعة';
                       $transaction['debit']=(float) abs($transaction->total_amount);
                       $transaction['credit']=0;
                   }
                   #CustomerPaymentReturn
                   return $transaction;
               });

               $this->Smarty->assign('transaction', $transaction);
               $customers = SalesCustomer::getAllCustomer();
               $this->Smarty->assign('customers', $customers);
               $_SESSION['searchArray'] = $post;
               break;
            }
       }
   public function search()
   {
       $query =CustomerTransaction::when($_SESSION['searchArray']['customer_id'], function ($query){
           $query->where('customer_id', $_SESSION['searchArray']['customer_id']);
       })
           ->when($_SESSION['searchArray']['TransactionsType'], function($query){
               $query->where('type', $_SESSION['searchArray']['TransactionsType']);
           })
           ->when($_SESSION['searchArray']['datefrom'], function ($query)  {
               $query->where('created_at','>=',$_SESSION['searchArray']['datefrom']);
           })->when($_SESSION['searchArray']['dateto'], function ($query)  {
               $query->where('created_at','<=',$_SESSION['searchArray']['dateto']);
           })
           ->get()->sortBy('created_at' , 'desc');
       $transaction= collect($query)->map(function ($transaction){
           $transaction['typeName']='';
           if($transaction->type==TransactionsType::Invoice){
               $transaction['typeName']='فاتورة مبيعات';
               $transaction['credit']=(float)$transaction->total_amount;
               $transaction['debit']=0;
           }
           elseif ($transaction->type==TransactionsType::ReturnedInvoice){
               $transaction['typeName']=' فاتورة مرتجعة';


               $transaction['debit']=(float)$transaction->total_amount;
               $transaction['credit']=0;
           }
           elseif ($transaction->type==TransactionsType::CustomerPayment){
               $transaction['typeName']='سداد فاتورة';
               $transaction['debit']=(float)abs($transaction->total_amount) ;
               $transaction['credit']=0;
           }
           elseif ($transaction->type==TransactionsType::ExpenseReimbursementInvoice){
               $transaction['typeName']='تسوية سداد فواتير مرتجعة';
               $transaction['credit']=(float) abs($transaction->total_amount);
               $transaction['debit']=0;
           }
           #CustomerPaymentReturn
           return $transaction;
       });
        return $transaction;
   }

    public function print()
    {
//        return $_SESSION['searchArray'];
        $transaction = $this->search();
        $transaction= collect($transaction)->map(function ($transaction){
            $transaction['typeName']='';
            if($transaction->type==TransactionsType::Invoice){
                $transaction['typeName']='فاتورة مبيعات';
                $transaction['debit']=(float)$transaction->total_amount;
                $transaction['credit']=0;
            }
            elseif ($transaction->type==TransactionsType::ReturnedInvoice){
                $transaction['typeName']=' فاتورة مرتجعه';


                $transaction['credit']=(float)$transaction->total_amount;
                $transaction['debit']=0;
            }
            elseif ($transaction->type==TransactionsType::CustomerPayment){
                $transaction['typeName']='سداد فاتورة';
                $transaction['credit']=(float)abs($transaction->total_amount) ;
                $transaction['debit']=0;
            }
//           elseif ($transaction->type==TransactionsType::CustomerPaymentReturn){
//               $transaction['typeName']='سداد فاتورة مرتجعه';
//           }
            elseif ($transaction->type==TransactionsType::ExpenseReimbursementInvoice){
                $transaction['typeName']='تسوية سداد فواتير مرتجعة';
                $transaction['debit']=(float) abs($transaction->total_amount);
                $transaction['credit']=0;
            }
            #CustomerPaymentReturn
            return $transaction;
        });

        $this->Smarty->assign('transaction', $transaction);
        generatePdf();

    }

}