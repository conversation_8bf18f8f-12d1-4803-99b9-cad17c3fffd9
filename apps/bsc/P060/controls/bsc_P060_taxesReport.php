<?php

/**
 * Created by PhpStorm.
 * User: developer
 * Date: 09/06/21
 * Time: 07:32 م
 */
use Models\Finance\Transaction;
use Models\sales\Account;
use Models\sales\SaleAccountSetting;

class bsc_P060_taxesReport extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0])
        {
            case 'menu':
                unset($_SESSION['searchArray']);
                break;
            case 'search':
                $_SESSION['searchArray'] = $post;
                $data = $this->getDetails();
//                return $data;
                $output_account_id = SaleAccountSetting::first()->account_tax_id;
                $input_account_id = SaleAccountSetting::first()->account_return_id;
                $this->Smarty->assign('input_account_id', $input_account_id);
                $this->Smarty->assign('output_account_id', $output_account_id);
                $this->Smarty->assign('data', $data);
                break;
        }
    }
    public function getDetails()
    {
        $outcomes = Account::outcome()->get();
        $incomes = Account::income()->get();
        $query = Transaction::query();
        $query->with('entry');
        $accounts = [];
        if ($incomes->count() != 0 or $outcomes->count() != 0){
            foreach ($incomes as $income){
                $accounts []= $income->account_id;
            }
            foreach ($outcomes as $outcome){
                $accounts []= $outcome->account_id;
            }
        } else {
            $output_account_id = SaleAccountSetting::first()->account_tax_id;
            $input_account_id = SaleAccountSetting::first()->account_tax_income_id;
            $accounts = [ $output_account_id , $input_account_id];
        }

//        return $accounts;
        $data = $query->whereIn('fin_trans_acc_id', $accounts)
            ->when($_SESSION['searchArray']['datefrom'], function ($query) {
                $query->whereHas('entry', function ($query){
                  $query->where('fin_entery_date','>=',$_SESSION['searchArray']['datefrom']);
                });
            })->when($_SESSION['searchArray']['dateto'], function ($query) {
                $query->whereHas('entry', function ($query){
                    $query->where('fin_entery_date','<=',$_SESSION['searchArray']['dateto']);
                });
            })
            ->orderBy('fin_trans_acc_id' , 'desc')
            ->get();


        $data->map(function ($transaction) use($input_account_id , $output_account_id , $outcomes , $incomes){
            if ($transaction->acc_id == $output_account_id or in_array($transaction->acc_id , $outcomes->pluck('account_id')->all()) ){
                $transaction->income = 0;
                if ($transaction->credit > 0){
                    $transaction->outcome = $transaction->credit;
                } else{
                    $transaction->outcome = -1 * $transaction->depit;
                }
            } else {
                $transaction->outcome = 0;
                if ($transaction->depit > 0){
                    $transaction->income = $transaction->depit;
                } else{
                    $transaction->income = -1 * $transaction->credit;
                }
            }
        });

        return $data;
    }
    public function print()
    {
        $output_account_id = SaleAccountSetting::first()->account_tax_id;
        $input_account_id = SaleAccountSetting::first()->account_return_id;
        $this->Smarty->assign('input_account_id', $input_account_id);
        $this->Smarty->assign('output_account_id', $output_account_id);
        $this->Smarty->assign('data', $this->getDetails());
        generatePdf();
    }
}