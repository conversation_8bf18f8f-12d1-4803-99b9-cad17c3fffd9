<?php

/**
 * Created by PhpStorm.
 * User: developer
 * Date: 09/06/21
 * Time: 07:32 م
 */
use Models\Finance\Account;
use Models\sales\PosInvoiceDetails;
use Models\sales\SalesCustomer;
use Models\sales\SalesInvoicesDetails;
use Models\Warehouse\Product;
use Models\sales\perfoma_operation;


class bsc_P060_performaInvoiceReport extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0])
        {
            case 'menu':
                unset($_SESSION['searchArray']);
                break;
            case 'search':
                $_SESSION['searchArray'] = $post;
                $invoicesStatuses = [
                    1 => 'فاتورة مبيعات',
                    2 => 'عرض سعر',
                    3 => 'ملغية'
                ];
                $invoicesDetails = perfoma_operation::getAllInvoicesForReports($post);
                $this->Smarty->assign('invoicesDetails', $invoicesDetails);
                $this->Smarty->assign('invoicesStatuses', $invoicesStatuses);
                break;
        }
        $customers = SalesCustomer::getAllCustomer();
        $invoicesStatuses = [
            1 => 'فاتورة مبيعات',
            2 => 'عرض سعر',
            3 => 'ملغية'];
        $this->Smarty->assign('invoicesStatuses', $invoicesStatuses);
        $this->Smarty->assign('customers', $customers);
    }
    public function print()
    {
        $invoicesDetails = perfoma_operation::getAllInvoicesForReports($_SESSION['searchArray']);
        $this->Smarty->assign('invoicesDetails', $invoicesDetails);
        generatePdf(true);
    }
}