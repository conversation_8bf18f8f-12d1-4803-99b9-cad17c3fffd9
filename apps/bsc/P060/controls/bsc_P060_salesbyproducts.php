<?php

/**
 * Created by PhpStorm.
 * User: developer
 * Date: 09/06/21
 * Time: 07:32 م
 */
use Models\Finance\Account;
use Models\sales\PosInvoiceDetails;
use Models\sales\SalesCustomer;
use Models\sales\SalesInvoicesDetails;
use Models\Warehouse\Product;
use Models\sales\perfoma_operation;


class bsc_P060_salesbyproducts extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0])
        {
            case 'menu':
                unset($_SESSION['searchArray']);
                break;
            case 'search':
                $_SESSION['searchArray'] = $post;
                $this->Smarty->assign('data', $this->getSalesDetails($post));
                break;
        }
        $customers = SalesCustomer::getAllCustomer();
        $products = Product::all();
        $this->Smarty->assign('customers', $customers);
        $this->Smarty->assign('products', $products);
    }
    public function getSalesDetails($post = null)
    {
        $data = SalesInvoicesDetails::with('Invoice.customer')->with('productUnit.unit')->with('product');

        $data = $data->when($post['customer_id'], function ($query) use ($post){
            $query->whereHas('Invoice' , function($query) use ($post){
                $query->where('customer_id', $post['customer_id']);
            });
        })->when($post['product'], function($query) use ($post){
            $query->whereHas('product', function($query) use ($post) {
                $query->whereIn('product_id', $post['product']);
            });
        })->when($post['datefrom'], function ($query) use ($post) {
            $query->where('date','>=',$post['datefrom']);
        })->when($post['dateto'], function ($query) use ($post) {
            $query->where('date','<=',$post['dateto']);
        })->get();
        $query = collect($data)->map(function ($query){
            $query['discount_value'] = ($query['qty'] * $query['sale_price']) * ($query['discount'] / 100);
            $query['price_to_vat'] = $query['qty'] * $query['sale_price'];
            return $query;
        });
        return $query;
    }
    public function print()
    {
        $this->Smarty->assign('data', $this->getSalesDetails($_SESSION['searchArray']));
        generatePdf(true);
    }
}