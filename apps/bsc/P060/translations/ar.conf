
[Setting]
direction = "rtl"
alignment = "right"
valignment = "left"

[Language]
code = "ar"

[Program]
name = "المبيعات ونقاط البيع"

[Menus]
P060 = "الضبط"
Sales = "الضبط"
salessetting = "الضبط"
sales = "المبيعات "
salesmanagement = "إدارة المبيعات "
productservicesetting = "ضبط فاتورة الخدمة   "
SalesReports = "التقارير"

[Operations]
Customers = "العملاء"
Invoice = "انشاء فاتورة"
Invoices = "فاتورة‬ مبيعات‬"
performaInvoice = "عرض سعر"
pointOfSales = "نقاط البيع"
setting = "ضبط حسابات المبيعات"
CustomerPayment = "سداد فواتير العملاء"
InvoicesReturnIn = "مردودات المبيعات"
productservicesetting = "ضبط  الفاتورة الخدمية"
ProductTaxes = "ضبط الضريبة"
ServiceProducts = "إضافة خدمة جديدة"
ServiceUnits = "ضبط وحدات الخدمات"
salesbyproducts = "تقرير المبيعات بالأصناف"
performaInvoiceReport = "تقرير عروض الأسعار"
CustomersReport = "تقرير العملاء"
CustomerTransactions = "تقرير حركة العميل "
taxesReport = "تقرير الضريبة"

[Customers]
p_customer_name = "إسم العميل "
p_customer_address = "العنوان"
p_customer_phone = "رقم الهاتف"
p_customer_email = "البريد الالكتروني"
p_customers_account = "حساب العملاء"
p_customer_responsible_name = "الشخص المسؤل"
p_customer_responsible_phone = "رقم هاتف الشخص المسؤل"
p_cusomer_palce_holder_name = "أدخل اسم العميل"
p_customer_palce_holder_address = "أدخل عنوان العميل "
p_customer_palce_holder_phone = "أدخل رقم هاتف العميل"
p_customer_palce_holder_email = "أدخل البريد الإلكتروني "
p_customer_palce_holder_responsible_name = "أدخل اسم الشخص المسؤل"
p_customer_palce_holder_responsible_phone = "أدخل رقم الشخص المسؤل"
p_validation_filed_required = "لا يمكن ترك هذا الحقل فارغا"
p_validation_field_must_be_email = "رجاء إدخال عنوان بريد إلكتروني صحيح"
p_validation_field_must_be_after = "يجب أن يكون التاريخ المدخل بعد "
p_customer_account = "حساب العملاء"
p_customer = "عميل"
p_customer_vat_tax = "الرقم الضريبي الموحد"
p_customer_palce_holder_responsible_vat_tax = " أدخل الضريبي الموحد"

[Invoices]
p_returned = "مرتجع منها"
P_gnr_details = "تفاصيل المرتجعه"
title1 = "يمكن عمل ارجاع منتج او اكثر او كل الفاتورة بعد اجراء عملية -استلام-"
title2 = " .إجراء عملية إلإستلام يعني التأكد التام بأن العميل قد استلم منتجاته كاملة دون نقص ."
title3 = "لا يمكنك ارجاع منتجات قبل تسليمها للعميل عن طريق اجراء-استلام- . تذكر ان الفاتورة يمكن ان تكون خدمية او منتجات او الاثنين معا . في حال ان الفاتورة اجلة الدفع يجب ان تكون طريقة السداد بالذهاب الي قائمة -إدارة المبيعات - سداد فواتير العملاء  . "
p_std_stock = "المستودع"
p_customer = "العميل"
p_std_pay_date = "تاريخ التوريد"
p_payment_method = "طريقة السداد"
p_cash = "سداد نقدي مباشر"
p_bank = "سداد بالبنك"
p_postment = "سداد اجل "
p_std_invoice_no = "رقم الفاتوره "
p_std_date = "تاريخ إصدار الفاتورة"
p_std_comment = " تعليق"
p_std_from_date = "من تاريخ"
p_std_to_date = "الي تاريخ"
p_std_product = "اسم المنتج"
p_std_unit = "الوحده"
p_std_qty = "الكمية"
p_sale_price = "سعر البيع"
p_discount = "الخصم %"
p_value_of_discount = "قيمة الخصم"
p_sale_taxi = "الضريبة %"
p_value_of_taxi = "قيمة الضريبة "
p_total_mount = "إجمالي المبلغ"
p_actual_pay_amount = "إجمال المبلغ"
p_total_tax_amount = "إجمالي الضريبه "
p_total_discount_amount = "إجمالي  الخصم"
p_actual_amount = "المجموع"
p_actual_pay = "  ‫إجمالي ‫المبلغ ‫المستحق‬‬‬"
p_actual_pay_amount_without_discount = "‫ ‫الإجمالي الخاضع‬  للضريبة‬ ‫‬"
p_std_current_qty = "الكمية الحالية"
p_std_total_qty = "المتوفر"
p_total_invoice_no = "جملة الفاتورة"
p_std_stock_deposit_print = "فاتورة ضريبية"
p_print = "طباعة فاتورة"
p_directInvoice = "فواتير منشأة مباشرة"
p_byQuotion_invoice = "فواتير صادره من عرض السعر"
p_std_invoice_quotation_no = "رقم العرض "
P_delivery_stock = "إستلام "
p_customer_vat_tax = "رقم تسجيل الضريبه القيمة المضافة"
p_vendor = "الشركة المورده"
p_delivery = "فاتورة ضريبة ﻷستلام الاصناف"
p_pay = "السداد"
p_ispayed = "تم السداد"
p_isnotpayed = "لم يتم السداد"
P_make_sure = "تم تأكيد استلام الفاتورة"
P_delivery_stock1 = "تأكيدإستلام الاصناف"
p_isdelevered = "تم التسليم"
p_isproductservice = "فاتورة خدمية"
p_returninvoice = "إرجاع منتج"
p_seller = "المورد"
p_total_exclude = "غير شامل ضريبة القيمة المضافة"
p_customer_name = "الإسم"
p_customer_address = "العنوان"
p_customer_phone = "رقم الهاتف"
P_city = "المنطقه"
p_vallage = "المدينة"
p_country = "الدوله"

[performaInvoice]
P_city = "المنطقه"
p_vallage = "المدينة"
p_country = "الدوله"
p_seller = "المورد"
p_total_exclude = "غير شامل ضريبة القيمة المضافة"
p_customer_vat_tax = "رقم تسجيل الضريبه القيمة المضافة"
p_std_stock = "المستودع"
p_customer = "العميل"
p_customer_name = "الإسم"
p_customer_address = "العنوان"
p_customer_phone = "رقم الهاتف"
p_std_invoice_no = "رقم الفاتوره "
p_std_invoice_quotation_no = "رقم العرض "
p_std_date = "تاريخ إصدار العرض"
p_std_comment = " تعليق"
p_std_from_date = "من تاريخ"
p_std_to_date = "الي تاريخ"
p_std_product = "اسم المنتج"
p_std_unit = "الوحده"
p_std_qty = "الكمية"
p_sale_price = "سعر البيع"
p_discount = "الخصم %"
p_value_of_discount = "قيمة الخصم"
p_sale_taxi = "الضريبة %"
p_value_of_taxi = "قيمة الضريبة "
p_total_mount = "إجمالي المبلغ"
p_actual_pay_amount = "إجمال المبلغ"
p_total_tax_amount = "إجمالي الضريبه "
p_total_discount_amount = "إجمالي  الخصم"
p_actual_amount = "المجموع "
p_actual_pay = "  ‫إجمالي ‫المبلغ ‫المستحق‬‬‬"
p_actual_pay_amount_without_discount = "‫ ‫الإجمالي الخاضع‬  للضريبة‬ ‫‬"
p_std_current_qty = "الكمية الحالية"
p_std_total_qty = "المتوفر"
p_total_invoice_no = "جملة الفاتورة"
p_std_stock_deposit_print = " عرض سعر "
p_print = "طباعة فاتورة"
p_print1 = "طباعة "
p_std_enddate = " تاريخ إنتهاء العرض"
p_makeInvoice = "إنشاء فاتورة مبيعات"
p_invoice_end = "إنتهاء العرض"
p_PerformaInvoices = "عرض سعر"
p_invoice_cancel = "فاتورة"
p_invlaid_invoice = "عروض اسعار منتهية الصلاحية"
p_invoice = "عرض سعر محول للمبيعات"
p_payment_method = "طريقة السداد"
p_cash = "سداد نقدي مباشر"
p_bank = "سداد بالبنك"
p_postment = "سداد اجل "
p_std_details= "تفاصيل"
p_created_by = "منشئ بواسطة"

[pointOfSales]
p_product_List = "قائمة الاصناف "
p_sale_pos = "بيع"
p_std_product = "اسم المنتج"
p_std_unit = "الوحده"
p_std_qty = "الكمية"
p_sale_price = "سعر البيع"
p_actual_pay_amount = "إجمال المبلغ"
p_total_tax_amount = "إجمالي الضريبه المضافة"
p_total_discount_amount = "إجمالي  الخصم"
p_actual_amount = "إجمالي المنتج"
p_actual_pay = "  ‫إجمالي ‫المبلغ ‫المستحق‬‬‬"
p_actual_pay_amount_without_discount = "‫ ‫الإجمالي الخاضع‬  للضريبة‬ ‫‬"
p_total_mount = "إجمالي المبلغ"
p_std_stock = "المستودع"
p_customer = "العميل"
p_std_invoice_no = "رقم الفاتوره "
p_std_date = "التاريخ"
p_std_comment = " تعليق"
p_std_from_date = "من تاريخ"
p_std_to_date = "الي تاريخ"
p_discount = "الخصم %"
p_value_of_discount = "قيمة الخصم"
p_sale_taxi = "الضريبة %"
p_value_of_taxi = "قيمة الضريبة "
p_std_current_qty = "الكمية الحالية"
p_std_total_qty = "المتوفر"
p_total_invoice_no = "جملة الفاتورة"
p_std_stock_deposit_print = "فاتورة مبيعات لفاتورة ضريبة"
p_print = "طباعة فاتورة"
p_directInvoice = "فواتير منشأة مباشرة"
p_byQuotion_invoice = "فواتير صادره من عرض السعر"
p_std_invoice_quotation_no = "رقم العرض "

[setting]
p_first_setup = "ضبط الحسابات للمرة الاولي"
p_accountSale = " حساب المبيعات"
p_accountCash = "حساب الكاش"
p_accountBank = "حساب البنك"
p_accountCustomer = "حساب العملاء"
p_accountTaxOutcome = "مخرجات الضرائب"
p_accountTaxIncome = "مدخلات الضرائب"
p_accountDiscount = "حساب الخصومات"
p_accountReturn = "حساب مردودات المبيعات"

[CustomerPayment]
p_std_expens_print = "إشعار تسوية مستحقات العملاء"
p_Expense_Reimbursement_Invoice = "تسوية مستحقات العملاء"
p_due_payment = "سداد فواتير مستحقه"
p_std_Payment_print = " سداد فاتورة مبيعات"
p_std_product = "اسم المنتج"
p_std_unit = "الوحده"
p_std_qty = "الكمية"
p_sale_price = "سعر البيع"
p_discount = "الخصم %"
p_value_of_discount = "قيمة الخصم"
p_sale_taxi = "الضريبة %"
p_value_of_taxi = "قيمة الضريبة "
p_total_mount = "إجمالي المبلغ"
p_actual_pay_amount = "إجمال المبلغ"
p_total_tax_amount = "إجمالي الضريبه "
p_total_discount_amount = "إجمالي  الخصم"
p_actual_amount = "المجموع "
p_actual_pay = "  ‫إجمالي ‫المبلغ ‫المستحق‬‬‬"
p_actual_pay_amount_without_discount = "‫ ‫الإجمالي الخاضع‬  للضريبة‬ ‫‬"
p_std_current_qty = "الكمية الحالية"
p_std_total_qty = "المتوفر"
p_total_invoice_no = "جملة الفاتورة"
p_std_stock_deposit_print = " عرض سعر لفاتورة ضريبية"
p_std_pay_date = "تاريخ التوريد"
p_std_date = "التاريخ"
p_std_invoice_no = "رقم الفاتوره "
p_ispayed = "تم السداد"
p_isnotpayed = "لم يتم السداد"
p_seller = "المورد"
p_total_exclude = "غير شامل ضريبة القيمة المضافة"
p_customer_vat_tax = "رقم تسجيل الضريبه القيمة المضافة"
p_std_stock = "المستودع"
p_customer = "اسم العميل"
p_customer_name = "الإسم"
p_customer_address = "العنوان"
p_customer_phone = "رقم الهاتف"
P_city = "المنطقه"
p_vallage = "المدينة"
p_country = "الدوله"
p_related_entry = "مكتمل السداد"
P_invoice = "رقم الاشعار"
p_total_amount = "جملة الفاتورة المستحقه"
p_paid_amount = "المدفوع منه"
p_note = "التعليق"
P_balance = "المتبقي"
p_payment_method = "طريقة السداد"
p_cash = "سداد نقدي مباشر"
p_bank = "سداد بالبنك"
p_postment = "سداد اجل "
P_do_pay = "تأكيد سداد الفاتورة"
p_date = "التاريخ"
p_pay = "السداد"
p_real_invoice="الفاتورة قبل الإرجاع"
p_return_invoice ="الفاتورة بعد الإرجاع"
p_payment_status="حالة التسويه"

[InvoicesReturnIn]
p_ispayed = "تم السداد"
p_isnotpayed = "لم يتم السداد"
p_seller = "المورد"
p_total_exclude = "غير شامل ضريبة القيمة المضافة"
p_customer_vat_tax = "رقم تسجيل الضريبه القيمة المضافة"
p_std_stock = "المستودع"
p_customer = "العميل"
p_customer_name = "الإسم"
p_customer_address = "العنوان"
p_customer_phone = "رقم الهاتف"
P_city = "المنطقه"
p_vallage = "المدينة"
p_country = "الدوله"
p_std_stock_Rprint = "إشعار دائن "
p_std_pay_date = "تاريخ التوريد"
p_payment_method = "طريقة السداد"
p_cash = "سداد نقدي مباشر"
p_bank = "سداد بالبنك"
p_postment = "سداد اجل "
p_std_invoice_no = "رقم الفاتوره "
p_std_date = "التاريخ"
p_std_comment = " تعليق"
p_std_from_date = "من تاريخ"
p_std_to_date = "الي تاريخ"
p_std_product = "اسم المنتج"
p_std_unit = "الوحده"
p_std_qty = "الكمية"
p_sale_price = "سعر البيع"
p_discount = "الخصم %"
p_value_of_discount = "قيمة الخصم"
p_sale_taxi = "الضريبة %"
p_value_of_taxi = "قيمة الضريبة "
p_total_mount = "إجمالي المبلغ"
p_actual_pay_amount = "إجمال المبلغ"
p_total_tax_amount = "إجمالي الضريبه "
p_total_discount_amount = "إجمالي  الخصم"
p_actual_amount = "المجموع"
p_actual_pay = "  ‫إجمالي ‫المبلغ ‫المستحق‬‬‬"
p_actual_pay_amount_without_discount = "‫ ‫الإجمالي الخاضع‬  للضريبة‬ ‫‬"
p_std_current_qty = "الكمية الحالية"
p_std_total_qty = "المتوفر"
p_total_invoice_no = "جملة الفاتورة"
p_std_stock_deposit_print = "فاتورة مبيعات"
p_print = "طباعة فاتورة"
p_directInvoice = "فواتير مبيعات"
p_byQuotion_invoice = "فواتير صادره من عرض السعر"
p_std_invoice_quotation_no = "رقم العرض "
P_delivery_stock = "إستلام "
p_vendor = "الشركة المورده"
p_delivery = "فاتورة ضريبة ﻷستلام الاصناف"
p_pay = "السداد"
P_returnIn = "ارجاع منتجات"
p_ReturnInvoice = "فواتير مرتجعة"
P_invoice = "رقم الفاتورة"
gnr_confirm = "تأكيد"

[ProductTaxes]
p_Taxes = "ضريبة"
p_tax_name = "إسم الضريبة"
p_tax_description = "الوصف"
p_tax_rate = "النسبة بالمائة %"
p_tax_palce_holder_name = "ادخل اسم المجموعة"
p_gro_palce_holder_description = ""

[ServiceUnits]
p_unt_name = "وحدة "
p_unt_description = "الوصف"
p_unt_stockUnit = "وحدة القياس "
p_unt_palce_holder_name = "ادخل اسم الوحدة"
p_unt_palce_holder_description = "ادخل وصف الوحدة"

[ServiceProducts]
p_pro_name = "الاسم "
p_std_product_no = "رقم الخدمة"
p_pro_scientific_name = "الاسم العلمي"
p_pro_supplier = "المورد"
p_std_product_no_genrate = " تلقائي"
p_pro_product_unit = "وحدات الخدمات "
p_pro_product = "الخدمات"
p_pro_product_setting = "ضبط الصنف"
p_pro_code = "الباركود"
p_pro_tax = "الضريبة"
p_pro_group = "المجموعة"
p_pro_description = "الوصف"
p_pro_palce_holder_name = "ادخل الاسم "
p_pro_palce_holder_tax = "ادخل الضريبة "
p_pro_palce_holder_description = "ادخل الوصف"
p_pro_palce_holder_santific_name = "ادخل الاسم العلمي"
p_pro_unit = "الوحدة"
p_pro_parent_unit = "الوحدة الاب"
p_pro_qty = "العدد"
p_pro_purch_price = "سعر التكلفة"
p_pro_m_purch_price = "متوسط سعر التكلفة"
p_pro_sale_price = "سعر الخدمة"
p_pro_palce_holder_pur_price = "ادخل سعر التكلفة "
p_pro_palce_holder_sale_price = "ادخل سعر البيع "
p_pro_palce_holder_m_pur_price = "ادخل متوسط سعر التكلفة "
p_pro_palce_holder_qty = "ادخل العدد "
p_tax_selected = "اختر الضرائب المطلوبه"
p_pro_service_price = "سعر الوحدة"

[Impeded]
Customers_customer_name_required_msg = "هذا الحقل لابد من اختياره"
Customers_customer_address_required_msg = "هذا الحقل لابد من اختياره"
Customers_customer_phone_required_msg = "هذا الحقل لابد من اختياره"
Invoices_date_required_msg = ""
Invoices_date_before_or_equal_msg = ""
Invoices_stock_id_required_msg = "هذا الحقل لابد من اختياره"
Invoices_customer_required = "هذا الحقل لابد من اختياره"
Invoices_customer_id_required_msg = ""
performaInvoice_invoice_end_required_msg = ""
performaInvoice_invoice_end_before_or_equal_msg = ""
Customers_Vat_tax_required_msg = ""
setting_account_sale_id_required_msg = ""
setting_account_cash_id_required_msg = ""
setting_account_bank_id_required_msg = ""
setting_account_customer_id_required_msg = ""
setting_account_tax_id_required_msg = ""
setting_account_discount_id_required_msg = ""
Customers_customer_name_regex_msg = "اسم العميل لا يقبل ارقام"
Customers_customer_address_regex_msg = "عنوان العميل لا يقبل ارقام"
Customers_customer_phone_numeric_msg = "هاتف العميل لا يقبل حروف"
Customers_customer_phone_min_msg = "هاتف العميل يحتوي على 10 ارقام على الافل"
Customers_customer_phone_max_msg = "هاتف العميل يحتوي على 12 رقم على الاكثر"
Customers_responsibly_persian_name_required_msg = "حقل الشخص المسؤول لابد من اختياره"
Customers_responsibly_persian_name_regex_msg = "حقل الشخص المسؤول لا يقبل ارقام"
Customers_responsibly_persian_phone_required_msg = "هاتف الشخص المسؤول لا بد من اختياره"
Customers_responsibly_persian_phone_numeric_msg = "هاتف الشخص المسؤول لا يقبل حروف"
Customers_responsibly_persian_phone_min_msg = "هاتف الشخص المسؤول يحتوي على 10 ارقام على الاقل"
Customers_responsibly_persian_phone_max_msg = "هاتف الشخص المسؤول يحتوي على 12 رقم على الاكثر"
Customers_customer_name_msg = ""
Customers_customer_phone_regex_msg = ""
Customers_customer_name_numeric_msg = ""
Customers_customer_phone_max_length_msg = ""
Customers_responsibly_persian_phone_max_length_msg = ""
ServiceUnits_name_required_msg = ""
ProductTaxes_name_required_msg = ""
ServiceProducts_name_required_msg = ""

[salesbyproducts]
p_product = "المنتج"
p_products = "المنتجات"
p_customer = "العميل"
p_from_date = "من تاريخ"
p_to_date = "الى تاريخ"
p_date = "التاريخ"
p_quantity = "الكمية"
p_sale_price = "سعر الوحدة"
p_discount = "الخصم"
p_discount_percent = "نسبة الخصم"
p_discount_value = "قيمة الخصم"
p_vat = "الضريبة"
p_total = "الاجمالي"
p_total_with_vat = "الاجمالي بعد الضريبة"
p_unit = "الوحدة"
p_sales_by_products_report = "تقرير المبيعات بالأصناف"
p_sum = "السعر الخاضع للضريبة"
p_no_data_to_print = "لا توجد بيانات للطباعة"
p_select_from_list_bellow_multiple = "يمكن إختيار أكثر من منتج"

[performaInvoiceReport]
p_status = "حالة الفاتورة"
p_customer = "العميل"
p_from_date = "من تاريخ"
p_to_date = "الى تاريخ"
p_invoice_qutation_no = "رقم عرض السعر"
p_invoice_no = "رقم الفاتورة"
p_vat = "الضريبة"
p_discount = "الخصم"
p_total = "الاجمالي"
p_date = "التاريخ"
p_performa_invoice_report = "تقرير عرض السعر"
p_invoice_type = "نوع الفاتورة"
p_sales_invoice = "فاتورة مبيعات"
p_performa_invoice = "عرض سعر"
p_cancelled = "ملغية"
p_sub_product_total = "المجموع(غير شامل الضريبة والخصم)"
p_sum_total_with_discount = "اجمالي الخاضع للضريبة"
p_no_data_to_print = "لا توجد بيانات للطباعة"

[CustomersReport]
p_customer_name = "إسم العميل "
p_customer_address = "العنوان"
p_customer_phone = "رقم الهاتف"
p_customer_vat_tax = "الرقم الضريبي الموحد"
p_customers_report = "تقرير العملاء"

[CustomerTransactions]
p_customer_movement_trans = "تقرير حركة العميل "
p_invoice = "فواتير مبيعات"
p_return_invoice = "فواتير مرتجعة"
p_customer_payment = " سداد فواتير مستحقه"
p_export_reimburse = "تسوية سداد فواتير مرتجعة"
p_reference = " المصدر "
p_related_entry = "القيد المحاسبي للمصروف"
p_product = "المنتج"
p_customer = "العميل"
p_from_date = "من تاريخ"
p_to_date = "الى تاريخ"
p_date = "التاريخ"
p_total = "الاجمالي"
p_std_invoice_no = "رقم الفاتوره "
p_sales_by_products_report = "تقرير المبيعات بالأصناف"
p_sum = "السعر الخاضع للضريبة"
p_type = "نوع العملية"
p_debit = "مدين"
p_credit = "دائن"
p_sales_invoice = "فاتورة مبيعات"
p_return_sales_invoice = "فاتورة مرتجعة"
p_invoice_pay = "سداد فاتورة"
p_return_invoice_pay = "سداد فاتورة مرتجعة"
p_return_invoice_outlay = "تسوية سداد فواتير مرتجعة"
p_no_data_to_print = "لا توجد بيانات للطباعة"
#p_difference = "الفرق"
#p_requester_name = "منشئ الطلب"

[taxesReport]
p_from_date = "من تاريخ"
p_to_date = "الى تاريخ"
p_output = "مخرجات الضريبة"
p_input = "مدخلات الضريبة"
p_tax_all = "المستحق الضريبي"
p_comment = "نوع الفاتورة"
p_total = "الاجمالي"
p_reference = "المرجع"
p_taxes_report = "تقرير الضريبة"
p_sales = "مبيعات"
p_returned = "مردود"
p_date = "التاريخ"
p_no_data_to_print = "لا توجد بيانات للطباعة"
p_entry = "قيد"
