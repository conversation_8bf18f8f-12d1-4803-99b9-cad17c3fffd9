
[Setting]
direction = "ltr"
alignment = "left"
valignment = "right"

[Language]
code = "en"

[Program]
name = "Client Manager"
about = "Program for manager  and owner of the system"

[Menus]
clientManagerDashboard = "Client Manager Dashboard"

[Operations]
clientManagerDashboard = " Portal of the facility manager"

[clientManagerDashboard]
general_manager_purposes = " The manager of the facility has the highest authority in the SNSO Resource Management System"
it_manager_purposes = "The Chief Technology Officer is technically responsible for SNSO and its first operational steps and daily routine technical procedures"
p_edit_client_data = " Update facility data"
p_show_appraisal_form = "Appraisal on Using snso erp"
p_change_it_manager = " Change the Technical Manager"
p_delete_all_user_privileges = " Delete all users' permissions"
p_org_name = "Facility Name"
define = "Definition "
p_view = "view"
p_mession = "mission"
p_aim = "aim"
atention_before_deletion = "CAUTION: Deleting user privileges is a permanent process that can not be undone. Deleting privileges does not affect the powers of the facility manager or technical manager"
delete_prvs_confirmation = "Are you shure about deleting all users' permissions?"
