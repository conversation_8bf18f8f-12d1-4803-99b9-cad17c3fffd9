
[Setting]
direction = "rtl"
alignment = "right"
valignment = "left"

[Language]
code = "ar"

[Program]
name = "المخازن و المستودعات "
about = "برنامج لإدارة المخازن"

[Menus]
P057 = "الضبط"
StockProcesses = "عمليات المخزون"
Reports = "التقارير"

[Operations]
locations = "المواقع"
stocks = "المستودعات"
StockUnits = "وحدات القياس والتعبئة"
Groups = "مجموعات الاصناف"
Suppliers = "الموردون"
Products = "المنتجات والخدمات "
StockDeposits = "استلام الاصناف والمنتجات "
StockDepositsReport = "تقرير الاصناف المستلمة"
DepositsReport = "تقرير المخزون"
StockWithdrawals = "صرف الاصناف والمنتجات"
StockWithdrawalsReport = "تقرير الأصناف المسحوبه"
StockMovementReport = "تقرير حركة المخزون "
InventoryAndStoreMatching = "جرد ومطابقة المخزن"
StockDamages = "إتلاف المخزون"
MovementBetweenStocks = "التحويل بين المستودعات"
StockAccountSetting = "ضبط حسابات المخزون "
Taxes = "الضرائب"

[locations]
loc_name = "إسم الموقع"
loc_description = "الوصف"
loc_location = "موقع"
loc_palce_holder_name = "ادخل اسم الموقع"
loc_palce_holder_description = "ادخل وصف الموقع"

[stocks]
p_stok_name = "إسم المستودع"
p_stok_type = "نوع المستودع"
p_stok_account = " حساب المستودع"
p_stok_location = " الموقع"
p_stok_stock = "مستودع"
p_stok_palce_holder_name = "ادخل اسم المستودع"
p_stok_palce_holder_type = "اختر نوع المستودع"
p_stok_palce_holder_location = "اختر الموقع"

[StockAccountSetting]
p_stok_account_cost = "حساب تكلفة البضاعة المباعة"
p_stok_account_consumable = "حساب مصروف مستهلكات"
p_stok_account_recancelation = "حساب تسوية جرد المخزون"
p_stok_account_damage = "حساب اتلاف المخزون"
p_stock_withdrawal = "صرف اصناف"
p_stock_recancelation = "جرد ومطابقة المخزون"
p_stock_damage = "اتلاف المخزون"

[StockUnits]
p_unt_name = "وحدة التعبئة"
p_unt_description = "الوصف"
p_unt_stockUnit = "وحدة القياس والتعبئة"
p_unt_palce_holder_name = "ادخل اسم الوحدة"
p_unt_palce_holder_description = "ادخل وصف الوحدة"

[Groups]
p_gro_name = "المجموعة"
p_gro_description = "الوصف"
p_gro_group = "المجموعة"
p_gro_palce_holder_name = "ادخل اسم المجموعة"
p_gro_palce_holder_description = "ادخل وصف المجموعة"

[Suppliers]
p_supp_name = "اسم المورد"
p_supp_responsible_name = "الشخص المسؤول"
p_supp_responsible_phone = "رقم هاتف الشخص المسؤول"
p_supp_address = "العنوان"
p_supp_phone = "رقم الهاتف"
p_supp_email = "البريد الالكتروني"
p_supp_supplier = "الموردون"
p_stok_account = "اختر الحساب"
p_supp_palce_holder_name = "ادخل اسم المورد"
p_supp_palce_holder_address = "ادخل عنوان المورد"
p_supp_palce_holder_phone = "ادخل هاتف المورد"
p_supp_palce_holder_email = "ادخل البريد الالكتروني للمورد"
p_supp_palce_holder_responsible_name = "ادخل اسم الشخص المسؤول"
p_supp_palce_holder_responsible_phone = "ادخل هاتف الشخص المسؤول"
p_customer_vat_tax = "الرقم الضريبي الموحد"
p_customer_palce_holder_responsible_vat_tax = " أدخل الضريبي الموحد"

[Products]
p_pro_name = "الاسم "
p_std_product_no = "رقم المنتج"
p_pro_scientific_name = "الاسم العلمي"
p_pro_supplier = "المورد"
p_std_product_no_genrate = " تلقائي"
p_pro_product_unit = "وحدات المنتجات "
p_pro_product = "المنتجات "
p_pro_product_setting = "ضبط الصنف"
p_pro_code = "الباركود"
p_pro_tax = "الضريبة"
p_pro_group = "المجموعة"
p_pro_description = "الوصف"
p_pro_palce_holder_name = "ادخل الاسم "
p_pro_palce_holder_code = "ادخل الباركود "
p_pro_palce_holder_tax = "ادخل الضريبة "
p_pro_palce_holder_description = "ادخل الوصف"
p_pro_palce_holder_santific_name = "ادخل الاسم العلمي"
p_pro_unit = "الوحدة"
p_pro_parent_unit = "الوحدة الاب"
p_pro_qty = "العدد"
p_pro_purch_price = "سعر التكلفة"
p_pro_m_purch_price = "متوسط سعر التكلفة"
p_pro_sale_price = "سعر البيع"
p_pro_palce_holder_pur_price = "ادخل سعر التكلفة "
p_pro_palce_holder_sale_price = "ادخل سعر البيع "
p_pro_palce_holder_m_pur_price = "ادخل متوسط سعر التكلفة "
p_pro_palce_holder_qty = "ادخل العدد "
p_tax_selected = "اختر الضرائب المطلوبه"

[StockDeposits]
p_std_stock_deposit = "استلام الاصناف"
p_std_stock_deposit_print = "سند استلام الاصناف"
p_std_invoice_no = "رقم سند الاستلام "
p_std_invoice_creater = "منشئ السند"
p_std_product = "المنتج"
p_std_unit = "الوحدة"
p_std_qty = "الكمية"
p_std_avilable_qty = "الكمية المتوفرة"
p_std_stock = "المستودع"
p_std_from_date = "التاريخ من"
p_std_to_date = "الي"
p_std_stock_type = "نوع المستودع"
p_std_date = "التاريخ"
p_std_comment = "تعليق"
p_std_details = "التفاصيل"
p_std_palce_holder_comment = "اترك تعليقا هنا"
p_std_palce_holder_qty = "ادخل العدد"

[StockWithdrawals]
p_std_from = "هذه الفاتورة من"
p_std_stock_deposit = "صرف الاصناف"
p_std_stock_deposit_print = "سند صرف الاصناف"
p_std_invoice_no = "رقم سند الصرف "
p_std_invoice_creater = "منشئ السند"
p_std_product = "المنتج"
p_std_unit = "الوحدة"
p_std_qty = "الكمية"
p_std_avilable_qty = "الكمية المتوفرة"
p_std_total_qty = "الكمية الكلية"
p_std_stock = "المستودع"
p_std_from_date = "التاريخ من"
p_std_to_date = "الي"
p_std_stock_type = "نوع المستودع"
p_std_date = "التاريخ"
p_std_comment = "تعليق"
p_std_details = "التفاصيل"
p_std_palce_holder_comment = "اترك تعليقا هنا"
p_std_palce_holder_qty = "ادخل العدد"

[MovementBetweenStocks]
p_std_stock_deposit = "تحويل بين المستودعات"
p_std_stock_deposit_print = "سند تحويل بين المستودعات"
p_std_invoice_no = "رقم السند "
p_std_invoice_creater = "منشئ السند"
p_std_product = "المنتج"
p_std_unit = "الوحدة"
p_std_qty = "الكمية"
p_std_avilable_qty = "الكمية المتوفرة"
p_std_avilable_qty_from_stock = "الكمية المتوفرة في المستودع الاول"
p_std_avilable_qty_to_stock = "الكمية المتوفرة في المستودع الثاني"
p_std_total_qty = "الكمية الكلية"
p_std_stock = "المستودع"
p_std_from_stock = "من المستودع"
p_std_to_stock = "الي المستودع"
p_std_from_date = "التاريخ من"
p_std_to_date = "الي"
p_std_stock_type = "نوع المستودع"
p_std_date = "التاريخ"
p_std_comment = "تعليق"
p_std_details = "التفاصيل"
p_std_palce_holder_comment = "اترك تعليقا هنا"
p_std_palce_holder_qty = "ادخل العدد"

[StockDamages]
p_std_stock_damage = "إتلاف المخزون"
p_std_stock_damage_print = "سند إتلاف المخزون"
p_std_invoice_no = "رقم العملية "
p_std_invoice_creater = "منشئ السند"
p_std_product = "المنتج"
p_std_unit = "الوحدة"
p_std_qty = "الكمية"
p_std_avilable_qty = "الكمية المتوفرة"
p_std_stock = "المستودع"
p_std_stock_type = "نوع المستودع"
p_std_from_date = "التاريخ من"
p_std_to_date = "الي"
p_std_status = "الحاله"
p_std_date = "التاريخ"
p_std_comment = "تعليق"
p_std_details = "التفاصيل"
p_std_palce_holder_comment = "اترك تعليقا هنا"
p_std_palce_holder_qty = "ادخل العدد"

[StockDepositsReport]
p_std_stock_deposit = "استلام الاصناف"
p_std_invoice_no = "رقم سند الاستلام "
p_std_product = "المنتج"
p_std_unit = "الوحدة"
p_std_qty = "العدد"
p_std_stock = "المستودع"
p_std_for_stock = "للمستودع"
p_std_stock_deposit_report = "تقرير سندات استلام المخزون"
p_std_from_date_to = "في الفترة من"
p_std_stock_type = "نوع المستودع"
p_std_from_type = "من نوع"
p_std_date = "التاريخ"
p_std_from_date = "التاريخ من"
p_std_to_date = "الي"
p_std_comment = "تعليق"
p_std_details = "التفاصيل"
p_std_palce_holder_comment = "اترك تعليقا هنا"
p_std_palce_holder_qty = "ادخل العدد"

[DepositsReport]
p_std_stock = "المستودع"
p_std_product = "المنتج"
p_std_for_product = "للمنتج"
p_std_group = "المجموعة"
p_std_for_group = "لمجموعة الاصناف"
p_std_unit = "الوحدة"
p_std_stock_deposit_report = "تقرير المخزون"
p_std_for_stock = "للمستودع"
p_std_from_type = "من نوع"
p_std_in_date_ = "في تاريخ"
p_std_date = "التاريخ"
p_std_in_date = "في تاريخ"
p_std_qty = "الكمية"

[StockWithdrawalsReport]
p_std_stock_deposit = "سحب الاصناف"
p_std_invoice_no = "رقم سند  السحب "
p_std_product = "المنتج"
p_std_unit = "الوحدة"
p_std_qty = "العدد"
p_std_stock = "المستودع"
p_std_stock_deposit_report = "تقرير سندات  الاصناف المسحوبه من المخزن"
p_std_for_stock = "للمستودع"
p_std_from_type = "من نوع"
p_std_from_date_to = "في الفترة من"
p_std_stock_type = "نوع المستودع"
p_std_date = "التاريخ"
p_std_from_date = "التاريخ من"
p_std_to_date = "الي"
p_std_comment = "تعليق"
p_std_details = "التفاصيل"
p_std_palce_holder_comment = "اترك تعليقا هنا"
p_std_palce_holder_qty = "ادخل العدد"

[StockMovementReport]
p_std_stock = "المستودع"
p_std_product = " اسم الصنف"
p_std_for_product = "للمنتج"
p_std_group = "المجموعة"
p_std_for_group = "لمجموعة الاصناف"
p_std_unit = "الوحدة"
p_std_stock_deposit_report = "تقرير  حركة المخزون "
p_std_for_stock = "للمستودع"
p_std_in_date_ = "في تاريخ"
p_std_date = "التاريخ"
p_std_from_date = "التاريخ من"
p_std_to_date = "الي"
p_std_in_date = "في تاريخ"
p_std_qty = "الكمية"
P_operation = "نوع العملية"

[InventoryAndStoreMatching]
p_std_process = "رقم العملية"
p_std_stock = "المستودع"
p_std_date = "التاريخ"
p_std_product = "المنتج"
p_std_unit = "الوحدة"
p_std_qty = "العدد"
p_f = "ف"
P_real_Stock = "تعديل أرصدة المخزن"
P_history_stock = " عمليات جرد سابقة"

[Taxes]
p_Taxes = "ضريبة"
p_tax_name = "إسم الضريبة"
p_tax_description = "الوصف"
p_tax_rate = "النسبة بالمائة %"
p_tax_palce_holder_name = "ادخل اسم المجموعة"
p_gro_palce_holder_description = ""

[Impeded]
Products_unit_id_required_msg = "الرجاء اختيار الوحدة"
Products_supplier_id_required_msg = "الرجاء اختيار المورد"
Products_group_id_required_msg = "الرجاء اختيار المحموعة"
Products_name_required_msg = "الرجاء كتابة الاسم "
Suppliers_name_required_msg = "الرجاء كتابة الاسم"
Suppliers_address_required_msg = "الرجاء كتابة العنوان"
Suppliers_phone_required_msg = "الرجاء كتابة رقم الهاتف"
Groups_name_required_msg = "الرجاء كتابة الاسم"
locations_name_required_msg = "الرجاء كتابة الاسم"
StockUnits_name_required_msg = "الرجاء كتابة الاسم"
stocks_name_required_msg = "الرجاء كتابة الاسم"
stocks_type_required_msg = "الرجاء اختيار النوع"
stocks_location_id_required_msg = "الرجاء اختيار الموقع"
StockDeposits_date_required_msg = "الرجاء كتابة التاريخ"
StockDeposits_qty_required_msg = "الرجاء كتابة الكمية"
StockDeposits_stock_id_required_msg = "الرجاء اختيار المستودع"
StockDeposits_product_id_required_msg = "الرجاء اختيار المنتج"
StockDeposits_unit_id_required_msg = "الرجاء اختيار الوحدة"
Products_stock_unit_id_required_msg = "الرجاء اختيار الوحدة"
Suppliers_phone_numeric_msg = "عفوا ! خطأ في رقم الهاتف"
Suppliers_phone_min_msg = "عفوا ! خطأ في رقم الهاتف"
Products_barcode_min_msg = "عفوا ! الباركود يجب ان لا يتخطي 12 رقم"
Products_barcode_numeric_msg = "عفوا ! الباركود يجب ان لا يحتوي حروف"
Products_barcode_min_length_msg = "عفوا الباركود يجب ان يكون 3 ارقام علي الاقل"
Products_barcode_max_length_msg = "عفوا الباركود يجب ان يكون 13 رقم علي الاكثر"
StockWithdrawals_date_required_msg = ""
StockWithdrawals_stock_id_required_msg = ""
InventoryAndStoreMatching_date_required_msg = ""
InventoryAndStoreMatching_stock_id_required_msg = ""
StockDeposits_date_before_or_equal_msg = "عفوا يجب عدم ادخال تاريخ في المستقبل"
StockWithdrawals_date_before_or_equal_msg = "عفوا يجب عدم ادخال تاريخ في المستقبل"
Suppliers_phone_regex_msg = ""
StockDamages_date_required_msg = ""
StockDamages_date_before_or_equal_msg = ""
StockDamages_stock_id_required_msg = ""
MovementBetweenStocks_date_required_msg = ""
MovementBetweenStocks_date_before_or_equal_msg = ""
MovementBetweenStocks_from_stock_id_required_msg = ""
MovementBetweenStocks_to_stock_id_required_msg = ""
Products_account_cost_id_required_msg = ""
Products_account_consumable_id_required_msg = ""
Products_account_recancelation_id_required_msg = ""
Products_account_damage_id_required_msg = ""
Groups_image_required_msg = ""
Suppliers_phone_min_length_msg = ""
Taxes_name_required_msg = ""
Suppliers_Vat_tax_required_msg = ""
