{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}
    <div class="widget-body">
        <form method="post"
              action='{url check=1 urltype="path" url_string="bsc/P057/StockMovementReport/show/0/{$smarty.session.lang}/filter"}'>

            <div class="row">

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_stock#}</div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                    <select name="stock_id" class="no-search form-control"
                            data-live-search="true">
                        <option value="0">{#gnr_select_from_list_bellow#}</option>
                        {foreach $stocks as $stock}
                            <option value="{$stock->id}" {if $smarty.session.filterParams.stock_id  eq {$stock->id}} selected {/if}>{$stock->name}</option>
                        {/foreach}

                    </select>
                </div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_group#}</div>
                <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 snsoinput">
                    <select name="group_id" class="no-search form-control" id="group" required
                            data-live-search="true">
                        <option value="0">{#gnr_select_from_list_bellow#}</option>
                        {foreach $groups as $group}
                            <option value="{$group->id}" {if $smarty.session.filterParams.group_id  eq {$group->id}} selected {/if}>{$group->name}</option>
                        {/foreach}

                    </select>
                </div>

            </div>

            <div class="row">

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_product#}</div>
                <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 snsoinput">
                    <select name="product_id" class="no-search form-control" id="product"
                            data-live-search="true">
                        <option value="">{#gnr_select_from_list_bellow#}</option>
                        {foreach $products as $product}
                            <option value="{$product->id}" {if $smarty.session.filterParams.product_id  eq {$product->id}} selected {/if}>{$product->name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-2 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_std_from_date#}</div>
                <div class="col-lg-3 col-md-8 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=datefrom type=edit row=$smarty.session.filterParams required=true}</div>

                <div class="col-lg-2 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_std_to_date#}</div>
                <div class="col-lg-3 col-md-8 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=dateto type=edit row=$smarty.session.filterParams required=true}</div>

            </div>


            <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>

            {if count($data)}
                {url check=1 urltype="alinkn" url_string="bsc/P057/StockMovementReport/print/0/{$smarty.session.lang}/" text_value="<i class='fa fa-print black'></i>&nbsp;{#gnr_print#}&nbsp;" style="btn btn-default"}
                {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P057/StockMovementReport/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
            {/if}


        </form>
    </div>
    <div class="widget-body mt-3">
        <p class="text-align-center">
            <bold class="alert alert-success">
                {#p_std_stock_deposit_report#}
                {if $smarty.session.filterParams.stock_id}
                    {#p_std_for_stock#} {$smarty.session.stock}

                {/if}
                {if $smarty.session.filterParams.group_id}
                    {#p_std_for_group#} {$smarty.session.group}

                {/if}

                {if $smarty.session.filterParams.product_id}
                    {#p_std_for_product#} {$smarty.session.product}
                {/if}
                {if $smarty.session.filterParams.date}
                    {#p_std_in_date#} {$smarty.session.filterParams.date}
                {/if}
            </bold>
        </p>
        <div class="row">
            <div class="col-lg-12">
                <div class="table-responsive" data-pattern="priority-columns">
                    <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer">
                        <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="20%">{#p_std_product#}</th>
                            <th width="10%">{#p_std_stock#}</th>
                            <th width="7%">{#P_operation#}</th>
                            <th width="7%">{#p_std_unit#}1</th>
                            <th width="7%">{#p_std_qty#}</th>
                            <th width="7%">{#p_std_unit#}2</th>
                            <th width="7%">{#p_std_qty#}</th>
                            <th width="7%">{#p_std_unit#}3</th>
                            <th width="7%">{#p_std_qty#}</th>
                            <th width="7%">{#p_std_date#}</th>

                        </tr>
                        </thead>
                        <tbody>
                        {$i=1}
                        {foreach $data as $row}
                            <tr>
                                <td>{$i++}</td>
                                <td>{$row.product_name}</td>
                                <td>{$row.stock_name}</td>
                                <td>{$row.type}</td>
                                {foreach $row.units as $unit}
                                    <td>{$unit.name}</td>
                                    <td>{abs($unit.qty)}</td>
                                {/foreach}
                                {if $row.num lt 3}
                                    {for $var=1 to (3-$row.num)}
                                        <td>-</td>
                                        <td>0</td>
                                    {/for }


                                {/if}
                                <td>{$row.date}</td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{/block}
{block name=page_header}
    <script>

        $(document).ready(function() {
            $("#group").change(function () {
                $.get('/framework/core/functions/ajax/stock_group_changed.php?group_id=' + $(this).val(), function (data) {
                    $("#product").html(data);
                    console.log(data);
                });

            });

        });
    </script>
{/block}