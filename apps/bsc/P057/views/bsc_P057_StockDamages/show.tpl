{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=body}
<div class="widget {if !$smarty.session.search_groups} collapsed {/if}">
    <div class="widget-header bg-blue">
        <i class="widget-icon fa fa-arrow-left"></i>
        <span class="widget-caption">{#gnr_search#}</span>
        <div class="widget-buttons">
            <a href="#" data-toggle="collapse">
                <i class="fa fa-{if $smarty.session.search_groups}minus{else}plus{/if}"></i>
            </a>
        </div><!--Widget Buttons-->
    </div><!--Widget Header-->
    <div class="widget-body">

        <form method="post"
              action='{url urltype="path" url_string="bsc/P057/StockDamages/show/0/{$smarty.session.lang}/filter"}'>

            <div class="row">

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_stock#}</div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                    <select name="stock_id" class="form-control"
                            data-live-search="true">
                        <option value="0">{#gnr_select_from_list_bellow#}</option>
                        {foreach $stocks as $stock}
                            <option value="{$stock->id}"  {if $smarty.session.filterParams.stock_id eq $stock->id} selected {/if}>{$stock->name}</option>
                        {/foreach}

                    </select>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_stock_type#}</div>
                <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 snsoinput">
                    <select name="type" class="form-control" required
                            data-live-search="true">
                        <option value="0">{#gnr_select_from_list_bellow#}</option>
                        {foreach $types as $type}
                            <option value="{$type->id}" {if $smarty.session.filterParams.type eq $type->id} selected {/if}>{$type->translatedName}</option>
                        {/foreach}

                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_invoice_creater#}</div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                    <select name="created_by" class="form-control"
                            data-live-search="true">
                        <option value="0">{#gnr_select_from_list_bellow#}</option>
                        {foreach $stockDamageUsers as $stockDamageUser}
                            <option value="{$stockDamageUser->created_by}" {if $smarty.session.filterParams.created_by eq $stockDamageUser->created_by} selected {/if}>{getname table=sh_user id=$stockDamageUser->created_by}</option>
                        {/foreach}

                    </select>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_invoice_no#}</div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput"><input type="text" name="invoice_no" value="{if $smarty.session.filterParams.invoice_no}{$smarty.session.filterParams.invoice_no}{/if}" class="form-control"></div>

            </div>

            <div class="row">

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_from_date#}</div>
                <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=datefrom type=edit row=$smarty.session.filterParams required=true}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_to_date#}</div>
                <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=dateto type=edit row=$smarty.session.filterParams required=true}</div>

            </div>
            <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>
            {*{if count($stockDamages)}*}
            {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P057/StockDamages/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
            {*{/if}*}
        </form>
    </div>
</div>
    <div class="row snsowraper">

        <div class="table-responsive" data-pattern="priority-columns">
            <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer">
                <thead>
                <tr>
                    <th width="5%">
                        {url check=1 style="btn btn-success" urltype="add" opr_code='StockDamages' url_string="bsc/P057/StockDamages/add/0/{$smarty.session.lang}"}
                    </th>
                    <th width="10%">{#p_std_invoice_no#}</th>
                    <th width="10%">{#p_std_stock#}</th>
                    <th width="10%">{#p_std_date#}</th>
                    <th width="10%">{#p_std_invoice_creater#}</th>
                    <th width="10%">{#p_std_details#}</th>
                    <th width="10%">{#gnr_action#}</th>
                    <th width="20%">{#gnr_settings#}</th>
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $stockDamages as $stockDamage}
                    <tr>
                        <td align="center">{{getCurrentPage()} + $i++}</td>
                        <td>{$stockDamage->invoice_no}</td>
                        <td>{$stockDamage->stock->name}</td>
                        <td>{$stockDamage->date}</td>
                        <td>{getname table=sh_user id=$stockDamage->created_by}</td>
                        <td align="center">{url  urltype="mbutton" text_value="{#gnr_details#}" style="btn btn-default shiny"  url_string="bsc/P057/StockDamages/details/0/{$smarty.session.lang}/{$stockDamage->id}"}</td>
                        <td align="center">

                            {workflow requestId=$stockDamage->request->wf_request_id backTo="bsc/P057/StockDamages/show/0/{$smarty.session.lang}/menu"}
                        </td>
                        <td align="center">
                            {if $stockDamage->created_by eq {$smarty.session.user->id} && $stockDamage->warehouse_stock_damage_request_success eq 1}
                                {url urltype="print" opr_code='StockDamages' url_string="bsc/P057/StockDamages/print/0/{$smarty.session.lang}/{$stockDamage->id}"}
                            {else}
	                            {url urltype="print" opr_code='StockDamages' url_string="bsc/P057/StockDamages/print/0/{$smarty.session.lang}/{$stockDamage->id}"}
	                            {url check=1 urltype="edit" opr_code='StockDamages' url_string="bsc/P057/StockDamages/edit/0/{$smarty.session.lang}/{$stockDamage->id}"}
	                            {url check=1 urltype="mdelete" opr_code='StockDamages' url_string="bsc/P057/StockDamages/confirm/0/{$smarty.session.lang}/{$stockDamage->id}"}
                            {/if}

                        </td>
                    </tr>
                {/foreach}
                </tbody>
            </table>

            {render view="/_pagination" rows=$stockDamages url="bsc/P057/StockDamages/show"}
        </div>
    </div>
{/block}
{block name=page_header}
{/block}