{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add#} {#p_Taxes#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url check=1 urltype="path" url_string="bsc/P057/Taxes/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_Taxes_token}"}' enctype="multipart/form-data">
                <div class="row snsowraper">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_tax_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <input type="text" name="name" required class="form-control" placeholder="{#p_gro_palce_holder_name#}"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_tax_description#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <textarea name="details" class="form-control"  placeholder="{#p_gro_palce_holder_description#}" ></textarea>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_tax_rate#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <input type="number" name="tax_rate" min="1" max="100" required class="form-control" placeholder="{#p_gro_palce_holder_name#}">

                        </div>
                        {*<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_document_name#}</div>*}
                        {*<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">*}
                            {*<input id="docTitle" type="text" class="form-control" name="filename" placeholder="{#gnr_enter_attachment_name#}">*}
                        {*</div>*}
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp" >{#gnr_add#}</button></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
{block name=footer}
<script src="/templates/assets/js/modal/getFileName.js"></script>
<script>
    let allowedFilles = {json_encode(Document::DOCUMENT_ALLOWED_FILE_TYPES)}
        $('#files').change(function(){
            let fileExtentionExist;
            allowedFilles.forEach(extintion =>{
                if( fileExtention == extintion ){
                fileExtentionExist = true

            }
        })
            if(!fileExtentionExist){
                $('.addDocument').addClass('disabled')
                $('#filesExtentions').show()
            }
            else{
                $('.addDocument').removeClass('disabled')
                $('#filesExtentions').hide()
            }
        })
</script>
{/block}
{*{block name=footer}*}

    {*<script type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>*}
    {*<script type="text/javascript">*}
        {*$(document).ready(function (e) {*}
            {*$('#upload').attr('value',"templates//assets//img//group.png");*}
        {*});*}
        {*});*}
    {*</script>*}
{*{/block}*}