{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}
    <div class="widget-body">
        <form method="post"
              action='{url check=1 urltype="path" url_string="bsc/P057/StockWithdrawalsReport/show/0/{$smarty.session.lang}/filter"}'>

            <div class="row">

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_stock#}</div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                    <select name="stock_id" class="no-search form-control" id="stock"
                            data-live-search="true">
                        <option value="0">{#gnr_select_from_list_bellow#}</option>
                        {foreach $stocks as $stock}
                            <option  value="{$stock->id}"
                                {if $smarty.session.filterParams.stock_id eq $stock->id} selected {/if}

                            >{$stock->name}</option>
                        {/foreach}

                    </select>
                </div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_stock_type#}</div>
                <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 snsoinput">
                    <select name="type" class="no-search form-control" id="type"
                            data-live-search="true">
                        <option value="0">{#gnr_select_from_list_bellow#}</option>
                        {foreach $types as $type}
                            <option value="{$type->id}" {if $smarty.session.filterParams.type eq $type->id} selected {/if}>{$type->translatedName}</option>
                        {/foreach}

                    </select>
                </div>
            </div>

            <div class="row">

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_from_date#}</div>
                <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=datefrom type=edit row=$smarty.session.filterParams required=true}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_to_date#}</div>
                <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=dateto type=edit row=$smarty.session.filterParams required=true}</div>

            </div>

            <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>

            {if count($data)}
                {url check=1 urltype="alinkn" url_string="bsc/P057/StockWithdrawalsReport/print/0/{$smarty.session.lang}/" text_value="<i class='fa fa-print black'></i>&nbsp;{#gnr_print#}&nbsp;" style="btn btn-default"}
                {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P057/StockWithdrawalsReport/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
            {/if}


        </form>
    </div>
    <div class="widget-body mt-3">
        <p class="text-align-center">
            <bold class="alert alert-success">
                {#p_std_stock_deposit_report#}
                {if $smarty.session.filterParams.stock_id}
                    {#p_std_for_stock#} {$smarty.session.stock}

                {/if}

                {if $smarty.session.filterParams.type}
                    {#p_std_from_type#} {$smarty.session.type}

                {/if}

                {if $smarty.session.filterParams.datefrom}
                    {#p_std_from_date_to#}
                    {$smarty.session.filterParams.datefrom}

                {/if}

                {if $smarty.session.filterParams.dateto}
                    {#p_std_to_date#}
                    {$smarty.session.filterParams.dateto}
                {/if}
            </bold>
        </p>
        <div class="row">
            <div class="col-lg-12">
                <div class="table-responsive" data-pattern="priority-columns">
                    <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer">
                        <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="20%">{#p_std_invoice_no#}</th>
                            <th width="30%">{#p_std_stock#}</th>
                            <th width="30%">{#p_std_stock_type#}</th>
                            <th width="10%">{#p_std_date#}</th>
                            <th width="10%">{#p_std_details#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {$i=1}
                        {foreach $data as $row}
                            <tr>
                                <td>{$i++}</td>
                                <td>{$row->invoice_no}</td>
                                <td>{$row->stock->name}</td>
                                <td>{t row=$row->stock->type}</td>
                                <td>{$row->date}</td>
                                <td align="center">{url check=0 urltype="mbutton" text_value="{#gnr_details#}" style="btn btn-default shiny"  url_string="bsc/P057/StockWithdrawalsReport/details/0/{$smarty.session.lang}/{$row->id}"}</td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{/block}
{block name=page_header}
    <script>

        $(document).ready(function() {
            $("#stock").change(function () {
                $.get('/framework/core/functions/ajax/stock_changed.php?stock_id=' + $(this).val(), function (data) {
                    $("#type").html(data);
                    console.log(data);
                });
            });
        });
    </script>
    <script type="text/javascript" src="/templates/assets/js/loader.js"></script>
    {DrawChart Data=$Data title={#gnr_chart#} RandNumber="usersType-chart"}
    <!-- Chart Libraries -->
    <script src="/templates/assets/js/charts/morris/raphael-2.0.2.min.js"></script>
    <script src="/templates/assets/js/charts/morris/morris.js"></script>
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
        function exportTo(ID, type) {
            $('#table' + ID).css('display', '').tableExport({type: type, escape: 'false'});
            $('#table' + ID).css('display', 'none');
        }
        {/literal}
    </script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 10,
                        "oTableTools": {
                            "aButtons": [],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                    $("tfoot input").keyup(function () {
                        /* Filter on the column (the index) of this element */
                        oTable.fnFilter(this.value, $("tfoot input").index(this));
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}
{*{block name=back}{url urltype="path" url_string="bsc/P057/StockDepositsReport/show/0/{$smarty.session.lang}"}{/block}*}