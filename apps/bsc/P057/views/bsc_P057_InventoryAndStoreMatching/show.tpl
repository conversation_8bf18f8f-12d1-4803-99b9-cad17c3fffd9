{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}
    <div class="widget-body">
        <form method="post"
              action='{url urltype="path" url_string="bsc/P057/InventoryAndStoreMatching/show/0/{$smarty.session.lang}/filter"}'>

            <div class="row">

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_process#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><h3 class=""><strong><span class=""> {$invoice_no}</span> </strong></h3></div>


                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_stock#}</div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                    <select name="stock_id" class="form-control" required
                            data-live-search="true">
                        <option value="0">{#gnr_select_from_list_bellow#}</option>
                        {foreach $stocks as $stock}
                            <option value="{$stock->id}" {if $smarty.session.filterParams.stock_id  eq {$stock->id}} selected {/if}>{$stock->name}</option>
                        {/foreach}

                    </select>
                </div>


            </div>

            <div class="row">


                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_date#}</div>
                <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=date type=edit row=$smarty.session.filterParams required=true}</div>

            </div>

            <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>

            {if count($data)}
                {*{url check=0 urltype="alinkn" url_string="bsc/P057/DepositsReport/print/0/{$smarty.session.lang}/" text_value="<i class='fa fa-print black'></i>&nbsp;{#gnr_print#}&nbsp;" style="btn btn-default"}*}
                {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P057/InventoryAndStoreMatching/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
            {/if}
            {*{url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P057/InventoryAndStoreMatching/show/0/{$smarty.session.lang}/filter" text_value="{#P_history_stock#}"}*}

        </form>
    </div>
    <div class="widget-body mt-3">
        <form method="post"
              action='{url urltype="path" url_string="bsc/P057/InventoryAndStoreMatching/show/0/{$smarty.session.lang}/insert" }'>
            <div class="row">
                <div class="col-lg-12">
                    <div class="table-responsive" data-pattern="priority-columns">
                        <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer">
                            <thead>
                            <tr>
                                <th width="5%" class="text-center">#</th>
                                <th width="20%" class="text-center">{#p_std_product#}</th>
                                <th width="20%" class="text-center">{#p_std_stock#}</th>
                                <th width="7%" class="text-center">{#p_std_unit#}1</th>
                                <th width="7%" class="text-center">{#p_std_qty#}</th>
                                <th width="7%" class="text-center">{#p_f#}1</th>
                                <th width="7%" class="text-center">{#p_std_unit#}2</th>
                                <th width="7%" class="text-center">{#p_std_qty#}</th>
                                <th width="7%" class="text-center">{#p_f#}2</th>
                                <th width="7%" class="text-center">{#p_std_unit#}3</th>
                                <th width="7%" class="text-center">{#p_std_qty#}</th>
                                <th width="7%" class="text-center">{#p_f#}3</th>
                            </tr>
                            </thead>
                            <tbody>

                            {$i=1}
                            {foreach $data as $row}
                                <tr>
                                    <td class="text-center">{$i++}</td>
                                    <td class="text-center">{$row.product_name}</td>
                                    <input class="text-center" type="hidden" name="product_ids[]" value="{$row.product_id}">
                                    <input class="text-center" type="hidden" name="stock_ids[]" value="{$row.stock_id}">

                                    <td class="text-center">{$row.stock_name}</td>
                                    {foreach $row.units as $unit}
                                        <td class="text-center">{$unit.name}</td>
                                        <td class="text-center">{$unit.qty}</td>
                                        <td class="text-center"><input type="text" value="{$unit.qty}"
                                                   name="real_qty[{$row.stock_id}][{$row.product_id}][{$unit.id}]"
                                                   size="15"></td>
                                    {/foreach}
                                    {if $row.num lt 3}

                                        {for $var=1 to (3-$row.num)}
                                            <td class="text-center">--</td>
                                            <td class="text-center">0</td>
                                            <td class="text-center">0</td>
                                        {/for }
                                    {/if}
                                </tr>
                            {/foreach}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col text-center">
                    {if count($data)}
                    <button type="submit" class="btn btn-success sharp ">{#P_real_Stock#}</button>
                    {/if}
                </div>

            </div>


        </form>
    </div>
{/block}
{block name=page_header}
    <script>

        $(document).ready(function () {
            $("#group").change(function () {
                $.get('/framework/core/functions/ajax/stock_group_changed.php?group_id=' + $(this).val(), function (data) {
                    $("#product").html(data);
                    console.log(data);
                });
            });

        });
    </script>
{/block}
