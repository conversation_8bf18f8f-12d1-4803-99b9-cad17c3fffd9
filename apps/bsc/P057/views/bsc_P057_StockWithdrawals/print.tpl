{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}

{block name=body}
    <div class="row">
        <div class="text-center">
            <h1><u>{#p_std_stock_deposit_print#}</u></h1>
        </div>
        {*<div class="modal-body">*}
                <table class="table mb-1">
                    <thead>
                    <tr>
                        <th width="15%">{#p_std_invoice_no#}</th>
                        <th width="25%">{$stockWithdrawal->invoice_no}</th>
                        <th width="8%">{#p_std_stock#}</th>
                        <th width="35%">{$stockWithdrawal->stock->name}</th>
                        <th width="8%">{#p_std_date#}</th>
                        <th width="10%">{$stockWithdrawal->date}</th>
                    </tr>
                    </thead>
                </table>
                    <div class="table-responsive" data-pattern="priority-columns">
                        <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer">
                            <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="40%">{#p_std_product#}</th>
                                <th width="40%">{#p_std_unit#}</th>
                                <th width="10%">{#p_std_qty#}</th>
                            </tr>
                            </thead>
                            <tbody>
                            {$i=1}
                            {foreach $details as $detail}
                                <tr>
                                    <td>{$i++}</td>
                                    <td>{$detail->product->name}</td>
                                    <td>{$detail->productUnit->unit->name}</td>
                                    <td>{$detail->qty}</td>
                                </tr>
                            {/foreach}
                            </tbody>
                        </table>
                    </div>
                <hr>
        <div class="row snsowraper">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                {#gnr_printed_by_user#}: {$smarty.session.user->full_name}<br>
                {#gnr_on_date#}: {$smarty.now|date_format:"%d/%m/%Y"}
            </div>
        </div>
{/block}
