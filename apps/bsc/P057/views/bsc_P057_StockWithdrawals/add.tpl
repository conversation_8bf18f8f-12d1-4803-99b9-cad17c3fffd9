{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}

{block name=page_body}
    <stockwithdrawals
            stocks='{$stocks}'
            products='{$products}'
            inline-template>

            <div>
                <form  method="post" action='{url check=1 urltype="path" url_string="bsc/P057/StockWithdrawals/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_stock_withdrawals_token}"}'>
                    <div class="widget-body">

                        <div class="row">
                            <div class="col-lg-6">
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_std_invoice_no#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">  <h3 class=""><strong><span class=""> {$invoice_no}</span> </strong></h3></div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_std_stock#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                    <selectize name="stock_id" @input="getType"  v-model="selectedStock"  class="form-control" required
                                            data-live-search="true">
                                        <option value="">{#gnr_select_from_list_bellow#}</option>
                                        <option  v-for="stock in stocksData" :key="stock.id" :value="stock.id" v-text="stock.name"></option>

                                    </selectize>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_std_stock_type#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" :value="type" class="form-control" readonly></div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_std_date#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate table=warehouse_stock_withdrawal col=date type=add future=false row=$row required=true}</div>

                            </div>
                        </div>

                        <div class="row">

                            <div class="col-lg-6">
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_std_comment#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                    <textarea name="note" class="form-control"  placeholder="{#p_std_palce_holder_comment#}" ></textarea>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row mt-2">
                        <div class="col-lg-12">
                            <table  class="table table-bordered table-hover">
                                <thead>
                                <tr>
                                    <th width="2%">
                                        <button @click.prevent="addProduct" class="btn btn-success btn-sm sharp icon-only">
                                            <i class='fa fa-plus'></i>
                                        </button>
                                    </th>
                                    <th width="5%">{#p_std_product#}</th>
                                    <th width="5%">{#p_std_unit#}</th>
                                    <th  width="5%">{#p_std_avilable_qty#}</th>
                                    <th width="5%">{#p_std_qty#}</th>
                                    <th  width="5%">{#gnr_status#}</th>
                                    <th  width="5%">{#gnr_settings#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr is="withdrawalUnits" v-for="(stock , index ) in productsData" :key="index" :index="index" :stock="stock" :selected-stock="selectedStock" :products-unit="productsUnit" @remove="removeProduct" @changed="changed"></tr>

                                </tbody>
                            </table>
                            <div class="snsoinput">
                                <button type="submit" class="btn btn-success sharp mt-1" :disabled="disabled" :title="disabled">{#gnr_save#}</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
    </stockwithdrawals>

        {/block}
{block name=back}{url urltype="path" url_string="bsc/P057/StockWithdrawals/show/0/{$smarty.session.lang}"}{/block}

