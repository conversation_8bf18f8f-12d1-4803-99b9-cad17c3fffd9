{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_edit#} {#p_stok_stock#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url check=1 urltype="path" url_string="bsc/P057/stocks/update/0/{$smarty.session.lang}/{$stock->id}"}'>
                <div class="row snsowraper">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="name" required class="form-control" placeholder="{#p_stok_palce_holder_name#}" value="{$stock->name}"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_type#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="type" class="form-control" required
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $types as $type}
                                    <option value="{$type->id}" {if $stock->type eq $type->id} selected {/if}>{$type->translatedName}</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_location#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="location_id" class="form-control" required
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $locations as $location}
                                    <option value="{$location->id}" {if $stock->location_id eq $location->id} selected {/if}>{$location->name}</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_account#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_id" class="form-control"
                                    data-live-search="true">
                                {if $stock->account_id eq '0'}
                                    <option>{#gnr_select_from_list_bellow#}</option>
                                {/if}
                                {foreach $accounts as $account}

                                    <option value="{$account.id}" {if $stock->account_id  eq $account.id} selected {/if}>{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}

