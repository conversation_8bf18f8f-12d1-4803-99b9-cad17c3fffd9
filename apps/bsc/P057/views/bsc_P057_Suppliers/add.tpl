{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add#} {#p_supp_supplier#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url check=1 urltype="path" url_string="bsc/P057/Suppliers/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_suppliers_token}"}'>
                <div class="row snsowraper">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_supp_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="name" class="form-control" required  placeholder="{#p_supp_palce_holder_name#}"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_supp_address#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="address" class="form-control" required  placeholder="{#p_supp_palce_holder_address#}"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_supp_phone#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" minlength="9" name="phone" class="form-control" required  placeholder="{#p_supp_palce_holder_phone#}"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_supp_email#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="email" name="email" class="form-control" placeholder="{#p_supp_palce_holder_email#}">
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_supp_responsible_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <input type="text" name="responsible_name" class="form-control" placeholder="{#p_supp_palce_holder_responsible_name#}">
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_supp_responsible_phone#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <input type="tel" name="responsible_phone" class="form-control" placeholder="{#p_supp_palce_holder_responsible_phone#}">
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_vat_tax#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <input type="text" name="Vat_tax" class="form-control"
                                   placeholder="{#p_customer_palce_holder_responsible_vat_tax#}">
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_account#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_id" class="form-control"
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $accounts as $account}
                                    <option value="{$account.id}">{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_status#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                                {foreach $activationStatus as $key => $status}
                                    <div class="radio">
                                        <label>
                                            <input name="status" value="{$status->id}" id="{$status->id}" {if $key eq 0} checked {/if} type="radio" required>
                                            <span class="text">{$status->translatedName}</span>
                                        </label>
                                    </div>
                                {/foreach}
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp" >{#gnr_add#}</button></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}

