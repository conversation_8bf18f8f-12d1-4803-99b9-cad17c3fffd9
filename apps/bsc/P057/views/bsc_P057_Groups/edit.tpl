{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_edit#} {#p_gro_group#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url check=1 urltype="path" url_string="bsc/P057/Groups/show/0/{$smarty.session.lang}/update/{$smarty.session.s_groups_token}/{$group->id}"}' enctype="multipart/form-data">
                <div class="row snsowraper">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_gro_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="name" required class="form-control" value="{$group->name}"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_gro_description#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <textarea name="description" class="form-control"  >{$group->description}</textarea>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_path#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <label for="files" class="btn btn-default col-lg-4" >{#gnr_choose#}</label>
                        <input type="file" id="upload" name="image" >
                            {if $group->image}
                                <img src="/framework/core/functions/image.php?image={$group->image}" height="80px" width="80px">
                            {else}
                                <img src="/templates/assets/img/group.png" height="80px" width="80px" alt="group image">
                            {/if}
                            <span id="filenameHolder"></span>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        
                        <button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
