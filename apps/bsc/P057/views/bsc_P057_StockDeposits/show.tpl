{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=body}
<div class="widget {if !$smarty.session.search_groups} collapsed {/if}">
    <div class="widget-header bg-blue">
        <i class="widget-icon fa fa-arrow-left"></i>
        <span class="widget-caption">{#gnr_search#}</span>
        <div class="widget-buttons">
            <a href="#" data-toggle="collapse">
                <i class="fa fa-{if $smarty.session.search_groups}minus{else}plus{/if}"></i>
            </a>
        </div><!--Widget Buttons-->
    </div><!--Widget Header-->
    <div class="widget-body">

        <form method="post"
              action='{url urltype="path" url_string="bsc/P057/StockDeposits/show/0/{$smarty.session.lang}/filter"}'>

            <div class="row">

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_stock#}</div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                    <select name="stock_id" class="form-control"
                            data-live-search="true">
                        <option value="0">{#gnr_select_from_list_bellow#}</option>
                        {foreach $stocks as $stock}
                            <option value="{$stock->id}"  {if $smarty.session.filterParams.stock_id eq $stock->id} selected {/if}>{$stock->name}</option>
                        {/foreach}

                    </select>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_stock_type#}</div>
                <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 snsoinput">
                    <select name="type" class="form-control"
                            data-live-search="true">
                        <option value="0">{#gnr_select_from_list_bellow#}</option>
                        {foreach $types as $type}
                            <option value="{$type->id}" {if $smarty.session.filterParams.type eq $type->id} selected {/if}>{$type->translatedName}</option>
                        {/foreach}

                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_invoice_creater#}</div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                    <select name="created_by" class="form-control"
                            data-live-search="true">
                        <option value="0">{#gnr_select_from_list_bellow#}</option>
                        {foreach $stockDepositUsers as $stockDepositUser}
                            <option value="{$stockDepositUser->created_by}" {if $smarty.session.filterParams.created_by eq $stockDepositUser->created_by} selected {/if}>{getname table=sh_user id=$stockDepositUser->created_by}</option>
                        {/foreach}

                    </select>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_invoice_no#}</div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput"><input type="text" name="invoice_no" value="{if $smarty.session.filterParams.invoice_no}{$smarty.session.filterParams.invoice_no}{/if}" class="form-control"></div>

            </div>

            <div class="row">

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_from_date#}</div>
                <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=datefrom type=edit row=$smarty.session.filterParams required=true}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_std_to_date#}</div>
                <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 ">{getdate attr="class='snsoinput'" table=warehouse_stock_deposit col=dateto type=edit row=$smarty.session.filterParams required=true}</div>

            </div>
            <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>
            {if count($stockDeposits)}
            {url check=0 urltype="button" style="btn btn-default shiny" url_string="bsc/P057/StockDeposits/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
            {/if}
        </form>
    </div>
</div>
    <div class="row snsowraper">

        <div class="table-responsive" data-pattern="priority-columns">
            <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer">
                <thead>
                <tr>
                    <th width="5%">
                        {url check=1 style="btn btn-success" urltype="add" opr_code='StockDeposits' url_string="bsc/P057/StockDeposits/add/0/{$smarty.session.lang}"}
                    </th>
                    <th width="10%">{#p_std_invoice_no#}</th>
                    <th width="10%">{#p_std_stock#}</th>
                    <th width="10%">{#p_std_date#}</th>
                    <th width="10%">{#p_std_invoice_creater#}</th>
                    <th width="10%">{#p_std_details#}</th>
                    <th width="20%">{#gnr_settings#}</th>
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $stockDeposits as $stockDeposit}
                    <tr>
                        <td align="center">{{getCurrentPage()} + $i++}</td>
                        <td>{$stockDeposit->invoice_no}</td>
                        <td>{$stockDeposit->stock->name}</td>
                        <td>{$stockDeposit->date}</td>
                        <td>{getname table=sh_user id=$stockDeposit->created_by}</td>
                        <td align="center">{url  urltype="mbutton" text_value="{#gnr_details#}" style="btn btn-default shiny"  url_string="bsc/P057/StockDeposits/details/0/{$smarty.session.lang}/{$stockDeposit->id}"}</td>
                        <td align="center">
                            {if $stockDeposit->created_by eq {$smarty.session.user->id}}
                                {url urltype="print" opr_code='StockDeposits' url_string="bsc/P057/StockDeposits/print/0/{$smarty.session.lang}/{$stockDeposit->id}"}
                                {url check=1 urltype="edit" opr_code='StockDeposits' url_string="bsc/P057/StockDeposits/edit/0/{$smarty.session.lang}/{$stockDeposit->id}"}
                                {url check=1 urltype="mdelete" opr_code='StockDeposits' url_string="bsc/P057/StockDeposits/confirm/0/{$smarty.session.lang}/{$stockDeposit->id}"}
                            {/if}

                        </td>
                    </tr>
                {/foreach}
                </tbody>
            </table>

            {render view="/_pagination" rows=$stockDeposits url="bsc/P057/StockDeposits/show"}
        </div>
    </div>
{/block}
{block name=page_header}
{/block}