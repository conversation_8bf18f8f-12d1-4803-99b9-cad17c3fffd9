{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add#} {#p_stok_stock#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url check=1 urltype="path" url_string="bsc/P057/StockAccountSetting/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_stock_account_setting_token}"}'>
                <div class="row snsowraper">
                    <h5 class="modal-title mr-1">{#gnr_add#} {#p_stock_withdrawal#}</h5>
                    <br>
                    <div class="col-lg-12 well">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_account_cost#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_cost_id" class="form-control" required
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $accounts as $account}
                                    <option value="{$account.id}">{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_account_consumable#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_consumable_id" class="form-control"
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $accounts as $account}
                                    <option value="{$account.id}">{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    <h5 class="modal-title mr-1">{#gnr_add#} {#p_stock_recancelation#}</h5>
                    <br>
                    <div class="col-lg-12 well">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_account_recancelation#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_recancelation_id" class="form-control" required
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $accounts as $account}
                                    <option value="{$account.id}">{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    <h5 class="modal-title mr-1">{#gnr_add#} {#p_stock_damage#}</h5>
                    <br>
                    <div class="col-lg-12 well">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_account_damage#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_damage_id" class="form-control"
                                    data-live-search="true">
                                <option value="">{#gnr_select_from_list_bellow#}</option>
                                {foreach $accounts as $account}
                                    <option value="{$account.id}">{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12"><button type="submit" class="btn btn-success sharp" >{#gnr_add#}</button></div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}

