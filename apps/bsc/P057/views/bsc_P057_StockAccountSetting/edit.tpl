{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_edit#} {#p_stok_stock#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url check=1 urltype="path" url_string="bsc/P057/StockAccountSetting/update/0/{$smarty.session.lang}/{$stockAccountSetting->id}"}'>
                <div class="row snsowraper">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_account_cost#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_cost_id" class="form-control"
                                    data-live-search="true">
                                {if $stockAccountSetting->account_cost_id eq '0'}
                                    <option>{#gnr_select_from_list_bellow#}</option>
                                {/if}
                                {foreach $accounts as $account}

                                    <option value="{$account.id}" {if $stockAccountSetting->account_cost_id  eq $account.id} selected {/if}>{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_account_consumable#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_consumable_id" class="form-control"
                                    data-live-search="true">
                                {if $stockAccountSetting->account_consumable_id eq '0'}
                                    <option>{#gnr_select_from_list_bellow#}</option>
                                {/if}
                                {foreach $accounts as $account}

                                    <option value="{$account.id}" {if $stockAccountSetting->account_consumable_id  eq $account.id} selected {/if}>{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_account_recancelation#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_recancelation_id" class="form-control"
                                    data-live-search="true">
                                {if $stockAccountSetting->account_recancelation_id eq '0'}
                                    <option>{#gnr_select_from_list_bellow#}</option>
                                {/if}
                                {foreach $accounts as $account}

                                    <option value="{$account.id}" {if $stockAccountSetting->account_recancelation_id  eq $account.id} selected {/if}>{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_stok_account_damage#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="account_damage_id" class="form-control"
                                    data-live-search="true">
                                {if $stockAccountSetting->account_damage_id eq '0'}
                                    <option>{#gnr_select_from_list_bellow#}</option>
                                {/if}
                                {foreach $accounts as $account}

                                    <option value="{$account.id}" {if $stockAccountSetting->account_damage_id  eq $account.id} selected {/if}>{$account.name}-{$account.code}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12"><button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button></div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}

