{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_edit#} {#p_pro_product#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">

            <form  method="post" action='{url check=1 urltype="path" url_string="bsc/P057/Products/show/0/{$smarty.session.lang}/update/{$smarty.session.s_products_token}/{$product->id}"}'>
                <div class="row snsowraper">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_std_product_no#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="product_no" value="{$product->product_no}" class="form-control" readonly></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="name" required class="form-control" value="{$product->name}"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_scientific_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="scientific_name" class="form-control" value="{$product->scientific_name}"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_supplier#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="supplier_id" class="form-control" required
                                    data-live-search="true">
                                {foreach $suppliers as $supplier}
                                    <option value="{$supplier->id}" {if $supplier->id eq $product->supplier_id} selected {/if}>{$supplier->name}</option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_group#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="group_id" class="form-control" required
                                    data-live-search="true">
                                {foreach $groups as $group}
                                    <option value="{$group->id}" {if $group->id eq $product->group_id}selected{/if}>{$group->name}</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_unit#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select name="stock_unit_id" class="form-control" required {if $product->units()->exists()} disabled  {/if}
                                    data-live-search="true">
                                {foreach $units as $unit}
                                    <option value="{$unit->id}" {if $unit->id eq $product->stock_unit_id}selected{/if}>{$unit->name}</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_code#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="barcode"  class="form-control" value="{$product->barcode}"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_tax#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
		                    {*<input type="number" name="tax" min="0" max="100" step=".01" class="form-control" placeholder="0.00 %">*}
                            <label for="cars">{#p_tax_selected#}:</label>

                            <select name="tax_id[]" class="form-control" id="tax" data-live-search="true" multiple>
			                    {foreach $taxes as $tax}
                                    <option value="{$tax->id}"
                                            {foreach $product->productTaxes as $index}
                                            {if $tax->id eq $index->tax_id}
                                                selected
                                            {/if}
                                            {/foreach}

                                    >   {$tax->name}-{$tax->tax_rate}% </option>
			                    {/foreach}
                            </select>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_status#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            {foreach $activationStatus as $status}
                                <div class="radio">
                                    <label>
                                        <input name="status" value="{$status->id}" id="{$status->id}"  {if $status->id eq $product->status} checked {/if} type="radio" required>
                                        <span class="text">{$status->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_pro_description#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <textarea name="description" class="form-control">{$product->description}</textarea>
                        </div>


                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}

