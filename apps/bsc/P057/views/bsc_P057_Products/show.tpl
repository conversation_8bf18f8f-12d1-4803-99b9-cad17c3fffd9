{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=body}
    <div class="row snsowraper">
        <div class="table-responsive" data-pattern="priority-columns">
            <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th width="5%">{url check=1 urltype="madd" opr_code='Products' url_string="bsc/P057/Products/add/0/{$smarty.session.lang}" modal="modal"}</th>
                    <th width="10%">{#p_pro_name#}</th>
                    <th width="10%"> {#p_std_product_no#} </th>
                    <th width="10%">{#p_pro_supplier#}</th>
                    <th width="10%">{#p_pro_group#}</th>
                    <th width="5%"> {#gnr_type#} </th>
                    <th width="5%"> {#gnr_status#} </th>
                    <th width="5%"> {#p_pro_tax#} </th>
                    <th width="10%">{#p_pro_product_setting#}</th>
                    <th  width="20%">{#gnr_settings#}</th>
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $products as $product}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td>{$product->name}</td>
                        <td>{$product->product_no}</td>
                        <td>{$product->supplier->name}</td>
                        <td>{$product->group->name}</td>
                        <td>{getname table=st_setting id=$product->type}</td>
                        <td>{getname table=st_setting id=$product->status}</td>
                        <td>{$product->tax}</td>
                        <td align="center">{url check=1 opr_code="Products" oprvtype=1 urltype="button" text_value="{#gnr_settings#}" style="btn btn-default shiny"  url_string="bsc/P057/Products/setting/0/{$smarty.session.lang}/{$product->id}/menu"}</td>
                        <td align="center">
                            {url check=1 urltype="medit" opr_code='Products' url_string="bsc/P057/Products/edit/0/{$smarty.session.lang}/{$product->id}"}
                            {if !($product->stockUnit()->exists() || $product->stockTransaction()->exists() || $product->stockDepositDetail()->exists() || $product->stockWithdrawalDetail()->exists())}
                            {url check=1 urltype="mdelete" opr_code='Products' url_string="bsc/P057/Products/confirm/0/{$smarty.session.lang}/{$product->id}"}
                            {/if}

                        </td>
                    </tr>
                {/foreach}
                </tbody>
            </table>
        </div>
    </div>
{/block}
{block name=page_header}
<script type="text/javascript" src="/templates/assets/js/loader.js"></script>
{DrawChart Data=$Data title={#gnr_chart#} RandNumber="usersType-chart"}
<!-- Chart Libraries -->
<script src="/templates/assets/js/charts/morris/raphael-2.0.2.min.js"></script>
<script src="/templates/assets/js/charts/morris/morris.js"></script>

<script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
<script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
<script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
<script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
<script>
    {literal}
    function exportTo(ID, type) {
        $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
    }
    {/literal}
</script>
<script>
    var InitiateSimpleDataTable = function() {
        return {
            init: function() {
                //Datatable Initiating
                var oTable = $('.sortable-table').dataTable({
                    "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                    "iDisplayLength": 10,
                    "oTableTools": {
                        "aButtons": [

                        ],
                        "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                    },
                    "language": {
                        "search": "",
                        "sLengthMenu": "_MENU_",
                        "oPaginate": {
                            "sPrevious": "{#gnr_previous#}",
                            "sNext": "{#gnr_next#}"
                        }
                    }
                });
                $("tfoot input").keyup(function() {
                    /* Filter on the column (the index) of this element */
                    oTable.fnFilter(this.value, $("tfoot input").index(this));
                });
            }

        };

    }();

    InitiateSimpleDataTable.init();
</script>
{/block}
