{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=body}
    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-list-alt blue"></i>
        {$product->name}
    </h5>
    <div class="row snsowraper">
        <div class="table-responsive" data-pattern="priority-columns">
            <table id="snsotable-1" class="table table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th width="5%" > {if count($productUnits) lt 3}{url check=1  urltype="madd" opr_code='Products' url_string="bsc/P057/Products/addUnit/0/{$smarty.session.lang}/{$product_id}" modal="modal"} {/if}</th>
                    <th width="10%">{#p_pro_unit#}</th>
                    <th width="5%">{#p_pro_qty#}</th>
                    <th width="5%">{#p_pro_purch_price#}</th>
                    <th width="5%">{#p_pro_m_purch_price#}</th>
                    <th width="5%">{#p_pro_sale_price#}</th>
                    <th  width="10%">{#gnr_settings#}</th>
                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $productUnits as $product}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td>{$product->unit->name}</td>
                        <td>{$product->number}</td>
                        <td>{$product->purchase_price}</td>
                        <td>{$product->m_purchase_price}</td>
                        <td>{$product->sale_price}</td>
                        <td align="center">
                            {url check=1 urltype="medit" opr_code='Products' url_string="bsc/P057/Products/editUnit/0/{$smarty.session.lang}/{$product->id}"}
                            {if !($product->stockDepositDetail()->exists() || $product->stockWithdrawalDetail()->exists() || $product->parent()->exists())}
                            {url check=1 urltype="mdelete" opr_code='Products' url_string="bsc/P057/Products/confirmUnit/0/{$smarty.session.lang}/{$product->id}"}
                            {/if}
                        </td>
                    </tr>
                {/foreach}
                </tbody>
            </table>
        </div>
    </div>
{/block}
{block name=page_header}
<script type="text/javascript" src="/templates/assets/js/loader.js"></script>
{DrawChart Data=$Data title={#gnr_chart#} RandNumber="usersType-chart"}
<!-- Chart Libraries -->
<script src="/templates/assets/js/charts/morris/raphael-2.0.2.min.js"></script>
<script src="/templates/assets/js/charts/morris/morris.js"></script>

<script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
<script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
<script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
<script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
<script>
    {literal}
    function exportTo(ID, type) {
        $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
    }
    {/literal}
</script>
<script>
    var InitiateSimpleDataTable = function() {
        return {
            init: function() {
                //Datatable Initiating
                var oTable = $('.sortable-table').dataTable({
                    "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                    "iDisplayLength": 10,
                    "oTableTools": {
                        "aButtons": [

                        ],
                        "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                    },
                    "language": {
                        "search": "",
                        "sLengthMenu": "_MENU_",
                        "oPaginate": {
                            "sPrevious": "{#gnr_previous#}",
                            "sNext": "{#gnr_next#}"
                        }
                    }
                });
                $("tfoot input").keyup(function() {
                    /* Filter on the column (the index) of this element */
                    oTable.fnFilter(this.value, $("tfoot input").index(this));
                });
            }

        };

    }();

    InitiateSimpleDataTable.init();
</script>
{/block}
{block name=back}{url urltype="path" url_string="bsc/P057/Products/show/0/{$smarty.session.lang}"}{/block}
