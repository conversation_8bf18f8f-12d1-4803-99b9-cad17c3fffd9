<?php

/**
 * Created by PhpStorm.
 * User: developer
 * Date: 19/05/21
 * Time: 03:35 م
 */
use Models\Warehouse\Group;
use Models\Warehouse\Product;
use Models\Warehouse\Stock;
use Models\Warehouse\StockTransaction;
use Models\Warehouse\StockWithdrawal;

class bsc_P057_StockMovementReport extends Controller
{
    public function show($parm, $post)
    {

        switch ($parm[0]) {
            case 'menu';
                $_SESSION['filterParams'] = null;
                break;

            case 'filter';
                $_SESSION['filterParams'] = $post;
                break;
        }

        $stocks = Stock::with('location')->get();
        $products = Product::with('group')->get();
        $groups = Group::all();
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('groups', $groups);
        $this->Smarty->assign('products', $products);
        $_SESSION['stock'] = Stock::where('id', $post['stock_id'])->select('name')->first()->name;
        $_SESSION['group'] = Group::where('id', $post['group_id'])->select('name')->first()->name;
        $_SESSION['product'] = Product::where('id', $post['product_id'])->select('name')->first()->name;
        if ($_SESSION['filterParams'])
             $this->filter($_SESSION['filterParams']);
    }

    public function filter($post)
    {

        $data = StockTransaction::with('product.units.unit')
            ->with('stock')->with('unit')
            ->whereHas('product', function ($query) use ($post) {
            $query->when($post['product_id'], function ($query) use ($post) {
                $query->where('product_id', $post['product_id']);
            });
        })->whereHas('product.group', function ($query) use ($post) {
            $query->when($post['group_id'], function ($query) use ($post) {
                $query->where('group_id', $post['group_id']);
            });
        })->when($post['stock_id'], function ($query) use ($post) {
            $query->where('stock_id', $post['stock_id']);
        })->when($post['date'], function ($query) use ($post) {
            $query->whereBetween('date',[$post['datefrom'], $post['dateto']]);
        })->get()->groupBy(['stock_id', 'product_id']);
        $report_data = [];

        foreach ($data as $stock_id => $stock) {
            $stock_name = $stock->first()->first()->stock->name;
            foreach ($stock as $transaction) {
                foreach ($transaction as $dataRow) {
                    $row_data = [];
                    $units = [];
                    $i = 0;
                    $product_name = $dataRow->product->name;
                    $row_data['date'] = $dataRow->created_at;
                    $row_data["stock_name"] = $stock_name;
                    $row_data["ref"]= substr($dataRow->ref, 0, 2 ) ;
                    $row_data["product_name"] = $product_name;
                    $qty = $dataRow->qty;
                    $base = $dataRow->product->units->last(); //////// get the product units ordered by DESC
                    $base_price = $base->sale_price;
                    $type = 0;
                    $realqty  = $qty;
                    foreach ($dataRow->product->units as $unit) {
                        $unit_row = [];
                        $unit_name = $unit->unit->name;
                        $sale_price = $unit->sale_price;
                        $quantity_from_first = $sale_price/($base_price==0?1:$base_price);
                        $qty_unit = (int)($qty / ($quantity_from_first == 0 ? 1 : $quantity_from_first));
                        $qty = $qty - ($qty_unit * $quantity_from_first);
                        $unit_row["name"] = $unit_name;
                        $unit_row["qty"] = $qty_unit;
                        $type = $this->stockType($row_data["ref"],$realqty,$dataRow->ref);
                        $units [] = $unit_row;
                        $i++;
                    }
                    $row_data["units"] = $units;
                    $row_data["type"] = $type;
                    $row_data['num'] = $i;
                    $report_data [] = $row_data;
                }
            }
        }
        $data = $report_data;
        $this->Smarty->assign('data', $data);
    }
    public function stockType($ref,$qty,$serial_invoice){
        $type="";
        if($ref =="RS" and $qty>=0){
            $type=" أيداع-جرد";
        }
        elseif ($ref =="RS" and $qty<0){
            $type="سحب-جرد";
        }elseif ($ref =="SD" and $qty<=0){
            $type="سحب-تالف";
        }
        elseif ($ref == "HO" ){
            $type="أيداع";
        }
        elseif ($ref =="WH" ){
            $withdral=StockWithdrawal::where('invoice_no' , $serial_invoice)->first();
            if ($withdral->fromInvoice()->first())
            {
                $type="مبيعات";
            }else{
                $type="سحب";
            }
        }
        elseif ($ref == "SI" ){
            $type="مبيعات";
        }
        elseif ($ref =="RI" ){
            $type="مردودات مبيعات";
        }
        elseif ($ref == "SD"){
            $type="سحب-تالف";
        }elseif ($ref == "TI" and $qty>=0){
            $type="ايداع-تحويل الي مخزن";
        }
        elseif ($ref == "TI" and $qty<=0)
        {
            $type="سحب-من مخزن";
        }
        return $type;

    }
    public function print()
    {
        if ($_SESSION['filterParams'])
            $this->filter($_SESSION['filterParams']);
        generatePdf();
    }
}