<?php


use Models\Warehouse\Stock;
use Models\Warehouse\Location;
use Models\Finance\Account;

class bsc_P057_stocks extends Controller{
    public function show($parm , $post)
    {
        switch ($parm[0]){
            case 'insert':
                if ($parm[1] === $_SESSION['s_stocks_token']){
                    Validation::rules($post, [
                        'name' => 'required',
                        'type' => 'required',
                        'location_id' => 'required'
                    ]);
                    if(Validation::check()) {
                        $this->insert($parm, $post);
                    }
                }
                break;
            case 'delete':
                if ($parm[1] === $_SESSION['s_stocks_token']){
                    $this->delete($parm, $post);
                }
                break;

            case 'update':
                if ($parm[1] === $_SESSION['s_stocks_token']){
                    $this->update($parm,$post);
                }
                break;
        }
        $stocks = Stock::with('location')->with('stockWithdrawal')->with('stockDeposit')->orderBy('id','desc')->get();
        $this->Smarty->assign('stocks', $stocks);
        $_SESSION['s_stocks_token'] = Helper::generateToken();
    }

    public function add($parm , $post){
        $location=Location::all();
        $types = Setting::getList(295);
        $accounts=Account::currentYear()
            ->where('fin_acc_code' ,'like' , '11%')
            ->where('fin_acc_level' , '>=' , 4)
            ->doesntHave('children')
            ->get();
        $accounts = $accounts->map(function ($account) {
            return collect($account->toArray())
                ->only(['id', 'name', 'code'])
                ->all();
        });
        $this->Smarty->assign('accounts',$accounts) ;
        $this->Smarty->assign('types',$types);
        $this->Smarty->assign('locations',$location);
        $_SESSION['s_stocks_token'] = Helper::generateToken();
    }

    public function insert($parm , $post){
        $stock = new Stock($post);
        $stock->save();
        redirect('stocks/show');
    }

    public function edit($parm , $post){
        $types = Setting::getList(295);
        $this->Smarty->assign('types',$types);
        $location=Location::all();
        $accounts=Account::currentYear()
            ->where('fin_acc_code' ,'like' , '11%')
            ->where('fin_acc_level' , '>=' , 4)
            ->doesntHave('children')
            ->get();
        $accounts = $accounts->map(function ($account) {
            return collect($account->toArray())
                ->only(['id', 'name', 'code'])
                ->all();
        });
        $this->Smarty->assign('accounts',$accounts) ;
        $this->Smarty->assign('locations',$location);
        $this->Smarty->assign('stock',Stock::find($parm[0]));
    }

    public function update($parm , $post){
        $stock = Stock::find($parm[0]);
        $stock->fill($post);
        $stock->save();
        Notification::updatedAlert();
        redirect('stocks/show');
    }
    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', Stock::find($parm[0]));

        $_SESSION['s_stocks_token'] = Helper::generateToken();
    }

    public function delete($parm, $post)
    {
        $stock = Stock::find($parm[2])->delete();
        Notification::deletedAlert();
    }
}