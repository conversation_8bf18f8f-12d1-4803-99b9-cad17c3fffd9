<?php
/**
 * Created by PhpStorm.
 * User: ahmed
 * Date: 03/08/21
 * Time: 10:56
 */

use Models\Warehouse\StockAccountSetting;
use Models\Finance\Account;

/**
 * Class bsc_P057_StockAccountSetting
 */
class bsc_P057_StockAccountSetting extends Controller{
    public function show($parm , $post)
    {
        warehouse_stock_account_setting::up();
        switch ($parm[0]){
            case 'insert':
                if ($parm[1] === $_SESSION['s_stock_account_setting_token']){
                    Validation::rules($post, [
                        'account_cost_id' => 'required',
                        'account_consumable_id' => 'required',
                        'account_recancelation_id' => 'required',
                        'account_damage_id' => 'required'
                    ]);
                    if(Validation::check()) {
                        $this->insert($parm, $post);
                    }
                }
                break;
            case 'update':
                if ($parm[1] === $_SESSION['s_stock_account_setting_token']){
                    $this->update($parm,$post);
                }
                break;
        }
        $stockAccountSetting =StockAccountSetting::with(['accountCost','accountConsumable','accountRecancelation','accountDamage'])->get();
        $this->Smarty->assign('stockAccountSetting', $stockAccountSetting);
        $_SESSION['s_stock_account_setting_token'] = Helper::generateToken();
    }

    public function add($parm , $post){
        $accounts=Account::currentYear()
            ->where('fin_acc_code' ,'like' , '11%')
            ->where('fin_acc_level' , '>=' , 4)
            ->doesntHave('children')
            ->get();
        $accounts = $accounts->map(function ($account) {
            return collect($account->toArray())
                ->only(['id', 'name', 'code'])
                ->all();
        });
        $this->Smarty->assign('accounts',$accounts) ;
        $this->Smarty->assign('$stockAccountSetting',$stockAccountSetting);
        $_SESSION['s_stock_account_setting_token'] = Helper::generateToken();
    }

    public function insert($parm , $post){
        $stockAccountSetting = new StockAccountSetting($post);
        $stockAccountSetting->save();
        redirect('StockAccountSetting/show');
    }

    public function edit($parm , $post){
        $accounts=Account::currentYear()
            ->where('fin_acc_code' ,'like' , '11%')
            ->where('fin_acc_level' , '>=' , 4)
            ->doesntHave('children')
            ->get();
        $accounts = $accounts->map(function ($account) {
            return collect($account->toArray())
                ->only(['id', 'name', 'code'])
                ->all();
        });
        $this->Smarty->assign('accounts',$accounts) ;
        $this->Smarty->assign('stockAccountSetting',StockAccountSetting::find($parm[0]));
    }

    public function update($parm , $post){
        $stockAccountSetting = StockAccountSetting::find($parm[0]);
        $stockAccountSetting->fill($post);
        $stockAccountSetting->save();
        Notification::updatedAlert();
        redirect('StockAccountSetting/show');
    }
}