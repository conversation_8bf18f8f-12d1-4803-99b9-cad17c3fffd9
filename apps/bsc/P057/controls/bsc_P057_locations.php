<?php


use Models\Warehouse\Location;

class bsc_P057_locations extends Controller
{
    public function show($parm, $post)
    {

        switch ($parm[0]){
            case 'insert':
                if ($parm[1] === $_SESSION['s_locations_token']){
                    Validation::rules($post, [
                        'name' => 'required',
                    ]);
                    if(Validation::check()) {
                        $this->insert($parm, $post);
                    }
                }
                break;
            case 'delete':
                if ($parm[1] === $_SESSION['s_locations_token']){
                    $this->delete($parm, $post);
                }
                break;

            case 'update':
                if ($parm[1] === $_SESSION['s_locations_token']){
                    Validation::rules($post, [
                        'name' => 'required',
                    ]);
                    if(Validation::check()) {
                        $this->update($parm, $post);
                    }
                }
                break;
        }

        $locations = Location::with('stocks') ->orderBy('id','desc')->get();
        $this->Smarty->assign('locations', $locations);

        $_SESSION['s_locations_token'] = Helper::generateToken();
    }

    public function add($parm, $post)
    {
        $_SESSION['s_locations_token'] = Helper::generateToken();
    }

    public function insert($parm, $post)
    {

        $location = new Location($post);

        $location->save();
        redirect('locations/show');

    }

    public function edit($parm, $post)
    {
        $this->Smarty->assign('location', Location::find($parm[0]));
    }

    public function update($parm, $post)
    {
        $location = Location::find($parm[2]);

        $location->fill($post);

        $location->save();
        Notification::updatedAlert();
        redirect('locations/show');

    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', Location::find($parm[0]));

        $_SESSION['s_locations_token'] = Helper::generateToken();
    }

    public function delete($parm, $post)
    {
        $location = Location::find($parm[2])->delete();

        Notification::deletedAlert();
    }
}