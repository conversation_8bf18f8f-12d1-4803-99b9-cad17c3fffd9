<?php

/**
 * Created by PhpStorm.
 * User: developer
 * Date: 19/05/21
 * Time: 01:09 م
 */

use Models\Warehouse\Stock;
use Models\Warehouse\StockWithdrawal;
use Models\Warehouse\StockWithdrawalDetail;


defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

//bsc_P057_StockMovementReport


class bsc_P057_StockWithdrawalsReport extends  Controller
{

    public function show($parm, $post)
    {

        switch ($parm[0]) {
            case 'menu';
                $_SESSION['filterParams'] = null;
                break;

            case 'filter';
                $_SESSION['filterParams'] = $post;
                break;
        }

        $types = Setting::getList(295);
        $types = collect($types);
        $_SESSION['type']=$types->where('id',$post['type'])->first()->translatedName;
        $stocks = Stock::with('location')->get();
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('types', $types);
        $_SESSION['stock']=Stock::where('id' , $post['stock_id'])->select('name')->first()->name;
        if ($_SESSION['filterParams'])
            $this->filter($_SESSION['filterParams']);


    }

    public function filter($post)
    {
        $data =StockWithdrawal::with('stockWithdrawalDetail.productUnit.unit')->with('stock')
            ->when($post['stock_id'], function ($query) use ($post) {
                $query->where('stock_id', $post['stock_id']);
            })
            ->whereHas('stock', function ($query) use($post){
                $query->when($post['type'], function ($query) use ($post) {
                    $query->where('type', $post['type']);
                });
            })
            ->when(($post['datefrom'] and $post['dateto']), function ($query) use ($post) {
                $query->whereBetween('date', [$post['datefrom'], $post['dateto']]);
            })
            ->get();
        $this->Smarty->assign('data', $data);
    }

    public function print()
    {
        if ($_SESSION['filterParams'])
            $this->filter($_SESSION['filterParams']);

        generatePdf();

    }

    public function details($parm,$post)
    {
        $details=StockWithdrawalDetail::where('stock_withdrawal_id',$parm[0])->with('product.units.unit')->get();
        $this->Smarty->assign('details', $details);
        $_SESSION['filterParams'] = $post;
    }

}