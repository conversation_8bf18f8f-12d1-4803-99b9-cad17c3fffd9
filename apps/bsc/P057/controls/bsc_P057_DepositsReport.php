<?php


use Models\Warehouse\Group;
use Models\Warehouse\Product;
use Models\Warehouse\Stock;
use Models\Warehouse\StockTransaction;


defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');
//
class bsc_P057_DepositsReport extends Controller
{
    public function show($parm, $post)
    {

        switch ($parm[0]) {
            case 'menu';
                $_SESSION['filterParams'] = null;
                break;

            case 'filter';
                $_SESSION['filterParams'] = $post;
                break;
        }

        $stocks = Stock::with('location')->get();
        $products =Product::with('group')->get();
        $groups = Group::all();
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('groups', $groups);
        $this->Smarty->assign('products', $products);
        $_SESSION['stock']=Stock::where('id' , $post['stock_id'])->select('name')->first()->name;
        $_SESSION['group']=Group::where('id' , $post['group_id'])->select('name')->first()->name;
        $_SESSION['product']=Product::where('id' , $post['product_id'])->select('name')->first()->name;
        if ($_SESSION['filterParams'])
            $this->filter($_SESSION['filterParams']);


    }

    public function filter($post)
    {

        $data =StockTransaction::with('product.units.unit')->with('stock')->with('unit')
            ->whereHas('product', function ($query) use($post){
                $query->when($post['product_id'], function ($query) use ($post) {
                    $query->where('product_id', $post['product_id']);
                });
            })
            ->whereHas('product.group', function ($query) use($post){
                $query->when($post['group_id'], function ($query) use ($post) {
                    $query->where('group_id', $post['group_id']);
                });
            })
            ->when($post['stock_id'], function ($query) use ($post) {
                $query->where('stock_id', $post['stock_id']);
            })
            ->when($post['date'], function ($query) use ($post) {
                $query->whereDate('date', $post['date']);
            })->get()->groupBy(['stock_id','product_id']);
        $report_data = [];

        foreach($data as $stock_id => $stock){
            $stock_name = $stock->first()->first()->stock->name;
            foreach($stock as $product_id => $product){
                $row_data = [];
                $units = [];
                $product_name = $product->first()->product->name;
                $row_data["stock_name"] = $stock_name;
                $row_data["product_name"] = $product_name;
                $qty = $product->sum('qty');
                $base = $product->first()->product->units->last();
                $base_price = $base->sale_price;
                foreach ($product->first()->product->units as $unit) {
                    $unit_row = [];
                    $unit_name = $unit->unit->name;
                    $sale_price = $unit->sale_price;
                    $quantity_from_first = $sale_price/($base_price==0?1:$base_price);
                    $qty_unit = (int)($qty / ($quantity_from_first == 0 ? 1 : $quantity_from_first));
                    $qty = $qty - ($qty_unit * $quantity_from_first);
                    $unit_row["name"] = $unit_name;
                    $unit_row["qty"] = $qty_unit;
                    $units [] = $unit_row;
                }
                $row_data["units"] = $units;
                $report_data []= $row_data;
            }
        }

        $data = $report_data;
        $this->Smarty->assign('data', $data);
    }

    public function print()
    {
        if ($_SESSION['filterParams'])
            $this->filter($_SESSION['filterParams']);
        generatePdf();

    }
}