<?php


use Models\Warehouse\Product;
use Models\Warehouse\Stock;
use Models\Warehouse\MovementBetweenStock;
use Models\Warehouse\MovementBetweenStockDetail;
use Models\Warehouse\StockTransaction;
use Models\Warehouse\ProductUnit;
use Models\Warehouse\StockAccountSetting;
use Models\Finance\Year;


defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class bsc_P057_MovementBetweenStocks extends Controller
{

    public function show($parm, $post)
    {
        switch ($parm[0]) {
            case 'menu';
                $_SESSION['filterParams'] = null;
                break;

            case 'filter';
                $_SESSION['filterParams'] = $post;
                break;
            case 'insert':
                if ($parm[1] === $_SESSION['s_move_between_stocks_token']) {
                    Validation::rules($post, [
                        'date' => 'required|before_or_equal:'.now(),
                        'from_stock_id' => 'required',
                        'to_stock_id' => 'required',
                    ]);
                    if(Validation::check()) {
                         $this->insert($parm, $post);
                    }
                }
                break;
            case 'delete':
                if ($parm[1] === $_SESSION['s_move_between_stocks_token']) {
                    $this->delete($parm, $post);
                }
                break;

            case 'update':
                if ($parm[2] === $_SESSION['s_move_between_stocks_token']) {
                    Validation::rules($post, [
                        'date' => 'required|before_or_equal:'.now(),
                        'from_stock_id' => 'required',
                        'to_stock_id' => 'required',
                    ]);
                    if(Validation::check()) {
                       $this->update($parm, $post);
                    }
                }
                break;
            case 'details':
                if ($parm[1] === $_SESSION['s_move_between_stocks_token']) {
                    $this->details($parm, $post);
                }
                break;
        }
        $types = Setting::getList(295);
        $types = collect($types);
        $_SESSION['type']=$types->where('id',$post['type'])->first()->translatedName;
        $_SESSION['from_stock']=Stock::where('id' , $post['from_stock_id'])->select('name')->first()->name;
        $_SESSION['to_stock']=Stock::where('id' , $post['to_stock_id'])->select('name')->first()->name;
        $movementBetweenStockUsers =MovementBetweenStock::all();
        $_SESSION['created_by']=MovementBetweenStock::where('created_by' , $post['created_by'])->select('created_by')->first()->created_by;
        $_SESSION['invoice_no']=MovementBetweenStock::where('invoice_no' , $post['invoice_no'])->select('invoice_no')->first()->invoice_no;
        $stocks = Stock::with('location')->get();
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('types', $types);
        $this->Smarty->assign('movementBetweenStockUsers',$movementBetweenStockUsers);
        $this->filter($_SESSION['filterParams']);
        $_SESSION['s_move_between_stocks_token'] = Helper::generateToken();
    }
    public function filter($post)
    {
        $movementBetweenStocks =MovementBetweenStock::with('movementBetweenStockDetail.productUnit.unit')->with('fromStock')->with('toStock')
            ->when($post['from_stock_id'], function ($query) use ($post) {
                $query->where('from_stock_id', $post['from_stock_id']);
            })
            ->when($post['to_stock_id'], function ($query) use ($post) {
                $query->where('to_stock_id', $post['to_stock_id']);
            })
            ->when($post['created_by'], function ($query) use ($post) {
                $query->where('created_by', $post['created_by']);
            })
            ->when($post['invoice_no'], function ($query) use ($post) {
                $query->where('invoice_no', $post['invoice_no']);
            })
            ->whereHas('fromStock', function ($query) use($post){
                $query->when($post['type'], function ($query) use ($post) {
                    $query->where('type', $post['type']);
                });
            })
            ->whereHas('toStock', function ($query) use($post){
                $query->when($post['type'], function ($query) use ($post) {
                    $query->where('type', $post['type']);
                });
            })
            ->when(($post['datefrom'] and $post['dateto']), function ($query) use ($post) {
                $query->whereBetween('date', [$post['datefrom'], $post['dateto']]);
            })
            ->orderBy('id','desc')->paginated();
        $this->Smarty->assign('movementBetweenStocks', $movementBetweenStocks);
    }

    public function add($parm, $post)
    {
        $date = now();
        $yearFormat=date_format($date,"Y-m");
        $pieces = explode("-", $yearFormat);
        $year =$pieces[0];
        $invoice_no = 'TI' .'-'.$year;
        $stocks = Stock::all();
        $products = Product::where('status',23)->where('type',1360)->with('unit')->get();
        $this->Smarty->assign('products', $products);
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('invoice_no', $invoice_no);
        $_SESSION['s_move_between_stocks_token'] = Helper::generateToken();
    }

    public function insert($parm, $post)
    {
        $post['invoice_no']=0;
        $post['created_by']=$_SESSION['user']->id;
        $movementBetweenStock= new MovementBetweenStock();
        $movementBetweenStock->fill($post);
        $movementBetweenStock->save();
        $random=str_pad((int)$movementBetweenStock->id,6, '0', STR_PAD_LEFT); //// generate 3 zeros before number like 0001
        $date = now();
        $yearFormat=date_format($date,"Y-m");
        $pieces = explode("-", $yearFormat);
        $year =$pieces[0];
        $invoice_no = 'TI' .'-'.$year.'-'.$random;
        $movementBetweenStock->invoice_no =$invoice_no;
        $movementBetweenStock->save();
        $debit = 0;
        $credit = 0;
        $narration = '';
        $totalAmount = 0;
        $debit= $movementBetweenStock->toStock->account_id;
        $credit = $movementBetweenStock->fromStock->account_id;
        foreach ($post['data'] as $d) {
            $movementBetweenStockDetail = new MovementBetweenStockDetail();
            $movementBetweenStockDetail->fill([
                "movement_between_stock_id" => $movementBetweenStock->id,
                "product_id" => $d["product"],
                "product_unit_id" => $d["unit"],
                "qty" => $d["qty"],
            ]);
            $movementBetweenStockDetail->save();

        }
        ///////////////// withdrawal from souece stock //////////////////
        foreach ($post['data'] as $index => $d) {

            $units = ProductUnit::where('product_id' , $post['data'][$index]['product'])->orderBy('sale_price')->get();
            $price = $units->where('id' , $d["unit"])->first()->sale_price;
            $first_sale = $units->first()->sale_price;
            $first_unit_id = $units->first()->stock_unit_id;
            $quantity_from_first = $price / $first_sale;
            $qty = $d["qty"] * $quantity_from_first;
            $transaction =new StockTransaction();
            $transaction->fill([
                "product_id" => $d["product"],
                "stock_id" =>  $movementBetweenStock->from_stock_id,
                "stock_unit_id" => $first_unit_id,
                "qty" => (-1*$qty),
                "date" =>  $movementBetweenStock->date,
                "ref" => $movementBetweenStock->invoice_no
            ]);
            $m_purchase_price=$units->where('id' , $d["unit"])->first()->m_purchase_price;
            $invoiceNo = $movementBetweenStock->invoice_no;
            $narration = $movementBetweenStock->note != null ? $movementBetweenStock->note :'عملية تحويل من مخزن ';
            $totalAmount +=$m_purchase_price * $d["qty"];
            $transaction->save();
        }

        //////////////// deposit from destenation stock /////////////////
        foreach ($post['data'] as $index => $d) {

            $units = ProductUnit::where('product_id' , $post['data'][$index]['product'])->orderBy('sale_price')->get();
            $price = $units->where('id' , $d["unit"])->first()->sale_price;
            $first_sale = $units->first()->sale_price;
            $first_unit_id = $units->first()->stock_unit_id;
            $quantity_from_first = $price / $first_sale;
            $qty = $d["qty"] * $quantity_from_first;
            $transaction =new StockTransaction();
            $transaction->fill([
                "product_id" => $d["product"],
                "stock_id" =>  $movementBetweenStock->to_stock_id,
                "stock_unit_id" => $first_unit_id,
                "qty" => ($qty),
                "date" =>  $movementBetweenStock->date,
                "ref" => $movementBetweenStock->invoice_no
            ]);
            $transaction->save();
        }
        //////////////////// start builing entry /////////////////////////
        $entry = new \Models\Finance\Entry();
        $entry->org_id = $_SESSION['organization']->id;
        $entry->amount = $totalAmount;
        $entry->type = FinEntry::SETTING_GENERAL_ENTRY;
        $entry->created_by = $entry->last_update_by = user('id');
        $entry->created_date = today();
        $entry->date = today();
        $entry->year_id = Year::getActiveYear()->id;
        $entry->ref_num = $invoice_no;
        $entry->statement = $narration;
        $entry->status = FinEntry::SETTING_PROTECTED_ENTRY;
        $entry->equilibrium = 0;
        $entry->save();

        $debitTransaction = new \Models\Finance\Transaction();
        $debitTransaction->year_id = $entry->year_id;
        $debitTransaction->org_id = organization('id');
        $debitTransaction->entery_id = $entry->id;
        $debitTransaction->entery_date = $entry->last_update_date;
        $debitTransaction->entery_type = $entry->type;
        $debitTransaction->entery_num = $entry->num ?? $entry->code ;
        $debitTransaction->acc_id = $debit;
        $debitTransaction->depit = $totalAmount;
        $debitTransaction->credit = 0;
        $debitTransaction->comment = $narration;
        $debitTransaction->created_by = user('id');
        $debitTransaction->created_date = $entry->created_date;
        $debitTransaction->save();

        $creditTransaction = new \Models\Finance\Transaction();
        $creditTransaction->year_id = $entry->year_id;
        $creditTransaction->org_id = organization('id');
        $creditTransaction->entery_id = $entry->id;
        $creditTransaction->entery_type = $entry->type;
        $creditTransaction->entery_date = $entry->last_update_date;
        $creditTransaction->acc_id = $credit;
        $creditTransaction->depit = 0;
        $creditTransaction->credit = $totalAmount;
        $creditTransaction->comment = $narration;
        $creditTransaction->created_by = user('id');
        $creditTransaction->created_date = $entery->created_date;
        $creditTransaction->save();
        $entry->lockEntry();
        $entry->num = null;
        $entry->save();
        try {
            FinEntry::registerEntry(fin_entery::readByID($entry->id));
        } catch (Exception $e){
//            Notification::existAlert();
        }
        $entry->postEntry();

//////////////////// end builing entry /////////////////////////
        redirect('MovementBetweenStocks/show');
    }


    public function edit($parm, $post)
    {
        $movementBetweenStock = MovementBetweenStock::find($parm[0])->load('movementBetweenStockDetail.product.units.unit');
        $stocks = Stock::all();
        $products = Product::where('status',23)->where('type',1360)->with('unit')->get();
        $this->Smarty->assign('products', $products);
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('movementBetweenStock',$movementBetweenStock);
    }

    public function update($parm, $post)
    {

        $movementBetweenStock = MovementBetweenStock::find($parm[1]);
        $movementBetweenStock->fill($post);
        $movementBetweenStock->save();
        $debit = 0;
        $credit = 0;
        $narration = '';
        $totalAmount = 0;
        $debit= $movementBetweenStock->toStock->account_id;
        $credit = $movementBetweenStock->fromStock->account_id;
        MovementBetweenStockDetail::where('movement_between_stock_id' , $movementBetweenStock->id)->delete();
        foreach ($post['data'] as $d) {
            $row = MovementBetweenStockDetail::updateOrCreate(
                [
                    'movement_between_stock_id' => $movementBetweenStock->id,
                    'product_unit_id' => $d["unit"],
                    "product_id" => $d["product"],
                ]
                ,[
                "qty" => $d["qty"],
            ]);
        }
        ///////////////// withdrawal from souece stock //////////////////
        foreach ($post['data'] as $index =>$d) {

            $units = ProductUnit::where('product_id' , $post['data'][$index]['product'])->orderBy('sale_price')->get();
            $price = $units->where('id' , $d["unit"])->first()->sale_price;
            $first_sale = $units->first()->sale_price;
            $first_unit_id = $units->first()->stock_unit_id;
            $quantity_from_first = $price / $first_sale;
            $qty = $d["qty"] * $quantity_from_first;
            $row =StockTransaction::updateOrCreate([
                    "ref" =>    $movementBetweenStock->invoice_no,
                    "product_id" => $d["product"],
                    "stock_unit_id" => $first_unit_id,
                    "stock_id" => $movementBetweenStock->from_stock_id
                ],[
                    "qty" => (-1*$qty),
                    "date" =>  $movementBetweenStock->date,
                ]);
            $m_purchase_price=$units->where('id' , $d["unit"])->first()->m_purchase_price;
            $invoiceNo = $movementBetweenStock->invoice_no;
            $narration = $movementBetweenStock->note != null ? $movementBetweenStock->note :'عملية تحويل من مخزن ';
            $totalAmount +=$m_purchase_price * $d["qty"];



        }
        //////////////// deposit from destenation stock /////////////////

        foreach ($post['data'] as $index =>$d) {

            $units = ProductUnit::where('product_id' , $post['data'][$index]['product'])->orderBy('sale_price')->get();
            $price = $units->where('id' , $d["unit"])->first()->sale_price;
            $first_sale = $units->first()->sale_price;
            $first_unit_id = $units->first()->stock_unit_id;
            $quantity_from_first = $price / $first_sale;
            $qty = $d["qty"] * $quantity_from_first;
            $row =StockTransaction::updateOrCreate([
                "ref" =>    $movementBetweenStock->invoice_no,
                "product_id" => $d["product"],
                "stock_unit_id" => $first_unit_id,
                "stock_id" => $movementBetweenStock->to_stock_id
            ],[
                "qty" => ($qty),
                "date" =>  $movementBetweenStock->date,
            ]);

        }
        //////////////////// start builing entry /////////////////////////
        $entry = Models\Finance\Entry::where('fin_entery_ref_num' , $invoiceNo)->first();
        $entry->amount = $totalAmount;
        $entry->statement = $narration;
        $entry->save();

        $debitTransaction = \Models\Finance\Transaction::where('fin_trans_entery_id', $entry->id)->where('fin_trans_depit' , '>' , 0)->first();
        $debitTransaction->depit = $totalAmount;
        $debitTransaction->comment = $narration;
        $debitTransaction->save();

        $creditTransaction  = \Models\Finance\Transaction::where('fin_trans_entery_id', $entry->id)->where('fin_trans_credit' , '>' , 0)->first();;
        $creditTransaction->credit = $totalAmount;
        $creditTransaction->comment = $narration;
        $creditTransaction->save();
        //////////////////// end builing entry /////////////////////////
        Notification::updatedAlert();
        redirect('MovementBetweenStocks/show');
    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', MovementBetweenStock::find($parm[0]));
        $_SESSION['s_move_between_stocks_token'] = Helper::generateToken();
    }

    public function delete($parm, $post)
    {
        $invoice_no = MovementBetweenStock::find($parm[2])->invoice_no;
        $stockTransaction=StockTransaction::where('ref',$invoice_no)->delete();
        $movementBetweenStockDetail = MovementBetweenStockDetail::where('movement_between_stock_id',$parm[2])->delete();
        $movementBetweenStock = MovementBetweenStock::find($parm[2])->delete();
        Notification::deletedAlert();
        redirect('MovementBetweenStocks/show');
    }

    public function details($parm)
    {
        $movementBetweenStock = MovementBetweenStock::with('fromStock')->with('toStock')->find($parm[0]);
        $details = MovementBetweenStockDetail::with('product')->with('productUnit.unit')->where('movement_between_stock_id', $movementBetweenStock->id)->get();
        $this->Smarty->assign('details', $details);
        $this->Smarty->assign('movementBetweenStock', $movementBetweenStock);
    }
    public function print($parm)
    {
        $this->details($parm);
        generatePdf();

    }
}