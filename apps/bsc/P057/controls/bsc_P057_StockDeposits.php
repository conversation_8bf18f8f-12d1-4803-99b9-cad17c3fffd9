<?php


use Domain\Warehouse\Services\WarehouseEntry;
use Illuminate\Validation\ValidationException;
use Models\Warehouse\Product;
use Models\Warehouse\Stock;
use Models\Warehouse\StockDeposit;
use Models\Warehouse\StockDepositDetail;
use Models\Warehouse\StockTransaction;
use Models\Warehouse\ProductUnit;
use Models\Warehouse\StockAccountSetting;
use Models\Finance\Year;


defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class bsc_P057_StockDeposits extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {
            case 'menu';
                $_SESSION['filterParams'] = null;
                break;
            case 'filter';
                $_SESSION['filterParams'] = $post;
                break;
            case 'insert':
                if ($parm[1] === $_SESSION['s_stock_deposits_token']) {
                    Validation::rules($post, [
                        'date' => 'required|before_or_equal:'.now(),
                        'stock_id' => 'required',
                    ]);
                    if(Validation::check()) {
                        $this->insert($parm, $post);
                    }
                }
                break;
            case 'delete':
                if ($parm[1] === $_SESSION['s_stock_deposits_token']) {
                    $this->delete($parm, $post);
                }
                break;

            case 'update':
                if ($parm[2] === $_SESSION['s_stock_deposits_token']) {
                    Validation::rules($post, [
                        'date' => 'required|before_or_equal:'.now(),
                        'stock_id' => 'required',
                    ]);
                    if(Validation::check()) {
                        $this->update($parm, $post);
                    }
                }
                break;
            case 'details':
                    $this->details($parm, $post);
                break;
        }
        $types = Setting::getList(295);
        $types = collect($types);
        $stockDepositUsers = StockDeposit::all();
        $_SESSION['created_by']=StockDeposit::where('created_by' , $post['created_by'])->select('created_by')->first()->created_by;
        $_SESSION['invoice_no']=StockDeposit::where('invoice_no' , $post['invoice_no'])->select('invoice_no')->first()->invoice_no;
        $stocks = Stock::with('location')->get();
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('stockDepositUsers', $stockDepositUsers);
        $this->Smarty->assign('types', $types);

        $this->filter($_SESSION['filterParams']);
        $_SESSION['s_stock_deposits_token'] = Helper::generateToken();
    }
    public function filter($post)
    {
        $stockDeposits = StockDeposit::with('stockDepositDetail.productUnit.unit')->with('stock')
            ->when($post['stock_id'], function ($query) use ($post) {
                $query->where('stock_id', $post['stock_id']);
            })
            ->when($post['created_by'], function ($query) use ($post) {
                $query->where('created_by', $post['created_by']);
            })
            ->when($post['invoice_no'], function ($query) use ($post) {
                $query->where('invoice_no', $post['invoice_no']);
            })
            ->whereHas('stock', function ($query) use($post){
                $query->when($post['type'], function ($query) use ($post) {
                    $query->where('type', $post['type']);
                });
            })
            ->when(($post['datefrom'] and $post['dateto']), function ($query) use ($post) {
                $query->whereBetween('date', [$post['datefrom'], $post['dateto']]);
            })
            ->orderBy('id','desc')->paginated();
        $this->Smarty->assign('stockDeposits', $stockDeposits);
    }

    public function add($parm, $post)
    {
        $date = now();
        $yearFormat=date_format($date,"Y-m");
        $pieces = explode("-", $yearFormat);
        $year =$pieces[0];
        $invoice_no = 'HO' .'-'.$year;
        $stocks = Stock::all();
        $products = Product::where('status',23)->where('type',1360)->with('unit')->get();
        $this->Smarty->assign('products', $products);
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('invoice_no', $invoice_no);
        $_SESSION['s_stock_deposits_token'] = Helper::generateToken();
    }

    public function insert($parm, $post)
    {
//        DB::beginTransaction();
        try {
            $post['invoice_no'] = 0;
            $post['created_by'] = $_SESSION['user']->id;
            $stockDeposit = new StockDeposit();
            $stockDeposit->fill($post);
            $stockDeposit->save();
            $date = now();
            $yearFormat = date_format($date, "Y-m");
            $pieces = explode("-", $yearFormat);
            $year = $pieces[0];
            $random = str_pad((int)$stockDeposit->id, 6, '0', STR_PAD_LEFT);
            $invoice_no = 'HO' . '-' . $year . '-' . $random;
            $stockDeposit->invoice_no = $invoice_no;
            $stockDeposit->save();
            $debit = 0;
            $credit = 0;
            $narration = '';
            $totalAmount = 0;
            $credit = $stockDeposit->stock->account_id;
            $debit=StockAccountSetting::first()->account_consumable_id;

            foreach ($post['data'] as $d) {

                $stockDepositDetail = new StockDepositDetail();
                $stockDepositDetail->fill([
                    "stock_deposit_id" => $stockDeposit->id,
                    "product_id" => $d["product"],
                    "product_unit_id" => $d["unit"],
                    "qty" => $d["qty"],
                ]);
                $stockDepositDetail->save();

            }
            foreach ($post['data'] as $index => $d) {

                $units = ProductUnit::where('product_id', $post['data'][$index]['product'])->orderBy('sale_price')->get();
                $price = $units->where('id', $d["unit"])->first()->sale_price;       /// get the sale price for this unit
                $first_sale = $units->first()->sale_price;                           /////// get the minimam sale price
                $first_unit_id = $units->first()->stock_unit_id;                ///// get the minimam unit
                $quantity_from_first = $price / $first_sale;                   /////// get the number of minimam unit
                $qty = $d["qty"] * $quantity_from_first;                      ///////// convert to the minimam unit
                $transaction = new StockTransaction();
                $transaction->fill([
                    "product_id" => $d["product"],
                    "stock_id" => $stockDeposit->stock_id,
                    "stock_unit_id" => $first_unit_id,
                    "qty" => $qty,
                    "date" => $stockDeposit->date,
                    "ref" => $stockDeposit->invoice_no
                ]);

                $m_purchase_price = $units->where('id', $d["unit"])->first()->m_purchase_price;
                $invoiceNo = $stockDeposit->invoice_no;
                $narration = $stockDeposit->note != null ? $stockDeposit->note : 'عملية استلام من مخزن ';
                $totalAmount += $m_purchase_price * $d["qty"];

                $transaction->save();

            }
            $warehouse = new WarehouseEntry();
            $isCreated = $warehouse->createDoubleEntry($debit,$credit,$totalAmount,user('id'),$invoice_no,$narration);
            if($isCreated){
                Notification::createdAlert();
            }
            Notification::failAlert();
        }
        catch(ValidationException $e)
        {
            Notification::failAlert();
        }
//        DB::commit();
//        Notification::createdAlert();
        redirect('StockDeposits/show');
    }


    public function edit($parm, $post)
    {
        $stockDeposit=StockDeposit::find($parm[0])->load('stockDepositDetail.product.units.unit');
        $type = Stock::findOrFail($stockDeposit->stock_id)->type;
        $stockType = Setting::readID($type)->translatedName;
        $stocks = Stock::all();
        $products = Product::where('status',23)->where('type',1360)->with('unit')->get();
        $this->Smarty->assign('products', $products);
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('stockDeposit',$stockDeposit);
        $this->Smarty->assign('stockType',$stockType);
    }

    public function update($parm, $post)
    {
        $stockDeposit = StockDeposit::find($parm[1]);
        $stockDeposit->fill($post);
        $stockDeposit->save();
        StockDepositDetail::where('stock_deposit_id',$stockDeposit->id)->delete();
        foreach ($post['data'] as $d) {
            $row = StockDepositDetail::updateOrCreate([
                'stock_deposit_id' => $stockDeposit->id,
                "product_id" => $d["product"],
                "product_unit_id" => $d["unit"],
                ],
                [
                "qty" => $d["qty"],
            ]);

        }
        StockTransaction::where('ref',$stockDeposit->invoice_no)->delete();
        foreach ($post['data'] as $index => $d) {
            $units = ProductUnit::where('product_id' , $post['data'][$index]['product'])->orderBy('sale_price')->get();
            $price = $units->where('id' , $d["unit"])->first()->sale_price;       /// get the sale price for this unit
            $first_sale = $units->first()->sale_price;                           /////// get the minimam sale price
            $first_unit_id = $units->first()->stock_unit_id;                ///// get the minimam unit
            $quantity_from_first = $price / $first_sale;                   /////// get the number of minimam unit
            $qty = $d["qty"] * $quantity_from_first;                      ///////// convert to the minimam unit
            $transaction = new StockTransaction();
            $transaction->fill([
                "ref" =>    $stockDeposit->invoice_no,
                "product_id" => $d["product"],
                "stock_unit_id" => $first_unit_id,
                "stock_id" => $stockDeposit->stock_id,
                "qty" =>$qty,
                "date" =>  $stockDeposit->date,
            ]);
            $transaction->save();
        }
        Notification::updatedAlert();
        redirect('StockDeposits/show');
    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', StockDeposit::find($parm[0]));
        $_SESSION['s_stock_deposits_token'] = Helper::generateToken();
    }

    public function delete($parm, $post)
    {
        $invoice_no = StockDeposit::find($parm[2])->invoice_no;
        $stockTransaction=StockTransaction::where('ref',$invoice_no)->delete();
        $stockDepositDetail = StockDepositDetail::where('stock_deposit_id',$parm[2])->delete();
        $stockDeposit = StockDeposit::find($parm[2])->delete();
        Notification::deletedAlert();
    }

    public function details($parm)
    {
        $stockDeposit = StockDeposit::with('stock')->find($parm[0]);
        $details = StockDepositDetail::with('product')->with('productUnit.unit')->where('stock_deposit_id', $stockDeposit->id)->get();
        $this->Smarty->assign('details', $details);
        $this->Smarty->assign('stockDeposit', $stockDeposit);
    }
    public function print($parm)
    {
        $this->details($parm);
        generatePdf();

    }
}