<?php

/**
 * Created by PhpStorm.
 * User: developer
 * Date: 01/06/21
 * Time: 11:21 ص
 */
use Models\sales\Model_processing;
use Models\Warehouse\ProductUnit;
use Models\Warehouse\Stock;
use Models\Warehouse\StockRecancelation;
use Models\Warehouse\StockRecancelationDetail;
use Models\Warehouse\StockTransaction;
use Models\Warehouse\StockAccountSetting;
use Models\Finance\Year;


use fin_entery;
use Operation;
use FinEntry;

class bsc_P057_InventoryAndStoreMatching extends Controller
{

    public function show($parm, $post)
    {

        switch ($parm[0]) {
            case 'menu';
                $_SESSION['filterParams'] = null;
                break;

            case 'filter';
                $_SESSION['filterParams'] = $post;
                break;
            case 'insert';
                $_SESSION['InsertParams'] = $post;
                $this->insert($_SESSION['InsertParams']);
                break;

        }
        $date = now();
        $year = date_format($date, "Y");
        $invoice_no = 'RS' . '-' . $year;

        $stocks = Stock::with('location')->get();
        $process = StockTransaction::all('id', 'ref');

        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('process', $process);
        $this->Smarty->assign('invoice_no', $invoice_no);
        $_SESSION['process'] = StockTransaction::where('id', $post['process_id'])->select('ref')->first()->ref;
        $_SESSION['stock'] = Stock::where('id', $post['stock_id'])->select('name')->first()->name;

        if ($_SESSION['filterParams']) $this->filter($_SESSION['filterParams']);


    }

    /**
     * this function calculate the current amount in a real world with current amount which
     * is stored in snso store system.
     * step as :
     * 1) if the amount in the real is less than in amount in the system
     * the user should enter the current amount in a real world  as it is.
     * calculate the different amount between the current amount in the system and current amount in a real world
     * the different amount stored in StockTransaction as negative amount with invoice start with RS-date-increment.
     * 2) if the amount in the real is greater  than in amount in the system
     * the user should enter the current amount in a real world  as it is.
     * calculate the different amount between the current amount in the system and current amount in a real world
     * the different amount stored in StockTransaction as positive amount with invoice start with RS-date-increment.
     *
     * the store this transaction as reconciliation_stock and detail in table( StockRecancelation,StockRecancelationDetail)
     **/

    public function insert($post)
    {   // from the request the show.tpl receive product_ids,stock_ids and real_stock which is already user added
        // as real stock after store matching
//        DB::beginTransaction();
        try {
            $product_ids = $post['product_ids'];
            $real_qty = $post['real_qty'];
            $invoice = 0;
            $i = 0;
            $dataEntry = [];
            foreach ($product_ids as $index => $product_id) {
                $stock_id = $post['stock_ids'][$index];
                $invoice=$this->generateInvoice($post['stock_ids'][$index]);
                // get that transaction which is filter before
                $transaction = $this->filterTransaction($product_id, $stock_id);
                // after generate different store the new store unit in the Unit array
                $Unit = [];
                $Entry = [];
                $Unit["stock_id"] = $stock_id;
                $Entry['stock_id'] = $stock_id;
                $Entry["product_id"] = $product_id;
                $Unit["product_id"] = $product_id;
                $Unit["num"] = $transaction["num"];
                $Entry['num'] = $transaction["num"];
                foreach ($transaction["units"] as $unit) {
                    $report_data = [];
                    $entryRepo = [];
                    $report_data["id"] = $unit["id"];
                    $report_data['name'] = $unit["name"];
                    $report_data['qty'] = $this->CalculateDifferentAmountBetweenSystemStockAndrealStock((int)$real_qty[$stock_id][$product_id][$unit["id"]], $unit["qty"]);
                    $entryRepo = $report_data;
                    $Unit []["unit"] = $report_data;
                    $entryRepo['unit_id'] = $unit['id'];
                    $entryRepo['newQty'] = (int)$real_qty[$stock_id][$product_id][$unit["id"]];
                    $entryRepo['oldQty'] = $unit["qty"];
                    $Entry[]['unit'] = $entryRepo;
                }



                $invoice_no = $this->add($Unit, $invoice);
                $Entry['invoice_no'] = $invoice_no;
                $dataEntry[] = $Entry;


            }
            $this->journalEntry($dataEntry);
            Notification::createdAlert();
            redirect('InventoryAndStoreMatching/show');
        }
        catch(ValidationException $e)
        {
            // Rollback and then redirect
            // back to form with errors
//            DB::rollback();
//            return $e->getMessage();
            redirect('InventoryAndStoreMatching/show');
        }
        catch(\Exception $e)
        {
//            DB::rollback();
//            return $e->getMessage();
//            throw $e;
        }

//        DB::commit();

    }

    public function journalEntry($dataEntry)
    {
        $debit = 0;
        $credit = 0;
        $narration = '';
        $newTotalAmount = 0;
        $oldTotalAmount=0;
        $invoice_no = 0;

        foreach ($dataEntry as  $journalEntryObject){
            for ($i = 0; $i < $journalEntryObject['num']; $i++) {
                $debit = StockAccountSetting::first()->account_recancelation_id;
                $stock = Stock::find($journalEntryObject['stock_id']);
                $credit = $stock->account_id;
                $units = ProductUnit::where('product_id', $journalEntryObject['product_id'])->orderBy('sale_price')->get();
                $invoice_no = $journalEntryObject['invoice_no'];
                $m_purchase_price = $units->where('id', $journalEntryObject[$i]['unit']['unit_id'])->first()->m_purchase_price;
                $narration = 'عملية جرد ومطابقة مخزون ';
                $newTotalAmount += $m_purchase_price * $journalEntryObject[$i]['unit']['newQty'];
                $oldTotalAmount += $m_purchase_price * $journalEntryObject[$i]['unit']['oldQty'];
            }
        }

        return $this->generateDoubleEntryTransaction($newTotalAmount,$oldTotalAmount,$invoice_no,$narration,$debit,$credit);




    }
    public function generateDoubleEntryTransaction($newTotalAmount,$oldTotalAmount,$invoice_no,$narration,$debit,$credit){
        $entry = new \Models\Finance\Entry();

        if ($newTotalAmount < $oldTotalAmount) {
            //////////////////// start builing entry /////////////////////////

            $entry->org_id = $_SESSION['organization']->id;
            $entry->amount = $newTotalAmount;
            $entry->type = FinEntry::SETTING_GENERAL_ENTRY;
            $entry->created_by = $entry->last_update_by = user('id');
            $entry->created_date = today();
            $entry->date = today();
            $entry->year_id = Year::getActiveYear()->id;
            $entry->ref_num = $invoice_no;
            $entry->statement = $narration;
            $entry->status = FinEntry::SETTING_PROTECTED_ENTRY;
            $entry->equilibrium = 0;
            $entry->save();

            $debitTransaction = new \Models\Finance\Transaction();
            $debitTransaction->year_id = $entry->year_id;
            $debitTransaction->org_id = organization('id');
            $debitTransaction->entery_id = $entry->id;
            $debitTransaction->entery_date = $entry->last_update_date;
            $debitTransaction->entery_type = $entry->type;
            $debitTransaction->entery_num = $entry->num;
            $debitTransaction->acc_id = $debit;
            $debitTransaction->depit = $newTotalAmount;
            $debitTransaction->credit = 0;
            $debitTransaction->comment = $narration;
            $debitTransaction->created_by = user('id');
            $debitTransaction->created_date = $entery->created_date;
            $debitTransaction->save();

            $creditTransaction = new \Models\Finance\Transaction();
            $creditTransaction->year_id = $entry->year_id;
            $creditTransaction->org_id = organization('id');
            $creditTransaction->entery_id = $entry->id;
            $creditTransaction->entery_type = $entry->type;
            $creditTransaction->entery_date = $entry->last_update_date;
            $creditTransaction->acc_id = $credit;
            $creditTransaction->depit = 0;
            $creditTransaction->credit = $newTotalAmount;
            $creditTransaction->comment = $narration;
            $creditTransaction->created_by = user('id');
            $creditTransaction->created_date = $entery->created_date;
            $creditTransaction->save();

//            try {
//                FinEntry::registerEntry(fin_entery::readByID($entry->id));
//                Notification::createdAlert();
//            } catch (Exception $e) {
//                // Notification::existAlert();
//
//            }
        }
        else if ($newTotalAmount > $oldTotalAmount){
            //////////////////// start builing entry /////////////////////////
//            $entry = new \Models\Finance\Entry();
            $entry->org_id = $_SESSION['organization']->id;
            $entry->amount = $newTotalAmount;
            $entry->type = FinEntry::SETTING_GENERAL_ENTRY;
            $entry->created_by = $entry->last_update_by = user('id');
            $entry->created_date = today();
            $entry->date = today();
            $entry->year_id = Year::getActiveYear()->id;
            $entry->ref_num = $invoice_no;
            $entry->statement = $narration;
            $entry->status = FinEntry::SETTING_PROTECTED_ENTRY;
            $entry->equilibrium = 0;
            $entry->save();

            $debitTransaction = new \Models\Finance\Transaction();
            $debitTransaction->year_id = $entry->year_id;
            $debitTransaction->org_id = organization('id');
            $debitTransaction->entery_id = $entry->id;
            $debitTransaction->entery_date = $entry->last_update_date;
            $debitTransaction->entery_type = $entry->type;
            $debitTransaction->entery_num = $entry->num;
            $debitTransaction->acc_id = $credit;
            $debitTransaction->depit = $newTotalAmount;
            $debitTransaction->credit = 0;
            $debitTransaction->comment = $narration;
            $debitTransaction->created_by = user('id');
            $debitTransaction->created_date = $entery->created_date;
            $debitTransaction->save();

            $creditTransaction = new \Models\Finance\Transaction();
            $creditTransaction->year_id = $entry->year_id;
            $creditTransaction->org_id = organization('id');
            $creditTransaction->entery_id = $entry->id;
            $creditTransaction->entery_type = $entry->type;
            $creditTransaction->entery_date = $entry->last_update_date;
            $creditTransaction->acc_id = $debit;
            $creditTransaction->depit = 0;
            $creditTransaction->credit = $newTotalAmount;
            $creditTransaction->comment = $narration;
            $creditTransaction->created_by = user('id');
            $creditTransaction->created_date = $entery->created_date;
            $creditTransaction->save();



        }
        try {
//
            $entry->lockEntry();
//            $entry->num = null;
//            $entry->code = null;

//            $entry->save();
//            return fin_entery::readByID($entry->id);
            FinEntry::registerEntry(fin_entery::readByID($entry->id));
            $entry->postEntry();
            Notification::createdAlert();

        } catch (Exception $e) {
            // Notification::existAlert();
//            return $e->getMessage().' error cannot reqister';
//            Notification::alertMessage("INFO",$e->getMessage());


        }
    }
    public function filterTransaction($product_id, $stock_id)
    {

        $data = StockTransaction::with('product.units.unit')
            ->with('stock')
            ->with('unit')
            ->when($product_id, function ($query) use ($product_id) {
                $query->where('product_id', $product_id);
            })->when($stock_id, function ($query) use ($stock_id) {
                $query->where('stock_id', $stock_id);
            })->get();
        $report_data = [];
        $stock = $data->first();
        $stock_name = $stock->stock->name;
        $row_data = [];
        $units = [];
        $num_unit = 0;
        $product_name = $stock->product->name;
        $row_data["stock_name"] = $stock_name;
        $row_data["product_name"] = $product_name;
        $qty = $data->sum('qty');
        $base = $stock->product->units->last();
        $base_price = $base->sale_price;
        foreach ($stock->product->units as $unit) {
                $unit_row = [];
                $unit_name = $unit->unit->name;
                $sale_price = $unit->sale_price;
                $quantity_from_first = ($sale_price / $base_price);
                $qty_unit = (int)($qty / $quantity_from_first);
                $qty = $qty - ($qty_unit * $quantity_from_first);
                $unit_row["name"] = $unit_name;
                $unit_row["id"] = $unit->id;
                $unit_row["qty"] = $qty_unit;
                $units [] = $unit_row;
                $num_unit++;
                }
                $row_data["units"] = $units;
                $row_data['num'] = $num_unit;
                $report_data = $row_data;


        $data = $report_data;
        return $data;

    }

    // this function is look like filter function but filtered by product and stock

    /**
     * this function calculate the differences between the System Stock and real stock
     * return different
     * */
    public function CalculateDifferentAmountBetweenSystemStockAndrealStock($new, $old)
    {
        return $new - $old;
    }

    /**
     * this function add new transaction in StockRecancelation,StockRecancelationDetail and StockTransaction
     */

    public function generateInvoice($stock_id){
        $date = now();
        $yearFormat=date_format($date,"Y-m");
        $pieces = explode("-", $yearFormat);
        $year =$pieces[0];
        $StockRecancelation = new StockRecancelation();
        $element = ["stock_id" => $stock_id, "date" => $date];
        $element['invoice_no'] = 0;
        $element['created_by'] = $_SESSION['user']->id;
        $StockRecancelation->fill($element);
        $StockRecancelation->save();
        $random = str_pad((int)$StockRecancelation->id , 6, '0', STR_PAD_LEFT);
        $invoice_no = 'RS' . '-' . $year . '-' . $random;
        $StockRecancelation->invoice_no =$invoice_no;
        $StockRecancelation->save();
        return $StockRecancelation->id;

    }
    public function add($object,$invoice_id)
    {
        /**
         *
         */


//        return $object;
        $StockRecancelation = StockRecancelation::find($invoice_id);
//        return $StockRecancelation;
        $num = $object['num'];

        for ($i = 0; $i < $num; $i++) {
            if ($object[$i]["unit"]["qty"] === 0) {

                continue;
            } else {
//                return $i;
                $StockRecancelationDetail = new StockRecancelationDetail();
                $data=[
                    "stock_recancelation_id" => $invoice_id,
                    "product_id" => (int)$object["product_id"],
                    "product_unit_id" => $object[$i]["unit"]['id'],
                    "qty" => $object[$i]["unit"]["qty"],
                ];
//                return $data;
                $StockRecancelationDetail->stock_recancelation_id= $invoice_id;
                $StockRecancelationDetail->product_id=(int)$object["product_id"];
                $StockRecancelationDetail->product_unit_id=$object[$i]["unit"]['id'];
                $StockRecancelationDetail->qty= $object[$i]["unit"]["qty"];

//                $StockRecancelationDetail->fill($data);
                $StockRecancelationDetail->save();

            }

        }
        for ($i = 0; $i < $num; $i++) {
            if ($object[$i]["unit"]["qty"] == 0) {
                continue;
            } else {
                $product_id=$object["product_id"];
                $unit_id=$object[$i]["unit"]['id'];
                $qty=$object[$i]["unit"]["qty"];
                $Model_processing = new Model_processing();
                $data=$Model_processing->convertToLessUnit($product_id, $unit_id,$qty);
                $qty=$data["qty"];
                $first_unit_id=$data['unit_id'];
                $transaction = new StockTransaction();
                $transaction->fill([
                    "product_id" => $object["product_id"],
                    "stock_id" => $StockRecancelation->stock_id,
                    "stock_unit_id" => $first_unit_id,
                    "qty" => $qty,
                    "date" => $StockRecancelation->date,
                    "ref" => $StockRecancelation->invoice_no
                ]);
                $transaction->save();
            }
        }
        return $StockRecancelation->invoice_no;

    }

    public function filter($post)
    {

        $data = StockTransaction::with('product.units.unit')->with('stock')->with('unit')->when($post['stock_id'], function ($query) use ($post) {
                $query->where('stock_id', $post['stock_id']);
            })->when($post['date'], function ($query) use ($post) {
                $query->whereDate('date', $post['date']);
            })->get()->groupBy(['stock_id', 'product_id']);
        $report_data = [];


        foreach ($data as $stock_id => $stock) {
            $stock_name = $stock->first()->first()->stock->name;
            foreach ($stock as $product_id => $product) {
                $row_data = [];
                $units = [];
                $i = 0;
                $product_name = $product->first()->product->name;
                $row_data["stock_name"] = $stock_name;
                $row_data["product_id"] = $product->first()->product->id;
                $row_data["stock_id"] = $stock->first()->first()->stock->id;
                $row_data["product_name"] = $product_name;
                $qty = $product->sum('qty');
                if($product->first()->product->units!=null) {
                        $base = $product->first()->product->units->last();
                        $base_price = $base->sale_price;
                        foreach ($product->first()->product->units as $unit) {
                            $unit_row = [];
                            $unit_name = $unit->unit->name;
                            $sale_price = $unit->sale_price;
                            $quantity_from_first = $sale_price / $base_price;
                            $qty_unit = (int)($qty / ($quantity_from_first == 0 ? 1 : $quantity_from_first));
                            $qty = $qty - ($qty_unit * $quantity_from_first);
                            $unit_row["name"] = $unit_name;
                            $unit_row["id"] = $unit->id;
                            $unit_row["qty"] = $qty_unit;
                            $units [] = $unit_row;
                            $i++;

                        }
                }
                $row_data["units"] = $units;
                $row_data['num'] = $i;
                $report_data [] = $row_data;

            }
        }

        $data = $report_data;

        $this->Smarty->assign('data', $data);
    }

    public function print()
    {
        if ($_SESSION['filterParams']) $this->filter($_SESSION['filterParams']);
        generatePdf();
    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', StockRecancelation::find($parm[0]));
        $_SESSION['s_stock_Recancelation_token'] = Helper::generateToken();
    }
}