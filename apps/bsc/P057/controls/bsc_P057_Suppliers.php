<?php


use Models\Warehouse\Supplier;
use Models\Finance\Account;

class bsc_P057_Suppliers extends Controller{
    public function show($parm , $post)
    {
        switch ($parm[0]){
            case 'insert':
                if ($parm[1] === $_SESSION['s_suppliers_token']){
                    Validation::rules($post, [
                        'name' => 'required',
                        'address' => 'required',
                        'phone' => 'required|numeric|min_length:9',
                        'Vat_tax'=>'required'
                    ]);
                    if(Validation::check()) {
                        $this->insert($parm, $post);
                    }
                }
                break;
            case 'delete':
                if ($parm[1] === $_SESSION['s_suppliers_token']){
                    $this->delete($parm, $post);
                }
                break;

            case 'update':
                if ($parm[1] === $_SESSION['s_suppliers_token']){
                    Validation::rules($post, [
                        'name' => 'required',
                        'address' => 'required',
                        'phone' => 'required|min:11|numeric',
                        'Vat_tax'=>'required'
                    ]);
                    if(Validation::check()) {
                        $this->update($parm, $post);
                    }
                }
                break;
        }
        $suppliers = Supplier::with('product')->orderBy('id','desc')->get();
        $this->Smarty->assign('suppliers', $suppliers);
        $_SESSION['s_suppliers_token'] = Helper::generateToken();
    }

    public function add($parm , $post){
        $activationStatus = Setting::getList(19);
        $accounts=Account::currentYear()
            ->where('fin_acc_code' ,'like' , '11%')
            ->where('fin_acc_level' , '>=' , 4)
            ->doesntHave('children')
            ->get();
        $accounts = $accounts->map(function ($account) {
            return collect($account->toArray())
                ->only(['id', 'name', 'code'])
                ->all();
        });
        $this->Smarty->assign('accounts',$accounts) ;
        $this->Smarty->assign('activationStatus', $activationStatus);
        $_SESSION['s_suppliers_token'] = Helper::generateToken();
    }

    public function insert($parm , $post){
        $supplier = new Supplier($post);
        $supplier->save();
        redirect('Suppliers/show');
    }

    public function edit($parm , $post){
        $activationStatus = Setting::getList(19);
        $accounts=Account::currentYear()
            ->where('fin_acc_code' ,'like' , '11%')
            ->where('fin_acc_level' , '>=' , 4)
            ->doesntHave('children')
            ->get();
        $accounts = $accounts->map(function ($account) {
            return collect($account->toArray())
                ->only(['id', 'name', 'code'])
                ->all();
        });
        $this->Smarty->assign('accounts',$accounts) ;
        $this->Smarty->assign('activationStatus',$activationStatus);
        $this->Smarty->assign('supplier',Supplier::find($parm[0]));
    }

    public function update($parm , $post){
        $supplier = Supplier::find($parm[0]);
        $supplier->fill($post);
        $supplier->save();
        Notification::updatedAlert();
        redirect('Suppliers/show');
    }
    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', Supplier::find($parm[0]));

        $_SESSION['s_suppliers_token'] = Helper::generateToken();
    }

    public function delete($parm, $post)
    {
        $supplier = Supplier::find($parm[2])->delete();
        Notification::deletedAlert();
    }
}
