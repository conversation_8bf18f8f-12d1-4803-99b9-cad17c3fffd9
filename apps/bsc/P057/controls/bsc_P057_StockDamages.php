<?php


use Models\Warehouse\Product;
use Models\Warehouse\Stock;
use Models\Warehouse\StockDamage;
use Models\Warehouse\StockDamageDetail;
use Models\Warehouse\StockTransaction;
use Models\Warehouse\ProductUnit;
use Models\Warehouse\StockAccountSetting;

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class bsc_P057_StockDamages extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {
            case 'menu';
                $_SESSION['filterParams'] = null;
                break;
            case 'filter';
                $_SESSION['filterParams'] = $post;
                break;
            case 'insert':
                if ($parm[1] === $_SESSION['s_stock_damages_token']) {
                    Validation::rules($post, [
                        'date' => 'required|before_or_equal:'.now(),
                        'stock_id' => 'required',
                    ]);
                    if(Validation::check()) {
                        $this->insert($parm, $post);
                    }
                }
                break;
            case 'delete':
                if ($parm[1] === $_SESSION['s_stock_damages_token']) {
                    $this->delete($parm, $post);
                }
                break;

            case 'update':
                if ($parm[2] === $_SESSION['s_stock_damages_token']) {
                    Validation::rules($post, [
                        'date' => 'required|before_or_equal:'.now(),
                        'stock_id' => 'required',
                    ]);
                    if(Validation::check()) {
                        $this->update($parm, $post);
                    }
                }
                break;
            case 'details':
                    $this->details($parm, $post);
                break;
        }
        $types = Setting::getList(295);
        $types = collect($types);
        $stockDamageUsers = StockDamage::all();
        $_SESSION['created_by']=StockDamage::where('created_by' , $post['created_by'])->select('created_by')->first()->created_by;
        $_SESSION['invoice_no']=StockDamage::where('invoice_no' , $post['invoice_no'])->select('invoice_no')->first()->invoice_no;
        $stocks = Stock::with('location')->get();
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('types', $types);
        $this->Smarty->assign('stockDamageUsers', $stockDamageUsers);

        $this->filter($_SESSION['filterParams']);
        $_SESSION['s_stock_damages_token'] = Helper::generateToken();
    }
    public function filter($post)
    {
        $stockDamages = StockDamage::with('stockDamageDetail.productUnit.unit')->with('stock')
            ->when($post['stock_id'], function ($query) use ($post) {
                $query->where('stock_id', $post['stock_id']);
            })
            ->when($post['created_by'], function ($query) use ($post) {
                $query->where('created_by', $post['created_by']);
            })
            ->when($post['invoice_no'], function ($query) use ($post) {
                $query->where('invoice_no', $post['invoice_no']);
            })
            ->whereHas('stock', function ($query) use($post){
                $query->when($post['type'], function ($query) use ($post) {
                    $query->where('type', $post['type']);
                });
            })
            ->when(($post['datefrom'] and $post['dateto']), function ($query) use ($post) {
                $query->whereBetween('date', [$post['datefrom'], $post['dateto']]);
            })
            ->orderBy('id')->paginated();
        $this->Smarty->assign('stockDamages', $stockDamages);
    }

    public function add($parm, $post)
    {
        $date = now();
        $yearFormat=date_format($date,"Y-m");
        $pieces = explode("-", $yearFormat);
        $year =$pieces[0];
        $invoice_no = 'HO' .'-'.$year;
        $stocks = Stock::all();
        $products = Product::where('status',23)->where('type',1360)->with('unit')->get();
        $this->Smarty->assign('products', $products);
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('invoice_no', $invoice_no);
        $_SESSION['s_stock_damages_token'] = Helper::generateToken();
    }

    public function insert($parm, $post)
    {
        $post['invoice_no']=0;
        $post['created_by']=$_SESSION['user']->id;
        $stockDamage = new StockDamage();
        $stockDamage->fill($post);
        $stockDamage->save();

        Request::createWFRequest(
            organization('id'),
            $_SESSION['program']->id,
            StockDamage::$operationCode,
            $_SESSION['user']->id,
            $stockDamage->getTable(),
            $stockDamage->id,
            $_SESSION['user']->id
        );
        $date = now();
        $yearFormat=date_format($date,"Y-m");
        $pieces = explode("-", $yearFormat);
        $year =$pieces[0];
        $random=str_pad((int)$stockDamage->id,6, '0', STR_PAD_LEFT);
        $invoice_no = 'SD' .'-'.$year.'-'.$random;
        $stockDamage->invoice_no =$invoice_no;
        $stockDamage->save();
        $debit = 0;
        $credit = 0;
        $narration = '';
        $totalAmount = 0;
        $debit=StockAccountSetting::first()->account_damage_id;
        $credit = $stockDamage->stock->account_id;
        foreach ($post['data'] as $d) {

            $stockDamageDetail = new StockDamageDetail();
            $stockDamageDetail->fill([
                "stock_damage_id" => $stockDamage->id,
                "product_id" => $d["product"],
                "product_unit_id" => $d["unit"],
                "qty" => $d["qty"],
            ]);
            $stockDamageDetail->save();

        }
        Notification::createdAlert();
        redirect('StockDamages/show');
    }


    public function edit($parm, $post)
    {
        $stockDamage=StockDamage::find($parm[0])->load('stockDamageDetail.product.units.unit');
        $type = Stock::findOrFail($stockDamage->stock_id)->type;
        $stockType = Setting::readID($type)->translatedName;
        $stocks = Stock::all();
        $products = Product::where('status',23)->where('type',1360)->with('unit')->get();
        $this->Smarty->assign('products', $products);
        $this->Smarty->assign('stocks', $stocks);
        $this->Smarty->assign('stockDamage',$stockDamage);
        $this->Smarty->assign('stockType',$stockType);
    }

    public function update($parm, $post)
    {
        $stockDamage = StockDamage::find($parm[1]);
        $stockDamage->fill($post);
        $stockDamage->save();
        $debit = 0;
        $credit = 0;
        $narration = '';
        $totalAmount = 0;
        $debit=StockAccountSetting::first()->account_damage_id;
        $credit = $stockDamage->stock->account_id;
        StockDamageDetail::where('stock_damage_id',$stockDamage->id)->delete();
        foreach ($post['data'] as $d) {
            $row = StockDamageDetail::updateOrCreate([
                'stock_damage_id' => $stockDamage->id,
                "product_id" => $d["product"],
                "product_unit_id" => $d["unit"],
                ],
                [
                "qty" => $d["qty"],
            ]);

        }
        foreach ($post['data'] as $index =>$d) {

            $units = ProductUnit::where('product_id' , $post['data'][$index]['product'])->orderBy('sale_price')->get();
            $price = $units->where('id' , $d["unit"])->first()->sale_price;
            $first_sale = $units->first()->sale_price;
            $first_unit_id = $units->first()->stock_unit_id;
            $quantity_from_first = $price / $first_sale;
            $qty = $d["qty"] * $quantity_from_first;
            $row =StockTransaction::updateOrCreate([
                "ref" =>    $stockDamage->invoice_no,
                "product_id" => $d["product"],
                "stock_unit_id" => $first_unit_id,
                "stock_id" => $stockDamage->stock_id
            ],[
                "qty" => (-1*$qty),
                "date" =>  $stockDamage->date,
            ]);
            $m_purchase_price=$units->where('id' , $d["unit"])->first()->m_purchase_price;
            $invoiceNo = $stockDamage->invoice_no;
            $narration = $stockDamage->note != null ? $stockDamage->note :'عملية اتلاف من مخزون';
            $totalAmount +=$m_purchase_price * $d["qty"];

        }
        //////////////////// start builing entry /////////////////////////
        $entry = Models\Finance\Entry::where('fin_entery_ref_num' , $invoiceNo)->first();
        $entry->amount = $totalAmount;
        $entry->statement = $narration;
        $entry->save();

        $debitTransaction = \Models\Finance\Transaction::where('fin_trans_entery_id', $entry->id)
            ->where('fin_trans_depit' , '>' , 0)
            ->first();
        $debitTransaction->depit = $totalAmount;
        $debitTransaction->comment = $narration;
        $debitTransaction->save();

        $creditTransaction  = \Models\Finance\Transaction::where('fin_trans_entery_id', $entry->id)->where('fin_trans_credit' , '>' , 0)->first();;
        $creditTransaction->credit = $totalAmount;
        $creditTransaction->comment = $narration;
        $creditTransaction->save();
        //////////////////// end builing entry /////////////////////////
        Notification::updatedAlert();
        redirect('StockDamages/show');
    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', StockDamage::find($parm[0]));
        $_SESSION['s_stock_damages_token'] = Helper::generateToken();
    }

    public function delete($parm, $post)
    {
        $invoice_no = StockDamage::find($parm[2])->invoice_no;
        $stockTransaction=StockTransaction::where('ref',$invoice_no)->delete();
        $stockDamageDetail = StockDamageDetail::where('stock_damage_id',$parm[2])->delete();
        $stockDamage = StockDamage::find($parm[2])->delete();
        Notification::deletedAlert();
    }

    public function details($parm)
    {

        $stockDamage = StockDamage::with('stock')->find($parm[0]);
        $details = StockDamageDetail::with('product')->with('productUnit.unit')->where('stock_damage_id', $stockDamage->id)->get();
        $this->Smarty->assign('details', $details);
        $this->Smarty->assign('stockDamage', $stockDamage);
    }
    public function print($parm)
    {
        $this->details($parm);
        generatePdf();

    }
}