<?php


use Models\Warehouse\Group;

class bsc_P057_Groups extends Controller
{
    public function show($parm, $post , $files)
    {
        switch ($parm[0]){
            case 'insert':
                if ($parm[1] === $_SESSION['s_groups_token']){
                    Validation::rules($post, [
                        'name' => 'required',
                    ]);
                    if(Validation::check()) {
                        $this->insert($parm, $post , $files);
                    }
                }
                break;
            case 'delete':
                if ($parm[1] === $_SESSION['s_groups_token']){
                    $this->delete($parm, $post);
                }
                break;

            case 'update':
                if ($parm[1] === $_SESSION['s_groups_token']){
                    Validation::rules($post, [
                        'name' => 'required',


                    ]);
                    if(Validation::check()) {
                        $this->update($parm, $post , $files);
                    }
                }
                break;
        }

        $groups = Group::orderBy('id','desc')->get();
        $this->Smarty->assign('groups', $groups);

        $_SESSION['s_groups_token'] = Helper::generateToken();
    }

    public function add($parm, $post)
    {
        $_SESSION['s_groups_token'] = Helper::generateToken();
    }

    public function insert($parm, $post , $files)
    {

        $group = new Group($post);
        if($files['image']['error']===0){
            $group->save();
            $this->saveImage($post,$group , $files);
        }else {
            $group->save();
        }
        Notification::createdAlert();
        redirect('Groups/show');

    }

    public function edit($parm, $post)
    {
//       return  Group::find($parm[0])->image;
        $this->Smarty->assign('group', Group::find($parm[0]));
    }

    public function update($parm, $post , $files)
    {
        $group = Group::find($parm[2]);
        if($files['image']['error']===0){

            $group->fill($post);
            $group->save();
            $this->saveImage($post,$group , $files);
        }else {
            $post["image"] = $group->image;
            $group->fill($post);
            $group->save();
        }


        Notification::updatedAlert();
        redirect('Groups/show');

    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', Group::find($parm[0]));

        $_SESSION['s_groups_token'] = Helper::generateToken();
    }

    public function delete($parm, $post)
    {
        $group = Group::find($parm[2])->delete();

        Notification::deletedAlert();
    }
    public function saveImage($file , $group , $files){

//        dd($files);
        $imageFile=[
              "image" =>[
                "name" => "group.png",
                "type" => "image/png",
                "tmp_name" => "/templates/assets/img/",
                "error" => 0,
                "size" => 2749
              ],
            ];

        try {

            $document = new Document();
            $post['permission'] = null;
            $document->bindProperties($post);
            $document->fileArray = $files['image'] ;
            $document->client_id = $_SESSION['organization']->id;
            $document->user_id = user('id');
            $document->operation_code = 'Groups';
            $document->table_name = warehouse_group::class;
            $document->row_id = $group->id;
            $document->created_by = $_SESSION['user']->id;
            $document->created_date = date('Y-m-d');
            $documentName = explode('.', $files['image']['name']);
            $document->name = $documentName[0];
            if ($document->save()) {

                $group->image = $document->absolutePath;
                $group->save();
                Notification::createdAlert();
            }
        }  catch (DocumentException $e) {
            /**
             * @TODO handling other errors and display some feed back for end user
             */
            $feedback = ($e->getCode() === self::CODE_FILE_TYPE_NOT_ALLOWED) ? self::MESSAGE_GNR_FILE_TYPE_NOT_ALLOWED : '';
            Notification::alertMessage(Notification::ERROR, $feedback);
        }

    }

    function base64_to_jpeg($base64_string, $output_file) {
        // open the output file for writing
        $ifp = fopen( $output_file, 'wb' ); 
    
        // split the string on commas
        // $data[ 0 ] == "data:image/png;base64"
        // $data[ 1 ] == <actual base64 string>
        $data = explode( ',', $base64_string );
    
        // we could add validation here with ensuring count( $data ) > 1
        fwrite( $ifp, base64_decode( $data[ 1 ] ) );
    
        // clean up the file resource
        fclose( $ifp ); 
    
        return $output_file; 
    }
}