<?php


use Models\Warehouse\StockUnit;

class bsc_P057_StockUnits extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]){
            case 'insert':
                if ($parm[1] === $_SESSION['s_stockUnits_token']){
                    Validation::rules($post, [
                        'name' => 'required',
                    ]);
                    if(Validation::check()) {
                        $this->insert($parm, $post);
                    }
                }
                break;
            case 'delete':
                if ($parm[1] === $_SESSION['s_stockUnits_token']){
                    $this->delete($parm, $post);
                }
                break;

            case 'update':
                if ($parm[1] === $_SESSION['s_stockUnits_token']){
                    Validation::rules($post, [
                        'name' => 'required',
                    ]);
                    if(Validation::check()) {
                        $this->update($parm, $post);
                    }
                }
                break;
        }

        $stockUnits = StockUnit::with('productUnit')->where('type',1360)->orderBy('id','desc')->get();
        $this->Smarty->assign('stockUnits', $stockUnits);

        $_SESSION['s_stockUnits_token'] = Helper::generateToken();
    }

    public function add($parm, $post)
    {
        $_SESSION['s_stockUnits_token'] = Helper::generateToken();
    }

    public function insert($parm, $post)
    {
        $post['type']=1360;
        $stockUnit = new StockUnit($post);

        $stockUnit->save();
        redirect('StockUnits/show');
    }

    public function edit($parm, $post)
    {
        $this->Smarty->assign('stockUnit', StockUnit::find($parm[0]));
    }

    public function update($parm, $post)
    {
        $stockUnit = StockUnit::find($parm[2]);

        $stockUnit->fill($post);
        $stockUnit->save();

        Notification::updatedAlert();
        redirect('StockUnits/show');

    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', StockUnit::find($parm[0]));

        $_SESSION['s_stockUnits_token'] = Helper::generateToken();
    }

    public function delete($parm, $post)
    {
        $stockUnit = StockUnit::find($parm[2])->delete();

        Notification::deletedAlert();
    }
}