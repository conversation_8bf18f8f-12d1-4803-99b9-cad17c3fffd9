{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=head_style}
    <style>
        .tooltip {
            display: block !important;
            z-index: 10000;
        }

        .tooltip .tooltip-inner {
            background: black;
            color: white;
            border-radius: 16px;
            padding: 5px 10px 4px;
        }

        .tooltip .tooltip-arrow {
            width: 0;
            height: 0;
            border-style: solid;
            position: absolute;
            margin: 5px;
            border-color: black;
            z-index: 1;
        }

        .tooltip[x-placement^="top"] {
            margin-bottom: 5px;
        }

        .tooltip[x-placement^="top"] .tooltip-arrow {
            border-width: 5px 5px 0 5px;
            border-left-color: transparent !important;
            border-right-color: transparent !important;
            border-bottom-color: transparent !important;
            bottom: -5px;
            left: calc(50% - 5px);
            margin-top: 0;
            margin-bottom: 0;
        }

        .tooltip[x-placement^="bottom"] {
            margin-top: 5px;
        }

        .tooltip[x-placement^="bottom"] .tooltip-arrow {
            border-width: 0 5px 5px 5px;
            border-left-color: transparent !important;
            border-right-color: transparent !important;
            border-top-color: transparent !important;
            top: -5px;
            left: calc(50% - 5px);
            margin-top: 0;
            margin-bottom: 0;
        }

        .tooltip[x-placement^="right"] {
            margin-left: 5px;
        }

        .tooltip[x-placement^="right"] .tooltip-arrow {
            border-width: 5px 5px 5px 0;
            border-left-color: transparent !important;
            border-top-color: transparent !important;
            border-bottom-color: transparent !important;
            left: -5px;
            top: calc(50% - 5px);
            margin-left: 0;
            margin-right: 0;
        }

        .tooltip[x-placement^="left"] {
            margin-right: 5px;
        }

        .tooltip[x-placement^="left"] .tooltip-arrow {
            border-width: 5px 0 5px 5px;
            border-top-color: transparent !important;
            border-right-color: transparent !important;
            border-bottom-color: transparent !important;
            right: -5px;
            top: calc(50% - 5px);
            margin-left: 0;
            margin-right: 0;
        }

        .tooltip.popover .popover-inner {
            background: #f9f9f9;
            color: black;
            padding: 24px;
            border-radius: 5px;
            box-shadow: 0 5px 30px rgba(black, .1);
        }

        .tooltip.popover .popover-arrow {
            border-color: #f9f9f9;
        }

        .tooltip[aria-hidden='true'] {
            visibility: hidden;
            opacity: 0;
            transition: opacity .15s, visibility .15s;
        }

        .tooltip[aria-hidden='false'] {
            visibility: visible;
            opacity: 1;
            transition: opacity .15s;
        }
    </style>
{/block}
{block name=title}
    {#p_gnr_entry#}
    {if $entry->id}
        <div class="fin-tag">
            <div class="fin-tag-content bg-black">

                <a data-toggle="modal" data-target="#modal"
                    href="{url check=0 urltype="path" url_string="bsc/P054/finenteries/browse/0/{$smarty.session.lang}/save_session/{$entry->id}"
            }">
                    <i class="fa fa-search darkgray"></i> {#gnr_view#}
                </a>

                |

                <a href="{url urltype="path" url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/finenteries/fin_entery/{$entry->id}/{$smarty.session.user->id}/bsc/P054/finenteries/edit/0/{$smarty.session.lang}/{$entry->id}/backUrl/{$editType|default:null}"
            }">
                    <i class="fa fa-paperclip darkgray"></i>{#gnr_attachments#}
                </a>

                |

                <a data-toggle="modal" data-target="#modal"
                    href="{url check=0 urltype="path" url_string="bsc/P054/entries/copyEntry/0/{$smarty.session.lang}/{$entry->id}"
            }">
                    <i class='fa fa-copy darkgray'></i> {#p_copy_entry#}
                </a>

            </div>
        </div>
    {/if}
{/block}
{block name=body}
    <a target="_blank" href="/future.php/finance/accounts" class="btn btn-default sharp">{#gnr_browse_finance_tree#}</a>
    {url check=1 opr_code="finenteries" oprvtype=2 urltype=button url_string="bsc/P054/finenteries/edit/0/{$smarty.session.lang}/0/backUrl/savedEntries"
    text_value="<i class='fa fa-tasks'></i> إنشاء قيد جديد" style="btn btn-default sharp"}
    <div>
        <finenteries :accounts='{$accounts->where('fin_acc_is_active' , 1)}' :initial-year='{$year}'
            :initial-entry='{$entry}' :profit-centers='{$profitCenters}' :cost-centers='{$costCenters}' inline-template>
            <div>
                <div>

                    <flash></flash>
                    <form id="newTransaction" method="POST" ref="form"
                        action='{url urltype="path" url_string="bsc/P054/finenteries/edit/0/{$smarty.session.lang}/{$entryId}/saveEntryData/{$smarty.session.s_fin_enteries_token}"}'>

                        <h6 v-if="year" class="row-title before-sky">
                            <i class="fa fa-calendar"></i>
                            <span v-text="year.name"></span> | <span v-text="year.start_date"></span>
                            &nbsp;&raquo;&nbsp;<span v-text="year.end_date"></span>
                        </h6>

                        <div v-else class="alert alert-danger">{#fin_no_active_year#}</div>


                        <div class="row">
                            <div class="col-md-12">
                                <div class="widget">
                                    <div class="widget-header bordered-bottom bordered-palegreen" style="text-align: right">
                                        <span class="widget-caption">
                                            {#p_entry_data#}
                                        </span>
                                    </div>
                                    <div class="widget-body">


                                        <div class="row">
                                            <div class="col-md-2">
                                                {#p_entry_number#}<br>
                                                {if $entryId eq 0}
                                                    <input type="hidden" name="code" value="{$entry->code}">
                                                {/if}
                                                {if $entry->num or $entry->code}
                                                    {if $entry->code eq 0}
                                                        {$entry->num}
                                                    {else}
                                                        GN-{$entry->code}
                                                    {/if}
                                                {else}
                                                    <span class="text-gray">القيد غير مؤمن</span>
                                                {/if}
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">

                                                    <label for="date">{#p_entry_date#}</label>
                                                    <br>
                                                    {getdate table=fin_entery col=date type=edit row=$entry vue_model=date id=entery_date}

                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="ref_num">{#p_doc_number#}</label>
                                                    <input type="text" id="ref_num" name="refNum" class="form-control "
                                                        value="{$entry->ref_num}" placeholder="{#p_doc_number#}">
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="ref_num">{#p_amount#}</label>
                                                    <input type="text" readonly id="amount" name="amount"
                                                        class="form-control" :value="entry.amount"
                                                        placeholder="{#p_doc_number#}">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="display_amount_letters">{#p_amount_in_letters#}</label>
                                                    <textarea id="display_amount_letters" name="amount_letters"
                                                        class="form-control" placeholder="{#p_amount_in_letters#}"
                                                        required="required" disabled="disabled"
                                                        v-text="amountText || amountInText"></textarea>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="row">
                                            <div class="col-lg-2">
                                                <label for="statement">{#p_cc_account_naration#}</label>
                                            </div>
                                            <div class="col-lg-4">
                                                <textarea name="statement" id="statement" rows="4"
                                                    class="form-control">{$entry->statement}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div v-if="$v.date.$error && !$v.date.betweenDates" class="alert alert-danger fade in">
                                    <i class="typcn typcn-info-outline"></i>

                                    {#p_year_outrange_error#}
                                </div>

                            </div>
                        </div>

                        <div class="row snsowraper">
                            <table id="snsotable-1" class="table table-hover table-striped table-bordered table-responsive">
                                <thead class="bordered-palegreen">
                                    <tr>
                                        <th width="5%"></th>
                                        <th width="25%">{#p_add_account_name#}</th>
                                        <th width="15%">{#p_cc_account_naration#}</th>
                                        <th width="15%">{#p_cc_account_cost_center#}</th>
                                        <th width="15%">{#p_cc_account_tag_center#}</th>
                                        <th width="10%">{#p_cc_account_depit#}</th>
                                        <th width="10%">{#p_cc_account_credit#}</th>
                                        <th width="5%"></th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <input type="hidden" value="0" id="hiddenField">
                                    <tr v-if="entry.transactions.length > 0"
                                        v-for="(transaction,index) in entry.transactions" :key="index">
                                        <td v-text="index+1"></td>
                                        <td>
                                            <div>

                                                <input type="hidden"
                                                    :value="transaction.account ? transaction.account.id :''"
                                                    :name="'arrayofValues['+index+'][acc_id]'" />

                                                <selectize v-if="editing" :clearable="false" :options="accounts"
                                                    :get-Option-Label="getAccountLabel" v-model="transaction.account">
                                                    <template slot="option" slot-scope="option">
                                                        <div dir="rtl" class="text-right flex justify-between py-1">
                                                            <div>
                                                                <span v-text="option.code"></span>
                                                                <span>-</span>
                                                                <span v-text="option.name"></span>
                                                            </div>
                                                            <span class="text-muted" v-text="option.type"></span>
                                                        </div>
                                                    </template>
                                                </selectize>


                                                <span class="has-error"
                                                    v-if="$v.entry.transactions.$each[index].account.id.$error">
                                                    {#p_required_feild#}
                                                </span>

                                            </div>

                                            <div v-show="! editing" v-text="getAccountLabel(transaction.account)">
                                            </div>
                                        </td>

                                        <td>
                                            <textarea v-show="editing" class="form-control" V-model="transaction.comment"
                                                :name="'arrayofValues['+index+'][comment]'"
                                                placeholder="{#p_cc_account_naration#}"></textarea>
                                            <div v-text="transaction.comment" v-show="!editing">

                                            </div>
                                        </td>

                                        <td>
                                            <div id="ccenter_list">
                                                <input type="hidden"
                                                    v-for="(costCenter, iCostCenter) in transaction.cost_center"
                                                    :name="'arrayofValues['+index+'][ccenter_ids][]'"
                                                    :value="transaction.cost_center[iCostCenter] ? transaction.cost_center[iCostCenter].id :'' ">
                                                <selectize
                                                    v-if="transaction.account && transaction.account.has_cost_center && editing"
                                                    :clearable="false" :options="costCenters"
                                                    :get-Option-Label="getCenterLabel" v-model="transaction.cost_center"
                                                    multiple>

                                                    <template slot="option" slot-scope="option">
                                                        <div dir="rtl" class="text-righ py-1">
                                                            <div v-tooltip="option.padding">
                                                                <span v-text="option.title"></span>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </selectize>
                                                <span
                                                    v-if="editing && transaction.cost_center.length == 0 && transaction.account && transaction.account.has_cost_center"
                                                    class="text-warning">
                                                    {#p_cost_center_hint#}
                                                </span>
                                            </div>

                                            <div v-for="(costCenter, iCostCenter) in transaction.cost_center"
                                                v-text="costCenter.title" v-show="!editing">

                                            </div>
                                        </td>

                                        <td>
                                            <div id="ccenter_list">
                                                <input type="hidden" :name="'arrayofValues['+index+'][tcenter_ids]'"
                                                    :value="transaction.profit_center ? transaction.profit_center.id : ''">
                                                <selectize
                                                    v-if="transaction.account && transaction.account.has_cost_center && editing"
                                                    :clearable="false" :options="profitCenters"
                                                    :get-Option-Label="getCenterLabel" v-model="transaction.profit_center">
                                                    <template slot="option" slot-scope="option">
                                                        <div dir="rtl" class="text-righ py-1">
                                                            <div v-tooltip="option.padding">
                                                                <span v-text="option.title"></span>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </selectize>

                                            </div>

                                            <div v-text="transaction.profit_center.title" v-show="!editing">
                                            </div>
                                        </td>

                                        <td>
                                            <input v-show="editing" class="form-control appearance-none"
                                                :class="{ 'has-error': $v.entry.transactions.$each[index].depit.$error }"
                                                v-model="transaction.debit" min="0" @input="transaction.credit = 0"
                                                type="number" id="depit" :name="'arrayofValues['+index+'][depit]'" size="5"
                                                placeholder="{#p_cc_account_depit#}">

                                            <span class="has-error" v-if="$v.entry.transactions.$each[index].credit.$error">
                                                {#p_required_feild#}
                                            </span>

                                            <div v-show="!editing" v-text="transaction.debit">

                                            </div>
                                        </td>


                                        <td>
                                            <input v-show="editing" class="form-control"
                                                :class="{ 'has-error': $v.entry.transactions.$each[index].credit.$error }"
                                                v-model="transaction.credit" min="0" @input="transaction.debit = 0"
                                                type="number" id="credit" :name="'arrayofValues['+index+'][credit]'"
                                                size="5" placeholder="{#p_cc_account_credit#}">

                                            <span class="has-error" v-if="$v.entry.transactions.$each[index].credit.$error">
                                                {#p_required_feild#}
                                            </span>

                                            <div v-show="!editing" v-text="transaction.credit">

                                            </div>
                                        </td>

                                        <td>

                                            <div
                                                v-if="!(entry.status == 'posted') || (entry.id && entry.type == {FinEntry::SETTING_OPENING_ENTRY})">

                                                <button type='button' class="btn btn-danger sharp" @click="remove(index)">
                                                    <i class="fa fa-trash"></i>
                                                </button>

                                            </div>

                                        </td>
                                        <input v-model="transaction.entery_date"
                                            :name="'arrayofValues['+index+'][entery_date]'" type="hidden"
                                            id="entery_date_field">
                                    </tr>
                                    <tr v-if="entry.transactions.length > 0">
                                        <td class="has-text-right" colspan="5">
                                            <h4 style="margin: 0">{#gnr_total#}</h4>
                                        </td>
                                        <td v-text="depitTotal"></td>
                                        <td v-text="creditTotal"></td>
                                        <td></td>
                                    </tr>
                                    <tr v-if="entry.transactions.length > 0">
                                        <td class="has-text-right" colspan="5">
                                        </td>

                                        <td colspan="3">
                                            <p class="flex justify-between" v-if="depitTotal != creditTotal">
                                                <span class="has-error">{#p_depit_credit_equilty_error#}</span>
                                                <span class="text-muted text-md" v-text="DebitCreditDeference"></span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr v-else>
                                        <td colspan="100%" class="text-muted text-center">
                                            there is no transactions till now
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="flex justify-between mt-1">
                                <div>
                                    <template
                                        v-if="!(entry.status == 'posted') || (entry.id && entry.type == {FinEntry::SETTING_OPENING_ENTRY})">
                                        <button @click.prevent="add" class="btn btn-default sharp">
                                            <i class="fa fa-plus"></i>{#gnr_add_filed#}
                                        </button>
                                        <button @click="editing = !editing" type="button" class="btn btn-warning">
                                            <i class="fa fa-edit"></i>{#gnr_edit#}
                                        </button>

                                        <button @click.prevent="submit()" ref='saveButton' class="btn btn-success">
                                            <i class="fa fa-save"></i>{#gnr_save#}
                                        </button>
                                    </template>
                                </div>

                                {* تأمين وترحيل *}
                                <div>
                                    <template v-if="entry.valid">
                                        {if can('lock', 'finenteries')}
                                            <button @click.prevent="lock()" v-if="entry.status == 'saved'" ref='saveButton'
                                                class="btn btn-default">
                                                <i class="fa fa-lock"></i>تأمين
                                            </button>

                                            <form id="newTransaction" method="POST" ref="form"
                                                action='{url urltype="path" url_string="bsc/P054/finenteries/edit/0/{$smarty.session.lang}/{$entryId}/saveEntryData/{$smarty.session.s_fin_enteries_token}"}'>
                                                <button type="submit"> تأمين</button>
                                            </form>


                                        {/if}

                                        {if can('post', 'finenteries')}
                                            <button @click.prevent="post()" v-if="entry.status == 'locked'" ref='saveButton'
                                                class="btn btn-default">
                                                <i class="fa fa-save"></i>ترحيل
                                            </button>
                                        {/if}
                                    </template>
                                </div>
                            </div>
                        </div>

                    </form>
                </div>


                {if $entry->integrated_table eq 'prl_batches'}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="widget">
                                <div class="widget-header bordered-bottom bordered-palegreen" style="text-align: right">
                                    <span class="widget-caption">{#p_pyroll#}</span>
                                </div>
                                <div class="widget-body">
                                    <a href="{url check=0 urltype="path" url_string="bsc/P051/payroll/payrollsheetbrowse/0/{$smarty.session.lang}/{$integratedObject->prl_batches_id}"
                                }" target="_blank" class="btn btn-default">{#gnr_view#} {#p_pyroll#}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                {/if}
                {if $entry->integrated_table eq 'payroll_batch'}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="widget">
                                <div class="widget-header bordered-bottom bordered-palegreen" style="text-align: right">
                                    <span class="widget-caption">{#p_pyroll#}</span>
                                </div>
                                <div class="widget-body">
                                    {workflow requestId=$integratedObject->wf_request_id backTo="bsc/P054/payrollRequests/show/0/{$smarty.session.lang}"}
                                </div>
                            </div>
                        </div>
                    </div>
                {/if}
                <hr>

            </div>
        </finenteries>
        {if count($entryVouchers)}
            <div class="row">
                <div class="col-md-12">
                    <div class="widget">
                        <div class="widget-header bordered-bottom bordered-palegreen" style="text-align: right">
                            <span class="widget-caption">{#p_related_entries#}</span>
                        </div>
                        <div class="widget-body">
                            <table class="table table-hover table-striped table-bordered">
                                <thead class="bordered-palegreen">
                                    <tr>
                                        <th>{#p_voucher_number#}</th>
                                        <th>{#gnr_date#}</th>{#p_bla_bla_date#}
                                        <th>{#p_entry_creator#}</th>
                                        <th>{#gnr_title#}</th>
                                        <th>{#p_entry_declare#}</th>
                                        <th>{#p_amount#}</th>
                                        <th>{#gnr_type#}</th>
                                        <th>{#gnr_attachments#}</th>
                                        <th>{#gnr_print#}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {$voucherTypes = ["{#gnr_reciption#}", "{#gnr_spending#}"]}
                                    {foreach $entryVouchers as $entryVoucher}
                                        <tr>
                                            <td class="text-center">
                                                {if !empty($PCLedger->extras['code'])}{$PCLedger->extras['code']}{/if}{$entryVoucher->no}
                                            </td>
                                            <td class="text-center">
                                                {getdate table="fin_voucher" col="issue_date" type="show" row=$entryVoucher}
                                                {date('H:i:s', strtotime($entryVoucher->issue_date))}</td>
                                            <td class="text-center">{getname table=sh_user id=$entryVoucher->issued_by}</td>
                                            <td class="text-center">{$entryVoucher->title()}</td>
                                            <td class="text-center">{$entryVoucher->statement}</td>
                                            <td class="text-center">{$entryVoucher->amount|number_format:2:".":","}</td>
                                            <td class="text-center">{$voucherTypes[$entryVoucher->type-1]}</td>
                                            <td class="text-center">
                                                <a href="{url urltype="path" url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/vouchers/fin_voucher/{$entryVoucher->id}/{$smarty.session.user->id}/bsc/P054/finenteries/edit/0/{$smarty.session.lang}/{$entry->id}/{$editType}"
                                        }" class="btn btn-default btn-xs" target="_blank">

                                                    <i class="fa fa-paperclip"></i>{#gnr_attachments#}
                                                </a>
                                            </td>
                                            <td>
                                                <a target="_blank"
                                                    href="{url urltype=path url_string="bsc/P054/vouchers/print/0/ar/{$entryVoucher->pcledger_id}/{$entryVoucher->id}"
                                        }" class="btn btn-default btn-sm">
                                                    <i class="fa fa-print"></i> {#gnr_print#}
                                                </a>
                                            </td>
                                        </tr>
                                    {/foreach}
                                </tbody>
                            </table>
                            <div class="row">
                                <div class="col-md-12">
                                    <h6 class="form-title">{#p_vouchers_summary#}</h6>
                                    <table class="table table-hover table-striped table-bordered">
                                        <thead class="bordered-palegreen">
                                            <tr>
                                                <th>{#p_vouchers_number#}</th>
                                                <th>{#p_receipt_vouchers#}</th>
                                                <th>{#p_payment_vouchers#}</th>
                                                <th>{#p_cash_vouchers#}</th>
                                                <th>{#p_check_vouchers#}</th>
                                                <th>{#p_transfer_vouchers#}</th>
                                                <th>{#p_pos_vouchers#}</th>
                                                <th>{#p_vouchers_general_summation#}</th>
                                                <th>{#p_vouchers_total#}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="text-align: center">{count($entryVouchers)}</td>
                                                <td style="text-align: center">
                                                    {if $types[FinVoucher::SETTING_VOUCHER_TYPE_RECEIPT][FinVoucher::SETTING_VOUCHER_TYPE_RECEIPT] <= 0}
                                                        <span
                                                        class="text-danger">{$types[FinVoucher::SETTING_VOUCHER_TYPE_RECEIPT][FinVoucher::SETTING_VOUCHER_TYPE_RECEIPT]|default:0|number_format:2:".":","}</span>{else}
                                                        {$types[FinVoucher::SETTING_VOUCHER_TYPE_RECEIPT][FinVoucher::SETTING_VOUCHER_TYPE_RECEIPT]|default:0|number_format:2:".":","}
                                                    {/if}
                                                </td>
                                                <td style="text-align: center">
                                                    {if $types[FinVoucher::SETTING_VOUCHER_TYPE_PAYMENT][FinVoucher::SETTING_VOUCHER_TYPE_PAYMENT] <= 0}
                                                        <span
                                                        class="text-danger">{$types[FinVoucher::SETTING_VOUCHER_TYPE_PAYMENT][FinVoucher::SETTING_VOUCHER_TYPE_PAYMENT]|default:0|number_format:2:".":","}</span>{else}
                                                        {$types[FinVoucher::SETTING_VOUCHER_TYPE_PAYMENT][FinVoucher::SETTING_VOUCHER_TYPE_PAYMENT]|default:0|number_format:2:".":","}
                                                    {/if}
                                                </td>
                                                <td style="text-align: center">
                                                    {if $paymentTypes[FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_CASH][FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_CASH] <= 0}
                                                        <span
                                                        class="text-danger">{$paymentTypes[FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_CASH][FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_CASH]|default: 0|number_format:2:".":","}</span>{else}
                                                        {$paymentTypes[FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_CASH][FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_CASH]|default: 0|number_format:2:".":","}
                                                    {/if}
                                                </td>
                                                <td style="text-align: center">
                                                    {if $paymentTypes[FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_CHEQUE][FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_CHEQUE] <= 0}
                                                        <span
                                                        class="text-danger">{$paymentTypes[FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_CHEQUE][FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_CHEQUE]|default: 0|number_format:2:".":","}</span>{else}
                                                        {$paymentTypes[FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_CHEQUE][FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_CHEQUE]|default: 0|number_format:2:".":","}
                                                    {/if}
                                                </td>
                                                <td style="text-align: center">
                                                    {if $paymentTypes[FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_TRANSFER][FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_TRANSFER] <= 0}
                                                        <span
                                                        class="text-danger">{$paymentTypes[FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_TRANSFER][FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_TRANSFER]|default: 0|number_format:2:".":","}</span>{else}
                                                        {$paymentTypes[FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_TRANSFER][FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_TRANSFER]|default: 0|number_format:2:".":","}
                                                    {/if}
                                                </td>
                                                <td style="text-align: center">
                                                    {if $paymentTypes[FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_POS][FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_POS] <= 0}
                                                        <span
                                                        class="text-danger">{$paymentTypes[FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_POS][FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_POS]|default: 0|number_format:2:".":","}</span>{else}
                                                        {$paymentTypes[FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_POS][FinVoucher::SETTING_VOUCHER_PAYMENT_METHOD_POS]|default: 0|number_format:2:".":","}
                                                    {/if}
                                                </td>
                                                <td style="text-align: center">
                                                    {if $allVouchers[$PCLedger->id]['summation'] <= 0} <span
                                                        class="text-danger">{$allVouchers[$PCLedger->id]['summation']|default: 0|number_format:2:".":","}</span>{else}
                                                        {$allVouchers[$PCLedger->id]['summation']|default: 0|number_format:2:".":","}
                                                    {/if}
                                                </td>
                                                <td style="text-align: center">
                                                    {if $allVouchers[$PCLedger->id]['total'] <= 0} <span
                                                        class="text-danger">{$allVouchers[$PCLedger->id]['total']|default: 0|number_format:2:".":","}</span>{else}
                                                        {$allVouchers[$PCLedger->id]['total']|default: 0|number_format:2:".":","}
                                                    {/if}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="form-title">{#p_purposes_summary#}</h6>
                                    <table class="table table-hover table-striped table-bordered">
                                        <thead class="bordered-palegreen">
                                            <tr>
                                                <th>{#p_purpose#}</th>
                                                <th>{#p_boxes_list#}</th>
                                                <th>{#p_purpose_total#}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {foreach $purposes as $purposeID => $analysisData}
                                                <tr>
                                                    <td style="vertical-align: middle">
                                                        {getname table=fin_receipt_purposes id=$purposeID}</td>
                                                    <td>
                                                        <table class="table table-hover table-bordered">
                                                            {foreach $analysisData['vouchers_boxes'] as $boxID => $total}
                                                                <tr>
                                                                    <td width="50%">{getname table=fin_receipt_boxes id=$boxID}</td>
                                                                    <td width="50%" style="text-align: center">
                                                                        {if $total <= 0} <span
                                                                            class="text-danger">{$total|number_format:2:".":","}</span>{else}
                                                                        {$total|number_format:2:".":","} {/if}
                                                                    </td>
                                                                </tr>
                                                            {/foreach}
                                                        </table>
                                                    </td>
                                                    <td style="text-align: center;vertical-align: middle">
                                                        {if $analysisData['total'] <= 0} <span
                                                            class="text-danger">{$analysisData['total']|number_format:2:".":","}</span>{else}
                                                        {$analysisData['total']|number_format:2:".":","} {/if}
                                                    </td>
                                                </tr>
                                            {/foreach}
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="form-title">{#p_boxes_summary#}</h6>
                                    <table class="table table-hover table-striped table-bordered">
                                        <thead class="bordered-palegreen">
                                            <tr>
                                                <th>{#p_box#}</th>
                                                <th>{#p_purposes#}</th>
                                                <th>{#p_box_total#}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {foreach $boxes as $boxID => $analysisData}
                                                <tr>
                                                    <td style="vertical-align: middle">{getname table=fin_receipt_boxes id=$boxID}
                                                    </td>
                                                    <td>
                                                        <table class="table table-hover table-bordered">
                                                            {foreach $analysisData['vouchers_purposes'] as $purposeID => $total}
                                                                <tr>
                                                                    <td width="50%">{getname table=fin_receipt_purposes id=$purposeID}
                                                                    </td>
                                                                    <td width="50%" style="text-align: center">
                                                                        {if $total <= 0} <span
                                                                            class="text-danger">{$total|number_format:2:".":","}</span>{else}
                                                                        {$total|number_format:2:".":","} {/if}
                                                                    </td>
                                                                </tr>
                                                            {/foreach}
                                                        </table>
                                                    </td>
                                                    <td style="text-align: center; vertical-align: middle">
                                                        {if $analysisData['total'] <= 0} <span
                                                            class="text-danger">{$analysisData['total']|number_format:2:".":","}</span>{else}
                                                        {$analysisData['total']|number_format:2:".":","} {/if}
                                                    </td>
                                                </tr>
                                            {/foreach}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {/if}

        {$integratedTables = ['hr_advancerequest', 'hr_outworkfees', 'hr_mndtfees', 'fin_exch', 'hr_deductaddition']}
        {if in_array($entry->integrated_table, $integratedTables)}
            <div class="row">
                <div class="col-md-12">
                    <div class="widget">
                        <div class="widget-header bordered-bottom bordered-palegreen" style="text-align: right">
                            <span class="widget-caption">
                                {#p_request#}
                            </span>
                        </div>
                        <div class="widget-body">

                            {if $integratedObject}
                                {php}
                                try {
                                {/php}
                                {$sourceTable = $integratedObject->getTableName()}
                                {$sourceTableId = "{$sourceTable}_id"}
                                {$sourceID = $integratedObject->$sourceTableId}
                                {$requestID = Request::read([Request::TABLE_NAME => "{$sourceTable}", Request::ROW_ID =>
                                "{$sourceID}"])}
                                {workflow requestId=$requestID[0]->id backTo="bsc/P054/finenteries/edit/0/{$smarty.session.lang}/save_session/{$entry->id}"}
                                {php}
                                } catch (Exception $e) {
                                echo "---";
                                }
                                {/php}
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        {/if}

        <div class="row">
            <div class="col-md-12">
                {showdocslist opr_code=finenteries row_id=$entry->id}
            </div>
        </div>
    </div>
{/block}
{block name=back}{url urltype="path" url_string="{$smarty.session.s_backPath}"}{/block}