{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{$inValidColor = "orange"}
{$validColor = "palegreen"}
{block name=pre}
    <div class="row">
        <div class="profile-container ">
            <div class="profile-header row" style="min-height: 10px;height: 100px;">
                <div class="col-lg-2 col-md-4 col-sm-12 text-center profile-stats" style="min-height: 10px;height: 100px;">
                    <div class="col-md-12 stats-col">
                        <div class="stats-value pink">{getname table=fin_year id=$smarty.session.s_activ_year_id}99</div>
                        <div class="stats-title">{#p_fin_year#}</div>
                    </div>
                </div>
                <div class="col-lg-5 col-md-8 col-sm-12 profile-info" style="min-height: 10px;height: 100px;">
                    <div class="text-center "><h4>{#p_gnr_entries#}</h4></div>
                </div>
                <div class="col-lg-5 col-md-12 col-sm-12 col-xs-12 profile-stats" style="min-height: 10px;height: 100px; display: none;">
                    <div class="row">
                        <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12 stats-col">
                            <div class="stats-value pink">10</div>
                            <div class="stats-title">{#p_entry#}</div>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12 stats-col">
                            <div class="stats-value pink">0</div>
                            <div class="stats-title">{#p_safe_entry#}</div>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12 stats-col">
                            <div cla<div class="profile-container ">
                <div class="profile-header row" style="min-height: 10px;height: 100px;">
                    <div class="col-lg-2 col-md-4 col-sm-12 text-center profile-stats" style="min-height: 10px;height: 100px;">
                        <div class="col-md-12 stats-col">
                            <div class="stats-value pink">{getname table=fin_year id=$smarty.session.s_activ_year_id}99</div>
                            <div class="stats-title">{#p_fin_year#}</div>
                        </div>
                    </div>
                    <div class="col-lg-5 col-md-8 col-sm-12 profile-info" style="min-height: 10px;height: 100px;">
                        <div class="text-center "><h4>{#p_gnr_entries#}</h4></div>
                    </div>
                    <div class="col-lg-5 col-md-12 col-sm-12 col-xs-12 profile-stats" style="min-height: 10px;height: 100px; display: none;">
                        <div class="row">
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12 stats-col">
                                <div class="stats-value pink">10</div>
                                <div class="stats-title">{#p_entry#}</div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12 stats-col">
                                <div class="stats-value pink">0</div>
                                <div class="stats-title">{#p_safe_entry#}</div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12 stats-col">
                                <div class="stats-value pink">3</div>
                                <div class="stats-title">{#p_invalid_entry#}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>ss="stats-value pink">3</div>
                            <div class="stats-title">{#p_invalid_entry#}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="form-title"></div>
{/block}

{block name=body}
    {if $smarty.session.s_activ_year_id neq 0}
        <a data-toggle="modal" data-target="#modal" href="{url check=1 urltype="path" opr_code='finenteries' oprvtype=2 url_string="bsc/P054/finenteries/form/0/{$smarty.session.lang}/0"}" class="btn btn-labeled btn-palegreen btn-sm">
            <i class="btn-label fa fa-plus"></i>{#create_gnr_entry#}
        </a>
    {/if}
    {if isset($loading)}
        <div class="progress progress-striped active">
            <div class="progress-bar progress-bar-success" id="progressBar" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width: 0">
                <span class="sr-only">
                    100% Complete (success)
                </span>
            </div>
        </div>
        <script>
            function count() {
                for(i=0; i<=100; i=i+20) {
                    $('#progressBar').css('width', i + '%');
                }
            }

            setTimeout(function(){
                count();
            }, 50);
        </script>
    {/if}

    {if $smarty.session.s_activ_year_id eq 0}
        <div class="alert alert-danger fade in">
            <button class="close" data-dismiss="alert">
                ×
            </button>
            {#p_no_active_year#}
        </div>
    {/if}

    {if isset($messages)}
        {foreach $messages as $message}
            <div class="alert alert-danger fade in">
                <button class="close" data-dismiss="alert">
                    ×
                </button>
                {$message}
            </div>
        {/foreach}
    {/if}

    {if $smarty.session.s_activ_year_id neq 0}
        <div class="tickets-container">
            <ul class="tickets-list">
                {*list header start*}
                <li class="ticket-item bg-carbon white">
                    <div class="row">
                        <div class="ticket-state bg-blue white" style="right: -11px;border-radius: 10%;width: 3em;">
                            #
                        </div>
                        <div class="ticket-type col-lg-1 col-sm-2 text-center">
                            <span class="user-name">{#p_cc_entery_number#}</span>
                        </div>
                        <div class="ticket-type col-lg-4 col-sm-12 text-center">
                            <div class="divider hidden-md hidden-sm hidden-xs"></div>
                            <span class="user-name">{#gnr_date#}</span>
                        </div>
                        <div class="ticket-type  col-lg-3 col-sm-6 col-xs-12 text-center">
                            <div class="divider hidden-md hidden-sm hidden-xs"></div>
                            <span class="user-name">{#p_cc_entery_amount#}</span>
                        </div>
                        <div class="ticket-type  col-lg-4 col-sm-6 col-xs-12 text-center">
                            <span class="divider hidden-xs"></span>
                            {#gnr_settings#}
                        </div>
                        <div class="ticket-state bg-gray">
                            <i class="fa fa-circle"></i>
                        </div>

                    </div>
                </li>
                {*list header end*}
                {if $smarty.session.s_activ_year_id neq 0}
                    {$i=1}
                    {foreach $entries as $entry}
                        <li class="ticket-item">
                            <div class="row">
                                <div class="ticket-state bg-blue white" style="right: -11px;border-radius: 10%;width: 3em;">
                                    {$i++}
                                </div>
                                <div class="ticket-type col-lg-1 col-sm-2 text-center">
                                    <span class="user-name">
                                        {if $entry->code neq 0}
                                            {if $entry->type eq FinEntry::SETTING_GENERAL_ENTRY}
                                                GN
                                            {elseif $entry->type eq FinEntry::SETTING_RECEIPT_ENTRY}
                                                RE
                                            {else}
                                                PE
                                            {/if}
                                            -{$entry->code}
                                        {else}
                                            {$entry->num}
                                        {/if}
                                    </span>
                                </div>
                                <div class="ticket-type col-lg-4 col-sm-12 text-center">
                                    <div class="divider hidden-md hidden-sm hidden-xs"></div>
                                    <span class="user-name">{getdate table=fin_entery col=date type=show row=$entry}</span>
                                </div>
                                <div class="ticket-type  col-lg-3 col-sm-6 col-xs-12 text-center">
                                    <div class="divider hidden-md hidden-sm hidden-xs"></div>
                                    <span class="user-name">{$entry->amount}</span>
                                </div>
                                <div class="ticket-type  col-lg-4 col-sm-6 col-xs-12 text-center">
                                    <span class="divider hidden-xs"></span>
                                    <a data-toggle="modal" data-target="#modal" href="{url check=1 urltype="path" opr_code='finenteries' oprvtype=2 url_string="bsc/P054/finenteries/browse/0/{$smarty.session.lang}/save_session/{$entry->id}"}" class="btn btn-default btn-sm">
                                        <i class="btn-label fa fa-search"></i> {#gnr_view#}
                                    </a>
                                    {if $entry->status eq FinEntry::SETTING_UNPROTECTED_ENTRY}
                                        <a data-toggle="modal" data-target="#modal" href="{url check=1 urltype="path" opr_code='finenteries' oprvtype=2 url_string="bsc/P054/costCenter/operation/0/{$smarty.session.lang}/save_session/{$entry->id}/finenteries/show"}" class="btn btn-default btn-sm">
                                            <i class="btn-label fa fa-dot-circle-o"></i> {#p_cost_centers_view#}
                                        </a>

                                        <a href="{url check=1 urltype="path" opr_code='finenteries' oprvtype=2 url_string="bsc/P054/finenteries/edit/0/{$smarty.session.lang}/save_session/{$entry->id}"}" class="btn btn-default btn-sm">
                                            <i class="btn-label fa fa-edit"></i> {#gnr_edit#}
                                        </a>
                                        <a href="javascript:_confirm({$entry->id})"
                                           class="btn btn-danger btn-sm" id="deleteEntry-{$entry->id}">
                                            <i class="btn-label fa fa-trash"></i> {#gnr_delete#}
                                        </a>

                                    {/if}
                                </div>

                                {if $entry->equilibrium eq 0}

                                    <div class="ticket-state bg-{$validColor}">
                                        <i class="fa fa-check"></i>
                                    </div>
                                {else}

                                    <div class="ticket-state bg-{$inValidColor}">
                                        <i class="fa fa-times"></i>
                                    </div>
                                {/if}
                            </div>
                        </li>
                    {/foreach}
                {/if}

            </ul>
        </div>
    {/if}

{/block}
{block name=page_header}
    <!--Modal Definition-->
    <div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="Modal"  style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" id="modal-content">

                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" >×</button>
                </div>
                <div class="modal-body">
                    Loading Content...
                    <div class="progress progress-striped active">
                        <div class="progress-bar progress-bar-success" id="progressBar" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
                                                <span class="sr-only">
                                                    100% Complete (success)
                                                </span>
                        </div>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <!--End Modal Definition-->
    <script src="/templates/assets/js/bootbox/bootbox.js"></script>
    <script>
        $('body').on('hidden.bs.modal', '.modal', function () {
            $('#modal-content').html('<div class="modal-header"> <button type="button" class="close" data-dismiss="modal" >×</button> </div> <div class="modal-body"> Loading Content... <div class="progress progress-striped active"> <div class="progress-bar progress-bar-success" id="progressBar" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width: 100%"> <span class="sr-only"> Loading Content. </span> </div></div></div>');
            $(this).removeData('bs.modal');
        });
        function _confirm(id, msg = '') {
            bootbox.confirm("<span class='orange'>{#p_confirm_delete_item#}" + msg +"</span>", function (result) {
                if (result) {
                    // similar behavior as clicking on a link
                    window.location.href = '{url urltype="path" url_string="bsc/P054/finenteries/show/0/{$smarty.session.lang}/delete_entery/"}' + id;
                }
            });
        }
    </script>
{/block}

