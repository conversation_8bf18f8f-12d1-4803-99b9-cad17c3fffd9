{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}

    <div class="col-lg-6 col-sm-6 col-xs-12">
        <div class="row">
            <div class="col-lg-12">
                <div class="well with-header">
                    <div class="header bordered-blue">{$committeeRow->name}</div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_committee_name#}</div>
                            <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$committeeRow->name}</div>

                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_committee_purposes#}</div>
                            <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                                {$committeeRow->purpose}
                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_duration#}</div>
                            <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                                {if $committeeRow->time_range eq 1}
                                    <span>{#gnr_temporary#}</span>
                                {/if}
                                {if $committeeRow->time_range eq 2}
                                    <span>{#gnr_continued#}</span>
                                {/if}
                            </div>

                            {if $committeeRow->time_range eq 1}
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_start_date#}</div>
                                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getdate table=es_committee col=start_date type=show row=$committeeRow}</div>

                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_end_date#}</div>
                                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getdate table=es_committee col=end_date type=show row=$committeeRow}</div>
                            {/if}

                        </div>
                    </div>
                </div>
                <div class="well with-header">
                    <div class="header bordered-blue">{#p_committee_membership#}</div>
                    <div class="row">
                        <div class="col-lg-12">
                            {foreach $committeeMembers as $member}
                                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 snsoinput">
                                    {$member->vacantObject->userObject->full_name}
                                    &nbsp;&raquo;&nbsp;
                                    {$member->vacantObject->jobObject->sh_job_name}
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 snsoinput">{$member->role}</div>
                            {/foreach}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 col-sm-6 col-xs-12">
        <div class="well with-header">
            <div class="header bordered-blue">{#gnr_tasks#}</div>
            <div class="row">
                {$i=1}
                {foreach $committeeTasks as $task}
                    <div class="col-lg-12">
                        <div class="col-lg-1 col-md-1 col-sm-12 col-xs-12 snsolabel">{$i++}</div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">{getname table=sh_opr id=$task->requestObject->wf_request_opr_id}</div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">{getname table=wf_step id=$task->stp_id}</div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">{getdate table=sh_notif col=created_date type=showauto row=$task->created_date}</div>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsoinput">
                            {url check=0 urltype="button" url_string="gnr/X000/wfrequest/committee/0/{$smarty.session.lang}/save_session/{$task->id}/gnr/X000/committee/show/0/{$smarty.session.lang}" text_value="معالجة"}
                        </div>
                    </div>
                {/foreach}
            </div>
        </div>
    </div>

{/block}
{block name=back}{url urltype="path" url_string="usr/P002/employeedashboard/show/0/{$smarty.session.lang}"}{/block}
