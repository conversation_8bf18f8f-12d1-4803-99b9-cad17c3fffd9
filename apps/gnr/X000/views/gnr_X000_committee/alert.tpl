{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_notice#}</h4>
    </div>
    <div class="modal-body">

        <div class="row snsowraper">
            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_name#}</div>
            <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$committeeRow->name}</div>

            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_aim#}</div>
            <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                {$committeeRow->purpose}
            </div>

            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_duration#}</div>
            <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                {if $committeeRow->time_range eq 1}
                    <span>{#gnr_temp#}</span>
                {/if}
                {if $committeeRow->time_range eq 2}
                    <span>{#gnr_continuous#}</span>
                {/if}
            </div>

            {if $committeeRow->time_range eq 1}
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_date_start#}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getdate table=es_committee col=start_date type=show row=$committeeRow}</div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_date_end#}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getdate table=es_committee col=end_date type=show row=$committeeRow}</div>
            {/if}
        </div>

        {if $committeeMembersCount gte 1}

            <div class="row snsowraper">

                <form method="post" action='{url urltype="path" url_string="gnr/X000/wfrequest/edit/0/{$smarty.session.lang}/notifyCommitteeMembers/{$smarty.session.s_wf_request_token}/{$requestRow->id}/{$stepRow->id}"}'>

                    {$i=1}
                    {foreach $committeeMembers as $member}
                        <div class="col-lg-12 snsoinput">
                            <div class="control-group">
                                <div class="checkbox">
                                    <label>
                                        <input name="memberIDs[]" value="{$member->id}" type="checkbox">
                                        <span class="text">
                                            {getname table=sh_user id=$member->user_id}
                                            &nbsp;&raquo;&nbsp;
                                            {$member->role}
                                        </span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    {/foreach}

                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-warning sharp">{#p_send#}</button>
                    </div>

                </form>
            </div>
        {else}
            <span class="red">{#p_error#}</span>
        {/if}
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}