{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_view#}</h4>
    </div>
    <div class="modal-body">
        <div class="row snsowraper">
            <div class="col-lg-12">
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_name#}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$committee->name}</div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_aim#}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$committee->purpose}</div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_duration#}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                    {if $committee->time_range eq 1}
                        <span>{#gnr_temp#}</span>
                    {/if}
                    {if $committee->time_range eq 2}
                        <span>{#gnr_continuous#}</span>
                    {/if}
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_date_start#}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getdate table=es_committee col=start_date type=show row=$committee}</div>

                {if $committee->time_range eq 1}
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_date_end#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getdate table=es_committee col=end_date type=show row=$committee}</div>
                {/if}

            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}