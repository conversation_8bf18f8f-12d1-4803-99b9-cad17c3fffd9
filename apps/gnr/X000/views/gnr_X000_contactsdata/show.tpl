{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>{/block}
{block name=page_body}
    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
        <thead>
        <tr>
            <th width="5%" style="background-color: #8CC474;">&nbsp;</th>
            <th width="25%" style="background-color: #8CC474;"> {#gnr_employee#} </th>
            <th width="20%" style="background-color: #8CC474;">{#gnr_email#}</th>
            <th width="20%" style="background-color: #8CC474;">{#gnr_tell#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $employeeList as $row}
            <tr>
                <td align="center">{$i++}</td>
                <td>{getname table=sh_user id=$row.sh_user_id}</td>
                <td>{$row.sh_user_email}</td>
                <td>{$row.sh_user_tell}</td>
            </tr>
        {/foreach}
        </tbody>
    </table>
{/block}
{block name=back}{url urltype="path" url_string="{$smarty.session.s_contactsdata_back_url}"}{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [

                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>

    <style>
        .panel-heading:hover
        {
            background-color: #dbdedb;
        }
    </style>
{/block}