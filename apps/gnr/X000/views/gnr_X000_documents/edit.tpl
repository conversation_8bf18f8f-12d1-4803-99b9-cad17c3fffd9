{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_attachment_add#}</h4>
    </div>
    <div class="modal-body">
        <form  method="post" action='{url urltype="path" url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/update/{$smarty.session.s_documents_token}/{$document->id}"}' enctype='multipart/form-data'>
            <div class="row snsowraper">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_document_name#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="name" value="{$document->name}" required></div>
                    {if (str_contains($smarty.session.s_document_back_path,'employeedashboard'))}
                    {$permission = json_decode($document->permission , true)}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"><br>{#p_show_hr#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="checkbox">
                            <label>
                                {$download = ""}
                                {if isset($permission['download'])   }
                                    {$download = "checked"}
                                {/if}
                                <input type="checkbox" name="permission[download]"
                                       value="1"  {$download}>
                                <span class="text">{#gnr_download#}</span>
                            </label>
                            <label>
                                {$view = ""}
                                {if isset($permission['view'])   }
                                    {$view = "checked"}
                                {/if}
                                <input type="checkbox" name="permission[view]"
                                       value="1" {$view} >
                                <span class="text">{#gnr_view#}</span>
                            </label>
                        </div>

                    </div>
                    {/if}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button></div>

                </div>

            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}