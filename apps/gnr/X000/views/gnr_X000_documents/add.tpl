{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#gnr_attachment_add#}</h4>
	</div>
	<div class="modal-body">
		<form  method="post" action='{url urltype="path" url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_documents_token}"}' enctype='multipart/form-data'>
			<div class="row snsowraper">
				<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_path#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<input type="file" id="files" name="fileArray" required class="hidden">
						<label for="files" class="btn btn-default">{#gnr_choose#}</label>
						<span id="filenameHolder"></span>
					</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_document_name#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<input id="docTitle" type="text" class="form-control" name="name" placeholder="{#gnr_enter_attachment_name#}">
					</div>
					{if (str_contains($smarty.session.s_document_back_path,'employeedashboard'))}
					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"><br>{#p_show_hr#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 checkbox">
						<div class="checkbox">
							<label>
								<input type="checkbox" name="permission[download]"
									   value="1"  checked>
								<span class="text">{#gnr_download#}</span>
							</label>
							<label>
								<input type="checkbox" name="permission[view]"
									   value="1"  checked>
								<span class="text">{#gnr_view#}</span>
							</label>
						</div>

					</div>
                    {/if}

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp addDocument" >{#gnr_attachment_add#}</button></div>
				</div>

				<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
					<div class="alert alert-warning fade in">
						<p><i class="fa-fw fa fa-warning"></i><span>{#p_max_size#}</span> {$document_max_size_limit}&nbsp;MB</p>
						<p><i class="fa-fw fa fa-warning"></i><span>{#p_max_number#}</span>{$operation->doc_number}&nbsp;{#p_documents#}</p>
						<p id="filesExtentions" style="display:none"><i class="fa-fw fa fa-warning"></i><span>{#p_extensions#}</span>
							<span style="word-wrap: break-word;">{foreach Document::DOCUMENT_ALLOWED_FILE_TYPES as $ext}[&nbsp;{$ext}&nbsp;]&nbsp;{/foreach}</span>
						</p>
					</div>
				</div>
			</div>
		</form>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}
{block name=footer}
	<script src="/templates/assets/js/modal/getFileName.js"></script>
	<script>
		let allowedFilles = {json_encode(Document::DOCUMENT_ALLOWED_FILE_TYPES)}
		$('#files').change(function(){
				let fileExtentionExist;
                allowedFilles.forEach(extintion =>{
                    if( fileExtention == extintion ){
                        fileExtentionExist = true

                    }
            })
			if(!fileExtentionExist){
                $('.addDocument').addClass('disabled')
                $('#filesExtentions').show()
			}
			else{
                $('.addDocument').removeClass('disabled')
                $('#filesExtentions').hide()
            }
		})
	</script>
{/block}