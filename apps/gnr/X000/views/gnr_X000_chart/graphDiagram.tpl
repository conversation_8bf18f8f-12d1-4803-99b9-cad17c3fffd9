{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}multi-page-learn.tpl"}
{$modalTitleError ={eval '<i class="fa {getcolumn table=sh_prg column=sh_prg_icon id=$graphRow->prg_id}"></i>&nbsp;{getname table=sh_prg id=$graphRow->prg_id}&nbsp;&nbsp;&raquo;&nbsp;&nbsp;{getname table=sh_opr id=$graphRow->opr_id}&nbsp;&nbsp;&raquo;&nbsp;&nbsp;{$graphRow->name}'}}
{block name=page_content}
    <div class="tabbable">
        <ul class="nav nav-tabs" id="myTab">
            <li class="active">
                <a data-toggle="tab" href="#diagram">
                    {#gnr_diagram#}
                </a>
            </li>

            <li class="tab-red">
                <a data-toggle="tab" href="#questionnaire">
                    {#gnr_questionnaire#}
                </a>
            </li>
        </ul>

        <div class="tab-content">
            <div id="diagram" class="tab-pane in active">
                <div class="widget-body" style="display: block;">
                    {if $temp}
                        <div class="row" style="padding: 0 15px;">
                            <div class="col-md-12 chart" id="{$rand}"
                                 style="direction: ltr; text-align: center; width: 100%;left: 5px; padding: 0 5px"></div>
                            {flatDiagram data=$temp container="#{$rand}"}
                        </div>
                    {else}
                        <span class="red">{#p_invalid#}</span>
                    {/if}
                </div>
            </div>

            <div id="questionnaire" class="tab-pane">
                {$i=1}
                {foreach $steps as $step}
                    <div class="well">
                        <h4 class="block">
                            <span class="badge badge-sky badge-square">{$i++}</span>
                            <span style="color: midnightblue; font-size: smaller">{$step->name}</span>
                        </h4>
                        <p>
                            {stepform stepId=$step->id display='form' type=$type}
                        </p>
                    </div>
                {/foreach}
            </div>
        </div>
    </div>
{/block}