{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">
			<i class="fa {getcolumn table=sh_prg column=sh_prg_icon id=$topicRow.sh_topic_prg_id}"></i>
			{getname table=sh_prg id=$topicRow.sh_topic_prg_id}
			&nbsp;&raquo;&nbsp;
			{$topicRow.sh_topic_name}
		</h4>
	</div>
	<div class="modal-body">
		<div class="tabbable">
			<ul class="nav nav-tabs" id="flowChartTab">
				{$i=1}
				{foreach $flowList as $fRow}
					<li class="{if $i eq 1}active{/if}">
						<a data-toggle="tab" href="#x{$fRow.sh_flow_order}">
							<span class="badge badge-square" style="background-color: #0a2b1d;">{$fRow.sh_flow_order}</span>
							{$fRow.sh_flow_name}
							<span style="display: none">{$i++}</span>
						</a>
					</li>
				{/foreach}
			</ul>

			<div class="tab-content">
				{$i=1}
				{foreach $flowList as $fRow}
					<div id="x{$fRow.sh_flow_order}" class="tab-pane {if $i eq 1}in active{/if}">

						<div class="row">
							<div class="col-lg-6"><span style="border-bottom: 1px solid #0a2b1d;"><span class="typcn typcn-document-text"></span>&nbsp;{#gnr_description#}</span></div>
							<div class="col-lg-6"><span style="border-bottom: 1px solid #0a2b1d;"><span class="typcn typcn-cog-outline"></span>&nbsp;{#p_requirements#}</span></div>
						</div>
						<div class="horizontal-space"></div>
						<div class="row">
							<div class="col-lg-6">
								{$fRow.sh_flow_description|nl2br}
								<span style="display: none">{$i++}</span>
							</div>
							<div class="col-lg-6">
								{$fRow.sh_flow_prerequisite|nl2br}
							</div>
						</div>
						<div class="horizontal-space"></div>
						<hr>
						<div class="horizontal-space"></div>
						<div class="row" style="font-size: small;">
							<div class="col-lg-6">
								<span style="border-bottom: 1px solid #0a2b1d;"><i class="fa fa-sign-in"></i>&nbsp;{#p_internal#}</span>
								<div class="horizontal-space"></div>
								<div class="panel-group accordion" id="accordions1">
									{foreach $fRow.internalRelation as $irRow}
										<div class="panel panel-default">
											<div class="panel-heading">
												<h4 class="panel-title">
													<a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="#accordions1" href="#collapse{$irRow.sh_relation_id}" aria-expanded="false">
														{getname table=sh_prg id=$irRow.sh_relation_with_prg_id}
														&raquo;
														{getname table=sh_sec id={getcolumn table=sh_opr column=sh_opr_sec_id id=$irRow.sh_relation_with_opr_id}}
														&raquo;
														{getname table=sh_opr id=$irRow.sh_relation_with_opr_id}
													</a>
												</h4>
											</div>
											<div id="collapse{$irRow.sh_relation_id}" class="panel-collapse collapse" aria-expanded="false" style="height: 0px;">
												<div class="panel-body border-red">
													{$irRow.sh_relation_describtion}
												</div>
											</div>
										</div>
									{/foreach}
								</div>
							</div>
							<div class="col-lg-6">
								<span style="border-bottom: 1px solid #0a2b1d;"><i class="fa fa-sign-out"></i> {#p_external#}</span>
								<div class="horizontal-space"></div>
								<div class="panel-group accordion" id="accordions2">
									{foreach $fRow.externalRelation as $erRow}
										<div class="panel panel-default">
											<div class="panel-heading">
												<h4 class="panel-title">
													<a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="#accordions2" href="#collapse{$erRow.sh_relation_id}" aria-expanded="false">
														{getname table=sh_prg id=$erRow.sh_relation_with_prg_id}
														&raquo;
														{getname table=sh_sec id={getcolumn table=sh_opr column=sh_opr_sec_id id=$erRow.sh_relation_with_opr_id}}
														&raquo;
														{getname table=sh_opr id=$erRow.sh_relation_with_opr_id}
													</a>
												</h4>
											</div>
											<div id="collapse{$erRow.sh_relation_id}" class="panel-collapse collapse" aria-expanded="false" style="height: 0px;">
												<div class="panel-body border-red">
													{$erRow.sh_relation_describtion}
												</div>
											</div>
										</div>
									{/foreach}
								</div>
							</div>
						</div>
					</div>
				{/foreach}
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}