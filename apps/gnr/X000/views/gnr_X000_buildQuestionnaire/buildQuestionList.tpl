{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=title}{#p_build_options#}{$QuestionRow->getName({$smarty.session.lang})}{/block}
{block name=body}
	<table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
		<thead>
		<tr>
			<th width="5%"  style="background-color: #A0D468 !important;">{url check=0 urltype="madd" opr_code='buildQuestionnaire' url_string="gnr/X000/buildQuestionnaire/addOption/0/{$smarty.session.lang}/{$QuestionRow->qs_question_id}"}</th>
			<th width="75%" style="background-color: #A0D468 !important;">{#gnr_option#}</th>
			<th width="20%" style="background-color: #A0D468 !important;">{#gnr_settings#}</th>
		</tr>
		</thead>
		<tbody>
		{$i=1}
		{foreach $OptionsList as $option}
			<tr>
				<td align="center">{$i++}</td>
				<td>{$option->name}</td>
				<td nowrap align="center">
					{url check=0 urltype="medit" opr_code='buildQuestionnaire' url_string="gnr/X000/buildQuestionnaire/editOption/0/{$smarty.session.lang}/{$QuestionRow->qs_question_id}/{$option->id}"}
					{url check=0 urltype="mdelete" opr_code='buildQuestionnaire' url_string="gnr/X000/buildQuestionnaire/confirmOption/0/{$smarty.session.lang}/{$QuestionRow->qs_question_id}/{$option->id}"}
				</td>
			</tr>
		{/foreach}
		</tbody>
	</table>
{/block}
{block name=back}{url urltype="path" url_string="gnr/X000/buildQuestionnaire/show/0/{$smarty.session.lang}"}{/block}