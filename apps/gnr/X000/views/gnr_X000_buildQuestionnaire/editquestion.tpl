{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#p_question_edit#}</h4>
	</div>
	<div class="modal-body">
		<div class="row">
			<form method="post" action='{url urltype="path" url_string="gnr/X000/buildQuestionnaire/show/0/{$smarty.session.lang}/question/update/{$smarty.session.s_buildQuestionnaire_token}/{$row->id}"}'>
				<div class="col-lg-12">
					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_question_text#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<textarea name="name" class="form-control" placeholder="{#p_question_text#}" required>{$row->name}</textarea>
					</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_answer_type#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<div class="control-group">
							{foreach $questionTypes as $type}
								<div class="radio">
									<label>
										<input name="answer_type" value="{$type->st_setting_id}" {if $type->st_setting_id eq $row->answer_type} checked {/if} type="radio" required>
										<span class="text">{getname table=st_setting id=$type->st_setting_id}</span>
									</label>
								</div>
							{/foreach}
						</div>
					</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_explain#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<textarea name="about" class="form-control" placeholder="{#p_explain_txt#}">{$row->about}</textarea>
					</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_order#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="number" name="order" value="{$row->order}" class="form-control" placeholder="{#gnr_order#}" required></div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_required#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<div class="control-group">
							{foreach $requireTypes as $type}
								<div class="radio">
									<label>
										<input name="required" value="{$type->st_setting_id}" {if $type->st_setting_id eq $row->required} checked {/if} type="radio" required>
										<span class="text">{getname table=st_setting id=$type->st_setting_id}</span>
									</label>
								</div>
							{/foreach}
						</div>
					</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
				</div>
			</form>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}