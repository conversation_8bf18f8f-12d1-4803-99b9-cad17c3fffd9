{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#gnr_options#}</h4>
	</div>
	<div class="modal-body" style="max-height: 500px;overflow-y: auto">
		<div class="row">
			<div class="alert alert-danger fade in" id="error" style="display: none"></div>
			<div id="copy" style="display: none">
				<div class="row">
					<div class="col-lg-2 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_text#}</div>
					<div class="col-lg-8 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<input name="name[]" class="form-control" placeholder="{#p_text#}" required>
					</div>
					<a href="#" class="btn btn-danger shiny"><i class="fa fa-trash"></i></a>
				</div>
			</div>
            <form method="post" action='{url urltype="path" url_string="gnr/X000/buildQuestionnaire/show/0/{$smarty.session.lang}/{$QuestionID}/insert/{$smarty.session.s_buildQuestionnaire_token}"}'>
                <div class="col-lg-12">

					{$i = 0}
					{foreach $oldOptions as $option}
						<div class="row">
							<div class="col-lg-2 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_text#}</div>
							<div class="col-lg-8 col-md-9 col-sm-12 col-xs-12 snsoinput">
								<input name="old_options[{$option->id}]" class="form-control" placeholder="{#p_text#}" value="{$option->name}" required>
							</div>
							<a href="#" class="btn btn-danger shiny"><i class="fa fa-trash"></i></a>
						</div>
					{/foreach}
					<div id="area">

					</div>
					<div class="row">
						<div class="col-lg-2 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
						<div class="col-lg-8 col-md-9 col-sm-12 col-xs-12 snsoinput">
							<button id="add" class="btn btn-success btn-block sharp" type="button"><i class="fa fa-plus"></i></button>
						</div>
						<div class="col-lg-2 col-md-9 col-sm-12 col-xs-12"></div>
					</div>

					<hr>
                    <div class="col-lg-2 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-8 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<input type="submit" class="btn btn-success sharp" value="{#gnr_save#}">
					</div>
					<div class="col-lg-2 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                </div>
            </form>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}
{block name=footer}
	<script>
		resize_modal('lg');
		$(function () {
			$('#add').click(function () {
				var option = $('#copy').html();
				$('#area').append(option);
            })
        });
		$(document).on('click', '.btn.btn-danger.shiny', function() {
			$(this).parent().remove();
		});
        function validateInput() {
            var ret = true;
            $('input[type=\'text\']').each(function () {
				 if ($(this).length == 0) {
				     ret = false;
                 }
            });
            return ret;
        }
	</script>
{/block}