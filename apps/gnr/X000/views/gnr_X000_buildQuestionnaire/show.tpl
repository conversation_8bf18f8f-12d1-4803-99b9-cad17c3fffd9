{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}

		<div class="widget">
			<div class="widget-header">
				<span class="widget-caption">
					{url check=0 urltype="medit" opr_code='buildQuestionnaire' url_string="gnr/X000/buildQuestionnaire/editQuestionnaire/0/{$smarty.session.lang}/{$questionnaire->id}" style="margin-top: 3px;"}
					&nbsp;
					{$questionnaire->name}
				</span>
				{url urltype=button url_string="gnr/X000/buildQuestionnaire/export/0/{$smarty.session.lang}/{$questionnaire->id}" text_value="<i class='fa fa-upload'></i> {#gnr_export#}" style="btn btn-default"}
			</div>
			<div class="widget-body">
				<div class="row">
					<div class="col-lg-12">{$questionnaire->about|nl2br}</div>
				</div>
			</div>
		</div>

		{url check=0 urltype="mbutton" opr_code='buildQuestionnaire' oprvtype=1 url_string="gnr/X000/buildQuestionnaire/addsection/0/{$smarty.session.lang}" text_value="إضافة قسم جديد" style="btn btn-success sharp"}

		<div class="horizontal-space"></div>

	{if $questionnaire->sections}
		{foreach $questionnaire->sections as $section}
		<div class="widget">
			<div class="widget-header">
				<span class="widget-caption" style="margin: 3px;">
					{url check=0 urltype="medit" opr_code='buildQuestionnaire' url_string="gnr/X000/buildQuestionnaire/editsection/0/{$smarty.session.lang}/{$section->id}"}
					{url check=0 urltype="mdelete" opr_code='buildQuestionnaire' url_string="gnr/X000/buildQuestionnaire/confirmsection/0/{$smarty.session.lang}/{$section->id}"}
					&nbsp;
					{$section->name}
				</span>

				<div class="widget-buttons" style="padding-top: 3px;">
					{url check=0 urltype="mbutton" opr_code='buildQuestionnaire' url_string="gnr/X000/buildQuestionnaire/addquestion/0/{$smarty.session.lang}/{$section->id}" text_value="&nbsp;<li class='fa fa-plus'></li>&nbsp;إضافة سؤال/معيار&nbsp;" style="btn btn-success sharp"}
				</div>
			</div>
			<div class="widget-body">

					{if isset($section->about)}
						<div class="row">
							<div class="col-lg-12">
								<div class="col-lg-12 snsoinput">{$section->about|nl2br}</div>
								<div class="horizontal-space"></div>
							</div>
						</div>

					{/if}

				{foreach $section->questions as $key => $question}
					<div class="row">
						<div class="col-lg-8">
							{Question QuestionRow=$question}
						</div>
						<div class="col-lg-2">
							<span>{#gnr_type#}</span><span>&nbsp;:&nbsp;</span>{getname table=st_setting id=$question->answer_type}<br>
							<span>{#gnr_required#}</span><span>&nbsp;:&nbsp;</span>{getname table=st_setting id=$question->required}<br>
						</div>
						<div class="col-lg-2">
							{if in_array($question->answer_type, [74,991,992])}
								{url urltype="mbutton" url_string="gnr/X000/buildQuestionnaire/options/0/{$smarty.session.lang}/{$question->id}" style="btn btn-default shiny" text_value="<i class='fa fa-cog'></i>"}
							{/if}
							{url check=0 urltype="medit" opr_code='buildQuestionnaire' url_string="gnr/X000/buildQuestionnaire/editquestion/0/{$smarty.session.lang}/{$question->id}"}
							{url check=0 urltype="mdelete" opr_code='buildQuestionnaire' url_string="gnr/X000/buildQuestionnaire/confirmquestion/0/{$smarty.session.lang}/{$question->id}"}
						</div>
					</div>
					<hr>
				{/foreach}

			</div>
		</div>
	{/foreach}
	{/if}

{/block}
{block name=back}{url urltype="path" url_string="{$smarty.session.s_back_url}"}{/block}