{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#p_edit_quiz#}</h4>
	</div>
	<div class="modal-body">
		<form method="post" action='{url urltype="path" url_string="gnr/X000/buildQuestionnaire/show/0/{$smarty.session.lang}/questionnaire/update/{$smarty.session.s_buildQuestionnaire_token}/{$row->id}"}'>
			<div class="row">
				<div class="col-lg-12">
					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_quiz_name#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="name" value="{$row->name}" placeholder="{#p_quiz_name#}" required></div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_quiz_purpose#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><textarea name="about" class="form-control" >{$row->about}</textarea></div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        {foreach $options as $option}
							<label style="cursor: pointer">
								<input name="type" type="radio" value="{$option->st_setting_id}" {if $row->type eq $option->st_setting_id}checked{/if}>
								<span class="text">{$option->st_setting_name_{$smarty.session.lang}}</span>
							</label>
							<br>
                        {/foreach}
					</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
				</div>
			</div>
		</form>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}