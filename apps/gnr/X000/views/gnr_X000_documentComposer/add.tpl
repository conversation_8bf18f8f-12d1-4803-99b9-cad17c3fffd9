{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script type="text/javascript">
        $(document).ready(function () {

            $("#inCaseOfMessage").css("display", "none");
            $("#inCaseOfTask").css("display", "none");

            $("#1049").click(function () {
                if ($("#1049").is(":checked")) {
                    $("#inCaseOfMessage").show('fast');
                    $("#inCaseOfTask").hide('fast');
                }
            });

            $("#1050").click(function () {
                if ($("#1050").is(":checked")) {
                    $("#inCaseOfMessage").hide('fast');
                    $("#inCaseOfTask").show('fast');
                }
            });

        });
    </script>
{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_add_new_transaction#}</h4>
    </div>
    <div class="modal-body">
        <form method="post" action='{url urltype="path" url_string="gnr/X000/documentComposer/compose/0/{$smarty.session.lang}/new/{$session['token']}/{$session['DMToken']}"}'>

            <div class="row">
                <div class="col-lg-12">

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_main_topic#}<label
                                style="color: rgb(251, 100, 30); ">*</label></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="main_topic" placeholder="{#p_main_topic#}" required></div>

                    {if str_contains($folder->name , 'وارد')}
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_transaction_mediator#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="company_name" placeholder="{#p_company#}"></div>
                    {elseif str_contains($folder->name , 'صادر')}

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_transaction_carrier#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="company_name" placeholder="{#p_company#}"></div>

                    {/if}


                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_comment#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control" name="comment" placeholder="{#p_comment#}"></textarea></div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_language#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <select name="language">
                            {foreach $languages as $language}
                                <option value="{$language->id}">{$language->name}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_transaction_tags#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <div class="checkbox">
                                {foreach $tags as $tag}
                                    <label>
                                        <input type="checkbox" name="tags[]" value="{$tag->name}">
                                        <span class="text">{$tag->name}</span>
                                    </label>
                                {/foreach}
                            </div>
                        </div>
                    </div>

                    {*<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_privacy_status#}</div>*}
                    {*<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">*}
                        {*<div class="control-group">*}
                            {*<div class="radio">*}
                                {*{foreach $privacyTypes as $privacy}*}
                                    {*<label>*}
                                        {*<input type="radio" name="privacy_status" value="{$privacy->id}" {if $privacy->id eq 1029} checked="checked"{/if}>*}
                                        {*<span class="text">{$privacy->translatedName}</span>*}
                                    {*</label>*}
                                {*{/foreach}*}
                            {*</div>*}
                        {*</div>*}
                    {*</div>*}

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp">{#gnr_add#}</button></div>

                </div>
            </div>

        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}