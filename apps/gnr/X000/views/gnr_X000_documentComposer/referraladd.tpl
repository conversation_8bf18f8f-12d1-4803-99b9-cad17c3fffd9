{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#p_add_referral#}</h4>
	</div>
	<div class="modal-body">
		<form  method="post" action='{url urltype="path" url_string="gnr/X000/documentComposer/compose/0/{$smarty.session.lang}/referrals/{$session['token']}/insert/{$session['DMToken']}"}' enctype='multipart/form-data'>
			<div class="row snsowraper">

				<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

					<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_subject#}</div>
					<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$referral->transactionObject->main_topic}</div>

					<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_action_needed#}</div>
					<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><textarea name="subject_comment" class="form-control"></textarea></div>

					<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_referral_type#}</div>
					<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
						<div class="control-group">
							<div class="radio">
                                {foreach $referralTypes as $referralType}
									<label>
										<input type="radio" name="type" id="{$referralType->id}" value="{$referralType->id}" {if $referralType->id eq 1032} checked="checked" {/if} required>
										<span class="text">{$referralType->translatedName}</span>
									</label>
                                {/foreach}
							</div>
						</div>
					</div>

					<div id="employeeReferral">

						<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_referral_to#}</div>
						<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
							<select name="employee_id">
                                {foreach $employees as $employee}
									<option value="{$employee->sh_user_id}">{$employee->sh_user_full_name}</option>
                                {/foreach}
							</select>
						</div>

						<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_referral_privileges#}</div>
						<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
							<div class="control-group">
                                {*{if $referral->transactionObject->isImported}*}
									{*<div class="checkbox">*}
										{*<label>*}
											{*<input type="checkbox" name="privileges[]" id="{ArchReferral::ARCH_PRIVILEGES_REFERRAL}" value="{ArchReferral::ARCH_PRIVILEGES_REFERRAL}">*}
											{*<span class="text">{t v=ArchReferral::ARCH_PRIVILEGES_REFERRAL}</span>*}
										{*</label>*}
									{*</div>*}
								{*{else}*}
                                    {foreach $privileges as $privilege}
                                        {if in_array($privilege->id, ','|explode:$referral->privileges)}
											<div class="checkbox">
												<label>
													<input type="checkbox" name="privileges[]" id="{$privilege->id}" value="{$privilege->id}">
													<span class="text">{$privilege->translatedName}</span>
												</label>
											</div>
                                        {/if}
                                    {/foreach}
                                {*{/if}*}
							</div>
						</div>

					</div>

					<div id="clientReferral">

						<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_referral_assignment_type#}</div>
						<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{#p_referral_to_client_outbox#}</div>

						<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_referral_destination#}</div>
						<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control" name="destination"></textarea></div>

					</div>

					<div id="publicationReferral" style="display: none">
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_publication_according_to#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <div class="radio">
                                <label>
                                    <input name="publication_with" id="unit_managers" type="radio" value="1">
                                    <span class="text">{#p_unit_managers#}</span>
                                </label>
                            </div>
							<div id="managers_menu" style="display: none">
								<div class="col-lg-11 col-lg-offset-1">
                                    {foreach $managers as $managerId => $managerName}
                                        {if in_array($managerId, $usersAlreadyInReferral)}
											<div class="checkbox">
												<label>
													<input type="checkbox" disabled>
													<span class="text">
														{$managerName} > {$unitsNames[$managerId]}
														<span style="color: orange">[ محال إليه مسبقاً ]</span>
													</span>
												</label>
											</div>
                                        {else}
											<div class="checkbox">
												<label>
													<input type="checkbox" name="managers[]" value="{$managerId}">
													<span class="text">{$managerName} > {$unitsNames[$managerId]}</span>
												</label>
											</div>
                                        {/if}
                                    {/foreach}
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_referral_privileges#}</div>
                                        <div class="col-lg-8">
                                            {foreach $privileges as $privilege}
                                                {if in_array($privilege->id, ','|explode:$referral->privileges)}
                                                    <div class="checkbox">
                                                        <label>
                                                            <input type="checkbox" name="managers_privileges[]" id="{$privilege->id}" value="{$privilege->id}">
                                                            <span class="text">{$privilege->translatedName}</span>
                                                        </label>
                                                    </div>
                                                {/if}
                                            {/foreach}
                                        </div>
                                    </div>
                                </div>
							</div>
                            <div class="radio">
                                <label>
                                    <input name="publication_with" id="user_classes" type="radio" value="2">
                                    <span class="text">{#p_user_classes#}</span>
                                </label>
                            </div>
							<div id="classes_menu" style="display: none;">
                                <div class="col-lg-11 col-lg-offset-1">
                                    {foreach $usersClasses as $userClass}
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="classes[]" value="{$userClass->id}">
                                                <span class="text">{$userClass->name}</span>
                                            </label>
                                        </div>
                                    {/foreach}
                                </div>
							</div>
                        </div>
					</div>

					<div id="assignmentDiv">
						<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_referral_assignment_type#}</div>
						<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
							<div class="control-group">
								<div class="radio">
                                    {foreach array_reverse($assignmentTypes) as $assignmentType}
										<label>
											<input type="radio" name="assignment_type" id="{$assignmentType->id}" value="{$assignmentType->id}" {if $assignmentType->id eq 1050} checked="checked" {/if} required>
											<span class="text">{$assignmentType->translatedName}</span>
										</label>
                                    {/foreach}
								</div>
							</div>
						</div>
					</div>

					<div id="inCaseOfMessage">

					</div>

					<div id="inCaseOfTask">

						<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_referral_execution_start_date#}</div>
						<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=arch_referral col=execution_start_date type=add row=[]}</div>

						<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_referral_execution_duration#}</div>
						<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="number" class="form-control" value="1" name="execution_duration" required></div>

						<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_referral_priority_status#}</div>
						<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
							<div class="control-group">
                                {foreach $priorities as $priority}
									<div class="radio">
										<label>
											<input type="radio" name="priority_status" id="{$priority->id}" value="{$priority->id}" {if $priority->id eq 1015} checked="checked" {/if} required>
											<span class="text">{$priority->translatedName}</span>
										</label>
									</div>
                                {/foreach}
							</div>
						</div>

					</div>

					<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
					<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp" >{#gnr_add#}</button></div>
				</div>
			</div>
		</form>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}
{block name=footer}
    <script type="text/javascript">
        resize_modal('lg');
        $(document).ready(function () {

            const user_classes = $('#user_classes');
            const unit_managers = $('#unit_managers');
            const managers_menu = $('#managers_menu');
            const classes_menu = $('#classes_menu');

            $("#inCaseOfMessage").css("display", "none");
            $("#inCaseOfTask").css("display", "none");

            if ($("#1050").is(":checked")) {
                $("#inCaseOfMessage").hide('fast');
                $("#inCaseOfTask").show('fast');
            } else {
                $("#inCaseOfMessage").show('fast');
                $("#inCaseOfTask").hide('fast');
            }

            $("#clientReferral").css("display", "none");

            $("#1049").click(function () {
                if ($("#1049").is(":checked")) {
                    $("#inCaseOfMessage").show('fast');
                    $("#inCaseOfTask").hide('fast');
                }
            });

            $("#1050").click(function () {
                if ($("#1050").is(":checked")) {
                    $("#inCaseOfMessage").hide('fast');
                    $("#inCaseOfTask").show('fast');
                }
            });

            $("#1032").change(function () {
                if ($("#1032").is(":checked")) {
                    $("#clientReferral").hide('fast');
                    $('#publicationReferral').hide('fast');
                    $("#employeeReferral").show('fast');
                }
            });

            $("#1033").change(function () {
                if ($("#1033").is(":checked")) {
                    $("#employeeReferral").hide('fast');
                    $('#publicationReferral').hide('fast');
                    $("#clientReferral").show('fast');
//                    $("#assignmentDiv").hide('fast');
                    if ($("#1050").is(":checked")) {
                        $("#inCaseOfMessage").hide('fast');
                        $("#inCaseOfTask").show('fast');
                    }
                }
            });

            $('#1181').change(function () {
                if ($('#1181').is(':checked')) {
                    $("#employeeReferral").hide('fast');
                    $("#clientReferral").hide('fast');
                    $('#publicationReferral').show('fast');
                }
            });

            user_classes.change(function () {
                if (user_classes.is(':checked')) {
                    classes_menu.show('fast');
                    managers_menu.hide('fast');
                }
            });
            unit_managers.change(function () {
                if (unit_managers.is(':checked')) {
                    managers_menu.show('fast');
                    classes_menu.hide('fast');
                }
            });

        });
    </script>
{/block}