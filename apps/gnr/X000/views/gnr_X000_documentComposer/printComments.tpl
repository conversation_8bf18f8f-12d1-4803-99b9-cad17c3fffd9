{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}
    <div class="row">

        <div class="col-lg-12">
            <div class="widget">
                <h4 class="text-center">{#p_document_data#}</h4>
                <div class="widget-body">
                    <div class="row">

                        <div class="col-lg-12">

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_main_topic#}<label
                                        style="color: rgb(251, 100, 30); ">*</label></div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">{$transaction->main_topic}</div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_sub_topic#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">{$transaction->sub_topic}</div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_action_needed#}
                                <label style="color: rgb(251, 100, 30); ">*</label></div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">{$transaction->action_needed}</div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_comment#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">{$transaction->comment}</div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_language#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">{getname table=sh_language id=$transaction->language}</div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_privacy_status#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">{t v=$transaction->privacy_status}</div>

                            {if $transaction->isImported or $transaction->isExported}
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_export_or_import_information#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">
                                    {if $transaction->isExported}
                                        <div class="row">
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_export_number#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">
                                                {$transaction->serial_number}
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_export_date#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">
                                                {getdate type=show row=$transaction col=issued_date}
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_export_attachments_count#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">
                                                {$transaction->attachment_count}
                                            </div>
                                        </div>
                                    {/if}
                                    {if $transaction->isImported}
                                        <div class="row">
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_import_number#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">
                                                {$transaction->serial_number}
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_import_date#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">
                                                {getdate type=show row=$transaction col=issued_date}
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_import_attachments_count#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">
                                                {$transaction->attachment_count}
                                            </div>
                                        </div>
                                    {/if}
                                </div>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>

            <div class="widget">
                <div class="widget-header separated hidden-print">
                    <span class="widget-caption">{#p_transaction_append_and_comment#}</span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-minus blue "></i>
                        </a>
                    </div>
                </div>
                <h4>{#p_transaction_append_and_comment#}</h4>
                <div class="widget-body">
                    <div class="row">

                        <div class="col-lg-12">
                            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table searchable">
                                <thead>
                                <tr>
                                    <th width="5%">
                                        {url check=0  urltype="madd" url_string="gnr/X000/documentComposer/commentadd/0/{$smarty.session.lang}/{$session['token']}" style="btn btn-success sharp"}
                                    </th>
                                    <th width="30%">{#p_comment_creator#}</th>
                                    <th width="50%">{#p_message#}</th>
                                    <th width="15%">{#p_comment_date#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i=1}
                                {foreach $comments as $comment}
                                    <tr {if $specific_comment_id eq $comment->id} style="background-color: #E1E2D2;" {/if}>
                                        <td>{$i++}</td>
                                        <td>{getname table=sh_user id=$comment->created_by}</td>
                                        <td>{$comment->subject|nl2br}</td>
                                        <td>{getdate type=show row=$comment col=created_date}</td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>

        </div>

    </div>
{/block}