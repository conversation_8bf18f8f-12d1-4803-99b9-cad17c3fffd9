{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#p_add_append_comment#}</h4>
	</div>
	<div class="modal-body">
		<form  method="post" action='{url urltype="path" url_string="gnr/X000/documentComposer/compose/0/{$smarty.session.lang}/comments/{$session['token']}/insert/{$session['DMToken']}"}' enctype='multipart/form-data'>
			<div class="row snsowraper">

				<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_message#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<textarea name="subject" class="form-control"></textarea>
					</div>


				</div>

				<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_forword_to#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<select multiple name="forword_to[]">
							<option value="0" selected>{#gnr_all#}</option>
                            {foreach $users as $user}
								<option value="{$user->id}">{$user->full_name}</option>
                            {/foreach}
						</select>

						<script>
                            $("select").selectize().on('change' , function(event) {
                                var selectize = this.selectize.items;
                                if (selectize.includes("0")){
//                                    var allIndex = selectize.indexOf("0");
                                    this.selectize.removeItem("0");
                                    this.selectize.removeOption("0");
//                                    console.log(selectize.splice(allIndex, 1));
                                }
							});
//							$('select').on('change' ,
						</script>

					</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp" >{#gnr_add#}</button></div>
				</div>


			</div>
		</form>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}