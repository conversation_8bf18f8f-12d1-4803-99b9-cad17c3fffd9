{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script type="text/javascript">
        $(document).ready(function () {

            $("#inCaseOfMessage").css("display", "none");
            $("#inCaseOfTask").css("display", "none");

            $("#1049").click(function () {
                if ($("#1049").is(":checked")) {
                    $("#inCaseOfMessage").show('fast');
                    $("#inCaseOfTask").hide('fast');
                }
            });

            $("#1050").click(function () {
                if ($("#1050").is(":checked")) {
                    $("#inCaseOfMessage").hide('fast');
                    $("#inCaseOfTask").show('fast');
                }
            });

        });
    </script>

    {$jsCode}

{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_transaction_data#}</h4>
    </div>
    <div class="modal-body">

        <form method="post" action='{url urltype="path" url_string="gnr/X000/documentComposer/compose/0/{$smarty.session.lang}/update/{$session['token']}/{$session['DMToken']}"}'>

            <div class="panel-group accordion" id="accordions">

                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordions" href="#collapseEditOnes" aria-expanded="true">
                                #1 {#p_document_data#}
                            </a>
                        </h4>
                    </div>
                    <div id="collapseEditOnes" class="panel-collapse collapse in" aria-expanded="true" style="">
                        <div class="panel-body border-red">

                            {if in_array(ArchReferral::ARCH_PRIVILEGES_EDIT,explode(',',$thisReferral->privileges)) and not $thisReferral->transactionObject->isExported}

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_main_topic#}<label
                                            style="color: rgb(251, 100, 30); ">*</label></div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="main_topic" value="{$thisReferral->transactionObject->main_topic}" placeholder="{#p_main_topic#}" required></div>
                                {if str_contains($folder->name , 'وارد')}
                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_transaction_mediator#}</div>
                                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="company_name" placeholder="{#p_company#}"  value="{$thisReferral->transactionObject->company_name}"></div>
                                {elseif str_contains($folder->name , 'صادر')}

                                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_transaction_carrier#}</div>
                                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="company_name" placeholder="{#p_company#}" value="{$thisReferral->transactionObject->company_name}"></div>

                                {/if}

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_comment#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="comment" value="{$thisReferral->transactionObject->comment}"></div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_language#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                    <select name="language">
                                        {foreach $languages as $language}
                                            <option value="{$language->id}" {if $language->id eq $thisReferral->transactionObject->language} selected="selected" {/if}>{$language->name}</option>
                                        {/foreach}
                                    </select>
                                </div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_transaction_tags#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                    <div class="control-group">
                                        <div class="checkbox">
                                            {foreach $tags as $tag}
                                                <label>
                                                    <input type="checkbox" name="tags[]" value="{$tag->name}" {if in_array($tag->name,explode(',',$thisReferral->transactionObject->tags))} checked="checked" {/if}>
                                                    <span class="text">{$tag->name}</span>
                                                </label>
                                            {/foreach}
                                        </div>
                                    </div>
                                </div>

                                {*<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_privacy_status#}</div>*}
                                {*<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">*}
                                    {*<div class="control-group">*}
                                        {*<div class="radio">*}
                                            {*{foreach $privacyTypes as $privacy}*}
                                                {*<label>*}
                                                    {*<input type="radio" name="privacy_status" value="{$privacy->id}" {if $privacy->id eq $thisReferral->transactionObject->privacy_status} checked="checked" {/if}>*}
                                                    {*<span class="text">{$privacy->translatedName}</span>*}
                                                {*</label>*}
                                            {*{/foreach}*}
                                        {*</div>*}
                                    {*</div>*}
                                {*</div>*}

                            {/if}

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_transaction_folder#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                <select name="folder_id">
                                    {foreach $folders as $folder}
                                        <option value="{$folder->id}" {if $folder->id eq $thisReferral->folder_id} selected="selected" {/if}>{$folder->name}</option>
                                    {/foreach}
                                </select>
                            </div>

                        </div>
                    </div>
                </div>

                {if in_array(ArchReferral::ARCH_PRIVILEGES_EDIT,explode(',',$thisReferral->privileges)) and not $thisReferral->transactionObject->isExported}
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="#accordions" href="#collapseEditTwos" aria-expanded="false">
                                    #2 {#p_transaction_mediator#}
                                </a>
                            </h4>
                        </div>
                        <div id="collapseEditTwos" class="panel-collapse collapse" aria-expanded="false">
                            <div class="panel-body border-red">

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_mediator_name#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="mediator_name" value="{$thisReferral->transactionObject->mediator_name}" placeholder="{#p_mediator_name#}"></div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_mediator_phone#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="mediator_phone" value="{$thisReferral->transactionObject->mediator_phone}" placeholder="{#p_mediator_phone#}"></div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_mediator_email#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="mediator_email" value="{$thisReferral->transactionObject->mediator_email}" placeholder="{#p_mediator_email#}"></div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_mediator_date#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate table=arch_transaction col=mediator_date type=edit row=$thisReferral->transactionObject}</div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_mediator_comment#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control" name="mediator_comment" placeholder="{#p_mediator_comment#}">{$thisReferral->transactionObject->mediator_comment}</textarea></div>

                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="#accordions" href="#collapseEditThrees" aria-expanded="false">
                                    #3 {#p_transaction_carrier#}
                                </a>
                            </h4>
                        </div>
                        <div id="collapseEditThrees" class="panel-collapse collapse" aria-expanded="false">
                            <div class="panel-body border-gold">

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_carrier_name#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="carrier_name" value="{$thisReferral->transactionObject->carrier_name}" placeholder="{#p_carrier_name#}"></div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_carrier_phone#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="carrier_phone" value="{$thisReferral->transactionObject->carrier_phone}" placeholder="{#p_carrier_phone#}"></div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_carrier_email#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="carrier_email" value="{$thisReferral->transactionObject->carrier_email}" placeholder="{#p_carrier_email#}"></div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_carrier_date#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate table=arch_transaction col=carrier_date type=edit row=$thisReferral->transactionObject}</div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_carrier_comment#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control" name="carrier_comment" placeholder="{#p_carrier_comment#}">{$thisReferral->transactionObject->carrier_comment}</textarea></div>

                            </div>
                        </div>
                    </div>

                {/if}

            </div>

            <div class="col-lg-12 col-md-9 col-sm-12 col-xs-12"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>

        </form>

    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}