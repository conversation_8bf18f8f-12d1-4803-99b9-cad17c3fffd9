{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <div class="row">

        <div class="col-lg-6 col-sm-12 col-xs-12">

            <div class="widget mb-0">
                <div class="widget-header separated">
                    <span class="widget-caption">{#p_document_data#}</span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-minus blue "></i>
                        </a>
                    </div>
                </div>
                <div class="widget-body">
                    <div class="row">

                        <div class="col-lg-12">

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_main_topic#}<label
                                        class="text-orange-dark">*</label></div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$transaction->main_topic}</div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_tags#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$transaction->tags}</div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_company#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$transaction->company_name}</div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_comment#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$transaction->comment}</div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_language#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getname table=sh_language id=$transaction->language}</div>

                            {*<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_privacy_status#}</div>*}
                            {*<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{t v=$transaction->privacy_status}</div>*}

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_folder#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$thisReferral->folderObject->name}</div>

                            <div class="horizontal-space"></div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                {if $transaction->isImported or $transaction->isExported}
                                {else}
                                    {url check=0 urltype="mbutton"  opr_code='documentComposer' url_string="gnr/X000/documentComposer/edit/0/{$smarty.session.lang}/{$session['token']}/{$thisReferral->id}" style="btn btn-default sharp shiny mr-1 mb-1" text_value="<i class='fa fa-edit'></i>&nbsp;{#p_edit_transaction_data#}"}
                                {/if}
                                {url check=0 urltype="print" opr_code='documentComposer' url_string="gnr/X000/documentComposer/printDocument/0/{$smarty.session.lang}/{$session['token']}" style="btn btn-default sharp shiny mr-1 mb-1" text_value="{#gnr_print#}"}
                            </div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_export_or_import_information#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">

                                {if $transaction->isExported or $transaction->issue_type eq 4}
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_export_number#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                {$transaction->serial_number}
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_export_date#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                {getdate type=show row=$transaction col=issued_date}
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_export_attachments_count#}</div>
                                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                {$transaction->attachment_count}
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div style="position: relative;">
                                                <img src="/framework/core/functions/image.php?image={SNSO_URL}/client/documents/qrcodes/{$qr_path}"
                                                     alt="qr-code" class="">
                                                <div style="text-align: center">
                                                    {url check=0 urltype="print"
                                                    opr_code='documentComposer'
                                                    url_string="gnr/X000/documentComposer/printQrCode/0/{$smarty.session.lang}/{$session['token']}" style="btn btn-default sharp shiny mr-1 mb-1" text_value="{#gnr_print#}"}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {/if}

                                {*{if $smarty.session.s_arch_from eq ArchFolder::LINK_FROM_ORG_OUTBOX}*}
                                {if $thisReferral->folderObject->type eq ArchFolder::ARCH_BOX_EMPLOYEE_OUTBOX or  $thisReferral->folderObject->type eq ArchFolder::ARCH_BOX_CLIENT_OUTBOX}
                                    {if $transaction->issue_type eq  ArchTransaction::EXPORT_NOT_SENT}
                                        {if ($transaction->created_by eq $smarty.session.user->id) or ($transaction->created_by eq 2) or ($transaction->created_by eq 1) }
                                            {url urltype=mbutton url_string="gnr/X000/documentComposer/confirmExport/0/{$smarty.session.lang}/{$session['token']}/{$transaction->id}/archClientInbox" text_value="<i class='fa fa-upload'></i>{#gnr_export#}" style="btn btn-default shiny mr-1 mb-1"}
                                        {/if}
                                    {else}
                                        {if !$transaction->isExported}
                                            {url urltype=mbutton
                                                url_string="gnr/X000/documentComposer/generateExportData/0/{$smarty.session.lang}/{$session['token']}/{$transaction->id}" text_value="<i class='fa fa-upload'></i>{#gnr_generate_export_data#}" style="btn btn-default mr-1 mb-1 shiny"}
                                        {/if}
                                    {/if}
                                {/if}

                                {if $transaction->isImported or $transaction->issue_type eq 3}
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="col-lg-6 col-md-8 col-sm-8">
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_import_number#}</div>
                                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                    {$transaction->serial_number}
                                                </div>
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_import_date#}</div>
                                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                    {getdate type=show row=$transaction col=issued_date}
                                                </div>
                                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_import_attachments_count#}</div>
                                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                                    {$transaction->attachment_count}
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div style="position: relative;">
                                                    <img src="/framework/core/functions/image.php?image={SNSO_URL}/client/documents/qrcodes/{$qr_path}"
                                                         alt="qr-code" class="">
                                                    <div style="text-align: center">
                                                        {url check=0 urltype="print"
                                                            opr_code='documentComposer'
                                                            url_string="gnr/X000/documentComposer/printQrCode/0/{$smarty.session.lang}/{$session['token']}" style="btn btn-default sharp shiny mr-1 mb-1" text_value="{#gnr_print#}"}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {/if}

                                {*{if $smarty.session.s_arch_from eq ArchFolder::LINK_FROM_ORG_INBOX}*}
                                {if $thisReferral->folderObject->type eq ArchFolder::ARCH_BOX_EMPLOYEE_INBOX or  $thisReferral->folderObject->type eq ArchFolder::ARCH_BOX_CLIENT_INBOX}
                                    {if $transaction->issue_type eq  ArchTransaction::IMPORT_NOT_SENT}
                                        {if ($transaction->created_by eq $smarty.session.user->id) or ($transaction->created_by eq 2) or ($transaction->created_by eq 1) }
                                            {url urltype=mbutton url_string="gnr/X000/documentComposer/confirmImport/0/{$smarty.session.lang}/{$session['token']}/{$transaction->id}/archClientInbox" text_value="<i class='fa fa-upload'></i>{#gnr_import#}" style="btn btn-default shiny mr-1 mb-1"}
                                        {/if}
                                    {else}
                                        {if !$transaction->isImported}
                                            {url urltype=mbutton url_string="gnr/X000/documentComposer/generateImportData/0/{$smarty.session.lang}/{$session['token']}/{$transaction->id}" text_value="<i class='fa fa-envelope-o'></i>{#gnr_generate_import_data#}" style="btn btn-default shiny mr-1 mb-1"}
                                        {/if}
                                    {/if}
                                {/if}

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-sm-12 col-xs-12">
            <div class="widget">
                <div class="widget-header separated">
                    <span class="widget-caption">{#gnr_attachments#}</span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-minus blue "></i>
                        </a>
                    </div>
                </div>
                <div class="tickets-container widget-body">
                    {if in_array(ArchReferral::ARCH_PRIVILEGES_ATTACH,explode(',',$thisReferral->privileges)) and not $transaction->isExported}
                        {url check=0  urltype="mbutton" url_string="gnr/X000/documentComposer/documentadd/0/{$smarty.session.lang}/{$session['token']}/documentComposer" style="btn btn-success sharp" text_value="{#gnr_attachment_add#}"}
                    {/if}

                    <div class="horizontal-space"></div>

                    <ul class="tickets-list">
                        {foreach $documents as $document}
                            <li class="ticket-item">
                                <div class="row">
                                    <div class="ticket-user col-lg-9 col-sm-12">
                                        {url check=0 urltype="button"  url_string="gnr/X000/documents/download/0/{$smarty.session.lang}/{$document->id}" text_value={#gnr_download#}}
                                        {url check=0 urltype="alinkn" oprvtype=3 url_string="gnr/X000/documents/preview/0/{$smarty.session.lang}/{$document->id}" text_value="{#gnr_view#}"}
                                        <span class="ml-2 mr-4">
                                            {$document->name}
                                        </span>
                                    </div>
                                    <div class="ticket-type  col-lg-3 col-sm-6 col-xs-12" style="text-align: center">
                                        <span class="divider hidden-xs"></span>
                                        <span class="type">
                                        {if $document->created_by eq $smarty.session.user->id and not $transaction->isExported}
                                            {url check=0 urltype="medit" url_string="gnr/X000/documentComposer/documentedit/0/{$smarty.session.lang}/{$session['token']}/documentComposer/{$document->id}"}
                                        {/if}
                                            {if $thisReferral->created_by eq $smarty.session.user->id and not $transaction->isExported}
                                                {url check=0 urltype="mdelete" url_string="gnr/X000/documentComposer/documentconfirm/0/{$smarty.session.lang}/{$session['token']}/{$document->id}"}
                                            {/if}
                                    </span>
                                    </div>
                                </div>
                            </li>
                        {/foreach}
                    </ul>
                </div>
            </div>
        </div>

    </div>
    {if in_array(ArchReferral::ARCH_PRIVILEGES_EDIT,explode(',',$thisReferral->privileges)) and not $thisReferral->transactionObject->isExported}

        <div class="row">
            <div class="col-md-6 mt-1">
                <div class="panel-group accordion"
                     id="accordion"
                >
                    <div class="panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a class="accordion-toggle collapsed" data-toggle="collapse"
                                   data-parent="#accordions" href="#mediator" aria-expanded="false">
                                    {#p_transaction_mediator#}
                                </a>
                            </h4>
                        </div>

                        <div id="mediator" class="panel-collapse collapse in" aria-expanded="false">
                            <div class="panel-body border-red">

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_mediator_name#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><label
                                            for="">{$thisReferral->transactionObject->mediator_name}</label>
                                </div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_mediator_phone#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><label
                                            for="">{$thisReferral->transactionObject->mediator_phone}</label>
                                </div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_mediator_email#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                    <lable>{$thisReferral->transactionObject->mediator_email}</lable>
                                </div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_mediator_date#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                    {getdate table=arch_transaction col=mediator_date type=show row=$thisReferral->transactionObject}
                                </div>


                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_mediator_comment#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><label
                                            for="">{$thisReferral->transactionObject->mediator_comment}</label>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a class="accordion-toggle collapsed" data-toggle="collapse"
                                   data-parent="#accordions" href="#carrier"
                                   aria-expanded="false">
                                    {#p_transaction_carrier#}
                                </a>
                            </h4>
                        </div>
                        <div id="carrier" class="panel-collapse collapse"
                             aria-expanded="false">
                            <div class="panel-body border-gold">

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_carrier_name#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><label
                                            for="">{$thisReferral->transactionObject->carrier_name}</label>
                                </div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_carrier_phone#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><label
                                            for="">{$thisReferral->transactionObject->carrier_phone}</label>
                                </div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_carrier_email#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                    <lable>{$thisReferral->transactionObject->carrier_email}</lable>
                                </div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_carrier_date#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate table=arch_transaction col=carrier_date type=show row=$thisReferral->transactionObject}</div>

                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_carrier_comment#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><label
                                            for="">{$thisReferral->transactionObject->carrier_comment}</label>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {/if}
    <div class="row">
        <div class="col-lg-12 col-sm-12 col-xs-12">
            <div class="widget">
                <div class="widget-header separated">
                    <span class="widget-caption">
                        {if $thisReferral->ownership eq 1}
                            {#p_transaction_information#}
                        {/if}
                        {if $thisReferral->ownership eq 0}
                            {#p_referral_information#}
                        {/if}
                    </span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-minus blue "></i>
                        </a>
                    </div>
                </div>
                <div class=" widget-body">

                    <div class="row">
                        <div class="col-lg-12">

                            {if $thisReferral->ownership eq 0}
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_main_topic#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$thisReferral->subject_title}</div>
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_action_needed#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$thisReferral->subject_comment|nl2br}</div>
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_assignment_type#}</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$thisReferral->icon}
                                    &nbsp;{t v=$thisReferral->assignment_type}</div>
                            {/if}


                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_created_by#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getname table=sh_user id=$thisReferral->created_by}</div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_created_date#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate table=arch_referral col=created_date type=showauto row=$thisReferral->created_date}</div>

                            {if $thisReferral->assignment_type eq ArchReferral::ARCH_REFERRAL_TYPES_TASK}
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                    {url check=0 urltype="mbutton" opr_code='documentComposer' url_string="gnr/X000/documentComposer/editMyReferral/0/{$smarty.session.lang}/{$session['token']}" style="btn btn-default sharp shiny" text_value="<i class='fa fa-cog'></i>&nbsp;{#p_my_referral_setting#}"}
                                </div>
                            {/if}

                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-sm-12 col-xs-12">
            <div class="widget">
                <div class="widget-header separated">
                    <span class="widget-caption">{#p_transaction_referrals#}</span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-minus blue "></i>
                        </a>
                    </div>
                </div>
                <div class="tickets-container widget-body">
                    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table searchable">
                        <thead>
                        <tr>
                            <th width="3%">
                                {*{if in_array(ArchReferral::ARCH_PRIVILEGES_REFERRAL,explode(',',$thisReferral->privileges)) and not $transaction->isExported}*}
                                {if in_array(ArchReferral::ARCH_PRIVILEGES_REFERRAL,explode(',',$thisReferral->privileges))}
                                    {url check=0  urltype="madd" url_string="gnr/X000/documentComposer/referraladd/0/{$smarty.session.lang}/{$session['token']}/{$thisReferral->id}" style="btn btn-success sharp"}
                                {/if}
                            </th>
                            <th width="20%">{#p_action_needed#}</th>
                            <th width="15%">{#gnr_sender#}</th>
                            <th width="15%">{#gnr_reciepent#}</th>
                            <th width="9%">{#p_edit_privilege#}</th>
                            <th width="9%">{#p_refer_privilege#}</th>
                            <th width="9%">{#p_attach_privilege#}</th>
                            <th width="9%">{#p_transaction_append_and_comment#}</th>
                            <th width="5%">{#gnr_type#}</th>
                            <th width="10%">{#gnr_created_date#}</th>
                            <th width="10%">{#gnr_settings#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {$i=1}
                        {foreach $referrals as $referral}
                            <tr>
                                <td>{$i++}</td>
                                <td>{$referral->subject_comment}</td>
                                <td>{getname table=sh_user id=$referral->created_by}</td>
                                <td>
                                    {if $referral->type eq 1032}
                                        {getname table=sh_user id=$referral->employee_id}
                                    {/if}

                                    {if $referral->type eq 1033}
                                        {#p_client_outbox#}
                                    {/if}
                                </td>
                                <td>
                                    {if in_array(ArchReferral::ARCH_PRIVILEGES_EDIT, ','|explode:$referral->privileges)}
                                        {#gnr_yes#}
                                    {else}
                                        {#gnr_no#}
                                    {/if}
                                </td>
                                <td>
                                    {if in_array(ArchReferral::ARCH_PRIVILEGES_REFERRAL, ','|explode:$referral->privileges)}
                                        {#gnr_yes#}
                                    {else}
                                        {#gnr_no#}
                                    {/if}
                                </td>
                                <td>
                                    {if in_array(ArchReferral::ARCH_PRIVILEGES_ATTACH, ','|explode:$referral->privileges)}
                                        {#gnr_yes#}
                                    {else}
                                        {#gnr_no#}
                                    {/if}
                                </td>
                                <td>
                                    {if in_array(ArchReferral::ARCH_PRIVILEGES_FORWOED, ','|explode:$referral->privileges)}
                                        {#gnr_yes#}
                                    {else}
                                        {#gnr_no#}
                                    {/if}
                                </td>
                                <td class="text-center">{getname table=st_setting id=$referral->assignment_type}</td>
                                <td class="text-center">{getdate type=show row=$referral col=created_date}</td>
                                <td class="text-center">
                                    {url check=0 urltype="mbutton" url_string="gnr/X000/documentComposer/referralbrowse/0/{$smarty.session.lang}/{$referral->id}" text_value='<i class="fa fa-file-text"></i>'}
                                    {if $referral->created_by eq $smarty.session.user->id and not $transaction->isExported}
                                        {url check=0 urltype="medit" url_string="gnr/X000/documentComposer/referraledit/0/{$smarty.session.lang}/{$session['token']}/{$referral->id}/{$thisReferral->id}"}
                                        {url check=0 urltype="mdelete" url_string="gnr/X000/documentComposer/referralconfirm/0/{$smarty.session.lang}/{$session['token']}/{$referral->id}"}
                                    {elseif $referral->employee_id eq $smarty.session.user->id and in_array(ArchReferral::ARCH_PRIVILEGES_EDIT, ','|explode:$referral->privileges) and not $transaction->isExported}
                                        {url check=0 urltype="medit" url_string="gnr/X000/documentComposer/referraledit/0/{$smarty.session.lang}/{$session['token']}/{$referral->id}/{$thisReferral->id}"}
                                        {url check=0 urltype="mdelete" url_string="gnr/X000/documentComposer/referralconfirm/0/{$smarty.session.lang}/{$session['token']}/{$referral->id}"}
                                    {/if}
                                </td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {*{if in_array(ArchReferral::ARCH_PRIVILEGES_FORWOED,explode(',',$thisReferral->privileges))}*}

    <div class="row">
        <div class="col-lg-12">
            <div class="widget">
                <div class="widget-header separated">
                    <span class="widget-caption">{#p_transaction_append_and_comment#}</span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-minus blue "></i>
                        </a>
                    </div>
                </div>
                <div class="tickets-container widget-body">
                    {if in_array(ArchReferral::ARCH_PRIVILEGES_FORWOED,explode(',',$thisReferral->privileges))}
                    <p>
                        {url check=0 urltype="print" opr_code='documentComposer' url_string="gnr/X000/documentComposer/printComments/0/{$smarty.session.lang}/{$session['token']}" style="btn btn-default sharp shiny" text_value="{#gnr_print#}"}
                    </p>
                    {/if}
                    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table searchable">
                        <thead>
                        <tr>
                            <th width="5%">
                                {if in_array(ArchReferral::ARCH_PRIVILEGES_FORWOED,explode(',',$thisReferral->privileges))}
                                {url check=0  urltype="madd" url_string="gnr/X000/documentComposer/commentadd/0/{$smarty.session.lang}/{$session['token']}" style="btn btn-success sharp"}
                                {/if}
                            </th>
                            <th width="25%">{#p_comment_creator#}</th>
                            <th width="40%">{#p_message#}</th>
                            <th width="20%">{#p_comment_date#}</th>
                            <th width="10%">{#gnr_settings#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {$i=1}
                        {foreach $comments as $comment}
                            {if in_array($smarty.session.user->id,explode(',',$comment->forword_to))
                            or $comment->forword_to eq "0"
                            or $comment->created_by eq $smarty.session.user->id
                            or is_null($comment->forword_to)}
                                <tr {if $specific_comment_id eq $comment->id} style="background-color: #E1E2D2;" {/if}>
                                    <td>{$i++}</td>
                                    <td>{getname table=sh_user id=$comment->created_by}</td>
                                    <td>{$comment->subject|nl2br}</td>
                                    <td class="text-center">{getdate type=show row=$comment col=created_date}</td>
                                    <td class="text-center">
                                        {if $comment->created_by eq $smarty.session.user->id and $comment@last and not $transaction->isExported}
                                            {url check=0 urltype="medit" url_string="gnr/X000/documentComposer/commentedit/0/{$smarty.session.lang}/{$session['token']}/{$comment->id}"}
                                            {url check=0 urltype="mdelete" url_string="gnr/X000/documentComposer/commentconfirm/0/{$smarty.session.lang}/{$session['token']}/{$comment->id}"}
                                        {/if}
                                    </td>
                                </tr>
                            {/if}
                        {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {*{/if}*}
{/block}
{block name=back}
    {if $smarty.session.s_arch_from eq 2}
        {url urltype="path" url_string="mng/P208/archClientInbox/show/0/ar/menu"}
    {elseif $smarty.session.s_arch_from eq 3}
        {url urltype="path" url_string="mng/P208/archClientOutbox/show/0/ar/menu"}
    {elseif $smarty.session.s_arch_from eq 4}
        {url urltype="path" url_string="mng/P208/archClientArchive/show/0/ar/menu"}
    {elseif $smarty.session.s_arch_from eq 5}
        {url urltype="path" url_string="mng/P208/archDashboard/show/0/ar/menu"}
    {else}
        {url urltype="path" url_string="usr/P002/employeeMailBox/show/0/ar/activateTab/{$thisReferral->folderObject->id}"}
    {/if}
{/block}