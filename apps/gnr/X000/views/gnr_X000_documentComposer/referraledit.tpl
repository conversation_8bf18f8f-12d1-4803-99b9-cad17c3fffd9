{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script type="text/javascript">
        $(document).ready(function () {

            $("#inCaseOfMessage").css("display", "none");
            $("#inCaseOfTask").css("display", "none");


            $("#clientReferral").css("display", "none");

            $("#1049").click(function () {
                if ($("#1049").is(":checked")) {
                    $("#inCaseOfMessage").show('fast');
                    $("#inCaseOfTask").hide('fast');
                }
            });

            $("#1050").click(function () {
                if ($("#1050").is(":checked")) {
                    $("#inCaseOfMessage").hide('fast');
                    $("#inCaseOfTask").show('fast');
                }
            });

            $("#1032").click(function () {
                if ($("#1032").is(":checked")) {
                    $("#employeeReferral").show('fast');
                    $("#clientReferral").hide('fast');
                    $("#assignmentDiv").show('fast');
                    $("#inCaseOfTask").hide('fast');
                }
            });

            $("#1033").click(function () {
                if ($("#1033").is(":checked")) {
                    $("#employeeReferral").hide('fast');
                    $("#clientReferral").show('fast');
                    $("#assignmentDiv").hide('fast');
                    $("#inCaseOfMessage").hide('fast');
                    $("#inCaseOfTask").show('fast');
                }
            });

        });
    </script>

    {$jsCode}

{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_referral#}</h4>
    </div>
    <div class="modal-body">
        <form  method="post" action='{url urltype="path" url_string="gnr/X000/documentComposer/compose/0/{$smarty.session.lang}/referrals/{$session['token']}/update/{$session['DMToken']}/{$referral->id}"}' enctype='multipart/form-data'>
            <div class="row snsowraper">

                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_subject#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$referral->subject_title}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_action_needed#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control" name="subject_comment">{$referral->subject_comment|nl2br}</textarea></div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_type#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <div class="radio">
                                {foreach $referralTypes as $referralType}
                                    {* Hide publication type in edit *}
                                    {if $referralType->id ne ArchReferral::ARCH_DESTINATION_PUBLIC}
                                        <label>
                                            <input type="radio" name="type" id="{$referralType->id}" value="{$referralType->id}" {if $referralType->id eq $referral->type} checked="checked" {/if} required>
                                            <span class="text">{$referralType->translatedName}</span>
                                        </label>
                                    {/if}
                                {/foreach}
                            </div>
                        </div>
                    </div>

                    <div id="employeeReferral">

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_to#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <select name="employee_id" required>
                                {foreach $employees as $employee}
                                    <option value="{$employee->sh_user_id}" {if $employee->sh_user_id eq $referral->employee_id} selected="selected" {/if}>{$employee->sh_user_full_name}</option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_privileges#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                {if $referral->transactionObject->isImported}
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="privileges[]" id="{ArchReferral::ARCH_PRIVILEGES_REFERRAL}" value="{ArchReferral::ARCH_PRIVILEGES_REFERRAL}">
                                        <span class="text">{t v=ArchReferral::ARCH_PRIVILEGES_REFERRAL}</span>
                                    </label>
                                </div>
                                {else}
                                    {foreach $privileges as $privilege}
                                        {if in_array($privilege->id, ','|explode:$thisReferral->privileges)}
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="privileges[]" id="{$privilege->id}" value="{$privilege->id}" {if in_array($privilege->id,explode(',',$referral->privileges))} checked="checked"{/if}>
                                                <span class="text">{$privilege->translatedName}</span>
                                            </label>
                                        </div>
                                        {/if}
                                    {/foreach}
                                {/if}
                            </div>
                        </div>

                    </div>

                    <div id="clientReferral">

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{#p_referral_to_client_outbox#}</div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control" name="destination">{$referral->destination}</textarea></div>
                    </div>

                    <div id="assignmentDiv">

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_assignment_type#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                {foreach array_reverse($assignmentTypes) as $assignmentType}
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="assignment_type" id="{$assignmentType->id}" value="{$assignmentType->id}" {if $assignmentType->id eq $referral->assignment_type} checked="checked" {/if} required>
                                            <span class="text">{$assignmentType->translatedName}</span>
                                        </label>
                                    </div>
                                {/foreach}
                            </div>
                        </div>

                    </div>

                    <div id="inCaseOfMessage">

                    </div>

                    <div id="inCaseOfTask">

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_execution_start_date#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate table=arch_referral col=execution_start_date type=edit row=$referral}</div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_execution_duration#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="number" class="form-control" name="execution_duration" value="{$referral->execution_duration}" required></div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_priority_status#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                {foreach $priorities as $priority}
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="priority_status" id="{$priority->id}" value="{$priority->id}" {if $priority->id eq $referral->priority_status} checked="checked" {/if} required>
                                            <span class="text">{$priority->translatedName}</span>
                                        </label>
                                    </div>
                                {/foreach}
                            </div>
                        </div>

                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button></div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}