{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_generate_import_data#}: {$transaction->main_topic}</h4>
    </div>
    <div class="modal-body">

        <form method="post" action='{url urltype="path" url_string="gnr/X000/documentComposer/compose/0/{$smarty.session.lang}/generateImportData/{$session['token']}/{$session['DMToken']}/{$transaction->id}/archClientInbox"}'>

            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_import_date#}</div>
            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                {getdate type=add col=date id=import_date}
            </div>

            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_attachments_count#}</div>
            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                <input type="number" min="0" name="attachments_count" class="form-control">
            </div>

            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_serial_number#}</div>
            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                <div class="radio">
                    <label>
                        <input name="enumeration_type" id="manual" value="1" type="radio" checked>
                        <span class="text">{#p_manual#}</span>
                    </label>
                </div>
                <div class="row">
                    <div id="manual_div" class="col-lg-11 col-lg-offset-1">
                        <input type="text" name="serial_number" class="form-control" required id="manual_input">
                    </div>
                </div>
                <div class="radio">
                    <label>
                        <input name="enumeration_type" id="automatic" value="2" type="radio">
                        <span class="text">{#p_automatic#}</span>
                    </label>
                </div>
                <div id="automatic_div" class="col-lg-11 col-lg-offset-1">
                    <div class="radio">
                        <label>
                            <input name="parts" value="1" type="radio">
                            <span class="text">{#p_year#}</span>
                        </label>
                    </div>
                    <div class="radio">
                        <label>
                            <input name="parts" value="2" type="radio">
                            <span class="text">{#p_year_and_month#}</span>
                        </label>
                    </div>
                    <div class="radio">
                        <label>
                            <input name="parts" value="3" type="radio" checked>
                            <span class="text">{#p_year_and_month_and_day#}</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                <button class="btn btn-warning shiny">{#p_confirm_import#}</button>
            </div>

        </form>

    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
{block name=footer}
    <script>
        $(document).ready(function () {
            $('form').parsley();

            var manual = $('#manual');
            var automatic = $('#automatic');
            var manual_div = $('#manual_div');
            var automatic_div = $('#automatic_div');
            var manual_input = $('#manual_input');

            automatic_div.hide();

            manual.change(function () {
                if (manual.is(':checked')) {
                    manual_div.show('fast');
                    manual_input.attr('required', true);
                    automatic_div.hide('fast');
                }
            });
            automatic.change(function () {
                if (automatic.is(':checked')) {
                    automatic_div.show('fast');
                    manual_input.attr('required', false);
                    manual_div.hide('fast');
                }
            });
        });
    </script>
{/block}