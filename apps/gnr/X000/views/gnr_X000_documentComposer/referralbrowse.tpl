{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_view_referral#}
            {url check=0 urltype="alinkn" url_string="gnr/X000/documentComposer/referralprint/0/{$smarty.session.lang}/{$referral->id}" text_value="<i class='fa fa-print black'></i>&nbsp;{#gnr_print#}&nbsp;" style="btn btn-default"}</h4>
    </div>
    <div class="modal-body">
        <div class="row snsowraper">

            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_subject#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$referral->subject_title|nl2br}</div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_action_needed#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$referral->subject_comment|nl2br}</div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_type#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{t v=$referral->type}</div>

                {if $referral->type eq ArchReferral::ARCH_DESTINATION_EXTERNAL}

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_destination#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$referral->destination}</div>

                {/if}

                {if $referral->type eq ArchReferral::ARCH_DESTINATION_INTERNAL}

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_to#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getname table=sh_user id=$referral->employee_id}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_privileges#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{explodeSettingArray array =$referral->privileges}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_assignment_type#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{t v=$referral->assignment_type}</div>

                {/if}

                {if $referral->assignment_type eq ArchReferral::ARCH_REFERRAL_TYPES_TASK}

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_execution_start_date#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate table=arch_referral col=execution_start_date type=show row=$referral}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_priority_status#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{t v=$referral->priority_status}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_execution_duration#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$referral->execution_duration}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_execution_type#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <span style="color: {$referral->executionTypeColor}">
                            <i class="fa fa-circle"></i>&nbsp;{t v=$referral->execution_type}</span></div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_referral_transaction_execution_status#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        {*<span style="color: {ArchReferral::referralExecutionStatusColor($referral->execution_status())}">*}
                        <span style="color: {$referral->executionStatusColor}">
                            <i class="fa fa-circle"></i>&nbsp;{t v=$referral->execution_status}</span>
                    </div>
                {/if}

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_created_date#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate table=arch_referral col=created_date type=show row=$referral}</div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_created_by#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getname table=sh_user id=$referral->created_by}</div>

            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}