{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_referral_change_status_notification#}</h4>
    </div>
    <div class="modal-body">
        <div class="row snsowraper">
            <h4 class="text-center">
                {getname table=sh_user id=$referral->employee_id}
                -> {#p_has_changed_referral_status_to#}: ( {t v=$referral->execution_type} )
            </h4>
            <div class="text-center">
                <a href="{url urltype=path url_string="gnr/X000/documentComposer/compose/0/{$smarty.session.lang}/browse/{$referral->id}/gnr/X000/notifications/show/0/{$smarty.session.lang}"}"
                   target="_blank"
                   class="btn btn-default sharp">
                    {#p_view_referral#}
                </a>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}