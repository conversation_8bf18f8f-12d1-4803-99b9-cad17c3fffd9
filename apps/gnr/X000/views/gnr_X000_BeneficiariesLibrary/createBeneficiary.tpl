{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script type="text/javascript" src="/templates/assets/js/vue.min.js"></script>
    <script type="text/javascript" src="/templates/assets/js/axios.js"></script>
    <script>
        var app = new Vue({
            el: '#app',
            data: {
                userData: {
                    fr_name: '',
                    secd_name: '',
                    thrd_name: '',
                    fm_name: '',
                    gender: '',
                    classifications: [],
                    register_by: 1,
                    belongs_to_user: '',
                    email: '',
                    tell: '',
                    sponsorship_number: '',
                    bank_number: ''
                },
                success: ''
            },
            methods: {

                sendData: function () {
                    this.userData.belongs_to_user = this.$refs.select.value
                    var params = new URLSearchParams();
                    params.append('userData', JSON.stringify(this.userData));

                    this.success = 'disabled';
                    axios.post('/framework/core/functions/ajax/create_beneficiary.php', params)
                        .then(function (response) {
                            location.reload();
                        }).catch(err => {
                        this.success = ''
                    });
                }
            }
        });
    </script>
    <script type="text/javascript">
        $(document).ready(function () {

            $("#regFollowDev").css("display", "none");

            $("#regNew").change(function () {
                if ($("#regNew").is(":checked")) {
                    $("#regFollowDev").hide();
                    $("#regAdditionDev").show();
                }
            });

            $("#regFollow").change(function () {
                if ($("#regFollow").is(":checked")) {
                    $("#regFollowDev").show();
                    $("#regAdditionDev").show();
                }
            })

        });
    </script>
{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_createBeneficiary#}</h4>
    </div>
    <div class="modal-body" id="app">
        <div class="row">
            <form method="post" action='#'>
                <div class="row snsowraper">

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_first_name#}{sup}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control"
                                                                                        name="fr_name"
                                                                                        v-model="userData.fr_name"
                                                                                        required>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_second_name#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control"
                                                                                        name="secd_name"
                                                                                        v-model="userData.secd_name">
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_third_name#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control"
                                                                                        name="thrd_name"
                                                                                        v-model="userData.thrd_name">
                    </div>

                    <div class="col-lg-1 col-md-1 col-sm-12"></div>
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_family_name#}{sup}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control"
                                                                                        name="fm_name"
                                                                                        v-model="userData.fm_name"
                                                                                        required>
                    </div>

                    <div class="col-lg-1 col-md-1 col-sm-12"></div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_sex#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $sex_list as $srow}
                                {if $srow->id ne 53}
                                    <div class="radio">
                                        <label>
                                            <input name="gender" v-model="userData.gender"
                                                   value="{$srow->id}" {if $srow@first} checked {/if}type="radio">
                                            <span class="text">{$srow->translatedName}</span>
                                        </label>
                                    </div>
                                {/if}
                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_category#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $classifications as $class}
                                <div class="checkbox">
                                    <label>
                                        <input name="classification[]" v-model="userData.classifications"
                                               value="{$class->id}"
                                               type="checkbox" {if $class->id eq $user_class} checked="checked" {/if}>
                                        <span class="text">{getname table=sh_userclasses id=$class->id}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-1 col-md-1 col-sm-12"></div>


                    <!-- Can not Register user without email&phone -->
                    {*{if $orgRow->sh_org_user_registeration_type eq 869}*}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_registration#} :</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <div class="radio">
                            <label>
                                <input type="radio" name="register_by" value="1"
                                       id="regNew" required="required" checked="checked" v-model="userData.register_by">
                                <span class="text">{#p_new#} جديد</span>
                            </label>
                            <br>
                            <label>
                                <input type="radio" name="register_by" value="2"
                                       id="regFollow" required="required" v-model="userData.register_by">
                                <span class="text">{#gnr_addBeneficiary#} تابع</span>
                            </label>
                        </div>
                    </div>

                    <div class="col-lg-1 col-md-1 col-sm-12"></div>

                    <div id="regFollowDev">
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">المستفيدين{sup}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <select required ref="select">
                                <option value="">{#gnr_unspecified#}</option>
                                {foreach $users as $user}
                                    <option value="{$user->id}">
                                        {$user->fr_name}&nbsp;
                                        {$user->secd_name}&nbsp;
                                        {$user->thrd_name}&nbsp;
                                        {$user->fm_name}&nbsp;&raquo;&nbsp;{$user->tell}
                                    </option>
                                {/foreach}
                            </select>
                        </div>

                    </div>

                    <div id="regAdditionDev">

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_mobile_number#}{sup}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                            class="form-control tooltip-maroon"
                                                                                            data-toggle="tooltip"
                                                                                            data-placement="top"
                                                                                            data-original-title="s{#p_email_registre_tooltip#}"
                                                                                            name="tell"
                                                                                            v-model="userData.tell">
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_sponsorship_number#}{sup}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <input type="text" class="form-control" name="sponsorship_number"
                                   v-model="userData.sponsorship_number" required>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_ayban_number#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                            <input type="text" class="form-control" name="bank_number" v-model="userData.bank_number">
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <button type="button" class="btn btn-success sharp" @click="sendData()"
                                :class="success">{#gnr_add#}</button>
                        <a target="_blank" class="btn btn-info"
                           href="{url urltype=path url_string="gnr/X000/BeneficiariesLibrary/search/0/{$smarty.session.lang}/clear"}">{#gnr_search#}</a>
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-12"></div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}