{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=title}{#gnr_user#}{/block}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />{/block}
{block name=page_body}

    <script type="text/javascript">
        $(document).ready(function () {

            $("#EmailMethod").css("display", "none");
            $("#TellMethod").css("display", "none");

            $("#regTypeEmail").change(function () {
                if ($("#regTypeEmail").is(":checked")) {
                    $("#EmailMethod").show();
                    $("#TellMethod").hide();
                }
            });

            $("#regTypeMobile").change(function () {
                if ($("#regTypeMobile").is(":checked")) {
                    $("#TellMethod").show();
                    $("#EmailMethod").hide();
                }
            });

            {if $smarty.session.post['search_type'] eq 1}$("#EmailMethod").show();{/if}
            {if $smarty.session.post['search_type'] eq 2}$("#TellMethod").show();{/if}


        });
    </script>

    <div class="well">
        <div class="row">
            <form method="post" action='{url urltype="path" url_string="gnr/X000/BeneficiariesLibrary/search/0/{$smarty.session.lang}/post"}'>

                <div class="col-lg-12">
                    <div class="radio">
                        <label>
                            <input type="radio" name="search_type" value="1" id="regTypeEmail" required="required" {if $smarty.session.post}{if $smarty.session.post['search_type'] eq 1}checked{/if}{else}checked{/if}>
                            <span class="text">{#gnr_email#} </span>
                        </label>
                    </div>

                    <div class="radio">
                        <label>
                            <input type="radio" name="search_type" value="2" id="regTypeMobile" required="required" {if $smarty.session.post['search_type'] eq 2}checked{/if}>
                            <span class="text">{#gnr_mobile_number#} </span>
                        </label>
                    </div>
                </div>

                <div id="EmailMethod">
                    <div class="col-lg-12"><input type="text" class="form-control" value="{if $smarty.session.post['search_type'] eq 1}{$smarty.session.post['email']}{/if}" name="email" placeholder="{#gnr_email#}"></div>
                </div>

                <div id="TellMethod">
                    <div class="col-lg-12"><input type="text" class="form-control" value="{if $smarty.session.post['search_type'] eq 2}{$smarty.session.post['tell']}{/if}" name="tell" placeholder="{#gnr_mobile_number#}"></div>
                </div>

                <div class="col-lg-12">
                    <div class="horizontal-space"></div>
                    <button type="submit" class="btn btn-info"><i class="fa fa-search"></i> {#gnr_search#}</button>
                </div>

            </form>
        </div>
    </div>
    
    {if $users}
        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
            <thead>
            <tr style="background-color: #A0D468 !important;" >
                <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                <th style="background-color: #A0D468 !important;" width="45%">{#gnr_name#}</th>
                <th style="background-color: #A0D468 !important;" width="25%">{#gnr_email#}/ {#gnr_mobile_number#}</th>
            </tr>

            </thead>
            <tbody>
            {$i=1}
            {foreach $users as $user}
                <tr {if $user->sh_user_activation_status eq 0}class="gray"{/if}>
                    <td align="center">{$i++}</td>
                    <td nowrap="nowrap">
                        <a data-toggle="modal" data-target="#modal" href="{url check=0 urltype="path" url_string="gnr/X000/resume/show/0/{$smarty.session.lang}/{$user->sh_user_id}"}" class="btn btn-default btn-sm">
                            <i class='fa fa-user'></i>
                        </a>
                        {$user->sh_user_full_name}
                    </td>
                    <td>{$user->sh_user_email} / {$user->sh_user_tell}</td>
                </tr>
            {/foreach}
            </tbody>
        </table>
    {/if}

    {if $smarty.session.s_users_class_id eq 'unclassified'}
        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
            <thead>
            <tr style="background-color: #A0D468 !important;" >
                <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                <th style="background-color: #A0D468 !important;" width="45%">{#gnr_name#}</th>
                <th style="background-color: #A0D468 !important;" width="25%">{#gnr_email#}/ {#gnr_mobile_number#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $unclassified_list as $xrow}
                <tr>
                    <td align="center">{$i++}</td>
                    <td nowrap="nowrap">
                        {$xrow->full_name}
                    </td>
                    <td>{$user->email} / {$user->tell}</td>
                </tr>
            {/foreach}
            </tbody>
        </table>
    {/if}


{/block}
{block name=page_header}
    <script type="text/javascript" src="/templates/assets/js/loader.js"></script>
    <!-- Chart Libraries -->
    <script src="/templates/assets/js/charts/morris/raphael-2.0.2.min.js"></script>
    <script src="/templates/assets/js/charts/morris/morris.js"></script>

    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
        function exportTo(ID, type) {
            $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
        }
        {/literal}
    </script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [

                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                    $("tfoot input").keyup(function() {
                        /* Filter on the column (the index) of this element */
                        oTable.fnFilter(this.value, $("tfoot input").index(this));
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}
