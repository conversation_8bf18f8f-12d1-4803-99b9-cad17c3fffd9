{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        {$user->full_name}
    </div>
    <div class="modal-body">
        {url check=0 urltype="alinkn" url_string="gnr/X000/mediacenter/userIncomePrint/0/{$smarty.session.lang}/{$user->id}" text_value="<i class='fa fa-print black'></i>&nbsp;{#gnr_print#}" style="btn btn-default"}
        <div class="row">
            <div class="col-lg-12">

                <h5 class="row-title before-blue">
                    <i class="glyphicon glyphicon-list-alt blue"></i>{#p_income_data#}
                </h5>

                <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">

                    <thead>
                    <tr>
                        <th width="5%">&nbsp;</th>
                        <th width="20%" data-priority="1">{#p_income_type#}</th>
                        <th width="15%" data-priority="1">{#p_income_value#}</th>
                        <th width="15%" data-priority="1">{#p_income_status#}</th>
                        <th width="15%" data-priority="1">{#gnr_from#}</th>
                        <th width="15%" data-priority="1">{#gnr_to#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $db_inc_list as $row}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>{t v=$row->work_type}</td>
                            <td align="center">{$row->monthly_incole_value}</td>
                            <td align="center">{t v=$row->monthly_income_type}</td>
                            <td align="center">{if $row->monthly_income_type eq 366}{getdate table=sh_user col=monthly_incole_vfrom type=show row=$row}{/if}</td>
                            <td align="center">{if $row->monthly_income_type eq 366}{getdate table=sh_user col=monthly_incole_vto type=show row=$row}{/if}</td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>

                <br>

                <h5 class="row-title before-blue">
                    <i class="glyphicon glyphicon-list-alt blue"></i>{#p_loan_data#}
                </h5>

                <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
                    <thead>
                    <tr>
                        <th width="5%">&nbsp;</th>
                        <th width="10%">{#gnr_type#}</th>
                        <th width="10%">{#p_loan_type#}</th>
                        <th width="10%">{#p_installment_type#}</th>
                        <th width="10%">{#p_loan_value#}</th>
                        <th width="10%">{#p_installment_value#}</th>
                        <th width="15%">{#gnr_comment#}</th>
                        <th width="10%">{#gnr_from#}</th>
                        <th width="10%">{#gnr_to#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $db_debt_list as $debt}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td class="text-center">{t v=$debt->type}</td>
                            <td class="text-center">{t v=$debt->loan_type}</td>
                            <td class="text-center">{t v=$debt->installment_type}</td>
                            <td class="text-center">{$debt->loan_value}</td>
                            <td class="text-center">{$debt->installment_value}</td>
                            <td class="text-center">{$debt->comment}</td>
                            <td class="text-center">{getdate table=db_debt col=from_date type=show row=$debt}</td>
                            <td class="text-center">{getdate table=db_debt col=to_date type=show row=$debt}</td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>

                <br>

                <h5 class="row-title before-blue">
                    <i class="glyphicon glyphicon-list-alt blue"></i>{#p_rent_data#}
                </h5>

                <table id="snsotable-1"
                       class="table table-hover table-striped table-bordered table-condensed">
                    <thead>
                    <tr>
                        <th width="5%">&nbsp;</th>
                        <th width="20%" data-priority="1">{#gnr_name#}</th>
                        <th width="15%" data-priority="1">{#p_house_type#}</th>
                        <th width="25%" data-priority="1">{#p_building_type#}</th>
                        <th width="10%" data-priority="1">{#p_house_space#}</th>
                        <th width="10%" data-priority="1">{#p_rent_value#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $houses_list as $row}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td align="center">{$row->name}</td>
                            <td>{t v=$row->type}</td>
                            <td align="center">{t v=$row->building_type}
                                {if $row->type eq 368}
                                    <br>
                                    <p class="small" style="color: #828282;">{#gnr_from#}: {getdate type=show row=$row col=contract_from} - {#gnr_to#}: {getdate type=show row=$row col=contract_to} </p>
                                {/if}
                            </td>
                            <td align="center">{t v=$row->space}</td>
                            <td align="center">{$row->rent_amount}</td>
                        </tr>
                    {/foreach}
                    </tbody>

                </table>


            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}