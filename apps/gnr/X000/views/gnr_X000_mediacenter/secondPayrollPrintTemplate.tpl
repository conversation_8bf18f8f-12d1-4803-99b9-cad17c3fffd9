{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}

{block name=body}
    <style>
        .payroll-container {
            margin: 0 !important;
            margin-top: 2cm !important;
            padding: 0 !important;
            width: 100% !important;
            box-sizing: border-box;
            font-family: 'IBM Plex Sans Arabic', 'IBM Plex Sans', Arial, sans-serif;
        }

        .payroll-title {
            font-size: 32px;
            color: #1a3c34;
            margin: 20px 0;
            text-align: center;
        }

        .payroll-subtitle {
            font-size: 20px;
            color: #000000;
            margin-bottom: 10px;
            text-align: center;
        }

        .payroll-period {
            font-size: 16px;
            color: #1a3c34;
            margin-bottom: 10px;
            text-align: center;
            font-weight: 500;
        }

        .payroll-datetime {
            font-size: 16px;
            color: #1a3c34;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 500;
        }

        .payroll-table {
            width: 100% !important;
            table-layout: fixed;
            border: 2px solid #4CAF50;
            background-color: #fff;
            font-size: 18px;
        }

        .payroll-table th,
        .payroll-table td {
            padding: 14px 10px;
            border: 1px solid #999;
            text-align: center;
            white-space: nowrap;
            color: #000000;
        }

        .payroll-table th {
            background-color: #4CAF50;
            color: #fff;
            font-weight: bold;
            padding: 16px 10px;
        }

        .payroll-table tbody tr:nth-child(even) {
            background-color: #f9fcf9;
        }

        .payroll-table tfoot td {
            font-weight: 700;
            background-color: #e6f3e6;
            padding: 14px 10px;
            color: #1a3c34;
        }

        .view-details-btn,
        .print-btn {
            display: none !important;
        }

        table {
            page-break-inside: auto;
        }

        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }

        .signatures-table {
            margin-top: 2cm;
            border: none;
        }

        .signature-title {
            font-size: 14pt;
            font-weight: bold;
        }

        .signature-name {
            font-size: 13pt;
        }
    </style>

    <div class="payroll-container">
        <p style="text-align: center; font-family: 'IBM Plex Sans Arabic', 'IBM Plex Sans', Arial, sans-serif; font-size: 14px; margin: 10px 0;">
            <strong style="font-size: 20px; font-weight: bold;">{$batch->name|default:'غير محدد'}</strong> |
            {if $unit}
                <strong style="font-size: 20px; font-weight: bold;">الوحدة: {$unit->name|default:'غير محدد'}</strong>
            {/if}
            {if $template}
                <strong style="font-size: 20px; font-weight: bold;">القالب: {$template->name|default:'غير محدد'}</strong>
            {/if}

            <br/>
            <br/>
            | الفترة: من {$batch->payroll_batch_from_date|date_format:"%Y-%m-%d"} إلى {$batch->payroll_batch_to_date|date_format:"%Y-%m-%d"}
            | تاريخ الطباعة: {date('Y-m-d H:i')|replace:'January':'يناير'|replace:'February':'فبراير'|replace:'March':'مارس'|replace:'April':'أبريل'|replace:'May':'مايو'|replace:'June':'يونيو'|replace:'July':'يوليو'|replace:'August':'أغسطس'|replace:'September':'سبتمبر'|replace:'October':'أكتوبر'|replace:'November':'نوفمبر'|replace:'December':'ديسمبر'}
        </p>

        <!-- حساب الإجماليات -->
        {assign var="totalBasic" value=0}
        {assign var="totalHousingAllowance" value=0}
        {assign var="totalOtherAllowances" value=0}
        {assign var="totalExtraAdditions" value=0}
        {assign var="totalAllAllowances" value=0}
        {assign var="totalInsuranceDeduction" value=0}
        {assign var="totalOtherDeductions" value=0}
        {assign var="totalAbsenceDeduction" value=0}
        {assign var="totalAllDeductions" value=0}
        {assign var="totalNet" value=0}

        {foreach from=$payrollEmployees item=payrollEmployee}
            {if $payrollEmployee->prl_trans_confirm_status neq 872}
                {* حساب الراتب الأساسي *}
                {assign var="basicSalary" value=$payrollEmployee->prl_trans_basic_salary|default:0}
                {assign var="totalBasic" value=$totalBasic + $basicSalary}
                
                {* حساب البدلات *}
                {assign var="housingAllowance" value=0}
                {assign var="otherAllowances" value=0}
                {assign var="extraAdditions" value=$payrollEmployee->prl_trans_extra_addition|default:0}
                
                {if isset($payrollEmployee->allowancesDetails) && is_array($payrollEmployee->allowancesDetails)}
                    {foreach from=$payrollEmployee->allowancesDetails item=allowanceValue key=allowanceName}
                        {if $allowanceName == "بدل السكن"}
                            {assign var="housingAllowance" value=$allowanceValue}
                            {assign var="totalHousingAllowance" value=$totalHousingAllowance + $allowanceValue}
                        {else}
                            {assign var="otherAllowances" value=$otherAllowances + $allowanceValue}
                            {assign var="totalOtherAllowances" value=$totalOtherAllowances + $allowanceValue}
                        {/if}
                    {/foreach}
                {/if}
                
                {assign var="allAllowances" value=$housingAllowance + $otherAllowances + $extraAdditions}
                {assign var="totalAllAllowances" value=$totalAllAllowances + $allAllowances}
                {assign var="totalExtraAdditions" value=$totalExtraAdditions + $extraAdditions}
                
                {* حساب الخصومات *}
                {assign var="insuranceDeduction" value=0}
                {assign var="otherDeductions" value=0}
                {assign var="absenceDeduction" value=0}
                
                {if isset($payrollEmployee->deductionDetails) && is_array($payrollEmployee->deductionDetails)}
                    {foreach from=$payrollEmployee->deductionDetails item=deductionValue key=deductionName}
                        {if $deductionName == "خصم التأمينات الاجتماعية"}
                            {assign var="insuranceDeduction" value=$deductionValue}
                            {assign var="totalInsuranceDeduction" value=$totalInsuranceDeduction + $deductionValue}
                        {else}
                            {assign var="otherDeductions" value=$otherDeductions + $deductionValue}
                            {assign var="totalOtherDeductions" value=$totalOtherDeductions + $deductionValue}
                        {/if}
                    {/foreach}
                {/if}
                
                {* خصومات إضافية من extraValueWithDetails *}
                {if isset($payrollEmployee->extraValueWithDetails) && is_array($payrollEmployee->extraValueWithDetails)}
                    {foreach from=$payrollEmployee->extraValueWithDetails item=deduction}
                        {if $deduction['is_absent'] eq 0}
                            {assign var="deductionValueFloat" value=$deduction['value']|floatval}
                            {assign var="otherDeductions" value=$otherDeductions + $deductionValueFloat}
                            {assign var="totalOtherDeductions" value=$totalOtherDeductions + $deductionValueFloat}
                        {/if}
                    {/foreach}
                {/if}
                
                {* خصم الغياب *}
                {assign var="extras" value=$payrollEmployee->prl_trans_extrastring|json_decode:1|default:[]}
                {if isset($extras['employeeAbsenceDays']) && $extras['employeeAbsenceDays'] > 0}
                    {assign var="dailyCost" value=($basicSalary + $housingAllowance - $insuranceDeduction) / 30}
                    {assign var="absenceDeduction" value=$dailyCost * $extras['employeeAbsenceDays']}
                    {assign var="totalAbsenceDeduction" value=$totalAbsenceDeduction + $absenceDeduction}
                {/if}
                
                {* إجمالي الخصومات *}
                {assign var="allDeductions" value=$insuranceDeduction + $otherDeductions + $absenceDeduction}
                {assign var="totalAllDeductions" value=$totalAllDeductions + $allDeductions}
                
                {* الراتب الصافي *}
                {assign var="netSalary" value=$basicSalary + $allAllowances - $allDeductions}
                {assign var="totalNet" value=$totalNet + $netSalary}
            {/if}
        {/foreach}

        <!-- عرض الجدول -->
        <table class="payroll-table">
            <thead>
                <tr>
                    <th width="5%">#</th>
                    <th width="10%">{#gnr_employee#}</th>
                    <th width="10%">{#gnr_job#}</th>
                    <th width="7%">{#gnr_basic#}</th>
                    <th width="7%">بدل السكن</th>
                    <th width="7%">بدلات أخرى</th>
                    <th width="7%">{#gnr_other_deductionAddition#}</th>
                    <th width="7%">إجمالي البدلات</th>
                    <th width="7%">خصم التأمينات</th>
                    <th width="7%">الغياب</th>
                    <th width="7%">خصومات أخرى</th>
                    <th width="7%">إجمالي الخصومات</th>
                    <th width="7%">{#gnr_net#}</th>
                </tr>
            </thead>
            <tbody>
                {assign var="i" value=1}
                {foreach from=$payrollEmployees item=payrollEmployee}
                    {if $payrollEmployee->prl_trans_confirm_status neq 872}
                        {assign var="basicSalary" value=0}
                        {assign var="housingAllowance" value=0}
                        {assign var="otherAllowances" value=0}
                        {assign var="extraAdditions" value=0}
                        {assign var="allAllowances" value=0}
                        {assign var="insuranceDeduction" value=0}
                        {assign var="otherDeductions" value=0}
                        {assign var="absenceDeduction" value=0}
                        {assign var="allDeductions" value=0}
                        {assign var="netSalary" value=0}

                        <tr>
                            <td align="center">{$payrollEmployee->att_device_num|default:'غير محدد'}</td>
                            <td align="center">{getname table=sh_user id=$payrollEmployee->prl_trans_user_id|default:'غير محدد'}</td>
                            <td align="center">{$payrollEmployee->job_name|default:'غير محدد'}</td>

                            <!-- Basic Salary -->
                            {assign var="basicSalary" value=$payrollEmployee->prl_trans_basic_salary|default:0}
                            <td align="center">{abs($basicSalary)|number_format:2:".":","}</td>

                            <!-- Housing Allowance -->
                            {assign var="allowancesDetails" value=$payrollEmployee->allowancesDetails|default:[]}
                            {foreach from=$allowancesDetails key=allowanceName item=allowanceValue}
                                {if $allowanceName == "بدل السكن"}
                                    {assign var="housingAllowance" value=$allowanceValue}
                                {/if}
                            {/foreach}
                            <td align="center">{$housingAllowance|number_format:2:".":","|default:'0.00'}</td>

                            <!-- Other Allowances -->
                            <td align="center">
                            {foreach from=$allowancesDetails key=allowanceName item=allowanceValue}
                                {if $allowanceName != "بدل السكن"}
                                    <div style="margin-bottom: 5px;">
                                        <div>{$allowanceName}</div>
                                        <div>{$allowanceValue|number_format:2:".":","}</div>
                                    </div>
                                    {assign var="otherAllowances" value=$otherAllowances + $allowanceValue}
                                {/if}
                            {/foreach}
                            {if empty($allowancesDetails) || ($otherAllowances == 0)}
                                {'0.00'|number_format:2:".":","}
                            {/if}
                            </td>

                            <!-- Extra Additions -->
                            {assign var="extraAdditions" value=$payrollEmployee->prl_trans_extra_addition|default:0}
                            <td align="center">{abs($extraAdditions)|number_format:2:".":","}</td>

                            <!-- Total Allowances -->
                            {assign var="allAllowances" value=$housingAllowance + $otherAllowances + $extraAdditions}
                            <td align="center">{abs($allAllowances)|number_format:2:".":","}</td>

                            <!-- Insurance Deduction -->
                            {assign var="deductionDetails" value=$payrollEmployee->deductionDetails|default:[]}
                            {foreach from=$deductionDetails key=deductionName item=deductionValue}
                                {if $deductionName == "خصم التأمينات الاجتماعية"}
                                    {assign var="insuranceDeduction" value=$deductionValue}
                                {/if}
                            {/foreach}
                            <td align="center">{$insuranceDeduction|number_format:2:".":","|default:'0.00'}</td>

                            <!-- Absence Deduction -->
                            <td align="center">
                                {assign var="extras" value=$payrollEmployee->prl_trans_extrastring|json_decode:1|default:[]}
                                {if isset($extras['employeeAbsenceDays']) && $extras['employeeAbsenceDays'] > 0}
                                    {assign var="dailyCost" value=($basicSalary + $housingAllowance - $insuranceDeduction) / 30}
                                    {assign var="absenceDeduction" value=$dailyCost * $extras['employeeAbsenceDays']}
                                    {$absenceDeduction|number_format:2:".":","}
                                    <br>
                                    <small class="text-danger">
                                        <i class="fa fa-calendar-times-o"></i> {$extras['employeeAbsenceDays']} أيام
                                    </small>
                                {else}
                                    {'0.00'|number_format:2:".":","}
                                    <br>
                                    <small class="text-success">
                                        <i class="fa fa-calendar-check-o"></i> 0 أيام
                                    </small>
                                {/if}
                            </td>

                            <!-- Other Deductions -->
                            <td align="center">
                                {assign var="otherDeductions" value=0}
                                {foreach from=$deductionDetails key=deductionName item=deductionValue}
                                    {if $deductionName != "خصم التأمينات الاجتماعية"}
                                        <div style="margin-bottom: 5px;">
                                            <div>{$deductionName}</div>
                                            <div>{$deductionValue|number_format:2:".":","}</div>
                                        </div>
                                        {assign var="otherDeductions" value=$otherDeductions + $deductionValue}
                                    {/if}
                                {/foreach}
                                
                                {if !empty($payrollEmployee->extraValueWithDetails)}
                                    {foreach from=$payrollEmployee->extraValueWithDetails item=deduction}
                                        {if $deduction['is_absent'] eq 0 && $deduction['inout'] eq 'out'}
                                            <div style="margin-bottom: 5px;">
                                                <div>{$deduction['desc']}</div>
                                                <div>{$deduction['value']|number_format:2:".":","}</div>
                                            </div>
                                            {assign var="otherDeductions" value=$otherDeductions + $deduction['value']}
                                        {/if}
                                    {/foreach}
                                {/if}
                                
                                {if $otherDeductions == 0}
                                    {'0.00'|number_format:2:".":","}
                                {/if}
                            </td>

                            <!-- Total Deductions -->
                            {assign var="allDeductions" value=$insuranceDeduction + $otherDeductions + $absenceDeduction}
                            <td align="center">{abs($allDeductions)|number_format:2:".":","}</td>

                            <!-- Net Salary -->
                            {assign var="netSalary" value=$basicSalary + $allAllowances - $allDeductions}
                            <td align="center">
                                <span class="{if $netSalary lt 0}text-red{/if}">
                                    {$netSalary|number_format:2:".":","}
                                </span>
                            </td>
                        </tr>
                    {/if}
                {/foreach}
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="3">المجموع</td>
                    <td>{abs($totalBasic)|number_format:2:".":","}</td>
                    <td>{abs($totalHousingAllowance)|number_format:2:".":","}</td>
                    <td>{abs($totalOtherAllowances)|number_format:2:".":","}</td>
                    <td>{abs($totalExtraAdditions)|number_format:2:".":","}</td>
                    <td>{abs($totalAllAllowances)|number_format:2:".":","}</td>
                    <td>{abs($totalInsuranceDeduction)|number_format:2:".":","}</td>
                    <td>{abs($totalAbsenceDeduction)|number_format:2:".":","}</td>
                    <td>{abs($totalOtherDeductions)|number_format:2:".":","}</td>
                    <td>{abs($totalAllDeductions)|number_format:2:".":","}</td>
                    <td>{abs($totalNet)|number_format:2:".":","}</td>
                </tr>
            </tfoot>
        </table>

        <!-- توقيعات -->
        <table class="signatures-table" width="100%">
            <tr>
                <td width="25%" align="center">
                    <div class="signature-title">مدير الموارد البشرية</div>
                    <div class="signature-name">سلطان بن عبد العزيز الفوزان</div>
                </td>
                <td width="25%" align="center">
                    <div class="signature-title">المحاسب</div>
                    <div class="signature-name">محمد بن عبد الله القحطاني</div>
                </td>
                <td width="25%" align="center">
                    <div class="signature-title">مدير إدارة الشؤون المالية والإدارية</div>
                    <div class="signature-name">بدر بن صالح العبد المنعم</div>
                </td>
                <td width="25%" align="center">
                    <div class="signature-title">المدير التنفيذي</div>
                    <div class="signature-name">فريح بن أحمد الفريح</div>
                </td>
            </tr>
        </table>
    </div>
{/block}