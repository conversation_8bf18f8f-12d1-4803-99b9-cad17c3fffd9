{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}

{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />
    <style>
        .table {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            border-collapse: collapse;
            width: 100%;
        }

        .table th,
        .table td {
            padding: 10px;
            text-align: center;
            border: 1px solid #ddd;
        }

        .table th {
            background-color: #A0D468 !important;
            color: white;
            font-weight: bold;
        }

        .table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .table tbody tr:hover {
            background-color: #e9ecef;
        }

        .table tfoot td {
            font-weight: bold;
            background-color: #f1f3f5;
            color: #2c3e50;
        }

        .success {
            color: #28a745;
        }

        .danger {
            color: #dc3545;
        }

        .text-gray-900 {
            color: #212529;
        }

        .text-red {
            color: #dc3545;
        }

        .allowance-details,
        .deduction-details {
            display: flex;
            flex-direction: column;
            gap: 6px;
            padding: 6px;
            font-size: 13px;
            line-height: 1.4;
            text-align: right;
        }

        .allowance-item,
        .deduction-item {
            display: flex;
            justify-content: space-between;
        }

        .allowance-name,
        .deduction-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .allowance-value,
        .deduction-value {
            color: #28a745;
            font-weight: 500;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            margin-bottom: 15px;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            margin-bottom: 15px;
        }

        .payroll-header {
            background-color: #f8f9fa;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .payroll-header h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .payroll-info {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }

        .payroll-info-item {
            flex: 1;
            text-align: center;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            margin: 0 5px;
        }

        .payroll-info-label {
            font-weight: bold;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .payroll-info-value {
            color: #2c3e50;
            font-size: 16px;
        }

        @media print {

            .allowance-details,
            .deduction-details {
                font-size: 11pt;
                padding: 5px;
            }

            .alert-success,
            .alert-error {
                display: none;
            }

            .payroll-header {
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
    </style>
{/block}

{block name=body}
    <!-- حساب الإجماليات -->
    {assign var="totalBasic" value=0}
    {assign var="totalHousingAllowance" value=0}
    {assign var="totalOtherAllowances" value=0}
    {assign var="totalExtraAdditions" value=0}
    {assign var="totalAllAllowances" value=0}
    {assign var="totalInsuranceDeduction" value=0}
    {assign var="totalOtherDeductions" value=0}
    {assign var="totalAbsenceDeduction" value=0}
    {assign var="totalAllDeductions" value=0}
    {assign var="totalNet" value=0}

    {foreach from=$payrollEmployees item=payrollEmployee}
        {if $payrollEmployee->prl_trans_confirm_status neq 872}
            {assign var="basicSalary" value=0}
            {assign var="housingAllowance" value=0}
            {assign var="otherAllowances" value=0}
            {assign var="extraAdditions" value=0}
            {assign var="allAllowances" value=0}
            {assign var="insuranceDeduction" value=0}
            {assign var="otherDeductions" value=0}
            {assign var="bankDeduction" value=0}
            {assign var="absenceDeduction" value=0}
            {assign var="allDeductions" value=0}
            {assign var="netSalary" value=0}

            <!-- الراتب الأساسي -->
            {assign var="basicSalary" value=$payrollEmployee->prl_trans_basic_salary|default:0}
            {assign var="totalBasic" value=$totalBasic + $basicSalary}

            <!-- البدلات -->
            {assign var="allowancesDetails" value=$payrollEmployee->allowancesDetails|default:[]}
            {foreach from=$allowancesDetails key=allowanceName item=allowanceValue}
                {if $allowanceName == "بدل السكن"}
                    {assign var="housingAllowance" value=$allowanceValue}
                    {assign var="totalHousingAllowance" value=$totalHousingAllowance + $housingAllowance}
                {else}
                    {assign var="otherAllowances" value=$otherAllowances + $allowanceValue}
                    {assign var="totalOtherAllowances" value=$totalOtherAllowances + $allowanceValue}
                {/if}
            {/foreach}

            <!-- الإضافات الأخرى -->
            {assign var="extraAdditions" value=$payrollEmployee->prl_trans_extra_addition|default:0}
            {assign var="totalExtraAdditions" value=$totalExtraAdditions + $extraAdditions}

            <!-- إجمالي البدلات -->
            {assign var="allAllowances" value=$housingAllowance + $otherAllowances + $extraAdditions}
            {assign var="totalAllAllowances" value=$totalAllAllowances + $allAllowances}

            <!-- خصم التأمينات -->
            {assign var="deductionDetails" value=$payrollEmployee->deductionDetails|default:[]}
            {foreach from=$deductionDetails key=deductionName item=deductionValue}
                {if $deductionName == "خصم التأمينات الاجتماعية"}
                    {assign var="insuranceDeduction" value=$deductionValue}
                    {assign var="totalInsuranceDeduction" value=$totalInsuranceDeduction + $insuranceDeduction}
                {else}
                    {assign var="otherDeductions" value=$otherDeductions + $deductionValue}
                    {assign var="totalOtherDeductions" value=$totalOtherDeductions + $deductionValue}
                {/if}
            {/foreach}

            <!-- خصم الغياب -->
            {assign var="extras" value=$payrollEmployee->prl_trans_extrastring|json_decode:1|default:[]}
            {if isset($extras['employeeAbsenceDays']) && $extras['employeeAbsenceDays'] > 0}
                {assign var="dailyCost" value=($basicSalary + $allAllowances - $insuranceDeduction - $bankDeduction) / 30}
                {assign var="absenceDeduction" value=$dailyCost * $extras['employeeAbsenceDays']}
                {assign var="totalAbsenceDeduction" value=$totalAbsenceDeduction + $absenceDeduction}
                {assign var="otherDeductions" value=$otherDeductions + $absenceDeduction}
                {assign var="totalOtherDeductions" value=$totalOtherDeductions + $absenceDeduction}
            {/if}

            <!-- خصومات إضافية -->
            {if !empty($payrollEmployee->extraValueWithDetails)}
                {foreach from=$payrollEmployee->extraValueWithDetails item=deduction}
                    {if $deduction['is_absent'] eq 0 && $deduction['inout'] eq 'out'}
                        {assign var="otherDeductions" value=$otherDeductions + $deduction['value']}
                        {assign var="totalOtherDeductions" value=$totalOtherDeductions + $deduction['value']}
                    {/if}
                {/foreach}
            {/if}

            <!-- مجموع الخصومات -->
            {assign var="allDeductions" value=$insuranceDeduction + $otherDeductions}
            {assign var="totalAllDeductions" value=$totalAllDeductions + $allDeductions}

            <!-- الراتب الصافي -->
            {assign var="netSalary" value=$basicSalary + $allAllowances - $allDeductions}
            {assign var="totalNet" value=$totalNet + $netSalary}
        {/if}
    {/foreach}

    <div class="payroll-header">
        <h3 class="text-center">{$batch->name}</h3>

        {if isset($template)}
            <div class="text-center">
                <strong>القالب:</strong> {$template->name}
            </div>
        {elseif isset($unit)}
            <div class="text-center">
                <strong>الوحدة:</strong> {$unit->name}
            </div>
        {/if}

        <div class="payroll-period text-center" style="margin: 15px 0;">
            <strong>الفترة:</strong> من {$batch->payroll_batch_from_date|date_format:"%Y-%m-%d"} إلى
            {$batch->payroll_batch_to_date|date_format:"%Y-%m-%d"}
        </div>

        <div class="payroll-info">
            <div class="payroll-info-item">
                <div class="payroll-info-label">عدد الموظفين</div>
                <div class="payroll-info-value">{count($payrollEmployees)}</div>
            </div>
            <div class="payroll-info-item">
                <div class="payroll-info-label">إجمالي الرواتب</div>
                <div class="payroll-info-value">{abs($totalNet)|number_format:2:".":","}</div>
            </div>
            <div class="payroll-info-item">
                <div class="payroll-info-label">إجمالي البدلات</div>
                <div class="payroll-info-value">{abs($totalAllAllowances)|number_format:2:".":","}</div>
            </div>
            <div class="payroll-info-item">
                <div class="payroll-info-label">إجمالي الخصومات</div>
                <div class="payroll-info-value">{abs($totalAllDeductions)|number_format:2:".":","}</div>
            </div>
        </div>
    </div>
    {if $payrollEmployees}
        <div class="text-left" style="margin-bottom: 20px;">
            {if $type eq 'template'}
                <a target="_blank" class="btn btn-default sharp"
                    href="{url urltype=path url_string="gnr/X000/mediacenter/secondPayrollPrintTemplate/0/ar/{$batch->id}/{$template->id}/template"
        }">
                    <i class="fa fa-print"></i> طباعة
                </a>
            {else}
                <a target="_blank" class="btn btn-default sharp"
                    href="{url urltype=path url_string="gnr/X000/mediacenter/secondPayrollPrintTemplate/0/ar/{$batch->id}/{$unit->id}/unit"
        }">
                    <i class="fa fa-print"></i> طباعة
                </a>
            {/if}

            <div class="btn-group">
                <a aria-expanded="false" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                    <i class="fa fa-share"></i> {#gnr_export#} <i class="fa fa-angle-down"></i>
                </a>
                <ul class="dropdown-menu">
                    <li><a href="#" onClick="exportTo('', 'excel')"><i class="fa fa-file-excel-o"></i> Excel</a></li>
                    <li><a href="#" onClick="exportTo('', 'csv')"><i class="fa fa-file-text-o"></i> CSV</a></li>
                    <li><a href="#" onClick="exportTo('', 'doc')"><i class="fa fa-file-word-o"></i> Word</a></li>
                    <li><a href="#" onClick="exportTo('', 'png')"><i class="fa fa-file-image-o"></i> PNG</a></li>
                </ul>
            </div>
        </div>
    {/if}

    <!-- عرض الجدول -->
    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
        <thead>
            <tr>
                <th width="5%">#</th>
                <th width="10%">{#gnr_employee#}</th>
                <th width="10%">{#gnr_job#}</th>
                <th width="7%">{#gnr_basic#}</th>
                <th width="7%">بدل السكن</th>
                <th width="7%">بدلات أخرى</th>
                <th width="7%">{#gnr_other_deductionAddition#}</th>
                <th width="7%">إجمالي البدلات</th>
                <th width="7%">خصم التأمينات</th>
                <th width="7%">الغياب</th>
                <th width="7%">خصومات أخرى</th>
                <th width="7%">إجمالي الخصومات</th>
                <th width="7%">{#gnr_net#}</th>
                <th width="10%">الحالة</th>
            </tr>
        </thead>
        <tbody>
            {$i=1}
            {foreach $payrollEmployees as $payrollEmployee}
                {if $payrollEmployee->prl_trans_confirm_status neq 872}
                    {assign var="basicSalary" value=0}
                    {assign var="housingAllowance" value=0}
                    {assign var="otherAllowances" value=0}
                    {assign var="extraAdditions" value=0}
                    {assign var="allAllowances" value=0}
                    {assign var="insuranceDeduction" value=0}
                    {assign var="otherDeductions" value=0}
                    {assign var="bankDeduction" value=0}
                    {assign var="absenceDeduction" value=0}
                    {assign var="allDeductions" value=0}
                    {assign var="netSalary" value=0}

                    <tr>
                        <td align="center">{$payrollEmployee->att_device_num|default:'غير محدد'}</td>
                        <td align="center">{getname table=sh_user id=$payrollEmployee->prl_trans_user_id|default:'غير محدد'}</td>
                        <td align="center">{$payrollEmployee->job_name|default:'غير محدد'}</td>

                        <!-- Basic Salary -->
                        {assign var="basicSalary" value=$payrollEmployee->prl_trans_basic_salary|default:0}
                        <td align="center">{abs($basicSalary)|number_format:2:".":","}</td>

                        <!-- Housing Allowance -->
                        {assign var="allowancesDetails" value=$payrollEmployee->allowancesDetails|default:[]}
                        {foreach from=$allowancesDetails key=allowanceName item=allowanceValue}
                            {if $allowanceName == "بدل السكن"}
                                {assign var="housingAllowance" value=$allowanceValue}
                            {else}
                                {assign var="otherAllowances" value=$otherAllowances + $allowanceValue}
                            {/if}
                        {/foreach}
                        <td align="center">{$housingAllowance|number_format:2:".":","|default:'0.00'}</td>

                        <!-- Other Allowances -->
                        <td align="center">
                            <!-- Button trigger modal -->
                            <button type="button" class="btn btn-light" data-toggle="modal" data-target="#allowanceModal{$i}">
                                {$otherAllowances|number_format:2:".":","}
                            </button>

                            <!-- Modal -->
                            <div class="modal fade" id="allowanceModal{$i}" tabindex="-1" role="dialog" 
                                aria-labelledby="allowanceModalLabel{$i}" >
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="allowanceModalLabel{$i}">تفاصيل البدلات الأخرى</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span >&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="allowance-details">
                                                {foreach from=$allowancesDetails key=allowanceName item=allowanceValue}
                                                    {if $allowanceName != "بدل السكن"}
                                                        <div class="border-bottom py-2" style="display: flex; justify-content: center; align-items: center;">
                                                            <div class="">
                                                                <span style="font-size: 18px; font-weight: bold;">{$allowanceName} : </span>
                                                            </div>
                                                            &nbsp;
                                                            &nbsp;
                                                            <div class="">
                                                                <span class="text-success" style="font-size: 18px; font-weight: bold;">
                                                                    {$allowanceValue|number_format:2:".":","|default:'0.00'}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    {/if}
                                                {/foreach}
                                                {if $otherAllowances == 0}
                                                    <div class="alert alert-info text-center" role="alert">
                                                        <i class="fa fa-info-circle"></i> لا توجد بدلات
                                                    </div>
                                                {/if}
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>

                        <!-- Extra Additions -->
                        {assign var="extraAdditions" value=$payrollEmployee->prl_trans_extra_addition|default:0}
                        <td align="center">
                            {abs($extraAdditions)|number_format:2:".":","}
                        </td>

                        <!-- Total Allowances -->
                        {assign var="allAllowances" value=$housingAllowance + $otherAllowances + $extraAdditions}
                        <td align="center">{abs($allAllowances)|number_format:2:".":","}</td>

                        <!-- Insurance Deduction -->
                        {assign var="deductionDetails" value=$payrollEmployee->deductionDetails|default:[]}
                        {foreach from=$deductionDetails key=deductionName item=deductionValue}
                            {if $deductionName == "خصم التأمينات الاجتماعية"}
                                {assign var="insuranceDeduction" value=$deductionValue}
                            {else}
                                {assign var="otherDeductions" value=$otherDeductions + $deductionValue}
                            {/if}
                        {/foreach}
                        <td align="center">{$insuranceDeduction|number_format:2:".":","|default:'0.00'}</td>

                        <!-- Absence Deduction -->
                        <td align="center">
                            {if isset($extras['employeeAbsenceDays']) && $extras['employeeAbsenceDays'] > 0}
                                {assign var="dailyCost" value=($basicSalary + $allAllowances - $insuranceDeduction - $bankDeduction) / 30}
                                {assign var="absenceDeduction" value=$dailyCost * $extras['employeeAbsenceDays']}
                                {$absenceDeduction|number_format:2:".":","}
                                <br><small>({$extras['employeeAbsenceDays']} أيام)</small>
                            {else}
                                {'0.00'|number_format:2:".":","}
                            {/if}
                        </td>

                        <!-- Other Deductions -->
                        <td>
                            <div class="deduction-details">
                                {assign var="otherDeductions" value=0}
                                {foreach from=$deductionDetails key=deductionName item=deductionValue}
                                    {if $deductionName != "خصم التأمينات الاجتماعية"}
                                        <div class="deduction-item">
                                            <span class="deduction-name">{$deductionName}:</span>
                                            <span class="deduction-value">{$deductionValue|number_format:2:".":","}</span>
                                        </div>
                                        {assign var="otherDeductions" value=$otherDeductions + $deductionValue}
                                    {/if}
                                {/foreach}
                                {if !empty($payrollEmployee->extraValueWithDetails)}
                                    {foreach from=$payrollEmployee->extraValueWithDetails item=deduction}
                                        {if $deduction['is_absent'] eq 0 && $deduction['inout'] eq 'out'}
                                            <div class="deduction-item">
                                                <span class="deduction-name">{$deduction['desc']}:</span>
                                                <span class="deduction-value">{$deduction['value']|number_format:2:".":","}</span>
                                            </div>
                                            {assign var="otherDeductions" value=$otherDeductions + $deduction['value']}
                                        {/if}
                                    {/foreach}
                                {/if}
                                {if $otherDeductions == 0}
                                    {'0.00'|number_format:2:".":","}
                                {/if}
                            </div>
                        </td>

                        <!-- Total Deductions -->
                        {assign var="allDeductions" value=$insuranceDeduction + $otherDeductions}
                        <td align="center">{abs($allDeductions)|number_format:2:".":","}</td>

                        <!-- Net Salary -->
                        {assign var="netSalary" value=$basicSalary + $allAllowances - $allDeductions}
                        <td align="center">
                            <span class="{if $netSalary lt 0}text-red{/if}">
                                {$netSalary|number_format:2:".":","}
                            </span>
                        </td>

                        <!-- Status -->
                        <td align="center">
                            {if $payrollEmployee->prl_trans_confirm_status eq 356}
                                <span class="success">
                                    <i class="fa fa-check-circle"></i> مدقق
                                </span>
                            {elseif $payrollEmployee->prl_trans_confirm_status eq 357}
                                <span class="danger">
                                    <i class="fa fa-times-circle"></i> غير مدقق
                                </span>
                            {elseif $payrollEmployee->prl_trans_confirm_status eq 872}
                                <span class="text-gray-900">
                                    <i class="fa fa-ban"></i> مستبعد
                                </span>
                            {/if}
                        </td>
                    </tr>
                {/if}
            {/foreach}
        </tbody>
        <tfoot>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td>{abs($totalBasic)|number_format:2:".":","}</td>
                <td>{abs($totalHousingAllowance)|number_format:2:".":","}</td>
                <td>{abs($totalOtherAllowances)|number_format:2:".":","}</td>
                <td>{abs($totalExtraAdditions)|number_format:2:".":","}</td>
                <td>{abs($totalAllAllowances)|number_format:2:".":","}</td>
                <td>{abs($totalInsuranceDeduction)|number_format:2:".":","}</td>
                <td>{abs($totalAbsenceDeduction)|number_format:2:".":","}</td>
                <td>{abs($totalOtherDeductions)|number_format:2:".":","}</td>
                <td>{abs($totalAllDeductions)|number_format:2:".":","}</td>
                <td>{abs($totalNet)|number_format:2:".":","}</td>
                <td></td>
            </tr>
        </tfoot>
    </table>

    <!-- جدول التصدير المخفي -->
    <table id="table" style="display: none">
        <thead>
            <tr>
                <th width="5%">رقم البصمة</th>
                <th width="10%">{#gnr_employee#}</th>
                <th width="10%">{#gnr_job#}</th>
                <th width="7%">{#gnr_basic#}</th>
                <th width="7%">بدل السكن</th>
                <th width="7%">بدلات أخرى</th>
                <th width="7%">{#gnr_other_deductionAddition#}</th>
                <th width="7%">إجمالي البدلات</th>
                <th width="7%">خصم التأمينات</th>
                <th width="7%">الغياب</th>
                <th width="7%">خصومات أخرى</th>
                <th width="7%">إجمالي الخصومات</th>
                <th width="7%">{#gnr_net#}</th>
                <th width="10%">الحالة</th>
            </tr>
        </thead>
        <tbody>
            {$i=1}
            {foreach $payrollEmployees as $payrollEmployee}
                {if $payrollEmployee->prl_trans_confirm_status neq 872}
                    {assign var="basicSalary" value=0}
                    {assign var="housingAllowance" value=0}
                    {assign var="otherAllowances" value=0}
                    {assign var="extraAdditions" value=0}
                    {assign var="allAllowances" value=0}
                    {assign var="insuranceDeduction" value=0}
                    {assign var="otherDeductions" value=0}
                    {assign var="bankDeduction" value=0}
                    {assign var="absenceDeduction" value=0}
                    {assign var="allDeductions" value=0}
                    {assign var="netSalary" value=0}

                    <tr>
                        <td align="center">{$payrollEmployee->att_device_num|default:'غير محدد'}</td>
                        <td align="center">{getname table=sh_user id=$payrollEmployee->prl_trans_user_id|default:'غير محدد'}</td>
                        <td align="center">{$payrollEmployee->job_name|default:'غير محدد'}</td>

                        <!-- Basic Salary -->
                        {assign var="basicSalary" value=$payrollEmployee->prl_trans_basic_salary|default:0}
                        <td align="center">{abs($basicSalary)|number_format:2:".":","}</td>

                        <!-- Housing Allowance -->
                        {assign var="allowancesDetails" value=$payrollEmployee->allowancesDetails|default:[]}
                        {foreach from=$allowancesDetails key=allowanceName item=allowanceValue}
                            {if $allowanceName == "بدل السكن"}
                                {assign var="housingAllowance" value=$allowanceValue}
                            {else}
                                {assign var="otherAllowances" value=$otherAllowances + $allowanceValue}
                            {/if}
                        {/foreach}
                        <td align="center">{$housingAllowance|number_format:2:".":","|default:'0.00'}</td>

                        <!-- Other Allowances -->
                        <td>
                            {foreach from=$allowancesDetails key=allowanceName item=allowanceValue}
                                {if $allowanceName != "بدل السكن"}
                                    {$allowanceName}: {$allowanceValue|number_format:2:".":","}<br>
                                {/if}
                            {/foreach}
                            {if $otherAllowances == 0}
                                {'0.00'|number_format:2:".":","}
                            {/if}
                        </td>

                        <!-- Extra Additions -->
                        {assign var="extraAdditions" value=$payrollEmployee->prl_trans_extra_addition|default:0}
                        <td align="center">{abs($extraAdditions)|number_format:2:".":","}</td>

                        <!-- Total Allowances -->
                        {assign var="allAllowances" value=$housingAllowance + $otherAllowances + $extraAdditions}
                        <td align="center">{abs($allAllowances)|number_format:2:".":","}</td>

                        <!-- Insurance Deduction -->
                        {assign var="deductionDetails" value=$payrollEmployee->deductionDetails|default:[]}
                        {foreach from=$deductionDetails key=deductionName item=deductionValue}
                            {if $deductionName == "خصم التأمينات الاجتماعية"}
                                {assign var="insuranceDeduction" value=$deductionValue}
                            {else}
                                {assign var="otherDeductions" value=$otherDeductions + $deductionValue}
                            {/if}
                        {/foreach}
                        <td align="center">{$insuranceDeduction|number_format:2:".":","|default:'0.00'}</td>

                        <!-- Absence Deduction -->
                        <td align="center">
                            {if isset($extras['employeeAbsenceDays']) && $extras['employeeAbsenceDays'] > 0}
                                {assign var="dailyCost" value=($basicSalary + $allAllowances - $insuranceDeduction - $bankDeduction) / 30}
                                {assign var="absenceDeduction" value=$dailyCost * $extras['employeeAbsenceDays']}
                                {$absenceDeduction|number_format:2:".":","}
                            {else}
                                {'0.00'|number_format:2:".":","}
                            {/if}
                        </td>

                        <!-- Other Deductions -->
                        <td>
                            {foreach from=$deductionDetails key=deductionName item=deductionValue}
                                {if $deductionName != "خصم التأمينات الاجتماعية"}
                                    {$deductionName}: {$deductionValue|number_format:2:".":","}<br>
                                {/if}
                            {/foreach}
                            {if !empty($payrollEmployee->extraValueWithDetails)}
                                {foreach from=$payrollEmployee->extraValueWithDetails item=deduction}
                                    {if $deduction['is_absent'] eq 0 && $deduction['inout'] eq 'out'}
                                        {$deduction['desc']}: {$deduction['value']|number_format:2:".":","}<br>
                                    {/if}
                                {/foreach}
                            {/if}
                            {if $otherDeductions == 0}
                                {'0.00'|number_format:2:".":","}
                            {/if}
                        </td>

                        <!-- Total Deductions -->
                        {assign var="allDeductions" value=$insuranceDeduction + $otherDeductions}
                        <td align="center">{abs($allDeductions)|number_format:2:".":","}</td>

                        <!-- Net Salary -->
                        {assign var="netSalary" value=$basicSalary + $allAllowances - $allDeductions}
                        <td align="center">
                            <span class="{if $netSalary lt 0}text-red{/if}">
                                {$netSalary|number_format:2:".":","}
                            </span>
                        </td>

                        <!-- Status -->
                        <td align="center">
                            {if $payrollEmployee->prl_trans_confirm_status eq 356}مدقق{/if}
                            {if $payrollEmployee->prl_trans_confirm_status eq 357}غير مدقق{/if}
                            {if $payrollEmployee->prl_trans_confirm_status eq 872}مستبعد{/if}
                        </td>
                    </tr>
                {/if}
            {/foreach}
        </tbody>
        <tfoot>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td>{abs($totalBasic)|number_format:2:".":","}</td>
                <td>{abs($totalHousingAllowance)|number_format:2:".":","}</td>
                <td>{abs($totalOtherAllowances)|number_format:2:".":","}</td>
                <td>{abs($totalExtraAdditions)|number_format:2:".":","}</td>
                <td>{abs($totalAllAllowances)|number_format:2:".":","}</td>
                <td>{abs($totalInsuranceDeduction)|number_format:2:".":","}</td>
                <td>{abs($totalAbsenceDeduction)|number_format:2:".":","}</td>
                <td>{abs($totalOtherDeductions)|number_format:2:".":","}</td>
                <td>{abs($totalAllDeductions)|number_format:2:".":","}</td>
                <td>{abs($totalNet)|number_format:2:".":","}</td>
                <td></td>
            </tr>
        </tfoot>
    </table>
{/block}

{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
            function exportTo(ID, type) {
                $('#table' + ID).css('display', '').tableExport({type: type, escape: 'false'});
                $('#table' + ID).css('display', 'none');
            }

            var InitiateSimpleDataTable = function() {
                return {
                    init: function() {
                        $('.sortable-table').dataTable({
                            "destroy": true,
                            "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                            "iDisplayLength": 50,
                            "oTableTools": {
                                "aButtons": [],
                                "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                            },
                            "language": {
                                "search": "",
                                "sLengthMenu": "_MENU_",
                                "oPaginate": {
                                    "sPrevious": "{#gnr_previous#}",
                                    "sNext": "{#gnr_next#}"
                                }
                            }
                        });
                    }
                };
            }();

            $(document).ready(function() {
                InitiateSimpleDataTable.init();
            });
        {/literal}
    </script>
{/block}