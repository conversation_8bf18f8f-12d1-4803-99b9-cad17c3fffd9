{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        {AssistanceFamilies::getFamilyName($visit->family_id)}
    </div>
    <div class="modal-body">

        <div class="row">
            <div class="col-lg-12">
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{AssistanceFamilies::getFamilyName($visit->family_id)}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_file_number#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$visit->familyObject->id}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_execution_status#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$visit->status}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_date#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=assist_visit col=date type=show row=$visit}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_recommendations#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$visit->recommendations}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_comment#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$visit->comment}</div>
            </div>
        </div>



    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}