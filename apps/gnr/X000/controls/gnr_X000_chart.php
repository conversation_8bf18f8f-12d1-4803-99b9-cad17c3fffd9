<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_chart extends Controller
{

    public function graphDiagram($parm, $post, $files)
    {

        try{

            $graph = Graph::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('graphRow', $graph);

        }catch (GraphException $e){

        }

        if($graph->id){

            // Diagram
            $x = Graph::graphDiagramArray((int)$graph->id);
            if($x){

                $this->Smarty->assign('temp', $this->Helper->preparePlottableData('diagram', [
                    'id' => 'theID',
                    'name' => 'theName',
                    'title' => 'theTitle',
                    'desc' => 'theDesc',
                    'parent' => 'theParent',
                    'class' => 'class'
                ], $x));

                $this->Smarty->assign('rand', md5(rand(0000, 9999)));
            }

            // Questionnaire

            $this->Smarty->assign('steps', Step::getGraphSteps((int)$graph->id,[1,2,3]));

        }

    }

    public function menuDiagram($parm, $post, $files)
    {
        $secId = 0;
        switch ($parm[0]) {

            case 'opr':

                $oprRow = Operation::readID((int) $parm[1] ?? 0);
                $secId = $oprRow->sec_id;

                break;

            case 'sec':

                $secId = $parm[1] ?? 0;

                break;

        }

        $secRow = Menu::readID($secId);
        $rand1 = rand(000000, 999999);
        $i = 0;
        $x = array();
        $x[$i]['theID'] = $rand1 . $secRow->id;
        $x[$i]['theName'] = $secRow->translatedName;
        $x[$i]['theTitle'] = '';
        $x[$i]['theDesc'] = '';
        $x[$i]['class'] = "bg-red white";
        $i++;

        try{

            $oprTopList = Operation::read([
                Operation::SEC_ID => $secId,
                Operation::HIRARCHICAL_TYPE => 940
            ],[0=>['property'=>Operation::ORDER,'sort'=>'ASC']]);

            foreach ($oprTopList as $tkey => $topOpr) {
                $x[$i]['theID'] = $topOpr->id;
                $x[$i]['theName'] = $topOpr->translatedName;
                $x[$i]['theTitle'] = '';
                $x[$i]['theDesc'] = '';
                $x[$i]['theParent'] = $rand1 . $secRow->id;
                switch ($parm[0]) {
                    case 'opr':
                        if ($topOpr->id == $parm[1]) {
                            $x[$i]['class'] = "bg-info white";
                        } else {
                            $x[$i]['class'] = "bg-white black";
                        }
                        break;
                    case 'sec':
                        $x[$i]['class'] = "bg-white black";
                        break;
                }
                $i++;
            }

        }catch (OperationException $e){

        }

        try{

            $oprList = Operation::read([
                Operation::SEC_ID => $secId,
                Operation::HIRARCHICAL_TYPE => 941
            ],[0=>['property'=>Operation::ORDER,'sort'=>'ASC']]);

            foreach ($oprList as $key => $opr) {

                if($opr->hirarchical_prior_opr_id != 0){

                    $x[$i]['theID'] = $opr->id;
                    $x[$i]['theName'] = $opr->translatedName;
                    $x[$i]['theTitle'] = '';
                    $x[$i]['theDesc'] = '';
                    $x[$i]['theParent'] = $opr->hirarchical_prior_opr_id;
                    switch ($parm[0]) {
                        case 'opr':
                            if ($opr->id == $parm[1]) {
                                $x[$i]['class'] = "bg-info white";
                            } else {
                                $x[$i]['class'] = "bg-white black";
                            }
                            break;
                        case 'sec':
                            $x[$i]['class'] = "bg-white black";
                            break;
                    }
                    $i++;

                }

            }

        }catch (OperationException $e){

        }

        $this->Smarty->assign('secRow', $secRow);
        $this->Smarty->assign('rand', md5(rand(0000, 9999)));
        $this->Smarty->assign('temp', $this->Helper->preparePlottableData('diagram', [
            'id' => 'theID',
            'name' => 'theName',
            'title' => 'theTitle',
            'desc' => 'theDesc',
            'parent' => 'theParent',
            'class' => 'class'
        ], $x));
    }

    public function prgDiagram($parm, $post, $files)
    {

        $prgRow = Program::readID((int)$parm[0] ?? 0);

        $i = 0;
        $rand1 = rand(000000, 999999);
        $x = array();
        $x[$i]['theID'] = $rand1 . $prgRow->id;
        $x[$i]['theName'] = $prgRow->translatedName;
        $x[$i]['theTitle'] = '';
        $x[$i]['theDesc'] = '';
        $x[$i]['class'] = "bg-red white";
        $i++;


        try{

            $secList = Menu::read([
                Menu::PRG_ID => $prgRow->id
            ],[0=>['property'=>Menu::ORDER,'sort'=>'ASC']]);

            foreach ($secList as $secRow) {

                $rand2 = rand(000000, 999999);
                $x[$i]['theID'] = $rand2 . $secRow->id;
                $x[$i]['theName'] = $secRow->translatedName;
                $x[$i]['theTitle'] = '';
                $x[$i]['theDesc'] = '';
                $x[$i]['theParent'] = $rand1 . $prgRow->id;
                $x[$i]['class'] = "bg-info black";
                $i++;

                try{

                    $oprTopList = Operation::read([
                        Operation::SEC_ID => $secRow->id,
                        Operation::HIRARCHICAL_TYPE => 940
                    ],[0=>['property'=>Operation::ORDER,'sort'=>'ASC']]);

                    foreach ($oprTopList as $tkey => $topOpr) {
                        $x[$i]['theID'] = $topOpr->id;
                        $x[$i]['theName'] = $topOpr->translatedName;
                        $x[$i]['theTitle'] = '';
                        $x[$i]['theDesc'] = '';
                        $x[$i]['theParent'] = $rand2 . $secRow->id;
                        switch ($parm[0]) {
                            case 'opr':
                                if ($topOpr->id == $parm[1]) {
                                    $x[$i]['class'] = "bg-info white";
                                } else {
                                    $x[$i]['class'] = "bg-white black";
                                }
                                break;
                            case 'sec':
                                $x[$i]['class'] = "bg-white black";
                                break;
                        }
                        $i++;
                    }

                }catch (OperationException $e){

                }

                $oprList = Operation::read([
                    Operation::SEC_ID => $secRow->id,
                    Operation::HIRARCHICAL_TYPE => 941
                ],[0=>['property'=>Operation::ORDER,'sort'=>'ASC']]);

                foreach ($oprList as $key => $opr) {

                    if($opr->hirarchical_prior_opr_id != 0){

                        $x[$i]['theID'] = $opr->id;
                        $x[$i]['theName'] = $opr->translatedName;
                        $x[$i]['theTitle'] = '';
                        $x[$i]['theDesc'] = '';
                        $x[$i]['theParent'] = $opr->hirarchical_prior_opr_id;
                        switch ($parm[0]) {
                            case 'opr':
                                if ($opr['sh_opr_id'] == $parm[1]) {
                                    $x[$i]['class'] = "bg-info white";
                                } else {
                                    $x[$i]['class'] = "bg-white black";
                                }
                                break;
                            case 'sec':
                                $x[$i]['class'] = "bg-white black";
                                break;
                        }
                        $i++;

                    }

                }

            }

            $this->Smarty->assign('temp', $this->Helper->preparePlottableData('diagram', [
                'id' => 'theID',
                'name' => 'theName',
                'title' => 'theTitle',
                'desc' => 'theDesc',
                'parent' => 'theParent',
                'class' => 'class'
            ], $x));
            $this->Smarty->assign('prgRow', $prgRow);
            $this->Smarty->assign('rand', md5(rand(0000, 9999)));

        }catch (MenuException $e){

        }

    }

}
