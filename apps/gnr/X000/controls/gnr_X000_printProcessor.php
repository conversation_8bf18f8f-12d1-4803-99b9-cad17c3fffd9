<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_printProcessor extends Controller
{

    public function show($parm, $post)
    {
        /*
         * Requirements:
         * 1- Base template
         * 2- operation code
         * 3- default print method
         * 4- default print parameters as url encoded
         */

        $hasDefault = false;
        $canPrint = true;

        try {
            $operation = Operation::readByCode((string)$parm[1]);

            $hasDefault = !empty($parm[2]);
            $this->Smarty->assign('printValuesOption', $_GET['printValuesOption'] ?? "");
            $this->Smarty->assign('defaultMethod', $parm[2] ?? "");
            $this->Smarty->assign('defaultMethodParameters', $_GET['defaultMethodParameters'] ?? "");

        } catch (OperationException $e) {
            $operation = null;
            $canPrint = false;
        }

        try {
            $basePrintTemplate = BasePrintTemplate::readByCode((string)$parm[0]);
            $printTemplates = PrintTemplate::usingBaseTemplate($basePrintTemplate->id);
            $fallBack = false;

            // send operation code, default and default's parameters

        } catch (BasePrintTemplateException $e) {
            // base template not found!
            $printTemplates = $basePrintTemplate = null;
            $fallBack = $canPrint && $hasDefault;
        } catch (PrintTemplateException $e) {
            // no templates defined for the selected
            $printTemplates = -1;
            $fallBack = $canPrint && $hasDefault;
        }

        $this->Smarty->assign('operation', $operation);
        $this->Smarty->assign('basePrintTemplate', $basePrintTemplate);
        $this->Smarty->assign('printTemplates', $printTemplates);
        $this->Smarty->assign('fallBack', $fallBack);
        $this->Smarty->assign('canPrint', $canPrint);
        $this->Smarty->assign('hasDefault', $hasDefault);
    }

    public function print($parm, $post)
    {
        if (strtolower($_SERVER['REQUEST_METHOD']) === "post" && $this->validPosts()) {
            // output the print here
            try {
                $operation = Operation::readByCode((string)$_POST['operationCode']);
            } catch (OperationException $e) {
                // can't proceed further
                return;
            }

            // check if selection was on the default template
            if ($_POST['template'] === 'default') {
                // use default behavior
                $this->defaultPrint($operation, $_POST['defaultMethod'], $_POST['defaultMethodParameters']);
                return;
            }

            // check the selected template is template and it requests a valid control
            if ($_POST['template'] !== 'template' || !$this->validControl($operation) || empty($_POST['printTemplate'])) {
                return;
            }
            $printValuesOption = "&printValuesOption=" . $_POST['printValuesOption'] ?? "";
            // redirect to the image
            $this->URL->redirect("gnr/X000/printProcessor/image/0/{$_SESSION['lang']}/{$_POST['printTemplate']}/{$operation->code}/{$printValuesOption}&background=" . $_POST['background'] ?? false,
                1, 1);
            return;

            // output the PDF
//            DocumentProcessor::preparePDF(
//                "<img src='" . SNSO_URL . "/index.php?gnr/X000/printProcessor/image/0/{$_SESSION['lang']}/{$_POST['printTemplate']}/{$operation->code}/&background=0'>"
//            )->Output();

        }
        // Default: use the operation to execute the print output while it need to has default or no fallback would fire
        // Template: get print values list
        try {
            $operation = Operation::readByCode((string)$parm[0]);
        } catch (OperationException $e) {
            return;
        }

        $this->defaultPrint($operation, $parm[1], $_GET['defaultMethodParameters'] ?? "");
    }

    public function image($parm, $post)
    {
        try {
            $operation = Operation::readByCode((string)$parm[1]);
        } catch (OperationException $e) {
            return;
        }
        if (!$this->validControl($operation)) {
            return;
        }

        $controlClassName = $this->getControlClassName($operation);

        global $snso;

        /**
         * @var $controlObject PrintableControlInterface
         */
        $controlObject = new $controlClassName($snso);

        try {
            $template = PrintTemplate::readID((int)$parm[0]);

        } catch (PrintTemplateException $e) {
            $template = null;
        }
        try {
            if($template){
                $image = $template->asImage($controlObject->printValues($_GET['printValuesOption'] ?? ""), null,(bool)$_GET['background'] ?? false);
                $image->output();
            }
        } catch (PrintTemplateException $e) {
        }catch (ErrorException $e){

        }

    }

    private function defaultPrint(Operation $operation, string $method, string $options = "")
    {
        $bundle = Bundle::readID((int)$operation->bnd_id);
        $program = Program::readID((int)$operation->prg_id);
        $options = base64_decode(urldecode($options));
        // redirect to the default as defined (if any)
        $this->URL->redirect("{$bundle->code}/{$program->code}/{$operation->code}/{$method}/0/{$_SESSION['lang']}/{$options}",
            1, 1);

    }

    private function validPosts()
    {
        return !empty($_POST['operationCode']) && !empty($_POST['template']);
    }

    private function validControl(Operation $operation)
    {
        /*
         * 1- check if class exists
         * 2- check it is a printControlInterface
         */

        $bundle = Bundle::readID((int)$operation->bnd_id);
        $program = Program::readID((int)$operation->prg_id);

        $controlClassName = "{$bundle->code}_{$program->code}_{$operation->code}";

        $classFile = new File(APP_DIR . DS . $bundle->code . DS . $program->code . DS . CONTROLS_DIR_NAME . DS . $controlClassName . ".php");
        return $classFile->requireOnce() && class_exists($controlClassName) && !empty(class_implements($controlClassName)["PrintableControlInterface"]);
    }

    private function getControlClassName(Operation $operation)
    {
        $bundle = Bundle::readID((int)$operation->bnd_id);
        $program = Program::readID((int)$operation->prg_id);

        return "{$bundle->code}_{$program->code}_{$operation->code}";
    }
}
