<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_service extends Controller
{

    public function show($parm, $post, $files)
    {

        switch ($parm[0]) {

            case 'save_session':
                $_SESSION['x_es_service_opr'] = $parm[1];  // Connected Operation
                $_SESSION['x_es_service_table'] = $parm[2];  // Service base table
                $_SESSION['x_es_service_row_id'] = $parm[3];  // Service base row id
                $_SESSION['x_es_service_type'] = $parm[4];  // Service base row id

                // Back path
                $_SESSION['x_es_bnd_code'] = $parm[5];  // bundle code
                $_SESSION['x_es_prg_code'] = $parm[6];  // program code
                $_SESSION['x_es_opr_code'] = $parm[7];  // operation code
                $_SESSION['x_es_sub_code'] = $parm[8];  // show , edit, delete ...
                $_SESSION['x_es_pagin'] = $parm[9];  // pagination
                $_SESSION['x_es_lang'] = $parm[10];  // Language

                global $snso;
                $_SESSION['x_es_prg_id'] = $snso->Database->get_id_from_code('sh_prg', $_SESSION['x_es_prg_code']);
                $_SESSION['x_es_opr_id'] = $snso->Database->get_id_from_code('sh_opr', $_SESSION['x_es_service_opr']);

                break;

            case 'insertProject':
                $service = new Service();
                $service->insertProject($post, $parm[1]);
                break;

            case 'updateServiceStatus':
                try {
                    $service = Service::readID($parm[1] ?? 0);
                    $service->status = $parm[2] ?? 0;
                    $service->save();
                } catch (ServiceException $e) {
                }
                break;

            case 'updateServiceData':
                if ($_SESSION['x_es_service_data_token'] === $parm[1]) {
                    try{
                        $service = Service::readID($parm[2]);
                        $service->bindProperties($post);
                        $service->intendedto = implode(',', $post['intendedto']);
                        $service->start_date = $this->Date->get_date('ad', $post['start_date']);
                        $service->end_date = $this->Date->get_date('ad', $post['end_date']);
                        $service->save();
                        $this->Smarty->assign('message', 'updateServiceData');
                    }catch (ServiceException $e){}
                }
                break;

            case 'updateServiceLicense':
                if ($_SESSION['x_es_service_data_token'] === $parm[1]) {
                    try {
                        $service = Service::readID($parm[2] ?? 0);
                        $service->conditions = $post['conditions'];
                        $service->save();
                    } catch (ServiceException $e) {
                    }
                    $this->Smarty->assign('message', 'updateServiceLicense');
                }
                break;

            case 'regdata':
                switch ($parm[1]) {
                    case 'update':
                        if ($_SESSION['x_es_service_data_token'] === $parm[2]) {

                            try {
                                if(es_reg_data::count([
                                    'es_reg_data_service_id' => $_SESSION['x_es_service_id'],
                                    'es_reg_data_class_id' => $parm[3]
                                ]) == 0){
                                    $regData = new ServiceRegistrationData();
                                    $regData->org_id = $_SESSION['organization']->id;
                                    $regData->created_by = $_SESSION['user']->id;
                                    $regData->service_id = $_SESSION['x_es_service_id'];
                                    $regData->class_id = $parm[3];
                                    $regData->data_ids = implode(",", $post[ServiceRegistrationData::DATA_IDS]);
                                    try{
                                        $regData->save();
                                    }catch (ServiceRegistrationDataException $e){}
                                }else{
                                    try{

                                        $regData = array_shift(ServiceRegistrationData::read([
                                            ServiceRegistrationData::SERVICE_ID => $_SESSION['x_es_service_id'],
                                            ServiceRegistrationData::CLASS_ID => $parm[3]]));
                                        /**
                                         * @var $regData ServiceRegistrationData;
                                         */
                                        $regData->data_ids = (string) implode(",", $post['es_reg_data_data_ids']);
                                        $regData->update();

                                    }catch (ServiceRegistrationDataException $e){}

                                }

                            } catch (ModelException $e) {

                            }

                            $_SESSION['s_userclass_active_tabe'] = $parm[3];
                            $_SESSION['s_user_data_and_doc_active_tab'] = 'data';
                            $_SESSION['x_es_service_data_token'] = md5(rand(0, 10000000));
                        }
                        break;
                }
                break;

            case 'regdoc':
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['x_es_service_data_token'] == $parm[2]) {
                            try {
                                $regDoc = new ServiceDocument();
                                $regDoc->org_id = $_SESSION['organization']->id;
                                $regDoc->created_by = $_SESSION['user']->id;
                                $regDoc->service_id = $_SESSION['x_es_service_id'];
                                $regDoc->name = $post['name'];
                                $regDoc->class_id = $parm[3];
                                $regDoc->save();
                            } catch (ServiceDocumentException $e) {
                            }

                            $_SESSION['s_userclass_active_tabe'] = $parm[3];
                            $_SESSION['s_user_data_and_doc_active_tab'] = 'doc';
                            $_SESSION['x_es_service_data_token'] = rand(10000, 19999);
                        }
                        break;
                    case 'update':
                        if ($_SESSION['x_es_service_data_token'] === $parm[3]) {
                            try {
                                $regDoc = ServiceDocument::readID($parm[3]);
                                $regDoc->name = $post['name'];
                                $regDoc->update();
                            } catch (ServiceDocumentException $e) {
                            }

                            $_SESSION['s_userclass_active_tabe'] = $parm[4];
                            $_SESSION['s_user_data_and_doc_active_tab'] = 'doc';
                            $_SESSION['x_es_service_data_token'] = rand(10000, 19999);
                        }
                        break;
                    case 'delete':
                        if ($_SESSION['x_es_service_data_token'] === $parm[3]) {
                            try {
                                $regDoc = ServiceDocument::readID($parm[2]);
                                $regDoc->delete();
                            } catch (ServiceDocumentException $e) {
                            }

                            $_SESSION['s_userclass_active_tabe'] = $parm[4];
                            $_SESSION['s_user_data_and_doc_active_tab'] = 'doc';
                            $_SESSION['x_es_service_data_token'] = rand(10000, 19999);
                        }
                        break;
                }
                break;

            case 'filterSelection':

                // the back url
                if (empty($_GET['backURL'])) {
                    break;
                }

                // validate access
                if (strtolower($_SERVER['REQUEST_METHOD']) !== 'post'
                    || empty($_POST['applicationStatusValue'])
                    || empty($_POST['applicationStatusValueUnset'])
                ) {

                    break;
                }

                // if the applications list is empty don't bother looping
                if (!empty($_POST['application'])) {

                    foreach ($_POST['application'] as $applicationID) {

                        // update the status regardless its current saved value
                        try {

                            $application = es_application::readByID($applicationID);


                            // assign depending on selection value
                            $application->es_application_status = (!empty($_POST['applicationCheck'][$applicationID])) ? $_POST['applicationStatusValue'] : $_POST['applicationStatusValueUnset'];
                            $application->save();

                        } catch (ModelException $e) {

                        }
                    }
                }

                $this->Smarty->assign('redirect', true);
                $this->URL->redirect("gnr/X000/service/show/0/{$_SESSION['lang']}/{$_GET['backURL']}");
                return;
                break;
        }

        $serviceObject = new Service();

        $service = $serviceObject->servicePound(
            $_SESSION['x_es_prg_id'],
            $_SESSION['x_es_opr_id'],
            $_SESSION['x_es_service_table'],
            $_SESSION['x_es_service_row_id'],
            $_SESSION['x_es_service_type']
        );
        $_SESSION['x_es_service_id'] = (int) $service->id;

        $intendedto = explode(',', $service->{Service::INTENDEDTO});
        $_SESSION['s_userclass_active_tabe'] = $intendedto[0];
        $_SESSION['s_user_data_and_doc_active_tab'] = 'data';



        try {
            $this->Smarty->assign('service', Service::readID($_SESSION['x_es_service_id'] ?? 0));
        } catch (ServiceException $e) {
            $this->Smarty->assign('service', []);
        }

        try {
            $this->Smarty->assign('User_Type_List', st_setting::simpleReadByProperty(array('st_setting_opr_id' => 167), 'st_setting_order', 'DESC', 0));
        } catch (ModelException $e) {
        }

        try {
            $this->Smarty->assign('User_Type_List2', sh_userclasses::simpleReadByProperty(['sh_userclasses_org_id' => $_SESSION['organization']->id]));
        } catch (ModelException $e) {
        }


        try {
            $this->Smarty->assign('sex_list', st_setting::simpleReadByProperty([
                'st_setting_opr_id' => 27
            ], 'st_setting_order'));
        } catch (ModelException $e) {
        }

        try {
            $this->Smarty->assign('applications', ServiceApplication::read([
                'es_application_org_id' => $_SESSION['organization']->id,
                'es_application_service_id' => $_SESSION['x_es_service_id']
            ]));
        } catch (ServiceApplicationException $e) {
            $this->Smarty->assign('applications', []);
        }

        $_SESSION['x_es_service_data_token'] = md5(rand(0, 1000000));
    }

    public function createService($parm, $post)
    {
        try{
            es_service::count([
                'es_service_table_name' => $parm[1] ?? 0,
                'es_service_table_row_id' => $parm[2] ?? 0
            ]);
            $this->Smarty->assign('serviceExist', 1);

        }catch (ModelException $e){

            if($e->getCode() === 2020){

                switch ($parm[1]) {
                    case 'sh_uao':
                        $this->Smarty->assign('serviceName', $this->DB->get_name('sh_uao', $parm[2] ?? 0));
                        break;
                }

                $this->Smarty->assign('parmArray', $parm);
                $this->Smarty->assign('serviceExist', 0);
            }

        }
    }

    public function status($parm, $post)
    {
        try{
            $this->Smarty->assign('row', Service::readID($_SESSION['x_es_service_id'] ?? 0));
            $this->Smarty->assign('status_list',st_setting::simpleReadByProperty(['st_setting_opr_id' => 171],'st_setting_order'));
        }catch (ModelException $e){

        }catch (ServiceException $e){

        }
    }

    public function regDocAdd($parm, $post)
    {
        $this->Smarty->assign("class_id", $parm[0]);
        $_SESSION['x_es_service_data_token'] = md5(rand(0, 1000000));
    }

    public function regDocEdit($parm, $post)
    {
        try{
            $this->Smarty->assign('row', ServiceRegistrationData::readID($parm[1] ?? 0));
            $this->Smarty->assign("class_id", $parm[0] ?? 0);
            $_SESSION['x_es_service_data_token'] = md5(rand(0, 1000000));
        }catch (ServiceRegistrationDataException $e){}
    }

    public function regDocConfirm($parm, $post)
    {
        try{
            $this->Smarty->assign('row', ServiceDocument::readID($parm[1] ?? 0));
            $this->Smarty->assign("class_id", $parm[0] ?? 0);
            $_SESSION['x_es_service_data_token'] = md5(rand(0, 1000000));
        }catch (ServiceDocumentException $e){}
    }

    public function viewServiceApplication($parm, $post)
    {
        $Service = new Service();
        // prevent any non legal access to application view.
        if (empty($_GET['applicationID'])) {
            exit();
        }

        try {
            $application = es_application::readByID($_GET['applicationID']);
        } catch (ModelException $e) {
            $application = new es_application();
        }

        // pass the application to view.
        $this->Smarty->assign('application', $application);

        $serviceFormName = $Service->getServiceFormName($application->es_application_service_id);

        // pass the service form name
        $this->Smarty->assign('serviceFormName', $serviceFormName);
    }
}
