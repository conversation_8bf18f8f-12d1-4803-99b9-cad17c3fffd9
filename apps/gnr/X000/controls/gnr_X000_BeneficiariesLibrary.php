<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

use SNSO\Core\HTTP;

// TODO Documentation Missing
class gnr_X000_BeneficiariesLibrary extends Controller
{

    public function createBeneficiary($parm)
    {

        $parm[] = 'createBeneficiary';

        try {

            $this->Smarty->assign('classifications', UserClass::read([UserClass::ORG_ID => $_SESSION['organization']->id]));

        } catch (UserClassException $e) {

            $this->Smarty->assign('classifications', []);
        }

        $this->Smarty->assign('users', User::getBeneficiariesForFinance());

        $this->Smarty->assign('sex_list', Setting::getList(27));

    }

    public function search($parm, $post)
    {
        switch ($parm[0]) {

            case 'clear':

                $_SESSION['post'] = false;

                break;

            case 'post':

                $_SESSION['post'] = $post;

                break;

        }

        if ($_SESSION['post']) {

            switch ($_SESSION['post']['search_type']) {


                case 1:

                    $this->Smarty->assign('users', DB::table(sh_user::class)->where(User::EMAIL, 'like', '%' . $_SESSION['post']['email'] . '%')->get());

                    break;

                case 2:

                    $this->Smarty->assign('users', DB::table(sh_user::class)->where(User::TELL, 'like', '%' . $_SESSION['post']['tell'] . '%')->get());

                    break;

                default:

                    $this->Smarty->assign('users', []);

                    break;

            }

        }

    }

}