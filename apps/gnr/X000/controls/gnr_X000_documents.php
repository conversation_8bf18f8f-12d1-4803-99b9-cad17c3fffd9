<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_documents extends Controller
{

    const CODE_FILE_TYPE_NOT_ALLOWED = 777002;

    const MESSAGE_GNR_FILE_TYPE_NOT_ALLOWED = 'gnr_file_type_not_allowed';

    public function show($parm, $post, $files)
    {

        switch ($parm[0]) {
            case 'save_session':

                $_SESSION['s_document_operation_code'] = $parm[1];
                $_SESSION['s_document_table_name'] = $parm[2];
                $_SESSION['s_document_row_id'] = $parm[3];
                $_SESSION['s_document_user_id'] = $parm[4];
                if ($parm[5]) {
                    $_SESSION['s_document_back_path'] = implode(
                        '/',
                        array_slice(explode('/', $this->URL->currentUrlPath()), 11)
                    );

                } else {
                    $_SESSION['s_document_back_path'] = null;
                }
                break;

            case 'insert':
                if ($_SESSION['s_documents_token'] === $parm[1]) {

                    try {

                        $document = new Document();
                        $post['permission'] =isset($post['permission']) ? json_encode($post['permission']):null;
                        $document->bindProperties($post);
                        $document->fileArray = $files['fileArray'];
                        $document->client_id = $_SESSION['organization']->id;
                        $document->user_id = $_SESSION['s_document_user_id'];
                        $document->operation_code = $_SESSION['s_document_operation_code'];
                        $document->table_name = $_SESSION['s_document_table_name'];
                        $document->row_id = $_SESSION['s_document_row_id'];
                        $document->created_by = $_SESSION['user']->id;
                        $document->created_date = date('Y-m-d');
                        if (empty($post['name'])) {
                            $documentName = explode('.', $files['fileArray']['name']);
                            $document->name = $documentName[0];
                        }
                        if ($document->save()) {
                            Notification::createdAlert();
                        }

                    } catch (DocumentException $e) {
                        /**
                         * @TODO handling other errors and display some feed back for end user
                         */
                        $feedback = ($e->getCode() === self::CODE_FILE_TYPE_NOT_ALLOWED) ? self::MESSAGE_GNR_FILE_TYPE_NOT_ALLOWED : '';
                        Notification::alertMessage(Notification::ERROR, $feedback);
                    }

                }

                break;

            case 'update':

                if ($_SESSION['s_documents_token'] === $parm[1]) {

                    try {

                        $document = Document::readID((int)$parm[2] ?? 0);
                        $post['permission'] =isset($post['permission']) ? json_encode($post['permission']):null;
                        $document->bindProperties($post);
                        $document->update();

                        Notification::updatedAlert();

                    } catch (DocumentException $e) {
                        /**
                         * @TODO handling other errors and display some feed back for end user
                         */
                        $feedback = ($e->getCode() === self::CODE_FILE_TYPE_NOT_ALLOWED) ? self::MESSAGE_GNR_FILE_TYPE_NOT_ALLOWED : '';
                        Notification::alertMessage(Notification::ERROR, $feedback);
                    }

                }

                break;

            case 'delete':

                if ($_SESSION['s_documents_token'] === $parm[1]) {

                    try {

                        $document = Document::readID((int)$parm[2] ?? 0);
                        $document->delete();

                        Notification::deletedAlert();

                    } catch (DocumentException $e) {

                    }
                }

                break;
        }



        $documents = \Models\Document::where(Document::OPERATION_CODE , $_SESSION['s_document_operation_code'])
            ->where(Document::TABLE_NAME , $_SESSION['s_document_table_name'])
            ->where(Document::ROW_ID  , $_SESSION['s_document_row_id'])
            ->select(Document::ID , Document::NAME , Document::CODE , Document::CREATED_BY , Document::CREATED_DATE , Document::PERMISSION)
            ->get()
        ;


        $documents = $documents->map(function ($document){
           return [
                "id" => $document->{Document::ID},
                "name" => $document->{Document::NAME},
                "code" => $document->{Document::CODE},
                "created_by" => $document->{Document::CREATED_BY},
                "created_date" => $document->{Document::CREATED_DATE},
                "permission" => $document->{Document::PERMISSION},
           ];
        });

//        return $documents;
        $this->Smarty->assign('documents' , $documents);




        $_SESSION['s_documents_token'] = md5(rand(0, 10000000));
    }

    public function add($parm, $post)
    {

        try {
            $this->Smarty->assign(
                'operation',
                Operation::readByCode((string)$_SESSION['s_document_operation_code'] ?? '')
            );
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        try {
            $this->Smarty->assign(
                'document_max_size_limit',
                number_format(
                    (((int)(new ConfigurationParser(CLIENT_CONFIG))->getSection(
                            'document'
                        )['document_max_size_limit']) / 1024) / 1024,
                    2,
                    '.',
                    ','
                )
            );
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('document_max_size_limit', 0);
        }

        $_SESSION['s_documents_token'] = md5(rand(0, 10000000));
    }

    public function edit($parm, $post)
    {

        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

        try {
            $this->Smarty->assign(
                'operation',
                Operation::readByCode((string)$_SESSION['s_document_operation_code'] ?? '')
            );
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        try {
            $this->Smarty->assign(
                'document_max_size_limit',
                number_format(
                    (((int)(new ConfigurationParser(CLIENT_CONFIG))->getSection(
                            'document'
                        )['document_max_size_limit']) / 1024) / 1024,
                    2,
                    '.',
                    ','
                )
            );
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('document_max_size_limit', 0);
        }

        $_SESSION['s_documents_token'] = md5(rand(0, 10000000));

    }

    public function confirm($parm, $post)
    {

        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

        $_SESSION['s_documents_token'] = md5(rand(0, 10000000));

    }

    public function browse($parm, $post, $files)
    {

        switch ($parm[0]) {

            case 'save_session':

                $_SESSION['s_document_operation_code'] = $parm[1];
                $_SESSION['s_document_table_name'] = $parm[2];
                $_SESSION['s_document_row_id'] = $parm[3];
                $_SESSION['s_document_user_id'] = $parm[4];
                $_SESSION['s_document_back_path'] = implode(
                    '/',
                    array_slice(explode('/', $this->URL->currentUrlPath()), 11)
                );

                break;
        }

        try {
            $this->Smarty->assign(
                'documents',
                Document::read(
                    [
                        Document::OPERATION_CODE => $_SESSION['s_document_operation_code'],
                        Document::TABLE_NAME => $_SESSION['s_document_table_name'],
                        Document::ROW_ID => $_SESSION['s_document_row_id'],
                    ]
                )
            );
        } catch (DocumentException $e) {

        }

        try {
            $this->Smarty->assign(
                'operation',
                Operation::readByCode((string)$_SESSION['s_document_operation_code'] ?? '')
            );
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

    }

    public function download($parm, $post, $files)
    {
        try {

            $document = Document::readID((int)$parm[0] ?? 0);
            $document->forceDownload();

        } catch (DocumentException $e) {

        }

    }

    public function preview($parm, $post, $files)
    {
        try {

            $document = Document::readID((int)$parm[0] ?? 0);
            $document->forcePreview();

        } catch (Exception $e) {
            redirect('page403/show');
        }

    }

    public function information($parm, $post)
    {
    }
}
