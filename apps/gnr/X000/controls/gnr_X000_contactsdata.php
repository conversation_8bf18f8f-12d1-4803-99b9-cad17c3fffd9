<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_contactsdata extends Controller
{


    function show($parm, $post)
    {
        switch ($parm[0]) {

            case 'save_session':
                $_SESSION['s_contactsdata_back_url'] = $parm[1] . '/' . $parm[2] . '/' . $parm[3] . '/' . $parm[4] . '/' . $parm[5] . '/' . $parm[6];
                break;
        }

        try {

            /**
             * @var $vacant Vacant
             */
            $vacant = Vacant::getEmployeeJobs($_SESSION['organization']->id, $_SESSION['user']->id, Vacant::EMPLOYEE_BASIC_VACANT);

            try {

                $CDRow = st_contactdata::simpleReadByProperty([
                    'st_contactdata_org_id' => CLIENT_ID,
                    'st_contactdata_user_id' => $_SESSION['user']->id])[0];

                $i = 0;
                $result = Vacant::getContactsEmployeesList($CDRow->st_contactdata_sex_available, $CDRow->st_contactdata_units_available, $vacant->jobObject->sh_job_unit_id);
                $empArray = array();
                foreach ($result as $key => $emp) {
                    $empArray[$i]['sh_user_id'] = $emp->userObject->id;
                    $empArray[$i]['sh_user_email'] = $emp->userObject->email;
                    $empArray[$i]['sh_user_tell'] = $emp->userObject->tell;
                    $i++;
                }

                $this->Smarty->assign('employeeList', $empArray);

            } catch (ModelException $e) {

            }
        } catch (VacantException $e) {

        }
    }
}