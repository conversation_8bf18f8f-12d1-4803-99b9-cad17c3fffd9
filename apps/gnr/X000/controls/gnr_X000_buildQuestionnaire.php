<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_buildQuestionnaire extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]){

            case 'saveSession':

                $_SESSION['s_qs_questionnaire_prg_id'] = $parm[1];
                $_SESSION['s_qs_questionnaire_id'] = $parm[2];
                $_SESSION['s_back_url'] = $parm[3] . "/" . $parm[4] . "/" . $parm[5] . "/" . $parm[6] . "/" . $parm[7] . "/" . $parm[8];

                break;
            case 'questionnaire':
                switch ($parm[1]){
                    case 'update':
                        if($_SESSION['s_buildQuestionnaire_token'] === $parm[2]){
                            try{
                                $questionnaire = Questionnaire::readID($parm[3]);
                                $questionnaire->bindProperties($post);
                                $questionnaire->update();

                                Notification::updatedAlert();

                            }catch (QuestionnaireException $e){}
                        }
                        break;
                }
                break;
            case 'section':
                switch ($parm[1]){
                    case 'insert':
                        if($_SESSION['s_buildQuestionnaire_token'] === $parm[2]){
                            try{
                                $section = new Section();
                                $section->bindProperties($post);
                                $section->org_id = $_SESSION['organization']->id;
                                $section->prg_id = $_SESSION['s_qs_questionnaire_prg_id'];
                                $section->questionnaire_id = $_SESSION['s_qs_questionnaire_id'];
                                $section->created_by = $_SESSION['user']->id;
                                $section->created_date = date('Y-m-d');
                                $section->create();

                                Notification::createdAlert();

                            }catch (SectionException $e){}
                        }
                        break;
                    case 'update':
                        if($_SESSION['s_buildQuestionnaire_token'] === $parm[2]){
                            try{
                                $section = Section::readID($parm[3] ?? 0);
                                $section->bindProperties($post);
                                $section->update();

                                Notification::updatedAlert();

                            }catch (SectionException $e){}
                        }
                        break;
                    case 'delete':
                        if($_SESSION['s_buildQuestionnaire_token'] === $parm[2]){
                            try{
                                $section = Section::readID($parm[3] ?? 0);
                                $section->delete();

                                Notification::deletedAlert();

                            }catch (SectionException $e){}
                        }
                        break;
                }
                break;
            case 'question':
                switch ($parm[1]){
                    case 'insert':
                        if($_SESSION['s_buildQuestionnaire_token'] === $parm[2]){
                            try{
                                $question = new Question();
                                $question->bindProperties($post);
                                $question->org_id = $_SESSION['organization']->id;
                                $question->prg_id = $_SESSION['s_qs_questionnaire_prg_id'];
                                $question->questionnaire_id = $_SESSION['s_qs_questionnaire_id'];
                                $question->section_id = $parm[3];
                                $question->create();

                                Notification::createdAlert();

                            }catch (QuestionException $e){}
                        }
                        break;
                    case 'update':
                        if($_SESSION['s_buildQuestionnaire_token'] === $parm[2]){
                            try{
                                $question = Question::readID($parm[3]);
                                $question->bindProperties($post);
                                $question->update();

                                Notification::updatedAlert();

                            }catch (QuestionException $e){}
                        }
                        break;
                    case 'delete':
                        if($_SESSION['s_buildQuestionnaire_token'] === $parm[2]){
                            try{
                                $question = Question::readID($parm[3] ?? 0);
                                $question->delete();

                                Notification::deletedAlert();

                            }catch (QuestionException $e){}
                        }
                        break;
                }
                break;
        }

        switch ($parm[1]) {
            case 'insert':
                if ($_SESSION['s_buildQuestionnaire_token'] == $parm[2]) {

                    try {
                        $oldOptions = Option::read([Option::QUESTION_ID => (int) $parm[0] ?? 0]);
                        foreach ($oldOptions as $option) {
                            if(!in_array($option->id, array_keys($post['old_options'])))
                                try{
                                    $option->delete();
                                }catch(OptionException $e){}
                        }
                    } catch (OptionException $exception) {}

                    try{
                        foreach ($post['old_options'] as $key => $option) {
                            $optionRow = Option::readID($key ?? 0);
                            $optionRow->name = $option;
                            try{
                                $optionRow->update();
                            }catch (OptionException $e){}
                        }
                    }catch (OptionException $e){}

                    try{
                        foreach ($post['name'] as $key => $option) {

                            $QuestionROw = Question::readID($parm[0] ?? 0);

                            $optionRow = new Option();
                            $optionRow->name = $option;
                            $optionRow->org_id = $QuestionROw->org_id;
                            $optionRow->prg_id = $QuestionROw->prg_id;
                            $optionRow->questionnaire_id = $QuestionROw->questionnaire_id;
                            $optionRow->section_id = $QuestionROw->section_id;
                            $optionRow->question_id = $QuestionROw->id;
                            try{
                                $optionRow->create();
                            }catch (OptionException $e){}
                        }
                    }catch (QuestionException $e){}

                    Notification::createdAlert();

                }
                break;
        }

        try{
            $this->Smarty->assign('questionnaire', Questionnaire::readID((int) $_SESSION['s_qs_questionnaire_id'] ?? 0));
        }catch (QuestionnaireException $e){
            $this->Smarty->assign('questionnaire',[]);
        }

        $_SESSION['s_buildQuestionnaire_token'] = md5(rand(0, 1000));
    }

    public function addsection($parm, $post)
    {
        $_SESSION['s_buildQuestionnaire_token'] = md5(rand(0, 1000));
    }

    public function editsection($parm, $post)
    {
        $this->Smarty->assign('row', Section::readID($parm[0] ?? 0));
        $_SESSION['s_buildQuestionnaire_token'] = md5(rand(0, 1000));
    }

    public function confirmsection($parm, $post)
    {
        $this->Smarty->assign('row', Section::readID($parm[0] ?? 0));
        $_SESSION['s_buildQuestionnaire_token'] = md5(rand(0, 1000));
    }

    public function addquestion($parm, $post)
    {
        $this->Smarty->assign('SectionID', $parm[0] ?? 0);
        $this->Smarty->assign('questionTypes', st_setting::SQL("SELECT * FROM st_setting where st_setting_id in (72,73,74,75,991,992) ",[]));
        $this->Smarty->assign('requireTypes', st_setting::simpleReadByProperty(['st_setting_opr_id'=>119]));
        $_SESSION['s_buildQuestionnaire_token'] = md5(rand(0, 1000));
    }

    public function editquestion($parm, $post)
    {
        $this->Smarty->assign('row', Question::readID($parm[0] ?? 0));
        $this->Smarty->assign('questionTypes', st_setting::SQL("SELECT * FROM st_setting where st_setting_id in (72,73,74,75,991,992) ",[]));
        $this->Smarty->assign('requireTypes', st_setting::simpleReadByProperty(['st_setting_opr_id'=>119]));
        $_SESSION['s_buildQuestionnaire_token'] = md5(rand(0, 1000));
    }

    public function confirmquestion($parm, $post)
    {
        $this->Smarty->assign('row', Question::readID($parm[0] ?? 0));
        $_SESSION['s_buildQuestionnaire_token'] = md5(rand(0, 1000));
    }

    public function editQuestionnaire($parm,$post){
        try {
            $this->Smarty->assign('row', Questionnaire::readID($parm[0] ?? 0));
        } catch (QuestionnaireException $e) {}

        try {
            $this->Smarty->assign('options', st_setting::simpleReadByProperty([
                'st_setting_opr_id' => Questionnaire::FILL_OPTIONS
            ]));
        } catch (ModelException $exception) {
            $this->Smarty->assign('options', []);
        }
        $_SESSION['s_evaluation_token'] = md5(rand(0,99999));
    }

    public function options($parm, $post, $files)
    {

        $this->Smarty->assign('QuestionID', $parm[0] ?? 0);
        try {
            $this->Smarty->assign('oldOptions', Option::read([
                Option::QUESTION_ID => $parm[0]
            ]));
        } catch (OptionException $exception) {
            $this->Smarty->assign('oldOptions', []);
        }
    }

    public function details($parm, $post, $files)
    {
        try {
            $this->Smarty->assign('evaluation', Evaluation::readID((int)$parm[0] ?? 0));
        } catch (EvaluationException $exception) {}
    }

    public function export($parm, $post)
    {
        try {
            $questionnaire = Questionnaire::readID((int)$parm[0]);
        } catch (QuestionnaireException $e) {
        }
        $questionnaireAsJson = serialize($questionnaire);

        header('Content-Description: File Transfer');
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename= ' . $questionnaire->name . '_' . time() . '.txt');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header("Content-Length: " . strlen($questionnaireAsJson));
        ob_clean();
        flush();
        echo $questionnaireAsJson;
        exit;
    }
}