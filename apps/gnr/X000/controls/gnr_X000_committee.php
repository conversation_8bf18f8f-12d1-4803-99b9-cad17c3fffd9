<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_committee extends Controller
{
    public function show($parm, $post, $files)
    {
        switch ($parm[0]) {

            case 'save_session':

                $_SESSION['s_committee_id'] = (int)$parm[1] ?? 0;

                break;

            case 'update_notification':

                try{

                    $notification = Notification::readID((int)$parm[1] ?? 0);
                    $notification->status = 0;
                    $notification->save();

                    $_SESSION['s_committee_id'] = $notification->committee_id;
                    $_SESSION['s_committee_back_url'] = $parm[2] . '/' . $parm[3] . '/' . $parm[4] . '/' . $parm[5] . '/' . $parm[6] . '/' . $parm[7];

                }catch (NotificationException $e){

                }

                break;

        }

        try{

            $committee = Committee::readID((int)$_SESSION['s_committee_id'] ?? 0);
            $this->Smarty->assign('committeeRow', $committee);

            try{


                $this->Smarty->assign('committeeMembers', CommitteeMember::read([
                    CommitteeMember::COMMITTEE_ID => $committee->id,
                    CommitteeMember::ACTIVATION_STATUS => 652
                ]));

            }catch (CommitteeMemberException $e){

            }

            try{

                $this->Smarty->assign('committeeTasks', Notification::read([
                    Notification::ORG_ID => $_SESSION['organization']->id,
                    Notification::COMMITTEE_ID => $committee->id,
                    Notification::TO_USER_ID => $_SESSION['user']->id
                ],[0=>['property'=>Notification::CREATED_DATE,'sort'=>'DESC']]));

            }catch (NotificationException $e){

            }

        }catch (CommitteeException $e){

        }

    }

    public function alert($parm, $post, $files)
    {

        try{

            $request = Request::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('requestRow', $request);

        }catch (RequestException $e){

        }

        try{

            $step = Step::readID((int)$parm[1] ?? 0);
            $this->Smarty->assign('stepRow', $step);

        }catch (StepException $e){

        }

        if($step){

            try{

                $committee = Committee::readID((int)$step->committee_id);
                $this->Smarty->assign('committeeRow', $committee);

            }catch (CommitteeException $e){

            }

            if($committee){

                try{

                    $committeeMembers = CommitteeMember::read([
                        CommitteeMember::COMMITTEE_ID => (int)$committee->id,
                        CommitteeMember::ACTIVATION_STATUS => 652
                    ]);
                    $this->Smarty->assign('committeeMembers', $committeeMembers);
                    $this->Smarty->assign('committeeMembersCount', count($committeeMembers));

                }catch (CommitteeMemberException $e){

                }

            }

        }

        $_SESSION['s_wf_request_token'] = md5(rand(0000, 9999));

    }

    public function browse($parm, $post)
    {
        try {

            $this->Smarty->assign('committee', Committee::readID((int)$parm[0] ?? 0));

        } catch (CommitteeException $e) {

            $this->Smarty->assign('committee', []);

        }
    }
}
