<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_servicetypeone extends Controller
{
    public function show($parm, $post, $files)
    {
        // no access without serviceID
        if (empty($parm[1])) {
            exit();
        }
        $this->Smarty->assign('parameters', $parm);

        try {

            if (!empty($_GET['applicationID'])) {

                try {
                    $application = es_application::readByID($_GET['applicationID']);
                    $this->Smarty->assign('applicationID', $application->es_application_id);
                    $waqfID = $_SESSION['s_waqf_id'] = $application->es_application_table_row_id;
                } catch (ModelException $e) {
                    $waqfID = $_SESSION['s_waqf_id'] = (empty($_SESSION['x_servicetypeone_waqf_id'])) ? 0 : $_SESSION['x_servicetypeone_waqf_id'];
                }

            } else {
                $waqfID = $_SESSION['s_waqf_id'] = (empty($_SESSION['x_servicetypeone_waqf_id'])) ? 0 : $_SESSION['x_servicetypeone_waqf_id'];

                try {
                    $application = es_application::readTableRowID((int)$parm[1], (int)$waqfID);

                    // this to fix the direct access to forms to add while it has data in session to refer to it..
                    $_GET['applicationID'] = $application->es_application_id;
                    $this->Smarty->assign('applicationID', $application->es_application_id);
                } catch (ModelException $e) {

                }
            }

            $this->Smarty->assign('waqfID', $waqfID);
            $this->Smarty->assign('service', es_service::readByID((int)$parm[1]));
            $_SESSION['x_servicetypeone_data_tokan'] = md5(rand(0, 1000000));
        } catch (ModelException $e) {

        }

    }
}
