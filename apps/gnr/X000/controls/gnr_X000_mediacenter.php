<?php
// Deny Direct Script Access
defined('ENVIRONMENT') or exit('No Direct Access Allowed!');

use Domain\Finance\Reports\AccountSheet;
use Models\EmployeePayrollTemplate;
use Models\Finance\PayrollTransaction;
use Application\Cache\SnsoCache;
use Carbon\Carbon;

use Models\Finance\Year;
use Models\User as UserModel;
use Models\HumanResource\Request as RequestModel;
use Models\HumanResource\Job as JobModel;
use Models\HumanResource\PayrollBatch as PayrollBatchModel;

class gnr_X000_mediacenter extends Controller
{
    public function show($parm, $post, $files)
    {

    }

    public function news($parm, $post, $files)
    {
        try {

            $this->Smarty->assign('news', MediaCenter::readID((int) $parm[0] ?? 0));

        } catch (MediaCenterException $e) {

            $this->Smarty->assign('news', []);

        }

    }

    public function adv($parm, $post, $files)
    {
        try {

            $this->Smarty->assign('adv', MediaCenter::readID((int) $parm[0] ?? 0));

        } catch (MediaCenterException $e) {

            $this->Smarty->assign('adv', []);

        }
    }

    public function license($parm, $post, $files)
    {
    }

    public function absenceDayslist($parm, $post, $files)
    {
        try {

            $absence = HRAttendanceRequest::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('absence', $absence);
            $this->Smarty->assign('details', json_decode($absence->detail, true));
            $this->Smarty->assign('parm0', $parm[0]);

        } catch (HRAttendanceRequestException $e) {

        }

        $_SESSION['s_personnel_tab'] = "absence";
        $_SESSION['s_personnel_token'] = md5(rand(0000, 9999));
    }

    public function absenceDayslistPrint($parm, $post, $files)
    {

        try {

            $absence = HRAttendanceRequest::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('absence', $absence);
            $this->Smarty->assign('details', json_decode($absence->detail, true));
            $this->Smarty->assign('parm0', $parm[0]);

        } catch (HRAttendanceRequestException $e) {

        }

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));
    }

    /**
     * @param $parm
     */
    public function jobCardHTML($parm)
    {
        $job = JobModel::find($parm[0]);
        $this->Smarty->assign('job', $job);
    }

    public function jobcard($parm, $post, $files)
    {

        try {

            $job = Job::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('job', $job);

            try {

                $unit = Unit::readID($job->unit_id);
                $this->Smarty->assign('unit', $unit);

            } catch (UnitException $e) {

                $this->Smarty->assign('unit', []);

            }

        } catch (JobException $e) {

            $this->Smarty->assign('job', []);

        }

    }

    public function jobcardprint($parm, $post, $files)
    {

        $this->jobcard($parm, $post, $files);

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));
    }

    public function advanceDetails($parm, $post)
    {
        try {

            $this->Smarty->assign('advance', HRAdvanceRequest::readID((int) $parm[0] ?? 0));

        } catch (HRAdvanceRequestException $e) {

            $this->Smarty->assign('advance', []);

        }
    }

    public function payslipadditions($parm, $post)
    {
        /** @var PayrollTrans $transRow */
        $transRow = PayrollTransaction::find($parm[0]);
        $this->Smarty->assign('transaction', $transRow);
        $extraAdditions = json_decode($transRow->extrastring, true);

        if ($transRow) {
            $this->Smarty->assign('absences', HRAttendanceRequest::convertFromQB(
                DB::table(hr_attendance::class)
                    ->whereIn(HRAttendanceRequest::ID, $extraAdditions['attendance_list'])
                    ->get()
            ));
            $this->Smarty->assign('installments', HRAdvanceInstallment::convertFromQB(
                DB::table(hr_advanceinstallment::class)
                    ->whereIn(HRAdvanceInstallment::ID, $extraAdditions['installments_list'])
                    ->get()
            ));
            $this->Smarty->assign('deductAdditions', HRDeductAdditionRequest::convertFromQB(
                DB::table(hr_deductaddition::class)
                    ->whereIn(HRDeductAdditionRequest::ID, $extraAdditions['deductions_list'])
                    ->get()
            ));
            $this->Smarty->assign('latencies', HRLatencyRequest::convertFromQB(
                DB::table(hr_latency::class)
                    ->whereIn(HRLatencyRequest::ID, $extraAdditions['latency_list'])
                    ->get()
            ));

            try {

                $this->Smarty->assign('user', User::readID($transRow->user_id));

            } catch (UserException $e) {

                $this->Smarty->assign('user', []);
            }

            try {

                /**
                 * @var $vacant Vacant
                 */
                $vacant = Vacant::getEmployeeJobs($_SESSION['organization']->id, $transRow->user_id, Vacant::EMPLOYEE_BASIC_VACANT);
                $this->Smarty->assign('employeeWorkDays', $vacant->salary_delivery == 227 ? 30 : $vacant->days_in_month);

            } catch (VacantException $e) {

            }

            $employee = Vacant::getEmployeeBasicVacantEntity($transRow->user_id);
            $template = PayrollTemplate::readID((int) $employee->sh_uao_payroll_template_id);
            $employeeSpecificTemplate = EmployeePayrollTemplate::where('user_id', $transRow->user_id)
                ->get();
            $templateItemsArray = collect([]);

            foreach ($template->rules as $temp) {
                $_template = [];
                $_template['effectInDay'] = $temp->effect_in_day_cost;
                $_template['type'] = $temp->category_id;
                if ($temp->category_id == PayrollRule::SALARY_RULE_BASIC)
                    $_template['value'] = $employee->sh_uao_salary_basic;
                else {
                    if ($temp->amount_type == 650)
                        $_template['value'] = $temp->amount_fixed_value;
                    else if ($temp->amount_type == 651) {
                        $_template['value'] = $temp->getParentPayrollValue($employee->sh_uao_salary_basic);
                    }
                }
                $templateItemsArray->push($_template);
            }

            foreach ($employeeSpecificTemplate as $temp) {
                $_template = [];
                $_template['effectInDay'] = $temp->template->effect_in_day_cost;
                $_template['type'] = $temp->template->category_id;
                if ($temp->template->category_id == PayrollRule::SALARY_RULE_BASIC)
                    $_template['value'] = $employee->sh_uao_salary_basic;
                else {
                    if ($temp->template->amount_type == 650)
                        $_template['value'] = $temp->template->amount_fixed_value;
                    else if ($temp->template->amount_type == 651) {
                        $_template['value'] = (new PayrollRule())->getParentPayrollValue($employee->sh_uao_salary_basic);
                    }
                }
                $templateItemsArray->push($_template);
            }

            $employeeAbsenceDays = (int) $transRow->employeeAbsenceDays;
            $this->Smarty->assign('employeeAbsenceDays', $employeeAbsenceDays);
            $this->Smarty->assign('employeeAbsenceValue', PayrollBatch::calculateDayCost($templateItemsArray) * $employeeAbsenceDays);

        }

    }

    public function payslipDeductadditions($parm, $post)
    {
        $deductionsAddition = [];
        $this->Smarty->assign('userID', (int) $parm[1] ?? 0);
        try {
            $deductionsAddition = HRDeductAdditionRequest::read([
                HRDeductAdditionRequest::ORG_ID => (int) $_SESSION['organization']->id,
                HRDeductAdditionRequest::USER_ID => (int) $parm[1] ?? 0,
                HRDeductAdditionRequest::TYPE => HRDeductAdditionRequest::SETTINGS_ADDITION,
                HRDeductAdditionRequest::REQUEST_SUCCESS => 1
            ], [0 => ['property' => HRDeductAdditionRequest::ID, 'sort' => 'DESC']]);
        } catch (HRDeductAdditionRequestException $e) {
        }

        $trans = PayrollTransaction::findOrFail($parm[0]);
        $this->Smarty->assign('deductions_list', json_decode($trans->prl_trans_additions_list, true));
        $this->Smarty->assign('deductionsAdditionNum', count($deductionsAddition));
        $this->Smarty->assign('deductionsAddition', $deductionsAddition);
    }

    public function payslipDeductadditionsPrint($parm, $post)
    {
        $deductionsAddition = [];
        $this->Smarty->assign('userID', (int) $parm[0] ?? 0);

        try {
            $deductionsAddition = HRDeductAdditionRequest::read([
                HRDeductAdditionRequest::ORG_ID => (int) $_SESSION['organization']->id,
                HRDeductAdditionRequest::USER_ID => (int) $parm[0] ?? 0,
                HRDeductAdditionRequest::TYPE => HRDeductAdditionRequest::SETTINGS_ADDITION,
                HRDeductAdditionRequest::REQUEST_SUCCESS => 1
            ], [0 => ['property' => HRDeductAdditionRequest::ID, 'sort' => 'DESC']]);
        } catch (HRDeductAdditionRequestException $e) {
        }
        $this->Smarty->assign('deductionsAdditionNum', count($deductionsAddition));
        $this->Smarty->assign('deductionsAddition', $deductionsAddition);
        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()), "", "", "", true);
    }

    public function payslipAdditionsPrint($parm, $post)
    {
        $this->payslipadditions($parm);
        generatePdf();
    }

    public function payslip($parm, $post)
    {

        try {

            $this->Smarty->assign('transaction', PayrollTrans::readID((int) $parm[0] ?? 0));

        } catch (PayrollTransException $e) {

            $this->Smarty->assign('transaction', []);
        }
    }

    public function userPrvs($parm, $post)
    {

        try {

            $employee = Vacant::getEmployeeJobs($_SESSION['organization']->id, (int) $parm[0] ?? 0, Vacant::EMPLOYEE_BASIC_VACANT);
            $this->Smarty->assign('employee', $employee);

            try {

                $program = Program::readID((int) $parm[1] ?? 0);
                $this->Smarty->assign('Program', $program);

                $this->Smarty->assign('opr_list', Privilege::prepareEmployeePrivilegesToShow($_SESSION['organization'], $program, $employee));

            } catch (ProgramException $e) {

                $this->Smarty->assign('Program', []);

            }

        } catch (VacantException $e) {

            $this->Smarty->assign('employee', []);

        }

        $_SESSION['s_prv_tokan'] = md5(rand(0000, 99999));
    }

    public function dateandlanguage($parm, $post)
    {
        try {
            $this->Smarty->assign('translations', Translation::read([], [0 => ['property' => Translation::ID, 'sort' => 'ASC']]));
        } catch (TranslationException $e) {
            $this->Smarty->assign('translations', []);
        }

        $this->Smarty->assign('From', $parm[0] ?? '');
        $this->Smarty->assign('row', sh_user::readByID($_SESSION['user']->id));
    }

    public function leaveTemplateBrowse($parm, $post)
    {

        try {

            $row = LeaveType::readID($parm[0] ?? 0);
            $this->Smarty->assign('leaveType', $row);

            switch ($row->annual_intrvl_type) {

                case 830:
                    $this->Smarty->assign('jsCode9', ' <script type="text/javascript"> $(document).ready(function () { $("#extraInput").hide("fast"); }); </script> ');
                    break;

                case 831:
                    $this->Smarty->assign('jsCode1', ' <script type="text/javascript"> $(document).ready(function () { $("#extraInput").show("fast");$("#extraTransfere").show("fast");$("#divdaysnumber").show("fast");}); </script> ');
                    break;

                case 837:
                    $this->Smarty->assign('jsCode10', ' <script type="text/javascript"> $(document).ready(function () { $("#extraInput").show("fast"); $("#extraTransfere").show("fast"); $("#divdaysnumber").hide("fast");}); </script> ');
                    if ($row->transfer_no == 838) {
                        $this->Smarty->assign('jsCode50', ' <script type="text/javascript"> $(document).ready(function () { $("#firstUse").show("fast");}); </script> ');
                    }
                    break;

                case 894:
                    $this->Smarty->assign('jsCode51', ' <script type="text/javascript"> $(document).ready(function () {$("#extraInput").show("fast"); $("#extraTransfere").hide("fast");$("#divzSomeDaySomeMonthYearly").hide("fast");}); </script> ');
                    break;

                case 884:
                    $this->Smarty->assign(
                        'jsCode9',
                        ' <script type="text/javascript"> $(document).ready(function () { $("#extraInput").hide("fast"); }); </script> '
                    );
                    break;

                case 808:
                    $this->Smarty->assign('jsCode2', ' <script type="text/javascript"> $(document).ready(function () { 
                    $("#divzSomeDaySomeMonthYearly").hide("fast");                    
                }); </script> ');
                    break;

                case 809:
                    $this->Smarty->assign('jsCode8', ' <script type="text/javascript"> $(document).ready(function () { 
                    $("#divzSomeDaySomeMonthYearly").show("fast");
                    $("#divdaysnumber").show("fast");
            }); </script> ');
                    break;

                case 838:
                    $this->Smarty->assign('jsCode3', ' <script type="text/javascript"> $(document).ready(function () { 
                    $("#divzSomeDaySomeMonthYearly").show("fast");
                    $("#divdaysnumber").hide("fast");}); </script> ');

                    if ($row->annual_intrvl_type == 837) {
                        $this->Smarty->assign('jsCode50', ' <script type="text/javascript"> $(document).ready(function () { $("#firstUse").show("fast");}); </script> ');
                    }
                    break;

                case 814:
                    $this->Smarty->assign('jsCode6', ' <script type="text/javascript"> $(document).ready(function () {$("#divzSomeDaySomeMonthYearly").show("fast");}); </script> ');
                    break;
            }

        } catch (LeaveAllowedException $e) {
            $this->Smarty->assign('allowedLeave', []);
        }

        $this->Smarty->config_load(APP_DIR . DS . "bsc" . DS . "P051" . DS . "translations" . DS . $_SESSION['lang'] . '.conf', 'leavesettings');
    }

    public function leaves($parm, $post, $files)
    {

        try {

            $allowedLeave = LeaveAllowed::readID($parm[0] ?? 0);
            $this->Smarty->assign('allowedleaveRow', $allowedLeave);

            try {
                $this->Smarty->assign('leaveList', LeaveRequest::read(
                    [
                        LeaveRequest::ORG_ID => $allowedLeave->org_id,
                        LeaveRequest::LEVST_ID => $allowedLeave->leave_id,
                        LeaveRequest::USER_ID => $allowedLeave->user_id,
                    ],
                    [
                        0 => [
                            'property' => 'hr_lev_created_date',
                            'sort' => 'DESC'
                        ]
                    ]
                ));

            } catch (LeaveRequestException $e) {
            }

        } catch (LeaveAllowedException $e) {

            $this->Smarty->assign('allowedleaveRow', []);

        }

    }

    public function leavedayslist($parm, $post, $files)
    {
        $daysList = LeaveData::LeaveEndDate($_SESSION['organization']->id, $parm[0], $parm[1]);
        $this->Smarty->assign('daysList', json_decode($daysList['arrjson'], true));
    }

    public function leaveDataBrowse($parm, $post, $files)
    {
        try {

            $this->Smarty->assign('data', LeaveData::readID((int) $parm[0]));

        } catch (LeaveDataException $e) {
            $this->Smarty->assign('data', []);
        }
    }

    public function circulateleavedayslist($parm, $post, $files)
    {
        // $parm[0] : Circulate Leave Start Date
        // $parm[1] : Circulate Leave Duration

        $daysList = LeaveScheduled::circulateLeaveEndDate($_SESSION['organization']->id, $parm[0] ?? '', (int) $parm[1] ?? 0);
        $this->Smarty->assign('daysList', json_decode($daysList['arrjson'], true));
    }

    public function recruitNotification($parm, $post)
    {

        try {

            $notification = Notification::readID((int) $parm[0] ?? 0);
            $notification->status = 0;
            $notification->save();

            try {

                $this->Smarty->assign('vacant', Vacant::readID($notification->row_id));

            } catch (VacantException $e) {
                $this->Smarty->assign('vacant', []);
            }

        } catch (NotificationException $e) {
        }

    }

    public function doamDayList($parm, $post)
    {
        try {
            $this->Smarty->assign('doam', Doam::readID(((int) $parm[0] ?? 0)));
        } catch (DoamException $e) {
            $this->Smarty->assign('doam', []);
        }

        $this->Smarty->assign('weekDays', Setting::getList(25));

    }

    public function browsedeductaddition($parm, $post)
    {
        try {
            $this->Smarty->assign('row', HRDeductAdditionRequest::readID((int) $parm[0] ?? 0));
        } catch (HRDeductAdditionRequestException $e) {
            $this->Smarty->assign('row', []);
        }
    }

    public function leaveCreditTransactions($parm, $post)
    {

        LeaveData::updateEmployeeLeavesCredit($_SESSION['organization']->id, $_SESSION['user']->id);

        try {

            $leaveAllowed = LeaveAllowed::readID((int) $parm[0]);
            $this->Smarty->assign('allowedleaveRow', $leaveAllowed);

        } catch (LeaveTypeException $e) {
            $this->Smarty->assign('allowedleaveRow', []);
        }

        try {

            $user = User::readID((int) $parm[1]);
            $this->Smarty->assign('userRow', $user);

        } catch (UserException $e) {
            $user = null;
            $this->Smarty->assign('userRow', null);
        }

        if ($user) {
            $this->Smarty->assign('vacantRow', Vacant::getEmployeeBasicVacantEntity((int) $user->id));
        } else {
            $this->Smarty->assign('vacantRow', null);
        }

        if ($leaveAllowed) {

            try {

                $this->Smarty->assign('transactions', LeaveTransaction::read([
                    LeaveTransaction::ORG_ID => $_SESSION['organization']->id,
                    LeaveTransaction::LEAVE_ID => (int) $leaveAllowed->leave_id,
                    LeaveTransaction::USER_ID => (int) $leaveAllowed->user_id
                ], [
                    0 => [
                        'property' => LeaveTransaction::ID,
                        'sort' => 'DESC'
                    ]
                ]));

            } catch (LeaveTransactionException $e) {

                $this->Smarty->assign('transactions', []);

            }

        }

    }

    public function userIncome($parm, $post)
    {
        try {
            $user = User::readID((int) $parm[0]);
        } catch (UserException $e) {
            $user = null;
        }

        if ($user) {

            $this->Smarty->assign('user', $user);

            /*
             * Income Data
             */
            try {
                $this->Smarty->assign('db_inc_list', DBIncome::read([
                    DBIncome::USER_ID => $user->id
                ]));
            } catch (DBIncomeException $e) {
                $this->Smarty->assign('db_inc_list', []);
            }

            /*
             * Debit Data
             */
            try {
                $this->Smarty->assign('db_debt_list', DBDebt::read([
                    DBDebt::USER_ID => $user->id
                ]));
            } catch (DBDeptException $e) {
                $this->Smarty->assign('db_debt_list', []);
            }

            /*
             * Houses Data
             */
            try {
                $this->Smarty->assign('houses_list', DBHouses::read([
                    DBHouses::USER_ID => $user->id
                ]));
            } catch (DBHousesException $e) {
                $this->Smarty->assign('houses_list', []);
            }

        }

    }

    public function userIncomePrint($parm, $post)
    {
        try {
            $user = User::readID((int) $parm[0]);
        } catch (UserException $e) {
            $user = null;
        }

        if ($user) {

            $this->Smarty->assign('user', $user);

            /*
             * Income Data
             */
            try {
                $this->Smarty->assign('db_inc_list', DBIncome::read([
                    DBIncome::USER_ID => $user->id
                ]));
            } catch (DBIncomeException $e) {
                $this->Smarty->assign('db_inc_list', []);
            }

            /*
             * Debit Data
             */
            try {
                $this->Smarty->assign('db_debt_list', DBDebt::read([
                    DBDebt::USER_ID => $user->id
                ]));
            } catch (DBDeptException $e) {
                $this->Smarty->assign('db_debt_list', []);
            }

            /*
             * Houses Data
             */
            try {
                $this->Smarty->assign('houses_list', DBHouses::read([
                    DBHouses::USER_ID => $user->id
                ]));
            } catch (DBHousesException $e) {
                $this->Smarty->assign('houses_list', []);
            }

        }

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));
    }

    public function browseAssistanceRequest($parm, $post)
    {
        $assistanceYear = AssistYear::getCurrentAssistanceActiveYear();

        if ($assistanceYear) {

            $this->Smarty->assign('assistanceYear', $assistanceYear);

            try {
                $assistanceRequest = AssistanceRequest::readID((int) $parm[0]);
                $this->Smarty->assign('request', $assistanceRequest);
            } catch (AssistanceRequestException $e) {
                $assistanceRequest = null;
                $this->Smarty->assign('request', $assistanceRequest);
            }

            if ($assistanceRequest) {

                try {
                    $this->Smarty->assign('assistance', Assistance::readID((int) $assistanceRequest->assistance_id));
                } catch (AssistanceException $e) {
                    $this->Smarty->assign('assistance', []);
                }

                try {
                    $this->Smarty->assign('termOfSelection', TermsOfSelection::readID((int) $assistanceRequest->term_of_selection_id));
                } catch (TermsOfSelectionException $e) {
                    $this->Smarty->assign('termOfSelection', []);
                }

                try {
                    $this->Smarty->assign('deliveries', AssistanceDelivery::read([AssistanceDelivery::REQUEST_ID => $assistanceRequest->id]));
                } catch (AssistanceDeliveryException $e) {
                    $this->Smarty->assign('deliveries', []);
                }

            }

        }

    }

    public function browseBeneficiaryAssistanceHistory($parm, $post)
    {

        try {
            $termOfSelection = TermsOfSelection::readID((int) $parm[0]);
        } catch (TermsOfSelectionException $e) {
            $termOfSelection = null;
        }

        if ($termOfSelection) {

            $this->Smarty->assign('termOfSelection', $termOfSelection);

            switch ($termOfSelection->type) {

                case TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_INDIVIDUAL:

                    try {
                        $user = User::readID((int) $parm[1]);
                    } catch (UserException $e) {
                        $user = null;
                    }

                    if ($user) {

                        try {

                            $this->Smarty->assign('constraints', AssistConstraint::read([
                                AssistConstraint::ORG_ID => $_SESSION['organization']->id,
                                AssistConstraint::USER_ID => $user->belongs_to_user
                            ]));

                        } catch (AssistConstraintException $e) {

                            $this->Smarty->assign('constraints', []);

                        }

                        $this->Smarty->assign('beneficiary', $user);

                        try {

                            $this->Smarty->assign('deliveries', AssistanceDelivery::read([
                                AssistanceDelivery::ORG_ID => $_SESSION['organization']->id,
                                AssistanceDelivery::BENEFICIARY_ID => $user->id,
                                AssistanceDelivery::STATUS => AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_YES
                            ]));

                        } catch (AssistanceDeliveryException $e) {

                            $this->Smarty->assign('deliveries', []);

                        }

                    }

                    break;

                case TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY:

                    try {
                        $family = AssistanceFamilies::readID((int) $parm[1]);
                    } catch (AssistanceFamiliesException $e) {
                        $family = null;
                    }

                    try {
                        $user = User::readID((int) $family->belongs_to_user);
                    } catch (UserException $e) {
                        $user = null;
                    }

                    try {

                        $this->Smarty->assign('constraints', AssistConstraint::read([
                            AssistConstraint::ORG_ID => $_SESSION['organization']->id,
                            AssistConstraint::USER_ID => $user->id
                        ]));

                    } catch (AssistConstraintException $e) {

                        $this->Smarty->assign('constraints', []);

                    }

                    if ($family) {

                        $this->Smarty->assign('family', $family);

                        try {

                            $this->Smarty->assign('deliveries', AssistanceDelivery::read([
                                AssistanceDelivery::ORG_ID => $_SESSION['organization']->id,
                                AssistanceDelivery::FAMILY_ID => $family->id,
                                AssistanceDelivery::STATUS => AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_YES
                            ]));

                        } catch (AssistanceDeliveryException $e) {

                            $this->Smarty->assign('deliveries', []);

                        }

                    }

                    break;

            }

        }

    }

    public function browseBeneficiaries($parm, $post)
    {
        switch ($parm[0]) {

            case 'category':

                try {
                    $category = AssistCategory::readID((int) $parm[1]);
                } catch (AssistCategoryException $e) {
                    $category = null;
                }

                if ($category) {
                    switch ($category->term_of_selection_type) {
                        case TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_INDIVIDUAL:
                            $this->Smarty->assign('beneficiaries', User::getUserInArrayOfIds($category->ids));
                            $this->Smarty->assign('type', 'beneficiaries');
                            break;
                        case TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY:
                            $this->Smarty->assign('families', AssistanceFamilies::getFamiliesInArrayOfIds($category->ids));
                            $this->Smarty->assign('type', 'families');
                            break;
                    }
                }

                break;

            case 'requestCategory':

                try {
                    $requestCategory = AssistRequestCategory::readID((int) $parm[1]);
                } catch (AssistRequestCategoryException $e) {
                    $requestCategory = null;
                }

                if ($requestCategory) {

                    switch ($requestCategory->term_of_selection_type) {

                        case TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_INDIVIDUAL:
                            $this->Smarty->assign('beneficiaries', User::getUserInArrayOfIds($requestCategory->request_ids));
                            $this->Smarty->assign('type', 'beneficiaries');
                            break;

                        case TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY:
                            $this->Smarty->assign('families', AssistanceFamilies::getFamiliesInArrayOfIds($requestCategory->request_ids));
                            $this->Smarty->assign('type', 'families');
                            break;

                    }

                }

                break;

            case 'termOfSelection':
                try {
                    $termOfSelection = TermsOfSelection::readID((int) $parm[1]);
                } catch (TermsOfSelectionException $e) {
                    $termOfSelection = null;
                }

                if ($termOfSelection) {

                    switch ($termOfSelection->type) {

                        case TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_INDIVIDUAL:
                            $this->Smarty->assign('beneficiaries', User::getUserInArrayOfIds($termOfSelection->beneficiaries));
                            $this->Smarty->assign('type', 'beneficiaries');
                            break;

                        case TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY:
                            $this->Smarty->assign('families', AssistanceFamilies::getFamiliesInArrayOfIds($termOfSelection->families));
                            $this->Smarty->assign('type', 'families');
                            break;

                    }

                }
                break;

            case 'assistance':
                break;

        }

    }

    public function visitDetails($parm, $post)
    {

        try {
            $visit = AssistVisit::readID((int) $parm[0]);
            $this->Smarty->assign('visit', $visit);
        } catch (AssistVisitException $e) {
            $visit = null;
            $this->Smarty->assign('visit', $visit);
        }

        if ($visit) {

            try {

                $this->Smarty->assign('exists', AssistVisitDetails::read([AssistVisitDetails::TYPE => AssistVisitDetails::EXIST, AssistVisitDetails::VISIT_ID => $visit->id]));

            } catch (AssistVisitDetailsException $e) {
                $this->Smarty->assign('exists', []);
            }

            try {
                $this->Smarty->assign('needs', AssistVisitDetails::read([AssistVisitDetails::TYPE => AssistVisitDetails::NEED, AssistVisitDetails::VISIT_ID => $visit->id]));
            } catch (AssistVisitDetailsException $e) {
                $this->Smarty->assign('needs', []);
            }

        }

    }

    public function createNewBeneficiary($parm, $post, $files)
    {

        switch ($parm[0]) {

            case 'formUrl':

                $_SESSION['formUrl'] = $parm[1] . '/' . $parm[2] . '/' . $parm[3] . '/' . $parm[4] . '/' . $parm[5] . '/' . $parm[6] . '/' . $parm[7] . '/' . $parm[8];
                //check if we want to insert new user for clubsAndActs prg
                if ($parm[2] == 'P253') {
                    $this->Smarty->assign('categories', \Models\ClubCategory::all());

                    try {
                        $this->Smarty->assign('educationalList', Setting::getList(2));
                    } catch (SettingException $e) {
                        $this->Smarty->assign('educationalList', []);
                    }
                } elseif ($parm[2] == 'P269') {
                    $class_id = AssistancesSettings::read([AssistancesSettings::ORG_ID => $_SESSION['organization']->id])[0]->client_id;
                    $class_name = UserClass::readID($class_id)->name;
                    $this->Smarty->assign('class_id', $class_id);
                    $this->Smarty->assign('class_name', $class_name);
                } elseif ($parm[2] == 'P268') {
                    $class_id = SanabelSettings::read([SanabelSettings::ORG_ID => $_SESSION['organization']->id])[0]->donors_classifications;
                    $class_name = UserClass::readID($class_id)->name;
                    $categories = SanabelDonorsCategory::read([SanabelDonorsCategory::ORG_ID => $_SESSION['organization']->id]);
                    $this->Smarty->assign('class_id', $class_id);
                    $this->Smarty->assign('class_name', $class_name);
                    $this->Smarty->assign('show_email', true);
                    $this->Smarty->assign('sanabel_categories', $categories);
                }


                break;

        }

        try {
            $this->Smarty->assign('classifications', UserClass::read([UserClass::ORG_ID => $_SESSION['organization']->id]));
        } catch (\UserClassException $e) {
            $this->Smarty->assign('classifications', []);
        }

        try {
            $this->Smarty->assign('sex_lists', Setting::getList(27));
        } catch (SettingException $e) {
            $this->Smarty->assign('sex_lists', []);
        }

        try {

            $this->Smarty->assign("regConfigs", json_decode((ClientConfiguration::read([ClientConfiguration::OPR_ID => 277])[0])->ids, true));

        } catch (ClientConfigurationException $e) {

        }

        $_SESSION['s_createNewBeneficiary_token'] = Helper::generateToken();
    }

    public function browseSponsershipBeneficiaryDeliveryRecords($parm, $post)
    {

        try {
            $addition = AssistanceAdditionExclusion::readID((int) $parm[0]);
        } catch (AssistanceAdditionExclusionException $e) {
            $addition = null;
        }

        if ($addition) {

            $this->Smarty->assign('addition', $addition);
            $this->Smarty->assign('deliveries', AssistanceDelivery::getDeliveryBelongToAddition($addition));
        }

    }

    public function employeeDataReport($parm, $post)
    {
        setBreadcrumb('gnr_employee_record_data');

        $_SESSION['emp_user_id'] = $parm[0];

        $employee = UserModel::find($parm[0]);
        $this->Smarty->assign('employee', $employee);

        $this->getEmployeeRequests($employee);
        $this->getEmployeeAccountabilties($employee);
        $this->getEmployeeCommittees();
        $this->getEmployeeJobs($employee);

        try {
            $vacations = LeaveAllowed::read([
                LeaveAllowed::USER_ID => $employee->id
            ]);
        } catch (LeaveAllowedException $e) {
            $vacations = null;
        }

        $this->Smarty->assign('vacations', $vacations);

    }

    public function browseArchTransaction($parm, $post)
    {

        try {
            $thisReferral = ArchReferral::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('thisReferral', $thisReferral);

            switch ($thisReferral->assignment_type) {
                case ArchReferral::ARCH_REFERRAL_TYPES_TASK:
                    $this->Smarty->assign('jsCode', '
                    <script type="text/javascript">
                        $(document).ready(function () {
                
                            $("#inCaseOfMessage").hide("fast");
                            $("#inCaseOfTask").show("fast");
                
                        });
                    </script>');
                    break;

            }

        } catch (ArchReferralException $e) {
            $this->Smarty->assign('thisReferral', []);
        }

        $this->Smarty->assign('assignmentTypes', Setting::getList(236));
        $this->Smarty->assign('privacyTypes', Setting::getList(229));
        $this->Smarty->assign('priorities', Setting::getList(225));
        $this->Smarty->assign('replayTypes', Setting::getList(235));
        $this->Smarty->assign('languages', Language::read());

        try {


            $this->Smarty->assign('folders', ArchFolder::read([
                ArchFolder::BELONG_TO => $thisReferral->belong_to,
                ArchFolder::CLIENT_ID => $_SESSION['organization']->id,
                ArchFolder::EMPLOYEE_ID => 0
            ]));


        } catch (ArchFolderException $e) {

            $this->Smarty->assign('folders', []);

        }

        try {

            $this->Smarty->assign('tags', ClientList::read([
                ClientList::CLIENT_ID => $_SESSION['organization']->id,
                ClientList::TYPE => ClientList::CLIENT_LIST_ARCHIVE_TRANSACTION_TAGS
            ]));

        } catch (ClientListException $e) {

        }


    }

    public function employeeDataReportPrint($parm, $post)
    {

        $this->employeeDataReport($parm, $post);

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));

    }

    private function getEmployeeRequests($employee)
    {

        $configTypesArray = [];

        $request = RequestModel::withTableNames()
            ->where('wf_request_user_id', '=', (int) $employee->id)
            ->totalStatus()
            ->first();

        $this->Smarty->assign('request', $request);

        try {
            $configTypesArray = ClientConfiguration::getClientConfigurationArray(
                (int) $_SESSION['organization']->id
            );

            $this->Smarty->assign('clientConfigurationArray', $configTypesArray);
        } catch (ClientConfigurationException $e) {
            $this->Smarty->assign('clientConfigurationArray', []);
        }


        if (in_array(Setting::EMPLOYEE_DASHBOARD_LEAVE_REQUEST, $configTypesArray)) {
            $this->Smarty->assign(
                'hr_lev_list',
                Request::getEmployeeFinshedRequests(
                    (int) $employee->id,
                    Request::REQUEST_TYPE_HR_LEAVE
                )
            );
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_RETREAT_LEAVE_REQUEST, $configTypesArray)) {
            $this->Smarty->assign(
                'hr_retreat_leave',
                Request::getEmployeeFinshedRequests(
                    (int) $employee->id,
                    Request::REQUEST_TYPE_HR_LEAVE_RETREAT,
                    $_SESSION['prg']->id
                )
            );
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_PERMISSION_REQUEST, $configTypesArray)) {
            $this->Smarty->assign(
                'hr_perm_list',
                Request::getEmployeeFinshedRequests(
                    (int) $employee->id,
                    Request::REQUEST_TYPE_HR_PERMISSION
                )
            );
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_ADVANCE_REQUEST, $configTypesArray)) {
            $this->Smarty->assign(
                'hr_advancerequest_list',
                Request::getEmployeeFinshedRequests(
                    (int) $employee->id,
                    Request::REQUEST_TYPE_HR_ADVANCE_REQUEST
                )
            );
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_MANDATE_REQUEST, $configTypesArray)) {
            $this->Smarty->assign(
                'hr_mndt_list',
                Request::getEmployeeFinshedRequests(
                    (int) $employee->id,
                    Request::REQUEST_TYPE_HR_MANDATE
                )
            );

            $this->Smarty->assign(
                'hr_mndtfees_list',
                Request::getEmployeeFinshedRequests(
                    (int) $employee->id,
                    Request::REQUEST_TYPE_HR_MANDATE_FEES
                )
            );
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_ADDITIONAL_WORK_REQUEST, $configTypesArray)) {
            $this->Smarty->assign(
                'hr_otwrk_list',
                Request::getEmployeeFinshedRequests(
                    (int) $employee->id,
                    Request::REQUEST_TYPE_HR_OUTWORK
                )
            );

            $this->Smarty->assign(
                'hr_outworkfees_list',
                Request::getEmployeeFinshedRequests(
                    (int) $employee->id,
                    Request::REQUEST_TYPE_HR_OUTWORK_FEES
                )
            );

        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_FINEXCH_REQUEST, $configTypesArray)) {
            $this->Smarty->assign(
                'fin_exch_request_list',
                Request::getEmployeeFinshedRequests(
                    (int) $employee->id,
                    Request::REQUEST_TYPE_FIN_EXCHANGE,
                    Program::PROGRAM_EMPLOYEE_DASHBOARD_P002
                )
            );
        }

        if (in_array(Setting::EMPLOYEE_DASHBOARD_LEAVE_CREDIT_REQUEST, $configTypesArray)) {
            $this->Smarty->assign(
                'leave_credit_request_list',
                Request::getEmployeeFinshedRequests(
                    (int) $employee->id,
                    Request::REQUEST_TYPE_HR_LEAVE_CREDIT_EDIT,
                    Program::PROGRAM_EMPLOYEE_DASHBOARD_P002
                )
            );
        }
    }

    private function getEmployeeAccountabilties($employee)
    {
        $this->Smarty->assign(
            'attendanceRequests',
            Request::getEmployeeFinshedRequests($employee->id, Request::REQUEST_TYPE_HR_ATTENDANCE)
        );

        $this->Smarty->assign(
            'latenciesRequests',
            Request::getEmployeeFinshedRequests($employee->id, Request::REQUEST_TYPE_HR_LATENCY)
        );
    }

    private function getEmployeeCommittees($employee)
    {
        try {
            $committees = CommitteeMember::read([
                CommitteeMember::USER_ID => $employee->id
            ]);
            ;

        } catch (CommitteeMemberException $e) {
            $committees = [];
        }
        try {
            $committees = collect($committees);
            $employeeCommittees = Committee::convertFromQB(DB::table('es_committee')
                ->whereIn(Committee::ID, $committees->pluck('committee_id'))
                ->get());

            foreach ($employeeCommittees as $committee) {
                $committee->memberships = $committees
                    ->where('committee_id', $committee->id);
            }
            ;

        } catch (CommitteeException $e) {
            $employeeCommittees = [];
        }
        $this->Smarty->assign('employeeCommittees', $employeeCommittees);
    }

    private function getEmployeeJobs($employee)
    {
        try {
            if ($employee->id) {
                $employeeJobs = JobModel::userJobs($employee->id);
                $this->Smarty->assign('employeeJobs', $employeeJobs);
            }
        } catch (Exception $e) {
            $this->Smarty->assign('employeeJobs', []);
        }
    }

    public function finaccsheet($parm)
    {
        list($yearId, $account, $fromDate, $toDate) = $parm;

        $_SESSION['s_mediacenter_parameters'] = [
            'year_id' => $yearId,
            'account' => $account,
            'from_date' => $fromDate,
            'to_date' => $toDate,
        ];

        $year = Year::find($yearId);
        $fromDate = new DateTime($fromDate);
        $toDate = new DateTime($toDate);

        $report = new AccountSheet($year, (int) $account, $fromDate, $toDate, FinTransaction::ENTERY_NUM);
        $this->Smarty->assign('report', $report);
    }

    public function costCenterAccountSheet($parm, $post)
    {
        list($yearId, $account, $fromDate, $toDate, $type, $id) = $parm;
        // Determine if this cost center or
        $costCenter = $tagCenter = 0;
        if ($type && $type == 'cost') {
            $costCenter = $id;
        } elseif ($type && $type == 'tag') {
            $tagCenter = $id;
        }
        $_SESSION['s_mediacenter_parameters'] = [
            'year_id' => $yearId,
            'account' => $account,
            'from_date' => $fromDate,
            'to_date' => $toDate,
            'type' => $type,
            'id' => $id,
        ];
        try {
            $year = FinYear::readID((int) $yearId);
        } catch (FinYearException $e) {
            $year = null;
        }
        $fromDate = new DateTime($fromDate);
        $toDate = new DateTime($toDate);

        $report = new FinanceReportCostOrTagCenterAccountSheet($_SESSION['organization'], $year, $account, $fromDate, $toDate, $costCenter, $tagCenter);
        //        return $report->transactions;
        $this->Smarty->assign('report', $report);
    }

    public function finaccsheetprint($parm, $post)
    {
        $year = Year::find((session('s_mediacenter_parameters.year_id')));
        $fromDate = new DateTime(snsoDate(session('s_mediacenter_parameters.from_date')));
        $toDate = new DateTime(snsoDate(session('s_mediacenter_parameters.to_date')));
        $account = $parm[0];

        $report = new AccountSheet($year, $account, $fromDate, $toDate, FinTransaction::ENTERY_NUM);
        $this->Smarty->assign('report', $report);

        switch ($parm[1]) {
            case 'landscape':
                DocumentProcessor::outputPDF(
                    $this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()),
                    "",
                    "",
                    "",
                    true
                );
                break;
            case 'portrait':
                DocumentProcessor::outputPDF(
                    $this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig())
                );
                break;
        }
    }

    public function costCenterAccountSheetPrint($parm, $post)
    {
        try {
            $year = FinYear::readID((int) $_SESSION['s_mediacenter_parameters']['year_id']);
        } catch (FinYearException $e) {
            $year = null;
        }
        $fromDate = new DateTime($this->Date->get_date('ad', $_SESSION['s_mediacenter_parameters']['from_date']));
        $toDate = new DateTime($this->Date->get_date('ad', $_SESSION['s_mediacenter_parameters']['to_date']));
        $type = $_SESSION['s_mediacenter_parameters']['type'];
        $id = $_SESSION['s_mediacenter_parameters']['id'];
        // Determine if this cost center or
        $costCenter = $tagCenter = 0;
        if ($type && $type == 'cost') {
            $costCenter = $id;
        } elseif ($type && $type == 'tag') {
            $tagCenter = $id;
        }

        $account = $parm[0];

        $report = new FinanceReportCostOrTagCenterAccountSheet($_SESSION['organization'], $year, $account, $fromDate, $toDate, $costCenter, $tagCenter);
        $this->Smarty->assign('report', $report);

        switch ($parm[1]) {
            case 'landscape':
                DocumentProcessor::outputPDF(
                    $this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()),
                    "",
                    "",
                    "",
                    true
                );
                break;
            case 'portrait':
                DocumentProcessor::outputPDF(
                    $this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig())
                );
                break;
        }
    }

    public function requestMoveOnNotification($parm, $post)
    {

        try {
            $notification = sh_notif::readByID((int) $parm[0]);
        } catch (ModelException $e) {
            $notification = null;
        }

        if ($notification) {

            try {

                $notification->sh_notif_status = 0;
                $notification->update();

            } catch (ModelException $e) {

            }

            $this->Smarty->assign('notification', $notification);

            try {
                $this->Smarty->assign('request', Request::readID((int) $notification->sh_notif_row_id));
            } catch (RequestException $e) {

            }

        }
    }

    
public function secondPayrollBrowseTemplate($parm)
{
    $batchId = isset($parm[0]) ? (int) $parm[0] : 0;
    $id = isset($parm[1]) ? (int) $parm[1] : 0;
    $type = isset($parm[2]) ? $parm[2] : null;
    $this->Smarty->assign('type', $type);
    $batch = PayrollBatchModel::find($batchId);

    if (!$batch) {
        $_SESSION['notification'] = [
            'type' => 'error',
            'message' => 'الدفعة غير موجودة'
        ];
        $this->Smarty->assign('payrollEmployees', []);
        $this->Smarty->assign('batch', null);
        $this->Smarty->assign('tempsArray', []);
        return [
            'status' => 'no_batch',
            'payrollEmployees' => [],
        ];
    }

    $this->Smarty->assign('batch', $batch);
    $this->Smarty->assign('tempsArray', explode(',', $batch->templates_based_on));
    $_SESSION['s_payslipPreparation_batch_id'] = $batchId;

    // Set session parameters if not set
    if (!isset($_SESSION['parameters']['from_date']) || !isset($_SESSION['parameters']['to_date'])) {
        $_SESSION['parameters']['from_date'] = $batch->payroll_batch_from_date;
        $_SESSION['parameters']['to_date'] = $batch->payroll_batch_to_date;
    }
    if (!isset($_SESSION['batch_to_edit_id'])) {
        $_SESSION['batch_to_edit_id'] = $batchId;
    }

    // Initialize unit IDs for fetching employees
    $unitIds = [];
    
    if ($type === 'unit' && $id) {
        try {
            $unit = Unit::readID($id);
            $this->Smarty->assign('unit', $unit);
            $unitIds = [$unit->id];
            // جلب الموظفين في الوحدة المسجلين في المسير
            $employees = PayrollTransaction::where('prl_trans_batch_id', $batch->payroll_batch_id)
                ->whereIn('prl_trans_user_id', function($query) use ($unitIds) {
                    $query->select('sh_uao_user_id')
                        ->from('sh_uao')
                        ->whereIn('sh_uao_job_unt_id', $unitIds)
                        ->where('sh_uao_basic', 1)
                        ->where('sh_uao_quit', 0)
                        ->where('sh_uao_deleted', 0);
                })
                ->get();
        } catch (UnitException $e) {
            
            $this->Smarty->assign('payrollEmployees', []);
            return [
                'status' => 'error',
                'message' => 'خطأ في جلب الوحدة',
                'payrollEmployees' => [],
            ];
        }
    } elseif ($type === 'template' && $id) {
        try {
            $template = PayrollTemplate::readID((int) $id);
            $this->Smarty->assign('template', $template);
            // جلب الموظفين المرتبطين مباشرة بهذا القالب فقط
            $employees = Vacant::getEmployeesByTemplateId($batch->org_id, [$template->id]);
        } catch (PayrollTemplateException $e) {
            
            $this->Smarty->assign('payrollEmployees', []);
            return [
                'status' => 'error',
                'message' => 'خطأ في جلب القالب',
                'payrollEmployees' => [],
            ];
        }
    } else {
        // Use unit_ids from batch
        $unitIds = json_decode($batch->unit_id, true) ?? [];
        // Optionally include template_ids if needed
        $templateIds = json_decode($batch->templates_ids, true) ?? [];
        if (!empty($templateIds)) {
            $templateUnitIds = [];
            foreach ($templateIds as $tid) {
                $emps = Vacant::getEmployeesByTemplateId($batch->org_id, [$tid]);
                foreach ($emps as $emp) {
                    if (!empty($emp->job_unt_id)) {
                        $templateUnitIds[] = $emp->job_unt_id;
                    }
                }
            }
            $unitIds = array_unique(array_merge($unitIds, $templateUnitIds));
        }
        // جلب الموظفين بناءً على الوحدات
        $employees = empty($unitIds) ? collect([]) : Vacant::getEmployeesBasicInUnits($unitIds);
    }

    $payrollEmployees = [];

    foreach ($employees as $employee) {
        $userId = $employee->user_id;
        
        // Validate userId
        if (empty($userId)) {
            
            continue;
        }

        // Get job details
        try {
            $job = Vacant::getEmployeeJobs(241, $userId, Vacant::EMPLOYEE_BASIC_VACANT);
        } catch (Exception $e) {
            
            $job = (object) ['jobObject' => (object) ['sh_job_name' => 'غير محدد']];
        }

        // Fetch payroll details
        try {
            $result = Vacant::getEmployeePayrollDetails($userId, session('batch_to_edit_id'));
        } catch (Exception $e) {
            
            continue;
        }

        // Fetch transaction data if batch is saved
        $transaction = null;
        if (isset($_SESSION['batch_to_edit_id'])) {
            // استبدال forBatch باستعلام مباشر
            $transaction = PayrollTransaction::where('prl_trans_batch_id', $batch->payroll_batch_id)
                ->where(PayrollTrans::USER_ID, $userId)
                ->first();
        }

        // Create payroll employee object
        $payrollEmployee = new stdClass();
        $payrollEmployee->prl_trans_user_id = $userId;
        $payrollEmployee->prl_trans_uao_id = $employee->id;
        $payrollEmployee->prl_trans_template_id = $employee->id;
        $payrollEmployee->prl_trans_basic_salary = $result['salary'];
        $payrollEmployee->prl_trans_allowances = $result['allowances'];
        $payrollEmployee->prl_trans_deductions = $result['deductions'];
        $payrollEmployee->prl_trans_net = $result['net'];
        $payrollEmployee->prl_trans_net_after_additions = $result['net'];
        $payrollEmployee->prl_trans_amount_paid = $result['paid'];
        $payrollEmployee->prl_trans_amount_not_paid = $result['not_paid'];
        $payrollEmployee->prl_trans_amount_comment = $result['amount_comment'];
        $payrollEmployee->prl_trans_deductions_list = json_encode($result['extraString']->deductions_list ?? []);
        $payrollEmployee->prl_trans_attendance_list = json_encode($result['extraString']->attendance_list ?? []);
        $payrollEmployee->prl_trans_latency_list = json_encode($result['extraString']->latency_list ?? []);
        $payrollEmployee->prl_trans_installments_list = json_encode($result['extraString']->installments_list ?? []);
        $payrollEmployee->prl_trans_employeeAbsenceDays = $result['extraString']->employeeAbsenceDays ?? 0;
        $payrollEmployee->prl_trans_extravalue = $result['extraValue'];
        $payrollEmployee->prl_trans_confirm_status = $result['confirm_status'];
        $payrollEmployee->prl_trans_batch_id = $batchId;
        $payrollEmployee->prl_trans_org_id = $_SESSION['organization']->id;
        $payrollEmployee->prl_trans_created_by = $_SESSION['user']->id;
        $payrollEmployee->prl_trans_created_date = date('Y-m-d');
        $payrollEmployee->prl_trans_update_status = 1;
        $payrollEmployee->prl_trans_allowancesDetails = json_encode($result['allowancesDetails']);
        $payrollEmployee->prl_trans_deductionDetails = json_encode($result['deductionDetails']);
        $payrollEmployee->prl_trans_extra_addition = $transaction ? $transaction->prl_trans_extra_addition : 0;
        $payrollEmployee->jobObject = $job->jobObject;
        $payrollEmployee->job_name = $job->jobObject->sh_job_name ?? 'غير محدد';
        // Get fingerprint device number - try multiple methods to ensure we get it
        $deviceNum = '';
        if (isset($employee->sh_uao_att_device_num) && !empty($employee->sh_uao_att_device_num)) {
            $deviceNum = $employee->sh_uao_att_device_num;
        } else {
            // Fallback: get from database directly using user ID
            try {
                $vacantRecord = Vacant::getEmployeeBasicVacantEntity($userId);
                if ($vacantRecord && isset($vacantRecord->sh_uao_att_device_num)) {
                    $deviceNum = $vacantRecord->sh_uao_att_device_num;
                }
            } catch (Exception $e) {
                // If all fails, try to get from user object if it exists
                if (isset($employee->userObject->sh_uao_att_device_num)) {
                    $deviceNum = $employee->userObject->sh_uao_att_device_num;
                }
            }
        }
        $payrollEmployee->att_device_num = $deviceNum;

        // Fetch additions from cache or database
        $cacheKey = $userId . '_extraDetails';
        $cachedAdditions = SnsoCache::get($cacheKey);
        if ($cachedAdditions) {
            $payrollEmployee->additions_list = json_encode($cachedAdditions->additions_list ?? []);
            $payrollEmployee->extra_addition = $cachedAdditions->additions_list_amount ?? 0;
        } elseif ($transaction) {
            $payrollEmployee->additions_list = $transaction->additions_list ?? json_encode([]);
            $payrollEmployee->extra_addition = $transaction->extra_addition ?? 0;
        } else {
            $payrollEmployee->additions_list = json_encode($result['extraString']->additions_list ?? []);
            $payrollEmployee->extra_addition = $result['extra_addition'] ?? 0;
        }

        // Prepare extraValueWithDetails
        $additions_list = json_decode($payrollEmployee->additions_list, true) ?? [];
        $extraValueWithDetails = [];
        if (!empty($additions_list)) {
            foreach ($additions_list as $deductionId) {
                try {
                    $addition = HRDeductAdditionRequest::readID($deductionId);
                    $extraValueWithDetails[] = [
                        'table' => 'hr_deductaddition',
                        'is_absent' => 0,
                        'desc' => $addition->reasons ?? 'إضافة',
                        'value' => $addition->amount,
                        'inout' => 'in',
                        'id' => $deductionId
                    ];
                } catch (HRDeductAdditionRequestException $e) {
                    dd($e);
                }
            }
        }

        // Add deductions from original extraValueWithDetails
        $originalExtraValueWithDetails = $this->calculateUserExtraValue([
            'employeeAbsenceDays' => $result['extraString']->employeeAbsenceDays ?? 0,
            'deductions_list' => $result['extraString']->deductions_list ?? [],
            'attendance_list' => $result['extraString']->attendance_list ?? [],
            'latency_list' => $result['extraString']->latency_list ?? [],
            'installments_list' => $result['extraString']->installments_list ?? [],
            'calculateExtraValue' => true
        ], [3 => $userId]);

        if (is_array($originalExtraValueWithDetails)) {
            foreach ($originalExtraValueWithDetails as $deduction) {
                if ($deduction['table'] === 'hr_deductaddition' && $deduction['is_absent'] == 0 && ($deduction['inout'] === 'out' || ($deduction['inout'] === 'in' && $deduction['id'] === '17'))) {
                    $extraValueWithDetails[] = $deduction;
                }
            }
        }

        $payrollEmployee->extraValueWithDetails = $extraValueWithDetails;
        $payrollEmployee->prl_trans_extrastring = json_encode(array_merge(
            (array) $result['extraString'],
            ['extraValueWithDetails' => $extraValueWithDetails]
        ));

        // Add calculated fields
        $payrollEmployee->employee_total_salary = $result['total_salary'] - $result['extraValue'];
        $payrollEmployee->salary = $result['salary'];
        $payrollEmployee->deduction = $result['deductions'];
        $payrollEmployee->allowances = $result['allowances'];
        $payrollEmployee->extraValue = $result['extraValue'];
        $payrollEmployee->allowancesDetails = $result['allowancesDetails'];
        $payrollEmployee->deductionDetails = $result['deductionDetails'];


        // Add to payroll employees array
        $payrollEmployees[] = $payrollEmployee;
    }

    // Assign data to template
    $this->Smarty->assign('payrollEmployees', $payrollEmployees);
}




    public function secondPayrollPrintTemplate($parm)
    {

        $this->secondPayrollBrowseTemplate($parm);

        generatePdf(true);
    }

    private function calculateUserExtraValue($post, $parm)
    {
        if (!isset($post['calculateExtraValue']) || !isset($parm[3]) || empty($parm[3])) {
            
            return [];
        }

        try {
            $userId = (int) $parm[3];
            $employeeSpecificTemplate = EmployeePayrollTemplate::where('user_id', $userId)->get();

            $employee = Vacant::getEmployeeBasicVacantEntity($userId);
            if (!$employee) {
                
                return [];
            }

            $template = PayrollTemplate::readID((int) $employee->sh_uao_payroll_template_id ?? 0);
            if (!$template) {
                
                return [];
            }

            $templateItemsArray = collect([]);
            foreach ($template->rules as $temp) {
                $_template = [];
                $_template['effectInDay'] = $temp->effect_in_day_cost;
                $_template['type'] = $temp->category_id;
                if ($temp->category_id == PayrollRule::SALARY_RULE_BASIC) {
                    $_template['value'] = $employee->sh_uao_salary_basic;
                } else {
                    if ($temp->amount_type == 650) {
                        $_template['value'] = $temp->amount_fixed_value;
                    } else if ($temp->amount_type == 651) {
                        $_template['value'] = $temp->getParentPayrollValue($employee->sh_uao_salary_basic);
                    }
                }
                $templateItemsArray->push($_template);
            }

            foreach ($employeeSpecificTemplate as $temp) {
                $_template = [];
                $_template['effectInDay'] = $temp->template->effect_in_day_cost;
                $_template['type'] = $temp->template->category_id;
                if ($temp->template->amount_type == 650) {
                    $_template['value'] = $temp->template->amount_fixed_value;
                } else if ($temp->template->amount_type == 651) {
                    $_template['value'] = $temp->getParentPayrollValue($employee->sh_uao_salary_basic);
                }
                $templateItemsArray->push($_template);
            }

            // Ensure session parameters are set
            if (!isset($_SESSION['parameters']['from_date']) || !isset($_SESSION['parameters']['to_date'])) {
                $batch = PayrollBatchModel::find(session('batch_to_edit_id'));
                if ($batch) {
                    $_SESSION['parameters']['from_date'] = $batch->from_date;
                    $_SESSION['parameters']['to_date'] = $batch->to_date;
                } else {
                    
                    return [];
                }
            }

            $from = Carbon::parse($_SESSION['parameters']['from_date']);
            $to = Carbon::parse($_SESSION['parameters']['to_date']);

            $batchDayNumber = 30;
            $this->Smarty->assign('batchDayNumber', $batchDayNumber);
            $dayCost = PayrollBatch::calculateDayCost($templateItemsArray, $batchDayNumber);
            $extraValueResult = PayrollTrans::calculateExtraValue($post, $dayCost);
            $extraValue = $extraValueResult['extraPayrollTransValue'];
            $extraValueWithDetails = $extraValueResult['extraArray'];

            if (isset($_SESSION['batch_to_edit_id'])) {
                $payrollBatch = PayrollBatchModel::find(session('batch_to_edit_id'));
                if (!$payrollBatch) {
                    
                    return $extraValueWithDetails;
                }

                $transaction = PayrollTransaction::forBatch($payrollBatch)
                    ->where(PayrollTrans::USER_ID, $userId)
                    ->first();

                if ($transaction) {
                    if (!isset($post['additions_list']) && $transaction->additions_list) {
                        $post['additions_list'] = json_decode($transaction->additions_list, true);
                    }
                    if (!isset($post['additions_list_amount']) && $transaction->additions_list_amount) {
                        $post['additions_list_amount'] = $transaction->additions_list_amount;
                    }

                    $transaction->extravalue = $extraValue;
                    $transaction->deductions_list = $post['deductions_list'];
                    $transaction->attendance_list = $post['attendance_list'];
                    $transaction->latency_list = $post['latency_list'];
                    $transaction->installments_list = $post['installments_list'];
                    $transaction->employeeAbsenceDays = $post['employeeAbsenceDays'];

                    if (isset($post['additions_list'])) {
                        $transaction->additions_list = json_encode($post['additions_list']);
                    }
                    if (isset($post['additions_list_amount'])) {
                        $transaction->additions_list_amount = $post['additions_list_amount'];
                    }

                    $extraString = new stdClass();
                    $extraString->deductions_list = $transaction->deductions_list;
                    $extraString->attendance_list = $transaction->attendance_list;
                    $extraString->latency_list = $transaction->latency_list;
                    $extraString->installments_list = $transaction->installments_list;
                    $extraString->employeeAbsenceDays = $transaction->employeeAbsenceDays;
                    $extraString->extraValueWithDetails = $extraValueWithDetails;
                    $transaction->extrastring = json_encode($extraString);
                    $transaction->update();
                }
            } else {
                $extraD = SnsoCache::get($userId . '_extraDetails') ?? new stdClass();

                if (!isset($post['additions_list']) && isset($extraD->additions_list)) {
                    $post['additions_list'] = $extraD->additions_list;
                }
                if (!isset($post['additions_list_amount']) && isset($extraD->additions_list_amount)) {
                    $post['additions_list_amount'] = $extraD->additions_list_amount;
                }

                $extraD->attendance_list = $post['attendance_list'];
                $extraD->latency_list = $post['latency_list'];
                $extraD->installments_list = $post['installments_list'];
                $extraD->deductions_list = $post['deductions_list'];
                $extraD->employeeAbsenceDays = $post['employeeAbsenceDays'];
                $extraD->extraValue = $extraValue;
                $extraD->extraValueWithDetails = $extraValueWithDetails;

                $extraString = new stdClass();
                $extraString->deductions_list = $extraD->deductions_list;
                $extraString->attendance_list = $extraD->attendance_list;
                $extraString->latency_list = $extraD->latency_list;
                $extraString->installments_list = $extraD->installments_list;
                $extraString->employeeAbsenceDays = $extraD->employeeAbsenceDays;
                $extraString->extraValueWithDetails = $extraD->extraValueWithDetails;
                $extraD->prl_trans_extrastring = json_encode($extraString);

                SnsoCache::update($userId . '_extraDetails', $extraD, 10800);
            }

            return $extraValueWithDetails;

        } catch (Exception $e) {
            
            return [];
        }
    }
}
