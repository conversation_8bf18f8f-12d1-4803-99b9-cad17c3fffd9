<?php

// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_notifications extends Controller
{

    const PAGINATION_LIMIT = 25;

    public function show($parm, $post)
    {

        switch ($parm[0]) {
            case 'update_notification_status':
                try {
                    $notification = Notification::readID((int)$parm[1]);
                    $notification->status = $parm[2];
                    $notification->save();
                } catch (NotificationException $e) {
                }
                break;
            case 'unVisited':
                $_SESSION['s_notification_select_type'] = 'unVisited';
                break;

            case 'Visited':
                $_SESSION['s_notification_select_type'] = 'Visited';
                break;

            case 'All':
                $_SESSION['s_notification_select_type'] = 'All';
                break;
            case 'search':
                $_SESSION['search'] = $post;
                break;
            case 'cancelSearch':
                $_SESSION['search'] = null;
                break;
            default:
                $_SESSION['search'] = null;
                $_SESSION['s_notification_select_type'] = 'All';
        }

        if ($_SESSION['search']) {
            $conditions = "sh_notif_org_id = {$_SESSION['organization']->id} AND sh_notif_to_user_id = {$_SESSION['user']->id}";
            if ($_SESSION['search']['from_user']) {
                $conditions .= " AND sh_notif_from_user_id = {$_SESSION['search']['from_user']}";
            }
            if ($_SESSION['search']['from_date'] && $_SESSION['search']['to_date']) {
                $fromDate = $this->Date->get_date('ad', $_SESSION['search']['from_date']);
                $toDate = $this->Date->get_date('ad', $_SESSION['search']['to_date']);
                $conditions .= " AND sh_notif_created_date BETWEEN '{$fromDate}' AND '{$toDate}'";
            }
            if (!($_SESSION['s_notification_select_type'] == 'All')) {
                $statusString = " AND sh_notif_status = ";
                $statusString .= $_SESSION['s_notification_select_type'] === 'Visited' ? 0 : 1;
            } else {
                $statusString = "";
            }
            $conditions .= $statusString;

            $this->GUI->prepare_paginiation('sh_notif', $conditions, 'string', self::PAGINATION_LIMIT);
        } else {
            $conditions = [
                Notification::ORG_ID => $_SESSION['organization']->id,
                Notification::TO_USER_ID => $_SESSION['user']->id
            ];

            if (!($_SESSION['s_notification_select_type'] == 'All')) {
                if ($_SESSION['s_notification_select_type'] == 'Visited') {
                    $conditions[Notification::STATUS] = 0;
                } elseif ($_SESSION['s_notification_select_type'] == 'unVisited') {
                    $conditions[Notification::STATUS] = 1;
                }
            }

            $this->GUI->prepare_paginiation('sh_notif', $conditions, 'array', self::PAGINATION_LIMIT);
        }

        switch ($_SESSION['s_notification_select_type']) {

            case 'unVisited':
                try {
                    $this->Smarty->assign('notifications',
                        Notification::getUnvisitedNotifications($_SESSION['organization'], $_SESSION['user'], self::PAGINATION_LIMIT, $_SESSION['search'])
                    );
                } catch (NotificationException $e) {
                }

                break;

            case 'Visited':
                try {
                    $this->Smarty->assign('notifications',
                        Notification::getVisitedNotifications($_SESSION['organization'], $_SESSION['user'], self::PAGINATION_LIMIT, $_SESSION['search']));
                } catch (NotificationException $e) {
                }
                break;

            default:
                try {
                    $this->Smarty->assign('notifications',
                        Notification::getNotifications($_SESSION['organization'], $_SESSION['user'], self::PAGINATION_LIMIT, $_SESSION['search']));
                } catch (NotificationException $e) {
                }
                break;
        }


        try {
            $this->Smarty->assign('EmployeeJobNumber',
                Vacant::getEmployeeJobsNumber($_SESSION['organization'], $_SESSION['user']));
        } catch (VacantException $e) {
            $this->Smarty->assign('EmployeeJobNumber', 0);
        }
        $this->Smarty->assign('employees', Vacant::getEmployeeListByEntity($_SESSION['organization']));

    }

    public function requestend($parm, $post, $files)
    {
        switch ($parm[0]) {
            case 'update_notification':
                try {
                    $notification = Notification::readID((int)$parm[1]);
                    $notification->status = 0;
                    $notification->save();
                    $request= $notification->requestObject;
                    $success = $request->wf_request_success;
                    $this->Smarty->assign('notification' , $notification);
                    $this->Smarty->assign('success' , $success);
                } catch (NotificationException $e) {
                    $notification = Notification::readID((int)$parm[1]);
                    $request= $notification->requestObject;
                    $success = $request->wf_request_success;

                    $this->Smarty->assign('notification' , $notification);
                    $this->Smarty->assign('success' , $success);
                }
                break;
        }
    }

}
