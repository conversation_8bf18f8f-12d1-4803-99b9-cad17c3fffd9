<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
use Models\Planning\ClosureChecklist;
use Models\Planning\Member;
use Models\Planning\Task as TaskModel;
use Models\Planning\Project as ProjectModel;

class gnr_X000_projectpreparation extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {

            case 'save_session':

                $_SESSION['s_project_prepare_id'] = $parm[1];
                $_SESSION['s_back_path'] = implode('/', array_slice(explode('/', $this->URL->currentUrlPath()), 8));
                $_SESSION['s_prepare_tab'] = 'ProjectDoc';

                break;

            case 'document':

                $_SESSION['s_prepare_tab'] = 'ProjectDoc';

                $project = null;

                try {
                    $project = Project::readID((int)$_SESSION['s_project_prepare_id']);
                } catch (ProjectException $e) {

                }

                if ($project->requestEntity->wf_request_send_status == Request::REQUEST_IS_NOT_SEND) {

                    Validation::rules($post, [
                        'about' => 'required',
                        'start_date' => 'required|date',
                        'newtask_prov' => 'required|numeric',
                    ]);

                } else {

                    Validation::rules($post, [
                        'about' => 'required',
                    ]);

                }

                if (Validation::check()) {

                    try {

                        $this->updateProjectDoc($post, $parm[1], (int)$project->id);

                    } catch (Exception $e) {

                        if ($e->getCode() === 405060) {

                            Notification::alertMessage(Notification::ERROR, 'ProjectStartDateError');

                        }
                    }
                }

                break;

            case 'team':

                $_SESSION['s_prepare_tab'] = 'ProjectTeam';

                switch ($parm[1]) {

                    case 'insert':

                        Validation::rules($post, [
                            'user_id' => 'required|numeric',
                            'rule_name' => 'required',
                        ]);

                        if (Validation::check()) {

                            $this->insertTeamMemeber($post, $parm[2], $_SESSION['s_project_prepare_id']);

                        }

                        break;

                    case 'update':

                        Validation::rules($post, [
                            'user_id' => 'required|numeric',
                            'rule_name' => 'required',
                        ]);

                        if (Validation::check()) {

                            $this->UpdateTeamMemeber($post, $parm[2], $parm[3], 'team');
                        }
                        break;

                    case 'updateprojectmanagerprivileges':

                        $this->UpdateTeamMemeber($post, $parm[2], $parm[3], 'manager');

                        break;

                    case 'delete':

                        $this->DeleteTeamMemeber($parm[2], $parm[3]);

                        break;

                    case 'move':
                        $this->moveTeamMemberTask($parm[3] , $post["to_user"]  , $post["project_id"]);
                }

                break;

            case 'indicator':

                $_SESSION['s_prepare_tab'] = 'ProjectIndicators';

                switch ($parm[1]) {

                    case 'insert':

                        $this->insertIndicator($post, $parm[2], $_SESSION['s_project_prepare_id']);

                        break;

                    case 'update':

                        $this->updateIndicator($post, $parm[2], $parm[3]);

                        break;

                    case 'delete':

                        $this->deleteIndicator($parm[2], $parm[3]);

                        break;
                }
                break;

            case 'risk':

                $_SESSION['s_prepare_tab'] = 'ProjectRisks';

                switch ($parm[1]) {

                    case 'insert':

                        Validation::rules($post, [
                            'risk' => 'required',
                            'mitigation_strategy' => 'required',
                        ]);

                        if (Validation::check()) {

                            $this->insertRisk($post, $parm[2], $_SESSION['s_project_prepare_id']);

                        }

                        break;

                    case 'update':

                        Validation::rules($post, [
                            'risk' => 'required',
                            'mitigation_strategy' => 'required',
                        ]);

                        if (Validation::check()) {

                            $this->updateRisk($post, $parm[2], $parm[3]);

                        }

                        break;

                    case 'delete':

                        $this->deleteRisk($parm[2], $parm[3]);

                        break;
                }
                break;

            case 'interval':

                switch ($parm[1]) {

                    case 'insert':

                        Validation::rules($post, [
                            'name' => 'required',
                        ]);

                        if (Validation::check()) {

                            $this->insertInterval($post, $parm[2], $_SESSION['s_project_prepare_id']);

                        }

                        break;

                    case 'update':

                        Validation::rules($post, [
                            'name' => 'required',
                        ]);

                        if (Validation::check()) {

                            $this->updateInterval($post, $parm[2], $parm[3]);

                        }

                        break;

                    case 'delete':

                        $this->deleteInterval($parm[2], $parm[3]);

                        break;
                }
                break;

            case 'activity':

                switch ($parm[1]) {

                    case 'insert':

                        Validation::rules($post, [
                            'div_id' => 'required|numeric',
                            'name' => 'required',
                            'about' => 'required',
                            'connect_status' => 'required|numeric',
                            'duration' => 'required|numeric',
                            'assignto' => 'required|numeric',
                            'priority' => 'required|numeric',
                        ]);

                        if (Validation::check()) {

                            if ($_SESSION['s_project_prepare_tokan'] == $parm[2]) {

                                Task::createNewTask($_SESSION['organization'], $_SESSION['user'], (int)$_SESSION['s_project_prepare_id'], $post, 1);

                            }
                        }
                        break;

                    case 'update':

                        Validation::rules($post, [
                            'div_id' => 'required|numeric',
                            'name' => 'required',
                            'about' => 'required',
                            'connect_status' => 'required|numeric',
                            'duration' => 'required|numeric|min:1',
                            'assignto' => 'required|numeric',
                            'priority' => 'required|numeric',
                        ]);

                        if (Validation::check()) {

                            if ($_SESSION['s_project_prepare_tokan'] == $parm[2]) {

                                Task::updateTask((int)$_SESSION['s_project_prepare_id'], (int)$parm[3], $post);

                            }
                        }
                        break;

                    case 'delete':

                        if ($_SESSION['s_project_prepare_tokan'] == $parm[2]) {

                            try {

                                $task = Task::readID((int)$parm[3]);
                                $task->delete();

                                Notification::deletedAlert();

                            } catch (Exception $e) {

                            }

                        }
                        break;

                    case 'duplicate':
                        if($_SESSION['s_mytaskmanager_tokan'] == $parm[2]) {

                            // Do what necessary to move the task

                            $project = ProjectModel::where(Project::ID,$post['project_id'])->with(["members" , "tasks"])->get()->first();

                            if($project){

                                $task = TaskModel::where(Task::ID,$parm[3])->with("relatedUser")->get()->first();

                                $interval_id = $post['interval_id'];

                                $user_id = $post['user_id'];

                                $notification = $task->copyTask($project , $user_id , $interval_id);


                                Notification::alertMessage($notification["type"] , $notification["message"]);

                            }

                        }

                        $_SESSION['s_mytaskmanager_tokan'] = md5(rand(0, 99999));



                        break;
                }
                break;

            case 'closure':
                $_SESSION['s_prepare_tab'] = 'ProjectClousreChecklist';

                switch ($parm[1]) {

                    case 'insert':

                        Validation::rules($post, [
                            'name' => 'required',
                            'about' => 'required',
                            'assignto' => 'required|numeric',
                            'project_id' => 'required|numeric',
                        ]);

                        if (Validation::check()) {

                            if ($_SESSION['s_project_prepare_tokan'] == $parm[2]) {

                                $closure = new ClosureChecklist();
                                $closure->fill($post);
                                $closure->execution = Task::TASK_NOT_DONE;
                                $closure->save();

                                Notification::createdAlert();

                            }
                        }
                        break;

                    case 'update':

                        Validation::rules($post, [
                            'name' => 'required',
                            'about' => 'required',
                            'assignto' => 'required|numeric',
                            'project_id' => 'required|numeric',
                        ]);

                        if (Validation::check()) {

                            if ($_SESSION['s_project_prepare_tokan'] == $parm[2]) {

                                $closure = ClosureChecklist::find($parm[3]);
                                $closure->fill($post);
//                                $closure->execution = Task::TASK_NOT_DONE;
                                $closure->save();

                                Notification::updatedAlert();


                            }
                        }
                        break;

                    case 'delete':

                        if ($_SESSION['s_project_prepare_tokan'] == $parm[2]) {

                            $closure = ClosureChecklist::find($parm[3]);
                            $closure->delete();

                            Notification::deletedAlert();

                        }
                        break;

                    case 'duplicate':
                        if($_SESSION['s_mytaskmanager_tokan'] == $parm[2]) {

                            // Do what necessary to move the task

                            $project = ProjectModel::where(Project::ID,$post['project_id'])->with(["members" , "tasks"])->get()->first();

                            if($project){

                                $task = TaskModel::where(Task::ID,$parm[3])->with("relatedUser")->get()->first();

                                $interval_id = $post['interval_id'];

                                $user_id = $post['user_id'];

                                $notification = $task->copyTask($project , $user_id , $interval_id);


                                Notification::alertMessage($notification["type"] , $notification["message"]);

                            }

                        }

                        $_SESSION['s_mytaskmanager_tokan'] = md5(rand(0, 99999));



                        break;
                }
                break;
        }

        try {
            $project = Project::readID((int)$_SESSION['s_project_prepare_id']);
            $this->Smarty->assign('project', $project);
        } catch (ProjectException $e) {
            $this->Smarty->assign('project', []);
        }

        if ($project) {

            $this->Smarty->assign('support_unit_list', Unit::supportUnitsExceptCurrent($project->unit_id));

            try {

                $this->Smarty->assign('project_risks_list', ProjectRisk::read([
                    ProjectRisk::ORG_ID => $project->org_id,
                    ProjectRisk::PROJECT_ID => $project->id
                ]));

            } catch (ProjectRiskException $e) {

                $this->Smarty->assign('project_risks_list', []);

            }

            try {

                $this->Smarty->assign('intervals_list', collect(ProjectInterval::getIntervalsWithItsTasks($_SESSION['organization'], $project))->sortBy('order'));

            } catch (ProjectIntervalException $e) {

                $this->Smarty->assign('intervals_list', []);

            }

            try {

                $this->Smarty->assign('indicators', Indicator::read([
                    Indicator::ACTUAL_PROJECT_ID => $project->id
                ], [0 => ['property' => Indicator::ID, 'sort' => 'ASC']]));

            } catch (IndicatorException $e) {

                $this->Smarty->assign('indicators', []);

            }

            if (!empty($project->oprplan_id)) {

                try {

                    $this->Smarty->assign('operationalPlan', OperationalPlan::readID((int)$project->oprplan_id));

                } catch (OperationalPlanException $e) {

                    $this->Smarty->assign('operationalPlan', []);

                }

            }

            $closures = ClosureChecklist::with('user')
                ->where('pm_closure_checklists_project_id' , $project->id)
                ->get();

            $users_id = $closures->unique('pm_closure_checklists_assignto')->pluck('pm_closure_checklists_assignto')->toArray();

//            return $users_id;

            $this->Smarty->assign('closures', $closures);
            $this->Smarty->assign('users_closures', $users_id);

        }

        $this->Smarty->assign('provlist', Setting::getList(95));
        $this->Smarty->assign('week_days', Setting::getList(25));
        $this->Smarty->assign('period_type_list', Setting::getList(292));

        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));

    }

    public function projectredirect($parm, $post)
    {
        try {
            $project = Project::readID((int)$parm[0]);
            $this->Smarty->assign('project', $project);
        } catch (ProjectException $e) {
            $this->Smarty->assign('project', []);
        }

        $this->Smarty->assign('manag_users_list', Vacant::getEmployeeListByEntity($project->org_id, Vacant::EMPLOYEE_ALL_VACANT, $project->unit_id));
        $this->Smarty->assign('users_list', Vacant::getEmployeeListByEntity($_SESSION['organization']));

        $_SESSION['s_project_tokan'] = md5(rand(00000, 99999));
        $_SESSION['s_project_tab'] = $parm[1];
    }

    public function projectmanager($parm, $post)
    {

        try {
            $project = Project::readID((int)$parm[0]);
            $this->Smarty->assign('project', $project);
        } catch (ProjectException $e) {
            $this->Smarty->assign('project', []);
        }

        $this->Smarty->assign('manag_users_list', Vacant::getEmployeeListByEntity($project->org_id, Vacant::EMPLOYEE_ALL_VACANT, $project->unit_id));

        $_SESSION['s_project_tab'] = $parm[1];
        $_SESSION['s_project_tokan'] = md5(rand(00000, 99999));
    }

    public function teamadd($parm, $post)
    {

        try {
            $project = Project::readID((int)$parm[0]);
            $this->Smarty->assign('project', $project);
        } catch (ProjectException $e) {
            $this->Smarty->assign('project', []);
        }

        if ($project) {

            if (!isset($project->unit_id)) {
                $project->unit_id = 0;
            }
            $unit_ids_ar[0] = $project->unit_id;
            if ($project->supportunitids != '') {
                $unit_ids_ar[1] = $project->supportunitids;
            }

            if (isset($unit_ids_ar)) {
                $this->Smarty->assign('users_list', Vacant::usersInUnits(implode(',', $unit_ids_ar)));
            }

        }

        $this->Smarty->assign('prvlist', Setting::getList(90));
        $this->Smarty->assign('report_prvlist', Setting::getList(221));
        $this->Smarty->assign('indicatorPrivileges', Setting::getList(242));

        $_SESSION['s_prepare_tab'] = 'ProjectTeam';
        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));

    }

    public function teamedit($parm, $post)
    {

        try {
            $teamMember = ProjectTeamMember::readID((int)$parm[0]);
            $this->Smarty->assign('teamMember', $teamMember);
        } catch (ProjectTeamMemberException $e) {

            $this->Smarty->assign('teamMember', []);
        }

        if ($teamMember) {

            try {

                $project = Project::readID((int)$teamMember->project_id);
                $this->Smarty->assign('project', $project);

                $unit_ids_ar[0] = $project->unit_id;
                if ($project->supportunitids != '') {
                    $unit_ids_ar[1] = $project->supportunitids;
                }


            } catch (ProjectException $e) {
                $this->Smarty->assign('users_list', []);
                $this->Smarty->assign('projectRow', []);
            }
        }


        if (isset($unit_ids_ar)) {
            $this->Smarty->assign('users_list', Vacant::usersInUnits(implode(',', $unit_ids_ar)));
        }

        $this->Smarty->assign('prvlist', Setting::getList(90));
        $this->Smarty->assign('report_prvlist', Setting::getList(221));
        $this->Smarty->assign('indicatorPrivileges', Setting::getList(242));

        $_SESSION['s_prepare_tab'] = 'ProjectTeam';
        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
    }

    public function projectmanagerprivileges($parm, $post)
    {

        try {

            $teamMember = ProjectTeamMember::readID((int)$parm[0]);
            $this->Smarty->assign('teamMember', $teamMember);

        } catch (ProjectTeamMemberException $e) {

            $this->Smarty->assign('teamMember', []);

        }

        $this->Smarty->assign('prvlist', Setting::getList(90));
        $this->Smarty->assign('report_prvlist', Setting::getList(221));
        $this->Smarty->assign('indicatorPrivileges', Setting::getList(242));

        $_SESSION['s_prepare_tab'] = 'ProjectTeam';
        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
    }

    public function teamconfirm($parm, $post)
    {

        try {
            $this->Smarty->assign('teamMember', ProjectTeamMember::readID((int)$parm[0]));
        } catch (ProjectTeamMemberException $e) {
            $this->Smarty->assign('teamMember', []);
        }

        $_SESSION['s_prepare_tab'] = 'ProjectTeam';
        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
    }

    public function riskadd($parm, $post)
    {
        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
        $_SESSION['s_prepare_tab'] = 'ProjectRisks';
    }

    public function riskedit($parm, $post)
    {

        try {
            $this->Smarty->assign('risk', ProjectRisk::readID((int)$parm[0]));
        } catch (ProjectRiskException $e) {
            $this->Smarty->assign('risk', []);
        }

        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
        $_SESSION['s_prepare_tab'] = 'ProjectRisks';
    }

    public function riskconfirm($parm, $post)
    {

        try {
            $this->Smarty->assign('risk', ProjectRisk::readID((int)$parm[0]));
        } catch (ProjectRiskException $e) {
            $this->Smarty->assign('risk', []);
        }

        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
        $_SESSION['s_prepare_tab'] = 'ProjectRisks';
    }

    public function intervaladd($parm, $post)
    {
        $_SESSION['s_prepare_tab'] = 'ProjectTimeTable';
        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
    }

    public function intervaledit($parm, $post)
    {

        try {
            $this->Smarty->assign('interval', ProjectInterval::readID((int)$parm[0]));
        } catch (ProjectIntervalException $e) {
            $this->Smarty->assign('interval', []);
        }

        $_SESSION['s_prepare_tab'] = 'ProjectTimeTable';
        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
    }

    public function intervalconfirm($parm, $post)
    {

        try {
            $interval = ProjectInterval::readID((int)$parm[0]);
            $this->Smarty->assign('interval', $interval);
        } catch (ProjectIntervalException $e) {
            $this->Smarty->assign('interval', []);
        }

        if ($interval) {

            try {

                $connectedTasksNum = Task::count([
                    Task::DIV_ID => $interval->id,
                    Task::PROJECT_ID => $interval->project_id
                ]);

                $this->Smarty->assign('conncectedTasksNum', $connectedTasksNum);

            } catch (TaskException $e) {

            }

            if ($connectedTasksNum) {

                try {

                    $this->Smarty->assign('conncectedTasks', Task::read([
                        Task::DIV_ID => $interval->id,
                        Task::PROJECT_ID => $interval->project_id
                    ]));

                } catch (TaskException $e) {
                    $this->Smarty->assign('conncectedTasks', []);
                }

            }
        }

        $_SESSION['s_prepare_tab'] = 'ProjectTimeTable';
        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
    }

    public function activityadd($parm, $post)
    {
        $project = Models\Planning\Project::find($parm[0])->load("members.user");
//        try {

//            $project = Project::readID((int)$parm[0]);
            // Get rid of duplicated ids on getdate plugin
            $project->project_start_date = $project->start_date;
            $this->Smarty->assign('project', $project);

            $operational_plan = Models\Planning\Operational\OperationalPlan::find($project->oprplan_id);
//            try {
            $this->Smarty->assign('operationalPlan', $operational_plan);
//            } catch (OperationalPlanException $e) {
//            }

//        } catch (ProjectException $e) {
//
//        }

        if ($project) {

            $intervals = Models\Planning\Interval::where(ProjectInterval::PROJECT_ID , $project->id)->orderBy(ProjectInterval::ORDER)->get();
            $this->Smarty->assign('intervals_list', $intervals);
//            try {

//                $this->Smarty->assign('intervals_list', collect(ProjectInterval::read([
//                    ProjectInterval::ORG_ID => $project->org_id,
//                    ProjectInterval::PROJECT_ID => $project->id
//                ]))->sortBy('order'));
//
//            } catch (ProjectIntervalException $e) {
//
//                $this->Smarty->assign('intervals_list', []);
//
//            }

            $tasks = Models\Planning\Task::where(Task::PROJECT_ID , $project->id)->get();
            $this->Smarty->assign('tasks', $tasks);

//            try {
//                $this->Smarty->assign('tasks', Task::read([Task::PROJECT_ID => $project->id]));
//            } catch (TaskException $e) {
//                $this->Smarty->assign('tasks', []);
//            }

        }

        $priority_list = Models\Planning\ProjectPriority::getPriorityList();

        $this->Smarty->assign('priority_list', $priority_list);
        $this->Smarty->assign('connection_list', Setting::getList(180));
        $_SESSION['s_prepare_tab'] = 'ProjectTimeTable';
        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
    }

    public function activityedit($parm, $post)
    {

        try {
            $activity = Task::readID((int)$parm[0]);
            $activity->task_start_date = $activity->start_date;
            $this->Smarty->assign('activity', $activity);

            switch ($activity->connect_status) {

                case 818:

                    $this->Smarty->assign('jscode1', '
                        <script type="text/javascript">
                            $(document).ready(function () {
                                $("#activityConnected").show();
                                $("#activityNotConnected").hide();
                            });
                        </script>');

                    break;

                case 819:

                    $this->Smarty->assign('jscode1', '
                        <script type="text/javascript">
                            $(document).ready(function () {
                                $("#activityConnected").hide();
                                $("#activityNotConnected").show();
                            });
                        </script>');

                    break;
            }

        } catch (TaskException $e) {
            $activity = [];
        }

        if ($activity) {

            try {

                $project = Project::readID((int)$activity->project_id);
                // Get rid of duplicated ids on getdate plugin
                $project->project_start_date = $project->start_date;
                $this->Smarty->assign('projectrow', $project);

            } catch (ProjectException $e) {

            }

            if ($project) {

                try {
                    $this->Smarty->assign('tasks', Task::read([Task::PROJECT_ID => $project->id]));
                } catch (TaskException $e) {
                    $this->Smarty->assign('tasks', []);
                }

                try {

                    $this->Smarty->assign('intervals_list', collect(ProjectInterval::read([
                        ProjectInterval::ORG_ID => $activity->org_id,
                        ProjectInterval::PROJECT_ID => $project->id
                    ]))->sortBy('order'));

                } catch (ProjectIntervalException $e) {

                }

                try {

                    $this->Smarty->assign('indic_list', Indicator::read([
                        Indicator::PROJECT_ID => $activity->project_id
                    ]));

                } catch (IndicatorException $e) {

                    $this->Smarty->assign('indic_list', []);

                }
            }

        }

        $this->Smarty->assign('priority_list', ProjectPriority::getPriorityList());
        $this->Smarty->assign('connection_list', Setting::getList(180));
        $_SESSION['s_prepare_tab'] = 'ProjectTimeTable';
        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
    }

    public function activityconfirm($parm, $post)
    {

        try {
            $this->Smarty->assign('activity', Task::readID((int)$parm[0]));
        } catch (TaskException $e) {
            $this->Smarty->assign('activity', []);
        }

        $_SESSION['s_prepare_tab'] = 'ProjectTimeTable';
        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
    }

    public function browse($parm, $post)
    {
        switch ($parm[0]) {

            case 'save_session':
                $_SESSION['s_project_prepare_id'] = $parm[1];
                $_SESSION['s_prepare_tab'] = 'ProjectDoc';
                break;
        }


        try {

            $project = Project::readID((int)$parm[0]);
            $this->Smarty->assign('project', $project);

        } catch (ProjectException $e) {
        }

        if ($project) {

            try {
                $this->Smarty->assign('intervals_list', collect(ProjectInterval::getIntervalsWithItsTasks($_SESSION['organization'], $project))->sortBy('order'));
            } catch (ProjectIntervalException $e) {
            }

            if (!isset($project->unit_id)) {
                $project->unit_id = 0;
            }

            try {

                $this->Smarty->assign('indicators', Indicator::read([
                    Indicator::ACTUAL_PROJECT_ID => $project->id
                ], [0 => ['property' => Indicator::ID, 'sort' => 'ASC']]));

            } catch (IndicatorException $e) {

                $this->Smarty->assign('indicators', []);

            }

            try {
                $this->Smarty->assign('project_risks_list', ProjectRisk::read([
                    ProjectRisk::ORG_ID => $project->org_id,
                    ProjectRisk::PROJECT_ID => $project->id
                ]));
            } catch (ProjectRiskException $e) {
            }
        }

        $this->Smarty->assign('support_unit_list', Unit::supportUnitsExceptCurrent($project->unit_id));
        $this->Smarty->assign('provlist', Setting::getList(95));
        $this->Smarty->assign('week_days', Setting::getList(25));

    }

    public function archive($parm, $post)
    {
        try {
            $this->Smarty->assign('project', Project::readID((int)$parm[0]));
        } catch (ProjectException $e) {
            $this->Smarty->assign('project', []);
        }

        $_SESSION['s_project_tab'] = $parm[1];
        $_SESSION['s_project_prepare_tokan'] = md5(rand(00000, 99999));
    }

    public function addindicator($parm, $post)
    {

        try {
            $this->Smarty->assign('project', Project::readID((int)$parm[0] ?? 0));
        } catch (ProjectException $e) {
            $this->Smarty->assign('project', []);
        }

        $this->Smarty->assign('units', Setting::getList(98));
        $this->Smarty->assign('indicatorCalculationTypes', Setting::getList(106));
        $_SESSION['s_project_prepare_tokan'] = md5(rand(0000, 9999));
    }

    public function editindicator($parm, $post)
    {

        try {
            $indicator = Indicator::readID((int)$parm[0]);
            $this->Smarty->assign('indicator', $indicator);
        } catch (IndicatorException $e) {
            $this->Smarty->assign('indicator', []);
        }

        if ($indicator) {

            try {
                $this->Smarty->assign('project', Project::readID($indicator->actual_project_id));
            } catch (OperationalPlanException $e) {
                $this->Smarty->assign('project', []);
            }

        }

        $this->Smarty->assign('units', Setting::getList(98));
        $this->Smarty->assign('indicatorCalculationTypes', Setting::getList(106));

        $_SESSION['s_project_prepare_tokan'] = md5(rand(0000, 9999));
    }

    public function confirmindicator($parm, $post)
    {

        try {
            $this->Smarty->assign('indicator', Indicator::readID((int)$parm[0]));
        } catch (IndicatorException $e) {
            $this->Smarty->assign('indicator', []);
        }

        $_SESSION['s_project_prepare_tokan'] = md5(rand(0000, 9999));
    }

    /*
     * Helping functions
     */

    public function updateProjectDoc($post, $tokan, int $projectId)
    {

        $project = Models\Planning\Project::find($projectId);

//        try {
//            $project = Project::readID((int)$projectId);
//        } catch (ProjectException $e) {
//            $project = [];
//        }

        if ($project) {

            $project->fill($post);
            $project->period_type = (int)$post['period_type'];
            $project->supportunitids = implode(',', $post['supportunitids']);
            $project->start_date = $this->Date->get_date('ad', $post['start_date']);

            $project->week_end_list = implode(',', $post['week_end_list']);

            if (empty($project->oprplan_id)) {

                $project->end_date = $this->Date->get_date('ad', $post['end_date']);

//                try {

                $project->save();

                Notification::updatedAlert();

//                } catch (ProjectException $e) {
//                }

            } else {

                $operationPlan = Models\Planning\Operational\OperationalPlan::find($project->oprplan_id);

//                try {
//                    $operationPlan = OperationalPlan::readID((int)$project->oprplan_id ?? 0);
//                } catch (OperationalPlanException $e) {
//                    $operationPlan = null;
//                }

                if ($operationPlan) {
                    $project->end_date = (string)$operationPlan->end_date;
                }

                if (strtotime($project->start_date) >= strtotime($operationPlan->start_date) &&
                    strtotime($project->start_date) <= strtotime($operationPlan->end_date)
                ) {

//                    try {

                    $project->save();
                    Notification::updatedAlert();

//                    } catch (ProjectException $e) {
//                    }

                } else {

                    Notification::alertMessage(Notification::WARNING, 'WrongProjectStartDate');

                }

            }

            $firstInterval = Models\Planning\Interval::where(ProjectInterval::PROJECT_ID , $project->id);

//            try {
//
//
//
//                $firstInterval = ProjectInterval::read([
//                    ProjectInterval::PROJECT_ID => $project->id
//                ], [0 => ['property' => ProjectInterval::ID, 'sort' => 'ASC']])[0];

            $firstInterval->start_date = $project->start_date;
            if ($firstInterval->end_date == '1970-01-01' || empty($firstInterval->end_date)) {
                $firstInterval->end_date = $project->end_date;
            }

            $firstInterval->save();

//            } catch (ProjectIntervalException $e) {
//
//            }

        }

    }

    public function insertTeamMemeber($post, $tokan, $projectId)
    {

        if ($_SESSION['s_project_tokan'] == $tokan) {

            if (ProjectTeamMember::count([
                    ProjectTeamMember::PROJECT_ID => $projectId,
                    ProjectTeamMember::USER_ID => $post['user_id']
                ]) == 0
            ) {

                $mem = new ProjectTeamMember();
                $mem->bindProperties($post);
                $mem->org_id = $_SESSION['organization']->id;
                $mem->project_id = $projectId;
                $mem->prvs = implode(',', $post['prvs']);
                $mem->indicator_privileges = implode(',', $post['indicator_privileges']);
                $mem->report_prvs = implode(',', $post['report_prvs']);
                $mem->created_date = $_SESSION['user']->id;
                $mem->save();

                Notification::createdAlert();

            } else {

                Notification::alertMessage(Notification::WARNING, 'SorryThisProjectMemeberExist');
            }

            $_SESSION['s_project_tokan'] = md5(rand(00000, 99999));
        }
    }

    public function UpdateTeamMemeber($post, $tokan, $id, $type)
    {
        if ($_SESSION['s_project_tokan'] == $tokan) {

            try {

                $mem = ProjectTeamMember::readID((int)$id);
                $mem->bindProperties($post);

            } catch (ProjectTeamMemberException $e) {

            }

            try {

                $project = Project::readID((int)$mem->project_id);

            } catch (ProjectException $e) {

            }

            if ($mem && $project) {

                switch ($mem->user_id) {

                    case $project->manager_user_id:

                        $mem->rule_type = 1;
                        $mem->prvs = '403,402,394,392,1114';
                        $mem->indicator_privileges = '1076,1077,1078,1079';
                        $mem->report_prvs = '997,998,999,1000';

                        break;

                    default:

                        $mem->rule_type = 0;
                        $mem->prvs = implode(',', $post['prvs']);
                        $mem->indicator_privileges = implode(',', $post['indicator_privileges']);
                        $mem->report_prvs = implode(',', $post['report_prvs']);

                        break;

                    case 'team':
                }

                try {

                    $mem->save();
                    Notification::updatedAlert();

                } catch (ProjectTeamMemberException $e) {

                }

            }

            $_SESSION['s_project_tokan'] = md5(rand(00000, 99999));
        }
    }

    public function DeleteTeamMemeber($tokan, $id)
    {
        if ($_SESSION['s_project_tokan'] == $tokan) {

            try {

                $member = ProjectTeamMember::readID((int)$id);
                $member->delete();

                Notification::deletedAlert();

            } catch (ProjectTeamMemberException $e) {
            }

            $_SESSION['s_project_tokan'] = md5(rand(00000, 99999));
        }
    }

    public function insertIndicator($post, $tokan, $projectId)
    {
        if ($_SESSION['s_project_prepare_tokan'] == $tokan) {

            $indicator = new Indicator();
            $indicator->bindProperties($post);
            $indicator->type = Setting::PROJECT_INDICATOR;
            $indicator->strategic_plan_id = 0;
            $indicator->operational_plan_id = 0;
            $indicator->project_id = 0;
            $indicator->correction_status = 0;
            $indicator->current_value = 0;
            $indicator->percentage_achieved = 0;
            $indicator->actual_project_id = $projectId;
            $indicator->save();

            Notification::createdAlert();

        }
    }

    public function updateIndicator($post, $tokan, $id)
    {
        if ($_SESSION['s_project_prepare_tokan'] == $tokan) {

            try {

                $indicator = Indicator::readID((int)$id);
                $indicator->bindProperties($post);
                $indicator->current_value = 0;
                $indicator->percentage_achieved = 0;
                $indicator->save();

                Notification::updatedAlert();

            } catch (IndicatorException $e) {
            }

        }
    }

    public function deleteIndicator($tokan, $id)
    {

        if ($_SESSION['s_project_prepare_tokan'] == $tokan) {

            try {

                (Indicator::readID((int)$id))->delete();
                Notification::deletedAlert();

            } catch (IndicatorException $e) {

            }

        }
    }

    public function insertRisk($post, $tokan, $projectId)
    {
        if ($_SESSION['s_project_prepare_tokan'] == $tokan) {


            try {
                $project = Project::readID((int)$projectId);
            } catch (ProjectException $e) {

            }

            if ($project) {

                $risk = new ProjectRisk();
                $risk->bindProperties($post);
                $risk->org_id = $project->org_id;
                $risk->project_id = $projectId;
                $risk->created_by = $_SESSION['user']->id;
                $risk->created_date = date('Y-m-d');
                $risk->save();

                Notification::createdAlert();

            }


        }
    }

    public function updateRisk($post, $tokan, $id)
    {
        if ($_SESSION['s_project_prepare_tokan'] == $tokan) {

            try {
                $risk = ProjectRisk::readID((int)$id);
                $risk->bindProperties($post);
                $risk->save();

                Notification::updatedAlert();

            } catch (ProjectRiskException $e) {
            }

        }
    }

    public function deleteRisk($tokan, $id)
    {
        if ($_SESSION['s_project_prepare_tokan'] == $tokan) {

            try {

                ProjectRisk::readID((int)$id)->delete();
                Notification::deletedAlert();

            } catch (ProjectRiskException $e) {
            }

        }
    }

    public function insertInterval($post, $tokan, $projectId)
    {
        if ($_SESSION['s_project_prepare_tokan'] == $tokan) {

            try {
                $project = Project::readID((int)$projectId);
            } catch (ProjectException $e) {
                $project = [];
            }

            if ($project) {

                $interval = new ProjectInterval();
                $interval->bindProperties($post);
                $interval->order = count($project->intervals) + 1;
                $interval->org_id = $project->org_id;
                $interval->project_id = $project->id;
                $interval->start_date = $project->start_date;
                $interval->end_date = $project->end_date;
                $interval->created_by = $_SESSION['user']->id;
                $interval->save();

            }

            Notification::createdAlert();

        }
    }

    public function updateInterval($post, $tokan, $id)
    {
        if ($_SESSION['s_project_prepare_tokan'] == $tokan) {

            try {

                $interval = ProjectInterval::readID((int)$id);
                $interval->bindProperties($post);
                $interval->save();

                Notification::updatedAlert();

            } catch (ProjectIntervalException $e) {
            }

            if ($interval->project_id && $interval->id) {
                ProjectInterval::updateProjectInterval((int)$interval->project_id);
            }

        }
    }

    public function deleteInterval($tokan, $id)
    {
        if ($_SESSION['s_project_prepare_tokan'] == $tokan) {

            try {

                ProjectInterval::readID((int)$id)->delete();
                Notification::deletedAlert();

            } catch (ProjectIntervalException $e) {
            }

        }
    }

    public function print($parm, $post)
    {

        $this->browse($parm, $post);

        // Output as PDF
        DocumentProcessor::outputPDF(
            $this->Smarty->fetch(
                DocumentProcessor::setSmartyFetchConfig()
            )
        );

    }

    public function copyTask($parm , $post){
        $members = Member::with(["project" , "project.intervals" , "user"])
//            ->where(ProjectTeamMember::USER_ID , (int)$_SESSION['user']->id)
            ->get();
//            ->unique(ProjectTeamMember::USER_ID);



        $projects = $members->unique(ProjectTeamMember::PROJECT_ID)->pluck('project');
//        $users = $members->pluck('user');
            //->pluck('project');

        $intervals = collect();

        foreach ($projects as $project){
            if($project->intervals){
                $intervals = $intervals->merge($project->intervals->all());
            }
        }
        $projects = $projects->all();
        $intervalsArray = $intervals->toJson();
        $intervals = $intervals->all();
        $membersArray = $members->toJson();


        $this->Smarty->assign('projects', $projects);
        $this->Smarty->assign('intervals', $intervals);
        $this->Smarty->assign('intervalsArray', $intervalsArray);
        $this->Smarty->assign('membersArray', $membersArray);
        $this->Smarty->assign('users', $members);

        $task = TaskModel::find((int)$parm[0]);
        $this->Smarty->assign('task', $task);

        $_SESSION['s_mytaskmanager_tokan'] = md5(rand(00000, 99999));

    }

    public function moveTask($parm , $post){


//        return $parm;
        $members = Member::where(ProjectTeamMember::PROJECT_ID , $parm[1])
            ->where(ProjectTeamMember::ID , '<>' , $parm[0])
            ->with("user")

            ->get();

//        return $members;

        $user = Member::find($parm[0])->user;

        $this->Smarty->assign('members', $members);
        $this->Smarty->assign('user', $user);
        $this->Smarty->assign('project_id', $parm[1]);

        $_SESSION['s_mytaskmanager_tokan'] = md5(rand(00000, 99999));

    }

    private function moveTeamMemberTask($from, $to , $porject_id) {

            $tasks = TaskModel::where(Task::PROJECT_ID , (int)$porject_id)
                ->where(Task::ASSIGNTO , (int)$from)
                ->where(Task::EXECUTION , Task::TASK_NOT_DONE)
//                ->get()
                ->update([
                    Task::ASSIGNTO => (int)$to,
                    Task::START_DATE => \Carbon\Carbon::today()
                ])

            ;

            Notification::updatedAlert();


        }
    public function checkManager($parm, $post)
    {

    }

    public function closureadd($parm, $post){
        $project = \Models\Planning\Project::find($parm[0]);
        $project->load('members.user');
        
        $users = $project->members;
        
        $this->Smarty->assign('project' , $project);
        $this->Smarty->assign('users' , $users);
        
        
    }

    //closureedit
    public function closureedit($parm, $post){
        $closure = ClosureChecklist::find($parm[0]);
        $project = \Models\Planning\Project::find($closure->project_id);
        $project->load('members.user');

        $users = $project->members;

        $this->Smarty->assign('closure' , $closure);
        $this->Smarty->assign('project' , $project);
        $this->Smarty->assign('users' , $users);


    }

    public function closureconfirm($parm, $post){
        $closure = ClosureChecklist::find($parm[0]);
        $this->Smarty->assign('closure' , $closure);
    }
}
