<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_servicetypetwo extends Controller
{

    public function show($parm, $post, $files)
    {
        // no access without serviceID
        if (empty($parm[1])) {
            exit();
        }
        $this->Smarty->assign('parameters', $parm);

        try {

            $this->Smarty->assign('service', es_service::readByID((int)$parm[1] ?? 0));
            $_SESSION['x_servicetypeone_data_tokan'] = md5(rand(0, 1000000));
        } catch (ModelException $e) {

        }
    }

}
