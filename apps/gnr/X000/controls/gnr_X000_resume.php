<?php
// Deny Direct Script Access
use Carbon\Carbon;
use Domain\Assistances\FamilyFormatter;
use Models\Assistances\ExclusionReasons;

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_resume extends Controller
{
    public function show($parm, $post)
    {

        $_SESSION['s_active_cv_tab'] = 'PrimaryData';

        try {

            $user = User::readID((int)$parm[0]);
            $this->Smarty->assign('user', $user);

            /*
         * Education Data
         */
            try {
                $this->Smarty->assign('db_edu_list', DBEducation::read([
                    DBEducation::USER_ID => $user->id
                ], [
                    0 => [
                        'property' => DBEducation::DATE,
                        'sort' => 'ASC'
                    ]
                ]));
            } catch (DBEducationException $e) {
            }

            /*
             * Work Data
             */
            try {
                $this->Smarty->assign('db_work_list', DBWork::read([
                    DBWork::USER_ID => $user->id
                ], [
                    0 => [
                        'property' => DBWork::FROM,
                        'sort' => 'ASC'
                    ]
                ]));
            } catch (DBWorkException $e) {
            }

            /*
             * Family Data
             */
            try {
                $family_list = DBFamily::read([
                    DBFamily::USER_ID => $user->id
                ], [
                    0 => [
                        'property' => DBFamily::BIRTH_DATE,
                        'sort' => 'ASC'
                    ]
                ]);
                return $family_list;

                if(count($family_list) == 0)
                {
                    $family_list = cache('family_' . $user->id);
                }
                return $family_list;
                $this->Smarty->assign('db_family_list', $family_list);

            } catch (DBFamilyException $e) {
                $family_list = cache('family_' . $user->id);
                return $family_list;
                $this->Smarty->assign('db_family_list', $family_list);
            }

            /*
             * Train Data
             */
            try {
                $this->Smarty->assign('db_train_list', DBTraining::read([
                    DBTraining::USER_ID => $user->id
                ]));
            } catch (DBTrainingException $e) {
            }

            /*
             * Income Data
             */
            try {
                $this->Smarty->assign('db_inc_list', DBIncome::read([
                    DBIncome::USER_ID => $user->id
                ]));
            } catch (DBIncomeException $e) {
            }

            try {
                $this->Smarty->assign('houses_list', DBHouses::read([
                    DBHouses::USER_ID => $user->id
                ]));
            } catch (DBHousesException $e) {
            }

            /*
             * Vehical Data
             */
            try {
                $this->Smarty->assign('db_vehicle_list', DBVehical::read([
                    DBVehical::USER_ID => $user->id
                ]));
            } catch (DBVehicalException $e) {
                $this->Smarty->assign('db_vehicle_list', []);
            }

            /*
             * Worker Data
             */
            try {
                $this->Smarty->assign('db_worker_list', DBWorker::read([
                    DBWorker::USER_ID => $user->id
                ]));
            } catch (DBWorkerException $e) {
            }

            /*
             * Debt Data
             */
            try {
                $this->Smarty->assign('db_dept_list', DBDebt::read([
                    DBDebt::USER_ID => $user->id
                ]));
            } catch (DBDeptException $e) {
            }

            /*
             * LiveStock Data
             */
            try {
                $this->Smarty->assign('db_livestock_list', DBLiveStock::read([
                    DBLiveStock::USER_ID => $user->id
                ]));
            } catch (DBLiveStockException $e) {
            }

            /*
             * Housing Data
             */

            $this->Smarty->assign('userclassifications', explode(',', $user->classification));

            $userTabArray = array();
            foreach ($user->classification as $class) {

                if (!empty($class)) {

                    try {

                        $classRow = UserClass::readID((int)$class);
                        $classTabsArray = explode(',', $classRow->userdata_ids);
                        foreach ($classTabsArray as $tab) {
                            if (!empty($tab) && !in_array($tab, $userTabArray)) {
                                $userTabArray[] = $tab;
                            }
                        }

                    } catch (UserClassException $e) {
                    }

                }
            }
            if (count($userTabArray) >= 1) {
                $this->Smarty->assign('allowedUserClasses', Setting::settingsInArray(167, $userTabArray));
            }


            if ($user->housing_type == 367) {
                $this->Smarty->assign('codeone',
                    '<script>$(document).ready(function(){$("#367div").show("fast");$("#368div").hide("fast");});</script>');
            }

            if ($user->housing_type == 368) {
                $this->Smarty->assign('codeone',
                    '<script>$(document).ready(function(){$("#367div").hide("fast");$("#368div").show("fast");});</script>');
            }

        } catch (UserException $e) {
            $this->Smarty->assign('user', []);
        }


    }

    /**
     * Get Name of bank using code of bank
     *
     * @param $banks
     * @param $post
     * @return mixed
     */
    public static  function getBankName($banks, $post)
    {
        if(isset($post['bank_code'])){
            foreach($banks as $bank) {
                if ($post['bank_code'] == $bank->code) {
                    return $bank->translatedName;
                }
            }
        }

        return $post['bank_name'];
    }
    public function exclusion($parm, $post){
        $reasons=ExclusionReasons::all();
//        return $reasons;
        $this->Smarty->assign('reasons', $reasons);

    }
    public function edit($parm, $post, $files)
    {

        global $snso;



        switch ($parm[0]) {

            case 'save_session':
                $_SESSION['s_current_user_id'] = (int)$parm[1] ?? 0;
                $_SESSION['s_back_bnd'] = $parm[2];
                $_SESSION['s_back_prg'] = $parm[3];
                $_SESSION['s_back_opr'] = $parm[4];
                $_SESSION['s_back_sub'] = $parm[5];
                $_SESSION['pagn'] = $parm[6];
                $_SESSION['lang'] = $parm[7];
                $_SESSION['extra'] = $parm[8];
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'PrimaryData';
                $_SESSION['s_active_debt_type_tab'] = 'debt';
                $_SESSION['s_active_debt_data_tab'] = 'person';


                break;

            case 'PrimaryData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'PrimaryData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            $this->Smarty->assign('alertmessage', 'insert');
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $user = User::readID((int)$_SESSION['s_current_user_id']);
                                $user->bindProperties($post);
                                $user->bank_name = $this->getBankName(Setting::getList(291), $post);
                                $user->bank_number = str_replace(' ', '', $post['bank_number']);
                                $user->identity_essue_date = $this->Date->get_date('ad', $post['identity_essue_date']);
                                $user->identity_expairy_date = $this->Date->get_date('ad', $post['identity_expairy_date']);

                                if ($user->address_neighborhood) {

                                    try {
                                        $neighborhood = st_neighborhood::readByID((int)$user->address_neighborhood);
                                    } catch (ModelException $e) {
                                        $neighborhood = null;
                                    }

                                    if ($neighborhood) {
                                        $user->country = $neighborhood->st_neighborhood_country_id;

                                        $user->address_region = $neighborhood->st_neighborhood_region_id;
                                        $user->address_city = $neighborhood->st_neighborhood_city_id;

                                        $userIsHeadOfFamily = \Models\Assistances\Family::where(\AssistanceFamilies::HEAD_OF_THE_FAMILY , $user->id)
                                            ->first();
                                        if($userIsHeadOfFamily){
                                            $userIsHeadOfFamily->country_id = $neighborhood->st_neighborhood_country_id;
                                            $userIsHeadOfFamily->region_id = $neighborhood->st_neighborhood_region_id;
                                            $userIsHeadOfFamily->city_id = $neighborhood->st_neighborhood_city_id;
                                            $userIsHeadOfFamily->neighborhood_id = $neighborhood->st_neighborhood_id;
                                            $userIsHeadOfFamily->save();
                                        }
                                    }

                                }

                                if(empty($post['bank_code'])){
                                    $user->bank_name = $post['bank_name'];
                                    $user->bank_code = "";
                                }
                                $user->birth_date = $this->Date->get_date('ad', $post['birth_date']);
                                $user->age = Helper::calculateAge($user);
                                $user->save();

                                Notification::updatedAlert();

                            } catch (UserException $e) {
                            }

                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            Notification::deletedAlert();
                        }

                        break;

                }
                break;

            case 'EducationalData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'EducationalData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $edu = new DBEducation();
                                $edu->bindProperties($post);
                                $edu->user_id = $_SESSION['s_current_user_id'];
                                $edu->date = $this->Date->get_date('ad', $post['date']);
                                $edu->save();

                                Notification::createdAlert();
                            } catch (DBEducationException $e) {

                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));

                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $edu = DBEducation::readID((int)$parm[3] ?? 0);
                                $edu->bindProperties($post);
                                $edu->date = $this->Date->get_date('ad', $post['date']);
                                $edu->save();
                                Notification::updatedAlert();
                            } catch (DBEducationException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBEducation::readID((int)$parm[3] ?? 0)->delete();
                                $this->DB->delete('db_edu', $parm[3]);
                                Notification::deletedAlert();
                            } catch (DBEducationException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'ExperianceData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'ExperianceData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            $work = new DBWork();
                            $work->bindProperties($post);
                            $work->from = $this->Date->get_date('ad', $post['from']);
                            $work->to = $this->Date->get_date(1, $post['to']);
                            $work->user_id = $_SESSION['s_current_user_id'];
                            if ($post['onwork'] == 'on') {
                                $work->onwork = 'on';
                            } else {
                                $work->onwork = '';
                            }
                            $work->save();
                            Notification::createdAlert();
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $work = DBWork::readID((int)$parm[3]);
                                $work->bindProperties($post);
                                $work->from = $this->Date->get_date('ad', $post['from']);
                                $work->to = $this->Date->get_date('ad', $post['to']);
                                if ($post['onwork'] == 'on') {
                                    $work->onwork = 'on';
                                } else {
                                    $work->onwork = '';
                                }
                                $work->save();
                                Notification::updatedAlert();
                            } catch (DBWorkException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBWork::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBWorkException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'FamilyData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'FamilyData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $family_member = new DBFamily();
                                $family_member->bindProperties($post);
                                $family_member->user_id = $_SESSION['s_current_user_id'];
                                $family_member->birth_date = $this->Date->get_date('ad', $post['birth_date']);
                                $family_member->save();
                                Notification::createdAlert();
                            } catch (DBFamilyException $e) {

                            }

                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $family_member = DBFamily::readID((int)$parm[3]);
                                $family_member->bindProperties($post);
                                $family_member->birth_date = $this->Date->get_date('ad', $post['birth_date']);
                                $family_member->save();
                                Notification::updatedAlert();
                            } catch (DBFamilyException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBFamily::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBFamilyException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'TrainingData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'TrainingData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            $training = new DBTraining();
                            $training->bindProperties($post);
                            $training->user_id = $_SESSION['s_current_user_id'];
                            $training->days = (int)($post['days'] ?? 0);
                            $training->hours = (int)($post['hours'] ?? 0);
                            $training->issuedate = $this->Date->get_date('ad', $post['issuedate']);
                            $training->save();
                            Notification::createdAlert();
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $training = DBTraining::readID((int)$parm[3]);
                                $training->bindProperties($post);
                                $training->issuedate = $this->Date->get_date('ad', $post['issuedate']);
                                $training->save();
                                Notification::updatedAlert();
                            } catch (DBTrainingException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBTraining::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBTrainingException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'Houses':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'HomeAddress';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {

                                $house = new DBHouses();
                                $house->bindProperties($post);
                                $house->user_id = $parm[3];
                                $house->contract_from = $this->Date->get_date('ad', $post['contract_from']);
                                $house->contract_to = $this->Date->get_date('ad', $post['contract_to']);

                                if ($post['status'] == 1) {
                                    $house->status = 1;
                                } else {
                                    $house->status = 0;
                                }

                                $house->save();

                                Notification::createdAlert();

                            } catch (DBHousesException $e) {

                            }

                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));

                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $house = DBHouses::readID((int)$parm[3]);
                                $house->bindProperties($post);
                                $house->contract_from = $this->Date->get_date('ad', $post['contract_from']);
                                $house->contract_to = $this->Date->get_date('ad', $post['contract_to']);

                                if ($post['status'] == 1) {
                                    $house->status = 1;
                                } else {
                                    $house->status = 0;
                                }

                                $house->save();
                                Notification::updatedAlert();
                            } catch (DBHousesException $e) {
                            }

                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBHouses::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBHousesException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'IncomeData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'IncomeData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $income = new DBIncome();
                                $income->bindProperties($post);
                                $income->monthly_incole_vfrom = $this->Date->get_date('ad', $post['monthly_incole_vfrom']);
                                $income->monthly_incole_vto = $this->Date->get_date('ad', $post['monthly_incole_vto']);
                                $income->user_id = $_SESSION['s_current_user_id'];
                                $income->created_date = date('Y-m-d');
                                $income->save();
                                Notification::createdAlert();
                            } catch (DBIncomeException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $income = DBIncome::readID((int)$parm[3]);
                                $income->bindProperties($post);
                                $income->monthly_incole_vfrom = $this->Date->get_date('ad', $post['monthly_incole_vfrom']);
                                $income->monthly_incole_vto = $this->Date->get_date('ad', $post['monthly_incole_vto']);
                                $income->save();
                                Notification::updatedAlert();
                            } catch (DBIncomeException $e) {
                            }

                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBIncome::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBIncomeException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'VehicleData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'VehicleData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $vehicle = new DBVehical();
                                $vehicle->user_id = $_SESSION['s_current_user_id'];
                                $vehicle->bindProperties($post);

                                if ($post['type']) {
                                    $vehicle->type = $post['type'];
                                }
                                if ($post['model']) {
                                    $vehicle->model = $post['model'];
                                }
                                $vehicle->created_date = date('Y-m-d');

                                $vehicle->save();

                                Notification::createdAlert();
                            } catch (DBVehicalException $e) {

                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $vehicle = DBVehical::readID((int)$parm[3]);
                                if ($post['type']) {
                                    $vehicle->type = $post['type'];
                                }
                                if ($post['model']) {
                                    $vehicle->model = $post['model'];
                                }
                                $vehicle->save();

                                Notification::updatedAlert();
                            } catch (DBVehicalException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBVehical::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBVehicalException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'WorkerData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'WorkerData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $worker = new DBWorker();
                                $worker->user_id = $_SESSION['s_current_user_id'];
                                $worker->bindProperties($post);

                                if ($post['stillworking']) {
                                    $worker->stillworking = $post['stillworking'];
                                }
                                if ($post['type']) {
                                    $worker->type = $post['type'];
                                }
                                if ($post['nationality']) {
                                    $worker->nationality = $post['nationality'];
                                }
                                $worker->created_date = date('Y-m-d');

                                $worker->save();

                                Notification::createdAlert();
                            } catch (DBWorkerException $e) {

                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $worker = DBWorker::readID((int)$parm[3]);

                                if ($post['stillworking']) {
                                    $worker->stillworking = $post['stillworking'];
                                }
                                if ($post['type']) {
                                    $worker->type = $post['type'];
                                }
                                if ($post['nationality']) {
                                    $worker->nationality = $post['nationality'];
                                }
                                $worker->save();

                                Notification::updatedAlert();
                            } catch (DBWorkerException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBWorker::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBWorkerException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'DebtData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'DebtData';
                $_SESSION['s_active_debt_type_tab'] = 'debt';
                $_SESSION['s_active_debt_data_tab'] = 'person';
                switch ($parm[1]) {
                    case 'insert':

                        $fromDate = Carbon::parse($post['from_date']);
                        $toDate = Carbon::parse($post['to_date']);

                        if ($_SESSION['s_resume_token'] == $parm[2]) {

                            if ($fromDate->lt($toDate)) {
                                try {
                                    $debt = new DBDebt();
                                    $debt->bindProperties($post);
                                    $debt->user_id = $_SESSION['s_current_user_id'];
                                    $debt->created_by = $_SESSION['s_current_user_id'];
                                    $debt->created_date = date('Y-m-d');
                                    $debt->save();

                                    Notification::createdAlert();
                                } catch (DBDeptException $e) {

                                }
                            } else {
                                Notification::alertMessage(Notification::WARNING, "p_to_date_must_be_greater_than_to_date");
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {

                            $fromDate = Carbon::parse($post['from_date']);
                            $toDate = Carbon::parse($post['to_date']);

                            if ($fromDate->lt($toDate)) {
                                try {
                                    $debt = DBDebt::readID((int)$parm[3]);
                                    $debt->bindProperties($post);
                                    switch ((int)$post['type']) {
                                        case 1206:
                                            $debt->installment_type = null;
                                            break;
                                        case 1207:
                                            $debt->loan_type = null;
                                            break;
                                    }
                                    $debt->save();
                                    Notification::updatedAlert();
                                } catch (DBDeptException $e) {
                                }
                            } else {
                                Notification::alertMessage(Notification::WARNING, "p_to_date_must_be_greater_than_to_date");
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBDebt::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBDeptException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'LiveStockData':
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'LiveStockData';
                switch ($parm[1]) {
                    case 'insert':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $liveStock = new DBLiveStock();
                                $liveStock->user_id = $_SESSION['s_current_user_id'];
                                $liveStock->bindProperties($post);

                                if ($post['amount']) {
                                    $liveStock->amount = $post['amount'];
                                }
                                if ($post['type']) {
                                    $liveStock->type = $post['type'];
                                }
                                $liveStock->created_date = date('Y-m-d');

                                $liveStock->save();

                                Notification::createdAlert();
                            } catch (DBLiveStockException $e) {

                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'update':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                $liveStock = DBLiveStock::readID((int)$parm[3]);
                                if ($post['amount']) {
                                    $liveStock->amount = $post['amount'];
                                }
                                if ($post['type']) {
                                    $liveStock->type = $post['type'];
                                }
                                $liveStock->save();

                                Notification::updatedAlert();
                            } catch (DBLiveStockException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                    case 'delete':
                        if ($_SESSION['s_resume_token'] == $parm[2]) {
                            try {
                                DBLiveStock::readID((int)$parm[3])->delete();
                                Notification::deletedAlert();
                            } catch (DBLiveStockException $e) {
                            }
                            $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
                        }
                        break;

                }
                break;

            case 'tab':
                $_SESSION['s_active_userdata_tab'] = 'userImage';
                $_SESSION['s_active_cv_tab'] = 'PrimaryData';

                break;

            case 'Classifications':
                if ($_SESSION['s_resume_token'] == $parm[1]) {

                    try {

                        $user = User::readID((int)$_SESSION['s_current_user_id']);
                        $user->classification = implode(',', $post['classification']);
                        $user->save();

                        Notification::updatedAlert();

                    } catch (UserException $e) {
                    }

                    $_SESSION['s_active_userdata_tab'] = 'userClassification';
                    $_SESSION['s_resume_token'] = md5(rand(0000, 9999));

                }
                break;

            case 'documents':

                switch ($parm[1]) {

                    case 'addProfilePicture':

                        if ($_SESSION['s_resume_token'] === $parm[2]) {

                            try {

                                $document = new Document();
                                $document->bindProperties($post);
                                $document->fileArray = $files['fileArray'];
                                $document->client_id = $_SESSION['organization']->id;
                                $document->user_id = $_SESSION['s_current_user_id'];
                                $document->operation_code = 'UserProfilePicture';
                                $document->table_name = 'sh_user';
                                $document->row_id = $_SESSION['s_current_user_id'];
                                $document->created_by = $_SESSION['s_current_user_id'];
                                $document->created_date = date('Y-m-d');
                                $document->save();

                                Notification::createdAlert();

                            } catch (DocumentException $e) {

                            }

                        }

                        $_SESSION['s_active_userdata_tab'] = 'userImage';
                        break;

                    case 'editProfilePicture':

                        if ($_SESSION['s_resume_token'] === $parm[2]) {

                            try {

                                $document = Document::readID((int)$parm[3] ?? 0);
                                $document->bindProperties($post);
                                $document->update();

                                Notification::updatedAlert();

                            } catch (DocumentException $e) {

                            }

                        }
                        $_SESSION['s_active_userdata_tab'] = 'userImage';
                        break;

                    case 'deleteProfilePicture':

                        if ($_SESSION['s_resume_token'] === $parm[2]) {

                            try {
                                $document = Document::readID((int)$parm[3] ?? 0);
                                $document->delete();

                                Notification::deletedAlert();
                            } catch (DocumentException $e) {

                            }

                        }
                        $_SESSION['s_active_userdata_tab'] = 'userImage';
                        break;

                    case 'activationProfilePicture':

                        if ($_SESSION['s_resume_token'] === $parm[2]) {

                            try {

                                $document = Document::readID((int)$parm[3] ?? 0);
                                $document->activate();

                                Notification::updatedAlert();

                            } catch (DocumentException $e) {

                            }

                            try {

                                User::updateCurrentUserSessionObject();

                            } catch (UserException $e) {

                            }

                        }
                        $_SESSION['s_active_userdata_tab'] = 'userImage';
                        break;
                }
                break;

            default:
                $_SESSION['s_active_userdata_tab'] = 'usercv';
                $_SESSION['s_active_cv_tab'] = 'PrimaryData';
                $_SESSION['s_current_user_id'] = (int)$parm[0] ?? 0;

                break;
        }

        /*
         * PrimaryData Data
         */
        try {

            $user = User::readID((int)$_SESSION['s_current_user_id']);
            
            $this->Smarty->assign('user', $user);
            $this->Smarty->assign('userclassifications', explode(',', $user->classification));

        } catch (UserException $e) {

            $this->Smarty->assign('user', []);

        }
        
        $this->Smarty->assign('social_list', Setting::getList(5));

        try {
            $this->Smarty->assign('nationality_list', Country::read());
            
        } catch (CountryException $e) {
            $this->Smarty->assign('nationality_list', []);
        }

        $this->Smarty->assign('identity_list', Setting::getList(6));

        $this->Smarty->assign('bank_list', Setting::getList(291));

        $this->Smarty->assign('work_status_list', Setting::getList(280));

        $this->Smarty->assign('educational_levels_list', Setting::getList(2));

        try {
            $this->Smarty->assign('neighborhoods', Neighborhood::read([Neighborhood::ORG_ID => $_SESSION['organization']->id, Neighborhood::ACTIVATION => Setting::ACTIVE]));
            
        } catch (NeighborhoodException $e) {
            $this->Smarty->assign('neighborhoods', []);
        }

        /*
         * Education Data
         */
        try {
            $this->Smarty->assign('db_edu_list', DBEducation::read([
                DBEducation::USER_ID => $_SESSION['s_current_user_id']
            ]));
           
        } catch (DBEducationException $e) {
            $this->Smarty->assign('db_edu_list', []);
        }
        
        /*
         * Work Data
         */
        try {
            $this->Smarty->assign('db_work_list', DBWork::read([
                DBWork::USER_ID => $_SESSION['s_current_user_id']
            ], [
                0 => [
                    'property' => DBWork::FROM,
                    'sort' => 'ASC'
                ]
            ]));
        } catch (DBWorkException $e) {
            $this->Smarty->assign('db_work_list', []);
        }

        /*
         * Family Data
         */
        /**
         * user -> member.family
        **/
        $user = \Models\User::with('familyMember.family.members.user.incomes')->where('sh_user_id' , $_SESSION['s_current_user_id'])->first();
        if($user->familyMember){
            $family_list = $user->familyMember->family;
            $this->Smarty->assign('family_id', $user->familyMember->family->id);
//            return $user->familyMember->family;
            $this->Smarty->assign('family_head', $user->familyMember->family->head_of_the_family);

//            $this->smary ;


//            return $family_list;
//        } else {
//            return 'false';
//        }
//            $family_list = \Models\Assistances\Family::with('members.user')->where(AssistanceFamilies::HEAD_OF_THE_FAMILY , $_SESSION['s_current_user_id'])->latest(AssistanceFamilies::ID)->first();
//            if($family_list){

//            return $family_list;
            $family = (new FamilyFormatter($family_list, 'database'))->data;
//            return $family_list;
            $database = $family ? 'database': 'empty';
            $this->Smarty->assign('database', $database);

            $this->Smarty->assign('head_of_family', $family_list->assist_families_head_of_the_family == $_SESSION['s_current_user_id']);


            $this->Smarty->assign('total_family_income', collect($family)->sum('accountable_in_income_calculation'));
//            return $family;
            $this->Smarty->assign('db_family_list', $family);
            $this->Smarty->assign('head_family', $family_list->assist_families_head_of_the_family);

            $this->Smarty->assign('step_completion', 100);
            $this->Smarty->assign('step', 4);
            

        } else {
            $family = cache('family_' . $_SESSION['s_current_user_id']);
            
            $step = $family["step"];
            $family_list = collect($family["data"]);
            $family_list = (new FamilyFormatter($family_list, 'redis'))->data;

            if($family_list){
                
                $this->Smarty->assign('database', 'cache');
                $this->Smarty->assign('step_completion', $step * 25);
                $this->Smarty->assign('step', $step);
                $this->Smarty->assign('total_family_income', collect($family)->sum('accountable_in_income_calculation'));
                $this->Smarty->assign('head_family', $_SESSION['s_current_user_id']);
            } else{
                $this->Smarty->assign('step', 0);
                $this->Smarty->assign('step_completion', 0 * 25);
                $this->Smarty->assign('database', 'empty');
                $this->Smarty->assign('head_family', $_SESSION['s_current_user_id']);
            }
            $this->Smarty->assign('db_family_list', $family_list);
//            $this->Smarty->assign('head_family', $family_list->assist_families_head_of_the_family);
            $this->Smarty->assign('head_of_family', true);
        }


        /*
         * Train Data
         */
        try {
            $this->Smarty->assign('db_train_list', DBTraining::read([
                DBTraining::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBTrainingException $e) {
            $this->Smarty->assign('db_train_list', []);
        }

        /*
         * Income Data
         */
        try {
            $this->Smarty->assign('db_inc_list', DBIncome::read([
                DBIncome::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBIncomeException $e) {
            $this->Smarty->assign('db_inc_list', []);
        }

        /*
         * Houses Data
         */
        try {
            $this->Smarty->assign('houses_list', DBHouses::read([
                DBHouses::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBHousesException $e) {
            $this->Smarty->assign('houses_list', []);
        }

        /*
         * Vehical Data
         */
        try {
            $this->Smarty->assign('db_vehicle_list', DBVehical::read([
                DBVehical::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBVehicalException $e) {
            $this->Smarty->assign('db_vehicle_list', []);
        }

        /*
        * Worker Data
        */
        try {
            $this->Smarty->assign('db_worker_list', DBWorker::read([
                DBWorker::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBWorkerException $e) {
            $this->Smarty->assign('db_worker_list', []);
        }

        /*
        * Debt Data
        */
        try {
            $this->Smarty->assign('db_debt_list', DBDebt::read([
                DBDebt::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBDeptException $e) {
            $this->Smarty->assign('db_debt_list', []);
        }

        /*
        * LiveStock Data
        */
        try {
            $this->Smarty->assign('db_livestock_list', DBLiveStock::read([
                DBLiveStock::USER_ID => $_SESSION['s_current_user_id']
            ]));
        } catch (DBLiveStockException $e) {
            $this->Smarty->assign('db_livestock_list', []);
        }

        $classes_id = explode(',', $user->classification);
        $userTabArray = array();
        foreach ($classes_id as $class) {
            try {

                $classRow = UserClass::readID((int)$class);
                $classTabsArray = explode(',', $classRow->userdata_ids);
                foreach ($classTabsArray as $tab) {
                    if (!empty($tab) && !in_array($tab, $userTabArray)) {
                        $userTabArray[] = $tab;
                    }
                }

            } catch (UserClassException $e) {

            }
        }

        if (count($userTabArray) >= 1) {
//            return $userTabArray;
            $this->Smarty->assign('allowedUserClasses', Setting::settingsInArray(167, $userTabArray));
        }

        // change profile picture
        try {
            $this->Smarty->assign('userProfilePictures', Document::read([
                Document::CLIENT_ID => $_SESSION['organization']->id,
                Document::USER_ID => $_SESSION['s_current_user_id'],
                Document::OPERATION_CODE => 'UserProfilePicture'
            ]));
        } catch (DocumentException $e) {
            $this->Smarty->assign('userProfilePictures', []);
        }

        ///////////////////////////////////////////////////////////////

        $this->Smarty->assign('housing_type_list', Setting::getList(83));
        $this->Smarty->assign('owning_type_list', Setting::getList(84));
        $this->Smarty->assign('housing_size_list', Setting::getList(85));
        $this->Smarty->assign('housing_kind_list', Setting::getList(86));
        $this->Smarty->assign('health_status_list', Setting::getList(269));

        switch ($user->housing_type) {
            case 367:
                $this->Smarty->assign('codeone',
                    '<script>$(document).ready(function(){$("#367div").show("fast");$("#368div").hide("fast");});</script>');
                break;
            case 368:
                $this->Smarty->assign('codeone',
                    '<script>$(document).ready(function(){$("#367div").hide("fast");$("#368div").show("fast");});</script>');
                break;
            default:

                break;
        }

        try {
            $this->Smarty->assign('User_Type_List', UserClass::read([
                UserClass::ORG_ID => $_SESSION['organization']->id
            ]));
        } catch (UserClassException $e) {
        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function print($parm, $post)
    {
        $this->show($parm, $post);

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));
    }

    public function addProfilePicture($parm, $post)
    {

        try {
            $this->Smarty->assign('operation', Operation::readByCode('UserProfilePicture'));
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        try {
            $this->Smarty->assign('document_max_size_limit', number_format((((int)(new ConfigurationParser(CLIENT_CONFIG))->getSection('document')['document_max_size_limit']) / 1024) / 1024, 2, '.', ','));
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('document_max_size_limit', 0);
        }

        $_SESSION['s_resume_token'] = md5(rand(0, 10000000));
    }

    public function editProfilePicture($parm, $post)
    {

        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

    }

    public function deleteProfilePicture($parm, $post)
    {

        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[0] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

    }

    public function Eduadd($parm, $post)
    {
        $this->Smarty->assign('edu_type_list', Setting::getList(2));
        $this->Smarty->assign('edu_field_list', Setting::getList(61));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Eduedit($parm, $post)
    {

        try {
            $this->Smarty->assign('row', DBEducation::readID((int)$parm[0]));
        } catch (DBEducationException $e) {
        }

        $this->Smarty->assign('edu_type_list', Setting::getList(2));
        $this->Smarty->assign('edu_field_list', Setting::getList(61));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Educonfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', DBEducation::readID((int)$parm[0] ?? 0));
        } catch (DBEducationException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Workadd($parm, $post)
    {
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Workedit($parm, $post)
    {

        try {

            $row = DBWork::readID((int)$parm[0]);

            if ($row->onwork == 'on') {
                $this->Smarty->assign('jscode',
                    "<script>$(document).ready(function(){ $('#search1').css('display','none'); });</script>");
            }

            $this->Smarty->assign('row', $row);

        } catch (DBWorkException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Workconfirm($parm, $post)
    {
        try {

            $this->Smarty->assign('row', DBWork::readID((int)$parm[0] ?? 0));

        } catch (DBWorkException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Familyadd($parm, $post)
    {
        $this->Smarty->assign('health_status_list', Setting::getList(4));
        $this->Smarty->assign('relative_list', Setting::getList(3));
        $this->Smarty->assign('edu_level_list', Setting::getList(2));
        $this->Smarty->assign('work_type_list', Setting::getList(60));
        $this->Smarty->assign('identity_list', Setting::getList(6));
        $this->Smarty->assign('housing_status_list', Setting::getList(7));
        $this->Smarty->assign('foster_type_list', Setting::getList(8));
        session('s_active_cv_tab' , 'FamilyData');
        $user = \Models\User::find($parm[0]);
        $this->Smarty->assign('user' , $user);

        $this->Smarty->assign('current_user', $parm[0]);
        $familyDb = \Models\Assistances\Family::with('members.user.documents')->where('assist_families_head_of_the_family' , $parm[0])->latest(AssistanceFamilies::ID)->first();


        //        cache_forget('family_' . $parm[0]);
        $family = cache('family_' . $parm[0]);


        if ($family){
            $this->Smarty->assign('step' , $family['step']);
        }
        else {
            if($familyDb){
                $familyDb = (new FamilyFormatter($familyDb , 'database'))->data;
                $family = cache('family_' . $parm[0] , [
                    "step" => 0,
                    "data" => $familyDb

                ]);
            }
            $this->Smarty->assign('step' , 0);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Familyedit($parm, $post)
    {

        try {
            $this->Smarty->assign('row', DBFamily::readID((int)$parm[0]));
        } catch (DBFamilyException $e) {
        }

        $this->Smarty->assign('health_status_list', Setting::getList(4));
        $this->Smarty->assign('relative_list', Setting::getList(3));
        $this->Smarty->assign('edu_level_list', Setting::getList(2));
        $this->Smarty->assign('work_type_list', Setting::getList(60));
        $this->Smarty->assign('identity_list', Setting::getList(6));
        $this->Smarty->assign('housing_status_list', Setting::getList(7));
        $this->Smarty->assign('foster_type_list', Setting::getList(8));


        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Familyconfirm($parm, $post)
    {

        try {

            $this->Smarty->assign('row', DBFamily::readID((int)$parm[0] ?? 0));
        } catch (DBFamilyException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Trainadd($parm, $post)
    {
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Trainedit($parm, $post)
    {

        try {
            $this->Smarty->assign('row', DBTraining::readID((int)$parm[0]));
        } catch (DBTrainingException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Trainconfirm($parm, $post)
    {

        try {
            $this->Smarty->assign('row', DBTraining::readID((int)$parm[0] ?? 0));
        } catch (DBTrainingException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Housingadd($parm, $post)
    {
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Housingedit($parm, $post)
    {
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Housingconfirm($parm, $post)
    {
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Incomeadd($parm, $post)
    {
        $this->Smarty->assign('work_list', Setting::getList(81));
        $this->Smarty->assign('inc_montyly_list', Setting::getList(82));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Incomeedit($parm, $post)
    {

        try {

            $row = DBIncome::readID((int)$parm[0]);

            if ($row->monthly_income_type == 365) {
                $this->Smarty->assign('codeone',
                    '<script>$(document).ready(function(){$("#365div").show("fast");$("#366div").hide("fast");});</script>');
            }
            if ($row->monthly_income_type == 366) {
                $this->Smarty->assign('codeone',
                    '<script>$(document).ready(function(){$("#365div").hide("fast");$("#366div").show("fast");});</script>');
            }

            $this->Smarty->assign('row', $row);

        } catch (DBIncomeException $e) {

            $this->Smarty->assign('row', []);

        }

        $this->Smarty->assign('work_list', Setting::getList(81));
        $this->Smarty->assign('inc_montyly_list', Setting::getList(82));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Incomeconfirm($parm, $post)
    {

        try {
            $this->Smarty->assign('row', DBIncome::readID((int)$parm[0] ?? 0));
        } catch (DBIncomeException $e) {
            $this->Smarty->assign('row', []);
        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Depitadd($parm, $post)
    {
        $this->Smarty->assign('LoanAdvanceTypes', Setting::getList(273));
        $this->Smarty->assign('LoanTypes', Setting::getList(274));
        $this->Smarty->assign('InstallmentsTypes', Setting::getList(275));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Depitedit($parm, $post)
    {
        try {
            $depit = DBDebt::readID((int)$parm[0]);
            $this->Smarty->assign('row', $depit);
        } catch (DBDeptException $e) {
            $this->Smarty->assign('row', []);
        }

        switch ($depit->type) {

            case 1206:
                $this->Smarty->assign('codeone', '<script>$(document).ready(function(){$("#loanTypes").show("fast");$("#InstallmentTypes").hide("fast");});</script>');
                break;

            case 1207:
                $this->Smarty->assign('codeone', '<script>$(document).ready(function(){$("#loanTypes").hide("fast");$("#InstallmentTypes").show("fast");});</script>');
                break;

        }

        $this->Smarty->assign('LoanAdvanceTypes', Setting::getList(273));
        $this->Smarty->assign('LoanTypes', Setting::getList(274));
        $this->Smarty->assign('InstallmentsTypes', Setting::getList(275));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Depitconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', DBDebt::readID((int)$parm[0]));
        } catch (DBDeptException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Vehicleadd($parm, $post)
    {
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Vehicleedit($parm, $post)
    {
        try {

            $row = DBVehical::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBVehicalException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Vehicleconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', DBVehical::readID((int)$parm[0] ?? 0));
        } catch (DBVehicalException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Workeradd($parm, $post)
    {
        $this->Smarty->assign('country_list', Country::read([]));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Workeredit($parm, $post)
    {
        try {

            $row = DBWorker::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBWorkerException $e) {

            $this->Smarty->assign('row', []);

        }
        $this->Smarty->assign('country_list', Country::read([]));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function Workerconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', DBWorker::readID((int)$parm[0] ?? 0));
        } catch (DBWorkerException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function liveStockadd($parm, $post)
    {
        $this->Smarty->assign('livestock_type_list', Setting::getList(271));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function liveStockedit($parm, $post)
    {
        try {

            $row = DBLiveStock::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBLiveStockException $e) {

            $this->Smarty->assign('row', []);

        }
        $this->Smarty->assign('livestock_type_list', Setting::getList(271));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function LiveStockconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', DBLiveStock::readID((int)$parm[0] ?? 0));
        } catch (DBLiveStockException $e) {
            $this->Smarty->assign('row', []);
        }
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function PersonDebtadd($parm, $post)
    {
        $_SESSION['debt_type'] = 'person';

        $_SESSION['s_active_debt_type_tab'] = 'debt';
        $_SESSION['s_active_debt_data_tab'] = 'person';

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function PersonDebtedit($parm, $post)
    {
        $_SESSION['s_active_debt_type_tab'] = 'debt';
        $_SESSION['s_active_debt_data_tab'] = 'person';

        try {

            $row = DBDebt::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBDeptException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function PersonDebtconfirm($parm, $post)
    {
        try {

            $row = DBDebt::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBDeptException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function BankDebtadd($parm, $post)
    {
        $_SESSION['debt_type'] = 'bank';

        $_SESSION['s_active_debt_type_tab'] = 'debt';
        $_SESSION['s_active_debt_data_tab'] = 'bank';

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function BankDebtedit($parm, $post)
    {
        $_SESSION['s_active_debt_type_tab'] = 'debt';
        $_SESSION['s_active_debt_data_tab'] = 'bank';

        try {

            $row = DBDebt::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBDeptException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function BankDebtconfirm($parm, $post)
    {
        try {

            $row = DBDebt::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBDeptException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function CarDebtadd($parm, $post)
    {
        $_SESSION['debt_type'] = 'car';

        $_SESSION['s_active_debt_type_tab'] = 'premium';
        $_SESSION['s_active_debt_data_tab'] = 'car';

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function CarDebtedit($parm, $post)
    {
        $_SESSION['s_active_debt_type_tab'] = 'premium';
        $_SESSION['s_active_debt_data_tab'] = 'car';
        try {

            $row = DBDebt::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBDeptException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function CarDebtconfirm($parm, $post)
    {
        try {

            $row = DBDebt::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBDeptException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function HouseDebtadd($parm, $post)
    {
        $_SESSION['debt_type'] = 'house';

        $_SESSION['s_active_debt_type_tab'] = 'premium';
        $_SESSION['s_active_debt_data_tab'] = 'house';

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function HouseDebtedit($parm, $post)
    {
        $_SESSION['s_active_debt_type_tab'] = 'premium';
        $_SESSION['s_active_debt_data_tab'] = 'house';
        try {

            $row = DBDebt::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBDeptException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function HouseDebtconfirm($parm, $post)
    {
        try {

            $row = DBDebt::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBDeptException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function OtherDebtadd($parm, $post)
    {
        $_SESSION['debt_type'] = 'other';

        $_SESSION['s_active_debt_type_tab'] = 'premium';
        $_SESSION['s_active_debt_data_tab'] = 'other';

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function OtherDebtedit($parm, $post)
    {
        $_SESSION['s_active_debt_type_tab'] = 'premium';
        $_SESSION['s_active_debt_data_tab'] = 'other';
        try {

            $row = DBDebt::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBDeptException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function OtherDebtconfirm($parm, $post)
    {
        try {

            $row = DBDebt::readID((int)$parm[0]);

            $this->Smarty->assign('row', $row);

        } catch (DBDeptException $e) {

            $this->Smarty->assign('row', []);

        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function HouseAdd($parm, $post)
    {
        $this->Smarty->assign('housing_type_list', Setting::getList(83));
        $this->Smarty->assign('owning_type_list', Setting::getList(84));
        $this->Smarty->assign('housing_size_list', Setting::getList(85));
        $this->Smarty->assign('housing_kind_list', Setting::getList(86));
        $this->Smarty->assign('userId', $parm[0]);

        $isFirstHouse = DB::table(db_houses::class)->where(DBHouses::USER_ID, $parm[0])->doesntExist();
        $this->Smarty->assign('isFirstHouse', $isFirstHouse);

    }

    public function HousesConfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('house', DBHouses::readID((int)$parm[0] ?? 0));
        } catch (DBHousesException $e) {
            $this->Smarty->assign('house', []);
        }

        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));
    }

    public function HousesEdit($parm, $post)
    {
        try {

            $house = DBHouses::readID((int)$parm[0]);

            $this->Smarty->assign('house', $house);

        } catch (DBHousesException $e) {

            $this->Smarty->assign('house', []);

        }

        $this->Smarty->assign('housing_type_list', Setting::getList(83));
        $this->Smarty->assign('owning_type_list', Setting::getList(84));
        $this->Smarty->assign('housing_size_list', Setting::getList(85));
        $this->Smarty->assign('housing_kind_list', Setting::getList(86));
        $this->Smarty->assign('health_status_list', Setting::getList(4));
        $_SESSION['s_resume_token'] = md5(rand(0000, 9999));

        $isFirstHouse = DB::table(db_houses::class)
                ->where(DBHouses::USER_ID, $house->user_id)
                ->count() == 1;
        $this->Smarty->assign('isFirstHouse', $isFirstHouse);
    }


}
