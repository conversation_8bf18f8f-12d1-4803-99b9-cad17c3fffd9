<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_serviceView extends Controller
{

    public function show($parm, $post, $files)
    {
        // no access without serviceID
        if (empty($_GET['applicationID'])) {
            exit();
        }

        $serviceObject = new Service();
        $this->Smarty->assign('parameters', $parm);

        try {

            $application = es_application::readByID($_GET['applicationID']);
            $this->Smarty->assign('application', $application);

            $service = es_service::readByID($application->es_application_service_id);
            $this->Smarty->assign('service', $service);

            $this->Smarty->assign('serviceFormName', $serviceObject->getServiceFormName($service));

        } catch (ModelException $e) {

        }

    }

}
