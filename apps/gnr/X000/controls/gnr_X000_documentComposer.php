<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
use Infrastructure\Common\QRCode\Facades\SnsoQR;

/**
 * 1- Build reports
 * 2- Review Privileges
 */
class gnr_X000_documentComposer extends Controller
{


    public function add($parm, $post, $files)
    {

        $this->Smarty->assign('assignmentTypes', Setting::getList(236));
        $this->Smarty->assign('privacyTypes', Setting::getList(229));
        $this->Smarty->assign('priorities', Setting::getList(225));
        $this->Smarty->assign('replayTypes', Setting::getList(235));
        $this->Smarty->assign('languages', Language::read());

        try {
            $thisFolder = ArchFolder::readID((int)$parm[0]);
        } catch(ArchFolderException $exception){
            $thisFolder = [];
        }

//        return $thisFolder->name;

        $this->Smarty->assign('folder', $thisFolder);

        try {

            $this->Smarty->assign('tags', ClientList::read([
                ClientList::CLIENT_ID => $_SESSION['organization']->id,
                ClientList::TYPE      => ClientList::CLIENT_LIST_ARCHIVE_TRANSACTION_TAGS,
            ]));

        } catch (ClientListException $e) {

        }

        $session = new SessionManager(md5(rand(0000, 9999)));
        $session->set('folder_id', $parm[0]);
        $session->set('back_path', implode('/', array_slice(explode('/', $this->URL->currentUrlPath()), 7)));
        $session->updateDataManipulationToken();
        $session->save();

        $this->Smarty->assign('session', $session->array);

    }

    public function edit($parm, $post, $files)
    {

        try {
            $thisReferral = ArchReferral::readID((int)$parm[1] ?? 0);
            $this->Smarty->assign('thisReferral', $thisReferral);

            $this->Smarty->assign('folder', $thisReferral->folderObject);

            switch ($thisReferral->assignment_type) {
                case ArchReferral::ARCH_REFERRAL_TYPES_MESSAGE:
                    break;
                case ArchReferral::ARCH_REFERRAL_TYPES_TASK:
                    $this->Smarty->assign('jsCode', '
                    <script type="text/javascript">
                        $(document).ready(function () {
                
                            $("#inCaseOfMessage").hide("fast");
                            $("#inCaseOfTask").show("fast");
                
                        });
                    </script>');
                    break;

            }

        } catch (ArchReferralException $e) {
            $this->Smarty->assign('thisReferral', []);
        }

        $this->Smarty->assign('assignmentTypes', Setting::getList(236));
        $this->Smarty->assign('privacyTypes', Setting::getList(229));
        $this->Smarty->assign('priorities', Setting::getList(225));
        $this->Smarty->assign('replayTypes', Setting::getList(235));
        $this->Smarty->assign('languages', Language::read());

        try {

            switch ($thisReferral->belong_to) {

                case ArchReferral::ARCH_BELONG_TO_CLIENT:

                    $this->Smarty->assign('folders', ArchFolder::read([
                        ArchFolder::BELONG_TO   => $thisReferral->belong_to,
                        ArchFolder::CLIENT_ID   => $_SESSION['organization']->id,
                        ArchFolder::EMPLOYEE_ID => 0,
                    ]));

                    break;
                case ArchReferral::ARCH_BELONG_TO_EMPLOYEE:

                    $this->Smarty->assign('folders', ArchFolder::read([
                        ArchFolder::BELONG_TO   => $thisReferral->belong_to,
                        ArchFolder::CLIENT_ID   => $_SESSION['organization']->id,
                        ArchFolder::EMPLOYEE_ID => $thisReferral->employee_id,
                    ]));

                    break;
            }


        } catch (ArchFolderException $e) {

            $this->Smarty->assign('folders', []);

        }

        try {

            $this->Smarty->assign('tags', ClientList::read([
                ClientList::CLIENT_ID => $_SESSION['organization']->id,
                ClientList::TYPE      => ClientList::CLIENT_LIST_ARCHIVE_TRANSACTION_TAGS,
            ]));

        } catch (ClientListException $e) {

        }

        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();

        $this->Smarty->assign('session', $session->array);

    }

    public function compose($parm, $post, $files)
    {

//        dd($parm);
        switch ($parm[0]) {

            case 'new':

                $session = new SessionManager($parm[1]);
                if ($session->getDataManipulationToken() == $parm[2]) {

                    try {

                        $thisFolder = ArchFolder::readID((int) $session->get('folder_id'));

                        try {

                            $transaction = new ArchTransaction();
                            $transaction->bindProperties($post);
                            $transaction->client_id = $thisFolder->client_id;
                            $transaction->number = 0;
                            $transaction->replay_status = ArchTransaction::ARCH_REPLAY_UN_REPLIED;
                            $transaction->archived_status = 0;
                            $transaction->tags = implode(',', $post['tags']);
                            $transaction->created_by = $_SESSION['user']->id;
                            $transaction->created_date = date('Y-m-d');
                            if ($post['company_name '] !== '') {
                                $transaction->company_name = $post['company_name'];
                            }

                            $transaction->create();

                            try {

                                $referral = new ArchReferral();
                                $referral->top_referral_id = 0;
                                $referral->ownership = 1;
                                $referral->type = ArchReferral::ARCH_DESTINATION_INTERNAL;
                                $referral->privileges = implode(',', [
                                    ArchReferral::ARCH_PRIVILEGES_EDIT,
                                    ArchReferral::ARCH_PRIVILEGES_REFERRAL,
                                    ArchReferral::ARCH_PRIVILEGES_ATTACH,
                                    ArchReferral::ARCH_PRIVILEGES_FORWOED,
                                ]);
                                $referral->folder_id = $thisFolder->id;
                                $referral->belong_to = $thisFolder->belong_to;
                                $referral->client_id = $thisFolder->client_id;
                                $referral->employee_id = $thisFolder->employee_id;
                                $referral->transaction_id = $transaction->id;
                                $referral->subject_title = '';
                                $referral->subject_comment = '';
                                $referral->assignment_type = ArchReferral::ARCH_REFERRAL_TYPES_MESSAGE;
                                $referral->priority_status = ArchReferral::ARCH_PRIORITY_NORMAL;
                                $referral->execution_type = ArchReferral::ARCH_TASK_EXECUTION_NOT_DONE;
                                $referral->execution_start_date = date('Y-m-d');
                                $referral->execution_status = ArchReferral::ARCH_TASK_EXECUTION_NOT_DONE;
                                $referral->created_by = $_SESSION['user']->id;
                                $referral->created_date = date('Y-m-d');
                                $referral->save();

                                $session->set('referral_id', $referral->id);

                            } catch (ArchReferralException $e) {

                                $transaction->delete();

                            }

                            Notification::createdAlert();

                        } catch (ArchTransactionException $e) {

                        }

                    } catch (ArchFolderException $e) {

                    }

                    $session->set('manipulation_type', 'edit');
                }

                break;

            case 'browse':

                $session = new SessionManager(md5(rand(0000, 9999)));
                $session->set('referral_id', $parm[1]);
                $session->set('back_path', implode('/', array_slice(explode('/', $this->URL->currentUrlPath()), 9)));
                $session->save();

                if ($parm[5] == 'notifications') {
                    $_SESSION['s_arch_from'] = ArchFolder::LINK_FROM_EMPLOYEE_MAIL_BOX;
                    try {

                        $notif = Notification::readID((int)$parm[2]);
                        $notif->status = 0;
                        $notif->save();

                    } catch (NotificationException $e) {
                    }
                } elseif ($parm[4] == 'archDashboard') {
                    $_SESSION['s_arch_from'] = ArchFolder::LINK_FROM_ARCH_DASHBOARD;
                }


                break;

            case 'browseComment':

                $session = new SessionManager(md5(rand(0000, 9999)));
                $session->set('referral_id', $parm[1]);
                $session->set('back_path', implode('/', array_slice(explode('/', $this->URL->currentUrlPath()), 10)));
                $session->save();

                $this->Smarty->assign('specific_comment_id', $parm[2]);

                if ($parm[5] == 'notifications') {
                    $_SESSION['s_arch_from'] = ArchFolder::LINK_FROM_EMPLOYEE_MAIL_BOX;
                    try {

                        $notif = Notification::readID((int)$parm[3]);
                        $notif->status = 0;
                        $notif->save();

                    } catch (NotificationException $e) {
                    }
                }



                break;

            case 'update':

                $session = new SessionManager($parm[1]);

                if ($session->getDataManipulationToken() == $parm[2]) {

                    try {

                        $referral = ArchReferral::readID($session->get('referral_id'));
                        $referral->folder_id = $post['folder_id'];
                        $referral->save();

                    } catch (ArchReferralException $e) {

                    } catch (ArchTransactionException $e) {

                    }

                    try {

                        $referral->transactionObject->bindProperties($post);
                        $referral->transactionObject->tags = implode(',', $post['tags']);
                        $referral->transactionObject->mediator_date = $this->Date->get_date('ad',
                            $post['mediator_date']);
                        $referral->transactionObject->carrier_date = $this->Date->get_date('ad', $post['carrier_date']);
                        $referral->transactionObject->save();

                    } catch (ArchTransactionException $e) {

                    }

                    Notification::updatedAlert();

                }

                break;

            case 'documents':

                $session = new SessionManager($parm[1]);
                $referral = ArchReferral::readID($session->get('referral_id'));

                switch ($parm[2]) {

                    case 'insert':

                        if ($session->getDataManipulationToken() === $parm[3]) {

                            try {

                                $document = new Document();
                                $document->bindProperties($post);
                                if (empty($post['name'])) {
                                    $document->name = array_first(explode('.', $files['fileArray']['name']));
                                }
                                $document->operation_code = 'documentComposer';
                                $document->table_name = 'arch_transaction';
                                $document->row_id = $referral->transactionObject->id;
                                $document->fileArray = $files['fileArray'];
                                $document->client_id = $referral->client_id;
                                $document->user_id = 0;
                                $document->created_by = $_SESSION['user']->id;
                                $document->created_date = date('Y-m-d');
                                $document->save();

                                Notification::createdAlert();

                            } catch (DocumentException $e) {

                            }

                        }
                        break;

                    case 'update':

                        if ($session->getDataManipulationToken() === $parm[3]) {

                            try {

                                $document = Document::readID((int)$parm[4] ?? 0);
                                $document->bindProperties($post);
                                $document->update();

                                Notification::updatedAlert();

                            } catch (DocumentException $e) {

                            }

                            $this->Smarty->assign('alertmessage', 'update');

                        }

                        break;

                    case 'delete':

                        if ($session->getDataManipulationToken() === $parm[3]) {

                            try {

                                $document = Document::readID((int)$parm[4] ?? 0);
                                $document->delete();

                                Notification::deletedAlert();

                            } catch (DocumentException $e) {

                            }

                        }

                        break;
                }

                break;

            case 'referrals':

                $session = new SessionManager($parm[1]);
                $thisReferral = ArchReferral::readID($session->get('referral_id'));

                switch ($parm[2]) {

                    case 'insert':

                        if ($session->getDataManipulationToken() === $parm[3]) {

                            Validation::rules($post, [
                                'type'                 => 'required',
                                'employee_id'          => 'required_if:type,1032',
                                'assignment_type'      => 'required_if:type,1032',
                                'execution_start_date' => 'required_if:assignment_type,1050',
                                'execution_duration'   => 'required_if:assignment_type,1050|min:1',
                                'priority_status'      => 'required_if:assignment_type,1050',
                            ]);

                            if (Validation::check()) {

                                switch ($post['type']) {

                                    case ArchReferral::ARCH_DESTINATION_INTERNAL:

                                        try {

                                            if (ArchReferral::count([
                                                    ArchReferral::TYPE           => ArchReferral::ARCH_DESTINATION_INTERNAL,
                                                    ArchReferral::BELONG_TO      => ArchReferral::ARCH_BELONG_TO_EMPLOYEE,
                                                    ArchReferral::TRANSACTION_ID => $thisReferral->transactionObject->id,
                                                    ArchReferral::EMPLOYEE_ID    => $post['employee_id'],
                                                ]) == 0
                                            ) {

                                                $referral = new ArchReferral();
                                                $referral->bindProperties($post);
                                                $referral->subject_title = $thisReferral->transactionObject->main_topic;
                                                $referral->top_referral_id = (int)$thisReferral->id;
                                                $referral->type = (int)ArchReferral::ARCH_DESTINATION_INTERNAL;
                                                $referral->belong_to = ArchReferral::ARCH_BELONG_TO_EMPLOYEE;
                                                $referral->client_id = (int)$thisReferral->client_id;
                                                $referral->transaction_id = (int)$thisReferral->transactionObject->id;
                                                $referral->folder_id = (int)ArchFolder::getFolderId(ArchFolder::ARCH_BELONG_TO_EMPLOYEE,
                                                    ArchFolder::ARCH_BOX_EMPLOYEE_INBOX, $_SESSION['organization']->id,
                                                    $post['employee_id']);
                                                $referral->ownership = 0;
                                                $referral->privileges = implode(',', $post['privileges']);
                                                $referral->execution_type = (int)ArchReferral::ARCH_TASK_EXECUTION_NOT_DONE;
                                                $referral->execution_status = (int)ArchReferral::ARCH_EXECUTION_NORMAL;
                                                $referral->execution_start_date = $this->Date->get_date('ad',
                                                    $post['execution_start_date']);
                                                $referral->created_by = (int)$_SESSION['user']->id;
                                                $referral->created_date = date('Y-m-d');
                                                $referral->save();

                                                Notification::sendNotification(Operation::OPERATION_DOCUMENT_COMPOSER,
                                                    0, 'arch_referral', $referral->id, $referral->created_by,
                                                    $referral->employee_id,
                                                    ArchReferral::NOTIFICATION_ARCHIVE_NEW_REFERRAL, []);

                                                try {
                                                    $referrals = ArchReferral::read([
                                                        ArchReferral::TRANSACTION_ID => (int)$thisReferral->transactionObject->id,
                                                        ArchReferral::OWNERSHIP      => 0,
                                                    ]);
                                                } catch (ArchReferralException $e) {
                                                    $referrals = [];
                                                }

                                                if ($referrals) {
                                                    foreach ($referrals as $referral) {
                                                        if ($referral->type == 1032 and $referral->employee_id != $_SESSION['user']->id) {
                                                            try {
                                                                Notification::sendNotification(
                                                                    Operation::OPERATION_DOCUMENT_COMPOSER,
                                                                    0,
                                                                    'arch_referral',
                                                                    $referral->id,
                                                                    $_SESSION['user']->id,
                                                                    $referral->employee_id,
                                                                    ArchReferral::NOTIFICATION_ARCHIVE_NEW_REFERRAL,
                                                                    []);
                                                            } catch (NotificationException $e) {
                                                            }
                                                        }
                                                    }
                                                }

                                                Notification::createdAlert();

                                            } else {
                                                Notification::alertMessage(Notification::WARNING,
                                                    'SorryThisReferralExistNotificationSendToHim');

                                                try {

                                                    $existenceReferral = ArchReferral::read([
                                                        ArchReferral::TYPE           => ArchReferral::ARCH_DESTINATION_INTERNAL,
                                                        ArchReferral::BELONG_TO      => ArchReferral::ARCH_BELONG_TO_EMPLOYEE,
                                                        ArchReferral::TRANSACTION_ID => $thisReferral->transactionObject->id,
                                                        ArchReferral::EMPLOYEE_ID    => $post['employee_id'],
                                                    ])[0];

                                                    try {

                                                        Notification::sendNotification(Operation::OPERATION_DOCUMENT_COMPOSER,
                                                            0, 'arch_referral', $existenceReferral->id,
                                                            $_SESSION['user']->id, $post['employee_id'],
                                                            ArchReferral::NOTIFICATION_ARCHIVE_NEW_REFERRAL, []);

                                                    } catch (NotificationException $e) {

                                                    }

                                                } catch (ArchFolderException $e) {

                                                }

                                            }

                                        } catch (ArchReferralException $e) {

                                        }

                                        break;

                                    case ArchReferral::ARCH_DESTINATION_EXTERNAL:

                                        try {

                                            if (ArchReferral::count([
                                                    ArchReferral::TYPE           => ArchReferral::ARCH_DESTINATION_EXTERNAL,
                                                    ArchReferral::TRANSACTION_ID => $thisReferral->transactionObject->id,
                                                    ArchReferral::EMPLOYEE_ID    => 0,
                                                ]) == 0
                                            ) {

                                                $referral = new ArchReferral();
                                                $referral->bindProperties($post);
                                                $referral->top_referral_id = (int)$thisReferral->id;
                                                $referral->type = (int)ArchReferral::ARCH_DESTINATION_EXTERNAL;
                                                $referral->belong_to = ArchReferral::ARCH_BELONG_TO_CLIENT;
                                                $referral->client_id = (int)$thisReferral->client_id;
                                                $referral->employee_id = 0;
                                                $referral->transaction_id = (int)$thisReferral->transactionObject->id;
                                                $referral->folder_id = (int)ArchFolder::getFolderId(ArchFolder::ARCH_BELONG_TO_CLIENT,
                                                    ArchFolder::ARCH_BOX_CLIENT_OUTBOX, $_SESSION['organization']->id,
                                                    0);
                                                $referral->ownership = 0;
                                                $referral->privileges = implode(',', [
                                                    (int)ArchReferral::ARCH_PRIVILEGES_EDIT,
                                                    (int)ArchReferral::ARCH_PRIVILEGES_REFERRAL,
                                                ]);
                                                $referral->execution_type = ArchReferral::ARCH_TASK_EXECUTION_NOT_DONE;
                                                $referral->execution_status = ArchReferral::ARCH_EXECUTION_NORMAL;
                                                $referral->execution_start_date = $this->Date->get_date('ad',
                                                    $post['execution_start_date']);
                                                $referral->created_by = $_SESSION['user']->id;
                                                $referral->created_date = date('Y-m-d');
                                                $referral->save();

                                                Notification::createdAlert();

                                                try {

                                                    Notification::sendNotification(Operation::OPERATION_DOCUMENT_COMPOSER,
                                                        0, 'arch_referral', $referral->id, $referral->created_by,
                                                        $referral->employee_id,
                                                        ArchReferral::NOTIFICATION_ARCHIVE_NEW_REFERRAL, []);

                                                } catch (NotificationException $e) {

                                                }

                                            } else {

                                                Notification::alertMessage(Notification::WARNING,
                                                    'SorryThisReferralExistNotificationSendToHim');

                                                try {

                                                    $existenceReferral = ArchReferral::read([
                                                        ArchReferral::TYPE           => ArchReferral::ARCH_DESTINATION_EXTERNAL,
                                                        ArchReferral::BELONG_TO      => ArchReferral::ARCH_BELONG_TO_CLIENT,
                                                        ArchReferral::TRANSACTION_ID => $thisReferral->transactionObject->id,
                                                        ArchReferral::EMPLOYEE_ID    => 0,
                                                    ])[0];

                                                    try {

                                                        //Notification::sendNotification(Operation::OPERATION_DOCUMENT_COMPOSER, 0, 'arch_referral', $existenceReferral->id, $_SESSION['user']->id, $post['employee_id'], ArchReferral::NOTIFICATION_ARCHIVE_NEW_REFERRAL, []);

                                                    } catch (NotificationException $e) {

                                                    }

                                                } catch (ArchFolderException $e) {

                                                }

                                            }


                                        } catch (ArchReferralException $e) {

                                        }

                                        break;

                                    case ArchReferral::ARCH_DESTINATION_PUBLIC:
                                        $employeesIds = DB::table(arch_referral::class)
                                            ->where(ArchReferral::CLIENT_ID, $_SESSION['organization']->id)
                                            ->where(ArchReferral::TRANSACTION_ID, $thisReferral->transaction_id)
                                            ->get()->pluck(ArchReferral::EMPLOYEE_ID)->unique()->all();
                                        switch ($post['publication_with']) {
                                            // Units managers
                                            case 1:
                                                /**
                                                 * @var $managers User[]
                                                 */
                                                $managers = User::convertFromQB(DB::table(sh_user::class)
                                                    ->whereIn(User::ID, $post['managers'])
                                                    ->get());
                                                $referrals = [];
                                                $affectedManagersIds = [];
                                                foreach ($managers as $manager) {
                                                    // If user already has referral skip it.
                                                    if (in_array($manager->id, $employeesIds)) {
                                                        continue;
                                                    }
                                                    $affectedManagersIds[] = $manager->id;

                                                    $referral = [];
                                                    $referral[ArchReferral::TOP_REFERRAL_ID] = $thisReferral->id;
                                                    $referral[ArchReferral::OWNERSHIP] = 0;
                                                    $referral[ArchReferral::TYPE] = ArchReferral::ARCH_DESTINATION_INTERNAL;
                                                    $referral[ArchReferral::BELONG_TO] = 'employee';
                                                    $referral[ArchReferral::FOLDER_ID] = (int)ArchFolder::getFolderId(ArchFolder::ARCH_BELONG_TO_EMPLOYEE,
                                                        ArchFolder::ARCH_BOX_EMPLOYEE_INBOX,
                                                        $_SESSION['organization']->id, $manager->id);
                                                    $referral[ArchReferral::CLIENT_ID] = $thisReferral->client_id;
                                                    $referral[ArchReferral::EMPLOYEE_ID] = $manager->id;
                                                    $referral[ArchReferral::TRANSACTION_ID] = $thisReferral->transaction_id;
                                                    $referral[ArchReferral::SUBJECT_TITLE] = $thisReferral->transactionObject->main_topic;
                                                    $referral[ArchReferral::SUBJECT_COMMENT] = $post['subject_comment'];
                                                    $referral[ArchReferral::PRIVILEGES] = ArchReferral::ARCH_PRIVILEGES_REFERRAL;
                                                    $referral[ArchReferral::PRIORITY_STATUS] = $post['priority_status'];
                                                    $referral[ArchReferral::ASSIGNMENT_TYPE] = $post['assignment_type'];
                                                    $referral[ArchReferral::EXECUTION_TYPE] = ArchReferral::ARCH_TASK_EXECUTION_NOT_DONE;
                                                    $referral[ArchReferral::EXECUTION_STATUS] = ArchReferral::ARCH_EXECUTION_NORMAL;
                                                    $referral[ArchReferral::EXECUTION_DURATION] = $post['execution_duration'];
                                                    $referral[ArchReferral::EXECUTION_START_DATE] = $this->Date->get_date('ad',
                                                        $post['execution_start_date']);
                                                    $referral[ArchReferral::CREATED_BY] = $_SESSION['user']->id;
                                                    $referral[ArchReferral::PRIVILEGES] = implode(",",$post['managers_privileges']);
                                                    $referral[ArchReferral::CREATED_DATE] = date('Y-m-d H:i:s');

                                                    $referrals[] = $referral;
                                                }
                                                // Insert into database
                                                DB::table(arch_referral::class)->insert($referrals);

                                                /**
                                                 * Prepare the notifications
                                                 * @var $currentReferrals arch_referral[]
                                                 */
                                                $currentReferrals = DB::table(arch_referral::class)
                                                    ->where(ArchReferral::CLIENT_ID, $_SESSION['organization']->id)
                                                    ->where(ArchReferral::TRANSACTION_ID, $thisReferral->transaction_id)
                                                    ->whereIn(ArchReferral::EMPLOYEE_ID, $affectedManagersIds)
                                                    ->get();
                                                $notifications = [];
                                                foreach ($currentReferrals as $referral) {
                                                    $notification = [];
                                                    $notification[Notification::ORG_ID] = $_SESSION['organization']->id;
                                                    $notification[Notification::PRG_ID] = Program::PROGRAM_GENERAL_PROGRAM_X000;
                                                    $notification[Notification::OPR_ID] = Operation::OPERATION_DOCUMENT_COMPOSER;
                                                    $notification[Notification::STP_ID] = 0;
                                                    $notification[Notification::TABLENAME] = 'arch_referral';
                                                    $notification[Notification::ROW_ID] = $referral->arch_referral_id;
                                                    $notification[Notification::FROM_USER_ID] = $_SESSION['user']->id;
                                                    $notification[Notification::TO_USER_ID] = $referral->arch_referral_employee_id;
                                                    $notification[Notification::TYPE] = ArchReferral::NOTIFICATION_ARCHIVE_NEW_REFERRAL;
                                                    $notification[Notification::STATUS] = 1;
                                                    $notification[Notification::CREATED_DATE] = date('Y-m-d H:i:s');

                                                    $notifications[] = $notification;
                                                }
                                                // Notify users
                                                DB::table(sh_notif::class)->insert($notifications);

                                                Notification::createdAlert();
                                                break;
                                            // Users classes
                                            case 2:
                                                if (! empty($post['classes'])) {
                                                    $usersQuery = DB::table(sh_user::class);
                                                    foreach ($post['classes'] as $classId) {
                                                        $usersQuery->orWhereRaw("FIND_IN_SET({$classId}, sh_user_classification)");
                                                    }
                                                    $users = User::convertFromQB($usersQuery->distinct()->get());
                                                }
                                                /**
                                                 * @var $users User[]
                                                 */
                                                $referrals = [];
                                                $affectedUsersIds = [];
                                                foreach ($users as $user) {
                                                    // If user already has referral skip it.
                                                    if (in_array($user->id, $employeesIds)) {
                                                        continue;
                                                    }
                                                    $affectedUsersIds[] = $user->id;
                                                    $referral = [];
                                                    $referral[ArchReferral::TOP_REFERRAL_ID] = $thisReferral->id;
                                                    $referral[ArchReferral::OWNERSHIP] = 0;
                                                    $referral[ArchReferral::TYPE] = ArchReferral::ARCH_DESTINATION_INTERNAL;
                                                    $referral[ArchReferral::BELONG_TO] = 'employee';
                                                    $referral[ArchReferral::FOLDER_ID] = (int)ArchFolder::getFolderId(ArchFolder::ARCH_BELONG_TO_EMPLOYEE,
                                                        ArchFolder::ARCH_BOX_EMPLOYEE_INBOX,
                                                        $_SESSION['organization']->id, $user->id);
                                                    $referral[ArchReferral::CLIENT_ID] = $thisReferral->client_id;
                                                    $referral[ArchReferral::EMPLOYEE_ID] = $user->id;
                                                    $referral[ArchReferral::TRANSACTION_ID] = $thisReferral->transaction_id;
                                                    $referral[ArchReferral::SUBJECT_TITLE] = $thisReferral->transactionObject->main_topic;
                                                    $referral[ArchReferral::SUBJECT_COMMENT] = $post['subject_comment'];
                                                    $referral[ArchReferral::PRIVILEGES] = ' ';
                                                    $referral[ArchReferral::PRIORITY_STATUS] = $post['priority_status'];
                                                    $referral[ArchReferral::ASSIGNMENT_TYPE] = $post['assignment_type'];
                                                    $referral[ArchReferral::EXECUTION_TYPE] = ArchReferral::ARCH_TASK_EXECUTION_NOT_DONE;
                                                    $referral[ArchReferral::EXECUTION_STATUS] = ArchReferral::ARCH_EXECUTION_NORMAL;
                                                    $referral[ArchReferral::EXECUTION_DURATION] = $post['execution_duration'];
                                                    $referral[ArchReferral::EXECUTION_START_DATE] = $this->Date->get_date('ad',
                                                        $post['execution_start_date']);
                                                    $referral[ArchReferral::CREATED_BY] = $_SESSION['user']->id;
                                                    $referral[ArchReferral::CREATED_DATE] = date('Y-m-d H:i:s');

                                                    $referrals[] = $referral;
                                                }
                                                // Insert into database
                                                DB::table(arch_referral::class)->insert($referrals);

                                                /**
                                                 * Prepare the notifications
                                                 * @var $currentReferrals arch_referral[]
                                                 */
                                                $currentReferrals = DB::table(arch_referral::class)
                                                    ->where(ArchReferral::CLIENT_ID, $_SESSION['organization']->id)
                                                    ->where(ArchReferral::TRANSACTION_ID, $thisReferral->transaction_id)
                                                    ->whereIn(ArchReferral::EMPLOYEE_ID, $affectedUsersIds)
                                                    ->get();
                                                $notifications = [];
                                                foreach ($currentReferrals as $referral) {
                                                    $notification = [];
                                                    $notification[Notification::ORG_ID] = $_SESSION['organization']->id;
                                                    $notification[Notification::PRG_ID] = Program::PROGRAM_GENERAL_PROGRAM_X000;
                                                    $notification[Notification::OPR_ID] = Operation::OPERATION_DOCUMENT_COMPOSER;
                                                    $notification[Notification::STP_ID] = 0;
                                                    $notification[Notification::TABLENAME] = 'arch_referral';
                                                    $notification[Notification::ROW_ID] = $referral->arch_referral_id;
                                                    $notification[Notification::FROM_USER_ID] = $_SESSION['user']->id;
                                                    $notification[Notification::TO_USER_ID] = $referral->arch_referral_employee_id;
                                                    $notification[Notification::TYPE] = ArchReferral::NOTIFICATION_ARCHIVE_NEW_REFERRAL;
                                                    $notification[Notification::STATUS] = 1;
                                                    $notification[Notification::CREATED_DATE] = date('Y-m-d H:i:s');


                                                    $notifications[] = $notification;
                                                }
                                                // Notify users
                                                DB::table(sh_notif::class)->insert($notifications);

                                                Notification::createdAlert();
                                                break;
                                        }
                                        break;

                                }

                            }

                        }

                        break;

                    case 'update':

                        if ($session->getDataManipulationToken() === $parm[3]) {

                            Validation::rules($post, [
                                'type'                 => 'required',
                                'employee_id'          => 'required_if:type,1032',
                                'assignment_type'      => 'required',
                                'execution_start_date' => 'required_if:assignment_type,1050',
                                'execution_duration'   => 'required_if:assignment_type,1050|min:1',
                                'priority_status'      => 'required_if:assignment_type,1050|min:1',
                            ]);

                            if (Validation::check()) {

                                switch ($post['type']) {

                                    case ArchReferral::ARCH_DESTINATION_INTERNAL:

                                        try {

                                            $referral = ArchReferral::readID((int)$parm[4] ?? 0);
                                            $referral->bindProperties($post);
                                            $referral->top_referral_id = (int)$thisReferral->id;
                                            $referral->type = (int)ArchReferral::ARCH_DESTINATION_INTERNAL;
                                            $referral->belong_to = ArchReferral::ARCH_BELONG_TO_EMPLOYEE;
                                            $referral->client_id = (int)$thisReferral->client_id;
                                            $referral->transaction_id = (int)$thisReferral->transactionObject->id;
                                            $referral->folder_id = (int)ArchFolder::getFolderId(ArchFolder::ARCH_BELONG_TO_EMPLOYEE,
                                                ArchFolder::ARCH_BOX_EMPLOYEE_INBOX, $_SESSION['organization']->id,
                                                $post['employee_id']);
                                            $referral->ownership = 0;
                                            $referral->privileges = implode(',', $post['privileges']);
                                            $referral->execution_type = (int)ArchReferral::ARCH_TASK_EXECUTION_NOT_DONE;
                                            $referral->execution_status = (int)ArchReferral::ARCH_EXECUTION_NORMAL;
                                            $referral->execution_start_date = $this->Date->get_date('ad',
                                                $post['execution_start_date']);
                                            $referral->created_by = (int)$_SESSION['user']->id;
                                            $referral->created_date = date('Y-m-d');
                                            $referral->save();

                                            Notification::createdAlert();

                                        } catch (ArchReferralException $e) {

                                        }

                                        break;

                                    case ArchReferral::ARCH_DESTINATION_EXTERNAL:

                                        try {

                                            $referral = ArchReferral::readID((int)$parm[4] ?? 0);
                                            $referral->bindProperties($post);
                                            $referral->top_referral_id = (int)$thisReferral->id;
                                            $referral->type = (int)ArchReferral::ARCH_DESTINATION_EXTERNAL;
                                            $referral->belong_to = ArchReferral::ARCH_BELONG_TO_CLIENT;
                                            $referral->client_id = (int)$thisReferral->client_id;
                                            $referral->employee_id = 0;
                                            $referral->transaction_id = (int)$thisReferral->transactionObject->id;
                                            $referral->folder_id = (int)ArchFolder::getFolderId(ArchFolder::ARCH_BELONG_TO_CLIENT,
                                                ArchFolder::ARCH_BOX_CLIENT_OUTBOX, $_SESSION['organization']->id, 0);
                                            $referral->ownership = 0;
                                            $referral->privileges = implode(',', [
                                                (int)ArchReferral::ARCH_PRIVILEGES_EDIT,
                                                (int)ArchReferral::ARCH_PRIVILEGES_REFERRAL,
                                            ]);
                                            $referral->assignment_type = (int)ArchReferral::ARCH_REFERRAL_TYPES_TASK;
                                            $referral->execution_type = (int)ArchReferral::ARCH_TASK_EXECUTION_NOT_DONE;
                                            $referral->execution_status = (int)ArchReferral::ARCH_EXECUTION_NORMAL;
                                            $referral->execution_start_date = $this->Date->get_date('ad',
                                                $post['execution_start_date']);
                                            $referral->created_by = (int)$_SESSION['user']->id;
                                            $referral->created_date = date('Y-m-d');
                                            $referral->save();

                                            Notification::createdAlert();


                                        } catch (ArchReferralException $e) {

                                        }

                                        break;

                                }

                            }

                        }

                        break;

                    case 'delete':

                        if ($session->getDataManipulationToken() === $parm[3]) {

                            try {

                                $referral = ArchReferral::readID((int)$parm[4] ?? 0);
                                $referral->delete();

                                Notification::deletedAlert();

                            } catch (ArchReferralException $e) {

                            }

                        }

                        break;
                }

                break;

            case 'comments':

                $session = new SessionManager($parm[1]);
                $thisReferral = ArchReferral::readID($session->get('referral_id'));

                switch ($parm[2]) {

                    case 'insert':

                        if ($session->getDataManipulationToken() === $parm[3]) {

                            try {

                                $post['forword_to'] = implode("," , $post['forword_to']);
                                $comment = new ArchComment();
                                $comment->bindProperties($post);
                                $comment->client_id = $thisReferral->client_id;
                                $comment->transaction_id = $thisReferral->transactionObject->id;
                                $comment->created_by = $_SESSION['user']->id;
                                $comment->created_date = date('Y-m-d');
                                $comment->save();

                                try {
                                    $referrals = ArchReferral::read([
                                        ArchReferral::TRANSACTION_ID => (int)$thisReferral->transactionObject->id
                                    ]);
                                } catch (ArchReferralException $e) {
                                    $referrals = [];
                                }

                                foreach ($referrals as $referral) {
                                    if ($referral->type == 1032
                                        and $referral->employee_id != $_SESSION['user']->id
                                        and (in_array($referral->employee_id , explode("," , $post['forword_to'])) or $post['forword_to'] == "0")
                                    ) {
                                        $toUser = $referral->employee_id == 0 ? $thisReferral->created_by : $referral->employee_id;
                                        Notification::sendNotification(
                                            Operation::OPERATION_DOCUMENT_COMPOSER,
                                            0,
                                            'arch_comment',
                                            $comment->id,
                                            $_SESSION['user']->id,
                                            $toUser,
                                            ArchReferral::NOTIFICATION_ARCHIVE_NEW_COMMENT,
                                            []);
                                    }
                                }

                                Notification::createdAlert();

                            } catch (ArchCommentException $e) {

                            }

                        }

                        break;

                    case 'update':

                        if ($session->getDataManipulationToken() === $parm[3]) {

                            try {

                                $comment = ArchComment::readID((int)$parm[4] ?? 0);
                                $comment->bindProperties($post);
                                $comment->save();

                                Notification::updatedAlert();

                            } catch (ArchCommentException $e) {

                            }

                        }

                        break;

                    case 'delete':

                        if ($session->getDataManipulationToken() === $parm[3]) {

                            try {

                                $comment = ArchComment::readID((int)$parm[4] ?? 0);
                                $comment->delete();

                                Notification::deletedAlert();

                            } catch (ArchCommentException $e) {

                            }

                        }

                        break;
                }

                break;

            case 'updateMyReferral':

                $session = new SessionManager($parm[1]);
                if ($session->getDataManipulationToken() == $parm[2]) {

                    try {

                        $thisReferral = ArchReferral::readID($session->get('referral_id'));
                        $thisReferral->bindProperties($post);
                        $thisReferral->save();

                        // Notify the creator
                        Notification::sendNotification(
                            $_SESSION['operation']->id,
                            0,
                            'arch_referral',
                            $thisReferral->id,
                            $_SESSION['user']->id,
                            $thisReferral->created_by,
                            1180,
                            []
                        );

                        Notification::updatedAlert();

                    } catch (ArchReferralException $e) {

                    }
                }
                break;

            case 'export':
                $session = new SessionManager($parm[1]);
                if ($session->getDataManipulationToken() == $parm[2]) {

                    try {
                        $transaction = ArchTransaction::readID((int)$parm[3]);
                        $transaction->issue_type = ArchTransaction::EXPORT_SETTING;
                        $transaction->serial_number = ArchTransaction::getOutboxPrefix(). '-' . $transaction->serial_number;
                        $transaction->update();

                        Notification::alertMessage(Notification::SUCCESS, 'gnr_export_done_successfully');
                    } catch (ArchTransactionException $e) {
                    }
                }
                break;

            case 'generateExportData':
                $session = new SessionManager($parm[1]);
                if ($session->getDataManipulationToken() == $parm[2]) {

                    try {
                        $transaction = ArchTransaction::readID((int)$parm[3]);
                        $transaction->issued_date = $this->Date->get_date('ad', $post['date']);
                        $transaction->issue_type = ArchTransaction::EXPORT_NOT_SENT;
                        $transaction->issued_by = $_SESSION['user']->id;
                        $transaction->attachment_count = $post['attachments_count'];
                        $transaction->serial_number = $post['serial_number'];
                        $transaction->save();

                        if ($post['enumeration_type'] == 2) {
                            $now = Carbon\Carbon::now();

                            switch ($post['parts']) {
                                case 1: // Year
                                    $transaction->serial_number = $now->format('y').str_pad($transaction->id, 6, '0',
                                            STR_PAD_LEFT);
                                    break;
                                case 2: // Year and month
                                    $transaction->serial_number = $now->format('y').str_pad($now->month, 2, '0',
                                            STR_PAD_LEFT).str_pad($transaction->id, 4, '0', STR_PAD_LEFT);
                                    break;
                                case 3: // Year and month and day
                                    $transaction->serial_number = $now->format('y').str_pad($now->month, 2, '0',
                                            STR_PAD_LEFT).str_pad($now->day, 2, '0',
                                            STR_PAD_LEFT).str_pad($transaction->id, 2, '0', STR_PAD_LEFT);
                                    break;
                            }

                            $transaction->save();

                        }


                        $transaction->path_qr_code = SnsoQR::generate(
                            $transaction->id,$transaction->main_topic,
                            $transaction->serial_number,
                            $transaction->issued_date,
                            $transaction->attachments_count,
                            'Export'
                        );

                        $transaction->save();

                        $session->getDataManipulationToken();
                        Notification::alertMessage(Notification::SUCCESS,
                            'gnr_export_number_generation_done_successfully');
                    } catch (ArchTransactionException $e) {
                    }
                }
                break;

            case 'import':
                $session = new SessionManager($parm[1]);
                if ($session->getDataManipulationToken() == $parm[2]) {

                    try {
                        $transaction = ArchTransaction::readID((int)$parm[3]);
                        $transaction->issue_type = ArchTransaction::IMPORT_SETTING;
                        $transaction->serial_number = ArchTransaction::getInboxPrefix(). '-' . $transaction->serial_number;
                        $transaction->update();

                        Notification::alertMessage(Notification::SUCCESS, 'gnr_import_done_successfully');
                    } catch (ArchTransactionException $e) {
                    }
                }

                break;
            case 'generateImportData':
                $session = new SessionManager($parm[1]);
                if ($session->getDataManipulationToken() == $parm[2]) {
                    try {
                        $transaction = ArchTransaction::readID((int)$parm[3]);
                        $transaction->issued_date = $this->Date->get_date('ad', $post['date']);
                        $transaction->issue_type = ArchTransaction::IMPORT_NOT_SENT;
                        $transaction->issued_by = $_SESSION['user']->id;
                        $transaction->attachment_count = $post['attachments_count'];
                        $transaction->serial_number = $post['serial_number'];
                        $transaction->save();

                        if ($post['enumeration_type'] == 2) {
                            $now = Carbon\Carbon::now();

                            switch ($post['parts']) {
                                case 1: // Year
                                    $transaction->serial_number = $now->format('y').str_pad($transaction->id, 6, '0',
                                            STR_PAD_LEFT);
                                    break;
                                case 2: // Year and month
                                    $transaction->serial_number = $now->format('y').str_pad($now->month, 2, '0',
                                            STR_PAD_LEFT).str_pad($transaction->id, 4, '0', STR_PAD_LEFT);
                                    break;
                                case 3: // Year and month and day
                                    $transaction->serial_number = $now->format('y').str_pad($now->month, 2, '0',
                                            STR_PAD_LEFT).str_pad($now->day, 2, '0',
                                            STR_PAD_LEFT).str_pad($transaction->id, 2, '0', STR_PAD_LEFT);
                                    break;
                            }

                            $transaction->path_qr_code = SnsoQR::generate($transaction->id,
                                $transaction->main_topic, $transaction->serial_number, $transaction->issued_date,
                                $transaction->attachments_count,
                            'Import');
                            $transaction->save();

                        }

                        $session->getDataManipulationToken();
                        Notification::alertMessage(Notification::SUCCESS,
                            'gnr_import_number_generation_done_successfully');
                    } catch (ArchTransactionException $e) {
                    }

                }

                break;
        }


        if ($session->keyHasValue('referral_id')) {

            try {

                // Referral
                $thisReferral = ArchReferral::readID((int)$session->get('referral_id') ?? 0);
                $this->Smarty->assign('thisReferral', $thisReferral);

                // Transaction
                try {

                    $this->Smarty->assign('transaction', $thisReferral->transactionObject);

                } catch (ArchTransactionException $e) {

                    $this->Smarty->assign('transaction', []);

                }

                // Transaction Comments
                try {

//                    return ArchComment::read([
//                        ArchComment::CLIENT_ID      => $thisReferral->client_id,
//                        ArchComment::TRANSACTION_ID => $thisReferral->transactionObject->id,
//                    ]);

                    $this->Smarty->assign('comments', ArchComment::read([
                        ArchComment::CLIENT_ID      => $thisReferral->client_id,
                        ArchComment::TRANSACTION_ID => $thisReferral->transactionObject->id,
                    ]));

                } catch (ArchCommentException $e) {

                    $this->Smarty->assign('comments', []);

                }


                // Transaction Referrals
                try {

                    $this->Smarty->assign('referrals', ArchReferral::read([
//                        ArchReferral::TOP_REFERRAL_ID => (int)$thisReferral->id,
                        ArchReferral::TRANSACTION_ID => (int)$thisReferral->transactionObject->id,
                        ArchReferral::OWNERSHIP      => 0,
                    ]));

                } catch (ArchReferralException $e) {

                    $this->Smarty->assign('referrals', []);

                }

                // Transaction Documents
                try {

                    $this->Smarty->assign('documents', Document::read([
                        Document::CLIENT_ID      => $thisReferral->client_id,
                        Document::OPERATION_CODE => 'documentComposer',
                        Document::TABLE_NAME     => 'arch_transaction',
                        Document::ROW_ID         => $thisReferral->transactionObject->id,
                    ]));

                } catch (DocumentException $e) {

                    $this->Smarty->assign('documents', []);

                }

            } catch (ArchReferralException $e) {

                $this->Smarty->assign('referral', []);

            }

        }


        $this->Smarty->assign('qr_path', SnsoQR::get($thisReferral->transactionObject->path_qr_code) ?? '');
        $this->Smarty->assign('privacyTypes', Setting::getList(229));
        $this->Smarty->assign('replayTypes', Setting::getList(235));
        $this->Smarty->assign('languages', Language::read());

        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);

    }

    public function printQrCode($parm, $post)
    {
        $session = new SessionManager($parm[0]);

        try {
            // Referral
            $referral = ArchReferral::readID((int)$session->get('referral_id') ?? 0);
            $this->Smarty->assign('qr_path', SnsoQR::get($referral->transactionObject->path_qr_code) ?? '');
        } catch (ArchTransactionException $e) {
            $this->Smarty->assign('qr_path', []);
        }

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));
    }

    public function printDocument($parm, $post)
    {
        $session = new SessionManager($parm[0]);

        try {

            // Referral
            $referral = ArchReferral::readID((int)$session->get('referral_id') ?? 0);
            $this->Smarty->assign('thisReferral', $referral);

            $this->Smarty->assign('qr_path', SnsoQR::get($referral->transactionObject->path_qr_code) ?? '');

            // Transaction
            try {

                $this->Smarty->assign('transaction', $referral->transactionObject);

            } catch (ArchTransactionException $e) {

                $this->Smarty->assign('transaction', []);

            }

            // Transaction Comments
            try {

                $this->Smarty->assign('comments', ArchComment::read([
                    ArchComment::CLIENT_ID      => $referral->client_id,
                    ArchComment::TRANSACTION_ID => $referral->transactionObject->id,
                ]));

            } catch (ArchCommentException $e) {

                $this->Smarty->assign('comments', []);

            }



            // Transaction Referrals
            try {

                $this->Smarty->assign('referrals', ArchReferral::read([
                    ArchReferral::TRANSACTION_ID => $referral->transactionObject->id,
                    ArchReferral::OWNERSHIP      => 0,
                ]));

            } catch (ArchCommentException $e) {

                $this->Smarty->assign('referrals', []);

            }

            // Transaction Documents
            try {

                $this->Smarty->assign('documents', Document::read([
                    Document::CLIENT_ID      => $referral->client_id,
                    Document::OPERATION_CODE => 'documentComposer',
                    Document::TABLE_NAME     => 'arch_transaction',
                    Document::ROW_ID         => $referral->transactionObject->id,
                ]));

            } catch (DocumentException $e) {

                $this->Smarty->assign('documents', []);

            }

        } catch (ArchReferralException $e) {

            $this->Smarty->assign('referral', []);

        }

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));

    }

    public function printComments($parm, $post)
    {

        $session = new SessionManager($parm[0]);

        try {

            // Referral
            $referral = ArchReferral::readID((int)$session->get('referral_id') ?? 0);
            $this->Smarty->assign('thisReferral', $referral);

            // Transaction
            try {

                $this->Smarty->assign('transaction', $referral->transactionObject);

            } catch (ArchTransactionException $e) {

                $this->Smarty->assign('transaction', []);

            }

            // Transaction Comments
            try {

                $this->Smarty->assign('comments', ArchComment::read([
                    ArchComment::CLIENT_ID      => $referral->client_id,
                    ArchComment::TRANSACTION_ID => $referral->transactionObject->id,
                ]));

            } catch (ArchCommentException $e) {

                $this->Smarty->assign('comments', []);

            }

        } catch (ArchReferralException $e) {
            $this->Smarty->assign('thisReferral', []);
        }

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));

    }

    public function documentadd($parm, $post)
    {

        try {
            $this->Smarty->assign('operation', Operation::readByCode((string)$parm[1] ?? ''));
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        try {
            $this->Smarty->assign('document_max_size_limit',
                number_format((((int)(new ConfigurationParser(CLIENT_CONFIG))->getSection('document')['document_max_size_limit']) / 1024) / 1024,
                    2, '.', ','));
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('document_max_size_limit', 0);
        }


        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);

    }

    public function documentedit($parm, $post)
    {

        try {
            $this->Smarty->assign('operation', Operation::readByCode((string)$parm[1] ?? ''));
        } catch (OperationException $e) {
            $this->Smarty->assign('operation', []);
        }

        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[2] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

        try {
            $this->Smarty->assign('document_max_size_limit',
                number_format((((int)(new ConfigurationParser(CLIENT_CONFIG))->getSection('document')['document_max_size_limit']) / 1024) / 1024,
                    2, '.', ','));
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('document_max_size_limit', 0);
        }

        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);

    }

    public function documentconfirm($parm, $post)
    {

        try {
            $this->Smarty->assign('document', Document::readID((int)$parm[1] ?? 0));
        } catch (DocumentException $e) {
            $this->Smarty->assign('document', []);
        }

        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function referraladd($parm, $post)
    {
//        return Setting::getList(52);
        $this->Smarty->assign('employees',
            Vacant::getEmployeeListByEntityWithBasicVacancies($_SESSION['organization'], null, null, null, null));
        $this->Smarty->assign('referralTypes', Setting::getList(230));
        $this->Smarty->assign('assignmentTypes', Setting::getList(236));
        $this->Smarty->assign('privileges', Setting::getList(52));
        $this->Smarty->assign('priorities', Setting::getList(225));
        try {
            $referral = ArchReferral::readID((int)$parm[1] ?? 0);
            $this->Smarty->assign('referral', $referral);
        } catch (ArchReferralException $e) {
            $this->Smarty->assign('referral', []);
        }
        $usersAlreadyInReferral = DB::table(arch_referral::class)
            ->where(ArchReferral::CLIENT_ID, $_SESSION['organization']->id)
            ->where(ArchReferral::TRANSACTION_ID, $referral->transaction_id)
            ->get()->pluck(ArchReferral::EMPLOYEE_ID)->unique()->all();
        $this->Smarty->assign('usersAlreadyInReferral', $usersAlreadyInReferral);
        try {
            $units = DB::table(sh_unt::class)
                ->where(Unit::ORG_ID, $_SESSION['organization']->id)
                ->where(Unit::ACTIVATION, Unit::UNIT_IS_ACTIVATED)
                ->whereNotNull(Unit::MANAGER_ID)
                ->where(Unit::MANAGER_ID, '!=', 0)
                ->get();

            $managersIds = $units->pluck(Unit::MANAGER_ID)->all();
            $managers = DB::table(sh_user::class)
                ->whereIn(User::ID, $managersIds)
                ->pluck(User::FULL_NAME, USER::ID)
                ->all();

            $unitsGroups = $units->groupBy(Unit::MANAGER_ID);
            $unitsNames = $unitsGroups->mapWithKeys(function ($unitsGroup, $key) {
                /** @var \Illuminate\Support\Collection $unitsGroup */
                $names = $unitsGroup->count() > 1
                    ? $unitsGroup->pluck(Unit::NAME)->implode(' / ')
                    : $unitsGroup->first()->sh_unt_name;
                return [$key => $names];
            })->all();
            $this->Smarty->assign('managers', $managers);
            $this->Smarty->assign('unitsNames', $unitsNames);
        } catch (UnitException $e) {
            $this->Smarty->assign('managers', []);
            $this->Smarty->assign('unitsNames', []);
        }
        try {
            $this->Smarty->assign('usersClasses', UserClass::read([
                UserClass::ORG_ID => $_SESSION['organization']->id,
            ]));
        } catch (UserClassException $e) {
            $this->Smarty->assign('usersClasses', []);
        }

        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function referraledit($parm, $post)
    {

        try {
            $referral = ArchReferral::readID((int)$parm[1] ?? 0);
            $this->Smarty->assign('referral', $referral);
            $this->Smarty->assign('thisReferral', ArchReferral::readID((int)$parm[2]));

            switch ($referral->type) {

                case ArchReferral::ARCH_DESTINATION_INTERNAL:

                    switch ($referral->assignment_type) {

                        case ArchReferral::ARCH_REFERRAL_TYPES_MESSAGE:
                            break;
                        case ArchReferral::ARCH_REFERRAL_TYPES_TASK:
                            $this->Smarty->assign('jsCode', '
                            <script type="text/javascript">
                                $(document).ready(function () {
                        
                                    $("#inCaseOfMessage").hide("fast");
                                    $("#inCaseOfTask").show("fast");
                        
                                });
                            </script>');
                            break;

                    }

                    break;

                case ArchReferral::ARCH_DESTINATION_EXTERNAL:

                    $this->Smarty->assign('jsCode', '
                    <script type="text/javascript">
                        $(document).ready(function () {                
                            $("#employeeReferral").hide("fast");
                            $("#clientReferral").show("fast");
                            $("#assignmentDiv").hide("fast");
                            $("#inCaseOfMessage").hide("fast");
                            $("#inCaseOfTask").show("fast");                
                        });
                    </script>');

                    break;

            }

        } catch (ArchReferralException $e) {
            $this->Smarty->assign('referral', []);
        }

        $this->Smarty->assign('employees',
            Vacant::getEmployeeListByEntityWithBasicVacancies($_SESSION['organization'], null, null, null, null));

        $this->Smarty->assign('referralTypes', Setting::getList(230));
        $this->Smarty->assign('assignmentTypes', Setting::getList(236));
        $this->Smarty->assign('privileges', Setting::getList(52));
        $this->Smarty->assign('priorities', Setting::getList(225));

        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);

    }

    public function referralbrowse($parm, $post)
    {

        try {
            $this->Smarty->assign('referral', ArchReferral::readID((int)$parm[0] ?? 0));
        } catch (ArchReferralException $e) {
            $this->Smarty->assign('referral', []);
        }

    }

    public function referralprint($parm, $post)
    {

        $this->referralbrowse($parm, $post);

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));

    }

    public function referralconfirm($parm, $post)
    {

        try {
            $this->Smarty->assign('referral', ArchReferral::readID((int)$parm[1] ?? 0));
        } catch (ArchReferralException $e) {
            $this->Smarty->assign('referral', []);
        }

        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function browseReferrals($parm, $post)
    {

        try {

            $thisReferral = ArchReferral::readID((int)$parm[0] ?? 0);

            try {
                $this->Smarty->assign('referrals',
                    ArchReferral::read([
                        ArchReferral::BELONG_TO  => ArchReferral::ARCH_BELONG_TO_EMPLOYEE,
                        ArchReferral::CREATED_BY => $thisReferral->employee_id,
                    ]));

            } catch (ArchReferralException $e) {
                $this->Smarty->assign('referrals', []);
            }
        } catch (ArchReferralException $e) {

        }
    }

    public function commentadd($parm, $post)
    {

        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        try{
            $referral = ArchReferral::readID($session->get('referral_id'));
//            return $referral->transaction_id;
            $referralsUsers = collect(ArchReferral::read([
                ArchReferral::TRANSACTION_ID => $referral->transaction_id
            ]));
            $referralsUsers= $referralsUsers->pluck('employee_id');

            $users = \Models\User::whereIn('sh_user_id' , $referralsUsers)->get();




            $this->Smarty->assign('users', $users);
        } catch (ArchReferralException $e){
            $this->Smarty->assign('users', []);
        }
        $this->Smarty->assign('session', $session->array);

    }

    public function commentedit($parm, $post)
    {

        try {
            $this->Smarty->assign('comment', ArchComment::readID((int)$parm[1] ?? 0));
        } catch (ArchCommentException $e) {
            $this->Smarty->assign('comment', []);
        }

        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);

    }

    public function commentconfirm($parm, $post)
    {

        try {
            $this->Smarty->assign('comment', ArchComment::readID((int)$parm[1] ?? 0));
        } catch (ArchCommentException $e) {
            $this->Smarty->assign('comment', []);
        }

        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);

    }

    public function editMyReferral($parm, $post)
    {

        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);

        try {

            $thisReferral = ArchReferral::readID($session->get('referral_id'));
            $this->Smarty->assign('thisReferral', $thisReferral);

            try {

                switch ($thisReferral->belong_to) {

                    case ArchReferral::ARCH_BELONG_TO_CLIENT:

                        $this->Smarty->assign('folders', ArchFolder::read([
                            ArchFolder::BELONG_TO   => $thisReferral->belong_to,
                            ArchFolder::CLIENT_ID   => $_SESSION['organization']->id,
                            ArchFolder::EMPLOYEE_ID => 0,
                        ]));

                        break;
                    case ArchReferral::ARCH_BELONG_TO_EMPLOYEE:

                        $this->Smarty->assign('folders', ArchFolder::read([
                            ArchFolder::BELONG_TO   => $thisReferral->belong_to,
                            ArchFolder::CLIENT_ID   => $_SESSION['organization']->id,
                            ArchFolder::EMPLOYEE_ID => $thisReferral->employee_id,
                        ]));

                        break;
                }


            } catch (ArchFolderException $e) {

                $this->Smarty->assign('folders', []);

            }

        } catch (ArchReferralException $e) {

            $this->Smarty->assign('thisReferral', []);

        }

        $this->Smarty->assign('replayTypes', Setting::getList(235));
        $this->Smarty->assign('executionTypes', Setting::getList(237));

    }

    public function browseComment($parm, $post)
    {
        try {
            $this->Smarty->assign('comment', ArchComment::readID((int)$parm[0]));
        } catch (ArchCommentException $e) {
            $this->Smarty->assign('comment', (object)[]);
        }
    }

    public function confirmExport($parm, $post)
    {
        try {
            $this->Smarty->assign('transaction', ArchTransaction::readID((int)$parm[1]));
        } catch (ArchTransactionException $e) {
            $this->Smarty->assign('transaction', (object)[]);
        }
        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function generateExportData($parm, $post)
    {
        try {
            $this->Smarty->assign('transaction', ArchTransaction::readID((int)$parm[1]));
        } catch (ArchTransactionException $e) {
            $this->Smarty->assign('transaction', (object)[]);
        }
        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function generateImportData($parm, $post)
    {
        try {
            $this->Smarty->assign('transaction', ArchTransaction::readID((int)$parm[1]));
        } catch (ArchTransactionException $e) {
            $this->Smarty->assign('transaction', (object)[]);
        }
        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function confirmImport($parm, $post)
    {
        try {
            $this->Smarty->assign('transaction', ArchTransaction::readID((int)$parm[1]));
        } catch (ArchTransactionException $e) {
            $this->Smarty->assign('transaction', (object)[]);
        }
        $session = new SessionManager($parm[0]);
        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function referralStatusChanged($parm, $post)
    {
        DB::table(sh_notif::class)
            ->where(Notification::ORG_ID, $_SESSION['organization']->id)
            ->where(Notification::ID, $parm[1])
            ->update([
                Notification::STATUS => 0,
            ]);
        try {
            $this->Smarty->assign('referral', ArchReferral::readID((int)$parm[2]));
        } catch (ArchReferralException $e) {
            $this->Smarty->assign('referral', null);
        }
    }
}