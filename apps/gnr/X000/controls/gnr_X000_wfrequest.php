<?php
// Deny Direct Script Access
use Illuminate\Support\Collection;
use Models\Finance\ExchangeRequest;
use Models\Privilege;

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_wfrequest extends Controller
{



    public function show($parm, $post)
    {

        switch ($parm[0]) {
            case 'save_session':
                $_SESSION['s_archive_browse_type'] = $parm[1];
                $_SESSION['s_archive_back_url'] = $parm[2].'/'.$parm[3].'/'.$parm[4].'/'.$parm[5].'/'.$parm[6].'/'.$parm[7];
                break;
        }

        try {
            $this->Smarty->assign('notifications',
                Notification::getArchiveListNotifications($_SESSION['organization'], $_SESSION['user']));
        } catch (NotificationException $e) {
            $this->Smarty->assign('notifications', []);
        }

    }

    public function edit($parm, $post)
    {
        // forced to place here to place correct notification data!
        // Validate actions.
            switch ($parm[0]) {

            case 'save_session':

                $_SESSION['s_wf_request_id'] = $parm[1];

                // path for program calling docs for help
                $_SESSION['x_wfrequest_edit_back_url'] = $parm[2].'/'.$parm[3].'/'.$parm[4].'/'.$parm[5].'/'.$parm[6].'/'.$parm[7];  // bundle code

                try {
                    $requestRow = Request::readID((int)$_SESSION['s_wf_request_id'] ?? 0);
                    $nextStep = $requestRow->stepObject;
                    switch ($nextStep->executor_type) {

                        case Step::FROM_OPERATION_ROW:


                            $recordRow = [];

                            try {
                                $recordRow = ($requestRow->table_name)::readByID((int)$requestRow->row_id);
                            } catch (ModelException $e) {
                            }

                            if ($nextStep->type == Request::LAST_STEP_IN_OPERATION_GRAPH) {

                                $toUserId = $requestRow->created_by;

                            } else {

                                try {

                                    $nptRow = WorkflowStepInput::readID((int)$nextStep->executor_id);
                                    $toUserId = $recordRow->{$nptRow->cname};

                                } catch (WorkflowStepInputException $e) {
                                    $toUserId = 0;
                                }

                            }

                            break;

                        case Step::REQUEST_OWNER:

                            $toUserId = $requestRow->user_id;

                            break;

                        case Step::REQUEST_OWNER_DIRECT_MANAGER:

                            $toUserId = (Vacant::getEmployeeDirectManager((int)$requestRow->user_id))->id;

                            break;

                        case Step::REQUEST_OWNER_UNIT_MANAGER:

                            /**
                             * @var $employeeBasicVacant Vacant
                             */

                            $employeeBasicVacant = Vacant::getEmployeeJobs((int)$requestRow->org_id,
                                (int)$requestRow->user_id, Vacant::EMPLOYEE_BASIC_VACANT);
                            $toUserId = (Unit::readID((int)$employeeBasicVacant->jobObject->sh_job_unit_id))->manager_id;

                            break;

                        case Step::REQUEST_CREATOR:

                            $toUserId = $requestRow->created_by;

                            break;

                        case Step::REQUEST_CREATOR_DIRECT_MANAGER:

                            $toUserId = (Vacant::getEmployeeDirectManager((int)$requestRow->created_by))->id;

                            break;

                        case Step::REQUEST_CREATOR_UNIT_MANAGER:

                            /**
                             * @var $employeeBasicVacant Vacant
                             */

                            $employeeBasicVacant = Vacant::getEmployeeJobs((int)$requestRow->org_id,
                                (int)$requestRow->created_by, Vacant::EMPLOYEE_BASIC_VACANT);
                            $toUserId = (Unit::readID((int)$employeeBasicVacant->jobObject->sh_job_unit_id))->manager_id;

                            break;

                        case Step::ANOTHER_UNIT_MANAGER:
                            try {
                                $unit = sh_unt::readByID((int)$nextStep->executor_id);
                                $toUserId = $unit->sh_unt_manager_id;
                            } catch (UnitException $e) {
                                $toUserId = 0;
                            }
                            break;

                        case Step::SPECIFIC_EMPLOYEE:
                            $toUserId = $nextStep->executor_id;
                            break;

                    }

                    try {
                        $userObject = User::readID((int)$toUserId);
                    } catch (UserException $e) {
                        $userObject = null;
                    }

                    $checkUserTodayActivities = UserActivity::inRange($userObject, date('Y-m-d'), date('Y-m-d'), Operation::readID(Operation::OPERATION_LEAVE_OPERATION));

                    if ($userObject && $checkUserTodayActivities) {

                        $alternativeUserId = 0;

                        foreach ($checkUserTodayActivities as $dayRow) {

                            /** @var ActivityData $userActivity */
                            foreach ($dayRow->data as $userActivity) {

                                $EntityTable = $userActivity->getObject()->getTableName();
                                if ($EntityTable == 'hr_lev' && $userActivity->getObject()->hr_lev_bdeel) {
                                    $alternativeUserId = (int)$userActivity->getObject()->hr_lev_bdeel;
                                }

                            }

                        }

                        if ($alternativeUserId) {

                            $acting = new WorkflowActing();
                            $acting->org_id = $requestRow->org_id;
                            $acting->request_id = $requestRow->id;
                            $acting->step_id = $nextStep->id;
                            $acting->owner_user_id = $toUserId;
                            $acting->alternative_user_id = $alternativeUserId;
                            $acting->created_by = $_SESSION['user']->id;
                            $acting->created_date = Date('Y-m-d H:i:s');
                            $acting->save();

                            $toUserId = $alternativeUserId;

                        }

                    }

                    if ($toUserId != user('id')) {
                        $this->URL->redirectToEmployeeDashboard();
                    }
                } catch (RequestException $e) {
                    $requestRow = null;
                }

                if ($requestRow) {

                    try {

                        $notifications = Notification::read([
                            Notification::TABLENAME => 'wf_request',
                            Notification::ROW_ID    => (int)$requestRow->id,
                            Notification::STP_ID    => (int)$requestRow->step_id,
                        ]);

                    } catch (NotificationException $e) {
                        $notifications = null;
                    }

                    if ($notifications) {

                        foreach ($notifications as $notification) {

                            try {

                                $notification->status = 0;
                                $notification->save();

                            } catch (NotificationException $e) {
                            }

                        }

                    }
                }

                break;

            case 'update':

                if ($_SESSION['s_wf_request_token'] == $parm[1]) {

                    Request::updateStepData($_SESSION['organization']->id, $post, $parm[2], $parm[3], 1,
                        $_SESSION['user']->id);
                    Notification::updatedAlert();

                }

                break;

            case 'notifyCommitteeMembers':

                if ($_SESSION['s_wf_request_token'] == $parm[1]) {

                    Request::notifyCommitteeMember($post, (int)$parm[2], (int)$parm[3]);
                    Notification::updatedAlert();

                }

                break;
        }

        $requestRow = [];

        try {
            $requestRow = Request::readID((int)$_SESSION['s_wf_request_id'] ?? 0);
            $this->Smarty->assign('request', $requestRow);
        } catch (RequestException $e) {
            $this->Smarty->assign('request', []);
        }

        try {
            $this->Smarty->assign('user', User::readID((int)$requestRow->created_by));
        } catch (UserException $e) {
            $this->Smarty->assign('user', []);
        }

        try {
            $this->Smarty->assign('step', $requestRow->stepObject);
        } catch (StepException $e) {
            $this->Smarty->assign('step', []);
        }

        try {
            $this->Smarty->assign('graph', $requestRow->graphObject);
        } catch (StepException $e) {
            $this->Smarty->assign('graph', []);
        }
        $orgSettings = ClientConfiguration::getEntriesSettings();
        $this->Smarty->assign('forwordFinExch', $orgSettings->forwordFinExch);


        if ($requestRow->opr_id == 179 and $orgSettings->forwordFinExch == 1){
            $users =  ExchangeRequest::getUsersWithFinExchPrivielge();

            $this->Smarty->assign('users', $users);
        }


        // Check if the step have back data
        try {

            $this->Smarty->assign('backDataList', WorkflowSendToBack::count([
                WorkflowSendToBack::ORG_ID     => $requestRow->org_id,
                WorkflowSendToBack::PRG_ID     => $requestRow->prg_id,
                WorkflowSendToBack::OPR_ID     => $requestRow->opr_id,
                WorkflowSendToBack::GRAPH_ID   => $requestRow->graph_id,
                WorkflowSendToBack::REQUEST_ID => $requestRow->id,
                WorkflowSendToBack::STEP_ID    => $requestRow->step_id,
            ]));

        } catch (WorkflowSendToBackException $e) {
            $this->Smarty->assign('backDataList', []);
        }

        /*
         * Operation Graph
         */
        if ($requestRow->graph_id && $requestRow->step_id) {

            $x = Graph::graphDiagramArray((int)$requestRow->graph_id, (int)$requestRow->step_id);

            if ($x) {

                $this->Smarty->assign('temp', $this->Helper->preparePlottableData('diagram', [
                    'id'     => 'theID',
                    'name'   => 'theName',
                    'title'  => 'theTitle',
                    'desc'   => 'theDesc',
                    'parent' => 'theParent',
                    'class'  => 'class',
                ], $x));

                $this->Smarty->assign('rand', md5(rand(0000, 9999)));

            }

        }

        $_SESSION['s_wf_request_token'] = md5(rand(0000, 9999));

    }

    public function send($parm, $post)
    {
        switch ($parm[0]) {

            case 'save_session':

                $_SESSION['s_wf_request_id'] = $parm[1];

                // path for program calling wfrequest
                $_SESSION['x_bnd_code'] = $parm[2];  // bundle code
                $_SESSION['x_prg_code'] = $parm[3];  // program id
                $_SESSION['x_opr_code'] = $parm[4];  // operation code
                $_SESSION['x_sub_code'] = $parm[5];  // edit, show, delete ...
                $_SESSION['x_pagin'] = $parm[6];     // pagination
                $_SESSION['x_lang'] = $parm[7];  // language
                break;

            case 'send':

                if ($_SESSION['wf_send_token'] == $parm[1]) {

                    Request::moveOnRequest((int)$parm[2] ?? 0);
                    Notification::alertMessage(Notification::SUCCESS, 'gnr_request_has_been_sent');

                }

                break;
        }

        try {

            $requestRow = Request::readID((int)$_SESSION['s_wf_request_id'] ?? 0);
            $this->Smarty->assign('request', $requestRow);

        } catch (RequestException $e) {

            $this->Smarty->assign('request', []);

        }

        try {
            $this->Smarty->assign('user', User::readID((int)$requestRow->created_by));
        } catch (UserException $e) {
            $this->Smarty->assign('user', []);
        }

        /*
         * Operation Graph
         */

        if ($requestRow->graph_id) {

            $x = Graph::graphDiagramArray((int)$requestRow->graph_id, (int)$requestRow->step_id);

            if ($x) {

                $this->Smarty->assign('temp', $this->Helper->preparePlottableData('diagram', [
                    'id'     => 'theID',
                    'name'   => 'theName',
                    'title'  => 'theTitle',
                    'desc'   => 'theDesc',
                    'parent' => 'theParent',
                    'class'  => 'class',
                ], $x));

                $this->Smarty->assign('rand', md5(rand(0000, 9999)));

            }

        }

        $_SESSION['wf_send_token'] = md5(rand(0000, 9999));
    }

    public function browse($parm, $post)
    {
        $requestRow = [];

        try {
            $requestRow = Request::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('request', $requestRow);
        } catch (RequestException $e) {
            $this->Smarty->assign('request', []);
        }

        try {
            $this->Smarty->assign('user', User::readID((int)$requestRow->created_by));
        } catch (UserException $e) {
            $this->Smarty->assign('user', []);
        }

        switch ($parm[1]) {
            case 'notification':

                try {

                    $notification = sh_notif::readByID((int)$parm[2]);
                    $notification->sh_notif_status = 0;
                    $notification->update();

                } catch (ModelException $e) {

                }
                break;
        }
        /*
         * Operation Graph
         */
        if ($requestRow->graph_id) {

            $x = Graph::graphDiagramArray((int)$requestRow->graph_id, (int)$requestRow->step_id);

            if ($x) {

                $this->Smarty->assign('temp', $this->Helper->preparePlottableData('diagram', [
                    'id'     => 'theID',
                    'name'   => 'theName',
                    'title'  => 'theTitle',
                    'desc'   => 'theDesc',
                    'parent' => 'theParent',
                    'class'  => 'class',
                ], $x));

                $this->Smarty->assign('rand', md5(rand(0000, 9999)));

            }

        }

    }

    public function committee($parm, $post)
    {
        // forced to place here to place correct notification data!
        // Validate actions.
        switch ($parm[0]) {

            case 'save_session':

                // path for program calling docs for help
                $_SESSION['x_wfrequest_edit_back_url'] = $parm[2].'/'.$parm[3].'/'.$parm[4].'/'.$parm[5].'/'.$parm[6].'/'.$parm[7];  // bundle code

                try {

                    $notif = Notification::readID((int)$parm[1]);

                } catch (NotificationException $e) {
                }

                if ($notif) {

                    try {

                        $notif->status = 0;
                        $notif->save();

                    } catch (NotificationException $e) {

                    }

                    $_SESSION['s_wf_request_id'] = $notif->requestObject->wf_request_id;
                    $_SESSION['s_wf_notif_id'] = $notif->id;
                }

                break;

            case 'update':
                if ($_SESSION['s_wf_request_token'] == $parm[1]) {

                    Request::updateStepData($_SESSION['organization']->id, $post, $parm[2], $parm[3], 2,
                        $_SESSION['user']->id);
                    Notification::updatedAlert();

                }
                break;

            case 'notifyCommitteeMembers':
                if ($_SESSION['s_wf_request_token'] == $parm[1]) {

                    try {
                        Request::notifyCommitteeMember($post, (int)$parm[2], (int)$parm[3]);
                    } catch (RequestException $e) {
                    }
                }
                break;
        }

        $requestRow = [];

        try {
            $requestRow = Request::readID((int)$_SESSION['s_wf_request_id'] ?? 0);
            $this->Smarty->assign('request', $requestRow);
        } catch (RequestException $e) {
            $this->Smarty->assign('request', []);
        }

        try {
            $notif = Notification::readID((int)$_SESSION['s_wf_notif_id']);
        } catch (NotificationException $e) {

        }

        if ($notif) {

            try {
                $step = Step::readID((int)$notif->stp_id);
                $this->Smarty->assign('step', $step);
            } catch (StepException $e) {
                $this->Smarty->assign('step', []);
            }

            /*
             * Operation Graph
             */

            if ($requestRow->graph_id) {

                $x = Graph::graphDiagramArray((int)$requestRow->graph_id, (int)$step->id);

                if ($x) {

                    $this->Smarty->assign('temp', $this->Helper->preparePlottableData('diagram', [
                        'id'     => 'theID',
                        'name'   => 'theName',
                        'title'  => 'theTitle',
                        'desc'   => 'theDesc',
                        'parent' => 'theParent',
                        'class'  => 'class',
                    ], $x));

                    $this->Smarty->assign('rand', md5(rand(0000, 9999)));

                }

            }

        }

        $_SESSION['s_wf_request_token'] = Helper::generateToken();
    }

    public function committeeLog($parm, $post)
    {

        $request = Request::readID((int)$parm[0] ?? 0);
        $this->Smarty->assign('request', $request);

        if ($request) {

            try {

                $this->Smarty->assign('steps', Step::read([
                    Step::GRAPH_ID          => $request->graph_id,
                    Step::COMMITTEE_SUPPORT => Setting::YES,
                ]));

            } catch (StepException $e) {

                $this->Smarty->assign('steps', []);

            }

            /*
             * Operation Graph
             */

            if ($request->graph_id) {

                $x = Graph::graphDiagramArray((int)$request->graph_id, (int)$request->stepObject->id);

                if ($x) {

                    $this->Smarty->assign('temp', $this->Helper->preparePlottableData('diagram', [
                        'id'     => 'theID',
                        'name'   => 'theName',
                        'title'  => 'theTitle',
                        'desc'   => 'theDesc',
                        'parent' => 'theParent',
                        'class'  => 'class',
                    ], $x));

                    $this->Smarty->assign('rand', md5(rand(0000, 9999)));

                }

            }

        }

        $_SESSION['s_wf_request_token'] = Helper::generateToken();
    }

    public function print($parm, $post)
    {

        $requestRow = [];

        try {
            $requestRow = Request::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('request', $requestRow);
        } catch (RequestException $e) {
            $this->Smarty->assign('request', []);
        }

        try {
            $this->Smarty->assign('user', User::readID((int)$requestRow->created_by));
        } catch (UserException $e) {
            $this->Smarty->assign('user', []);
        }

        try {
            $this->Smarty->assign('step', $requestRow->stepObject);
        } catch (StepException $e) {
            $this->Smarty->assign('step', []);
        }

        /*
         * Operation Graph
         */

        if ($requestRow->graph_id) {

            $x = Graph::graphDiagramArray((int)$requestRow->graph_id, (int)$requestRow->step_id);

            if ($x) {

                $this->Smarty->assign('temp', $this->Helper->preparePlottableData('diagram', [
                    'id'     => 'theID',
                    'name'   => 'theName',
                    'title'  => 'theTitle',
                    'desc'   => 'theDesc',
                    'parent' => 'theParent',
                    'class'  => 'class',
                ], $x));

                $this->Smarty->assign('rand', md5(rand(0000, 9999)));

            }

        }

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));
    }

    public function stepback($parm, $post)
    {
        $requestRow = [];

        try {
            $requestRow = Request::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('request', $requestRow);
        } catch (ModelException $e) {
            $this->Smarty->assign('request', []);
        }

        try {
            $this->Smarty->assign('prevStep', Step::readID($requestRow->stepObject->previous_step_id));
        } catch (StepException $e) {
            $this->Smarty->assign('prevStep', null);
        }

        try {
            $this->Smarty->assign('backData', array_shift(
                WorkflowSendToBack::read([
                    WorkflowSendToBack::ORG_ID     => $requestRow->org_id,
                    WorkflowSendToBack::PRG_ID     => $requestRow->prg_id,
                    WorkflowSendToBack::OPR_ID     => $requestRow->opr_id,
                    WorkflowSendToBack::OPR_CODE   => $requestRow->opr_code,
                    WorkflowSendToBack::GRAPH_ID   => $requestRow->graph_id,
                    WorkflowSendToBack::REQUEST_ID => $requestRow->id,
                    WorkflowSendToBack::STEP_ID    => $requestRow->stepObject->id,
                    WorkflowSendToBack::CREATED_BY => $_SESSION['user']->id,
                ])));
        } catch (WorkflowSendToBackException $e) {
            $this->Smarty->assign('backData', []);
        }

        $_SESSION['s_wf_request_token'] = Helper::generateToken();
    }

    public function stepBackData($parm, $post)
    {

        $requestRow = [];

        // Check if the step have back data
        try {
            $requestRow = Request::readID((int)$parm[0] ?? 0);
        } catch (RequestException $e) {
        }

        try {

            $this->Smarty->assign('backDataList',
                WorkflowSendToBack::read([
                    WorkflowSendToBack::ORG_ID     => $requestRow->org_id,
                    WorkflowSendToBack::PRG_ID     => $requestRow->prg_id,
                    WorkflowSendToBack::OPR_ID     => $requestRow->opr_id,
                    WorkflowSendToBack::GRAPH_ID   => $requestRow->graph_id,
                    WorkflowSendToBack::REQUEST_ID => $requestRow->id,
                    WorkflowSendToBack::STEP_ID    => $requestRow->stepObject->id,
                ]));

        } catch (WorkflowSendToBackException $e) {
        }
    }



}

