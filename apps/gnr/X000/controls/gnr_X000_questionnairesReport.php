<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_questionnairesReport extends Controller
{
    public function show($parm, $post, $files)
    {
        try {
            $this->Smarty->assign('evaluation', Evaluation::readID((int) $parm[0]));
        } catch (EvaluationException $exception) {
            $this->Smarty->assign('evaluation', null);
        }

    }

    public function analyseQuestionnaire($parm, $post, $files)
    {

        $questionnaire = null;

        try {
            $questionnaire = Questionnaire::readID((int)$parm[0]);
            $this->Smarty->assign('questionnaire', $questionnaire);
        } catch (QuestionnaireException $exception) {
            $this->Smarty->assign('questionnaire', []);
        }

        try {
            $this->Smarty->assign('units', Unit::read([
                Unit::ORG_ID => $_SESSION['organization']->id,
                Unit::ACTIVATION => Unit::UNIT_IS_ACTIVATED
            ]));
        } catch (UnitException $e) {
        }
        try {
            $this->Smarty->assign('classes', UserClass::read([
                UserClass::ORG_ID => $_SESSION['organization']->id
            ]));
        } catch (UserClassException $e) {
        }

        if ($post['report_type'] == Questionnaire::REPORT_ADVANCED) {
            $this->Smarty->assign('DataArray', Questionnaire::dataArrayForChart($questionnaire, $post));
            $this->Smarty->assign('DataArrayReport', Questionnaire::dataArrayForReport($questionnaire, $post));
            $this->Smarty->assign('post', $post);
        } else {
            $this->Smarty->assign('DataArray', Questionnaire::dataArrayForChart($questionnaire));
            $this->Smarty->assign('DataArrayReport', Questionnaire::dataArrayForReport($questionnaire));
        }

    }
}