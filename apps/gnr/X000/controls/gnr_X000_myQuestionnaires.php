<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class gnr_X000_myQuestionnaires extends Controller
{
    public function show($parm, $post, $files)
    {
        switch ($parm[0]) {
            case 'update_notification':
                    try {
                        $notification = Notification::readID((int) $parm[1] ?? 0);
                        $notification->status = 0;
                        $notification->save();
                    } catch (NotificationException $e) {
                    }
                break;

            case 'add':
                $questionnaire = $sample = null;
                $count = 0;
                try {
                    $questionnaire = Questionnaire::readID($_SESSION['s_questionnaire_id']);
                } catch (QuestionnaireException $exception) {
                }
                try {
                    $count = QTimes::count([
                        QTimes::QUESTIONNAIRE_ID => $questionnaire->id,
                        QTimes::USER_ID => $_SESSION['user']->id
                    ]);
                } catch (QTimesException $exception) {
                }

                try {
                    $sample = Sample::read([
                        Sample::USER_ID => $_SESSION['user']->id,
                        Sample::QUESTIONNAIRE_ID => $questionnaire->id
                    ])[0];
                } catch (SampleException $exception) {
                }
                if ($parm[1] == $_SESSION['s_myQuestionnaires_token']) {
                    $time = null;
                    switch ($count) {
                        case 0:
                            $time = new QTimes();
                            $time->bindProperties([]);
                            $time->eva_id = $questionnaire->eva_id;
                            $time->questionnaire_id = $questionnaire->id;
                            $time->sample_id = $sample->id;
                            $time->sample_type = $sample->type;
                            $time->save();
                            break;
                        default:
                            try {
                                $time = QTimes::read([
                                    QTimes::QUESTIONNAIRE_ID => $questionnaire->id,
                                    QTimes::USER_ID => $_SESSION['user']->id
                                ], [
                                    'orderBy' => QTimes::ID,
                                    'sortOrder' => 'DESC'
                                ])[0];
                            } catch (QTimesException $exception) {
                            }
                            break;
                    }
                    $questions = Question::read([Question::QUESTIONNAIRE_ID => $parm[2] ?? 0]);
                    foreach ($questions as $question) {
                        $qsdata = new QSData();
                        $qsdata->org_id = $_SESSION['organization']->id;
                        $qsdata->prg_id = $question->prg_id;
                        $qsdata->eva_id = $questionnaire->eva_id;
                        $qsdata->questionnaire_id = $questionnaire->id;
                        $qsdata->times_id = $time->id;
                        $qsdata->section_id = $question->section_id;
                        $qsdata->question_id = $question->id;
                        $qsdata->question_type = $question->answer_type;
                        $qsdata->value = $post[$question->id];
                        $qsdata->user_id = $_SESSION['user']->id;
                        $qsdata->sample_id = $sample->id;
                        $qsdata->sample_type = $sample->type;
                        $qsdata->created_by = $_SESSION['user']->id;
                        $qsdata->created_date = date('Y-m-d');
                        $qsdata->save();
                    }
                }
                break;

            case 'edit':

                break;

        }

        try{
            $this->Smarty->assign('SampleList', Sample::read([Sample::USER_ID => $_SESSION['user']->id],[
                0 => [
                    'property' => Sample::ID,
                    'sort' => 'DESC'
                ]
            ]));
        }catch (SampleException $e){
            $this->Smarty->assign('SampleList',[]);
        }

        $_SESSION['s_myQuestionnaires_token'] = md5(rand(0, 999));
    }

    public function save($parm, $post, $files)
    {
        $time = null;
        if ($parm[1] == $_SESSION['s_myQuestionnaires_token']) {
            try {
                $time = QTimes::readID((int) $parm[0]);
                $time->status = QTimes::ANSWERED;
                $time->save();
            } catch (QTimesException $exception) {}
        }
        $_SESSION['s_myQuestionnaires_token'] = md5(rand(0, 999));
        $this->Smarty->assign('time', $time->id);
        try {
            $this->Smarty->assign('questionnaire', Questionnaire::readID((int)$time->questionnaire_id));
        } catch (QuestionnaireException $exception) {}
    }

    public function browse($parm, $post, $files)
    {
        if ($parm[1] == 'browse' && Questionnaire::readID((int)$parm[0] ?? 0)->type == Questionnaire::FILL_ONCE) {
            $this->Smarty->assign('readonly', true);
        }
        try {
            $this->Smarty->assign('qs_data', QSData::read([
                QSData::USER_ID => $_SESSION['user']->id,
                QSData::QUESTIONNAIRE_ID => $parm[0] ?? 0,
                QSData::TIMES_ID => $parm[1] ?? 0
            ]));
        } catch (QSDataException $exception) {
        }
        $questionnaire = null;
        try {
            $questionnaire = Questionnaire::readID($parm[0] ?? 0);
            $this->Smarty->assign('questionnaire', $questionnaire);
        } catch (QuestionnaireException $exception) {
        }
        try {
            $this->Smarty->assign('evaluation', Evaluation::readID($questionnaire->eva_id));
        } catch (EvaluationException $exception) {
        }
        $_SESSION['s_myQuestionnaires_token'] = md5(rand(0, 999));
        $this->Smarty->assign('time', $parm[1] ?? 0);
    }

    public function add($parm, $post, $files)
    {
        $questionnaire = $sample = null;
        $time = null;
        $parmIndex = 1;
        if ($parm[1] == 'add') {
            $parmIndex = 3;
            $count = 0;
            try {
                $questionnaire = Questionnaire::readID($_SESSION['s_questionnaire_id']);
            } catch (QuestionnaireException $exception) {
            }
            try {
                $count = QTimes::count([
                    QTimes::QUESTIONNAIRE_ID => $questionnaire->id,
                    QTimes::USER_ID => $_SESSION['user']->id
                ]);
            } catch (QTimesException $exception) {
            }

            try {
                $sample = Sample::read([
                    Sample::USER_ID => $_SESSION['user']->id,
                    Sample::QUESTIONNAIRE_ID => $questionnaire->id
                ])[0];
            } catch (SampleException $exception) {
            }
            if($parm[1] == 'new') {
                $count = 0;
            }
            if ($parm[2] == $_SESSION['s_myQuestionnaires_token']) {
                switch ($count) {
                    case 0:
                        $time = new QTimes();
                        $time->bindProperties([]);
                        $time->eva_id = $questionnaire->eva_id;
                        $time->questionnaire_id = $questionnaire->id;
                        $time->sample_id = $sample->id;
                        $time->sample_type = $sample->type;
                        $time->save();
                        $this->Smarty->assign('time', $time->id);

                        // Create and save the answers
                        $questions = Question::read([Question::QUESTIONNAIRE_ID => $parm[0] ?? 0]);
                        foreach ($questions as $question) {
                            if($question->answer_type == Question::LIST_CHECKBOX) {
                                // Multiple insertion for checkbox

                                foreach ($post[$question->id ] as $option) {
                                    $qsdata = new QSData();
                                    $qsdata->org_id = $_SESSION['organization']->id;
                                    $qsdata->prg_id = $question->prg_id;
                                    $qsdata->eva_id = $questionnaire->eva_id;
                                    $qsdata->questionnaire_id = $questionnaire->id;
                                    $qsdata->times_id = $time->id;
                                    $qsdata->section_id = $question->section_id;
                                    $qsdata->question_id = $question->id;
                                    $qsdata->question_type = $question->answer_type;
                                    $qsdata->value = $option;
                                    $qsdata->user_id = $_SESSION['user']->id;
                                    $qsdata->sample_id = $sample->id;
                                    $qsdata->sample_type = $sample->type;
                                    $qsdata->created_by = $_SESSION['user']->id;
                                    $qsdata->created_date = date('Y-m-d');
                                    $qsdata->save();
                                }
                            } else {

                                // Single insertion
                                $qsdata = new QSData();
                                $qsdata->org_id = $_SESSION['organization']->id;
                                $qsdata->prg_id = $question->prg_id;
                                $qsdata->eva_id = $questionnaire->eva_id;
                                $qsdata->questionnaire_id = $questionnaire->id;
                                $qsdata->times_id = $time->id;
                                $qsdata->section_id = $question->section_id;
                                $qsdata->question_id = $question->id;
                                $qsdata->question_type = $question->answer_type;
                                $qsdata->value = $post[$question->id];
                                $qsdata->user_id = $_SESSION['user']->id;
                                $qsdata->sample_id = $sample->id;
                                $qsdata->sample_type = $sample->type;
                                $qsdata->created_by = $_SESSION['user']->id;
                                $qsdata->created_date = date('Y-m-d');
                                $qsdata->save();
                            }
                        }
                        break;
                    default:
                        try {
                            $time = QTimes::read([
                                QTimes::QUESTIONNAIRE_ID => $questionnaire->id,
                                QTimes::USER_ID => $_SESSION['user']->id
                            ], [
                                [
                                    'property' => QTimes::ID,
                                    'sort' => 'DESC'
                                ]
                            ])[0];
                        } catch (QTimesException $exception) {
                        }

                        $questions = Question::read([Question::QUESTIONNAIRE_ID => $parm[0] ?? 0]);
                        foreach ($questions as $question) {
                            if($question->answer_type == Question::LIST_CHECKBOX) {
                                $qsdata = [];
                                try {
                                    $qsdata = QSData::read([
                                        QSData::TIMES_ID => $time->id,
                                        QSData::QUESTION_ID => $question->id
                                    ]);
                                } catch (QSDataException $exception) {}

                                foreach ($post[$question->id] as $key => $option) {
                                    $data = $qsdata[$key];
                                    $data->save();
                                }
                            } else {
                                $qsdata = null;
                                try {
                                    $qsdata = QSData::read([
                                        QSData::TIMES_ID => $time->id,
                                        QSData::QUESTION_ID => $question->id
                                    ])[0];
                                } catch (QSDataException $exception) {}
                                try {
                                    $qsdata->value = $post[$question->id];
                                    $qsdata->save();
                                } catch (Exception $exception) {}
                            }

                        }
                        break;
                }

            }
            $this->Smarty->assign('alertmessage', 'update');
            $this->Smarty->assign('timeId', $time->id);
        }

        try {
            $this->Smarty->assign('qs_data', QSData::read([
                QSData::USER_ID => $_SESSION['user']->id,
                QSData::QUESTIONNAIRE_ID => $parm[0] ?? 0
            ], [
                0 => ['property' => QSData::ID, 'sort' => 'DESC']
            ])[0]);
        } catch (QSDataException $exception) {
        }
        $questionnaire = null;
        try {
            $questionnaire = Questionnaire::readID($parm[0] ?? 0);
            $this->Smarty->assign('questionnaire', $questionnaire);
        } catch (QuestionnaireException $exception) {
        }

        $evaluation = null;
        try {
            $evaluation =  Evaluation::readID($questionnaire->eva_id);
        } catch (EvaluationException $exception) {
        }
        $currentDate = new DateTime();
        $startDate = new DateTime($evaluation->start_date);
        $endDate = new DateTime($evaluation->end_date);

        if(($currentDate < $startDate) || ($currentDate > $endDate)) {
            $this->Smarty->assign('outDate', true);
        } else {
            $this->Smarty->assign('outDate', false);
        }

        $this->Smarty->assign('evaluation', $evaluation);
        $_SESSION['s_myQuestionnaires_token'] = md5(rand(0, 999));
        $_SESSION['s_questionnaire_id'] = (int)$parm[0];
        if($time != null) {
            $this->Smarty->assign('timeId', $parm[$parmIndex]);
        }

    }

    public function edit($parm, $post, $files)
    {
        $time = null;
        if ($parm[2] == 'edit') {
            if ($parm[3] == $_SESSION['s_myQuestionnaires_token']) {

                $questions = Question::read([Question::QUESTIONNAIRE_ID => $parm[0] ?? 0]);
                foreach ($questions as $question) {
                    try {
                        $time = QTimes::readID((int)$parm[1]);
                    } catch (QTimesException $exception) {
                    }
                    $qsdata = null;
                    try {
                        $qsdata = QSData::read([
                            QSData::TIMES_ID => $time->id,
                        ])[0];
                    } catch (QSDataException $exception) {}
                    $qsdata->value = $post[$question->id];
                    try {
                        $qsdata->save();
                    } catch (Exception $exception) {
                    }
                }
            }
            $this->Smarty->assign('alertmessage', 'update');
        }

        $questionnaire = null;
        try {
            $questionnaire = Questionnaire::readID((int)$parm[0]);
            $this->Smarty->assign('questionnaire', $questionnaire);
        } catch (QuestionnaireException $exception) {
        }
        $evaluation = null;
        try {
            $evaluation =  Evaluation::readID($questionnaire->eva_id);
        } catch (EvaluationException $exception) {
        }
        $currentDate = new DateTime();
        $startDate = new DateTime($evaluation->start_date);
        $endDate = new DateTime($evaluation->end_date);

        if(($currentDate < $startDate) || ($currentDate > $endDate)) {
            $this->Smarty->assign('outDate', true);
        } else {
            $this->Smarty->assign('outDate', false);
        }

        $_SESSION['s_myQuestionnaires_token'] = md5(rand(0, 999999));
        $this->Smarty->assign('time', $parm[1]);
        $this->Smarty->assign('timeId', $parm[1]);
    }

    public function fillQuestionnaire($parm,$post){

        $sample = [];

        try{
            $sample = Sample::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('sample', $sample);
        }catch (SampleException $e){
            $this->Smarty->assign('sample', []);
        }

        switch ($parm[1]){
            case 'sample':

                try{

                    if(QTimes::count([
                        QTimes::ORG_ID => $sample->org_id,
                        QTimes::PRG_ID => $sample->questionnaire->prg_id,
                        QTimes::EVA_ID => $sample->eva_id,
                        QTimes::USER_ID => $sample->user_id,
                        QTimes::QUESTIONNAIRE_ID => $sample->questionnaire->id,
                        QTimes::CREATED_BY => $_SESSION['user']->id
                    ])==0){
                        try{
                            $time = new QTimes();
                            $time->org_id = $sample->org_id;
                            $time->prg_id = $sample->questionnaire->prg_id;
                            $time->eva_id = $sample->eva_id;
                            $time->user_id = $sample->user_id;
                            $time->questionnaire_id = $sample->questionnaire->id;
                            $time->sample_id = $sample->id;
                            $time->created_by = $_SESSION['user']->id;
                            $time->created_date = date('Y-m-d');
                            $time->status = 0;
                            $timeID = $time->save();
                        }catch (QTimesException $e){}
                    }else{
                        if ($sample->questionnaire->type == Questionnaire::FILL_ONCE) {
                            $this->Smarty->assign('already_filled', true);
                        } else {
                            $this->Smarty->assign('already_filled', false);
                        }
                        $time = QTimes::read([
                            QTimes::ORG_ID => $sample->org_id,
                            QTimes::PRG_ID => $sample->questionnaire->prg_id,
                            QTimes::EVA_ID => $sample->eva_id,
                            QTimes::USER_ID => $sample->user_id,
                            QTimes::QUESTIONNAIRE_ID => $sample->questionnaire->id,
                            QTimes::CREATED_BY => $_SESSION['user']->id
                        ],[0=>['property'=>QTimes::ID,'sort'=>'DESC']],[1]);

                        $timeID = (int) $time[0]->id;
                    }
                }catch (QTimesException $e){}

                break;
            case 'time':
                $timeID = $parm[2];
                break;
            case 'newTime':
                try{
                    $time = new QTimes();
                    $time->org_id = $sample->org_id;
                    $time->prg_id = $sample->questionnaire->prg_id;
                    $time->eva_id = $sample->eva_id;
                    $time->user_id = $sample->user_id;
                    $time->sample_id = $sample->id;
                    $time->questionnaire_id = $sample->questionnaire->id;
                    $time->created_by = $_SESSION['user']->id;
                    $time->created_date = date('Y-m-d');
                    $time->status = 0;
                    $timeID = $time->save();
                }catch (QTimesException $e){}

                break;
            case 'updateQuestionnaireData':
                $timeID = $parm[3];
                if($_SESSION['s_myQuestionnaires_token'] === $parm[2]){
                    QSData::updateQuestionnaireData($post,(int) $parm[3] ?? 0);
                }
                break;
        }

        try{
            $this->Smarty->assign('time', QTimes::readID((int) $timeID ?? 0));
        }catch (QTimesException $e){
            $this->Smarty->assign('time', []);
        }

        $_SESSION['s_myQuestionnaires_token'] = md5(rand(0,9999));
    }

    public function showTimes($parm, $post)
    {
        /**
         * @var $sample Sample
         */
        $sample = [];

        try {
            $sample = Sample::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('sample', $sample);
        } catch (SampleException $exception) {
            $this->Smarty->assign('sample', []);
        }

        try {
            $this->Smarty->assign('timesList', QTimes::read([
                QTimes::QUESTIONNAIRE_ID => $sample->questionnaire_id,
                QTimes::SAMPLE_ID => $sample->id,
                QTimes::USER_ID => $_SESSION['user']->id
            ]));
        } catch (QTimesException $exception) {
        }
    }
}