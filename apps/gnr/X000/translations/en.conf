
[Setting]
direction = "ltr"
alignment = "left"
valignment = "right"

[Language]
code = "en"

[Program]
name = " General Program"
about = "This program contains all the functions that are caught between other system programs"

[Menus]
Notifications = "Notifications"
GeneralProgramMemories = "General program notes"
Licenses = " Licenses"
AboutUs = "About Us"
Attachments = "Attachments"
Workflow = "Workflow"
ServiceLibrary = "Service Library"
DataSamples = " Data Samples"
User = "User"
SupportTechnicalSupport = "Technical Support"
Committees = "Committees"
QuestionnaireLibrary = "Library of questionnaires"
ProjectsPreparation = " Project preparation"
EmployeeData = "Employee Data"
ArchDocumentsProcessor = "documents"
ReportsProcessMenu = "Reports Process Menu"
mediamenu = " Content"

[Operations]
notifications = "Notifications"
cronjobs = "task repeated"
showlicense = "License Review"
wordofmouth = "said about us"
documents = "documents"
wfrequest = "request"
service = "service"
servicetypeone = "the services from first type"
servicetypetwo = "the services from second type"
serviceView = "service form"
printProcessor = "print Processor"
resume = "Biography"
tickets = "Technical Support Tickets"
committee = "committee"
myQuestionnaires = " My Account"
questionnairesReport = "Evaluation Report"
buildQuestionnaire = "Build the form"
projectpreparation = " Project preparation"
employeetimeline = " Employee timeline report"
contactsdata = "contacts data"
documentComposer = "document Composer"
reportView = " Report Browser"
mediacenter = " Display"

[notifications]
p_record = "Document Records"
display_reminder = "reminder review"
p_go_to_task = " Go for the task"
p_review_task = "task review"
p_go_to_read_task = "return to review task"
p_go_to_note = "go to observation"
display_goal = "goal display"
display_policy = "policy display"
display_indicator = "indicator display"
project_preparation = "project preparation"
display_project = "project display"
project_review = "project review"
p_confirm = "project confirm"
projects_creation = "project creation"
display_notification = "display notification"
operational_projects = " Operational projects"
display_general_goal = "General goal display"
display_detailed_goal = "display detailed goal"
p_evaluation_request = "evaluation request"
p_request_file = "Request a file"
p_board = "committee board"
p_end = " End of Procedure"
p_view_forms = "Review the application register"
p_recruit_notification = " Appointment notice"
p_recruitment_requests = "Appointment Requests"
p_request_was_end = "end of request"
p_request_was_end_message = "the request was send to the end"

[documents]
SizeNotAllowed = "Size Not Allowed"
TypeNotAllowed = "Type Not Allowed"
p_attachment_title = "attachment title"
p_attachment_name = "attachment name"
p_max_size = "max size"
p_max_number = "max number"
p_extensions = "extensions"

[wfrequest]
p_creator = " Creator"
p_action_data = " Action data"
p_request_creator = "request creator"
p_date = "Date created"
p_notice = " Notice of the Committee"
p_required = "required action"
p_let_or_pass = "Keep / pass procedure"
p_hint = ". Keeping your order with you means not passing it on to the person following you, so you can go back to the procedure for later editing"
p_action_status = "action status"
p_opinion = " Opinion of the Committee"
p_accessories = "ٌRelated Actions"
p_view_payslip = "Salary Review"
p_view_strategic = " Review of the Strategic Plan"
p_view_operational = "Review of the Operational Plan"
p_view_project = " Project review"
p_view_absence = " Accountability of Absence"
p_chart = " Execution Plan"
p_actions_sys = "System of procedures"
p_error = "! You must set the process chart first"
p_record = "Action Record"
p_back = "Return to Employee Portal"
p_request_data = " Application data"
p_data = "datas"
p_step_back = "Repeat the procedure"
p_step_back_reason = " Cause the action to be reverted"
p_step_back_confirm = " Confirm the re-action"
p_step_back_data_list = " Causes of back-to-back procedure"

[service]
p_service_desc = " Service description"
p_service_settings = "Service settings"
p_service_terms = " Terms of Service"
p_service_name = "Service Name"
p_text = " Logo script"
p_targets = "Targeted"
p_ben = "number of beneficiaries"
p_open = "open"
p_required = " required number"
p_acceptance = " Needs final approval"
p_acceptance_from = "Final approval"
p_need_clear = "?are you want a service to liquidate "
p_clear_type = "Liquidation type"
p_confirm_only = "confirm only"
p_confirm_and_revision = "Adoption and review"
p_revision = " Review"
p_confirm = "confirm"
p_applicant_name = "Name of Applicant"
p_applicant_date = "date of application"
p_request = "request"
p_view_request = " View the request"
p_select_specified = "select specified"
p_required_data = "required data"
p_required_docs = " Required Documents"
p_required_to_show = " Required to be filled"
p_doc_name = "name of the document"
p_create_service = " Create a service"
p_create_service_confirm = " Are you sure you want to create this service?"
p_create_service_error = "!Sorry, this service already exists"
p_edit_service = "Modify the status of the service"
p_add_doc = " Add a new document"
p_edit_doc = "Modify document data"
p_delete_doc_confirm = "warning:When you delete this document, all user documents with the same document name will automatically be deleted"

[servicetypeone]
p_service_definition = " Definition of service"

[serviceView]
p_view_request = "View request"
p_service_data = "Service data"
p_service = "service"
p_targeted = " Service targeting category"
p_date = "date of sending the request"
p_status = "Order status"
p_pending = " Under review"
p_reviewed = " Reviewed"
p_confirmed = "confirmed"
p_comments = "comments"

[printProcessor]
p_template = " Print template"
p_invalid_printing_template = " Basic printing template or unknown process, please contact technical support"
p_no_print_templates_available = " No print templates available for type"
p_contact_it_manager = "Communicate with the Technical Manager to add templates."
p_default = "default template"
p_selected_template = " Selected print template"
p_choose = " Choose the template"
p_print = " Print background image"
p_print_using_default_template = " Print using the default organization template"

[resume]
p_user_data = "User data"
p_data = "datas"
p_image = "image"
p_address = " home address"
p_on_the_job = "On the job"
p_course_name = "name of the course"
p_train_foundation = "Name of the Center"
p_housing_type = " Accommodation type"
p_ownership_type = "ownership type"
p_rent_value = "Rental value"
p_accommodation_size = "Housing size"
p_build_type = "Type of construction"
p_work_type = " type of employment"
p_name_in_english = " Name in English"
p_talk_about_your_self = " My Vision / Mission"
p_profile_picture = " personal picture"
p_choose = " Select the file"
p_max_size = "Maximum size"
p_max_number = " Maximum number"
p_extensions = " Formulas"
p_profile_picture_deleteion_confirm = "? Are you sure you want to delete this photo"
p_delete_confirmation_message = "?Are you sure you want to delete this history"
p_name_of_work_or_employment = "Work or job title"
birthday = "Date of Birth"
p_course_to = "Training"
p_course_location = "Certification Authority"
p_select_from_menu = " Select from the menu"
amount = "Amount"
riyal = "riyal"
p_from = "from"
p_to = "to"
year = "year"
month = "month"

[committee]
p_notice = " Notice to members of the Committee"
p_name = " Name of Committee"
p_type = "Type of committee"
p_aim = " General objective of the Committee"
p_send = " Send notification"
p_error = "Sorry, there is no active committee member"
p_view = " Review of the Committee's data"

[myQuestionnaires]
p_user_forms = " Forms sent to me"
p_evaluation_name = "Name of evaluation"
p_form_name = " Name of Form"
p_form_saved = "The form has been completed"
p_thankyou_msg = " I answered this question by thanking you for this"
p_refill = " New packaging"
p_sorry = "!Sorry, the rating was closed by the originator"
p_send = " Send the form"
p_you_can_send = " You can approve the answers by clicking on (submit form)"
p_sorry_outdate = "!Sorry, the evaluation is not in its specified date"
p_my_times = "many time filled on the form"
p_time_number = "filled number"

[questionnairesReport]
p_fillers_inside = "fillers"
p_filling_count = "Number of fillings"
p_analys_questionnaire = " Form Analysis"
p_gender_report = "gender report"

[buildQuestionnaire]
p_add_section = "Add a new section"
p_section_name = " Data section name"
p_purposes = "Evaluation purposes"
p_edit_section = " Edit Section Data"
p_delete_section = " Delete section"
p_delete_section_confirm = "?Are you sure you want to delete this section"
p_delete_section_hint = "warning:When you delete this section, the head will automatically delete all sub-sections, questions, standards and terms related to this section"
p_add_question = " Add a question / standard"
p_question_text = "question text / standard"
p_answer_type = "Type of answer"
p_explain = "Clarification"
p_required = "Must be filled"
p_question_edit = "edit question / standard"
p_explain_txt = "clarification/explanation/hint about the question"
p_question_delete = "Delete criterion / question"
p_question_delete_confirm = "? Are you sure you want to delete this question / criterion"
p_edit_quiz = " Edit form data"
p_quiz_name = "Name of Form"
p_quiz_purpose = "Their purpose"
p_text = " Text option"

[projectpreparation]
p_doc = " Project Document"
p_team = "team work"
p_tabulation = "scheduled project"
p_risks = "risks"
p_indicators = "indicators"
p_unit = "Administrative unit"
p_project_name = " project name"
p_project_desc = "project defination"
p_objectives = "project objectives"
p_assumptions = " Project Assumptions"
p_include = "Within the scope of the project"
p_exclude = "Outside the scope of the project"
p_units = " Support Units"
date_of_start_and_end_of_operational_plan = "The beginning and end date of the operational plan"
p_end_date_of_project_is_end_of_last_interval = "The end date of the project is similar to the end date of the last stage of the project"
p_eval = "Evaluation Options"
p_new_tasks = " New Tasks"
p_weekends = " Rest days"
p_add_interval = " Add phase"
p_add = "add activity"
p_risk = "risk"
p_strategy = " Strategy to Overcome Risk"
p_indicator = "indicator"
p_current_value = " current value"
p_target_value = "Target value"
p_calculation_type = " Method of calculation"
p_select_manager = "Identify the project manager and approver"
p_project = "project"
p_unit_manager = "Unit Manager"
p_manager = " Project manager"
p_reviewer = "Project references"
p_confirmer = "project confirmer"
p_select_manager_error = "!You must select the unit manager first"
p_add_team_member = " Add team member"
p_task_desc = "Description of the task"
p_reports_prvs = "User report "
p_edit_team_member = " Edit team member data"
p_report_prvs = "User Report Permissions"
p_edit_manager = "Modify the project manager"
p_delete_team_member = " Delete team member"
p_delete_team_member_confirm = "? Are you sure you want to delete this member from the team"
p_add_risk = " Add risk"
p_edit_risk = "edit risk"
p_delete_risk = "delete risk"
p_delete_risk_confirm = "?Are you sure you want to delete this risk"
p_interval_name = "stage name"
division_name = "stage name"
p_interval_desc = " Stage description"
p_edit_interval = "edit stage"
p_delete_interval = "delete stage"
p_delete_interval_confirm = "?Are you sure you want to delete this stage"
p_delete_interval_error = "sorry, there are tasks related to this phase you must disassociate first!"
p_stage = "stage"
p_activity_name = " Activity Name"
p_activity_desc = " Activity description"
p_relation = "relation"
p_relation_with = "relation with"
p_project_start_date_is = ":The project's end date is"
p_activity_duration = " Duration of activity"
p_assigned_to = "executor"
p_priority = "priority"
p_edit = "Edit activity"
p_without_indicator = "without indicator"
p_delete = " Delete activity"
p_delete_confirm = "?Are you sure you want to delete this activity"
p_measure_unit = " measuring unit"
reactivate_project_confirmation = "؟ Are you sure you want to reactivate the project"
p_reactivate = "Reactivate the project"
p_add_indicator = "add indicator"
p_unit_name = " Unit name"
p_unit_type = "unit type"
p_targeted_value = "Target value"
p_edit_indicator = "edit indicator"
p_delete_indicator_confirm = "?Are you sure you want to delete this indicator"

[documentComposer]
p_add_new_transaction = "create new transaction"
p_main_topic = "topic"
p_sub_topic = "sub topic"
p_action_needed = "action needed"
p_comment = "message"
p_language = "Document Language"
p_transaction_tags = "transaction tags"
p_privacy_status = "privacy status"
p_referral_assignment_type = "type"
p_referral_execution_start_date = "starting date"
p_referral_execution_duration = "duration"
p_referral_priority_status = "priority"
p_edit_transaction_data = " Modify document data"
p_document_data = "document data"
p_transaction_folder = "folder"
p_transaction_mediator = "We received the document from"
p_mediator_name = "name"
p_mediator_phone = " Mobile number"
p_mediator_email = " E-mail"
p_mediator_date = "Date of supply"
p_mediator_comment = "deliver observation"
p_transaction_carrier = " We sent the document via"
p_carrier_name = "name"
p_carrier_phone = "Mobile number"
p_carrier_email = " E-mail"
p_carrier_date = "export date"
p_carrier_comment = "export observation"
p_tags = "Composition"
p_referral_folder = "folder"
p_mediator_data = "Date Document Reciever"
p_sender_data = " Sender data via"
p_sender_name = "name"
p_sender_phone = "Mobile Number"
p_sender_email = "Email"
p_sending_comment = "sending comment"
p_documentary_information = " Document data"
p_transaction_information = " Document information"
p_referral_subject_title = "title"
p_referral_subject_comment = "message"
p_referral_execution_type = "execution"
p_referral_transaction_execution_status = "execution status"
p_my_referral_setting = "setting"
p_transaction_referrals = " Referrals"
p_client_outbox = "client outbox"
p_transaction_append_and_comment = " Attach and comment"
p_referrals = "referrals"
p_referral_to = "refer to "
p_referral_title = "title"
p_title = "title"
p_attach_title = "attach title"
p_max_size = "maximum size"
p_documents = "documents"
p_max_number = "Maximum Limit"
p_extensions = "extensions"
p_add_referral = "add referral"
p_referral_type = "referral type"
p_referral_privileges = " terms of reference"
p_referral_to_client_outbox = " Export via an issued Outbox"
p_edit_referral = "edit referral"
p_referral_destination = " Referral Destination"
p_delete_referral = "delete referral"
p_referral_deletion_confirm_message = "? Confirm deletion of this referral"
p_add_append_comment = " Add Append / Trackback"
p_subject = "subject"
p_edit_append_comment = " Edit append / trackback"
p_delete_append_comment = " Delete append / trackback"
p_edit_mandate = "Modification of assignment data"
p_transaction_execution_types = " Execution options"

[mediacenter]
p_license = "license"
p_days = "days"
p_view = "view"
p_job_name = "job name"
p_job_code = "job code"
p_job_unit = "job unit"
p_job_def = "job def"
p_job_type = "job type"
p_emp_sex = "emp gender"
p_doam_type = "doam type"
p_job_gnr_def = "job gnr def"
p_desc = "desc"
p_res_tasks = "res tasks"
p_relations = "relations"
p_direct_boss = "direct boss"
p_emp_supervising = "emp supervising"
p_in_org_contact = "in org contact"
p_out_org_contact = "out org contact"
p_emp_power = "emp power"
p_job_invironment = "job environment"
p_job_places = "job places"
p_eidt_job_invorn = "edit job invi"
p_job_condition = "job condition"
p_job_coalif = "job coal"
p_work_exep = "work exp"
p_maarif = "maarif"
p_rep_job_name_ar = "rep job name ar"
p_rep_job_unit = "rep job unit"
p_job_card_report = "job card report"
p_edit_job_code = "edit job code"
p_edit_job_def = "edit job def"
p_edit_job_type = "edit job type"
p_edit_emp_sex = "edit emp gender"
p_edit_doam_type = "edit doam type"
p_edit_job_gnr_def = "edit job gnr def"
p_edit_res_tasks = "edit res tasks"
p_edit_relations = "edit relations"
p_edit_direct_boss = "edit direct boss"
p_edit_emp_supervising = "edit emp supervising"
p_edit_in_org_contact = "edit in org contact"
p_edit_out_org_contact = "edit out org contact"
p_edit_emp_power = "edit emp power"
p_edit_job_invironment = "edit job environment"
p_edit_job_places = "edit job places"
p_edit_job_condition = "edit job condition"
p_edit_job_coalif = "edit job coalif"
p_edit_work_exep = "edit work exep"
p_edit_maarif = "edit maarif"
p_advance = "advance"
p_done = "done"
p_not_done = "not done"
p_absence = "absent"
p_no_absence_records = "no absence records"
p_advances = "advances"
p_no_advances_requests = "no advances requests"
p_deducting_requests = "deducting requests"
p_no_deducting_requests = "no deducting requests"
p_the_absence = "absence"
p_number_of_duty_days = "number of duty days"
p_number_of_absence_days = "number of absence days"
p_deduction_value = "deduction leave"
p_set_date_and_lang = "set date and language"
p_display_lang = "display language"
p_browse_leave_details = "browse leave details"
p_leave_name = "leave name"
p_leave_description = "leave description"
p_leave_entitled_sex = "leave entitled gender"
p_leave_conditions = "leave conditions"
p_annual_leave_credit = "annual leave credit"
p_credit_num_of_days = "credit number of days"
p_transfer_credit = "transfer credit"
p_credit_max_limit_when_transfer = "credit max limit when transfer"
p_end_of_leave_year = "end of leave year"
p_type = "type"
p_balance = "balance"
p_year_balance = "year balance"
p_max_balance = "max balance"
p_holidays_record = "holidays record"
p_holidays = "holidays"
p_weekly = "weekly"
p_relax_day = "relax day"
