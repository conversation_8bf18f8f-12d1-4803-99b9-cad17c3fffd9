
[Setting]
direction = "ltr"
alignment = "left"
valignment = "right"

[Language]
code = "bn"

[Program]
name = "নাম"
about = "সংজ্ঞা"

[Menus]
Notifications = "বিজ্ঞপ্তিগুলি"
GeneralProgramMemories = "সাধারণ প্রোগ্রাম নোট"
Licenses = "লাইসেন্সকরণ"
AboutUs = "আমাদের সম্পর্কে"
Attachments = "সংযুক্তিসমূহ"
Workflow = "প্রক্রিয়া উইজার্ড"
ServiceLibrary = "লাইব্রেরি সেবা"
DataSamples = "ডেটা মডেল"
User = ""
SupportTechnicalSupport = ""
Committees = ""
QuestionnaireLibrary = ""
ProjectsPreparation = ""
EmployeeData = ""
ArchDocumentsProcessor = ""
ReportsProcessMenu = ""
mediamenu = ""

[Operations]
notifications = "বিজ্ঞপ্তিগুলি"
cronjobs = "পুনরাবৃত্তিমূলক কাজগুলি"
showlicense = "লাইসেন্স পর্যালোচনা"
wordofmouth = "তারা আমাদের সম্পর্কে বলেন"
documents = "কাগজপত্র"
wfrequest = "অ্যাপ্লিকেশন"
service = "সেবা"
servicetypeone = "টাইপ I এর পরিষেবাগুলি"
servicetypetwo = "টাইপ দ্বিতীয় পরিষেবা"
serviceView = "পরিষেবা মডেল"
printProcessor = "প্রিন্ট উইজার্ড"
resume = ""
tickets = ""
committee = ""
myQuestionnaires = ""
questionnairesReport = ""
buildQuestionnaire = ""
projectpreparation = ""
employeetimeline = ""
contactsdata = ""
documentComposer = ""
reportView = ""
mediacenter = ""

[notifications]
p_record = "কর্মের রেকর্ড"
display_reminder = "অনুস্মারক পর্যালোচনা করুন"
p_go_to_task = "টাস্ক জন্য যান"
p_review_task = "টাস্ক পর্যালোচনা করুন"
p_go_to_read_task = ""
p_go_to_note = "একটি নোট জন্য যান"
display_goal = "লক্ষ্য পর্যালোচনা করুন"
display_policy = "নীতি পর্যালোচনা"
display_indicator = "সূচক পর্যালোচনা করুন"
project_preparation = "প্রকল্প প্রস্তুতি"
display_project = "প্রকল্প পর্যালোচনা"
project_review = "প্রকল্পটি পর্যালোচনা করুন"
p_confirm = "একটি খসড়া গ্রহণ"
projects_creation = "প্রকল্পগুলি তৈরি করুন"
display_notification = "নোটিশটি পর্যালোচনা করুন"
operational_projects = "অপারেশন প্রজেক্ট"
display_general_goal = "অপারেশন প্রজেক্ট"
display_detailed_goal = "বিস্তারিত লক্ষ্য পর্যালোচনা"
p_evaluation_request = "طلب تقييم"
p_request_file = "একটি ফাইল অভ্যর্থনা অনুরোধ"
p_board = "প্যানেল প্যানেল"
p_end = "প্রক্রিয়া শেষে"
p_view_forms = "আবেদন পত্রের পর্যালোচনা করুন"
p_recruit_notification = "নিয়োগ বিজ্ঞপ্তি"
p_recruitment_requests = "নিয়োগের অনুরোধ"
p_request_was_end = "অনুরোধ শেষ হয়"
p_request_was_end_message = "চাহিদা শেষ হয়ে গেছে"

[documents]
SizeNotAllowed = "SizeNotAllowed"
TypeNotAllowed = "TypeNotAllowed"
p_attachment_title = "p_attachment_title"
p_attachment_name = "p_attachment_name"
p_max_size = "p_max_size"
p_max_number = " p_max_number"
p_extensions = "p_extensions"

[wfrequest]
p_creator = "জন্মদাতা"
p_action_data = "অ্যাকশন ডেটা"
p_request_creator = "অনুরোধ বিল্ডার"
p_date = "প্রতিষ্ঠিতকমিটির নোটিশ"
p_notice = "কমিটির নোটিশ"
p_required = "কর্ম প্রয়োজন"
p_let_or_pass = "রাখুন / পদ্ধতি পাস"
p_hint = "আপনার সাথে আপনার অর্ডার রাখা আপনার অনুসরণকারী ব্যক্তির উপর এটি না পাস মানে, তাই আপনি পরে সম্পাদনা করার জন্য প্রক্রিয়া ফিরে যেতে পারেন"
p_action_status = "মোশন অ্যাকশন"
p_opinion = "কমিটির মতামত"
p_accessories = "অ্যাকশন আইটেম"
p_view_payslip = "বেতন পর্যালোচনা"
p_view_strategic = "কৌশলগত পরিকল্পনা পর্যালোচনা"
p_view_operational = "অপারেশন প্ল্যানের পর্যালোচনা"
p_view_project = "প্রকল্প পর্যালোচনা"
p_view_absence = "অযোগ্যতার দায়বদ্ধতা"
p_chart = "এক্সিকিউশন প্ল্যান"
p_actions_sys = "পদ্ধতির "
p_error = "আপনি প্রথম প্রক্রিয়াকরণ চার্ট সেট করতে হবে"
p_record = "কর্মের রেকর্ড"
p_back = "কর্মচারী গেটে ফিরে যান"
p_request_data = "অ্যাপ্লিকেশন ডেটা"
p_data = "উপাত্ত"
p_step_back = "পদ্ধতি পুনরাবৃত্তি"
p_step_back_reason = "কর্ম ফেরত হতে হবে কারণ"
p_step_back_confirm = "পুনরায় কর্ম নিশ্চিত করুন"
p_step_back_data_list = "ব্যাক টু ব্যাক পদ্ধতির কারণগুলি"

[service]
p_service_desc = "পরিষেবা বিবরণ"
p_service_settings = "পরিষেবা সেটিংস"
p_service_terms = "পরিষেবার শর্তাদি"
p_service_name = "সার্ভিস নাম"
p_text = "লোগো স্ক্রিপ্ট"
p_targets = "অভিপ্রেত"
p_ben = "লাভবানদের সংখ্যা"
p_open = "খোলা"
p_required = "নম্বর প্রয়োজন"
p_acceptance = "চূড়ান্ত অনুমোদন প্রয়োজন"
p_acceptance_from = "চূড়ান্ত অনুমোদন"
p_need_clear = "আপনি ফিল্টার পরিষেবা প্রয়োজন"
p_clear_type = "ফিল্টার টাইপ"
p_confirm_only = "শুধু অনুমোদন"
p_confirm_and_revision = "দত্তক এবং পর্যালোচনা"
p_revision = "পর্যালোচনা"
p_confirm = "আস্থা"
p_applicant_name = "আবেদনকারীর নাম"
p_applicant_date = "অগ্রগতির তারিখ"
p_request = "চাহিদা"
p_view_request = "অনুরোধটি দেখুন"
p_select_specified = "নির্বাচিত নির্বাচন করুন"
p_required_data = "প্রয়োজনীয় তথ্য"
p_required_docs = "প্রয়োজনীয় নথি"
p_required_to_show = "ভরাট করা আবশ্যক"
p_doc_name = "নথির নাম"
p_create_service = "একটি সেবা তৈরি করুন"
p_create_service_confirm = "আপনি কি এই পরিষেবাটি তৈরি করতে চান তা নিশ্চিত"
p_create_service_error = "দুঃখিত, এই পরিষেবাটি ইতিমধ্যে বিদ্যমান"
p_edit_service = "পরিষেবাটির অবস্থা পরিবর্তন করুন"
p_add_doc = "একটি নতুন নথি যোগ করুন"
p_edit_doc = "নথি ডেটা সংশোধন করুন"
p_delete_doc_confirm = "সাবধানতা: যখন আপনি এই দস্তাবেজটি মুছবেন, তখন একই ডকুমেন্টের নাম সহ সমস্ত ব্যবহারকারীর নথি স্বয়ংক্রিয়ভাবে মোছা হবে"

[servicetypeone]
p_service_definition = "সেবা সংজ্ঞা"

[serviceView]
p_view_request = "অনুরোধ দেখুন"
p_service_data = "পরিষেবা ডেটা"
p_service = "সেবা"
p_targeted = "পরিষেবা লক্ষ্য বিভাগ"
p_date = "অনুরোধ পাঠানোর তারিখ"
p_status = "অর্ডারের স্থিতি"
p_pending = "পর্যালোচনা অধীনে"
p_reviewed = "রিভিউটা"
p_confirmed = "স্বীকৃতি"
p_comments = "মন্তব্য"

[printProcessor]
p_template = "প্রিন্ট টেমপ্লেট"
p_invalid_printing_template = "বেসিক প্রিন্টিং টেমপ্লেট বা অজানা প্রক্রিয়া, প্রযুক্তিগত সহায়তার সাথে যোগাযোগ করুন"
p_no_print_templates_available = "টাইপ জন্য কোন মুদ্রণ টেম্পলেট উপলব্ধ"
p_contact_it_manager = "টেমপ্লেট যোগ করতে টেকনিক্যাল ম্যানেজারের সাথে যোগাযোগ করুন"
p_default = ""
p_selected_template = ""
p_choose = ""
p_print = ""
p_print_using_default_template = ""

[resume]
p_user_data = ""
p_data = ""
p_image = ""
p_address = ""
p_on_the_job = ""
p_course_name = ""
p_train_foundation = ""
p_housing_type = ""
p_ownership_type = ""
p_rent_value = ""
p_accommodation_size = ""
p_build_type = ""
p_work_type = ""
p_name_in_english = ""
p_talk_about_your_self = ""
p_profile_picture = ""
p_choose = ""
p_max_size = ""
p_max_number = ""
p_extensions = ""
p_profile_picture_deleteion_confirm = ""
p_delete_confirmation_message = ""
p_name_of_work_or_employment = ""
birthday = ""
p_course_to = ""
p_course_location = ""
p_select_from_menu = ""
amount = ""
riyal = ""
p_from = ""
p_to = ""
year = ""
month = ""

[committee]
p_notice = ""
p_name = ""
p_type = ""
p_aim = ""
p_send = ""
p_error = ""
p_view = ""

[myQuestionnaires]
p_user_forms = ""
p_evaluation_name = ""
p_form_name = ""
p_form_saved = ""
p_thankyou_msg = ""
p_refill = ""
p_sorry = ""
p_send = ""
p_you_can_send = ""
p_sorry_outdate = ""
p_my_times = ""
p_time_number = ""

[questionnairesReport]
p_fillers_inside = ""
p_filling_count = ""
p_analys_questionnaire = ""
p_gender_report = ""

[buildQuestionnaire]
p_add_section = ""
p_section_name = ""
p_purposes = ""
p_edit_section = ""
p_delete_section = ""
p_delete_section_confirm = ""
p_delete_section_hint = ""
p_add_question = ""
p_question_text = ""
p_answer_type = ""
p_explain = ""
p_required = ""
p_question_edit = ""
p_explain_txt = ""
p_question_delete = ""
p_question_delete_confirm = ""
p_edit_quiz = ""
p_quiz_name = ""
p_quiz_purpose = ""
p_text = ""

[projectpreparation]
p_doc = ""
p_team = ""
p_tabulation = ""
p_risks = ""
p_indicators = ""
p_unit = ""
p_project_name = ""
p_project_desc = ""
p_objectives = ""
p_assumptions = ""
p_include = ""
p_exclude = ""
p_units = ""
date_of_start_and_end_of_operational_plan = ""
p_end_date_of_project_is_end_of_last_interval = ""
p_eval = ""
p_new_tasks = ""
p_weekends = ""
p_add_interval = ""
p_add = ""
p_risk = ""
p_strategy = ""
p_indicator = ""
p_current_value = ""
p_target_value = ""
p_calculation_type = ""
p_select_manager = ""
p_project = ""
p_unit_manager = ""
p_manager = ""
p_reviewer = ""
p_confirmer = ""
p_select_manager_error = ""
p_add_team_member = ""
p_task_desc = ""
p_reports_prvs = ""
p_edit_team_member = ""
p_report_prvs = ""
p_edit_manager = ""
p_delete_team_member = ""
p_delete_team_member_confirm = ""
p_add_risk = ""
p_edit_risk = ""
p_delete_risk = ""
p_delete_risk_confirm = ""
p_interval_name = ""
division_name = ""
p_interval_desc = ""
p_edit_interval = ""
p_delete_interval = ""
p_delete_interval_confirm = ""
p_delete_interval_error = ""
p_stage = ""
p_activity_name = ""
p_activity_desc = ""
p_relation = ""
p_relation_with = ""
p_project_start_date_is = ""
p_activity_duration = ""
p_assigned_to = ""
p_priority = ""
p_edit = ""
p_without_indicator = ""
p_delete = ""
p_delete_confirm = ""
p_measure_unit = ""
reactivate_project_confirmation = ""
p_reactivate = ""
p_add_indicator = ""
p_unit_name = ""
p_unit_type = ""
p_targeted_value = ""
p_edit_indicator = ""
p_delete_indicator_confirm = ""

[documentComposer]
p_add_new_transaction = ""
p_main_topic = ""
p_sub_topic = ""
p_action_needed = ""
p_comment = ""
p_language = ""
p_transaction_tags = ""
p_privacy_status = ""
p_referral_assignment_type = ""
p_referral_execution_start_date = ""
p_referral_execution_duration = ""
p_referral_priority_status = ""
p_edit_transaction_data = ""
p_document_data = ""
p_transaction_folder = ""
p_transaction_mediator = ""
p_mediator_name = ""
p_mediator_phone = ""
p_mediator_email = ""
p_mediator_date = ""
p_mediator_comment = ""
p_transaction_carrier = ""
p_carrier_name = ""
p_carrier_phone = ""
p_carrier_email = ""
p_carrier_date = ""
p_carrier_comment = ""
p_tags = ""
p_referral_folder = ""
p_mediator_data = ""
p_sender_data = ""
p_sender_name = ""
p_sender_phone = ""
p_sender_email = ""
p_sending_comment = ""
p_documentary_information = ""
p_transaction_information = ""
p_referral_subject_title = ""
p_referral_subject_comment = ""
p_referral_execution_type = ""
p_referral_transaction_execution_status = ""
p_my_referral_setting = ""
p_transaction_referrals = ""
p_client_outbox = ""
p_transaction_append_and_comment = ""
p_referrals = ""
p_referral_to = ""
p_referral_title = ""
p_title = ""
p_attach_title = ""
p_max_size = ""
p_max_number = ""
p_extensions = ""
p_add_referral = ""
p_referral_type = ""
p_referral_privileges = ""
p_referral_to_client_outbox = ""
p_edit_referral = ""
p_referral_destination = ""
p_delete_referral = ""
p_referral_deletion_confirm_message = ""
p_add_append_comment = ""
p_subject = ""
p_edit_append_comment = ""
p_delete_append_comment = ""
p_edit_mandate = ""
p_transaction_execution_types = ""

[mediacenter]
p_license = ""
p_days = ""
p_view = ""
p_job_name = ""
p_job_code = ""
p_job_unit = ""
p_job_def = ""
p_job_type = ""
p_emp_sex = ""
p_doam_type = ""
p_job_gnr_def = ""
p_desc = ""
p_res_tasks = ""
p_relations = ""
p_direct_boss = ""
p_emp_supervising = ""
p_in_org_contact = ""
p_out_org_contact = ""
p_emp_power = ""
p_job_invironment = ""
p_job_places = ""
p_eidt_job_invorn = ""
p_job_condition = ""
p_job_coalif = ""
p_work_exep = ""
p_maarif = ""
p_rep_job_name_ar = ""
p_rep_job_unit = ""
p_job_card_report = ""
p_edit_job_code = ""
p_edit_job_def = ""
p_edit_job_type = ""
p_edit_emp_sex = ""
p_edit_doam_type = ""
p_edit_job_gnr_def = ""
p_edit_res_tasks = ""
p_edit_relations = ""
p_edit_direct_boss = ""
p_edit_emp_supervising = ""
p_edit_in_org_contact = ""
p_edit_out_org_contact = ""
p_edit_emp_power = ""
p_edit_job_invironment = ""
p_edit_job_places = ""
p_edit_job_condition = ""
p_edit_job_coalif = ""
p_edit_work_exep = ""
p_edit_maarif = ""
p_advance = ""
p_done = ""
p_not_done = ""
p_absence = ""
p_no_absence_records = ""
p_advances = ""
p_no_advances_requests = ""
p_deducting_requests = ""
p_no_deducting_requests = ""
p_the_absence = ""
p_number_of_duty_days = ""
p_number_of_absence_days = ""
p_deduction_value = ""
p_set_date_and_lang = ""
p_display_lang = ""
p_browse_leave_details = ""
p_leave_name = ""
p_leave_description = ""
p_leave_entitled_sex = ""
p_leave_conditions = "p_leave_conditions"
p_annual_leave_credit = ""
p_credit_num_of_days = "p_credit_num_of_days"
p_transfer_credit = "_transfer_credit"
p_credit_max_limit_when_transfer = "p_credit_max_limit_when_transfer"
p_end_of_leave_year = "p_end_of_leave_year"
p_type = "p_type"
p_balance = "p_balance"
p_year_balance = "p_year_balance"
p_max_balance = "p_max_balance"
p_holidays_record = "p_holidays_record"
p_holidays = "p_holidays"
p_weekly = "p_weekly"
p_relax_day = "p_relax_day"
