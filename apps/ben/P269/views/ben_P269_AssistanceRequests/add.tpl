{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add#}</h4>
    </div>
    <div class="modal-body">
        {if count($gates) gte 1}

        <form method="post"
              action='{url urltype="path" url_string="ben/P269/AssistanceRequests/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_AssistanceRequests_token}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">



                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_assistance_year#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        {$assistanceYear->name}&nbsp;:&nbsp;{getdate table=assist_year col=start_date type=show row=$assistanceYear}
                        &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
                    </div>


                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_assistance#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $assistances as $assistance}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="assistance_id" value="{$assistance->id}">
                                        <span class="text">{t v=$assistance->class_id}
                                            &nbsp;&raquo;&nbsp;{getname table=assist_type id=$assistance->type_id}&nbsp;&raquo;&nbsp;{$assistance->name}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    {*<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_sponsership_type#}</div>*}
                    {*<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">*}
                        {*<div class="control-group">*}
                            {*<select name="sponsorship_type">*}
                                {*{foreach $sponsorshipTypes as $type}*}
                                    {*<option value="{$type->id}">{$type->translatedName}</option>*}
                                {*{/foreach}*}
                            {*</select>*}
                        {*</div>*}
                    {*</div>*}

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_sponsership_type#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <select name="gate_id">
                                {foreach $gates as $gate}
                                    <option value="{$gate->id}">
                                        {if $gate->is_public eq '1134'}
                                            خاص
                                        {elseif $gate->is_public eq '1135'}
                                            عام
                                        {/if}
                                        >>
                                        {$gate->category->name}
                                        >>
                                        {$gate->name}
                                    </option>
                                {/foreach}
                            </select>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_date#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=assist_request col=date type=add row=$row min=$assistanceYear->start_date max=$assistanceYear->end_date}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                    </div>
                </div>
            </div>
        </form>

        {else}
            <span class="danger">{#p_gates_doesnot_found#}</span>
        {/if}
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
