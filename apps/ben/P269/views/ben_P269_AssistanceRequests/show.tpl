{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
`{block name=head_style}
    <!--Page Related styles-->
<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}

    {if $assistanceYear}

        <h5 class="row-title before-blue">
            <i class="glyphicon glyphicon-list-alt blue"></i>
            {$assistanceYear->name}&nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
            &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
        </h5>

        {if $AssistanceRequestGrf eq 0}
            {GraphNotPreparedCorrectlly oprCode='AssistanceRequests'}
        {else}
            <div class="widget {if $requests}collapsed{/if}">
                <div class="widget-header bg-blue">
                    <i class="widget-icon fa fa-arrow-left"></i>
                    <span class="widget-caption">{#gnr_search#}</span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-{if $requests}plus{else}minus{/if}"></i>
                        </a>
                    </div>
                </div>

                <div class="widget-body" style="padding: 2rem;">

                    <form action="{url urltype=path url_string="ben/P269/AssistanceRequests/show/0/{$smarty.session.lang}/search"}"
                          method="post">

                            <div class="row">

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_type#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                    <select name="type">
                                        <option value="">{#p_choose_type#}</option>
                                        {foreach $types as $type}
                                            <option  value="{$type->id}" {if $type->id eq $smarty.session.search_tasks['type_id']} selected {/if} >
                                                {$type->name}
                                            </option>
                                        {/foreach}
                                    </select>
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_name#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                    class="form-control"
                                                                                                    name="name"
                                                                                                    value="{$smarty.session.search_tasks['name']}">
                                </div>



                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_category#}</div>
                                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><select name="category_id">
                                            <option value="">{#p_choose_category#}</option>
                                            {foreach $categories as $category}
                                                <option value="{$category->id}" {if $category->id eq $smarty.session.search_tasks['category_id']} selected {/if}>
                                                    {$category->translatedName}
                                                </option>
                                            {/foreach}
                                        </select>
                                    </div>

                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_created_by#}</div>
                                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                        <select name="created_by">
                                            <option value="">{#p_choose_created_by#}</option>
                                            {foreach $users as $user}
                                                <option value="{$user->sh_user_id}" {if $user->sh_user_id eq $smarty.session.search_tasks['created_by']} selected {/if}>
                                                    {$user->sh_user_full_name}&nbsp;
                                                </option>
                                            {/foreach}
                                        </select>
                                    </div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_from#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                    {getdate type=report row={$smarty.session.search_tasks['from']|default:''} col=from}
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_to#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                    {getdate type=report row={$smarty.session.search_tasks['to']|default:''} col=to}
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 p-0">
                                    <input type="submit" class="btn btn-success sharp mr-4-px" name="submit"
                                           value="{#gnr_view#}">

                                    {if $requests}
                                        <a href="{url urltype=path url_string="ben/P269/AssistanceRequests/show/0/{$smarty.session.lang}/menu"}" class="btn btn-default shiny">
                                            {#gnr_cancel_search#}
                                        </a>
                                    {/if}
                                </div>
                            </div>
                    </form>
                </div><!--Widget Body-->
            </div>



            <table  class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th width="5%" style="background-color: #A0D468 !important;">{url check=1 urltype="madd" opr_code='AssistanceRequests' url_string="ben/P269/AssistanceRequests/add/0/{$smarty.session.lang}" }</th>
                    <th width="10%" style="background-color: #A0D468 !important;">{#p_date#}</th>
                    <th width="35%" style="background-color: #A0D468 !important;">{#p_data#}</th>
                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_preparation#}</th>
                    <th width="15%" style="background-color: #A0D468 !important;">{#p_setting_procedure#}</th>
                    <th width="15%" style="background-color: #A0D468 !important;">{#gnr_settings#}</th>

                </tr>
                </thead>
                <tbody>
                {$i=1}
                {foreach $requests as $request}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td>{getdate table=wf_request col=date type=show row=$request->dataObject}</td>
                        <td style="text-align: {#alignment#};">
                            <span style= "font-size: smaller ; color: #1f6377; font-weight: bolder;">
                                {getname table=assist_assistance id=$request->dataObject->assistance_id}
                                /
                                {getname table=assist_type id=$request->dataObject->type_id}
                                &nbsp;/&nbsp;
                                {t v=$request->dataObject->class_id}
                            </span>
                        </td>
                        <td>{getname table=sh_user id=$request->created_by}</td>
                        <td align="center">
                            {if $request->dataObject->requestIsReadyToSend}
                                {if $request->created_by eq $smarty.session.user->id}
                                    {workflow requestId=$request->id backTo="ben/P269/AssistanceRequests/show/0/{$smarty.session.lang}"}
                                {/if}
                            {else}
                                <span style="color: red">{#p_request_is_not_ready#}</span>
                            {/if}
                        </td>
                        <td nowrap align="center">
                            {if $request->send_status eq Request::REQUEST_IS_NOT_SEND}
                                {url check=1 urltype="mdelete" opr_code='AssistanceRequests' url_string="ben/P269/AssistanceRequests/confirm/0/{$smarty.session.lang}/{$request->dataObject->id}"}
                            {/if}
                            {url check=0 urltype="new" opr_code='AssistanceRequests' url_string="gnr/X000/mediacenter/browseAssistanceRequest/0/{$smarty.session.lang}/{$request->dataObject->id}" text_value="<i class='fa fa-file-o'></i>"}
                            {if !$request->dataObject->requestIsReadyToSend}
                                {url check=0 urltype="button" opr_code='AssistanceRequests' url_string="ben/P269/AssistanceRequests/prepareRequest/0/{$smarty.session.lang}/{$request->dataObject->id}" text_value="{#gnr_preparation#}"}
                            {/if}
                        </td>
                    </tr>
                {/foreach}
                </tbody>
            </table>

        {/if}

    {else}

        <span style="color: red">{#p_you_should_activate_assistance_year_first#}</span>

    {/if}

{/block}

{block name=page_header}
<script type="text/javascript" src="/templates/assets/js/loader.js"></script>
{DrawChart Data=$Data title={#gnr_chart#} RandNumber="usersType-chart"}
<!-- Chart Libraries -->
<script src="/templates/assets/js/charts/morris/raphael-2.0.2.min.js"></script>
<script src="/templates/assets/js/charts/morris/morris.js"></script>

<script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
<script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
<script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
<script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
<script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
<script>
    {literal}
    function exportTo(ID, type) {
        $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
    }
    {/literal}
</script>
<script>
    var InitiateSimpleDataTable = function() {
        return {
            init: function() {
                //Datatable Initiating
                var oTable = $('.sortable-table').dataTable({
                    "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                    "iDisplayLength": 50,
                    "oTableTools": {
                        "aButtons": [

                        ],
                        "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                    },
                    "language": {
                        "search": "",
                        "sLengthMenu": "_MENU_",
                        "oPaginate": {
                            "sPrevious": "{#gnr_previous#}",
                            "sNext": "{#gnr_next#}"
                        }
                    }
                });
                $("tfoot input").keyup(function() {
                    /* Filter on the column (the index) of this element */
                    oTable.fnFilter(this.value, $("tfoot input").index(this));
                });
            }

        };

    }();

    InitiateSimpleDataTable.init();
</script>
{/block}