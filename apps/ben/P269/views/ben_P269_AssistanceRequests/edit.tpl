{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal">&times;</button>
	<h4 class="modal-title">{#p_edit_fin_exch#}</h4>
</div>
<div class="modal-body">

<form  method="post" action='{url urltype="path" url_string="ben/P269/AssistanceRequests/show/0/{$smarty.session.lang}/update/{$smarty.session.s_AssistanceRequests_token}/{$row->id}"}'>
	<div class="row">
		<div class="col-lg-12">
			<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_beneficiary#}</div>
			<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" name="beneficiary" dir="rtl" value="{$row->beneficiary}" class="form-control" placeholder="{#p_receipt_beneficiary#}" required="required"></div>

			<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_amount_as_number#}</div>
			<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="number" step="any" name="amount_number" class="form-control" dir="rtl" value="{$row->amount_number}" placeholder="{#p_amount_as_number#}" required="required"></div>

			<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_amount_written#}</div>
			<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" name="amount_text" dir="rtl" value="{$row->amount_text}" class="form-control" placeholder="{#p_amount_written#}" required="required"></div>

			<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_purpose#}</div>
			<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control" id="purpose" name="purpose" placeholder="{#p_receipt_purpose#}" required="required">{$row->purpose}</textarea></div>

			<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
			<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button></div>
		</div>
	</div>
</form>
</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>

{/block}
