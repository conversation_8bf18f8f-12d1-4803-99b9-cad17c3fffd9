{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <form method="post" action='{url urltype="path" url_string="ben/P269/AssistanceRequests/editCashCategory/0/{$smarty.session.lang}/{$requestCategory->id}/updateCategory/{$smarty.session.s_AssistanceRequests_token}"}'>

        <div class="widget ">
            <div class="widget-header bg-blue">
                <i class="widget-icon"></i>
                <span class="widget-caption">{$requestCategory->name}</span>
                <div class="widget-buttons">
                    <a href="#" data-toggle="collapse">
                        <i class="fa fa-minus"></i>
                    </a>
                </div><!--Widget Buttons-->
            </div><!--Widget Header-->
            <div class="widget-body">

                <div class="row">
                    <div class="col-lg-12">
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_value#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="number" name="request_value" value="{$requestCategory->request_value / count($requestCategory->idsArray)}" class="form-control" placeholder="{#gnr_value#}" required="required"></div>

                        {if $assistance->class_id eq 1163}
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_materials#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <label>
                                    <input type="checkbox"
                                           onchange="toggleSelection(this)"
                                           id="selectAll"
                                    >
                                    <span class="text">{#gnr_select_all#}</span>
                                </label>
                                <hr>
                                <div class="control-group">
                                    {foreach $assistance->materialsArray as $key => $materialId}
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="materials[]"
                                                       value="{$materialId}" {if in_array($materialId, $requestCategory->materialsArray)} checked {/if}>
                                                <span class="text">{getname table=assist_materials id=$materialId}</span>
                                            </label>
                                        </div>
                                    {/foreach}
                                </div>
                            </div>
                        {/if}

                        {if $termOfSelection->type eq 1229}
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_beneficiaries#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <label>
                                    <input type="checkbox"
                                           onchange="toggleSelection(this)"
                                           id="selectAll"
                                    >
                                    <span class="text">{#gnr_select_all#}</span>
                                </label>
                                <hr>
                                <div class="control-group">
                                    {foreach $requestCategory->idsArray as $key => $userId}
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="beneficiariesAndFamiliesAds[]"
                                                       value="{$userId}" {if in_array($userId, $requestCategory->requestIdsArray)} checked {/if}>
                                                <span class="text">{getname table=sh_user id=$userId}</span>
                                            </label>
                                        </div>
                                    {/foreach}
                                </div>
                            </div>
                        {/if}

                        {if $termOfSelection->type eq 1230}
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_families#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <label>
                                    <input type="checkbox"
                                           onchange="toggleSelection(this)"
                                           id="selectAll"
                                    >
                                    <span class="text">{#gnr_select_all#}</span>
                                </label>
                                <hr>
                                <div class="control-group">
                                    {foreach $requestCategory->idsArray as $key => $familyId}
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="beneficiariesAndFamiliesAds[]"
                                                       value="{$familyId}" {if in_array($familyId, $requestCategory->requestIdsArray)} checked {/if}>
                                                <span class="text">{AssistanceFamilies::getFamilyName($familyId)}</span>
                                            </label>
                                        </div>
                                    {/foreach}
                                </div>
                            </div>
                        {/if}

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
                    </div>
                </div>
            </div><!--Widget Body-->
        </div>
    </form>

    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
        <thead>
        <tr>
            <th width="5%" style="background-color: #A0D468 !important;">&nbsp;</th>
            <th width="25%" style="background-color: #A0D468 !important;">{#gnr_name#}</th>
            <th width="15%" style="background-color: #A0D468 !important;">{#p_history_records#}</th>
            <th width="15%" style="background-color: #A0D468 !important;">{#gnr_neighborhood#}</th>
            <th width="10%" style="background-color: #A0D468 !important;">{#gnr_value#}</th>
            <th width="20%" style="background-color: #A0D468 !important;">{#gnr_comments#}</th>
            <th width="10%" style="background-color: #A0D468 !important;">{#gnr_settings#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $deliveries as $delivery}
            <tr>
                <td align="center">{$i++}</td>
                <td align="right">
                    {url urltype="button" check=0 oprvtype=3 opr_code="employees" url_string="gnr/X000/resume/edit/0/{$smarty.session.lang}/save_session/{$delivery->getDeliveryBeneficiaryId()}/ben/P269/AssistanceRequests/editCashCategory/0/{$smarty.session.lang}/{$delivery->category_id}"  text_value="<i class='fa fa-file-text'></i>&nbsp;{#gnr_data#}"}
                    &nbsp;
                    {$delivery->getDeliveryName()}
                </td>
                <td align="center">
                    {if AssistanceDelivery::checkSponserConditionsExist($delivery)}<span style="color: green"><i class="fa fa-circle"></i></span>{else}<span style="color: grey"><i class="fa fa-circle"></i></span>{/if}
                    {url check=0 urltype="mbutton" opr_code='AssistanceRequests' url_string="gnr/X000/mediacenter/browseBeneficiaryAssistanceHistory/0/{$smarty.session.lang}/{$delivery->term_of_selection_id}/{$delivery->getDeliveryBeneficiaryId()}" text_value="{#p_beneficiary_history#}"}
                </td>
                <td align="center">{getname table=st_neighborhood id=$delivery->neighborhood_id}</td>
                <td align="center">{$delivery->value}</td>
                <td align="center">{$delivery->delivery_comment}</td>
                <td nowrap align="center">
                    {url check=0 urltype="medit" opr_code='AssistanceRequests' url_string="ben/P269/AssistanceRequests/editDelivery/0/{$smarty.session.lang}/{$requestCategory->id}/{$delivery->id}"}
                    {url check=0 urltype="mdelete" opr_code='AssistanceRequests' url_string="ben/P269/AssistanceRequests/confirmDelivery/0/{$smarty.session.lang}/{$requestCategory->id}/{$delivery->id}"}
                </td>
            </tr>
        {/foreach}
        </tbody>
        <tfoot>
        <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td align="center">{$requestCategory->request_value}</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        </tfoot>
    </table>
{/block}
{block name="page_header"}
    <script>
        var checkboxes = document.getElementsByName('beneficiariesAndFamiliesAds[]')
        var selectAll = document.getElementById('selectAll')

        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].addEventListener('change', function () {
                if (!this.checked) {
                    selectAll.checked = false
                }
            })

        }


        function toggleSelection(source) {

            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = source.checked;
            }
        }
    </script>
{/block}
{block name=back}{url urltype=path url_string="ben/P269/AssistanceRequests/prepareRequest/0/{$smarty.session.lang}/{$assistanceRequest->id}"}{/block}