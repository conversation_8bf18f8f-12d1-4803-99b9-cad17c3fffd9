{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{$requestCategory->name}&nbsp;&raquo;&nbsp;{#gnr_add#}</h4>
    </div>
    <div class="modal-body">
        <form method="post" action='{url urltype="path" url_string="ben/P269/AssistanceRequests/editBeneficiaryAndFamilyMaterialDelivery/0/{$smarty.session.lang}/{$delivery->id}/insertMaterial/{$smarty.session.s_AssistanceRequests_token}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_materials#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $materialTypes as $materialType}
                                <b>{$materialType->name}</b><br>
                                {foreach $materialType->materials as $material}
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="material_id" value="{$material->assist_materials_id}" required>
                                            <span class="text">{$material->assist_materials_name}</span>
                                        </label>
                                    </div>
                                {/foreach}

                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_material_amount#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" pattern="[0-9]+([.][0-9]+)?" class="form-control" name="material_amount" placeholder="{#p_material_amount#}" required="required"></div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_unit#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="material_unit" placeholder="{#gnr_unit#}" required="required"></div>

                    <div class="col-lg-2 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp">{#gnr_add#}</button></div>
                </div>

            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
