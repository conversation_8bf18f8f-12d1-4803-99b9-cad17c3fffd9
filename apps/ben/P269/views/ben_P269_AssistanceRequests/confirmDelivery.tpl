{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_delete_exchange_request#}</h4>
    </div>
    <div class="modal-body">
        <div class="snsowraper danger text-center">
            {#gnr_delete_row_confirm#}<br><br>
            {$delivery->getDeliveryName()}
            &nbsp;&raquo;&nbsp;
            {$delivery->value}
            <br>
            {$delivery->delivery_comment}
            <br>
            <br>
            {url check=0 urltype="delete" opr_code='finexch' url_string="ben/P269/AssistanceRequests/editCashCategory/0/{$smarty.session.lang}/{$requestCategory->id}/deleteDelivery/{$smarty.session.s_AssistanceRequests_token}/{$delivery->id}"}
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}