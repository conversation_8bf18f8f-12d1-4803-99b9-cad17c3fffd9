{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <h5 class="row-title before-darkorange">
        <i class="fa fa-reply-all darkorange"></i>
        {$requestCategory->name}
        &nbsp;&raquo;&nbsp;
        {$delivery->getDeliveryName()}
    </h5>

    <div class="well">
        <form method="post"
              action='{url urltype="path" url_string="ben/P269/AssistanceRequests/editBeneficiaryAndFamilyMaterialDelivery/0/{$smarty.session.lang}/{$delivery->id}/updateDelivery/{$smarty.session.s_AssistanceRequests_token}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_comments#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control" name="delivery_comment" placeholder="{#gnr_comments#}">{$delivery->delivery_comment}</textarea></div>

                    <div class="col-lg-2 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
                </div>

            </div>
        </form>
    </div>

    <table class="table table-striped table-bordered">
        <thead>
        <tr>
            <th style="background-color: #A0D468 !important;"
                width="5%">{url check=0 urltype="madd" url_string="ben/P269/AssistanceRequests/addDeliveryMaterial/0/{$smarty.session.lang}/{$delivery->id}"}</th>
            <th style="background-color: #A0D468 !important;"
                width="55%">{#p_materials#}</th>
            <th style="background-color: #A0D468 !important;" width="20%">{#gnr_values#}
                /{#gnr_unit#}</th>
            <th style="background-color: #A0D468 !important;"
                width="20%">{#gnr_settings#}</th>
        </tr>
        </thead>
        {$i=1}
        <tbody>
        {foreach $delivery->materialsEntities as $material}
            <tr>
                <td align="center">{$i++}</td>
                <td>{getname table=assist_materials id=$material->assist_delivery_material_material_id}</td>
                <td class="text-center">{$material->assist_delivery_material_material_amount}
                    &nbsp;{$material->assist_delivery_material_material_unit}</td>
                <td class="text-center">
                    {url check=0 urltype="medit" url_string="ben/P269/AssistanceRequests/editDeliveryMaterial/0/{$smarty.session.lang}/{$delivery->id}/{$material->assist_delivery_material_id}"}
                    {url check=0 urltype="mdelete" url_string="ben/P269/AssistanceRequests/confirmDeliveryMaterial/0/{$smarty.session.lang}/{$delivery->id}/{$material->assist_delivery_material_id}"}
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
{/block}
{block name=back}{url urltype=path url_string="ben/P269/AssistanceRequests/editMaterialCategory/0/{$smarty.session.lang}/{$delivery->category_id}"}{/block}