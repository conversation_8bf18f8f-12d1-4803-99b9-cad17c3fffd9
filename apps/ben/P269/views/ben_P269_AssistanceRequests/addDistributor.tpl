{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add_row#}</h4>
    </div>
    <div class="modal-body">
    <div class="row">
        <form method="post" action='{url urltype="path" url_string="ben/P269/AssistanceRequests/prepareRequest/0/{$smarty.session.lang}/{$request->id}/addDistributor/{$smarty.session.s_AssistanceRequests_token}"}'>

            <div class="col-lg-12">

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_user#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="user_id" required="required">
                        <option value="" selected>{#gnr_select_from_menu#}</option>
                        {foreach $users as $user}
                            <option value="{$user->sh_user_id}">{$user->sh_user_fr_name}&nbsp;{$user->sh_user_secd_name}&nbsp;{$user->sh_user_thrd_name}&nbsp;{$user->sh_user_fm_name}</option>
                        {/foreach}
                    </select>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp">{#gnr_add#}</button></div>

            </div>

        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}