{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <form method="post"
          action='{url urltype="path" url_string="ben/P269/AssistanceRequests/editMaterialCategory/0/{$smarty.session.lang}/{$requestCategory->id}/updateCategory/{$smarty.session.s_AssistanceRequests_token}"}'>
        <div class="widget collapsed">
            <div class="widget-header bg-blue">
                <i class="widget-icon"></i>
                <span class="widget-caption">{$requestCategory->name}</span>
                <div class="widget-buttons">
                    <a href="#" data-toggle="collapse">
                        <i class="fa fa-minus"></i>
                    </a>
                </div><!--Widget Buttons-->
            </div><!--Widget Header-->
            <div class="widget-body" style="display: none;">

                <div class="row">
                    <div class="col-lg-12">

                        {if $termOfSelection->type eq 1229}
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_beneficiaries#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <div class="control-group">
                                    <label>
                                        <input type="checkbox"
                                               onchange="toggleSelection(this)"
                                               id="selectAll"
                                        >
                                        <span class="text">{#gnr_select_all#}</span>
                                    </label>
                                    <hr>
                                    {foreach $requestCategory->idsArray as $key => $userId}
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="beneficiariesAndFamiliesAds[]"
                                                       value="{$userId}" {if in_array($userId, $requestCategory->requestIdsArray)} checked {/if}>
                                                <span class="text">{getname table=sh_user id=$userId}</span>
                                            </label>
                                        </div>
                                    {/foreach}
                                </div>
                            </div>
                        {/if}

                        {if $termOfSelection->type eq 1230}
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_families#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <label>
                                    <input type="checkbox"
                                           onchange="toggleSelection(this)"
                                           id="selectAll"
                                    >
                                    <span class="text">{#gnr_select_all#}</span>
                                </label>
                                <hr>
                                <div class="control-group">
                                    {foreach $requestCategory->idsArray as $key => $familyId}
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="beneficiariesAndFamiliesAds[]"
                                                       value="{$familyId}" {if in_array($familyId, $requestCategory->requestIdsArray)} checked {/if}>
                                                <span class="text">{AssistanceFamilies::getFamilyName($familyId)}</span>
                                            </label>
                                        </div>
                                    {/foreach}
                                </div>
                            </div>
                        {/if}

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                        </div>
                    </div>
                </div>
            </div><!--Widget Body-->
        </div>
    </form>
    <div class="widget collapsed">
        <div class="widget-header bg-blue">
            <i class="widget-icon"></i>
            <span class="widget-caption">{#p_materials_settings#}</span>
        </div><!--Widget Header-->
        <div class="widget-body" style="display: block;">
            <table class="table table-striped table-bordered">
                <thead>
                <tr>
                    <th style="background-color: #A0D468 !important;"
                        width="5%">{url check=0 urltype="madd" url_string="ben/P269/AssistanceRequests/addMaterial/0/{$smarty.session.lang}/{$requestCategory->id}"}</th>
                    <th style="background-color: #A0D468 !important;"
                        width="55%">{#p_materials#}</th>
                    <th style="background-color: #A0D468 !important;" width="20%">{#gnr_values#}
                        /{#gnr_unit#}</th>
                    <th style="background-color: #A0D468 !important;"
                        width="20%">{#gnr_settings#}</th>
                </tr>
                </thead>
                {$i=1}
                <tbody>
                {foreach $requestCategory->materialsList as $material}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td>{$material->materialObject->materialType->assist_materials_name}
                            &nbsp;&raquo;&nbsp;{$material->materialObject->name}</td>
                        <td class="text-center">{$material->amount}&nbsp;{$material->unit}</td>
                        <td class="text-center">
                            {url check=0 urltype="medit" url_string="ben/P269/AssistanceRequests/editMaterial/0/{$smarty.session.lang}/{$requestCategory->id}/{$material->id}"}
                            {url check=0 urltype="mdelete" url_string="ben/P269/AssistanceRequests/confirmMaterial/0/{$smarty.session.lang}/{$requestCategory->id}/{$material->id}"}
                        </td>
                    </tr>
                {/foreach}
                </tbody>
            </table>

            <div class="horizontal-space"></div>

            {url check=0 urltype="mbutton" opr_code='AssistanceRequests' url_string="ben/P269/AssistanceRequests/updateBeneficiariesAndFamiliesMaterials/0/{$smarty.session.lang}/{$requestCategory->id}" text_value="{#p_update_families_materials#}"}
        </div><!--Widget Body-->
    </div>
    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
        <thead>
        <tr>
            <th width="5%" style="background-color: #A0D468 !important;">&nbsp;</th>
            <th width="25%" style="background-color: #A0D468 !important;">{#gnr_name#}</th>
            <th width="15%" style="background-color: #A0D468 !important;">{#p_history_records#}</th>
            <th width="15%" style="background-color: #A0D468 !important;">{#gnr_neighborhood#}</th>
            <th width="25%" style="background-color: #A0D468 !important;">{#gnr_comments#}</th>
            <th width="20%" style="background-color: #A0D468 !important;">{#gnr_comments#}</th>
            <th width="10%" style="background-color: #A0D468 !important;">{#gnr_settings#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $deliveries as $delivery}
            <tr>
                <td align="center">{$i++}</td>
                <td align="right">
                    {url urltype="button" check=0 oprvtype=3 opr_code="employees" url_string="gnr/X000/resume/edit/0/{$smarty.session.lang}/save_session/{$delivery->getDeliveryBeneficiaryId()}/ben/P269/AssistanceRequests/editMaterialCategory/0/{$smarty.session.lang}/{$delivery->category_id}"  text_value="<i class='fa fa-file-text'></i>&nbsp;{#gnr_data#}"}
                    &nbsp;
                    {$delivery->getDeliveryName()}
                </td>
                <td align="center">
                    {if AssistanceDelivery::checkSponserConditionsExist($delivery)}<span style="color: green"><i class="fa fa-circle"></i></span>{else}<span style="color: grey"><i class="fa fa-circle"></i></span>{/if}
                    {url check=0 urltype="mbutton" opr_code='AssistanceRequests' url_string="gnr/X000/mediacenter/browseBeneficiaryAssistanceHistory/0/{$smarty.session.lang}/{$delivery->term_of_selection_id}/{$delivery->getDeliveryBeneficiaryId()}" text_value="{#p_beneficiary_history#}"}
                </td>
                <td align="center">{getname table=st_neighborhood id=$delivery->neighborhood_id}</td>
                <td align="center">{$delivery->delivery_comment}</td>
                <td align="center">
                    {$x=1}
                    {foreach $delivery->materialsEntities as $material}
                        {$x++}&nbsp;-&nbsp;{getname table=assist_materials id=$material->assist_delivery_material_material_id}
                        <br>
                    {/foreach}
                </td>
                <td nowrap align="center">
                    {url check=0 urltype="button" opr_code='AssistanceRequests' url_string="ben/P269/AssistanceRequests/editBeneficiaryAndFamilyMaterialDelivery/0/{$smarty.session.lang}/{$delivery->id}" text_value="{#p_edit_material_delivery#}"}
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
{/block}
{block name="page_header"}
    <script>
        var checkboxes = document.getElementsByName('beneficiariesAndFamiliesAds[]')
        var selectAll = document.getElementById('selectAll')

        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].addEventListener('change', function () {
                if (!this.checked) {
                    selectAll.checked = false
                }
            })

        }


        function toggleSelection(source) {

            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = source.checked;
            }
        }
    </script>
{/block}
{block name=back}{url urltype=path url_string="ben/P269/AssistanceRequests/prepareRequest/0/{$smarty.session.lang}/{$assistanceRequest->id}"}{/block}