{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}

    {if $assistanceYear}

        <div class="widget collapsed">
            <div class="widget-header bg-blue">
                <i class="widget-icon"></i>
                <span class="widget-caption">
                {$assistanceYear->name}
                    &nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
                    &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
                    &nbsp;&raquo;&nbsp;
                    {$assistance->name}
            </span>
                <div class="widget-buttons">
                    <a href="#" data-toggle="collapse">
                        <i class="fa fa-minus"></i>
                    </a>
                </div><!--Widget Buttons-->
            </div><!--Widget Header-->
            <div class="widget-body" style="display: none;">
                <div class="row">

                    <div class="col-lg-6">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$assistance->name}</div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$assistance->class_id}
                                    &nbsp;&raquo;&nbsp;{getname table=assist_type id=$assistance->type_id}</div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_activation_status#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{t v=$assistance->activation_status}</div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_term_of_selection#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$termOfSelection->name}</div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$termOfSelection->type}</div>

                                {if $assistance->term_of_selection_type eq 1229}
                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_category#}</div>
                                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                        {foreach $termOfSelection->userClassesIds as $key => $class}
                                            &nbsp;-&nbsp;{getname table=sh_userclasses id=$class}
                                        {/foreach}
                                    </div>
                                {/if}
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="row">
                            <div class="col-lg-12">

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_criteria#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                    <table class="table table-striped table-bordered">
                                        <thead>
                                        <tr>
                                            <th style="background-color: #A0D468 !important;" width="5%"></th>
                                            <th style="background-color: #A0D468 !important;"
                                                width="25%">{#gnr_criterion#}</th>
                                            <th style="background-color: #A0D468 !important;"
                                                width="70%">{#gnr_values#}</th>
                                        </tr>
                                        </thead>
                                        {$i=1}
                                        <tbody>
                                        {foreach $termOfSelection->getCriteriaList() as $criterion}
                                            <tr>
                                                <td align="center">{$i++}</td>
                                                <td>{t v=$criterion->type_id}</td>
                                                <td class="text-center">{$criterion->browseTermCriterionDetails()}</td>
                                            </tr>
                                        {/foreach}
                                        </tbody>
                                    </table>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div><!--Widget Body-->
        </div>
        <h5 class="row-title before-darkorange"><i class="fa fa-reply-all darkorange"></i>ضبط الفئات</h5>
        <div class="horizontal-space"></div>
        {if $assistance->class_id eq AssistType::SETTING_ASSISTANCE_TYPE_CASH}
            <table class="table table-striped table-bordered">
                <thead>
                <tr>
                    <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                    <th style="background-color: #A0D468 !important;" width="20%">{#p_category#}</th>
                    <th style="background-color: #A0D468 !important;" width="15%">{#gnr_number#}</th>
                    {*<th style="background-color: #A0D468 !important;" width="15%">{#gnr_number#}</th>*}
                    <th style="background-color: #A0D468 !important;" width="15%">{#gnr_value#}</th>
                    <th style="background-color: #A0D468 !important;" width="25%">{#gnr_settings#}</th>
                </tr>
                </thead>
                {$i=1}
                <tbody>
                {foreach $request->requestCategories as $category}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td align="center">{$category->name}</td>
                        <td align="center">
                            {count(array_filter($category->idsArray))}
                            &nbsp;
                            {url check=0 urltype="mbutton" url_string="gnr/X000/mediacenter/browseBeneficiaries/0/{$smarty.session.lang}/category/{$category->category_id}" text_value="{#gnr_view#}"}
                        </td>
                        {*<td align="center">*}
                            {*{count(array_filter($category->requestIdsArray))}*}
                            {*&nbsp;*}
                            {*{url check=0 urltype="mbutton" url_string="gnr/X000/mediacenter/browseBeneficiaries/0/{$smarty.session.lang}/requestCategory/{$category->id}" text_value="{#gnr_view#}"}*}
                        {*</td>*}
                        <td align="center">{$category->request_value}</td>
                        <td align="center">
                            {url check=0 urltype="button" url_string="ben/P269/AssistanceRequests/editCashCategory/0/{$smarty.session.lang}/{$category->id}" text_value="{#p_assistance_record_setting#}"}
                        </td>
                    </tr>
                {/foreach}
                </tbody>

                <tfoot>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td align="center">{$request->value}</td>
                    <td>&nbsp;</td>
                </tr>
                </tfoot>

            </table>
        {/if}

        {if $assistance->class_id eq AssistType::SETTING_ASSISTANCE_TYPE_MATERIAL}
            <table class="table table-striped table-bordered">
                <thead>
                <tr>
                    <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                    <th style="background-color: #A0D468 !important;" width="20%">{#p_category#}</th>
                    <th style="background-color: #A0D468 !important;" width="15%">{#gnr_number#}</th>
                    <th style="background-color: #A0D468 !important;" width="15%">{#gnr_number#}</th>
                    <th style="background-color: #A0D468 !important;" width="25%">{#gnr_settings#}</th>
                </tr>
                </thead>
                {$i=1}
                <tbody>
                {foreach $request->requestCategories as $category}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td align="center">{$category->name}</td>
                        <td align="center">
                            {count(array_filter($category->idsArray))}
                            &nbsp;
                            {url check=0 urltype="mbutton" url_string="gnr/X000/mediacenter/browseBeneficiaries/0/{$smarty.session.lang}/category/{$category->category_id}" text_value="{#gnr_view#}"}
                        </td>
                        <td align="center">
                            {count(array_filter($category->requestIdsArray))}
                            &nbsp;
                            {url check=0 urltype="mbutton" url_string="gnr/X000/mediacenter/browseBeneficiaries/0/{$smarty.session.lang}/requestCategory/{$category->id}" text_value="{#gnr_view#}"}
                        </td>
                        <td align="center">
                            {url check=0 urltype="button" url_string="ben/P269/AssistanceRequests/editMaterialCategory/0/{$smarty.session.lang}/{$category->id}" text_value="{#p_assistance_record_setting#}"}
                        </td>
                    </tr>
                {/foreach}
                </tbody>

                <tfoot>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    {if $assistance->class_id neq 1163}
                        <td align="center">{$request->value}</td>
                    {/if}
                    <td>&nbsp;</td>
                </tr>
                </tfoot>

            </table>
        {/if}
        <hr>
        {url check=0 urltype="button" url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/AssistanceRequests/assist_request/{$request->id}/{$smarty.session.user->id}/ben/P269/AssistanceRequests/prepareRequest/0/{$smarty.session.lang}/{$request->id}" text_value="{#gnr_attachments#}"}

        {showdocslist opr_code=AssistanceRequests row_id=$request->id}

    {else}
        <span style="color: red">{#p_you_should_activate_assistance_year_first#}</span>
    {/if}

{/block}
{block name=back}{url urltype=path url_string="ben/P269/AssistanceRequests/show/0/{$smarty.session.lang}"}{/block}