{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <div class="widget collapsed">
        <div class="widget-header bg-blue">
            <i class="widget-icon"></i>
            <span class="widget-caption">
                {$assistanceYear->name}
                &nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
                &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
                &nbsp;&raquo;&nbsp;
                {$assistance->name}
            </span>
        </div><!--Widget Header-->
        <div class="widget-body" style="display: block;">
            <div class="row">

                <div class="col-lg-6">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$assistance->name}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$assistance->class_id}
                                &nbsp;&raquo;&nbsp;{getname table=assist_type id=$assistance->type_id}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_activation_status#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{t v=$assistance->activation_status}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_term_of_selection#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$termOfSelection->name}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$termOfSelection->type}</div>

                            {if $assistance->term_of_selection_type eq 1229}
                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_category#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                    {foreach $termOfSelection->userClassesIds as $key => $class}
                                        &nbsp;-&nbsp;{getname table=sh_userclasses id=$class}
                                    {/foreach}
                                </div>
                            {/if}
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="row">
                        <div class="col-lg-12">

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_criteria#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <tr>
                                        <th style="background-color: #A0D468 !important;" width="5%"></th>
                                        <th style="background-color: #A0D468 !important;"
                                            width="25%">{#gnr_criterion#}</th>
                                        <th style="background-color: #A0D468 !important;"
                                            width="70%">{#gnr_values#}</th>
                                    </tr>
                                    </thead>
                                    {$i=1}
                                    <tbody>
                                    {foreach $termOfSelection->getCriteriaList() as $criterion}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td>{t v=$criterion->type_id}</td>
                                            <td class="text-center">{$criterion->browseTermCriterionDetails()}</td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div><!--Widget Body-->
    </div>

    <h5 class="row-title before-darkorange"><i class="fa fa-reply-all darkorange"></i>الفئات</h5>

    <div class="horizontal-space"></div>

    <table class="table table-striped table-bordered">
        <thead>
            <tr>
                <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                <th style="background-color: #A0D468 !important;" width="25%">{#p_category#}</th>
                <th style="background-color: #A0D468 !important;" width="20%">{#gnr_number#}</th>
                <th style="background-color: #A0D468 !important;" width="15%">{#gnr_value#}</th>

                {if $termOfSelection->type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY}
                    <th style="background-color: #A0D468 !important;" width="20%">{#p_materials#}</th>
                {/if}

                {if $termOfSelection->type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_INDIVIDUAL}
                    <th style="background-color: #A0D468 !important;" width="20%">{#p_beneficiaries#}</th>
                {/if}

                {if $termOfSelection->type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY}
                    <th style="background-color: #A0D468 !important;" width="20%">{#p_families#}</th>
                {/if}
            </tr>
        </thead>
        {$i=1}
        <tbody>
        {foreach $request->requestCategories as $category}
            <tr>
                <td align="center">{$i++}</td>
                <td align="center">{$category->name}</td>
                <td align="center">{count($category->idsArray)}</td>
                <td align="center">{count($category->requestIdsArray)}</td>

                {if $termOfSelection->type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY}
                    <td align="center">{$category->request_value}</td>
                {/if}

                {if $termOfSelection->type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_INDIVIDUAL}
                    <td>
                        {$i=1}
                        {foreach $category->requestIdsArray as $userId}
                            {$i++}&nbsp;-&nbsp;{getname table=sh_user id=$userId}<br>
                        {/foreach}
                    </td>
                {/if}

                {if $termOfSelection->type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY}
                    <td>
                        {foreach $category->requestIdsArray as $familyId}
                            {getname table=assist_families id=$familyId}<br>
                        {/foreach}
                    </td>
                {/if}
            </tr>
        {/foreach}
        </tbody>
    </table>

    <div class="horizontal-space"></div>

    <h5 class="row-title before-darkorange"><i class="fa fa-reply-all darkorange"></i>المسلِّمون</h5>

    <table class="table table-striped table-bordered">
        <thead>
        <tr>
            <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
            <th style="background-color: #A0D468 !important;" width="80%">{#gnr_name#}</th>
        </tr>
        </thead>
        {$i=1}
        <tbody>
        {foreach $request->distributors as $distributor}
            <tr>
                <td align="center">{$i++}</td>
                <td align="center">{$distributor->userObject->full_name}</td>
            </tr>
        {/foreach}
        </tbody>
    </table>

    <hr>

    {showdocslist opr_code=AssistanceRequests row_id=$request->id}
{/block}
{block name=back}{url urltype=path url_string="ben/P269/AssistanceRequests/show/0/{$smarty.session.lang}"}{/block}