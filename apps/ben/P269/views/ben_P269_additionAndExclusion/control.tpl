{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}
    
    <h5 class="row-title before-darkorange"><i class="fa fa-user darkorange"></i>{$sponsorship->full_name}</h5>

    <div class="row">
        <div class="col-md-12">
            <table class="table table-striped table-bordered table-hover no-footer">
                <thead>
                    <tr>
                        <th class="text-right" width="50%">{#p_individuals#}</th>
                        <th class="text-right" width="50%">{#p_families#}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-right">
                            {$i=1}
                            {foreach $individuals as $individual}
                                {$i++}&nbsp;-&nbsp;{$individual->full_name}                                
                            {/foreach}
                        </td>
                        <td class="text-right">
                            {$i=1}
                            {foreach $families as $family}
                                {$i++}&nbsp;-&nbsp;{AssistanceFamilies::getFamilyName($family->id)}                               
                            {/foreach}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="horizontal-space"></div>

    <div class="tabbable">
        <ul class="nav nav-tabs" id="myTab">
            <li class="{if $smarty.session.s_addition_and_exclusion_tab eq 'individual'} active {/if}">
                <a data-toggle="tab" href="#individual">{#p_individuals#}</a>
            </li>

            <li class="{if $smarty.session.s_addition_and_exclusion_tab eq 'family'} active {/if} tab-red">
                <a data-toggle="tab" href="#family">{#p_families#}</a>
            </li>
        </ul>

        <div class="tab-content">
            <div id="individual" class="tab-pane {if $smarty.session.s_addition_and_exclusion_tab eq 'individual'} in active {/if}">
                <div class="well" style="margin-bottom: 0">
                    <div class="row">
                        <div class="col-lg-6"><h4><i class="typcn typcn-th-menu blueberry"></i>&nbsp;سجلات الاضافة</h4></div>
                        <div class="col-lg-1"></div>
                        <div class="col-lg-5" style="text-align: left">
                            {url check=0 urltype="mbutton" url_string="ben/P269/additionAndExclusion/add/0/{$smarty.session.lang}/{$sponsorship->id}/{TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_INDIVIDUAL}" text_value="{#gnr_add#}"}
                        </div>
                    </div>
                </div>
                <table class="table table-striped table-bordered table-hover no-footer">
                    <thead>
                    <tr>
                        <th class="text-center" width="40%">{#p_user_name#}</th>
                        <th class="text-center" width="20%">{#p_addition_date#}</th>
                        <th class="text-center" width="20%">{#p_exclusion_date#}</th>
                        <th class="text-center" width="20%">{#gnr_setting#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $individual_additions as $addition}
                        <tr>
                            <td class="text-right">
                                {url check=0 urltype="mbutton" url_string="gnr/X000/mediacenter/browseSponsershipBeneficiaryDeliveryRecords/0/{$smarty.session.lang}/{$addition->id}" text_value="<i class='fa fa-ellipsis-v'></i>&nbsp;{$addition->deliveryRecordsNumber}&nbsp;"}
                                &nbsp;
                                {getname table=sh_user id=$addition->beneficiary_id}
                            </td>
                            <td class="text-center">{getdate type=show table=assist_addition_exclusion col=created_date row=$addition}</td>
                            <td class="text-center">{if $addition->exclusionObject}{getdate type=show table=assist_addition_exclusion col=date row=$addition->exclusionObject}{/if}</td>
                            <td class="text-center">
                                {if !$addition->exclusionObject}
                                    {url check=0 urltype="mbutton" url_string="ben/P269/additionAndExclusion/exclusion/0/{$smarty.session.lang}/{$sponsorship->id}/{$addition->id}" text_value="{#p_exclusion#}"}
                                {/if}
                                {url check=0 urltype="medit" url_string="ben/P269/additionAndExclusion/edit/0/{$smarty.session.lang}/{$sponsorship->id}/{$addition->id}"}
                                {if !$addition->exclusionObject && $addition->deliveryRecordsNumber eq 0}
                                    {url check=0 urltype="mdelete" url_string="ben/P269/additionAndExclusion/confirm/0/{$smarty.session.lang}/{$sponsorship->id}/{$addition->id}"}
                                {/if}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
                <div class="horizontal-space"></div>
                <div class="well" style="margin-bottom: 0">
                    <div class="row">
                        <div class="col-lg-6"><h4><i class="typcn typcn-th-menu blueberry"></i>&nbsp;سجلات الاستبعاد</h4></div>
                        <div class="col-lg-1"></div>
                    </div>
                </div>
                <table class="table table-striped table-bordered table-hover no-footer">
                    <thead>
                    <tr>
                        <th class="text-center" width="30%">{#p_user_name#}</th>
                        <th class="text-center" width="10%">{#p_exclusion_date#}</th>
                        <th class="text-center" width="50%">{#gnr_comments#}</th>
                        <th class="text-center" width="10%">{#gnr_setting#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $individual_exclusions as $exclusion}
                        <tr>
                            <td class="text-center">{getname table=sh_user id=$exclusion->beneficiary_id}</td>
                            <td class="text-center">{getdate type=show table=assist_addition_exclusion col=date row=$exclusion}</td>
                            <td class="text-center">{$exclusion->comment}</td>
                            <td class="text-center">
                                {url check=0 urltype="medit" url_string="ben/P269/additionAndExclusion/edit/0/{$smarty.session.lang}/{$sponsorship->id}/{$exclusion->id}"}
                                {url check=0 urltype="mdelete" url_string="ben/P269/additionAndExclusion/confirm/0/{$smarty.session.lang}/{$sponsorship->id}/{$exclusion->id}"}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>

            <div id="family" class="tab-pane {if $smarty.session.s_addition_and_exclusion_tab eq 'family'} in active {/if}">
                <div class="well" style="margin-bottom: 0">
                    <div class="row">
                        <div class="col-lg-6"><h4><i class="typcn typcn-th-menu blueberry"></i>&nbsp;سجلات الاضافة</h4></div>
                        <div class="col-lg-1"></div>
                        <div class="col-lg-5" style="text-align: left">
                            {url check=0 urltype="button" url_string="ben/P269/additionAndExclusion/familyControl/0/{$smarty.session.lang}/{$sponsorship->id}/menu" text_value="{#gnr_add#}"}
                        </div>
                    </div>
                </div>
                <table class="table table-striped table-bordered table-hover no-footer">
                    <thead>
                    <tr>
                        <th class="text-center" width="40%">{#p_family#}</th>
                        <th class="text-center" width="20%">{#p_addition_date#}</th>
                        <th class="text-center" width="20%">{#p_exclusion_date#}</th>
                        <th class="text-center" width="20%">{#gnr_setting#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $family_additions as $addition}
                        <tr>
                            <td class="text-right">
                                {url check=0 urltype="mbutton" url_string="gnr/X000/mediacenter/browseSponsershipBeneficiaryDeliveryRecords/0/{$smarty.session.lang}/{$addition->id}" text_value="<i class='fa fa-ellipsis-v'></i>&nbsp;{$addition->deliveryRecordsNumber}&nbsp;"}
                                &nbsp;
                                {AssistanceFamilies::getFamilyName($addition->beneficiary_id)}
                            </td>
                            <td class="text-center">{getdate type=show table=assist_addition_exclusion col=date row=$addition}</td>
                            <td class="text-center">{if $addition->exclusionObject}{getdate type=show table=assist_addition_exclusion col=date row=$addition->exclusionObject}{/if}</td>
                            <td class="text-center">
                                {if $addition->exclusionObject}
                                    {url check=0 urltype="mbutton" url_string="ben/P269/additionAndExclusion/exclusion/0/{$smarty.session.lang}/{$sponsorship->id}/{$addition->id}" text_value="{#p_exclusion#}"}
                                {/if}
                                {url check=0 urltype="medit" url_string="ben/P269/additionAndExclusion/edit/0/{$smarty.session.lang}/{$sponsorship->id}/{$addition->id}"}
                                {if !$addition->exclusionObject && $addition->deliveryRecordsNumber eq 0}
                                    {url check=0 urltype="mdelete" url_string="ben/P269/additionAndExclusion/confirm/0/{$smarty.session.lang}/{$sponsorship->id}/{$addition->id}"}
                                {/if}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
                <div class="horizontal-space"></div>
                <div class="well" style="margin-bottom: 0">
                    <div class="row">
                        <div class="col-lg-6"><h4><i class="typcn typcn-th-menu blueberry"></i>&nbsp;سجلات الاستبعاد</h4></div>
                        <div class="col-lg-1"></div>
                    </div>
                </div>
                <table class="table table-striped table-bordered table-hover no-footer">
                    <thead>
                    <tr>
                        <th class="text-center" width="30%">{#p_user_name#}</th>
                        <th class="text-center" width="10%">{#p_exclusion_date#}</th>
                        <th class="text-center" width="50%">{#gnr_comments#}</th>
                        <th class="text-center" width="10%">{#gnr_setting#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $family_exclusions as $exclusion}
                        <tr>
                            <td class="text-center">{AssistanceFamilies::getFamilyName($exclusion->beneficiary_id)}</td>
                            <td class="text-center">{getdate type=show table=assist_addition_exclusion col=date row=$exclusion}</td>
                            <td class="text-center">{$exclusion->comment}</td>
                            <td class="text-center">
                                {url check=0 urltype="medit" url_string="ben/P269/additionAndExclusion/edit/0/{$smarty.session.lang}/{$sponsorship->id}/{$exclusion->id}"}
                                {url check=0 urltype="mdelete" url_string="ben/P269/additionAndExclusion/confirm/0/{$smarty.session.lang}/{$sponsorship->id}/{$exclusion->id}"}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
    </div>    
{/block}
{block name=back}{url urltype="path" url_string="ben/P269/additionAndExclusion/show/0/{$smarty.session.lang}"}{/block}