{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add#}</h4>
    </div>
    <div class="modal-body">
        <form method="post"
              action='{url urltype="path" url_string="ben/P269/additionAndExclusion/control/0/{$smarty.session.lang}/{$sponsorship->id}/exclusion/{$addition->id}/{$smarty.session.s_additionAndExclusion_token}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_exclusion_date#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {getdate table=assist_addition_exclusion col=date type=add}
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_exclusion_comment#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <textarea name="comment" class="form-control" id="" cols="30" rows="5"></textarea>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                    </div>

                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
