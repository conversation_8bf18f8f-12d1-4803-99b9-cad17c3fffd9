{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add#}</h4>
    </div>
    <div class="modal-body">
        <form method="post" action='{url urltype="path" url_string="ben/P269/additionAndExclusion/control/0/{$smarty.session.lang}/{$sponsorship->id}/update/{$smarty.session.s_additionAndExclusion_token}/{$row->id}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{t v=$row->type}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_date#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=assist_addition_exclusion col=date type=edit row=$row}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_comments#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><textarea name="comment" class="form-control" cols="30" rows="5">{$row->comment}</textarea></div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>

                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
