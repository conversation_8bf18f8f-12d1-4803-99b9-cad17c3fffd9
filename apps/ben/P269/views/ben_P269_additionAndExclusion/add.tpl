{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add#}</h4>
    </div>
    <div class="modal-body">
        <form method="post" action='{url urltype="path" url_string="ben/P269/additionAndExclusion/control/0/{$smarty.session.lang}/{$sponsorship->id}/insert/{$smarty.session.s_additionAndExclusion_token}/{$type}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">

                    {if $type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_INDIVIDUAL}
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_beneficiary#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <select name="beneficiary_id">
                                <option value=""></option>
                                {foreach $individuals as $individual}
                                    <option value="{$individual->id}">{$individual->full_name}</option>
                                {/foreach}
                            </select>
                        </div>
                    {/if}

                    {if $type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY}
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <select name="beneficiary_id">
                                <option value=""></option>
                                {foreach $families as $family}
                                    <option value="{$family->id}">{AssistanceFamilies::getFamilyName($family->id)}</option>
                                {/foreach}
                            </select>
                        </div>
                    {/if}

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_date#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=assist_addition_exclusion col=date type=add}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_comments#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><textarea name="comment" class="form-control" cols="30" rows="5"></textarea></div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp">{#gnr_add#}</button></div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
