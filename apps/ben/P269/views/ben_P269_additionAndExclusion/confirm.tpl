{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_exclusion_delet#}</h4>
    </div>
    <div class="modal-body">
        <div class="row snsowraper">
            {if $row->beneficiary_type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_INDIVIDUAL}
                {if $row->type eq AssistanceAdditionExclusion::ADDITION}
                    {#gnr_delete_row_confirm#}
                    <br><br>
                    [&nbsp;{$row->id}&nbsp;] &nbsp;&raquo;&nbsp;{getname table=sh_user id=$row->beneficiary_id}
                    <br>
                    {url urltype=delete url_string="ben/P269/additionAndExclusion/control/0/{$smarty.session.lang}/{$sponsorship->id}/delete/{$smarty.session.s_additionAndExclusion_token}/{$row->id}"}
                {/if}

                {if $row->type eq AssistanceAdditionExclusion::EXCLUSION}
                    {if $addition->user_id eq $user->belongs_to_user or $user->belongs_to_user eq 0 or $user->belongs_to_user eq null}
                        {#gnr_delete_row_confirm#}
                        <br><br>
                        [&nbsp;{$row->id}&nbsp;] &nbsp;&raquo;&nbsp;{getname table=sh_user id=$row->beneficiary_id}
                        <br>
                        {url urltype=delete url_string="ben/P269/additionAndExclusion/control/0/{$smarty.session.lang}/{$sponsorship->id}/delete/{$smarty.session.s_additionAndExclusion_token}/{$row->id}"}
                    {else}
                        <span>{#p_sorry_you_cant_able_to_delete_this_records#}</span>
                    {/if}                
                {/if}
            {/if}
            {if $row->beneficiary_type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY}
                {if $row->type eq AssistanceAdditionExclusion::ADDITION}
                    {#gnr_delete_row_confirm#}
                    <br><br>
                    [&nbsp;{$row->id}&nbsp;] &nbsp;&raquo;&nbsp;{AssistanceFamilies::getFamilyName($row->beneficiary_id)}
                    <br>
                    {url urltype=delete url_string="ben/P269/additionAndExclusion/control/0/{$smarty.session.lang}/{$sponsorship->id}/delete/{$smarty.session.s_additionAndExclusion_token}/{$row->id}"}
                {/if}

                {if $row->type eq AssistanceAdditionExclusion::EXCLUSION}
                    {if $addition->beneficiary_id eq $family->belongs_to_user or $family->belongs_to_user eq 0 or $family->belongs_to_user eq null}
                        {#gnr_delete_row_confirm#}
                        <br><br>
                        [&nbsp;{$row->id}&nbsp;] &nbsp;&raquo;&nbsp;{AssistanceFamilies::getFamilyName($row->beneficiary_id)}
                        <br>
                        {url urltype=delete url_string="ben/P269/additionAndExclusion/control/0/{$smarty.session.lang}/{$sponsorship->id}/delete/{$smarty.session.s_additionAndExclusion_token}/{$row->id}"}
                    {else}
                        <span>{#p_sorry_you_cant_able_to_delete_this_records#}</span>
                    {/if}                
                {/if}
            {/if}            
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}