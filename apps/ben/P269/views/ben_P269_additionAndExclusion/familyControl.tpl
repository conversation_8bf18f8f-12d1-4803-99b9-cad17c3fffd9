{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}

    <h5 class="row-title before-darkorange"><i class="fa fa-user darkorange"></i>{$sponsorship->full_name}</h5>

    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <form method="post" action='{url urltype="path" url_string="ben/P269/additionAndExclusion/familyControl/0/{$smarty.session.lang}/{$sponsorship->id}/save_session"}'>
                <select name="family_id" style="width:100%;" required onchange="this.form.submit()">
                    <option value="">{#gnr_unspecified#}</option>
                    {foreach $families as $familyRow}
                        <option value="{$familyRow->id}" {if $familyRow->id eq $family->id} selected {/if}>{AssistanceFamilies::getFamilyName($familyRow->id)}</option>
                    {/foreach}
                </select>
            </form>
        </div>
    </div>

    <div class="horizontal-space"></div>
    
    {if $family}

        {if empty($family->belongs_to_user)}
            {url check=0 urltype="button" url_string="ben/P269/additionAndExclusion/familyControl/0/{$smarty.session.lang}/{$sponsorship->id}/updateFamily/{$smarty.session.s_additionAndExclusion_token}/{$family->id}" text_value="{#p_add_family_to_sponsorship#}"}
        {/if}

        <div class="horizontal-space"></div>

        <table class="table table-striped table-bordered">
            <thead>
            <tr style="background-color: #A0D468 !important;">
                <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                <th style="background-color: #A0D468 !important;" width="35%">{#gnr_name#}</th>
                <th style="background-color: #A0D468 !important;" width="30%">{#p_belong_to#}</th>
                <th style="background-color: #A0D468 !important;" width="30%">{#gnr_status#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $members as $member}
                <tr>
                    <td align="center">{$i++}</td>
                    <td nowrap="nowrap">{$member->userObject->full_name}</td>
                    <td nowrap="nowrap">{getname table=sh_user id=$member->userObject->belongs_to_user}</td>
                    <td>{t v=$member->userObject->live_status}</td>
                </tr>
            {/foreach}
            </tbody>
        </table>
    {else}
        <span style="color: red">{#p_please_select_a_family_from_above_list#}</span>
    {/if}
{/block}
{block name=back}{url urltype="path" url_string="ben/P269/additionAndExclusion/control/0/{$smarty.session.lang}/{$sponsorship->id}/setTab/family"}{/block}
