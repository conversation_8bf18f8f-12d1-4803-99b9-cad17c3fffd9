{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=title}{#gnr_user#}{/block}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}

    <div class="margin-bottom-20">
            {url urltype="mbutton" check=0 url_string="gnr/X000/mediacenter/createNewBeneficiary/0/{$smarty.session.lang}/formUrl/ben/P269/additionAndExclusion/show/0/ar/createNewBeneficiary/{$smarty.session.s_additionAndExclusion_token}" text_value="{#p_add_new_beneficiary#}" style="btn btn-default shiny"}
    </div>

    <div class="row">

        <!-- Project select menu -->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <form method="post"
                  action='{url urltype="path" url_string="ben/P269/additionAndExclusion/show/0/{$smarty.session.lang}/changeClassification"}'>
                <select name="classificationType" style="width:100%;" required onchange="this.form.submit()">
                    <option value="">{#gnr_unspecified#}</option>
                    {foreach $classifications as $class}
                        <option value="{$class->id}" {if $class->id eq $smarty.session.s_users_class_id} selected {/if}>{$class->name}</option>
                    {/foreach}
                </select>
            </form>
        </div>
    </div>
    {if is_numeric($smarty.session.s_users_class_id)}
        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
            <thead>
            <tr style="background-color: #A0D468 !important;">
                <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                <th style="background-color: #A0D468 !important;" width="80%">{#gnr_name#}</th>
                <th style="background-color: #A0D468 !important;" width="15%">{#p_control_panel#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $users as $user}
                <tr>
                    <td align="center">{$i++}</td>
                    <td nowrap="nowrap">&nbsp;
                        &nbsp;
                        {$user->full_name}
                    </td>
                    <td align="center">
                        {url urltype="button" check=0 url_string="ben/P269/additionAndExclusion/control/0/{$smarty.session.lang}/{$user->id}/setTab/individual"  text_value="<i class='fa fa-file-text'></i>&nbsp;{#gnr_data#}"}
                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>
    {/if}

{/block}
{block name=page_header}
    <script type="text/javascript" src="/templates/assets/js/loader.js"></script>
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
        function exportTo(ID, type) {
            $('#table' + ID).css('display', '').tableExport({type: type, escape: 'false'});
            $('#table' + ID).css('display', 'none');
        }
        {/literal}
    </script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 25,
                        "oTableTools": {
                            "aButtons": [],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                    $("tfoot input").keyup(function () {
                        /* Filter on the column (the index) of this element */
                        oTable.fnFilter(this.value, $("tfoot input").index(this));
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
    <script>
        (function () {
            window.events.$on('beneficiaryCreated', function () {
                window.location.reload()
            })
        })()
    </script>
{/block}
