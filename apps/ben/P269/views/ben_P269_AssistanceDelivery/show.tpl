{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}

    {if $assistanceYear}
        <h5 class="row-title before-blue">
            <i class="glyphicon glyphicon-list-alt blue"></i>
            {$assistanceYear->name}&nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
            &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
            &nbsp;|&nbsp;
            <i class="glyphicon glyphicon-user blue"></i>
            {$currentUser->full_name}
        </h5>
        <div class="widget collapsed">
            <div class="widget-header bg-blue">
                <i class="widget-icon fa fa-arrow-left"></i>
                <span class="widget-caption">{#gnr_search#}</span>
                <div class="widget-buttons">
                    <a href="#" data-toggle="collapse">
                        <i class="fa fa-minus"></i>
                    </a>
                </div><!--Widget Buttons-->
            </div><!--Widget Header-->
            <div class="widget-body">

                <form action="{url urltype=path url_string="ben/P269/AssistanceDelivery/show/0/{$smarty.session.lang}/search"}"
                      method="post">
                    <div class="row">
                        <div class="col-lg-6">

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_term_of_selection#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                <div class="control-group">
                                    {foreach $termsType as $term}
                                        <div class="radio">
                                            <label>
                                                <input type="radio" name="type" value="{$term->id}"
                                                       id="{$term->id}" {if $term->id eq {$smarty.session.search_deliveries['type']}} checked {/if}>
                                                <span class="text">{$term->translatedName}</span>
                                            </label>
                                        </div>
                                    {/foreach}
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                <select name="assistanceId">
                                    <option value="">{#gnr_unspecified#}</option>
                                    {foreach $assistancesTypes as $assistance}
                                        <option value="{$assistance->id}"
                                                {if $assistance->id eq $smarty.session.search_deliveries['assistanceId']}selected{/if}>{$assistance->name}</option>
                                    {/foreach}
                                </select>
                            </div>

                        </div>

                        <div class="col-lg-2">
                            <div class="checkbox">
                                {foreach $categories as $category}
                                    <label>
                                        <input type="checkbox" name="category[]"
                                               value="{$category->id}" {if in_array($category->id,$smarty.session.search_deliveries['category'])} checked {/if}>
                                        <span class="text">{$category->translatedName}</span>
                                    </label>
                                    <br>
                                {/foreach}
                            </div>
                        </div>

                        <div class="col-lg-2">
                            <div class="control-group">
                                {foreach $statusList as $status}
                                    <label>
                                        <input type="radio" name="status"
                                               value="{$status->id}" {if $status->id eq $smarty.session.search_deliveries['status']} checked {/if}>
                                        <span class="text">{$status->translatedName}</span>
                                    </label>
                                    <br>
                                {/foreach}
                            </div>
                        </div>

                        <div class="col-lg-2">
                            <button type="submit" class="btn btn-default shiny">{#gnr_search#}</button>
                        </div>
                    </div>
                </form>

                <br>
            </div><!--Widget Body-->
        </div>
        {if $smarty.session.search_deliveries}
            <div class="well text-center">
                {url urltype=alink url_string="ben/P269/AssistanceDelivery/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#}"}
            </div>
        {/if}
        {if $deliveries}
            <div class="form-group">
                {url check=0 urltype="alinkn" url_string="ben/P269/AssistanceDelivery/print/0/{$smarty.session.lang}/LandScape" text_value="<i class='fa fa-print black'></i>&nbsp;{#gnr_print#}&nbsp;{#gnr_horizontal#}" style="btn btn-default"}
                {url check=0 urltype="alinkn" url_string="ben/P269/AssistanceDelivery/print/0/{$smarty.session.lang}/Portrait" text_value="<i class='fa fa-print black'></i>&nbsp;{#gnr_print#}&nbsp;{#gnr_vertical#}" style="btn btn-default"}
            </div>
        {/if}
        <table class="table table-striped table-bordered">
            <thead>
            <tr>
                <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                <th style="background-color: #A0D468 !important;" width="20%">{#gnr_beneficiary#}</th>
                <th style="background-color: #A0D468 !important;" width="25%">{#p_assistance#}</th>
                <th style="background-color: #A0D468 !important;" width="25%">{#gnr_value#}
                    &nbsp;/&nbsp;{#p_materials#}</th>
                <th style="background-color: #A0D468 !important;" width="10%">{#p_delivery_status#}</th>
                <th style="background-color: #A0D468 !important;" width="15%">{#gnr_settings#}</th>
            </tr>
            </thead>
            {$i=1}
            <tbody>
            {foreach $deliveries as $delivery}
                <tr>
                    <td align="center">{$i++}</td>
                    <td align="right">
                        {$delivery->getDeliveryName()}
                    </td>
                    <td align="right">{t v=$delivery->assistance_class_id}
                        &nbsp;&raquo;&nbsp;{getname table=assist_assistance id=$delivery->assistance_id}</td>
                    <td align="center">
                        {if  $delivery->assistance_class_id eq AssistType::SETTING_ASSISTANCE_TYPE_CASH}{$delivery->value}{/if}
                        {if  $delivery->assistance_class_id eq AssistType::SETTING_ASSISTANCE_TYPE_MATERIAL}
                            {$x=1}
                            {foreach $delivery->materialsEntities as $material}
                                {$x++}&nbsp;-&nbsp;{getname table=assist_materials id=$material->assist_delivery_material_material_id}&nbsp;&nbsp;
                                {$material->assist_delivery_material_material_amount}&nbsp;{$material->assist_delivery_material_material_unit}

                                        {if $material->assist_delivery_material_status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_NO}
                                            <span style="color: red">&nbsp;&raquo;&nbsp;{t v=$material->assist_delivery_material_status}</span>
                                        {/if}
                                        {if $material->assist_delivery_material_status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_YES}
                                            <span style="color: green">&nbsp;&raquo;&nbsp;{t v=$material->assist_delivery_material_status}</span>
                                        {/if}
                                        <br>
                                    {/foreach}
                                {/if}
                            </td>
                            <td align="center">
                                {if $delivery->status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_NO}
                                    <span style="color: red">{t v=$delivery->status}</span>
                                {/if}
                                {if $delivery->status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_YES}
                                    <span style="color: green">{t v=$delivery->status}&nbsp;{#gnr_riyal#}</span>
                                {/if}
                            </td>
                            <td align="center">
                                {url check=0 urltype="mbutton" opr_code='AssistanceDelivery' url_string="ben/P269/AssistanceDelivery/browseComment/0/{$smarty.session.lang}/{$delivery->id}" text_value="<i class='fa fa-file-text'></i>"}
                                &nbsp;
                                {if  $delivery->assistance_class_id eq AssistType::SETTING_ASSISTANCE_TYPE_CASH}
                                    {url check=0 urltype="mbutton" opr_code='AssistanceDelivery' url_string="ben/P269/AssistanceDelivery/cashDelivery/0/{$smarty.session.lang}/{$delivery->id}" text_value="{#p_do_delivery#}"}
                                {/if}
                                {if  $delivery->assistance_class_id eq AssistType::SETTING_ASSISTANCE_TYPE_MATERIAL}
                                    {url check=0 urltype="mbutton" opr_code='AssistanceDelivery' url_string="ben/P269/AssistanceDelivery/materialDelivery/0/{$smarty.session.lang}/{$delivery->id}" text_value="{#p_do_delivery#}"}
                                {/if}

                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>
    {else}
        <span style="color: red">{#p_you_should_activate_assistance_year_first#}</span>
    {/if}

{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": {#gnr_next#}
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}