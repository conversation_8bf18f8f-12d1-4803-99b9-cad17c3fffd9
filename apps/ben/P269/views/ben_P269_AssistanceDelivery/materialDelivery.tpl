{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">
            {$assistanceYear->name}&nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
            &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
            &nbsp;&raquo;&nbsp;{$assistance->name}
            &nbsp;&raquo;&nbsp;{$assistanceRequest->date}
        </h4>
    </div>
    <div class="modal-body">
        <form method="post" action='{url urltype="path" url_string="ben/P269/AssistanceDelivery/show/0/{$smarty.session.lang}/materialsDoDelivery/{$smarty.session.s_AssistanceDelivery_token}/{$delivery->id}/{$sponsorship->id}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_beneficiary#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$delivery->getDeliveryName()}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_sponsership#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$sponsorship->full_name}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_receivable_name#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" name="receivable_name" class="form-control" value="{$delivery->receivable_name}" required></div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_receivable_identity_number#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" name="receivable_identity_number" class="form-control" value="{$delivery->receivable_identity_number}" maxlength="10" required></div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_comments#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><textarea type="text" name="receive_comment" class="form-control">{$delivery->receive_comment}</textarea></div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_delivery_materials#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $delivery->materialsEntities as $material}
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="materialsIds[]" value="{$material->assist_delivery_material_id}" {if $material->assist_delivery_material_status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_YES} checked {/if} required>
                                        <span class="text">{$status->translatedName}{getname table=assist_materials id=$material->assist_delivery_material_material_id}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_delivery_status#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $statusList as $status}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="status" value="{$status->id}" {if $status->id eq $delivery->status} checked {/if} required>
                                        <span class="text">{$status->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_delivered_by#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$smarty.session.user->full_name}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
