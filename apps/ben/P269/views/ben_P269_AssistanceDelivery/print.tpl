{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=title}
    {$smarty.session.organization->full_name}
    -
    {#p_fin_sheet#}
    -
    {$inquiredAccount->name} <span dir="ltr">[{$inquiredAccount->code}]</span>
{/block}
{block name=body}

    {if $assistanceYear}
        <h5 class="row-title before-blue">
            <i class="glyphicon glyphicon-list-alt blue"></i>
            {$assistanceYear->name}&nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
            &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
            &nbsp;|&nbsp;
            <i class="glyphicon glyphicon-user blue"></i>
            {$currentUser->full_name}
        </h5>
        <table class="table table-striped table-bordered">
            <thead>
            <tr>
                <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                <th style="background-color: #A0D468 !important;" width="20%">{#gnr_beneficiary#}</th>
                <th style="background-color: #A0D468 !important;" width="25%">{#p_assistance#}</th>
                <th style="background-color: #A0D468 !important;" width="25%">{#gnr_value#}&nbsp;/&nbsp;{#p_materials#}</th>
                <th style="background-color: #A0D468 !important;" width="10%">{#p_delivery_status#}</th>
            </tr>
            </thead>
            {$i=1}
            <tbody>
            {foreach $deliveries as $delivery}
                <tr>
                    <td align="center">{$i++}</td>
                    <td align="right">
                        {$delivery->getDeliveryName()}
                    </td>
                    <td align="right">{t v=$delivery->assistance_class_id}&nbsp;&raquo;&nbsp;{getname table=assist_assistance id=$delivery->assistance_id}</td>
                    <td align="center">
                        {if  $delivery->assistance_class_id eq AssistType::SETTING_ASSISTANCE_TYPE_CASH}{$delivery->value}{/if}
                        {if  $delivery->assistance_class_id eq AssistType::SETTING_ASSISTANCE_TYPE_MATERIAL}
                            {$x=1}
                            {foreach $delivery->materialsEntities as $material}
                                {$x++}&nbsp;-&nbsp;{getname table=assist_materials id=$material->assist_delivery_material_material_id}&nbsp;&nbsp;
                                {$material->assist_delivery_material_material_amount}&nbsp;{$material->assist_delivery_material_material_unit}

                                {if $material->assist_delivery_material_status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_NO}
                                    <span style="color: red">&nbsp;&raquo;&nbsp;{t v=$material->assist_delivery_material_status}</span>
                                {/if}
                                {if $material->assist_delivery_material_status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_YES}
                                    <span style="color: green">&nbsp;&raquo;&nbsp;{t v=$material->assist_delivery_material_status}</span>
                                {/if}
                                <br>
                            {/foreach}
                        {/if}
                    </td>
                    <td align="center">
                        {if $delivery->status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_NO}
                            <span style="color: red">{t v=$delivery->status}</span>
                        {/if}
                        {if $delivery->status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_YES}
                            <span style="color: green">{t v=$delivery->status}&nbsp;{#gnr_riyal#}</span>
                        {/if}
                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>
    {/if}

{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": {#gnr_next#}
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}