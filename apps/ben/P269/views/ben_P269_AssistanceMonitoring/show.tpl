{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}
    <div class="tabbable tabs-left">
        <ul class="nav nav-tabs" id="myTab3">
            <li class="active tab-sky">
                <a data-toggle="tab" href="#one"><span
                            class="badge badge-azure badge-square">1</span>&nbsp;{#p_delivering_status#}</a>
            </li>

            <li class="tab-sky">
                <a data-toggle="tab" href="#two"><span
                            class="badge badge-azure badge-square">2</span>&nbsp;{#p_assistances_types#}</a>
            </li>

            <li class="tab-sky">
                <a data-toggle="tab" href="#three"><span
                            class="badge badge-azure badge-square">3</span>&nbsp;{#p_assistances_classes#}</a>
            </li>

            <li class="tab-sky">
                <a data-toggle="tab" href="#four"><span
                            class="badge badge-azure badge-square">4</span>&nbsp;{#p_assistances_years#}</a>
            </li>

            <li class="tab-sky">
                <a data-toggle="tab" href="#five"><span
                            class="badge badge-azure badge-square">5</span>&nbsp;{#p_assistances_distributors#}</a>
            </li>

            <li class="tab-sky">
                <a data-toggle="tab" href="#six"><span
                            class="badge badge-azure badge-square">6</span>&nbsp;{#p_assistances_requests#}</a>
            </li>
            <li class="tab-sky">
                <a data-toggle="tab" href="#seven"><span
                            class="badge badge-azure badge-square">7</span>&nbsp;{#p_assistances_materials#}</a>
            </li>
        </ul>

        <div class="tab-content">
            <div id="one" class="tab-pane in active">
                <div class="col-lg-4 col-md-5 col-sm-12 col-xs-12">
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th width="40%">{#gnr_status#}</th>
                            <th width="20%">{#gnr_number#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {foreach $reportOneArray as $status}
                            <tr>
                                <td>{$status['name']}</td>
                                <td align="center">{$status['count']}</td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>
                </div>
                <div class="col-lg-8 col-md-7 col-sm-12 col-xs-12" style="text-align: center">
                    <div>
                        <img src="https://chart.googleapis.com/chart?chs=750x350&amp;chd=t:{$graph['chd']}&amp;cht=p&amp;chds=0,{$graph['max']}&amp;chm={$graph['chm']}&amp;chl={$graph['chl']}&amp;chbh=50,10,15&amp;chco={$graph['colors']}"
                             alt="Execution Chart" class="img-responsive" style="margin: auto;"/>
                    </div>
                </div>
            </div>
            <div id="two" class="tab-pane">
                <table class="table table-bordered table-hover">
                    <thead>
                    <tr>
                        <th width="40%">{#gnr_type#}</th>
                        <th width="20%">{#p_delivered#}</th>
                        <th width="20%">{#p_not_delivered#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $reportTwoArray as $type}
                        <tr>
                            <td>{$type['name']}</td>
                            <td align="center">{$type['delivered']}</td>
                            <td align="center">{$type['notDelivered']}</td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
            <div id="three" class="tab-pane">
                <div class="col-lg-4 col-md-5 col-sm-12 col-xs-12">
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th width="40%">{#gnr_class#}</th>
                            <th width="20%">{#p_deliveredAssits#}</th>
                            <th width="20%">{#p_undeliveredAssits#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {foreach $reportThreeArray as $class}
                            <tr>
                                <td>{$class['name']}</td>
                                <td align="center">{$class['delivered']}</td>
                                <td align="center">{$class['notDelivered']}</td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>
                </div>
                <div class="col-lg-8 col-md-7 col-sm-12 col-xs-12" style="text-align: center">
                    <div>
                        <img src="https://chart.googleapis.com/chart?chs=750x350&amp;chd=t:{$graph2['chd']}&amp;cht=p&amp;chds=0,{$graph2['max']}&amp;chm={$graph2['chm']}&amp;chl={$graph2['chl']}&amp;chbh=50,10,15&amp;chco={$graph2['colors']}"
                             alt="Execution Chart" class="img-responsive" style="margin: auto;"/>
                    </div>
                </div>
            </div>
            <div id="four" class="tab-pane">
                <table class="table table-bordered table-hover">
                    <thead>
                    <tr>
                        <th width="40%">{#gnr_year#}</th>
                        <th width="20%">{#p_deliveredAssits#}</th>
                        <th width="20%">{#p_undeliveredAssits#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $reportFourArray as $year}
                        <tr>
                            <td>{$year['name']}</td>
                            <td align="center">{$year['delivered']}</td>
                            <td align="center">{$year['notDelivered']}</td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
            <div id="five" class="tab-pane">
                <table class="table table-bordered table-hover">
                    <thead>
                    <tr>
                        <th width="40%">{#gnr_user#}</th>
                        <th width="20%">{#p_deliveredAssits#}</th>
                        <th width="20%">{#p_undeliveredAssits#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $reportFiveArray as $user}
                        <tr>
                            <td>{$user['name']}</td>
                            <td align="center">{$user['delivered']}</td>
                            <td align="center">{$user['notDelivered']}</td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
            <div id="six" class="tab-pane">
                <table class="table table-bordered table-hover">
                    <thead>
                    <tr>
                        <th width="40%">{#gnr_requests#}</th>
                        <th width="20%">{#p_deliveredAssits#}</th>
                        <th width="20%">{#p_undeliveredAssits#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $reportSixArray as $request}
                        <tr>
                            <td>{$request['name']}</td>
                            <td align="center">{$request['delivered']}</td>
                            <td align="center">{$request['notDelivered']}</td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
            <div id="seven" class="tab-pane">
                {foreach $reportSevenArray as $material}
                    <h5 class="row-title before-blue">
                        <i class="glyphicon glyphicon-list-alt blue"></i>{$material['name']}
                    </h5>
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th width="5%">&nbsp;</th>
                            <th width="45%">{#gnr_name#}</th>
                            <th width="25%">{#p_deliveredAssits#}</th>
                            <th width="25%">{#p_undeliveredAssits#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {$i=1}
                        {foreach $material['materials'] as $subMaterial}
                            <tr>
                                <td>{$i++}</td>
                                <td>{$subMaterial['name']}</td>
                                <td align="center">{$subMaterial['delivered']}</td>
                                <td align="center">{$subMaterial['notDelivered']}</td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>
                {/foreach}
            </div>
        </div>
    </div>
{/block}
