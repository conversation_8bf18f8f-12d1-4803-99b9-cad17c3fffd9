{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <!--Page Related styles-->
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}
    {*<div class="container-fluid">*}
    <div class="widget">
        <div class="widget-header bg-blue">
            <i class="widget-icon fa fa-arrow-left"></i>
            <span class="widget-caption">{#gnr_search#}</span>
            <div class="widget-buttons">
                <div class="widget-buttons">
                    <a href="#" data-toggle="collapse">
                    </a>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="widget-body">
                    <form action="{url urltype="path" url_string="ben/P269/AssistanceVisitsReport/show/0/{$smarty.session.lang}/report/{$smarty.session.s_AssistanceVisitsReport_token}"}"
                          method="post">

                        <div class="row mb-1">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                <select name="family" placeholder="{#p_family#}">
                                    <option></option>
                                    {foreach $families as $family}
                                        <option value="{$family->id}" {if $smarty.post["family"] eq $family->id} selected {/if}>
                                            {getname table=sh_user id=$family->head_of_the_family}
                                        </option>
                                    {/foreach}
                                    {*{foreach $families as $family}*}
                                        {*<option value="{$family->id}" {if $smarty.post["family"] eq $family->id} selected {/if} >*}
                                            {*[&nbsp;{$family->file_number}*}
                                            {*&nbsp;]&nbsp;{getname table=sh_user id=$family->husband_id}&nbsp;&raquo;&nbsp;{getname table=sh_user id=$family->wife_id}</option>*}
                                    {*{/foreach}*}
                                </select>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_executor#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">

                                <select name="executor"
                                        placeholder="{#p_visit_executor#}">
                                    <option></option>

                                    {foreach $researchers as $researcher}
                                        <option value="{$researcher->user_id}" {if $smarty.post["executor"] eq $researcher->user_id} selected {/if}>
                                            {$researcher->userObject->full_name}
                                        </option>
                                    {/foreach}
                                </select>
                            </div>

                        </div>
                        <div class="row mb-1">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_execution_statuses#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                <select name="status"
                                        placeholder="{#p_execution_statuses#}">
                                    <option></option>
                                    {foreach $executionStatues as $status}
                                        <option value="{$status->id}"
                                                {if $status->id eq $smarty.post['status']}selected{/if}>{$status->translatedName}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>


                        <div class="row mb-1">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_from#} {#gnr_date#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                {getdate col=fromDate type=report row={$smarty.post['fromDate']|default:''}}
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_to#} {#gnr_date#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                {getdate col=toDate type=report row={$smarty.post['toDate']|default:''}}
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 p-0">
                                    <input type="submit" class="btn btn-success sharp mr-4-px" name="submit"
                                           value="{#gnr_view#}">
                                    {if $smarty.post}
                                        {url check=0 urltype="button" style="btn btn-default shiny" url_string="ben/P269/AssistanceFamiliesReport/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
                                    {/if}
                                </div>


                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>

        <div class="row  mt-2 mb-1">
            <div class="col-sm-12">
                {url check=0 urltype="print" url_string="ben/P269/AssistanceVisitsReport/print/0/{$smarty.session.lang}" text_value="{#gnr_print_portrait#}" style="btn btn-default sharp"}
                {url check=0 urltype="print" url_string="ben/P269/AssistanceVisitsReport/print/0/{$smarty.session.lang}/landscape" text_value="{#gnr_print_landscape#}" style="btn btn-default sharp"}
            </div>
        </div>

        {*<div class="row  mt-2 mb-1">*}
            {*<div class="col-sm-12">*}
                {*{url urltype="print" check=0 url_string="ben/P269/AssistanceVisitsReport/print/0/{$smarty.session.lang}/{$family->id}" text_value="{#gnr_print#}" style="btn btn-default sharp"}*}
            {*</div>*}
        {*</div>*}
        <div class="row snsowraper">
            <div class="col-lg-12">
                <div class="widget-body">
                    <div class="row snsowraper">
                        <div class="table-responsive" data-pattern="priority-columns">
                            <table class="table table-bordered table-hover dataTable no-footer sortable-table">
                                <thead>
                                    <tr>
                                        <th width="5%"></th>
                                        <th width="20%">{#p_beneficiarey_name#}</th>
                                        <th width="15%">{#p_file_number#}</th>
                                        <th width="15%">{#p_visit_date#}</th>
                                        <th width="15%">{#p_record_type#}</th>
                                        <th width="15%">{#p_execution_status#}</th>
                                        <th width="15%">{#p_visit_executor#}</th>
                                        <th width="20%">{#p_visit_detail#}</th>
                                    </tr>
                                </thead>
                                {$i=1}
                                <tbody>
                                    {foreach $visits as $visit}
                                        {if ($visit->type eq AssistVisit::VISIT or ($visit->type eq AssistVisit::VISIT_REQUEST and $visit->requestEntity->wf_request_success))}
                                            <tr>
                                                <td class="text-center">{$i++}</td>
                                                <td class="text-center">{getname table=sh_user id=$visit->familyObject->head_of_the_family}</td>
                                                <td class="text-center">{$visit->familyObject->file_number}</td>
                                                <td class="text-center">{getdate table=assist_visit col=date type=show row=$visit}</td>
                                                <td class="text-center">
                                                    {getname table=st_setting id=$visit->type}
                                                </td>
                                                <td>
                                                    {if $visit->status eq 1051}
                                                        <span class="badge badge-warning">{#p_not_executed#}</span>
                                                    {elseif $visit->status eq 1052}
                                                        <span class="badge badge-info">{#p_under_execution#}</span>
                                                    {else}
                                                        <span class="badge badge-success">{#p_executed#}</span>
                                                    {/if}
                                                </td>
                                                <td>{getname table=sh_user id=$visit->executor}</td>
                                                <td class="text-center">
                                                    {if $visit->executor eq $smarty.session.user->id}
                                                        {url check=0 urltype="new" opr_code='fieldVisit' url_string="ben/P269/fieldVisits/details/0/{$smarty.session.lang}/visitData/{$visit->id}" text_value="<i class='fa fa-file-o'></i>"}
                                                    {else}
                                                        {url urltype="modal" check=0 url_string="gnr/X000/mediacenter/visitDetails/0/{$smarty.session.lang}/{$visit->id}" text_value="{#p_visit_details#}" style="btn btn-default shiny"}
                                                    {/if}
                                                </td>
                                            </tr>
                                        {/if}
                                    {/foreach}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 15,
                        "oTableTools": {
                            "aButtons": [
                                //"copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}

