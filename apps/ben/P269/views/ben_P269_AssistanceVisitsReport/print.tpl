{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}
    <div class="container-fluid">
        <div class="text-center">
            <h1><u>{#p_visits_data#}</u></h1>
        </div>
        <div class="panel panel-default">
            <div class="panel-body" style="display: inline-block;">
                {if !empty($filter['family'])}
                    {#p_family#} : {getname table=sh_user id=$filter['family']}
                    <br>
                {/if}
                {if !empty($filter['executor'])}
                    {#p_visit_executor#} : {getname table=sh_user id=$filter['executor']}
                    <br>
                {/if}

                {if !empty($filter['status'])}
                    {#p_execution_statuses#} : {t v=$filter['status']}
                    <br>
                {/if}
                {if !empty($filter['fromDate'])}
                    {#gnr_from#} : {$filter['fromDate']}
                    <br>
                {/if}
                {if !empty($filter['toDate'])}
                    {#gnr_to#} : {$filter['toDate']}
                    <br>
                {/if}

            </div>
        </div>
        <div class="row ">
            <div class="col-lg-12">
                <div class="widget-body">
                    <table class="table table-striped table-bordered table-hover no-footer">
                        <thead>
                        <tr>
                            <th width="5%"></th>
                            <th width="20%">{#p_beneficiarey_name#}</th>
                            <th width="15%">{#p_file_number#}</th>
                            <th width="15%">{#p_visit_date#}</th>
                            <th width="15%">{#p_record_type#}</th>
                            <th width="15%">{#execution_status#}</th>
                            <th width="15%">{#p_visit_executor#}</th>
                        </tr>
                        </thead>
                        {$i=1}
                        <tbody>
                        {foreach $visits as $visit}
                            {if ($visit->type eq AssistVisit::VISIT or ($visit->type eq AssistVisit::VISIT_REQUEST and $visit->requestEntity->wf_request_success))}
                                <tr>
                                    <td class="text-center">{$i++}</td>
                                    <td class="text-center">{getname table=sh_user id=$visit->familyObject->head_of_the_family}</td>
                                    <td class="text-center">{$visit->familyObject->file_number}</td>
                                    <td class="text-center">{getdate table=assist_visit col=date type=show row=$visit}</td>
                                    <td class="text-center">
                                        {getname table=st_setting id=$visit->type}
                                    </td>
                                    <td>
                                        {if $visit->status eq 1051}
                                            <span class="badge badge-warning">{#p_not_executed#}</span>
                                        {elseif $visit->status eq 1052}
                                            <span class="badge badge-info">{#p_under_execution#}</span>
                                        {else}
                                            <span class="badge badge-success">{#p_executed#}</span>
                                        {/if}
                                    </td>
                                    <td>{getname table=sh_user id=$visit->executor}</td>
                                </tr>
                            {/if}
                        {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{/block}