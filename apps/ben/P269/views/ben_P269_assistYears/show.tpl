{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}
    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
        <thead>
        <tr>
            <th style="background-color: #A0D468 !important;" width="5%">{url check=1 opr_code="assistYears" urltype=madd url_string="ben/P269/assistYears/add/0/{$smarty.session.lang}" modal=modal}</th>
            <th style="background-color: #A0D468 !important;" width="45%">{#gnr_name#}&nbsp;:&nbsp;{#gnr_start_date#}&nbsp;&raquo;&nbsp;{#gnr_end_date#}</th>
            <th style="background-color: #A0D468 !important;" width="25%">{#gnr_activation_status#}</th>
            <th style="background-color: #A0D468 !important;" width="25%">{#gnr_settings#}</th>
        </tr>
        </thead>
        {$i=1}
        <tbody>
        {foreach $years as $year}
            <tr>
                <td align="center">{$i++}</td>
                <td align="right">{$year->name}&nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$year}&nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$year}</td>
                <td align="center">
                    {if $year->activation_status eq Setting::NOT_ACTIVE}
                        {url urltype="mbutton" url_string="ben/P269/assistYears/activateYear/0/{$smarty.session.lang}/{$year->id}" text_value="{#p_activate_as_current_assistance_year#}"}
                    {else}
                        <span class="btn-success">{#p_current_assistance_year#}</span>
                    {/if}
                </td>
                <td class="text-center">
                    {if $year->activation_status eq Setting::NOT_ACTIVE}
                        {if !$year->haveAssistances()}
                            {if $year->usage eq 0}
                                {url check=1 opr_code="assistYears" urltype=medit url_string="ben/P269/assistYears/edit/0/{$smarty.session.lang}/{$year->id}" modal=modal}
                                {url check=1 opr_code="assistYears" urltype=mdelete url_string="ben/P269/assistYears/confirm/0/{$smarty.session.lang}/{$year->id}" modal=modal}
                            {/if}
                        {else}
                            {url check=0 urltype="mbutton" url_string="ben/P269/assistYears/help/0/{$smarty.session.lang}/{$year->id}" text_value="<i class='fa fa-question-circle'></i>" modal="modal"}
                        {/if}
                    {else}
                       {url check=0 urltype="mbutton" url_string="ben/P269/assistYears/help_for_Activated/0/{$smarty.session.lang}/{$year->id}" text_value="<i class='fa fa-question-circle'></i>" modal="modal"}
                    {/if}
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
{/block}