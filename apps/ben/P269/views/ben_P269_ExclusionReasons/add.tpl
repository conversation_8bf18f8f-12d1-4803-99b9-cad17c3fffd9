{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add#} {#p_gro_palce_holder_name#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form  method="post" action='{url check=1 urltype="path" url_string="ben/P269/ExclusionReasons/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_ExclusionReasons_token}"}' enctype="multipart/form-data">
                <div class="row snsowraper">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_assist_exclusion_reasons_reason#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <input type="text" name="assist_exclusion_reasons_reason" required class="form-control" placeholder="{#p_name#}{#p_gro_palce_holder_name#}"></div>


                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp" >{#gnr_add#}</button></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
{block name=footer}
<script src="/templates/assets/js/modal/getFileName.js"></script>
<script>
    let allowedFilles = {json_encode(Document::DOCUMENT_ALLOWED_FILE_TYPES)}
        $('#files').change(function(){
            let fileExtentionExist;
            allowedFilles.forEach(extintion =>{
                if( fileExtention == extintion ){
                fileExtentionExist = true

            }
        })
            if(!fileExtentionExist){
                $('.addDocument').addClass('disabled')
                $('#filesExtentions').show()
            }
            else{
                $('.addDocument').removeClass('disabled')
                $('#filesExtentions').hide()
            }
        })
</script>
{/block}
{*{block name=footer}*}

    {*<script type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>*}
    {*<script type="text/javascript">*}
        {*$(document).ready(function (e) {*}
            {*$('#upload').attr('value',"templates//assets//img//group.png");*}
        {*});*}
        {*});*}
    {*</script>*}
{*{/block}*}