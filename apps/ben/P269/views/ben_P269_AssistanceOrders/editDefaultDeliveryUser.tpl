{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">
            {$assistanceYear->name}&nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
            &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
            &nbsp;&raquo;&nbsp;{$assistance->name}
            &nbsp;&raquo;&nbsp;{$assistanceRequest->date}
        </h4>
    </div>
    <div class="modal-body">
        <form method="post"
              action='{url urltype="path" url_string="ben/P269/AssistanceOrders/geographicalDistributionSetting/0/{$smarty.session.lang}/{$geographical->id}/updateDefault/{$smarty.session.s_AssistanceOrders_token}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_neighborhood#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getname table=st_neighborhood id=$geographical->neighborhood_id}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_delivery_user#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $deliveryUsers as $user}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="delivery_user_id" value="{$user->user_id}" {if $user->user_id eq $geographical->delivery_user_id} checked {/if} required>
                                        <span class="text">{$user->userObject->full_name}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
