{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>{/block}
{block name=page_body}
    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-list-alt blue"></i>
        {$assistanceYear->name}&nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
        &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
        &nbsp;&raquo;&nbsp;{$assistance->name}
        &nbsp;&raquo;&nbsp;{$assistanceRequest->date}
    </h5>

    <br>

    {if $sortType eq 1}
        {if $assistanceRequest->term_of_selection_type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_INDIVIDUAL}

            {foreach $assistanceRequest->requestCategories as $category}

                <h5 class="row-title before-blue">
                    <i class="glyphicon glyphicon-list-alt blue"></i>{$category->name}
                </h5>

                <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                    <thead>
                    <tr>
                        <th style="background-color: #A0D468 !important;" width="5%"></th>
                        <th style="background-color: #A0D468 !important;" width="20%">{#gnr_name#}</th>
                        <th style="background-color: #A0D468 !important;" width="10%">{#p_identity_number#}</th>
                        {if in_array('neighborhood',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_neighborhood#}</th>{/if}
                        {if in_array('tell',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_tell#}</th>{/if}
                        {if in_array('email',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_email#}</th>{/if}
                        {if in_array('age',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_age#}</th>{/if}
                        {if in_array('gender',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_gender#}</th>{/if}
                        {if in_array('nationality',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_nationality#}</th>{/if}
                        {if in_array('identity_number',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_identity_number#}</th>{/if}
                        {if in_array('birth_date',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_birth_date#}</th>{/if}
                        {if in_array('work_status',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_work_status#}</th>{/if}
                        {if in_array('bank_number',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_ayban_number#}</th>{/if}
                        {if in_array('belongs_to_user',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_belongs_to_user#}</th>{/if}
                    </tr>
                    </thead>
                    {$i=1}
                    <tbody>
                    {foreach $category->beneficiaries as $beneficiary}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>{$beneficiary->full_name}</td>
                            <td>{$beneficiary->identity_number}</td>
                            {if in_array('neighborhood',$dataSelected)}<td>{getname table=st_neighborhood id=$beneficiary->address_neighborhood}</td>{/if}
                            {if in_array('tell',$dataSelected)}<td>{$beneficiary->tell}</td>{/if}
                            {if in_array('email',$dataSelected)}<td>{$beneficiary->email}</td>{/if}
                            {if in_array('age',$dataSelected)}<td>{$beneficiary->age}</td>{/if}
                            {if in_array('gender',$dataSelected)}<td>{t v=$beneficiary->gender}</td>{/if}
                            {if in_array('nationality',$dataSelected)}<td>{getname table=st_country id=$beneficiary->nationality}</td>{/if}
                            {if in_array('identity_number',$dataSelected)}<td>{$beneficiary->identity_number}</td>{/if}
                            {if in_array('birth_date',$dataSelected)}<td>{$beneficiary->birth_date}</td>{/if}
                            {if in_array('work_status',$dataSelected)}<td>{t v=$beneficiary->work_status}</td>{/if}
                            {if in_array('bank_number',$dataSelected)}<td>{$beneficiary->bank_number}</td>{/if}
                            {if in_array('belongs_to_user',$dataSelected)}<td>{getname table=sh_user id=$beneficiary->belongs_to_user}</td>{/if}
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            {/foreach}

        {/if}

        {if $assistanceRequest->term_of_selection_type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY}

            {foreach $assistanceRequest->requestCategories as $category}

                <h5 class="row-title before-blue">
                    <i class="glyphicon glyphicon-list-alt blue"></i>{$category->name}
                </h5>

                <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                    <thead>
                    <tr>
                        <th style="background-color: #A0D468 !important;" width="5%"></th>
                        <th style="background-color: #A0D468 !important;" width="25%">{#gnr_name#}</th>
                        {if in_array('file_number',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_file_number#}</th>{/if}
                        {if in_array('nationality_id',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_nationality_id#}</th>{/if}
                        {if in_array('neighborhood_id',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_neighborhood_id#}</th>{/if}
                        {if in_array('floor_number',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_floor_number#}</th>{/if}
                        {if in_array('national_number',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_national_number#}</th>{/if}
                        {if in_array('housing_type',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_housing_type#}</th>{/if}
                        {if in_array('housing_rent',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_housing_rent#}</th>{/if}
                        {if in_array('family_member_number',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_family_member_number#}</th>{/if}
                        {if in_array('health_status',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_health_status#}</th>{/if}
                        {if in_array('average_family_income',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_average_family_income#}</th>{/if}
                        {if in_array('belongs_to_user',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_belongs_to_user#}</th>{/if}
                    </tr>
                    </thead>
                    {$i=1}
                    <tbody>
                    {foreach $category->beneficiaries as $beneficiary}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>{AssistanceFamilies::getFamilyName($beneficiary->id)}</td>
                            {if in_array('file_number',$dataSelected)}<td>{$beneficiary->file_number}</td>{/if}
                            {if in_array('nationality_id',$dataSelected)}<td>{getname table=st_country id=$beneficiary->nationality_id}</td>{/if}
                            {if in_array('neighborhood_id',$dataSelected)}<td>{getname table=st_neighborbood id=$beneficiary->address_neighborhood}</td>{/if}
                            {if in_array('floor_number',$dataSelected)}<td>{$beneficiary->floor_number}</td>{/if}
                            {if in_array('national_number',$dataSelected)}<td>{$beneficiary->national_number}</td>{/if}
                            {if in_array('housing_type',$dataSelected)}<td>{t v=$beneficiary->housing_type}</td>{/if}
                            {if in_array('housing_rent',$dataSelected)}<td>{$beneficiary->housing_rent}</td>{/if}
                            {if in_array('family_member_number',$dataSelected)}<td>{$beneficiary->family_member_number}</td>{/if}
                            {if in_array('health_status',$dataSelected)}<td>{t v=$beneficiary->health_status}</td>{/if}
                            {if in_array('average_family_income',$dataSelected)}<td>{$beneficiary->average_family_income}</td>{/if}
                            {if in_array('belongs_to_user',$dataSelected)}<td>{getname table=sh_user id=$beneficiary->belongs_to_user}</td>{/if}
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            {/foreach}

        {/if}
    {/if}

    {if $sortType eq 2}
        {if $request->term_of_selection_type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_INDIVIDUAL}

            {foreach $neighborhoods as $neighborhood}

                <h5 class="row-title before-blue">
                    <i class="glyphicon glyphicon-list-alt blue"></i>{$neighborhood->name}
                </h5>

                <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                    <thead>
                    <tr>
                        <th style="background-color: #A0D468 !important;" width="5%"></th>
                        <th style="background-color: #A0D468 !important;" width="25%">{#gnr_name#}</th>
                        <th style="background-color: #A0D468 !important;" width="40%">{#p_identity_number#}</th>
                        {if in_array('neighborhood',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_neighborhood#}</th>{/if}
                        {if in_array('tell',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_tell#}</th>{/if}
                        {if in_array('email',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_email#}</th>{/if}
                        {if in_array('age',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_age#}</th>{/if}
                        {if in_array('gender',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_gender#}</th>{/if}
                        {if in_array('nationality',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_nationality#}</th>{/if}
                        {if in_array('identity_number',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_identity_number#}</th>{/if}
                        {if in_array('birth_date',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_birth_date#}</th>{/if}
                        {if in_array('work_status',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_work_status#}</th>{/if}
                        {if in_array('bank_number',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_ayban_number#}</th>{/if}
                        {if in_array('belongs_to_user',$dataSelected)}<th style="background-color: #A0D468 !important;" width="10%">{#p_belongs_to_user#}</th>{/if}
                    </tr>
                    </thead>
                    {$i=1}
                    <tbody>
                    {foreach $neighborhood->beneficiaries as $beneficiary}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>{$beneficiary->full_name}</td>
                            <td>{$beneficiary->identity_number}</td>
                            {if in_array('neighborhood',$dataSelected)}<td>{getname table=st_neighborhood id=$beneficiary->address_neighborhood}</td>{/if}
                            {if in_array('tell',$dataSelected)}<td>{$beneficiary->tell}</td>{/if}
                            {if in_array('email',$dataSelected)}<td>{$beneficiary->email}</td>{/if}
                            {if in_array('age',$dataSelected)}<td>{$beneficiary->age}</td>{/if}
                            {if in_array('gender',$dataSelected)}<td>{t v=$beneficiary->gender}</td>{/if}
                            {if in_array('nationality',$dataSelected)}<td>{getname table=st_country id=$beneficiary->nationality}</td>{/if}
                            {if in_array('identity_number',$dataSelected)}<td>{$beneficiary->identity_number}</td>{/if}
                            {if in_array('birth_date',$dataSelected)}<td>{$beneficiary->birth_date}</td>{/if}
                            {if in_array('work_status',$dataSelected)}<td>{t v=$beneficiary->work_status}</td>{/if}
                            {if in_array('bank_number',$dataSelected)}<td>{$beneficiary->bank_number}</td>{/if}
                            {if in_array('belongs_to_user',$dataSelected)}<td>{getname table=sh_user id=$beneficiary->belongs_to_user}</td>{/if}
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            {/foreach}

        {/if}

        {if $request->term_of_selection_type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY}

            {foreach $neighborhoods as $neighborhood}

                <h5 class="row-title before-blue">
                    <i class="glyphicon glyphicon-list-alt blue"></i>{$neighborhood->name}
                </h5>

                <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                    <thead>
                    <tr>
                        <th style="background-color: #A0D468 !important;" width="5%"></th>
                        <th style="background-color: #A0D468 !important;" width="25%">{#gnr_name#}</th>
                        {if in_array('husband_id',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_husband_id#}</th>{/if}
                        {if in_array('file_number',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_file_number#}</th>{/if}
                        {if in_array('nationality_id',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_nationality_id#}</th>{/if}
                        {if in_array('neighborhood_id',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_neighborhood_id#}</th>{/if}
                        {if in_array('floor_number',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_floor_number#}</th>{/if}
                        {if in_array('national_number',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_national_number#}</th>{/if}
                        {if in_array('housing_type',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_housing_type#}</th>{/if}
                        {if in_array('housing_rent',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_housing_rent#}</th>{/if}
                        {if in_array('family_member_number',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_family_member_number#}</th>{/if}
                        {if in_array('health_status',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_health_status#}</th>{/if}
                        {if in_array('average_family_income',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_average_family_income#}</th>{/if}
                        {if in_array('belongs_to_user',$dataSelected)}<th style="background-color: #A0D468 !important;" width="25%">{#p_belongs_to_user#}</th>{/if}
                    </tr>
                    </thead>
                    {$i=1}
                    <tbody>
                    {foreach $neighborhood->beneficiaries as $beneficiary}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>{AssistanceFamilies::getFamilyName($beneficiary->id)}</td>
                            {if in_array('file_number',$dataSelected)}<td>{$beneficiary->file_number}</td>{/if}
                            {if in_array('nationality_id',$dataSelected)}<td>{getname table=st_country id=$beneficiary->nationality_id}</td>{/if}
                            {if in_array('neighborhood_id',$dataSelected)}<td>{getname table=st_neighborbood id=$beneficiary->address_neighborhood}</td>{/if}
                            {if in_array('floor_number',$dataSelected)}<td>{$beneficiary->floor_number}</td>{/if}
                            {if in_array('national_number',$dataSelected)}<td>{$beneficiary->national_number}</td>{/if}
                            {if in_array('housing_type',$dataSelected)}<td>{t v=$beneficiary->housing_type}</td>{/if}
                            {if in_array('housing_rent',$dataSelected)}<td>{$beneficiary->housing_rent}</td>{/if}
                            {if in_array('family_member_number',$dataSelected)}<td>{$beneficiary->family_member_number}</td>{/if}
                            {if in_array('health_status',$dataSelected)}<td>{t v=$beneficiary->health_status}</td>{/if}
                            {if in_array('average_family_income',$dataSelected)}<td>{$beneficiary->average_family_income}</td>{/if}
                            {if in_array('belongs_to_user',$dataSelected)}<td>{getname table=sh_user id=$beneficiary->belongs_to_user}</td>{/if}
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            {/foreach}

        {/if}
    {/if}

{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": {#gnr_next#}
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}