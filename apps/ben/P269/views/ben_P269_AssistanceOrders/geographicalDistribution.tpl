{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-list-alt blue"></i>
        {$assistanceYear->name}&nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
        &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
        &nbsp;&raquo;&nbsp;{$assistance->name}
        &nbsp;&raquo;&nbsp;{$assistanceRequest->date}
    </h5>
    <table class="table table-striped table-bordered">
        <thead>
        <tr>
            <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
            <th style="background-color: #A0D468 !important;" width="25%">{#gnr_name#}</th>
            <th style="background-color: #A0D468 !important;" width="15%">{#p_assistances_number#}</th>
            <th style="background-color: #A0D468 !important;" width="15%">{#p_delivery_user#}</th>
            <th style="background-color: #A0D468 !important;" width="15%">{#p_delivery_helper_user#}</th>
            <th style="background-color: #A0D468 !important;" width="15%">{#gnr_settings#}</th>
        </tr>
        </thead>
        {$i=1}
        <tbody>
        {foreach $geographicals as $geographical}
            <tr>
                <td align="center">{$i++}</td>
                <td align="right">
                    {getname table=st_region id=$geographical->region_id}
                    &nbsp;&raquo;&nbsp;
                    {getname table=st_city id=$geographical->city_id}
                    &nbsp;&raquo;&nbsp;
                    {getname table=st_neighborhood id=$geographical->neighborhood_id}
                </td>
                <td align="center">{count($geographical->deliveries)}</td>
                <td align="center">{getname table=sh_user id=$geographical->delivery_user_id}</td>
                <td align="center">{$geographical->distributionHelperUsers()}</td>
                <td align="center">
                    {url check=0 urltype="button" opr_code='AssistanceRequests' url_string="ben/P269/AssistanceOrders/geographicalDistributionSetting/0/{$smarty.session.lang}/{$geographical->id}" text_value="{#gnr_settings#}"}
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
{/block}
{block name=back}{url urltype=path url_string="ben/P269/AssistanceOrders/show/0/{$smarty.session.lang}"}{/block}