{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <!--Page Related styles-->
<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}

    {if $assistanceYear}

        <h5 class="row-title before-blue">
            <i class="glyphicon glyphicon-list-alt blue"></i>
            {$assistanceYear->name}&nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
            &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
        </h5>

        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
            <thead>
            <tr>
                <th width="5%" style="background-color: #A0D468 !important;">&nbsp;</th>
                <th width="10%" style="background-color: #A0D468 !important;">{#gnr_date#}</th>
                <th width="25%" style="background-color: #A0D468 !important;">{#gnr_type#}</th>
                <th width="25%" style="background-color: #A0D468 !important;">{#p_term_of_selection#}</th>
                <th width="10%" style="background-color: #A0D468 !important;">{#b_beneficiaries_number#}</th>
                <th width="10%" style="background-color: #A0D468 !important;">{#gnr_export_data#}</th>
                <th width="10%" style="background-color: #A0D468 !important;">{#gnr_settings#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $requests as $request}
                <tr>
                    <td align="center">{$i++}</td>
                    <td align="center">{getdate table=wf_request col=created_date type=showauto row=$request->created_date}</td>
                    <td>{t v=$request->dataObject->class_id}&nbsp;&raquo;&nbsp;{getname table=assist_type id=$request->dataObject->type_id}</td>
                    <td>{t v=$request->dataObject->term_of_selection_type}&nbsp;&raquo;&nbsp;{getname table=assist_terms_of_selection id=$request->dataObject->term_of_selection_id}</td>
                    <td align="center">{$request->dataObject->deliveriesNumber}</td>
                    <td align="center">
                        {url check=0 urltype="mbutton" opr_code='AssistanceRequests' url_string="ben/P269/AssistanceOrders/exportData/0/{$smarty.session.lang}/{$request->dataObject->id}" text_value="{#gnr_export_data#}"}
                    </td>
                    <td align="center">
                        {url check=0 urltype="button" opr_code='AssistanceRequests' url_string="ben/P269/AssistanceOrders/geographicalDistribution/0/{$smarty.session.lang}/{$request->dataObject->id}" text_value="{#p_adjust_delivery_records#}"}
                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>

    {else}

        <span style="color: red">{#p_you_should_activate_assistance_year_first#}</span>

    {/if}

{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": {#gnr_next#}
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}