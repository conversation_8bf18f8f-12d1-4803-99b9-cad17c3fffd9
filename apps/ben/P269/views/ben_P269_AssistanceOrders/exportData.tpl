{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_export_data#}</h4>
    </div>
    <div class="modal-body">
        <form method="post"
              action='{url urltype="path" url_string="ben/P269/AssistanceOrders/exportDataRecords/0/{$smarty.session.lang}/{$request->id}"}' target="_blank">
            <div class="row snsowraper">
                <div class="col-lg-12">

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_data#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                        {if $request->term_of_selection_type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_INDIVIDUAL}
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="neighborhood"><span class="text">{#p_neighborhood#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="tell"><span class="text">{#p_tell#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="email"><span class="text">{#p_email#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="age"><span class="text">{#p_age#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="gender"><span class="text">{#p_gender#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="nationality"><span class="text">{#p_nationality#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="identity_number"><span class="text">{#p_identity_number#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="birth_date"><span class="text">{#p_birth_date#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="work_status"><span class="text">{#p_work_status#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="bank_number"><span class="text">{#p_ayban_number#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="belongs_to_user"><span class="text">{#p_belongs_to_user#}</span></label></div>
                        {/if}

                        {if $request->term_of_selection_type eq TermsOfSelection::TERMS_OF_CONDITION_TYPE_FOR_FAMILY}
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="file_number"><span class="text">{#file_number#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="nationality_id"><span class="text">{#nationality_id#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="neighborhood_id"><span class="text">{#neighborhood_id#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="floor_number"><span class="text">{#floor_number#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="national_number"><span class="text">{#national_number#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="housing_type"><span class="text">{#housing_type#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="housing_rent"><span class="text">{#housing_rent#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="family_member_number"><span class="text">{#family_member_number#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="health_status"><span class="text">{#health_status#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="average_family_income"><span class="text">{#average_family_income#}</span></label></div>
                            <div class="checkbox"><label><input type="checkbox" name="dataSelected[]" value="belongs_to_user"><span class="text">{#belongs_to_user#}</span></label></div>
                        {/if}
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_based_on#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <div class="radio"><label><input type="radio" name="sortType" value="1" required><span class="text">{#p_by_categories#}</span></label></div>
                            <div class="radio"><label><input type="radio" name="sortType" value="2" required><span class="text">{#p_by_neighborhoods#}</span></label></div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
