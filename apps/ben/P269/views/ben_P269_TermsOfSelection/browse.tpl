{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{$term->name}</h4>
    </div>
    <div class="modal-body" id="root">
        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{t v=$term->type}</div>

        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_criteria#}</div>
        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th style="background-color: #A0D468 !important;" width="5%"></th>
                    <th style="background-color: #A0D468 !important;" width="25%">{#gnr_criterion#}</th>
                    <th style="background-color: #A0D468 !important;" width="70%">{#gnr_values#}</th>
                </tr>
                </thead>
                {$i=1}
                <tbody>
                {foreach $term->getCriteriaList() as $criterion}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td>{t v=$criterion->type_id}</td>
                        <td class="text-center">{$criterion->browseTermCriterionDetails()}</td>
                    </tr>
                {/foreach}
                </tbody>
            </table>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_assistances#}</div>
        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th style="background-color: #A0D468 !important;" width="5%"></th>
                    <th style="background-color: #A0D468 !important;" width="95%">{#p_assistance#}</th>
                </tr>
                </thead>
                {$i=1}
                <tbody>
                {foreach $assistances as $assistance}
                    <tr>
                        <td align="center">{$i++}</td>
                        <td>{$assistance->assist_assistance_name}</td>
                    </tr>
                {/foreach}
                </tbody>
            </table>
        </div>


    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
{block name=footer}

{/block}