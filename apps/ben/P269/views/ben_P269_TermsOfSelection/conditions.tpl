{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}multi-page.tpl"}
{block name=title}{getname table=assist_conditions id=$condition->id}{/block}
{$backURLString = "ben/P269/TermsOfSelection/show/0/{$smarty.session.lang}/menu"}
{block name=page_content}
    <div>
        <div class="col col-md-12">
            <h4>
                {$condition->name}
            </h4>
            <br>
        </div>
        <conditions :initial-data='{$data}' inline-template>
            <form method="post"
                  action='{url urltype="path" url_string="ben/P269/TermsOfSelection/conditions/0/{$smarty.session.lang}/insert/{$condition->id}/{$criteria->id}/{$smarty.session.s_TermsOfSelection_token}"}'>
                <div class="row snsowraper">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">
                        <span class="text">{#p_nationalty#}</span>
                        <span class="info"><i
                                    class="fa fa-question-circle tooltip-info"
                                    data-toggle="tooltip" data-placement="top"
                                    data-original-title="{#p_nationalities_hint#}"></i></span>
                    </div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <Selectize name="nationalities[]"
                                   multiple="multiple"
                                   v-model="nationalities">
                            <option value="">{#gnr_unspecific#}</option>
                            {foreach $countries as $nationality}
                                <option value="{$nationality->id}"> {$nationality->name} </option>
                            {/foreach}
                        </Selectize>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">السكن</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <Selectize name="country"
                                   @input="getRegion"
                                   v-model="country">

                            <option value="">{#gnr_unspecified#}</option>
                            {foreach $countries as $country}
                                <option value="{$country->id}">{$country->name}</option>
                            {/foreach}

                        </Selectize>

                        <div v-if="country" class="form-group">
                            <div class="radio">
                                <label>
                                    <input name="specificRegion" v-model="specificRegion" :value="false" type="radio">
                                    <span class="text">{#p_all_regions#}</span>
                                </label>
                            </div>
                            <div class="radio">
                                <label>
                                    <input name="specificRegion" v-model="specificRegion" :value="true" type="radio">
                                    <span class="text">{#p_specific_region#}</span>
                                </label>
                                <div v-if="specificRegion && regionArray.length" style="margin-top: 10px !important;">
                                    <Selectize name="region"
                                               required="required"
                                               @input="getCity"
                                               v-model="region">

                                        <option value="">{#gnr_unspecified#}</option>
                                        <option v-for="region in regionArray" :value="region.id"
                                                v-text="region.name"></option>
                                    </Selectize>
                                </div>
                                <p v-if="!regionArray.length" class="text-warning">{#p_region_error#}</p>
                            </div>
                        </div>

                        <div v-if="region && specificRegion">
                            <div class="radio">
                                <label>
                                    <input name="specificCity" v-model="specificCity" :value="false" type="radio" checked>
                                    <span class="text">{#p_all_cities#}</span>
                                </label>
                            </div>
                            <div class="radio">
                                <label>
                                    <input name="specificCity"
                                           v-model="specificCity" :value="true" type="radio">
                                    <span class="text">{#p_specific_city#}</span>
                                </label>
                                <div v-if="specificCity && cityArray.length" :key="'city'" style="margin-top: 10px !important;">
                                    <Selectize v-model="city"
                                               name="city"
                                               required="required">
                                        <option v-for="city in cityArray" :value="city.id"
                                                :key="city.id" v-text="city.name"></option>
                                    </Selectize>
                                </div>
                                <p v-if="!cityArray.length" class="text-warning">{#p_cities_error#}</p>

                            </div>
                        </div>

                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">

                        <span class="text">{#p_age#}</span>
                        <span class="info"><i
                                    class="fa fa-question-circle tooltip-info"
                                    data-toggle="tooltip" data-placement="top"
                                    data-original-title="{#p_age_hint#}"></i></span>

                    </div>

                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <input v-model="age" type="number" step="0.1" name="age" class="form-control">
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_housing_type#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        {foreach $housing_type_list as $housing_type}
                            <div class="radio">
                                <label>
                                    <input name="housingType" v-model="housingType" :value="{$housing_type->id}" type="radio">
                                    <span class="text">{$housing_type->translatedName}</span>
                                </label>
                            </div>
                        {/foreach}
                        <div v-if="housingType == 368" style="margin-top: 10px !important;">
                            <input type="number" min="0" class="col col-md-4 form-group form-control"
                                   v-model="rentAmount"
                                   placeholder="{#p_rent_placeholder#}" name="rentAmount">
                        </div>

                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">
                        <span class="text">{#p_family_members_count#}</span>
                        <span class="info"><i
                                    class="fa fa-question-circle tooltip-info"
                                    data-toggle="tooltip" data-placement="top"
                                    data-original-title="{#p_family_members_hint#}"></i></span>
                    </div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <input type="number" name="familyMembers" v-model="familyMembers" class="form-control">
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">
                        <span class="text">{#p_family_income_average#}</span>
                        <span class="info"><i
                                    class="fa fa-question-circle tooltip-info"
                                    data-toggle="tooltip" data-placement="top"
                                    data-original-title="{#p_family_income_average_hint#}"></i></span>
                    </div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <input type="number" name="familyIncomeAverage" v-model="familyIncomeAverage" class="form-control">
                    </div>
                    <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                </div>
            </form>
        </conditions>
    </div>
{/block}
