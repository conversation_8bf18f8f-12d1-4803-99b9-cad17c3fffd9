{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script type="text/javascript">
        $(document).ready(function () {

            $("#1229").click(function () {
                if ($("#1229").is(":checked")) {
                    $("#1231").show('fast');
                    $("#1233").show('fast');
                    $("#1254").show('fast');
                    $("#1255").show('fast');
                    $("#1256").show('fast');
                    $("#1259").show('fast');
                    $("#1258").show('fast');
                    $("#1261").show('fast');
                    $("#1276").show('fast');
                    $("#1234").hide('fast');
                    $("#1232").hide('fast');
                    $("#1236").hide('fast');
                    $("#1260").hide('fast');
                    $("#1235").hide('fast');
                    $("#1257").hide('fast');
                    $("#classes").show('fast');
                }
            });

            $("#1230").click(function () {
                if ($("#1230").is(":checked")) {
                    $("#1234").show('fast');
                    $("#1235").show('fast');
                    $("#1236").show('fast');
                    $("#1260").show('fast');
                    $("#1232").show('fast');
                    $("#1257").show('fast');
                    $("#1276").show('fast');
                    $("#1231").hide('fast');
                    $("#1233").hide('fast');
                    $("#1254").hide('fast');
                    $("#1255").hide('fast');
                    $("#1256").hide('fast');
                    $("#1259").hide('fast');
                    $("#1258").hide('fast');
                    $("#1261").hide('fast');
                    $("#classes").hide('fast');
                }
            });

        });
    </script>
    {$typeCode}
{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">إضافة قالب إختيار جديد</h4>
    </div>
    <div class="modal-body">
        <form method="post" action='{url urltype="path" url_string="ben/P269/TermsOfSelection/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_TermsOfSelection_token}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">اسم القالب</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="name" placeholder="اسم قالب الشروط" required="required"></div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">نوع القالب</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $termsType as $term}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="type" value="{$term->id}" id="{$term->id}" {if $term->id eq 1229} checked {/if} required>
                                        <span class="text">{$term->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div id="classes">
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">أصناف المستفيدين</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">

                                <div class="radio">
                                    <label>
                                        <input type="radio" checked name="user_classes_ids[]" value="{$class->id}" id="{$class->id}" >
                                        <span class="text">{$class->name}</span>
                                    </label>
                                </div>
                                {*{foreach $categories as $category}*}
                                    {*<div class="checkbox">*}
                                        {*<label>*}
                                            {*<input type="checkbox" name="user_classes_ids[]" value="{$category->id}" id="{$category->id}">*}
                                            {*<span class="text">{$category->name}</span>*}
                                        {*</label>*}
                                    {*</div>*}
                                {*{/foreach}*}
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_criteria#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $criterionType as $criterion}
                                <div class="checkbox" id="{$criterion->id}">
                                    <label>
                                        <input type="checkbox" name="criteria_ids[]" value="{$criterion->id}">
                                        <span class="text">{$criterion->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp">{#gnr_add#}</button></div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>

{/block}
