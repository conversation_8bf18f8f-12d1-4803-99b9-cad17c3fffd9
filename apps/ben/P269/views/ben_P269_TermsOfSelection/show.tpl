{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}
    <table class="table table-striped table-bordered">
        <thead>
        <tr>
            <th style="background-color: #A0D468 !important;" width="5%">{url check=1 opr_code="TermsOfSelection" urltype=madd url_string="ben/P269/TermsOfSelection/add/0/{$smarty.session.lang}" modal=modal}</th>
            <th style="background-color: #A0D468 !important;" width="35%">{#gnr_name#}</th>
            <th style="background-color: #A0D468 !important;" width="20%">{#gnr_type#}</th>
            <th style="background-color: #A0D468 !important;" width="20%">{#gnr_beneficiaries#}</th>
            <th style="background-color: #A0D468 !important;" width="20%">{#gnr_settings#}</th>
        </tr>
        </thead>
        {$i=1}
        <tbody>
        {foreach $terms as $term}
            <tr>
                <td align="center">{$i++}</td>
                <td>
                    {url check=0 urltype=mbutton url_string="ben/P269/TermsOfSelection/browse/0/{$smarty.session.lang}/{$term->id}" text_value="{#gnr_view#}" style="btn btn=default shiny"}
                    {$term->name}
                </td>
                <td align="center">{t v=$term->type}</td>
                <td align="center">
                    {url check=0 urltype=button url_string="ben/P269/TermsOfSelection/show/0/{$smarty.session.lang}/updateTermBeneficiaries/{$smarty.session.s_TermsOfSelection_token}/{$term->id}" text_value="<i class='fa fa-refresh'></i>" style="btn btn=default shiny"}
                    {if $term->type eq 1229}
                        [&nbsp;{$term->beneficiariesNumber}&nbsp;]&nbsp;
                        {url check=0 urltype=mbutton url_string="ben/P269/TermsOfSelection/beneficiaries/0/{$smarty.session.lang}/{$term->id}" text_value="{#gnr_beneficiaries#}" style="btn btn=default shiny"}
                    {/if}

                    {if $term->type eq 1230}
                        [&nbsp;{$term->familiesNumber}&nbsp;]&nbsp;
                        {url check=0 urltype=mbutton url_string="ben/P269/TermsOfSelection/families/0/{$smarty.session.lang}/{$term->id}" text_value="{#gnr_beneficiaries#}" style="btn btn=default shiny"}
                    {/if}
                </td>
                <td class="text-center">
                    {if $term->assistanceCount eq 0}
                        {url check=0 urltype=button url_string="ben/P269/TermsOfSelection/criteria/0/{$smarty.session.lang}/save_session/{$term->id}" text_value="{#p_criteria_configuration#}" style="btn btn=default shiny"}
                        {url check=0 opr_code="TermsOfSelection" urltype=medit url_string="ben/P269/TermsOfSelection/edit/0/{$smarty.session.lang}/{$term->id}/backToShow" modal=modal}
                        {url check=0 opr_code="TermsOfSelection" urltype=mdelete url_string="ben/P269/TermsOfSelection/confirm/0/{$smarty.session.lang}/{$term->id}" modal=modal}
                    {else}
                        <span style="color: green">{#p_term_of_selection_has_been_used#}</span>
                    {/if}
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
{/block}