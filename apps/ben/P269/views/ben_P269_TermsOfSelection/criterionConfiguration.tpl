{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{getname table=st_setting id=$criterion->type_id}</h4>
    </div>
    <div class="modal-body">
        <form method="post"
              action='{url urltype="path" url_string="ben/P269/TermsOfSelection/criteria/0/{$smarty.session.lang}/updateCriterion/{$smarty.session.s_TermsOfSelection_token}/{$criterion->id}"}'>
            <div class="row snsowraper">

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_NATIONALITY}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">الجنسية/الجنسيات</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {*{foreach $nationalities as $nationality}*}
                                {*<div class="checkbox">*}
                                    {*<label>*}
                                        {*<input type="checkbox" name="nationalities[]"*}
                                               {*value="{$nationality->id}" {if in_array($nationality->id, $criterion->valueArray)} checked {/if}>*}
                                        {*<span class="text">{$nationality->translatedName}</span>*}
                                    {*</label>*}
                                {*</div>*}
                            {*{/foreach}*}
                            <select name="nationalities[]" placeholder="{#gnr_select_from_list_bellow#}"
                                   multiple required>
                                <option></option>
                                {foreach $nationalities as $nationality}
                                    <option value="{$nationality->id}" {if in_array($nationality->id, $criterion->valueArray)} checked {/if} >
                                  {$nationality->translatedName}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_HOUSING_PLACE}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">مكان السكن</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $neighborhoods as $neighborhood}
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="neighborhoods[]"
                                               value="{$neighborhood->id}" {if in_array($neighborhood->id, $criterion->valueArray)} checked {/if}>
                                        <span class="text">
                                             {getname table=st_region id=$neighborhood->region_id}
                                            &nbsp;&raquo;&nbsp;
                                            {getname table=st_city id=$neighborhood->city_id}
                                            &nbsp;&raquo;&nbsp;
                                            {$neighborhood->name}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_EDUCATION_LEVEL}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">المستوى التعليمي</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $educationLevels as $education}
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="educations[]"
                                               value="{$education->id}" {if in_array($education->id, $criterion->valueArray)} checked {/if}>
                                        <span class="text">{$education->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_GENDER}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">الجنس</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $GenderTypes as $gender}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="gender"
                                               value="{$gender->id}" {if $gender->id eq $criterion->value} checked {/if}
                                               required>
                                        <span class="text">{$gender->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_MARTIAL_STATUS}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">الحالة الإجتماعية</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $martialStatusTypes as $martial}
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="martials[]"
                                               value="{$martial->id}" {if in_array($martial->id, $criterion->valueArray)} checked {/if}>
                                        <span class="text">{$martial->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_USER_HEALTH_STATUS}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">الحالة الصحية</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $userHealthStatus as $health}
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="healthStatus[]"
                                               value="{$health->id}" {if in_array($health->id, $criterion->valueArray)} checked {/if}>
                                        <span class="text">{$health->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_WORK_STATUS}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">حالة العمل</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $workStatus as $work}
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="workStatus[]"
                                               value="{$work->id}" {if in_array($work->id, $criterion->valueArray)} checked {/if}>
                                        <span class="text">{$work->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_FAMILY_HEALTH_STATUS}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">الحالة الصحية للأسرة</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $familyHealthStatus as $familyHealth}
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="familyHealth[]"
                                               value="{$familyHealth->id}" {if in_array($familyHealth->id, $criterion->valueArray)} checked {/if}>
                                        <span class="text">{$familyHealth->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_USER_INCOME}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">المقارنة</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $comparisons as $comparison}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="comparison_type"
                                               value="{$comparison->id}" {if $comparison->id eq $criterion->comparison_type} checked {/if}
                                               required>
                                        <span class="text">{$comparison->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_value#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="number" name="value"
                                                                                        class="form-control"
                                                                                        value="{$criterion->value}"
                                                                                        required></div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_HOUSING_RENT}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">المقارنة</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $comparisons as $comparison}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="comparison_type"
                                               value="{$comparison->id}" {if $comparison->id eq $criterion->comparison_type} checked {/if}
                                               required>
                                        <span class="text">{$comparison->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_value#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="number" name="value"
                                                                                        class="form-control"
                                                                                        value="{$criterion->value}"
                                                                                        required></div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_HOUSING_TYPE}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">مكان السكن</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $housingTypeList as $type}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="housingType"
                                               value="{$type->id}" {if $type->id eq $criterion->value} checked {/if}
                                               required>
                                        <span class="text">{$type->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_AGE}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">المقارنة</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $comparisons as $comparison}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="comparison_type"
                                               value="{$comparison->id}" {if $comparison->id eq $criterion->comparison_type} checked {/if}
                                               required>
                                        <span class="text">{$comparison->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_value#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="number" name="value"
                                                                                        class="form-control"
                                                                                        value="{$criterion->value}"
                                                                                        required></div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_FAMILY_MEMBERS_NUMBER}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">المقارنة</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $comparisons as $comparison}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="comparison_type"
                                               value="{$comparison->id}" {if $comparison->id eq $criterion->comparison_type} checked {/if}>
                                        <span class="text">{$comparison->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_value#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="number" name="value"
                                                                                        class="form-control"
                                                                                        value="{$criterion->value}"
                                                                                        required></div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_FAMILY_INCOME_AVERAGE}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">المقارنة</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $comparisons as $comparison}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="comparison_type"
                                               value="{$comparison->id}" {if $comparison->id eq $criterion->comparison_type} checked {/if}
                                               required>
                                        <span class="text">{$comparison->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_value#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="number" name="value"
                                                                                        class="form-control"
                                                                                        value="{$criterion->value}"
                                                                                        required></div>
                {/if}

                {if $criterion->type_id eq TermsOfSelection::TERMS_OF_CONDITION_CRITERION_TYPE_SPONSORSHIP}
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">حالة الكفالة</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $sponserStatus as $sponser}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="sponserTypes"
                                               value="{$sponser->id}" {if in_array($sponser->id, $criterion->valueArray)} checked {/if}>
                                        <span class="text">{$sponser->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                {/if}

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                </div>

            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
