{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}

    {url check=1 urltype="mbutton" opr_code='materialsSetting' oprvtype=1 url_string="ben/P269/materialsSetting/addMaterialType/0/{$smarty.session.lang}" text_value="إضافة نوع أعيان"}

    <div class="horizontal-space"></div>

    <div class="tabbable tabs-left">

        <ul class="nav nav-tabs" id="myTab3">
            {foreach $materials as $material}
                <li class="tab-sky  {if $smarty.session.s_assistance_materials_tab eq $material->id} active {/if}">
                    <a data-toggle="tab" href="#{$material->id}type">
                        <i class="typcn typcn-th-menu blueberry"></i>&nbsp;{$material->name}
                    </a>
                </li>
            {/foreach}
        </ul>

        <div class="tab-content">

            {foreach $materials as $material}

                <div id="{$material->id}type" class="tab-pane  {if $smarty.session.s_assistance_materials_tab eq $material->id} is active {/if}">

                    <div class="col-lg-12">
                        <div class="well">
                            <div class="row">
                                <div class="col-lg-6"><h4><i class="typcn typcn-th-menu blueberry"></i>&nbsp;{$material->name}</h4></div>
                                <div class="col-lg-1">{$material->childrenCount}</div>
                                <div class="col-lg-5" style="text-align: left">
                                    {url check=1 urltype="mbutton" oprvtype=2 opr_code='materialsSetting' url_string="ben/P269/materialsSetting/add/0/{$smarty.session.lang}/{$material->id}" text_value="إضافة عين"}
                                    &nbsp;&nbsp;
                                    {url check=1 urltype="medit" opr_code='materialsSetting' url_string="ben/P269/materialsSetting/editMaterialType/0/{$smarty.session.lang}/{$material->id}"}
                                    {if $material->childrenCount eq 0}
                                        {url check=1 urltype="mdelete" opr_code='materialsSetting' url_string="ben/P269/materialsSetting/confirmMaterialType/0/{$smarty.session.lang}/{$material->id}"}
                                    {/if}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12">

                        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">

                            <tr>
                                <th width="5%" style="background-color: #A0D468 !important;">م</th>
                                <th width="65%" style="background-color: #A0D468 !important;">العين</th>
                                <th width="15%" style="background-color: #A0D468 !important;">الضبط</th>
                            </tr>

                            {$i=1}
                            {foreach $material->materials as $levelTowMaterial}
                                <tr>
                                    <td>{$i++}</td>
                                    <td>{$levelTowMaterial->assist_materials_name}</td>
                                    <td>
                                        {url check=1 urltype="medit" opr_code='materialsSetting' url_string="ben/P269/materialsSetting/edit/0/{$smarty.session.lang}/{$material->id}/{$levelTowMaterial->assist_materials_id}"}
                                        &nbsp;&nbsp;
                                        {if $levelTowMaterial->usage eq 0}
                                            {url check=1 urltype="mdelete" opr_code='materialsSetting' url_string="ben/P269/materialsSetting/confirm/0/{$smarty.session.lang}/{$material->id}/{$levelTowMaterial->assist_materials_id}"}
                                        {/if}
                                    </td>
                                </tr>
                            {/foreach}
                        </table>

                    </div>
                </div>
            {/foreach}

        </div>
    </div>
{/block}