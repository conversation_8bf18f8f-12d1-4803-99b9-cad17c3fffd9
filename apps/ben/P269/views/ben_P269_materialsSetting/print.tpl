{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}
    <div class="row">
        <div class="col-lg-12">
            {foreach $centers as $center}
                <div id="{$center['id']}center"
                     class="tab-pane  {if $smarty.session.s_tag_centers_active_tab eq $center['id']} is active {/if}">

                    <div class="col-lg-12">
                        <div class="row">
                            <div class="col-lg-6">
                                <h5>{$center['name']} &nbsp;|&nbsp; {$center['code']}</h5>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12">

                        <table class="table table-striped table-bordered">
                            <thead>
                            <tr>
                                <th>المركز</th>
                                <th>المستوى</th>
                                <th>النوع</th>
                            </tr>
                            </thead>

                            {foreach $center['centers'] as $levelTwoCenter}
                                <tr>
                                    <td>{$levelTwoCenter['code']}&nbsp;{$levelTwoCenter['name']}&nbsp;</td>
                                    <td>{$levelTwoCenter['level']}&nbsp;</td>
                                    <td>
                                        {if $levelTwoCenter['branching'] == FinTagCenter::SETTING_HEAD_FINANCIAL_CENTER}
                                            <span>
                                            <i class="fa fa-circle"></i>
                                                {t v=$levelTwoCenter['branching']}&nbsp;
                                        </span>
                                        {else}
                                            <span>
                                            <i class="fa fa-code-fork"></i>
                                                {t v=$levelTwoCenter['branching']}&nbsp;
                                        </span>
                                        {/if}
                                    </td>
                                </tr>
                                {foreach $levelTwoCenter['centers'] as $levelThreeCenter}
                                    <tr>
                                        <td>&nbsp;&nbsp;&nbsp;&nbsp;&raquo;&nbsp;&raquo;&nbsp;{$levelThreeCenter['code']}&nbsp;{$levelThreeCenter['name']}&nbsp;</td>
                                        <td>{$levelThreeCenter['level']}&nbsp;</td>
                                        <td>

                                            {if $levelThreeCenter['branching'] == FinTagCenter::SETTING_HEAD_FINANCIAL_CENTER}
                                                <span>
                                            <i class="fa fa-circle"></i>
                                                    {t v=$levelThreeCenter['branching']}&nbsp;
                                            </span>
                                            {else}
                                                <span>
                                                <i class="fa fa-code-fork"></i>
                                                    {t v=$levelThreeCenter['branching']}&nbsp;
                                            </span>
                                            {/if}

                                    </tr>
                                    {foreach $levelThreeCenter['centers'] as $levelFourCenter}
                                        <tr>
                                            <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&raquo;&nbsp;&raquo;&nbsp;&raquo;&nbsp;{$levelFourCenter['code']}&nbsp;{$levelFourCenter['name']}&nbsp;</td>
                                            <td>{$levelFourCenter['level']}&nbsp;</td>
                                            <td>
                                                {if $levelFourCenter['branching'] == FinTagCenter::SETTING_HEAD_FINANCIAL_CENTER}
                                                    <span>
                                                    <i class="fa fa-circle"></i>
                                                        {t v=$levelFourCenter['branching']}&nbsp;
                                                </span>
                                                {else}
                                                    <span>
                                                    <i class="fa fa-code-fork"></i>
                                                        {t v=$levelFourCenter['branching']}&nbsp;
                                                </span>
                                                {/if}
                                            </td>
                                        </tr>
                                        {foreach $levelFourCenter['centers'] as $levelFiveCenter}
                                            <tr>
                                                <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&raquo;&nbsp;&raquo;&nbsp;&raquo;&nbsp;{$levelFiveCenter['code']}&nbsp;{$levelFiveCenter['name']}&nbsp;</td>
                                                <td>{$levelFiveCenter['level']}&nbsp;</td>
                                                <td>
                                                    {if $levelFourCenter['branching'] == FinTagCenter::SETTING_HEAD_FINANCIAL_CENTER}
                                                        <span>
                                                    <i class="fa fa-circle"></i>
                                                            {t v=$levelFiveCenter['branching']}&nbsp;
                                                </span>
                                                    {else}
                                                        <span>
                                                    <i class="fa fa-code-fork"></i>
                                                            {t v=$levelFiveCenter['branching']}&nbsp;
                                                </span>
                                                    {/if}
                                                </td>
                                            </tr>
                                        {/foreach}
                                    {/foreach}

                                {/foreach}

                            {/foreach}
                        </table>

                    </div>
                </div>
            {/foreach}
        </div>
    </div>
{/block}