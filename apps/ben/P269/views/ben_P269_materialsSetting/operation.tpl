{*start content*}
{capture name=contentContainer assign=contents}{/capture}
{capture contentContainer assign=contents}
    {$modalTitleError = {#p_entry_ccenter#}}
    {if isset($loading)}
        <div class="progress progress-striped active">
            <div class="progress-bar progress-bar-success" id="progressBar" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width: 0">
                                            <span class="sr-only">
                                                100% Complete (success)
                                            </span>
            </div>
        </div>
        <script>
            function count() {
                for(i=0; i<=100; i=i+20) {
                    $('#progressBar').css('width', i + '%');
                }
            }

            setTimeout(function(){
                count();
            }, 50);
        </script>
    {/if}
    {if $error}
    {else}
        <!-- /////////////////////////////////////HEADER////////////////////////////////////// -->
        {if $entery->type eq FinEntry::SETTING_GENERAL_ENTRY}
            <div class="row snsowraper ">
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_date#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=fin_entery col=date type=show row=$entery}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_amount#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$entery->amount}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_number#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$entery->num}</div>
            </div>
        {/if}

        {if $entery->type eq FinEntry::SETTING_RECEIPT_ENTRY}
            <div class="row snsowraper ">
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_date#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=fin_entery col=date type=show row=$entery}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_amount#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$entery->amount}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_number#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$entery->num}</div>
            </div>
        {/if}

        {if $entery->type eq FinEntry::SETTING_PAYMENT_ENTRY}
            <div class="row snsowraper ">
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_date#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=fin_entery col=date type=show row=$entery}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_amount#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$entery->amount}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_number#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$entery->num}</div>
            </div>
        {/if}

        {if $entery->type eq FinEntry::SETTING_OPENING_ENTRY}
            <div class="row snsowraper ">
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_date#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=fin_entery col=date type=show row=$entery}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_amount#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$entery->amount}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_number#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$entery->num}</div>
            </div>
        {/if}

        {if $entery->type eq FinEntry::SETTING_CLOSING_ENTRY}
            <div class="row snsowraper">
                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_date#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=fin_entery col=date type=show row=$entery}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_amount#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$entery->amount}</div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_cc_entery_number#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$entery->num}</div>
            </div>
        {/if}

        <!-- /////////////////////////////////////BODY////////////////////////////////////// -->
        <div class="row snsowraper">
            <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
                <thead>
                <tr>
                    <th width="5%">&nbsp;</th>
                    <th width="20%" >{#p_cc_account_name#}</th>
                    <th width="40%" >{#p_cc_account_naration#}</th>
                    <th width="10%" >{#p_cc_account_depit#}</th>
                    <th width="10%">{#p_cc_account_credit#}</th>
                    <th width="15%">{#p_cc_account_center#}</th>
                </tr>
                </thead>
                {$i=1}

                {foreach $transactions as $transaction}
                    {if $transaction->entery_type eq FinEntry::SETTING_RECEIPT_ENTRY and empty($transaction->depit)}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>{getname table=fin_acc id=$transaction->acc_id}</td>
                            <td>{$transaction->comment}</td>
                            <td align="center">{$transaction->depit}</td>
                            <td align="center">{$transaction->credit}</td>
                            <td>
                                <form method="post"
                                      action='{url urltype="path" url_string="bsc/P054/costCenter/operation/0/{$smarty.session.lang}/update/{$transaction->id}/{$transaction->entery_id}"}'>
                                    <select name="ccenter_id">
                                        <option value="0" selected>&nbsp;</option>
                                        {if $transaction->ccenter_id neq 0}
                                            <option value="{$transaction->ccenter_id}"
                                                    selected>{getname table=fin_ccenter id=$transaction->ccenter_id}</option>
                                        {/if}
                                        {foreach $ccenter_list as $ccrow}
                                            <option value="{$ccrow->id}">{$ccrow->padding}</option>
                                        {/foreach}
                                    </select>
                                    <input type="hidden" name="redirect" value="bsc/P054/finrecipt/show/0/">
                                    <button type="submit" name="submit"
                                            class="btn btn-warning sharp">{#gnr_update#}</button>
                                </form>
                            </td>
                        </tr>

                    {elseif $transaction->entery_type eq FinEntry::SETTING_PAYMENT_ENTRY and empty($transaction->credit)}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>{getname table=fin_acc id=$transaction->acc_id}</td>
                            <td>{$transaction->comment}</td>
                            <td align="center">{$transaction->depit}</td>
                            <td align="center">{$transaction->credit}</td>
                            <td>
                                <form method="post"
                                      action='{url urltype="path" url_string="bsc/P054/costCenter/operation/0/{$smarty.session.lang}/update/{$transaction->id}/{$row->entery_id}"}'>
                                    <select name="ccenter_id">
                                        <option value="0" selected>&nbsp;</option>
                                        {if $transaction->ccenter_id neq 0}
                                            <option value="{$transaction->ccenter_id}"
                                                    selected>{getname table=fin_ccenter id=$transaction->ccenter_id}</option>
                                        {/if}
                                        {foreach $ccenter_list as $ccrow}
                                            <option value="{$ccrow.fin_ccenter_id}">{$ccrow.fin_ccenter_padding}</option>
                                        {/foreach}
                                    </select>
                                    <input type="hidden" name="redirect" value="bsc/P054/finpay/show/0/">
                                    <button type="submit" name="submit"
                                            class="btn btn-warning sharp">{#gnr_update#}</button>
                                </form>
                            </td>
                        </tr>
                    {elseif $transaction->entery_type eq FinEntry::SETTING_GENERAL_ENTRY}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>{getname table=fin_acc id=$transaction->acc_id}</td>
                            <td>{$transaction->comment}</td>
                            <td align="center">{$transaction->depit}</td>
                            <td align="center">{$transaction->credit}</td>
                            <td>
                                <form method="post"
                                      action='{url urltype="path" url_string="bsc/P054/costCenter/operation/0/{$smarty.session.lang}/update/{$transaction->id}/{$transaction->entery_id}"}'>
                                    <select name="ccenter_id">
                                        <option value="0" selected>&nbsp;</option>
                                        {if $transaction->ccenter_id neq 0}
                                            <option value="{$transaction->ccenter_id}"
                                                    selected>{getname table=fin_ccenter id=$transaction->ccenter_id}</option>
                                        {/if}
                                        {foreach $ccenter_list as $ccrow}
                                            <option value="{$ccrow.fin_ccenter_id}">{$ccrow.fin_ccenter_padding}</option>
                                        {/foreach}
                                    </select>
                                    <input type="hidden" name="redirect" value="bsc/P054/finenteries/show/0/">
                                    <button type="submit" name="submit"
                                            class="btn btn-warning sharp">{#gnr_update#}</button>
                                </form>
                            </td>
                        </tr>
                    {/if}
                {/foreach}
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>{#p_cc_account_sum#}</td>
                    <td align="center">{$entry->debit_sum}</td>
                    <td align="center">{$entry->credit_sum}</td>
                </tr>
            </table>
        </div>
        <script>
            var loaders = 0;
            $(function () {
                $('form').submit(function (e) {

                    loaders++;
                    var currentLoader = loaders;

                    var submitButton = $(this).children("[type='submit']");

                    var formAction = $(this).attr('action');

                    // Disable the submit button until the request is been processed.
                    submitButton.attr('disabled', 'disabled').addClass('disabled');

                    // Display spinner to indicate loading..
                    $(this).append('<i id="l' + currentLoader  + '" class="fa fa-spinner fa-spin"></i>');

                    var loadingElement = $("#l" + currentLoader);

                    // Prepare data
                    var ccenterSelect = "form[action='" + formAction +"'] > select[name='ccenter_id']";
                    var ccenter = $(ccenterSelect).val();

                    // Send Data
                    $.ajax({
                        url: formAction,
                        type: "POST",
                        data: {literal}{ccenter_id: ccenter}{/literal},
                        cache: false,
                        success: function () { // Enable button & show success message
                            submitButton.removeAttr('disabled').removeClass('disabled');
                            loadingElement.removeClass('fa-spin').removeClass('fa-spinner')
                                .addClass('fa-check').addClass('success').delay(500).fadeOut(500);

                            window.setTimeout(function () {
                                loadingElement.remove();
                            }, 1000);
                        }, error: function () { // Fail message
                            submitButton.removeAttr('disabled').removeClass('disabled');
                            loadingElement.removeClass('fa-spin');
                            loadingElement.removeClass('fa-spinner');
                            loadingElement.addClass('fa-times');
                            loadingElement.addClass('danger').delay(500).fadeOut(500);
                            window.setTimeout(function () {
                                loadingElement.remove();
                            }, 1000);
                        }
                    });

                    return false;
                });
            });
        </script>
    {/if}
{/capture}
{*end content*}

{if $directAccess}
    {if isset($message)}
        {$contents = "<div class='alert alert-danger fade in'>{$message}</div>"}
    {/if}

    {*Run the full design layout mode for direct access*}
    {$titleString = {t v=$entery->type}}
    {$backURLString = "bsc/P054/finenteries/show/0/{$smarty.session.lang}"}
    {include "{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}extra.tpl"}

{else}
    <div class="modal-content">

        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" >×</button>
            {$modalTitleError|default:null}
        </div>
        <div class="modal-body">
            {if isset($message)}
                <div class="alert alert-danger fade in">{$message}</div>
            {else}
                {$contents}
            {/if}
        </div>
    </div>
    <!-- /.modal-content -->
{/if}
