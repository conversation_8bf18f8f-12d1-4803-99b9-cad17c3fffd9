test{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}

    {if $grfExistenceNum eq 1}
        {*<div class="row">*}
            {*<div class="col-lg-12">*}
                {*<form action="{url urltype=path url_string="ben/P269/fieldVisitRequests/show/0/{$smarty.session.lang}/changeEmployee"}"*}
                      {*method="post">*}
                    {*<select name="employee_id" onchange="this.form.submit()"*}
                            {*required {if empty($currentEmployee)} placeholder="{#gnr_unspecified#}" {else} placeholder="{$currentEmployee->full_name}" {/if}>*}
                        {*<option value=""></option>*}
                        {*{foreach $employees as $employee}*}
                            {*<option value="{$employee->sh_user_id}">*}
                                {*{$employee->sh_user_fr_name}&nbsp;*}
                                {*{$employee->sh_user_secd_name}&nbsp;*}
                                {*{$employee->sh_user_thrd_name}&nbsp;*}
                                {*{$employee->sh_user_fm_name}*}
                                {*&nbsp;&raquo;&nbsp;*}
                                {*{$employee->sh_uao_job_name}*}
                            {*</option>*}
                        {*{/foreach}*}
                    {*</select>*}
                {*</form>*}
            {*</div>*}
        {*</div>*}
        {*{if $smarty.session.s_employee_id}*}
{*<div id="{$gate->id}" class="tab-pane {if $smarty.session.s_gate_active_tab eq $gate->id}in active{/if}">*}
    <div class="row snsowraper">
        <div class="tabbable">
            <ul class="nav nav-tabs nav-justified" id="myTab5">
                <li class="tab-sky {if $smarty.session.s_active_horizontal_tab eq 'manage'}active{/if}">
                    <a href="{url urltype=path url_string="ben/P269/fieldVisitRequests/show/0/{$smarty.session.lang}/manage"}">
                        {#p_manage_requests#}
                    </a>
                </li>

                <li class="tab-red {if $smarty.session.s_active_horizontal_tab eq 'myRequests'}active{/if}">
                    <a href="{url urltype=path url_string="ben/P269/fieldVisitRequests/show/0/{$smarty.session.lang}/myRequests"}">
                        {#p_my_requests#}
                    </a>
                </li>
            </ul>

            <div class="tab-content">

                {*<div class="horizontal-space"></div>*}
                {*{url urltype="mbutton" check=0 url_string="ben/P269/fieldVisitRequests/add/0/{$smarty.session.lang}" text_value="{#p_new_visit_request#}" style="btn btn-default shiny"}*}
                {*<div class="horizontal-space"></div>*}
                {*<table class="table table-striped table-bordered table-hover no-footer">*}
                    {*<thead>*}
                    {*<tr>*}
                        {*<th width="10%"></th>*}
                        {*<th style="background-color: #A0D468 !important;" width="20%">{#p_beneficiarey_name#}</th>*}
                        {*<th style="background-color: #A0D468 !important;" width="10%">{#p_file_number#}</th>*}
                        {*<th style="background-color: #A0D468 !important;" width="10%">{#p_visit_date#}</th>*}
                        {*<th style="background-color: #A0D468 !important;" width="10%">{#p_record_type#}</th>*}
                        {*<th style="background-color: #A0D468 !important;" width="20%">{#gnr_position_procedure#}</th>*}
                        {*<th width="10%">{#gnr_settings#}</th>*}
                    {*</tr>*}
                    {*</thead>*}
                    {*{$i=1}*}
                    {*<tbody>*}
                    {*{foreach $visits as $visit}*}
                        {*<tr>*}
                            {*<td class="text-center">{$i++}</td>*}
                            {*<td class="text-center">{getname table=sh_user id=$visit->familyObject->husband_id}</td>*}
                            {*<td class="text-center">{$visit->familyObject->file_number}</td>*}
                            {*<td class="text-center">{getdate table=assist_visit col=date type=show row=$visit}</td>*}
                            {*<td class="text-center">{getname table=st_setting id=$visit->type}</td>*}
                            {*<td class="text-center">*}
                                {*{workflow requestId=$visit->requestEntity->wf_request_id backTo="ben/P269/fieldVisitRequests/show/0/{$smarty.session.lang}"}*}
                            {*</td>*}
                            {*<td class="text-center">*}
                                {*{if !($visit->requestEntity->wf_request_success)}*}
                                    {*{if $visit->executor eq $smarty.session.user->id}*}
                                        {*{url check=0 urltype="medit" url_string="ben/P269/fieldVisitRequests/edit/0/{$smarty.session.lang}/{$visit->id}"}*}
                                        {*{url check=0 urltype="mdelete" url_string="ben/P269/fieldVisitRequests/confirm/0/{$smarty.session.lang}/{$visit->id}"}*}
                                    {*{/if}*}
                                {*{/if}*}
                            {*</td>*}
                        {*</tr>*}
                    {*{/foreach}*}
                    {*</tbody>*}
                {*</table>*}
            {*{/if}*}

        {*{else}*}
            {*{GraphNotPreparedCorrectlly oprCode='fieldVisitRequests'}*}
        {*{/if}*}
                <div class="tab-pane {if $smarty.session.s_active_horizontal_tab eq 'manage'}in active{/if}">
                    <table class="table table-striped table-bordered table-hover no-footer">
                        <thead>
                            <tr>
                                {if can('add')}
                                    <th style="background-color: #A0D468 !important;" width="5%">{url check=1 opr_code="fieldVisits" urltype=madd url_string="ben/P269/fieldVisitRequests/add/0/{$smarty.session.lang}" modal=modal}</th>
                                {/if}
                                <th style="background-color: #A0D468 !important;" width="10%">{#p_date#}</th>
                                <th style="background-color: #A0D468 !important;" width="10%">{#p_executer#}
                                <th style="background-color: #A0D468 !important;" width="10%">{#p_visit_type#}</th>
                                <th style="background-color: #A0D468 !important;" width="15%">{#p_created_by#}</th>
                                <th style="background-color: #A0D468 !important;" width="20%">{#p_procedure#}</th>
                                <th width="10%">{#gnr_settings#}</th>
                            </tr>
                        </thead>
                        {$i=1}
                        <tbody>
                            {foreach $visits as $visit}
                                <tr>
                                    {*<td class="text-center">{$i++}</td>*}
                                    {*<td class="text-center">{getname table=sh_user id=$visit->familyObject->husband_id}</td>*}
                                    {*<td class="text-center">{$visit->familyObject->executor}</td>*}
                                    {*<td class="text-center">{getdate table=assist_visit col=date type=show row=$visit}</td>*}
                                    {*<td class="text-center">*}
                                    {*{getname table=st_setting id=$visit->type}*}
                                    {*{if $visit->status eq 1051}*}
                                    {*<span class="badge badge-warning">{#p_not_executed#}</span>*}
                                    {*{elseif $visit->status eq 1052}*}
                                    {*<span class="badge badge-info">{#p_under_execution#}</span>*}
                                    {*{else}*}
                                    {*<span class="badge badge-success">{#p_executed#}</span>*}
                                    {*{/if}*}
                                    {*</td>*}
                                    {*<td class="text-center">*}
                                    {*{if $visit->executor eq $smarty.session.user->id}*}
                                    {*{url urltype="button" check=0 url_string="ben/P269/fieldVisits/details/0/{$smarty.session.lang}/visitData/{$visit->id}" text_value="{#p_visit_details#}" style="btn btn-default shiny"}*}
                                    {*{else}*}
                                    {*{url urltype="mbutton" check=0 url_string="gnr/X000/mediacenter/visitDetails/0/{$smarty.session.lang}/{$visit->id}" text_value="{#p_visit_details#}" style="btn btn-default shiny"}*}
                                    {*{/if}*}
                                    {*</td>*}
                                    {*<td class="text-center">*}
                                    {*{if $visit->executor eq $smarty.session.user->id}*}
                                    {*{url check=0 urltype="medit" url_string="ben/P269/fieldVisits/edit/0/{$smarty.session.lang}/{$visit->id}"}*}
                                    {*{url check=0 urltype="mdelete" url_string="ben/P269/fieldVisits/confirm/0/{$smarty.session.lang}/{$visit->id}"}*}
                                    {*{/if}*}
                                    {*</td>*}
                                    <td class="text-center">{$i++}</td>
                                    {*<td class="text-center">1</td>*}

                                    <td class="text-center">{$visit->date}</td>
                                    <td class="text-center">{getname table=sh_user id=$visit->executor}</td>
                                    <td class="text-center">
                                        {if $visit->data == AssistVisit::VISIT_NEW_DATA}
                                            {#p_add_new_beneficiaries#}
                                        {else}
                                            {#p_update_beneficiaries#}
                                        {/if}
                                    </td>
                                    <td class="text-center">{getname table=sh_user id=$visit->created_by}</td>
                                    <td class="text-center">
                                        {if $visit->requestEntity->wf_request_created_by eq $smarty.session.user->id}
                                            {workflow requestId=$visit->requestEntity->wf_request_id backTo="ben/P269/fieldVisitRequests/show/0/{$smarty.session.lang}"}
                                        {/if}
                                    </td>

                                    <td class="text-center">
                                        {if !$visit->requestEntity->wf_request_success eq 1}
                                            {if $visit->requestEntity->wf_request_created_by eq $smarty.session.user->id}
                                                {url check=1 urltype="newattach" url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/fieldVisitRequests/assist_visit/{$visit->id}/{$smarty.session.user->id}/ben/P269/fieldVisitRequests/show/0/{$smarty.session.lang}"}
                                                {url check=0 urltype="medit" url_string="ben/P269/fieldVisitRequests/edit/0/{$smarty.session.lang}/{$visit->id}"}
                                            {/if}
                                            {if $visit->requestEntity->wf_request_created_by eq $smarty.session.user->id}
                                                {url check=0 urltype="mdelete" url_string="ben/P269/fieldVisitRequests/confirm/0/{$smarty.session.lang}/{$visit->id}"}
                                            {/if}
                                        {/if}
                                    </td>
                                </tr>
                            {/foreach}
                        </tbody>
                    </table>
                </div>
                <div class="tab-pane {if $smarty.session.s_active_horizontal_tab eq 'myRequests'}in active{/if}">
                    <table class="table table-striped table-bordered table-hover no-footer">
                        <thead>
                            <tr>
                                <th style="background-color: #A0D468 !important;" width="5%"></th>
                                <th style="background-color: #A0D468 !important;" width="10%">{#p_date#}</th>
                                <th style="background-color: #A0D468 !important;" width="15%">{#p_period_day#}
                                <th style="background-color: #A0D468 !important;" width="15%">{#p_created_by#}</th>
                                <th style="background-color: #A0D468 !important;" width="10%">{#p_visit_type#}</th>
                                <th style="background-color: #A0D468 !important;" width="20%">{#p_procedure#}</th>
                                <th style="background-color: #A0D468 !important;" width="10%">{#p_status#}</th>
                                <th style="background-color: #A0D468 !important;" width="10%">{#p_visit_details#}</th>
                            </tr>
                        </thead>
                        {$i=1}
                        <tbody>
                        {foreach $myVisits as $visit}
                            {if $visit->requestEntity->wf_request_success eq 1}
                                <tr>
                                {*<td class="text-center">{$i++}</td>*}
                                {*<td class="text-center">{getname table=sh_user id=$visit->familyObject->husband_id}</td>*}
                                {*<td class="text-center">{$visit->familyObject->executor}</td>*}
                                {*<td class="text-center">{getdate table=assist_visit col=date type=show row=$visit}</td>*}
                                {*<td class="text-center">*}
                                {*{getname table=st_setting id=$visit->type}*}
                                {*{if $visit->status eq 1051}*}
                                {*<span class="badge badge-warning">{#p_not_executed#}</span>*}
                                {*{elseif $visit->status eq 1052}*}
                                {*<span class="badge badge-info">{#p_under_execution#}</span>*}
                                {*{else}*}
                                {*<span class="badge badge-success">{#p_executed#}</span>*}
                                {*{/if}*}
                                {*</td>*}
                                {*<td class="text-center">*}
                                {*{if $visit->executor eq $smarty.session.user->id}*}
                                {*{url urltype="button" check=0 url_string="ben/P269/fieldVisits/details/0/{$smarty.session.lang}/visitData/{$visit->id}" text_value="{#p_visit_details#}" style="btn btn-default shiny"}*}
                                {*{else}*}
                                {*{url urltype="mbutton" check=0 url_string="gnr/X000/mediacenter/visitDetails/0/{$smarty.session.lang}/{$visit->id}" text_value="{#p_visit_details#}" style="btn btn-default shiny"}*}
                                {*{/if}*}
                                {*</td>*}
                                {*<td class="text-center">*}
                                {*{if $visit->executor eq $smarty.session.user->id}*}
                                {*{url check=0 urltype="medit" url_string="ben/P269/fieldVisits/edit/0/{$smarty.session.lang}/{$visit->id}"}*}
                                {*{url check=0 urltype="mdelete" url_string="ben/P269/fieldVisits/confirm/0/{$smarty.session.lang}/{$visit->id}"}*}
                                {*{/if}*}
                                {*</td>*}
                                <td class="text-center">{$i++}</td>
                                <td class="text-center">{$visit->date}</td>
                                <td class="text-center">{$visit->period}</td>
                                <td class="text-center">{getname table=sh_user id=$visit->created_by}</td>
                                <td class="text-center">
                                    {if $visit->data == AssistVisit::VISIT_NEW_DATA}
                                        {#p_add_new_beneficiaries#}
                                    {else}
                                        {#p_update_beneficiaries#}
                                    {/if}
                                </td>

                                    <td class="text-center">
                                        {if $visit->requestEntity->wf_request_created_by eq $smarty.session.user->id}
                                            {workflow requestId=$visit->requestEntity->wf_request_id backTo="ben/P269/fieldVisitRequests/show/0/{$smarty.session.lang}"}
                                        {/if}
                                    </td>
                                <td class="text-center">
                                    {if $visit->status eq 1051}
                                        <span class="badge badge-warning">{#p_not_executed#}</span>
                                        {elseif $visit->status eq 1052}
                                            <span class="badge badge-info">{#p_under_execution#}</span>
                                        {else}
                                            <span class="badge badge-success">{#p_executed#}</span>
                                    {/if}
                                </td>

                                <td class="text-center">
                                    {if $visit->executor eq $smarty.session.user->id}
                                        {url check=0 urltype="new" opr_code='fieldVisits' url_string="ben/P269/fieldVisits/details/0/{$smarty.session.lang}/visitData/{$visit->id}" text_value="<i class='fa fa-file-o'></i>"}
                                        {if $visit->data == AssistVisit::VISIT_NEW_DATA}
                                            {url check=0 urltype="medit" url_string="ben/P269/fieldVisitRequests/edit_my_request/0/{$smarty.session.lang}/{$visit->id}"}
                                        {/if}
                                    {else}
                                        {url urltype="new" check=0 url_string="gnr/X000/mediacenter/visitDetails/0/{$smarty.session.lang}/{$visit->id}" text_value="{#p_visit_details#}" style="btn btn-default shiny"}
                                    {/if}

                                    {*{if $visit->executor eq $smarty.session.user->id}*}
                                        {*{url check=0 urltype="new" opr_code='fieldVisits' url_string="ben/P269/fieldVisits/details/0/{$smarty.session.lang}/visitData/{$visit->id}" text_value="<i class='fa fa-file-o'></i>"}*}
                                        {*remove if (family id == null)*}{*ben/P269/fieldVisits/detail*}
                                        {*{try to go to edit in field visit}*}
                                        {*{if $visit->data == AssistVisit::VISIT_NEW_DATA}*}
                                            {*{url check=0 urltype="madd" url_string="ben/P269/fieldVisitRequests/add_my_request/0/{$smarty.session.lang}/{$visit->id}"}*}
                                            {*{url check=0 urltype="medit" url_string="ben/P269/fieldVisits/edit/0/{$smarty.session.lang}/{$visit->id}"}*}
                                        {*{/if}*}
                                    {*{else}*}
                                        {*{url urltype="new" check=0 url_string="gnr/X000/mediacenter/visitDetails/0/{$smarty.session.lang}/{$visit->id}" text_value="{#p_visit_details#}" style="btn btn-default shiny"}*}
                                    {*{/if}*}

                                </td>
                            </tr>
                            {/if}
                        {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {*{/if}*}

    {else}
        {GraphNotPreparedCorrectlly oprCode='fieldVisitRequests'}
    {/if}
{/block}