{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_visit#}</h4>
    </div>
    <div class="modal-body">
        <form method="post" action='{url urltype="path" url_string="ben/P269/fieldVisitRequests/show/0/{$smarty.session.lang}/update/{$smarty.session.s_FieldVisitRequests_token}/{$visit->id}"}'>
            <div x-data="VisitCreate('{(int)$visit->data}')" class="row snsowraper">
                <div class="col-lg-12">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visitor#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="user_id">
                            {foreach $researchers as  $researcher}
                                <option value="{$researcher->user_id}" {if $researcher->user_id eq $visit->executor}selected{/if}>{getname table=sh_user id=$researcher->user_id}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_type#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <div id = "radio" class="radio">
                                <label>
                                    <input name="data" type="radio" x-model="type" value="{AssistVisit::VISIT_NEW_DATA}">
                                    <span class="text">{#p_add_new_beneficiaries#}</span>
                                </label>
                                <br>
                                <label id = "update_visitor">
                                    <input name="data" type="radio" x-model="type" value="{AssistVisit::VISIT_UPDATE_DATA}">
                                    <span class="text">{#p_update_beneficiaries#}</span>
                                </label>
                            </div>
                    </div>
                    <template x-if="parseInt(type) == 0">
                        <div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_choose_location#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <select name="neighborhood_id">
                                    {*{if $visit->family_id eq null}*}
                                        {*{getname table=st_neighborhood id=$neighborhood}*}
                                    {*{/if}*}
                                    {*{if $neighborhood->id eq $user->neighborhood}{$neighborhood->region->name}&nbsp;&raquo;&nbsp;{$neighborhood->city->name}&nbsp;&raquo;&nbsp;{$neighborhood->name}{/if}*}
                                    {foreach $neighborhoods as $neighborhood}
                                        <option value="{$neighborhood->id}" {if $neighborhood->id eq $user->neighborhood}selected{/if}>{$neighborhood->name}</option>
                                    {/foreach}
                                </select>
                                {*<select name="neighborhood_id">*}
                                    {*<option value="{$user->nationality}"*}
                                            {*selected>{getname table=st_country id=$user->nationality}</option>*}
                                    {*{foreach $neighborhoods as $neighborhood}*}
                                        {*<option value="{$neighborhood->id}">{$neighborhood->region->name}&nbsp;&raquo;&nbsp;{$neighborhood->city->name}&nbsp;&raquo;&nbsp;{$neighborhood->name}</option>*}
                                    {*{/foreach}*}
                                {*</select>*}
                            </div>
                        </div>
                    </template>

                    <template x-if="parseInt(type) == 1">
                        <div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <select name="family_id" id="">
                                    <option value="">{#p_choose_family#}</option>
                                    {foreach $families as $family}
                                        <option value="{$family->id}" {if $family->id eq $visit->family_id}selected{/if}> {getname table=sh_user id=$family->head_of_the_family}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                    </template>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_date#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div name = "date" class="control-group" >
                            {getdate table=assist_visit col=date type=edit row=$visit }
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_period#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div>
                            <input name = "period" type = "text" style = "width: 10%" value="{$visit->period}">
                            {#p_day#}
                        </div>
                    </div>

                    <div class="row col-lg-12">
                        <div class=" col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_additional_information#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                <textarea name="recommendations" class="form-control" id="" >{$visit->recommendations}</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-success sharp">{#gnr_update#}</button>
                    </div>
                </div>

            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
{block name=footer}
    <script src="/templates/assets/resources/js/alpine/components/AssistVisitForm.js"></script>
{/block}
