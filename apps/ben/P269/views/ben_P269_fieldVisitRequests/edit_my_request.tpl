{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_visit_data#}</h4>
    </div>
    <div class="modal-body">
        {assign var="doNotUseAppJs" value=1}
        <form method="post"
              action='{url urltype="path" url_string="ben/P269/fieldVisitRequests/show/0/{$smarty.session.lang}/update/{$smarty.session.s_FieldVisitRequests_token}/{$visit->id}"}'>
            <di class="row snsowraper">
            {*<div class="row snsowraper">*}
                {*<div class="mt-3 row col-lg-12">*}
                    {*<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_type#}</div>*}
                    {*<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">*}
                        {*<div class="radio">*}
                            {*<label>*}
                                {*<input name="data" type="radio" x-model="type" value="{AssistVisit::VISIT_NEW_DATA}">*}
                                {*<span class="text">{#p_add_new_beneficiaries#}</span>*}
                            {*</label>*}
                            {*<br>*}

                            {*<label>*}
                                {*<input name="data" type="radio" x-model="type" value="{AssistVisit::VISIT_UPDATE_DATA}">*}
                                {*<span class="text">{#p_update_beneficiaries#}</span>*}
                            {*</label>*}
                        {*</div>*}
                    {*</div>*}
                {*</div>*}

                {*<template x-if="parseInt(type) == 0">*}
                    <div>
                        <div class=" row col-lg-12">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_first_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                class="form-control"
                                                                                                name="fr_name"
                                                                                                value="{$user->fr_name}">
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_father_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                class="form-control"
                                                                                                name="secd_name"
                                                                                                value = "{$user->secd_name}">
                            </div>
                        </div>
                        <div class="row col-lg-12">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_grandfather_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                class="form-control"
                                                                                                name="thrd_name"
                                                                                                value="{$user->thrd_name}">
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                class="form-control"
                                                                                                name="fm_name"
                                                                                                value="{$user->fm_name}">
                            </div>

                        </div>
                        <div class=" row col-lg-12" >
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_marital_status#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                <select name="social_status">
                                    {*<option value="{$user->social_status}"*}
                                            {*selected>{getname table=st_setting id=$user->social_status}</option>*}
                                    {foreach $social_list as  $social}
                                        <option value="{$social->id}" {if $social->id eq $user->social_status}selected{/if}>{$social->translatedName}</option>
                                    {/foreach}
                                </select>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_nationality#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                <select name="nationality">
                                    {*<option value="{$user->nationality}"*}
                                            {*selected>{getname table=st_country id=$user->nationality}</option>*}
                                    {foreach $nationality_list as $nationality}
                                        <option value="{$nationality->id}" {if $nationality->id eq $user->nationality}selected{/if}>{$nationality->translatedName}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="row col-lg-12" >
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_job_status#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                <select name="work_status">
                                    {*<option value="{$user->work_status}"*}
                                            {*selected>{getname table=st_setting id=$user->work_status}</option>*}
                                    {foreach $work_status_list as  $work}
                                        <option value="{$work->id}" {if $work->id eq $user->work_status}selected{/if}>{$work->translatedName}</option>
                                    {/foreach}
                                </select>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_location#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                <select name="neighborhood_id">
                                    <option value="{$neighborhood->id}" {if ($neighborhood->id eq $user->address_neighborhood) or ($neighborhood->id eq $user->neighborhood_id)}selected{/if}>{$neighborhood->region->name}&nbsp;&raquo;&nbsp;{$neighborhood->city->name}&nbsp;&raquo;&nbsp;{$neighborhood->name}</option>
                                    {foreach $neighborhoods as $neighborhood}
                                        <option value="{$neighborhood->id}">{$neighborhood->region->name}&nbsp;&raquo;&nbsp;{$neighborhood->city->name}&nbsp;&raquo; {$neighborhood->name}</option>
                                    {/foreach}

                                </select>
                            </div>
                        </div>
                {*</template>*}


                <div class="row col-lg-12">
                    {*<template x-if="parseInt(type) == 1">*}
                        {*<div>*}
                            {*<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family#}</div>*}
                            {*<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">*}
                                {*<select name="family_id">*}
                                    {*<option>{#p_choose_family#}</option>*}
                                    {*{foreach $families as $family}*}
                                        {*<option value="{$family->id}">{getname table=sh_user id=$family->head_of_the_family}</option>*}
                                    {*{/foreach}*}
                                {*</select>*}
                            {*</div>*}
                        {*</div>*}
                    {*</template>*}
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_date#}</div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {getdate table=assist_visit col=date type=add row=$visit}
                        </div>
                    </div>
                </div>


                <div class="row col-lg-12">
                    <div class=" col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_recommendations#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <textarea name="recommendations" class="form-control" id="" cols="30" rows="5">{$visit->recommendations}</textarea>
                        </div>
                    </div>
                </div>
                <div class="row col-lg-12">
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_comments#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <textarea name="comment" class="form-control" id="" cols="30" rows="5">{$visit->comment}</textarea>
                        </div>
                    </div>
                </div>
                <div class="mb-3 row col-lg-12">
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-success sharp">{#gnr_update#}</button>
                    </div>
                </div>
            </div>
            </di>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
{*{block name=footer}*}
    {*<script src="/templates/assets/resources/js/alpine/components/AssistVisitForm.js"></script>*}
{*{/block}*}
