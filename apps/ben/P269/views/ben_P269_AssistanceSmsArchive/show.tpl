{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}

    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-list-alt blue"></i>
        أرشيف الرسائل القصيره الخاصه بالماساعدات
    </h5>
    <div class="widget collapsed">
        <div class="widget-header bg-blue">
            <i class="widget-icon fa fa-arrow-left"></i>
            <span class="widget-caption">{#gnr_search#}</span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                    <i class="fa fa-minus"></i>
                </a>
            </div><!--Widget Buttons-->
        </div><!--Widget Header-->
        <div class="widget-body">

            <form action="{url urltype=path url_string="ben/P269/AssistanceSmsArchive/show/0/{$smarty.session.lang}/filter"}"
                  method="post">
                <div class="row">
                    <div class="col-lg-6">

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_from#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                {getdate table=sms_log col=from_date type=report row={$smarty.session.filter_sms['from_date']|default:''}}
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_to#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            {getdate table=sms_log col=to_date type=report row={$smarty.session.filter_sms['to_date']|default:''}}
                        </div>

                    </div>

                    <div class="col-lg-2">
                        <div class="checkbox">

                        </div>
                    </div>

                    <div class="col-lg-2">
                        <div class="control-group">

                        </div>
                    </div>

                    <div class="col-lg-2">
                        <button type="submit" class="btn btn-default shiny">{#gnr_search#}</button>
                    </div>
                </div>
            </form>

            <br>
        </div><!--Widget Body-->
    </div>

    {if $smarty.session.filter_sms}
        <div class="well text-center">
            {url urltype=alink url_string="ben/P269/AssistanceSmsArchive/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#}"}
        </div>
    {/if}

        <table class="table table-striped table-bordered table-hover no-footer">
            <thead>
            <tr>
                <th width="5%"></th>
                <th width="15%">{#p_sms_receiver_name#}</th>
                <th width="10%">{#p_sms_phone_number#}</th>
                <th width="50%">{#p_sms_message_text#}</th>
                <th width="10%">{#p_sms_sending_time#}</th>
                <th width="10%">{#p_sms_message_status#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=$smarty.session.s_startpage + 1}
            {foreach $messages as $message}
                <tr>
                    <td class="text-center">{$i++}</td>
                    <td class="text-center">{getname table=sh_user id=$message->sent_by}</td>
                    <td class="text-center">{$message->phone_number}</td>
                    <td>{$message->message}</td>
                    <td class="text-center">
                        {getdate table=sms_log col=created_date type=show row=$message}
                    </td>
                    <td class="text-center">
                        <span class="badge badge-warning">غير محدد</span>
                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>

    <div class="text-center margin-top-20">
        {paginate startpage=$smarty.session.s_startpage lastpage=$smarty.session.s_lastpage}
    </div>

{/block}