{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}
    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
        <thead>
        <tr>
            <th style="background-color: #A0D468 !important;" width="5%">{url check=1 opr_code="assistTypes" urltype=madd url_string="ben/P269/assistTypes/add/0/{$smarty.session.lang}" modal=modal}</th>
            <th style="background-color: #A0D468 !important;" width="35%">{#gnr_name#}</th>
            <th style="background-color: #A0D468 !important;" width="35%">{#gnr_type#}</th>
            <th style="background-color: #A0D468 !important;" width="20%">{#gnr_settings#}</th>
        </tr>
        </thead>
        {$i=1}
        <tbody>
        {foreach $types as $type}
            <tr>
                <td align="center">{$i++}</td>
                <td align="right">{$type->name}</td>
                <td align="right">{t v=$type->class_id}</td>
                <td class="text-center">
                    {if $type->usage eq 0}
                        {url check=1 opr_code="assistTypes" urltype=medit url_string="ben/P269/assistTypes/edit/0/{$smarty.session.lang}/{$type->id}" modal=modal}
                        {url check=1 opr_code="assistTypes" urltype=mdelete url_string="ben/P269/assistTypes/confirm/0/{$smarty.session.lang}/{$type->id}" modal=modal}
                    {else}
                        <span style="color: green">{#p_type_has_been_used#}</span>
                    {/if}
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
{/block}