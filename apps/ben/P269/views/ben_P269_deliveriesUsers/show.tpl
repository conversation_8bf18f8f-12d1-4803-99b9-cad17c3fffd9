{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}
    <table  class="table table-striped table-bordered table-hover dataTable no-footer sortable-table" >
        <div>
            <h4 class="alert alert-info" style="width: 17%; background-color: white; color: grey;">المسلمون</h4>
        </div>

        <thead>
        <tr>
            <th style="background-color: #A0D468 !important;" width="5%">{url check=1 opr_code="deliveriesUsers" urltype=madd url_string="ben/P269/deliveriesUsers/add/0/{$smarty.session.lang}" modal=modal}</th>
            <th style="background-color: #A0D468 !important;" width="70%">{#gnr_name#}</th>
            <th style="background-color: #A0D468 !important;" width="25%">{#gnr_settings#}</th>
        </tr>
        </thead>
        {$i=1}
        <tbody>
        {foreach $distributors as $distributor}
            <tr>
                <td align="center">{$i++}</td>
                <td align="right">{$distributor->userObject->full_name}</td>
                <td class="text-center">
                    {if $distributor->usage eq 0}
                        {url check=1 opr_code="deliveriesUsers" urltype=mdelete url_string="ben/P269/deliveriesUsers/confirm/0/{$smarty.session.lang}/{$distributor->id}" modal=modal}
                    {/if}
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>

    <br>
    <br>
    <br>
    <br>
    <br>
    <br>
    <div>
        <h4 class="alert alert-info" style="width: 17%; background-color: white; color: grey;">الباحثون الاجتماعيون</h4>
    </div>

    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
        <thead>
        <tr>
            <th style="background-color: #A0D468 !important;" width="5%">{url check=1 opr_code="deliveriesUsers" urltype=madd url_string="ben/P269/deliveriesUsers/add_researcher/0/{$smarty.session.lang}" modal=modal}</th>
            <th style="background-color: #A0D468 !important;" width="80%">{#gnr_name#}</th>
            <th style="background-color: #A0D468 !important;" width="15%">{#gnr_settings#}</th>
        </tr>
        </thead>
        {$i=1}
        <tbody>
        {foreach $researchers as $researcher}
            <tr>
                <td align="center">{$i++}</td>
                <td align="right">{$researcher->userObject->full_name}</td>
                <td class="text-center">
                    {if $researcher->usage eq 0}
                        {url check=1 opr_code="deliveriesUsers" urltype=mdelete url_string="ben/P269/deliveriesUsers/confirm_researcher/0/{$smarty.session.lang}/{$researcher->id}" modal=modal}
                    {/if}
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
{/block}