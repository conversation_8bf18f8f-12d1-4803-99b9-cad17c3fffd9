{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name="header"}
    <script type="text/javascript">
        $(document).ready(function () {

            $("#rentDiv").css("display", "none");

            $("#367").click(function () {
                if ($("#367").is(":checked")) {
                    $("#rentDiv").hide('fast');
                }
            });

            $("#368").click(function () {
                if ($("#368").is(":checked")) {
                    $("#rentDiv").show('fast');
                }
            });

        });
    </script>
{/block}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add_row#}</h4>
    </div>
    <div class="modal-body">
    <div class="row">
        <form method="post"
              action='{url urltype="path"
              url_string="ben/P269/AssistanceFamiliesManagement/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_AssistanceFamilyManagement_token}/{$user->id}"}'>
            <div class="col-lg-12">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_husband_name#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {if $user->gender eq Setting::FEMALE}
                        <select name="husband_id" required="required">
                            <option value="0" selected>{#gnr_select_from_menu#}</option>
                            {foreach $men as $man}
                                <option value="{$man->id}" {if $man->id eq $family->husband_id} selected {/if}>{$man->full_name}</option>
                            {/foreach}
                        </select>
                    {else}
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$user->full_name}</div>
                    {/if}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_wife_name#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {if $user->gender eq Setting::FEMALE }
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$user->full_name}</div>
                    {else}
                        <select name="wife_id" required="required">
                            <option value="" selected>{#gnr_select_from_menu#}</option>
                            {foreach $women as $woman}
                                <option value="{$woman->id}">{$woman->full_name}
                                    &nbsp;&raquo;&nbsp;{t v=$woman->social_status}</option>
                            {/foreach}
                        </select>
                    {/if}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_file_number#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control"
                                                                                    id="file_number" name="file_number"
                                                                                    placeholder="{#p_file_number#}"
                                                                                    required></div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_nationality#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="nationality_id" required="required">
                        <option value="" selected>{#gnr_select_from_menu#}</option>
                        {foreach $nationalities as $nationality}
                            <option value="{$nationality->st_country_id}" {if $nationality->st_country_id eq 56} selected {/if}>{$nationality->st_country_name}</option>
                        {/foreach}
                    </select>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_neighborhood#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="neighborhood_id" required="required">
                        <option value="" selected>{#gnr_select_from_menu#}</option>
                        {foreach $neighborhoods as $neighborhood}
                            <option value="{$neighborhood->st_neighborhood_id}">{$neighborhood->st_neighborhood_name}</option>
                        {/foreach}
                    </select>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_fload_number#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="floor_number" required="required">
                        {for $i=0 to 10}
                            <option value="{$i}">{$i}</option>
                        {/for}
                    </select>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_national_number#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" name="national_number"
                                                                                    placeholder="{#p_national_number#}"
                                                                                    class="form-control"></div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_housing_type#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <div class="control-group">
                        {foreach $housing_type_list as $key => $hrow}
                            <div class="radio">
                                <label>
                                    <input type="radio" id="{$hrow->id}" name="housing_type"
                                           value="{$hrow->id}" {if $key eq 0} checked {/if} required>
                                    <span class="text">{$hrow->translatedName}</span>
                                </label>
                            </div>
                        {/foreach}
                    </div>
                </div>

                <div id="rentDiv">
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_rent_value#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" id="housing_rent"
                                                                                        size="10" name="housing_rent"
                                                                                        value=""
                                                                                        placeholder="{#p_rent_value#}">
                        &nbsp; {#gnr_per_year#}</div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_health_status#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="health_status" required="required">
                        <option value="" selected>{#gnr_select_from_menu#}</option>
                        {foreach $healthStatus as $health}
                            <option value="{$health->id}" {if $health->id eq 1193} selected {/if}>{$health->translatedName}</option>
                        {/foreach}
                    </select>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}