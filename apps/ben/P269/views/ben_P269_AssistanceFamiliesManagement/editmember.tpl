{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_edit_row#}</h4>
    </div>
    <div class="modal-body">
    <div class="row">
        <form method="post"
              action='{url urltype="path"
              url_string="ben/P269/AssistanceFamiliesManagement/controlmembers/0/{$smarty.session.lang}/update/{$smarty.session.s_AssistanceFamilyManagement_token}/{$member->id}"}'>
            <div class="col-lg-12">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_member_name#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {if !in_array($member->relationship_id, [AssistanceFamilyMembers::FAMILY_MEMBER_TYPE_HUSBAND,AssistanceFamilyMembers::FAMILY_MEMBER_TYPE_WIFE])}
                        <select name="user_id" required="required">
                            <option value="0" selected>{#gnr_select_from_menu#}</option>
                            {foreach key=id item=etrow from=$members_list}
                                {if $etrow->type eq 0}
                                    <option value="{$etrow->id}" {if $etrow->id eq $member->user_id} selected {/if}>{$etrow->full_name}</option>
                                {/if}
                            {/foreach}
                        </select>
                    {else}
                        {$member->userObject->full_name}
                    {/if}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_realtionship#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {if !in_array($member->relationship_id, [AssistanceFamilyMembers::FAMILY_MEMBER_TYPE_HUSBAND,AssistanceFamilyMembers::FAMILY_MEMBER_TYPE_WIFE])}
                        <select name="relationship_id" required="required">
                            <option value="0" selected>{#gnr_select_from_menu#}</option>
                            {foreach key=id item=etrow from=$relationship_list}
                                <option value="{$etrow->id}" {if $etrow->id eq $member->relationship_id} selected {/if}>{$etrow->translatedName}</option>
                            {/foreach}
                        </select>
                    {else}
                        {t v=$member->relationship_id}
                    {/if}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_social_status#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <div class="control-group">
                        {foreach $social_list as $social}
                            {if $social->id neq 9}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="social_status" value="{$social->id}"
                                                {if $social->id eq $member->social_status} checked {/if}
                                               required>
                                        <span class="text">{$social->translatedName}</span>
                                    </label>
                                </div>
                            {else}
                                {if !in_array($member->relationship_id, [AssistanceFamilyMembers::FAMILY_MEMBER_TYPE_HUSBAND,AssistanceFamilyMembers::FAMILY_MEMBER_TYPE_WIFE])}
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="social_status" value="{$social->id}"
                                                    {if $social->id eq $member->social_status} checked {/if}
                                                   required>
                                            <span class="text">{$social->translatedName}</span>
                                        </label>
                                    </div>
                                {/if}
                            {/if}
                        {/foreach}
                    </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_accountable_in_calculation#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <div class="control-group">
                        {foreach $yesNo as $row}
                            <div class="radio">
                                <label>
                                    <input type="radio" name="accountable_in_income_calculation" value="{$row->id}"
                                           id="{$row->id}" {if $row->id eq $member->accountable_in_income_calculation} checked {/if}
                                           required>
                                    <span class="text">{$row->translatedName}</span>
                                </label>
                            </div>
                        {/foreach}
                    </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_calculation_percentage#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                    pattern="[0-9]+([.][0-9]+)?"
                                                                                    name="percentage_in_income_calculation"
                                                                                    class="form-control"
                                                                                    value="{$member->percentage_in_income_calculation}"
                                                                                    max="100"></div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}