{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}{$smarty.session.layout}.tpl"}
{block name=page_body}
    {if $smarty.session.fromUrl neq 'user_dashboard'}
        <div class="row">
            <div class="col-lg-12">
                <form action="{url urltype=path url_string="ben/P269/AssistanceFamiliesManagement/show/0/{$smarty.session.lang}/save_session"}" method="post">
                    <select name="userId" onchange="this.form.submit()" required {if empty($currentBeneficiary)} placeholder="{#gnr_unspecified#}" {else} placeholder="{$currentBeneficiary->full_name}&nbsp;&raquo;&nbsp;{$currentBeneficiary->tell}" {/if}>
                        <option></option>
                        {foreach $beneficiaries as $beneficiary}
                            <option value="{$beneficiary->id}">{$beneficiary->full_name}</option>
                        {/foreach}
                    </select>
                </form>
            </div>
        </div>
    {/if}

    {if $assistanceYear}

        {if $smarty.session.s_beneficiary_id}

            <h5 class="row-title before-blue">
                <i class="glyphicon glyphicon-list-alt blue"></i>
                {$assistanceYear->name}&nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
                &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
            </h5>

            <br>

            <h5 class="row-title before-blue">
                <i class="glyphicon glyphicon-user blue"></i>{$currentBeneficiary->full_name}&nbsp;&raquo;&nbsp;{getname table=st_setting id=$currentBeneficiary->gender}&nbsp;&raquo;&nbsp;{getname table=st_setting id=$currentBeneficiary->social_status}
                {if !$smarty.session.fromUrl eq 'user_dashboard'}
                    {url urltype="button" check=0 oprvtype=3 opr_code="employees" url_string="gnr/X000/resume/edit/0/{$smarty.session.lang}/save_session/{$currentBeneficiary->id}/ben/P269/AssistanceFamiliesManagement/show/0/{$smarty.session.lang}/{$currentBeneficiary->id}"  text_value="<i class='fa fa-file-text'></i>&nbsp;{#gnr_data#}"}
                {/if}
                </h5>

            <table class="table table-striped table-bordered table-hover no-footer">
                <thead>
                <tr>
                    <th width="10%">
                        {url check=0 urltype="madd" url_string="ben/P269/AssistanceFamiliesManagement/add/0/{$smarty.session.lang}/{$currentBeneficiary->id}"}
                    </th>
                    <th style="background-color: #A0D468 !important;" width="10%">{#p_file_number#}</th>
                    <th style="background-color: #A0D468 !important;" width="20%">{#p_husband_name#}</th>
                    <th style="background-color: #A0D468 !important;" width="20%">{#p_wife_name#}</th>
                    <th style="background-color: #A0D468 !important;" width="10%">{#p_family_members_count#}</th>
                    <th style="background-color: #A0D468 !important;" width="20%">{#p_members#}</th>
                    <th width="10%">{#gnr_settings#}</th>
                </tr>
                </thead>
                {$i=1}
                <tbody>
                {foreach $families as $family}
                    <tr>
                        <td class="text-center">{$i++}</td>
                        <td class="text-center">{$family->file_number}</td>
                        <td class="text-center">{getname table=sh_user id=$family->husband_id}</td>
                        <td class="text-center">{getname table=sh_user id=$family->wife_id}</td>
                        <td class="text-center">{$family->family_member_number}</td>
                        <td class="text-center">
                        {if $smarty.session.fromUrl eq 'user_dashboard'}
                            {url urltype="button" check=0 url_string="ben/P269/AssistanceFamiliesManagement/controlmembers/0/{$smarty.session.lang}/save_session/{$family->id}/user_dashboard" text_value="أفراد الأسرة" style="btn btn-default shiny"}
                        {else}
                            {url urltype="button" check=0 url_string="ben/P269/AssistanceFamiliesManagement/controlmembers/0/{$smarty.session.lang}/save_session/{$family->id}" text_value="أفراد الأسرة" style="btn btn-default shiny"}
                        {/if}
                        </td>
                        <td class="text-center">
                            {url check=0 urltype="medit" url_string="ben/P269/AssistanceFamiliesManagement/edit/0/{$smarty.session.lang}/{$family->id}"}
                            {if $family->membersCountOtherThanHusbandAndMother eq 0}
                                {url check=0 urltype="mdelete" url_string="ben/P269/AssistanceFamiliesManagement/confirm/0/{$smarty.session.lang}/{$family->id}"}
                            {/if}
                        </td>
                    </tr>
                {/foreach}
                </tbody>
            </table>
        {else}

            <div class="alert alert-warning">
                {#p_select_beneficiary_from_above_menu#}
            </div>
        {/if}

    {else}
        <span style="color: red">{#p_you_should_activate_assistance_year_first#}</span>
    {/if}

{/block}
{block name=back}{url urltype="path" url_string="{$smarty.session.backUrl}"}{/block}
