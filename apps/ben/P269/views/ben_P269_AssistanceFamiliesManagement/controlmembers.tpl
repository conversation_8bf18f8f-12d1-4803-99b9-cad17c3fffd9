{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}

{block name=page_body}

    <div class="well">
        <h4 class="block">
            {if $family->head_of_the_family}
                {getname table=sh_user id=$family->head_of_the_family}&nbsp;
            {else}
                {getname table=sh_user id=$family->husband_id}&nbsp;
            {/if}
        <hr></h4>
        
        <div class="row">
            <div class="col-lg-10"></div>
            <div class="col-lg-2">
                {url check=0 urltype="button" url_string="gnr/X000/documents/show/0/{$smarty.session.lang}/save_session/AssistanceFamiliesManagement/controlmembers/{$family->id}/{$smarty.session.lang}/ben/P269/AssistanceFamiliesManagement/controlmembers/0/{$smarty.session.lang}/{$family->id}" text_value="{#gnr_attachments#}"}
            </div>
        </div>
        
        <div class="horizontal-space"></div>
    
        <div class="row">
            <div class="col-lg-12">
                <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_file_number#}</div>
                <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$family->file_number}</div>

                <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_nationality#}</div>
                <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_country id=$family->nationality_id}</div>

                <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_neighborhood#}</div>
                <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                    {getname table=st_region id=$family->region_id}
                    &nbsp;&raquo;&nbsp;
                    {getname table=st_city id=$family->city_id}
                    &nbsp;&raquo;&nbsp;
                    {getname table=st_neighborhood id=$family->neighborhood_id}
                    &nbsp;
                    [&nbsp;{#p_fload_number#}&nbsp;{$family->floor_number}&nbsp;|&nbsp;{#p_national_number#}&nbsp;{$family->national_number}]
                </div>

                <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_housing_type#}</div>
                <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                    {t v=$family->housing_type}
                    {if $family->housing_type eq 368}
                        &nbsp;[&nbsp;{#p_rent_value#}&nbsp;{$family->housing_rent}]
                    {/if}
                </div>

                <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family_member_number#}</div>
                <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$family->family_member_number}</div>

                <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_total_family_income#}</div>
                <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$family->total_family_income}</div>

                <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_average_family_income#}</div>
                <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$family->average_family_income}</div>

                <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_head_of_the_family#}</div>
                <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$head_of_the_family->full_name}</div>

                <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_transfere_on_user_account#}</div>
                <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                    {$transfere_on_user_account->full_name}
                    &nbsp;&raquo;&nbsp;
                    {$transfere_on_user_account->bank_number}
                </div>


            </div>
        </div>
    </div>

    <div class="row">

    </div>

    <div class="tabbable">
        <ul class="nav nav-tabs nav-justified" id="myTab5">
            <li class="active">
                <a data-toggle="tab" href="#members">
                    {#p_members_manager#}
                </a>
            </li>

            <li class="tab-red">
                <a data-toggle="tab" href="#income">
                    {#p_incomde_data#}
                </a>
            </li>

            <li class="tab-warning">
                <a data-toggle="tab" href="#visits">
                    {#p_visits#}
                </a>
            </li>

            <li class="tab-green">
                <a data-toggle="tab" href="#cards">
                    {#p_cards#}
                </a>
            </li>
        </ul>

        <div class="tab-content">
            <div id="members" class="tab-pane in active">
                <div class="table-responsive snsoinput" data-pattern="priority-columns">

                    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                        <thead>
                        <tr>
                            <th width="5%" style="background-color: #A0D468 !important;"></th>
                            <th width="30%" style="background-color: #A0D468 !important;">{#p_member_name#}</th>
                            <th width="10%" style="background-color: #A0D468 !important;">{#p_realtionship#}</th>
                            <th width="15%" style="background-color: #A0D468 !important;">{#p_birth_date#}</th>
                            <th width="15%" style="background-color: #A0D468 !important;">{#p_accountable_in_calculation#}</th>
                            <th width="15%" style="background-color: #A0D468 !important;">{#p_calculation_percentage#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {$i=1}
                        {foreach $familyMembers as $member}
                            <tr>
                                <td align="center">{$i++}</td>
                                <td>
                                    {if !$smarty.session.fromUrl eq 'user_dashboard'}
                                        {url urltype="button" check=0 oprvtype=3 opr_code="employees" url_string="gnr/X000/resume/edit/0/{$smarty.session.lang}/save_session/{$member->userObject->id}/ben/P269/AssistanceFamiliesManagement/controlmembers/0/{$smarty.session.lang}"  text_value="<i class='fa fa-file-text'></i>&nbsp;{#gnr_data#}"}
                                    {/if}
                                    {if $member->userObject->live_status eq Setting::USER_IS_DEAD}
                                        <span style="color: grey">{$member->userObject->full_name}</span>
                                    {else}
                                        <span style="color: green">{$member->userObject->full_name}</span>
                                    {/if}

                                    {if $member->familyMembershipNumber gte 2}
                                        {url urltype="mbutton" check=0 url_string="ben/P269/AssistanceFamiliesManagement/browseMemberFamilies/0/{$smarty.session.lang}/{$member->user_id}" text_value="{$member->familyMembershipNumber}&nbsp;{#p_family#}"}
                                    {/if}
                                </td>
                                <td align="center">{t v=$member->relationship_id}&nbsp;[&nbsp;{t v=$member->social_status}&nbsp;]</td>
                                <td align="center">{$member->userObject->birth_date}&nbsp;&raquo;&nbsp;{$member->userObject->age}&nbsp;{#gnr_year#}</td>
                                <td align="center">{$member->accountable_in_income_calculation}</td>
                                <td align="center">{$member->percentage_in_income_calculation} %</td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>

                </div>
            </div>
            <div id="income" class="tab-pane">
                {if $family->members}
                    <div class="table-responsive snsoinput" data-pattern="priority-columns" >

                        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                            <thead>
                            <tr>
                                <th width="5%" style="background-color: #A0D468 !important;"></th>
                                <th width="23%" style="background-color: #A0D468 !important;">{#p_name#}</th>
                                <th width="12%" style="background-color: #A0D468 !important;">{#p_realtionship#}</th>
                                <th width="12%" style="background-color: #A0D468 !important;">{#p_income#}</th>
                                <th width="12%" style="background-color: #A0D468 !important;">{#p_accountable_in_calculation#}</th>
                                <th width="12%" style="background-color: #A0D468 !important;">{#p_percentage#}</th>
                            </tr>
                            </thead>
                            <tbody>
                            {$i=1}
                            {foreach $family->members as $data}
                                <tr>
                                    <td align="center">{$i++}</td>
                                    <td>
                                        <a data-toggle="modal" data-target="#modal" href="{url check=0 urltype="path" url_string="gnr/X000/resume/show/0/{$smarty.session.lang}/{$data->user_id}"}" class="btn btn-default btn-sm">
                                            <i class='fa fa-user'></i>
                                        </a>
                                        &nbsp;
                                        {url check=0 urltype="mbutton" url_string="gnr/X000/mediacenter/userIncome/0/{$smarty.session.lang}/{$data->user_id}" text_value="{#p_user_income#}"}
                                        &nbsp;
                                        {getname table=sh_user id=$data->user_id}
                                    </td>
                                    <td align="center">{t v=$data->relationship_id}</td>
                                    <td align="center">{$data->income}</td>
                                    <td align="center">{$data->accountable_in_income_calculation}</td>
                                    <td align="center">{$data->percentage_in_income_calculation} %</td>
                                </tr>
                            {/foreach}
                            </tbody>
                        </table>

                    </div>
                {else}
                    <div class="alert alert-warning">{#p_sorry_there_is_no_members_in_this_family#}</div>
                {/if}
            </div>
            <div id="visits" class="tab-pane">
                <div class="row">
                    <div class="col-lg-4">
                        {if !($smarty.session.fromUrl eq 'user_dashboard')}
                            {url urltype="mbutton" check=0 url_string="ben/P269/AssistanceFamiliesManagement/addVisit/0/{$smarty.session.lang}" text_value="{#p_add_new_visit#}" style="btn btn-default shiny"}
                            {url urltype="mbutton" check=0 url_string="ben/P269/AssistanceFamiliesManagement/addVisitRequest/0/{$smarty.session.lang}" text_value="{#p_add_visit_request#}" style="btn btn-default shiny"}
                        {/if}
                    </div>
                </div>
                <br>
                <table class="table table-striped table-bordered table-hover no-footer">
                    <thead>
                    <tr>
                        <th width="10%"></th>
                        <th style="background-color: #A0D468 !important;" width="30%">{#p_executor#}</th>
                        <th style="background-color: #A0D468 !important;" width="30%">{#gnr_created_by#}</th>
                        <th style="background-color: #A0D468 !important;" width="10%">{#p_visit_date#}</th>
                        <th style="background-color: #A0D468 !important;" width="20%">{#p_visit_detials#}</th>
                    </tr>
                    </thead>
                    {$i=1}
                    <tbody>
                    {foreach $visits as $visit}
                        <tr>
                            <td class="text-center">{$i++}</td>
                            <td class="text-center">{getname table=sh_user id=$visit->executor}</td>
                            <td class="text-center">{getname table=sh_user id=$visit->created_by}</td>
                            <td class="text-center">{getdate table=assist_visit col=date type=show row=$visit}</td>
                            <td class="text-center">
                                {url urltype="mbutton" check=0 url_string="gnr/X000/mediacenter/visitDetails/0/{$smarty.session.lang}/{$visit->id}" text_value="تفاصيل الزيارة" style="btn btn-default shiny"}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>

            <div id="cards" class="tab-pane">
                <table class="table table-striped table-bordered table-hover no-footer">
                    <thead>
                    <tr>
                        <th width="10%">{url urltype="madd" check=0 url_string="ben/P269/AssistanceFamiliesManagement/cardAdd/0/{$smarty.session.lang}/{$family->id}" text_value="{#gnr_add#}" style="btn btn-default shiny"}</th>
                        <th style="background-color: #A0D468 !important;" width="20%">{#p_card_type#}</th>
                        <th style="background-color: #A0D468 !important;" width="10%">{#p_delivery_date#}</th>
                        <th style="background-color: #A0D468 !important;" width="50%">{#p_comment#}</th>
                        <th style="background-color: #A0D468 !important;" width="10%">{#gnr_settings#}</th>
                    </tr>
                    </thead>
                    {$i=1}
                    <tbody>
                    {foreach $familyCards as $card}
                        <tr>
                            <td class="text-center">{$i++}</td>
                            <td class="text-center">{getname table=assist_card id=$card->card_id}</td>
                            <td class="text-center">{getdate table=assist_family_card col=delivery_date type=show row=$card}</td>
                            <td class="text-center">{$card->comment}</td>
                            <td class="text-center">
                                {url check=1 opr_code="assistCards" urltype=medit url_string="ben/P269/AssistanceFamiliesManagement/cardEdit/0/{$smarty.session.lang}/{$card->id}" modal=modal}
                                {url check=1 opr_code="assistCards" urltype=mdelete url_string="ben/P269/AssistanceFamiliesManagement/cardConfirm/0/{$smarty.session.lang}/{$card->id}" modal=modal}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>

        </div>
    </div>

{/block}
{block name=back}{url urltype="path" url_string="ben/P269/BeneficiariesManagement/show/0/{$smarty.session.lang}/menu"}{/block}
{block name=page_header}
    <script src="/templates/assets/resources/dist/app.js"></script>
{/block}
