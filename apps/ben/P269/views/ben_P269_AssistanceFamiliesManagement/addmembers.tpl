{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add_row#}</h4>
    </div>
    <div class="modal-body">
    <div class="row">
        <form method="post"
              action='{url urltype="path"
              url_string="ben/P269/AssistanceFamiliesManagement/show/0/{$smarty.session.lang}/addmembers/{$smarty.session.s_AssistanceFamilyManagement_token}"}'>
            <div class="col-lg-12">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_member_name#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="user_id" required="required">
                        <option value="0" selected>{#gnr_select_from_menu#}</option>
                        {foreach key=id item=etrow from=$members_list}
                            {if $etrow->type eq 0}
                                <option value="{$etrow->id}">{$etrow->full_name}</option>
                            {/if}
                        {/foreach}
                    </select>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_realtionship#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="relationship_id" required="required">
                        <option value="0" selected>{#gnr_select_from_menu#}</option>
                        {foreach key=id item=etrow from=$relationship_list}
                            {if !in_array($etrow->id,[
                                AssistanceFamilyMembers::FAMILY_MEMBER_TYPE_HUSBAND,
                                AssistanceFamilyMembers::FAMILY_MEMBER_TYPE_WIFE
                            ])}
                                <option value="{$etrow->id}">{$etrow->translatedName}</option>
                            {/if}
                        {/foreach}
                    </select>
                </div>
                <input type="hidden" name="family_id" value="{$family_id}">

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}