{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=title}{getname table=st_region id=$city->region_id}&nbsp;&raquo;&nbsp;{$city->name}{/block}
{block name=body}
    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
        <thead>
        <tr>
            <th style="background-color: #A0D468 !important;" width="5%">{url check=1 opr_code="AssistanceRegions" urltype=madd url_string="ben/P269/AssistanceRegions/addNeighborhood/0/{$smarty.session.lang}" modal=modal}</th>
            <th style="background-color: #A0D468 !important;" width="35%">{#gnr_neighborhood#}</th>
            <th style="background-color: #A0D468 !important;" width="35%">{#gnr_activation_status#}</th>
            <th style="background-color: #A0D468 !important;" width="25%">{#gnr_settings#}</th>
        </tr>
        </thead>
        {$i=1}
        <tbody>
        {foreach $neighborhoods as $neighborhood}
            <tr>
                <td align="center">{$i++}</td>
                <td>{$neighborhood->name}</td>
                <td>{t v=$neighborhood->activation}</td>
                <td class="text-center">
                    {url check=1 opr_code="AssistanceRegions" urltype=medit url_string="ben/P269/AssistanceRegions/editNeighborhood/0/{$smarty.session.lang}/{$neighborhood->id}" modal=modal}

                    {if $neighborhood->deletable}
                        {url check=1 opr_code="AssistanceRegions" urltype=mdelete url_string="ben/P269/AssistanceRegions/confirmNeighborhood/0/{$smarty.session.lang}/{$neighborhood->id}" modal=modal}
                    {else}
                        <span class="text-danger">{#p_can_not_delete_neig#}</span>
                    {/if}

                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
{/block}
{block name=back}{url urltype="path" url_string="ben/P269/AssistanceRegions/show/0/{$smarty.session.lang}"}{/block}