{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=title}{getname table=st_country id=$smarty.session.organization->country}{/block}
{block name=body}
    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
        <thead>
        <tr>
            <th style="background-color: #A0D468 !important;" width="5%">{url check=1 opr_code="AssistanceRegions" urltype=madd url_string="ben/P269/AssistanceRegions/add/0/{$smarty.session.lang}" modal=modal}</th>
            <th style="background-color: #A0D468 !important;" width="20%">{#gnr_region#}</th>
            <th style="background-color: #A0D468 !important;" width="20%">{#gnr_city#}</th>
            <th style="background-color: #A0D468 !important;" width="20%">{#gnr_neighborhoods#}</th>
            <th style="background-color: #A0D468 !important;" width="20%">{#gnr_number#}</th>
            <th style="background-color: #A0D468 !important;" width="15%">{#gnr_settings#}</th>
        </tr>
        </thead>
        {$i=1}
        <tbody>
        {foreach $regions as $region}
            <tr>
                <td align="center">{$i++}</td>
                <td align="center">{getname table=st_region id=$region->region_id}</td>
                <td align="center">{getname table=st_city id=$region->city_id}</td>
                <td align="center">{url urltype=button url_string="ben/P269/AssistanceRegions/neighborhoods/0/{$smarty.session.lang}/save_session/{$region->id}" text_value="{#gnr_neighborhoods#}" style="btn btn=default shiny"}</td>
                <td align="center">{$region->neighborhoodsNumber}</td>
                <td class="text-center">
                    {if $region->neighborhoodsNumber eq 0}
                        {url check=1 opr_code="AssistanceRegions" urltype=mdelete url_string="ben/P269/AssistanceRegions/confirm/0/{$smarty.session.lang}/{$region->id}" modal=modal}
                    {/if}
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
{/block}