{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{getname table=st_region id=$city->region_id}&nbsp;&raquo;&nbsp;{$city->name}&nbsp;&raquo;&nbsp;{#gnr_add#}&nbsp;{#gnr_neighborhood#}</h4>
    </div>
    <div class="modal-body">
        <form method="post"
              action='{url urltype="path" url_string="ben/P269/AssistanceRegions/neighborhoods/0/{$smarty.session.lang}/insert/{$smarty.session.s_AssistanceRegions_token}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_neighborhood#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <select name="neighborhoods[]" multiple>
                            {foreach $neighborhoods as $neighborhood}
                                <option value="{$neighborhood->id}">{$neighborhood->name}</option>
                            {/foreach}
                        </select>
                        {*<input type="text" name="name" class="form-control" required>*}

                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_activation_status#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $activationsStatus as $status}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="activation" value="{$status->id}" {if $status->id eq Setting::ACTIVE} checked {/if}required>
                                        <span class="text">{$status->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp">{#gnr_add#}</button></div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
