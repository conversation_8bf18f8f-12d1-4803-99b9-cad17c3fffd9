{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}
    <div class="container-fluid">
        <div class="text-center">
            <h1><u>{#p_families_data#}</u></h1>
        </div>
        <div class="panel panel-default">
                <div class="panel-body" style="display: inline-block;">
                {if !empty($filter['familyNumber'])}
                    {#p_family_number#} : {$filter['familyNumber']}
                    <br>
                {/if}

                {if !empty($filter['nationality'])}
                    {#gnr_nationality#} : {getname table=st_country id=$filter['nationality']}
                    <br>
                {/if}
                {if !empty($filter['head_of_the_family'])}
                    {#p_husband#} : {getname table=sh_user id=$filter['head_of_the_family']}
                    <br>
                {/if}
                {if !empty($filter['neighborhood'])}
                    {#gnr_neighborhood#} : {getname table=st_neighborhood id=$filter['neighborhood']}
                    <br>
                {/if}
                {if !empty($filter['residenceType'])}
                    {#p_residence_type#} : {t v=$filter['residenceType']}
                    <br>
                {/if}
                {if !empty($filter['healthStatus'])}
                    {#p_health_status#} : {t v=$filter['healthStatus']}
                    <br>
                {/if}
                {if !empty($filter['rentFrom'])}
                    {#gnr_from#} : {$filter['rentFrom']}
                    <br>
                {/if}
                {if !empty($filter['rentTo'])}
                    {#gnr_to#} : {$filter['rentTo']}
                    <br>
                {/if}

                </div>
            </div>
        <div class="row ">
            <div class="col-sm-12">
                <div class="widget-body">
                    <table class=" table table-bordered table-hover dataTable no-footer sortable-table">
                        <thead>
                        <tr>
                            <th width="10%">{#p_family_number#}</th>
                            <th width="20%">{#p_husband#}</th>
                            {*<th width="20%">{#p_wife#}</th>*}
                            <th
                                    width="10%">{#p_family_members_count#}</th>
                            {*<th width="20%">{#p_members#}</th>*}

                            {if in_array('nationality', $smarty.session['report_data']['dataSelected'])}
                                <th width="14.3%">{#gnr_nationality#}</th>
                            {/if}
                            {if in_array('neighborhood', $smarty.session['report_data']['dataSelected'])}
                                <th width="14.3%">{#gnr_neighborhood#}</th>
                            {/if}
                            {if in_array('residence_type', $smarty.session['report_data']['dataSelected'])}
                                <th width="20.3%">{#p_residence_type#}</th>
                            {/if}
                            {if in_array('total_family_income', $smarty.session['report_data']['dataSelected'])}
                                <th width="14.3%">{#p_total_family_income#}</th>
                            {/if}
                            {if in_array('average_family_income', $smarty.session['report_data']['dataSelected'])}
                                <th width="14.3%">{#p_average_family_income#}</th>
                            {/if}
                        </tr>
                        </thead>
                        <tbody>
                        {foreach $families as $family}
                            <tr>
                                <td class="text-center">{$family->file_number}</td>
                                <td class="text-center">{getname table=sh_user id=$family->head_of_the_family}</td>
                                {*<td class="text-center">{getname table=sh_user id=$family->husband_id}</td>*}
                                {*<td class="text-center">{getname table=sh_user id=$family->wife_id}</td>*}
                                <td class="text-center" title="{$member->userObject->full_name}">{$family->family_member_number}</td>
                                {*<td class="text-center">*}
                                {*{url urltype="mbutton" check=0 url_string="ben/P269/AssistanceFamiliesReport/familyMembers/0/{$smarty.session.lang}/{$family->id}" text_value="أفراد الأسرة" style="btn btn-default sharp"}*}
                                {*</td>*}

                                {if in_array('nationality', $smarty.session['report_data']['dataSelected'])}
                                    <td class="text-center">{getname table=st_country id=$family->nationality_id}</td>
                                {/if}
                                {if in_array('neighborhood', $smarty.session['report_data']['dataSelected'])}
                                    <td class="text-center" width = "20%">
                                        {foreach $neighborhoods as $neighborhood}
                                            {if $neighborhood->id eq $family->neighborhood_id}
                                                {$neighborhood->region->name}&nbsp;&raquo;&nbsp;{$neighborhood->city->name}&nbsp;&raquo;&nbsp;{getname table=st_neighborhood id=$family->neighborhood_id}
                                            {/if}
                                        {/foreach}
                                        {*{getname table=st_region id=$neighborhood->st_neighborhood_region_id}*}
                                        {*&nbsp;&raquo;&nbsp;*}
                                        {*{getname table=st_city id=$neighborhood->st_neighborhood_city_id}*}
                                        {*&nbsp;&raquo;*}
                                        {*{getname table=st_neighborhood id=$family->neighborhood_id}*}

                                        &nbsp;
                                        {*[&nbsp;{#p_fload_number#}&nbsp;{$family->floor_number}&nbsp;|&nbsp;{#p_national_number#}*}
                                        {*&nbsp;{$family->national_number}]*}
                                    </td>
                                {/if}
                                {if in_array('residence_type', $smarty.session['report_data']['dataSelected'])}
                                    <td class="text-center">
                                        {t v=$family->housing_type}
                                        {if $family->housing_type eq 368}
                                            &nbsp;[&nbsp;{#p_rent_value#}&nbsp;{$family->rent_amount}]
                                        {/if}</td>
                                {/if}
                                {if in_array('total_family_income', $smarty.session['report_data']['dataSelected'])}
                                    <td class="text-center">{$family->total_family_income}</td>
                                {/if}
                                {if in_array('average_family_income', $smarty.session['report_data']['dataSelected'])}
                                    <td class="text-center">{$family->average_family_income}</td>
                                {/if}
                            </tr>
                            {foreachelse}
                            <td colspan="100%"
                                class="text-muted text-center">
                                {#p_no_families_for_now#}
                            </td>
                        {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{/block}
{block name=back}{url urltype="path" url_string="ben/P269/AssistanceReports/show/0/{$smarty.session.lang}/menu"}{/block}

