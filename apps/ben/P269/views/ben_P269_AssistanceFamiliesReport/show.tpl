{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <!--Page Related styles-->
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
    <script src="/templates/assets/js/Chart.bundle.min.js"></script>
{/block}
{block name=page_body}
    {*<div class="container-fluid">*}
        <div class="widget">
        <div class="widget-header bg-blue">
            <i class="widget-icon fa fa-arrow-left"></i>
            <span class="widget-caption">{#gnr_search#}</span>
            <div class="widget-buttons">
                <div class="widget-buttons">
                    <a href="#" data-toggle="collapse">
                    </a>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="widget-body">
                    <form action="{url urltype="path" url_string="ben/P269/AssistanceFamiliesReport/show/0/{$smarty.session.lang}/report/{$smarty.session.s_AssistanceFamiliesReport_token}"}"
                          method="post">

                        <div class="row mb-1">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family_number#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                <input type="text"
                                       value="{$smarty.post["familyNumber"]}"
                                       name="familyNumber"
                                       class="form-control">
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_nationalitiy#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                <select name="nationality"
                                        placeholder="{#p_nationalitiy#}">
                                    <option></option>
                                    {foreach $nationalities as $nationality}
                                        <option value="{$nationality->id}" {if $nationality->id eq $smarty.post["nationality"]} selected {/if}
                                        >{$nationality->name}</option>
                                    {/foreach}
                                </select>
                            </div>

                        </div>
                        <div class="row mb-1">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_husband#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                <select name="head_of_the_family"
                                        placeholder="{#p_husband#}">
                                    <option></option>
                                    {foreach $families_search as $family}
                                        <option value="{$family->head_of_the_family}" {if $family->head_of_the_family eq $smarty.post["head_of_the_family"]} selected {/if}>
                                            {getname table=sh_user id=$family->head_of_the_family}</option>
                                        {*<option value="{$family->head_of_the_family}"*}
                                                {*{if {$family->head_of_the_family} eq $smarty.post["husband"]}selected{/if}*}
                                        {*>{$family->head_of_the_family->full_name}</option>*}
                                    {/foreach}
                                </select>
                            </div>

                            {*<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_wife#}</div>*}
                            {*<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">*}
                                {*<select name="wife"*}
                                        {*placeholder="{#p_wife#}">*}
                                    {*<option></option>*}

                                    {*{foreach $women as $woman}*}
                                        {*<option value="{$woman->id}"*}
                                                {*{if {$woman->id} eq $smarty.post["wife"]}selected{/if}*}
                                        {*>{$woman->full_name}</option>*}
                                    {*{/foreach}*}
                                {*</select>*}
                            {*</div>*}

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_national_number#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                <input type="text"
                                       name="nationalNumber"
                                       value="{$smarty.post["nationalNumber"]}"
                                       class="form-control">
                            </div>
                        </div>

                        <div class="row mb-1">


                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_neighborhood#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                <select name="neighborhood"
                                        placeholder="{#gnr_neighborhood#}"
                                        id="year">
                                    <option></option>

                                    {foreach $neighborhoods as $neighborhood}
                                        <option value="{$neighborhood->id}"
                                                {if {$neighborhood->id} eq $smarty.post["neighborhood"]}selected{/if}
                                        >{$neighborhood->region->name}&nbsp;&raquo;&nbsp;{$neighborhood->city->name}&nbsp;&raquo;&nbsp;{$neighborhood->name}
                                        </option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_residence_type#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                {foreach $housing_type_list as $key => $hrow}
                                    <div class="radio-inline">
                                        <label>
                                            <input type="radio"
                                                   name="residenceType"
                                                   value="{$hrow->id}"
                                                    {if $hrow->id eq $smarty.post["residenceType"]} checked {/if} >
                                            <span class="text">{$hrow->translatedName}</span>
                                        </label>
                                    </div>
                                {/foreach}
                            </div>

                        </div>
                        <div class="row mb-1">


                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_health_status#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                <select name="healthStatus"
                                        placeholder="{#p_health_status#}"
                                        id="year">
                                    <option></option>

                                    {foreach $healthStatus as $health}
                                        <option value="{$health->id}" {if $health->id eq $smarty.post["healthStatus"]} selected {/if}>{$health->translatedName}</option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_rent_rate#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 flex">
                                <input name="rentFrom"
                                       class="form-control"
                                       type="number"
                                       placeholder="{#gnr_from#}"
                                       value="{$smarty.post["rentFrom"]}"
                                       id="year">
                                <input name="rentTo"
                                       class="form-control mr-1"
                                       placeholder="{#gnr_to#}"
                                       value="{$smarty.post["rentTo"]}"
                                       type="number"
                                       id="year">
                            </div>

                        </div>
                        {*<div class="row mb-1">*}
                            {**}
                        {*</div>*}

                        <div class="row col-lg-12">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_data#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-10 col-xs-12 snsoinput">
                                <div class="col-lg-12">
                                    <div class="row">
                                        <div class="flex justify-center items-center mb-2 mt-1">
                                            <input style="background:lightgreen;" type="button" onclick='selectAll("dataSelected[]")' value="الكل"/>
                                            <input class="mr-1" style=" background:orangered;" type="button" onclick='UnSelectAll("dataSelected[]")' value="إلغاء"/>
                                        </div>
                                        <div class="flex items-center justify-center">
                                            <div class="checkbox"></div>
                                            <div class="checkbox">
                                                <label><input {if in_array('nationality', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                            type="checkbox" name="dataSelected[]" value="nationality"><span
                                                            class="text">{#gnr_nationality#}</span></label>
                                            </div>
                                            <div class="checkbox">
                                                <label><input {if in_array('neighborhood', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                            type="checkbox" name="dataSelected[]" value="neighborhood"><span
                                                            class="text">{#gnr_neighborhood#}</span></label>
                                            </div>
                                            <div class="checkbox">
                                                <label><input {if in_array('residence_type', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                            type="checkbox" name="dataSelected[]"
                                                            value="residence_type"><span
                                                            class="text">{#p_residence_type#}</span></label>
                                            </div>
                                            <div class="checkbox">
                                                <label><input {if in_array('total_family_income', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                            type="checkbox" name="dataSelected[]"
                                                            value="total_family_income"><span
                                                            class="text">{#p_total_family_income#}</span></label>
                                            </div>
                                            <div class="checkbox">
                                                <label><input {if in_array('average_family_income', $smarty.session['report_data']['dataSelected'])} checked {/if}
                                                            type="checkbox" name="dataSelected[]" value="average_family_income"><span
                                                            class="text">{#p_average_family_income#}</span></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-12">
                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 p-0">
                                    <input type="submit" class="btn btn-success sharp mr-4-px" name="submit"
                                           value="{#gnr_view#}">
                                    {if $smarty.post}
                                        {url check=0 urltype="button" style="btn btn-default shiny" url_string="ben/P269/AssistanceFamiliesReport/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
                                    {/if}
                                </div>
                            </div>
                        </div>

                        {*<div class="row">*}
                            {*<div class="col-lg-12">*}
                                {*<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>*}
                                {*<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 p-0">*}
                                    {*<input type="submit" class="btn btn-success sharp mr-4-px" name="submit"*}
                                           {*value="{#gnr_view#}">*}
                                {*</div>*}
                            {*</div>*}
                        {*</div>*}

                    </form>
                </div>
            </div>
        </div>
        {if $families}
        <div class="row  mt-2 mb-1">
            <div class="col-sm-12">
                {url check=0 urltype="print" url_string="ben/P269/AssistanceFamiliesReport/print/0/{$smarty.session.lang}" text_value="{#gnr_print_portrait#}" style="btn btn-default sharp"}
                {url check=0 urltype="print" url_string="ben/P269/AssistanceFamiliesReport/print/0/{$smarty.session.lang}/landscape" text_value="{#gnr_print_landscape#}" style="btn btn-default sharp"}
            </div>
        </div>

        <div class="row ">
            <div class="col-lg-12">
                <div class="widget-body">
                    <table class=" table table-bordered table-hover dataTable no-footer sortable-table">
                        <thead>
                        <tr>
                            <th width="10%">{#p_family_number#}</th>
                            <th width="20%">{#p_husband#}</th>
                            {*<th width="20%">{#p_wife#}</th>*}
                            <th
                                    width="10%">{#p_family_members_count#}</th>
                            {*<th width="20%">{#p_members#}</th>*}

                            {if in_array('nationality', $smarty.session['report_data']['dataSelected'])}
                                <th width="14.3%">{#gnr_nationality#}</th>
                            {/if}
                            {if in_array('neighborhood', $smarty.session['report_data']['dataSelected'])}
                                <th width="14.3%">{#gnr_neighborhood#}</th>
                            {/if}
                            {if in_array('residence_type', $smarty.session['report_data']['dataSelected'])}
                                <th width="20.3%">{#p_residence_type#}</th>
                            {/if}
                            {if in_array('total_family_income', $smarty.session['report_data']['dataSelected'])}
                                <th width="14.3%">{#p_total_family_income#}</th>
                            {/if}
                            {if in_array('average_family_income', $smarty.session['report_data']['dataSelected'])}
                                <th width="14.3%">{#p_average_family_income#}</th>
                            {/if}
                        </tr>
                        </thead>
                        <tbody>
                        {foreach $families as $family}
                            <tr>
                                <td class="text-center">{$family->file_number}</td>
                                <td class="text-center">{getname table=sh_user id=$family->head_of_the_family}</td>
                                {*<td class="text-center">{getname table=sh_user id=$family->husband_id}</td>*}
                                {*<td class="text-center">{getname table=sh_user id=$family->wife_id}</td>*}
                                <td class="text-center" title="{$member->userObject->full_name}">{$family->family_member_number}</td>
                                {*<td class="text-center">*}
                                    {*{url urltype="mbutton" check=0 url_string="ben/P269/AssistanceFamiliesReport/familyMembers/0/{$smarty.session.lang}/{$family->id}" text_value="أفراد الأسرة" style="btn btn-default sharp"}*}
                                {*</td>*}

                                {if in_array('nationality', $smarty.session['report_data']['dataSelected'])}
                                    <td class="text-center">{getname table=st_country id=$family->nationality_id}</td>
                                {/if}
                                {if in_array('neighborhood', $smarty.session['report_data']['dataSelected'])}
                                    <td class="text-center" width = "20%">
                                        {foreach $neighborhoods as $neighborhood}
                                            {if $neighborhood->id eq $family->neighborhood_id}
                                                {$neighborhood->region->name}&nbsp;&raquo;&nbsp;{$neighborhood->city->name}&nbsp;&raquo;&nbsp;{getname table=st_neighborhood id=$family->neighborhood_id}
                                            {/if}
                                        {/foreach}
                                            {*{getname table=st_region id=$neighborhood->st_neighborhood_region_id}*}
                                            {*&nbsp;&raquo;&nbsp;*}
                                            {*{getname table=st_city id=$neighborhood->st_neighborhood_city_id}*}
                                            {*&nbsp;&raquo;*}
                                            {*{getname table=st_neighborhood id=$family->neighborhood_id}*}

                                        &nbsp;
                                        {*[&nbsp;{#p_fload_number#}&nbsp;{$family->floor_number}&nbsp;|&nbsp;{#p_national_number#}*}
                                        {*&nbsp;{$family->national_number}]*}
                                    </td>
                                {/if}
                                {if in_array('residence_type', $smarty.session['report_data']['dataSelected'])}
                                    <td class="text-center">
                                        {t v=$family->housing_type}
                                        {if $family->housing_type eq 368}
                                            &nbsp;[&nbsp;{#p_rent_value#}&nbsp;{$family->rent_amount}]
                                        {/if}</td>
                                {/if}
                                {if in_array('total_family_income', $smarty.session['report_data']['dataSelected'])}
                                    <td class="text-center">{$family->total_family_income}</td>
                                {/if}
                                {if in_array('average_family_income', $smarty.session['report_data']['dataSelected'])}
                                    <td class="text-center">{$family->average_family_income}</td>
                                {/if}
                            </tr>
                            {foreachelse}
                            <td colspan="100%"
                                class="text-muted text-center">
                                {#p_no_families_for_now#}
                            </td>
                        {/foreach}
                        </tbody>
                    </table>
                    <br>
                </div>
            </div>
        </div>
        {else}
            <div class="alert alert-warning fade in"><i class="fa-fw fa fa-warning"></i>{#p_no_data#}</div>
        {/if}
    </div>
{/block}
{block name=page_header}
    <script>
        function selectAll(type) {
            var items = document.getElementsByName(type);
            for (var i = 0; i < items.length; i++) {
                if (items[i].type == 'checkbox')
                    items[i].checked = true;
            }
        }

        function UnSelectAll(type) {
            var items = document.getElementsByName(type);
            for (var i = 0; i < items.length; i++) {
                if (items[i].type == 'checkbox')
                    items[i].checked = false;
            }
        }
    </script>


    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function () {
            return {
                init: function () {
                    //Datatable Initiating
                    $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 15,
                        "oTableTools": {
                            "aButtons": [
                                //"copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}