{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}
    <div class="modal-body">
        <div class="well">
            <h4 class="block">{getname table=sh_user id=$family->husband_id}
                &nbsp;&raquo;&nbsp;{getname table=sh_user id=$family->wife_id}
                <hr>
            </h4>

            <div class="row">
                <div class="col-xs-12">
                    <div class="row">
                        <div class="col-xs-2 snsolabel">{#p_family_number#}</div>
                        <div class="col-xs-9 snsoinput">{$family->file_number}</div>
                    </div>

                    <div class="row">

                        <div class="col-xs-2 snsolabel">{#gnr_nationality#}</div>
                        <div class="col-xs-9 snsoinput">{getname table=st_country id=$family->nationality_id}</div>
                    </div>
                    <div class="row">

                        <div class="col-xs-2 snsolabel">{#gnr_neighborhood#}</div>
                        <div class="col-xs-9 snsoinput">
                            {getname table=st_region id=$family->region_id}
                            &nbsp;&raquo;&nbsp;
                            {getname table=st_city id=$family->city_id}
                            &nbsp;&raquo;&nbsp;
                            {getname table=st_neighborhood id=$family->neighborhood_id}
                            &nbsp;
                            [&nbsp;{#p_fload_number#}&nbsp;{$family->floor_number}&nbsp;|&nbsp;{#p_national_number#}
                            &nbsp;{$family->national_number}]
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-2 snsolabel">{#p_residence_type#}</div>
                        <div class="col-xs-9 snsoinput">
                            {t v=$family->housing_type}
                            {if $family->housing_type eq 368}
                                &nbsp;[&nbsp;{#p_rent_value#}&nbsp;{$family->rent_amount}]
                            {/if}
                        </div>

                    </div>
                    <div class="row">

                        <div class="col-xs-2 snsolabel">{#p_family_members_count#}</div>
                        <div class="col-xs-9 snsoinput">{$family->family_member_number}</div>
                    </div>
                    <div class="row">
                        <div class="col-xs-2 snsolabel">{#p_total_family_income#}</div>
                        <div class="col-xs-9 snsoinput">{$family->total_family_income}</div>

                    </div>
                    <div class="row">
                        <div class="col-xs-2 snsolabel">{#p_average_family_income#}</div>
                        <div class="col-xs-9 snsoinput">{$family->average_family_income}</div>

                    </div>
                    <div class="row">
                        <div class="col-xs-2 snsolabel">{#p_head_of_the_family#}</div>
                        <div class="col-xs-9 snsoinput">{$head_of_the_family->full_name}</div>

                    </div>
                    <div class="row">
                        <div class="col-xs-2 snsolabel">{#p_transfere_on_user_account#}</div>
                        <div class="col-xs-9 snsoinput">
                            {$transfere_on_user_account->full_name}
                            &nbsp;&raquo;&nbsp;
                            {$transfere_on_user_account->bank_number}
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="profile-container">
            <div class="profile-body">

                <div class="row">
                    <div class="col-xs-12">
                        <h5>{#p_members_manager#}</h5>
                        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                            <thead>
                            <tr>
                                <th width="30%"
                                >{#p_member_name#}</th>
                                <th width="10%"
                                >{#p_realtionship#}</th>
                                <th width="15%"
                                >{#p_birth_date#}</th>
                                <th width="15%"
                                >{#p_accountable_in_calculation#}</th>
                                <th width="15%"
                                >{#p_calculation_percentage#}</th>
                            </tr>
                            </thead>
                            <tbody>
                            {$i=1}
                            {foreach $familyMembers as $member}
                                <tr>
                                    <td>

                                        {if $member->userObject->live_status eq Setting::USER_IS_DEAD}
                                            <span style="color: grey">{$member->userObject->full_name}</span>
                                        {else}
                                            <span style="color: green">{$member->userObject->full_name}</span>
                                        {/if}
                                    </td>
                                    <td align="center">{t v=$member->relationship_id}
                                        &nbsp;[&nbsp;{t v=$member->social_status}&nbsp;]
                                    </td>
                                    <td align="center">{$member->userObject->birth_date}
                                        &nbsp;&raquo;&nbsp;{$member->userObject->age}&nbsp;{#gnr_year#}</td>
                                    <td align="center">{t v=$member->accountable_in_income_calculation}</td>
                                    <td align="center">{$member->percentage_in_income_calculation} %</td>
                                </tr>
                            {/foreach}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <h5>{#p_incomde_data#}</h5>
                        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                            <thead>
                            <tr>
                                <th width="23%"
                                >{#p_name#}</th>
                                <th width="12%"
                                >{#p_realtionship#}</th>
                                <th width="12%"
                                >{#p_income#}</th>
                                <th width="12%"
                                >{#p_family_income#}</th>
                                <th width="12%"
                                >{#p_accountable_in_calculation#}</th>
                                <th width="12%"
                                >{#p_percentage#}</th>
                                <th width="12%"
                                >{#p_income_medium#}</th>
                            </tr>
                            </thead>
                            <tbody>
                            {$i=1}
                            {foreach $family->IncomeDataStructure as $data}
                                <tr>
                                    <td>&nbsp;
                                        {getname table=sh_user id=$data['userId']}
                                    </td>
                                    <td align="center">{t v=$data['relation']}</td>
                                    <td align="center">{$data['userIncome']}</td>
                                    <td align="center">{$data['income']}</td>
                                    <td align="center">{t v=$data['countable']}</td>
                                    <td align="center">{$data['percentage']} %</td>
                                    <td align="center">{$data['incomeMedium']}</td>
                                </tr>
                            {/foreach}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <h5>{#p_visits#}</h5>
                        <table class="table table-striped table-bordered table-hover no-footer">
                            <thead>
                            <tr>
                                <th
                                        width="10%">{#p_delivery_date#}</th>
                                <th width="50%">{#p_comment#}</th>
                            </tr>
                            </thead>
                            {$i=1}
                            <tbody>
                            {foreach $familyCards as $card}
                                <tr>
                                    <td class="text-center">{getname table=assist_card id=$card->card_id}</td>
                                    <td class="text-center">{getdate table=assist_family_card col=delivery_date type=show row=$card}</td>
                                    <td class="text-center">{$card->comment}</td>
                                </tr>
                            {/foreach}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
