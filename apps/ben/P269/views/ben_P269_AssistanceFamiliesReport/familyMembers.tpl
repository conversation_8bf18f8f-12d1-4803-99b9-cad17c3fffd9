{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_members#}</h4>
    </div>
    <div class="modal-body">
        <div class="row mb-1">
            <div class="col-sm-12">
                {url urltype="print" check=0 url_string="ben/P269/AssistanceFamiliesReport/familyMemberPrint/0/{$smarty.session.lang}/{$family->id}" text_value="{#gnr_print#}" style="btn btn-default sharp"}
            </div>
        </div>
        <div class="well">
            <h4 class="block">{getname table=sh_user id=$family->head_of_the_family}
                {*&nbsp;&raquo;&nbsp;{getname table=sh_user id=$family->wife_id}*}
                <hr>
            </h4>

            <div class="row">
                <div class="col-lg-12">
                    <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family_number#}</div>
                    <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$family->file_number}</div>

                    <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_nationality#}</div>
                    <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_country id=$user->nationality}</div>

                    <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_neighborhood#}</div>
                    <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        {getname table=st_region id=$region->st_neighborhood_region_id}
                        &nbsp;&raquo;&nbsp;
                        {getname table=st_city id=$region->st_neighborhood_city_id}
                        &nbsp;&raquo;&nbsp;
                        {getname table=st_neighborhood id=$region->st_neighborhood_id}
                        &nbsp;
                        [&nbsp;{#p_fload_number#}&nbsp;{$family->floor_number}&nbsp;|&nbsp;{#p_national_number#}
                        &nbsp;{$family->national_number}]
                    </div>

                    <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_residence_type#}</div>
                    <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        {t v=$family->housing_type}
                        {if $family->housing_type eq 368}
                            &nbsp;[&nbsp;{#p_rent_value#}&nbsp;{$family->rent_amount}]
                        {/if}
                    </div>

                    <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family_members_count#}</div>
                    <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$family->family_member_number}</div>

                    <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_total_family_income#}</div>
                    <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$family->total_family_income}</div>

                    <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_average_family_income#}</div>
                    <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$family->average_family_income}</div>

                    <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_head_of_the_family#}</div>
                    <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$head_of_the_family->full_name}</div>

                    <div class="clo-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_transfere_on_user_account#}</div>
                    <div class="clo-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        {$transfere_on_user_account->full_name}
                        &nbsp;&raquo;&nbsp;
                        {$transfere_on_user_account->bank_number}
                    </div>


                </div>
            </div>
        </div>
        <div class="profile-container">
            <div class="profile-body">
                <div class="tabbable tabs-flat">
                    <ul class="nav nav-tabs  tabs-flat nav-justified" id="myTab5">
                        <li class="active">
                            <a data-toggle="tab" href="#members">
                                {#p_members_manager#}
                            </a>
                        </li>

                        <li class="tab-red">
                            <a data-toggle="tab" href="#income">
                                {#p_incomde_data#}
                            </a>
                        </li>

                        <li class="tab-warning">
                            <a data-toggle="tab" href="#visits">
                                {#p_visits#}
                            </a>
                        </li>

                        <li class="tab-green">
                            <a data-toggle="tab" href="#cards">
                                {#p_cards#}
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content tabs-flat">
                        <div id="members" class="tab-pane in active">
                            <div class="table-responsive snsoinput" data-pattern="priority-columns">

                                <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                    <thead>
                                    <tr>
                                        <th width="30%"
                                        >{#p_member_name#}</th>
                                        <th width="10%"
                                        >{#p_realtionship#}</th>
                                        <th width="15%"
                                        >{#p_birth_date#}</th>
                                        <th width="15%"
                                        >{#p_accountable_in_calculation#}</th>
                                        <th width="15%"
                                        >{#p_calculation_percentage#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $familyMembers as $member}
                                        <tr>
                                            <td>

                                                {if $member->userObject->live_status eq Setting::USER_IS_DEAD}
                                                    <span style="color: grey">{$member->userObject->full_name}</span>
                                                {else}
                                                    <span style="color: green">{$member->userObject->full_name}</span>
                                                {/if}
                                            </td>
                                            <td align="center">{t v=$member->relationship_id}
                                                &nbsp;[&nbsp;{t v=$member->social_status}&nbsp;]
                                            </td>
                                            <td align="center">{$member->userObject->birth_date}
                                                &nbsp;&raquo;&nbsp;{$member->userObject->age}&nbsp;{#gnr_year#}</td>
                                            <td align="center">{t v=$member->accountable_in_income_calculation}</td>
                                            <td align="center">{$member->percentage_in_income_calculation} %</td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>

                            </div>
                        </div>
                        <div id="income" class="tab-pane">
                            {if $family->members}
                                <div class="table-responsive snsoinput" data-pattern="priority-columns">

                                    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                                        <thead>
                                        <tr>
                                            <th width="23%"
                                            >{#p_name#}</th>
                                            <th width="12%"
                                            >{#p_realtionship#}</th>
                                            <th width="12%"
                                            >{#p_income#}</th>
                                            <th width="12%"
                                            >{#p_family_income#}</th>
                                            <th width="12%"
                                            >{#p_accountable_in_calculation#}</th>
                                            <th width="12%"
                                            >{#p_percentage#}</th>
                                            <th width="12%"
                                            >{#p_income_medium#}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {$i=1}
                                        {foreach $family->IncomeDataStructure as $data}
                                            <tr>
                                                <td>&nbsp;
                                                    {getname table=sh_user id=$data['userId']}
                                                </td>
                                                <td align="center">{t v=$data['relation']}</td>
                                                <td align="center">{$data['userIncome']}</td>
                                                <td align="center">{$data['income']}</td>
                                                <td align="center">{t v=$data['countable']}</td>
                                                <td align="center">{$data['percentage']} %</td>
                                                <td align="center">{$data['incomeMedium']}</td>
                                            </tr>
                                        {/foreach}
                                        </tbody>
                                    </table>

                                </div>
                            {else}
                                <div class="alert alert-warning">{#p_sorry_there_is_no_members_in_this_family#}</div>
                            {/if}
                        </div>
                        <div id="visits" class="tab-pane">
                            <table class="table table-striped table-bordered table-hover no-footer">
                                <thead>
                                <tr>
                                    <th
                                            width="10%">{#p_delivery_date#}</th>
                                    <th width="50%">{#p_comment#}</th>
                                </tr>
                                </thead>
                                {$i=1}
                                <tbody>
                                {foreach $familyCards as $card}
                                    <tr>
                                        <td class="text-center">{getname table=assist_card id=$card->card_id}</td>
                                        <td class="text-center">{getdate table=assist_family_card col=delivery_date type=show row=$card}</td>
                                        <td class="text-center">{$card->comment}</td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
