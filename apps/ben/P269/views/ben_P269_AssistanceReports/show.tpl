{*usage: {if $dir eq $rtl}output awesome message{else}output anothe$smarty.config.directionr awesome message{/if}*}
{assign var='rtl' value='rtl'}
{assign var='ltr' value='ltr'}
{capture get_direction assign=dir}
    {$smarty.config.direction}
{/capture}

{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <style>
        #container-search {
            -webkit-transition: width 0.4s ease-in-out;
            transition: width 0.4s ease-in-out;
            border-radius: 15px !important;
            -moz-border-radius: 15px !important;
            -webkit-border-radius: 15px !important;
            text-align: center;
        }

        /*!* When the input field gets focus, change its width to 100% *!*/
        /*#container-search:focus


        {*/
                                    /**/
                                /*}   */
        .search-input {
            margin-bottom: 5%;
        }

        .row-flex {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-wrap: wrap;
            flex-wrap: wrap;
        }

        .grid-content {
            height: 100%;
            padding: 20px 20px 10px;
            color: #fff;
        }

        a:hover {
            /* Applies to links under the pointer */
            text-decoration: none;
        }
    </style>
    <link rel="stylesheet" href="https://printjs-4de6.kxcdn.com/print.min.css" type="text/css">
{/block}
{block name=body}
    <div class="row search-input">
        <div class="col-lg-4 col-lg-offset-4">
            <span class="input-icon">
                <input type="search" id="container-search" value="" class="form-control" placeholder="{#gnr_search#}"
                       autofocus>
                <i class="fa fa-search blue palegreen"></i>
                {*<i class="fa fa-user"></i>circular *}
            </span>
        </div>
    </div>
    <div class="row fix" id="searchable-container">

        <div class="col-lg-6 col-lg-6 col-sm-6 col-xs-12 subject">
            <div class="widget col-lg-12">
                <div class="widget-header bg-blueberry bordered-palegreen">
                <span class="widget-caption">
                    التقارير الأساسية
                </span>
                </div><!--Widget Header-->
                <div class="widget-body">
                    <li class="dd-item dd2-item" data-id="13">
                        <div class="dd-handle dd2-handle">
                            <i class="normal-icon fa fa-bar-chart"></i>
                            <i class="drag-icon fa fa-arrows-alt "></i>
                        </div>
                        <a href="{url check=0 urltype="path" url_string="ben/P269/AssistanceMonitoring/show/0/{$smarty.session.lang}/menu"}"
                           class="dd2-content">
                            <div class="report">
                                {#p_assists_monitering#}
                            </div>
                        </a>
                        <div id="div1"></div>
                    </li>

                    <li class="dd-item dd2-item" data-id="13">
                        <div class="dd-handle dd2-handle">
                            <i class="normal-icon fa fa-bar-chart"></i>
                            <i class="drag-icon fa fa-arrows-alt "></i>
                        </div>
                        <a href="{url check=0 urltype="path" url_string="ben/P269/AssistanceFamiliesReport/show/0/{$smarty.session.lang}/menu"}"
                           class="dd2-content">
                            <div class="report">
                                {#p_family_report#}
                            </div>
                        </a>
                        <div id="div1"></div>
                    </li>

                </div><!--Widget Body-->
            </div>
        </div>


    </div>
    <script src="/templates/assets/js/jquery.searchable-1.0.0.min.js"></script>
{literal}

{/literal}
    <script>
        $(function () {
            $('#searchable-container').searchable({
                searchField: '#container-search',
                selector: '.subject',
                striped: true,
                childSelector: '.report',
                show: function (elem) {
                    elem.slideDown(150);
                },
                hide: function (elem) {
                    elem.slideUp(150);
                }
            });
        });
    </script>
    <script>
        $(document).ready(function () {
            $('button').click(function (evt) {
                return false;
            });

            function JSONToCSVConvertor(JSONData, ReportTitle, ShowLabel) {
                //If JSONData is not an object then JSON.parse will parse the JSON string in an Object
                var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;

                var CSV = '';
                //Set Report title in first row or line

                CSV += ReportTitle + '\r\n\n';

                //This condition will generate the Label/Header
                if (ShowLabel) {
                    var row = "";

                    //This loop will extract the label from 1st index of on array
                    for (var index in arrData[0]) {

                        //Now convert each value to string and comma-seprated
                        row += index + ',';
                    }

                    row = row.slice(0, -1);

                    //append Label row with line break
                    CSV += row + '\r\n';
                }

                //1st loop is to extract each row
                for (var i = 0; i < arrData.length; i++) {
                    var row = "";

                    //2nd loop will extract each column and convert it in string comma-seprated
                    for (var index in arrData[i]) {
                        row += '"' + arrData[i][index] + '",';
                    }

                    row.slice(0, row.length - 1);

                    //add a line break after each row
                    CSV += row + '\r\n';
                }

                if (CSV == '') {
                    alert("Invalid data");
                    return;
                }

                //Generate a file name
                var fileName = "MyReport_";
                //this will remove the blank-spaces from the title and replace it with an underscore
                fileName += ReportTitle.replace(/ /g, "_");

                //Initialize file format you want csv or xls
                var uri = 'data:text/csv;charset=utf-8,' + escape(CSV);

                // Now the little tricky part.
                // you can use either>> window.open(uri);
                // but this will not work in some browsers
                // or you will not get the correct file extension

                //this trick will generate a temp <a /> tag
                var link = document.createElement("a");
                link.href = uri;

                //set the visibility hidden so it will not effect on your web-layout
                link.style = "visibility:hidden";
                link.download = fileName + ".csv";

                //this part will append the anchor tag and remove it after automatic click
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        });
    </script>
{/block}