{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <style>
        .text-3xl{

            font-size: 1.875rem;
        }

        .container {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            -webkit-box-align: center;
            -moz-box-align: center;
            box-align: center;
            -webkit-align-items: center;
            -moz-align-items: center;
            -ms-align-items: center;
            -o-align-items: center;
            align-items: center;
            -ms-flex-align: center;
            display: -webkit-box;
            display: -moz-box;
            display: box;
            display: -webkit-flex;
            display: -moz-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: center;
            -moz-box-pack: center;
            box-pack: center;
            -webkit-justify-content: center;
            -moz-justify-content: center;
            -ms-justify-content: center;
            -o-justify-content: center;
            justify-content: center;
            -ms-flex-pack: center;
            background-color: #bf7a6b;
            background-image: -webkit-linear-gradient(bottom left, #bf7a6b 0%, #e6d8a7 100%);
            background-image: linear-gradient(to top right, #bf7a6b 0%, #e6d8a7 100%);
        }

        .form {
            width: 400px;
        }

        .file-upload-wrapper {
            position: relative;
            width: 100%;
            height: 60px;
        }

        .file-upload-wrapper:after {
            content: attr(data-text);
            font-size: 18px;
            position: absolute;
            top: 0;
            right: 18%;
            background: #fff;
            padding: 10px 15px;
            display: block;
            width: calc(82%);
            pointer-events: none;
            z-index: 20;
            height: 60px;
            line-height: 40px;
            color: #999;
            border-radius: 5px 10px 10px 5px;
            font-weight: 300;
        }

        .file-upload-wrapper:before {
            content: "ارفاق ملف";
            position: absolute;
            top: 5;
            right: 0;
            /*display: inline-block;*/
            display: flex;
            justify-content: center;
            align-items: center;
            height: 60px;
            text-align: center;
            background: #4daf7c;
            color: #fff;
            font-weight: 700;
            z-index: 25;
            font-size: 16px;
            line-height: 60px;
            padding: 0 15px;
            text-transform: uppercase;
            pointer-events: none;
            border-radius: 0 5px 5px 0;
        }

        .file-upload-wrapper:hover:before {
            background: #3d8c63;
        }

        .file-upload-wrapper input {
            opacity: 0;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 99;
            height: 60px;
            text-align: center;
            margin: 0;
            padding: 0;
            display: block;
            cursor: pointer;
            width: 100%;
        }
    </style>
{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"> {#p_std_details#}</h4>
    </div>
    <div class="modal-body " {if count($reasons) gte 1} style="min-height: 200px;"{/if}>
        {if count($reasons) gte 1}

            <form class="col-lg-12" method="post" action='{url urltype="path" url_string="ben/P269/exclusion/createExclusion/0/{$smarty.session.lang}"}' enctype="multipart/form-data">


                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel text-3xl ">حدد سبب الإسبتعاد</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">

                    <input type="hidden" name="user_id" value="{$user_id}">
                    <input type="hidden" name="family_id" value="{$family_id}">
                    {foreach $reasons as $reason}
                        <div class="radio">
                            <label>
                                {*<input id="activated" name="sms_activation" onchange="toggleDiv()" value="1"*}
                                {*type="radio" {if $assistance->sms_activation} checked {/if}>*}

                                <input type="radio" name="reason_id" value="{$reason->id}"
                                       {if $reason->id eq $reason_id}checked{/if}>
                                <span class="text">{$reason->assist_exclusion_reasons_reason}</span>
                            </label>
                        </div>
                    {/foreach}
                </div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel text-3xl ">المرفقات</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <div class="file-upload-wrapper  " data-text="اختر ملفك من هنا!">
                        <input name="file" type="file" class="file-upload-field" value="">
                    </div>
                </div>


                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                </div>
            </form>
        {else}
            <div class="text-center">
                <span class="danger">{#p_reasons_not_found#}</span>
            </div>
        {/if}

    </div>
{/block}
{block name=footer}
    <script>
        $("form").on("change", ".file-upload-field", function () {
            $(this)
                .parent(".file-upload-wrapper")
                .attr(
                    "data-text",
                    $(this)
                        .val()
                        .replace(/.*(\/|\\)/, "")
                );
        });
    </script>
{/block}
