{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_standard#}</h4>
    </div>
    <div class="modal-body" id="root">
        <form method="post" @submit="submitForm" action="{url urltype=path url_string="ben/P269/ExchangeRuleSettings/standards/0/{$smarty.session.lang}/update/{$smarty.session.s_ExchangeRuleSettings_token}/{$standard->id}"}">

            <input type="hidden" name="postData" id="postData">

            <div class="row snsowraper">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_choose_standard#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="type" id="type"
                            @change="selectChanged"
                            class="no-search form-control"
                            style="height: 40px !important; margin-bottom: 10px !important;"
                            disabled>
                        <option value="">{#gnr_unspecified#}</option>
                        {foreach $standards as $newStandard}
                            <option value="{$newStandard->id}" {if $standard->type eq $newStandard->id}selected{/if}>{$newStandard->translatedName}</option>
                        {/foreach}
                    </select>
                    <table class="table" v-if="showTable">
                        <tbody>
                        <tr>
                            <td>{#p_highest_value#}</td>
                            <td>
                                <input type="text" class="form-control" v-model="max" name="max">
                            </td>
                            <td>
                                <button type="button" @click="addNewClass" v-if="classesEmpty" class="btn btn-warning btn-sm">{#gnr_update#}</button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    {foreach AssistanceStandardClass::ALL_STANDARDS as $key => $standards}
                        <div class="col-lg-12" v-if="showArea.area{$key}">
                            <table class="table">
                                <tbody>
                                {foreach $standards as $standard}
                                    <tr>
                                        <td style="padding-right:10px">{Translation::translate($smarty.session.program, 'p_standard_class_'|cat:{$standard})}</td>
                                        <td><input type="number" name="standard_{$key}[{$standard}]" value="{$classesWithCodes.$standard}"> {#gnr_point#}</td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>
                        </div>
                    {/foreach}
                    <div>
                        <table v-if="showTable" class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                            <thead>
                            <tr>
                                <th width="75%">@{ title }</th>
                                <th width="25%">{#gnr_points#}</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr is="class-component" :max="max" v-for="item in classes"></tr>
                            </tbody>
                            <tfoot>
                            <tr>
                                <th colspan="6">
                                    <button type="button" @click="addClass" class="btn btn-sm btn-success" v-if=" ! classesEmpty"><i class="fa fa-plus"></i> {#p_add_new_class#}</button>
                                </th>
                            </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_activation#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {foreach $activation_options as $option}
                        <div class="radio">
                            <label>
                                <input name="is_active" value="{$option->id}" type="radio" {if $option->id eq $standard->is_active}checked{/if}>
                                <span class="text">{t v=$option->id}</span>
                            </label>
                        </div>
                    {/foreach}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
{block name=footer}
    <script>
        resize_modal('lg');

        let dynamic_standards_types = [1153, 1155, 1156, 1157];
        let static_standards_types = JSON.parse('{$static_standard_classes}');

        let all_standards = JSON.parse('{$standards|json_encode}');

        let data = {
            dynamic_standards: dynamic_standards_types,
            static_standards: static_standards_types,
            from_text: '{#gnr_from#}',
            to_text: '{#gnr_to#}',
            point_text: '{#gnr_point#}'
        };

        let newClass = {  };
        let selectedStandardId = '{$standard->type}';
        // Fill classes array
        let classes = JSON.parse('{$classes}');
        let selectedMaxValue = '{$standard->max}';

        {literal}

        Vue.component('class-component', {
            props: ['max'],
            template: `
            <tr>
                <td>
                    <table>
                        </thead>
                        <tbody>
                            <td>
                                <table>
                                    <tr>
                                    <td>
                                        {{ exData.from_text }}:
                                    </td>
                                    <td>
                                        <input type="number" min="0" style="width: 60%" v-model="newClass.points_min" v-if=" ! saved">
                                        <span v-if="saved">{{ newClass.points_min }}</span>
                                    </td>
                                    </tr>
                                </table>
                            </td>
                            <td>
                                <table>
                                    <tr>
                                    <td>
                                        {{ exData.to_text }}:
                                    </td>
                                    <td>
                                        <input type="number" min="0" style="width: 60%" v-model="newClass.points_max" v-if=" ! saved">
                                        <span v-if="saved">{{ newClass.points_max }}</span>
                                    </td>
                                    </tr>
                                </table>
                            </td>
                        </tbody>
                    </table>
                </td>
                <td>
                    <input type="number" style="width: 60%" v-model="newClass.points" v-if=" ! saved">
                    <span v-if="saved">{{ newClass.points }}</span> {{ exData.point_text }}
                </td>
            </tr>
            `,
            data() {
                return {
                    newClass: {},
                    exData: data,
                    saved: false,
                    save_button_class: ''
                }
            },
            created() {
                if ( ! jQuery.isEmptyObject(newClass)) {
                    this.newClass = newClass;
                    newClass = {};
                }
            },
            methods: {

            }

        });


        new Vue({
            el: '#root',
            delimiters: ['@{', '}'],
            data: {
                title: '',
                classes: [],
                type: '',
                max: 0,
                exData: data,
                showArea: {
                    area1152: false,
                    area1154: false,
                    area1158: false,
                    area1159: false,
                    area1160: false,
                },
                showTable: false,
                submitted: false,
            },
            mounted () {
                if (_.indexOf(this.exData.static_standards, parseInt(selectedStandardId)) !== -1) {
                    this.showArea['area' + selectedStandardId] = true;
                } else if (_.indexOf(this.exData.dynamic_standards, parseInt(selectedStandardId)) !== -1) {
                    this.showTable = true;
                    this.max = selectedMaxValue;
                    this.title = _.findWhere(all_standards, {id: selectedStandardId})['translatedName'];
                }
                _.each(classes, (item) => {
                    // I don't why to use setTimeout in this situation but it works :) need revision after understand JS sync
                    setTimeout(() => {
                        newClass = {
                            points: item.points,
                            points_min: item.points_min,
                            points_max: item.points_max
                        };
                        this.classes.push(newClass);
                    }, 1)
                })
            },
            computed: {
                classesEmpty () {
                    return this.classes.length === 0;
                }
            },
            methods: {
                selectChanged (e) {
                    let selected_type = e.target.value;

                    if (_.indexOf(this.exData.static_standards, parseInt(selected_type)) !== -1) {
                        // Hide all others areas
                        _.map(this.showArea, (item, index) => {
                            this.showArea[index] = false;
                        });
                        this.showArea['area' + selected_type] = true; //e.g: area1152
                        this.showTable = false;
                    } else if (_.indexOf(this.exData.dynamic_standards, parseInt(selected_type)) !== -1) {
                        _.map(this.showArea, (item, index) => {
                            this.showArea[index] = false;
                        });
                        this.showTable = true;
                        this.title = _.findWhere(all_standards, {id: selected_type})['translatedName'];
                    }
                },
                addNewClass () {
                    newClass = {
                        points: 0,
                        points_min: 0,
                        points_max: this.max
                    };
                    this.classes.push(newClass);
                },
                addClass () {
                    let previousClass = this.classes[this.classes.length - 1];
                    newClass = {
                        points: 0,
                        points_min: 0,
                        points_max: previousClass.points_min
                    };

                    this.classes.push(newClass);
                },
                submitForm () {
                    $('#postData').val(JSON.stringify(this.classes));

                    return true;
                }
            }
        });
        {/literal}
    </script>
{/block}