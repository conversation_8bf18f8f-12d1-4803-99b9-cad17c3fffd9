{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />{/block}
{block name=body}
    <div class="row">
        <div class="col-lg-12">
            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th style="background-color: #A0D468 !important;" width="5%">
                        {url check=1 opr_code="ExchangeRuleSettings" urltype=add url_string="ben/P269/ExchangeRuleSettings/add/0/{$smarty.session.lang}" modal=modal}
                    </th>
                    <th style="background-color: #A0D468 !important;" width="45%">{#p_exchange_rule_name#}</th>
                    <th style="background-color: #A0D468 !important;" width="25%">{#p_standards#}</th>
                    <th style="background-color: #A0D468 !important;" width="25%">{#gnr_settings#}</th>
                </tr>
                </thead>
                {$i=1}
                <tbody>
                    {foreach $rules as $rule}
                        <tr>
                            <td>{$i++}</td>
                            <td>{$rule->name}</td>
                            <td class="text-center">{url urltype=button url_string="ben/P269/ExchangeRuleSettings/standards/0/{$smarty.session.lang}/save_session/{$rule->id}" text_value="{#p_standards#}" style="btn btn=default shiny"}</td>
                            <td class="text-center">
                                {url check=0 urltype=mbutton url_string="ben/P269/ExchangeRuleSettings/browse/0/{$smarty.session.lang}/{$rule->id}" text_value="<i class='fa fa-file-o'></i>" style="" modal=modal}
                                {url check=1 opr_code="ExchangeRuleSettings" urltype=edit url_string="ben/P269/ExchangeRuleSettings/add/0/{$smarty.session.lang}/{$rule->id}" modal=modal}
                                {if $rule->deletable() and $rule->id ne 1}
                                    {url check=1 opr_code="ExchangeRuleSettings" urltype=mdelete url_string="ben/P269/ExchangeRuleSettings/confirm/0/{$smarty.session.lang}/{$rule->id}" modal=modal}
                                {/if}
                            </td>
                        </tr>
                    {/foreach}
                </tbody>
            </table>
        </div>
    </div>
{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
        function exportTo(ID, type) {
            $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
        }
        {/literal}
    </script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}