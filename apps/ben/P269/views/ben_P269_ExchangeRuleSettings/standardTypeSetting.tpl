{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_set_criterion#}{t v=$standardType->id}</h4>
    </div>
    <div class="modal-body">

        <form method="post" @submit="submitForm" action="{url urltype=path url_string="ben/P269/ExchangeRuleSettings/standards/0/{$smarty.session.lang}/updateStandardTypeSetting/{$smarty.session.s_ExchangeRuleSettings_token}/{$rule->id}/{$standardType->id}"}">

            {if $standardType->id eq AssistanceRuleStandard::ASSISTANCE_GENDER_STANDARD}

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_male#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{Setting::MALE}" value="{$male->points}">{#gnr_point#}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_female#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{Setting::FEMALE}" value="{$female->points}">{#gnr_point#}
                </div>

            {/if}

            {if $standardType->id eq AssistanceRuleStandard::ASSISTANCE_AGE_STANDARD}

            {/if}

            {if $standardType->id eq AssistanceRuleStandard::ASSISTANCE_DEPENDENCY_STANDARD}

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_PERSON_STATUS_ALONE}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_PERSON_STATUS_ALONE}" value="{$PersonStatusAlone->points}">{#gnr_point#}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_PERSON_STATUS_FOSTER}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_PERSON_STATUS_FOSTER}" value="{$PersonStatusFoster->points}">{#gnr_point#}
                </div>

            {/if}

            {if $standardType->id eq AssistanceRuleStandard::ASSISTANCE_RENT_STANDARD}

            {/if}

            {if $standardType->id eq AssistanceRuleStandard::ASSISTANCE_INCOME_STANDARD}

            {/if}

            {if $standardType->id eq AssistanceRuleStandard::ASSISTANCE_SONS_COUNT_STANDARD}

            {/if}

            {if $standardType->id eq AssistanceRuleStandard::ASSISTANCE_JOB_SITUATION_STANDARD}

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_WORK_STATUS_NOT_WORKING}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_WORK_STATUS_NOT_WORKING}" value="{$workStatusNotWorking->points}">{#gnr_point#}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_WORK_STATUS_TEMPORARY_WORK}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_WORK_STATUS_TEMPORARY_WORK}" value="{$workStatusTemporaryWork->points}">{#gnr_point#}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_WORK_STATUS_PERMANENT_WORK}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_WORK_STATUS_PERMANENT_WORK}" value="{$workStatusPermanentWork->points}">{#gnr_point#}
                </div>

            {/if}

            {if $standardType->id eq AssistanceRuleStandard::ASSISTANCE_HEALTH_STATUS_STANDARD}

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_HEALTH_STATUS_NORMAL}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_HEALTH_STATUS_NORMAL}" value="{$healthStatusNormal->points}">{#gnr_point#}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_HEALTH_STATUS_SICK_TEMPORARY}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_HEALTH_STATUS_SICK_TEMPORARY}" value="{$healthStatusSickTemporary->points}">{#gnr_point#}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_HEALTH_STATUS_SICK_PERMANENT}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_HEALTH_STATUS_SICK_PERMANENT}" value="{$healthStatusSickPermanent->points}">{#gnr_point#}
                </div>

            {/if}

            {if $standardType->id eq AssistanceRuleStandard::ASSISTANCE_FAMILY_HEALTH_STATUS_STANDARD}

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_FAMILY_HEALTH_NO_SICK}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_FAMILY_HEALTH_NO_SICK}" value="{$familyHealthNoSick->points}">{#gnr_point#}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_FAMILY_HEALTH_ONE_TEMPORARY}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_FAMILY_HEALTH_ONE_TEMPORARY}" value="{$familyHealthOneTemporary->points}">{#gnr_point#}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_FAMILY_HEALTH_ONE_PERMANENT}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_FAMILY_HEALTH_ONE_PERMANENT}" value="{$healthOnePermanent->points}">{#gnr_point#}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_FAMILY_HEALTH_MORE_PERMANENT}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_FAMILY_HEALTH_MORE_PERMANENT}" value="{$healthMorePermanent->points}">{#gnr_point#}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{t v=AssistanceStandardClass::SETTING_ASSISTANCE_FAMILY_HEALTH_MORE_TEMPORARY}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="number" name="{AssistanceStandardClass::SETTING_ASSISTANCE_FAMILY_HEALTH_MORE_TEMPORARY}" value="{$healthMoreTemporary->points}">{#gnr_point#}
                </div>

            {/if}

            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp">{#gnr_add#}</button></div>

        </form>

    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
{block name=footer}

{/block}