{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />{/block}
{block name=body}
    <div class="row">
        <div class="col-lg-12">
            <h5 class="well bordered-top bordered-blue"><b>{#p_set_criteria#}</b>: ( {$rule->name} )</h5>
            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                <thead>
                <tr>
                    <th style="background-color: #A0D468 !important;" width="5%">
                        {if $standard->rule_id ne 1}
                            {url check=1 opr_code="ExchangeRuleSettings" urltype=madd url_string="ben/P269/ExchangeRuleSettings/addStandard/0/{$smarty.session.lang}" modal=modal}
                        {/if}
                    </th>
                    <th style="background-color: #A0D468 !important;" width="45%">{#p_standard#}</th>
                    <th style="background-color: #A0D468 !important;" width="25%">{#gnr_status#}</th>
                    <th style="background-color: #A0D468 !important;" width="25%">{#gnr_settings#}</th>
                </tr>
                </thead>
                {$i=1}
                <tbody>
                {foreach $standards as $standard}
                    <tr>
                        <td>{$i++}</td>
                        <td>{t v=$standard->type}</td>
                        <td class="text-center">
                            {if $standard->rule_id ne 1}
                                {url urltype=mbutton url_string="ben/P269/ExchangeRuleSettings/changeActivationStatus/0/{$smarty.session.lang}/{$standard->id}" text_value="{#gnr_activation#}" modal=modal}
                            {/if}
                            {t v=$standard->is_active}
                        </td>
                        <td class="text-center">

                            {if $standard->type eq AssistanceRuleStandard::ASSISTANCE_GENDER_STANDARD}
                                {url urltype=mbutton url_string="ben/P269/ExchangeRuleSettings/standardTypeSetting/0/{$smarty.session.lang}/{$standard->id}/{$standard->type}" text_value="{#gnr_settings#}" modal=modal}
                            {/if}

                            {if $standard->type eq AssistanceRuleStandard::ASSISTANCE_AGE_STANDARD}
                                {url urltype=button url_string="ben/P269/ExchangeRuleSettings/addStandardVue/0/{$smarty.session.lang}/{$standard->id}/{$standard->type}" text_value="{#gnr_settings#}" modal=modal}
                            {/if}

                            {if $standard->type eq AssistanceRuleStandard::ASSISTANCE_DEPENDENCY_STANDARD}
                                {url urltype=mbutton url_string="ben/P269/ExchangeRuleSettings/standardTypeSetting/0/{$smarty.session.lang}/{$standard->id}/{$standard->type}" text_value="{#gnr_settings#}" modal=modal}
                            {/if}

                            {if $standard->type eq AssistanceRuleStandard::ASSISTANCE_RENT_STANDARD}
                                {url urltype=button url_string="ben/P269/ExchangeRuleSettings/addStandardVue/0/{$smarty.session.lang}/{$standard->id}/{$standard->type}" text_value="{#gnr_settings#}" modal=modal}
                            {/if}

                            {if $standard->type eq AssistanceRuleStandard::ASSISTANCE_INCOME_STANDARD}
                                {url urltype=button url_string="ben/P269/ExchangeRuleSettings/addStandardVue/0/{$smarty.session.lang}/{$standard->id}/{$standard->type}" text_value="{#gnr_settings#}" modal=modal}
                            {/if}

                            {if $standard->type eq AssistanceRuleStandard::ASSISTANCE_SONS_COUNT_STANDARD}
                                {url urltype=button url_string="ben/P269/ExchangeRuleSettings/addStandardVue/0/{$smarty.session.lang}/{$standard->id}/{$standard->type}" text_value="{#gnr_settings#}" modal=modal}
                            {/if}

                            {if $standard->type eq AssistanceRuleStandard::ASSISTANCE_JOB_SITUATION_STANDARD}
                                {url urltype=mbutton url_string="ben/P269/ExchangeRuleSettings/standardTypeSetting/0/{$smarty.session.lang}/{$standard->id}/{$standard->type}" text_value="{#gnr_settings#}" modal=modal}
                            {/if}

                            {if $standard->type eq AssistanceRuleStandard::ASSISTANCE_HEALTH_STATUS_STANDARD}
                                {url urltype=mbutton url_string="ben/P269/ExchangeRuleSettings/standardTypeSetting/0/{$smarty.session.lang}/{$standard->id}/{$standard->type}" text_value="{#gnr_settings#}" modal=modal}
                            {/if}

                            {if $standard->type eq AssistanceRuleStandard::ASSISTANCE_FAMILY_HEALTH_STATUS_STANDARD}
                                {url urltype=mbutton url_string="ben/P269/ExchangeRuleSettings/standardTypeSetting/0/{$smarty.session.lang}/{$standard->id}/{$standard->type}" text_value="{#gnr_settings#}" modal=modal}
                            {/if}
                            {if $standard->type eq AssistanceRuleStandard::ASSISTANCE_FAMILY_INCOME_AVERAGE_STANDARD}
                                {url urltype=button url_string="ben/P269/ExchangeRuleSettings/addStandardVue/0/{$smarty.session.lang}/{$standard->id}/{$standard->type}" text_value="{#gnr_settings#}" modal=modal}
                            {/if}



                            {*{url check=0 urltype=mbutton url_string="ben/P269/ExchangeRuleSettings/browseStandard/0/{$smarty.session.lang}/{$standard->id}" modal=modal text_value="<i class='fa fa-file-o'></i>" style="btn btn-default shiny"}*}
                            {*{url check=1 opr_code="ExchangeRuleSettings" urltype=medit url_string="ben/P269/ExchangeRuleSettings/editStandard/0/{$smarty.session.lang}/{$standard->id}" modal=modal}*}
                                {url check=1 opr_code="ExchangeRuleSettings" urltype=mdelete url_string="ben/P269/ExchangeRuleSettings/confirmStandard/0/{$smarty.session.lang}/{$standard->id}" modal=modal}
                        </td>
                    </tr>
                {/foreach}
                </tbody>
            </table>
        </div>
    </div>
{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
        function exportTo(ID, type) {
            $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
        }
        {/literal}
    </script>
    <script>
        let InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    let oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}
{block name=back}{url urltype=path url_string="ben/P269/ExchangeRuleSettings/show/0/{$smarty.session.lang}"}{/block}