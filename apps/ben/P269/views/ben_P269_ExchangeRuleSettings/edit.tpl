{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <style>
        .modal-dialog {
            width: 90%;
            max-width:1200px;
            min-height: 500px;
        }
    </style>
{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_exchange_rule#}</h4>
    </div>
    <div class="modal-body" id="root">
        <form method="post" @submit.prevent="onSubmit" action="{$smarty.server.PHP_SELF}">
            <div class="row snsowraper">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_exchange_rule_name#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="text" name="name" class="form-control" v-model="name">
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_exchange_points_sum#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <div v-if="! button_update && classesIsEmpty">
                        <input type="number" min="0" data-parsley-type="number" name="points_sum" v-model="total_points_sum">
                        <button class="btn btn-warning" @click="addNewClass" type="button">{#gnr_update#}</button>
                    </div>
                    <div v-else>
                        @{ total_points_sum }
                    </div>
                </div>

                <h4>{#p_exchange_classes#}</h4>
                <table class="table table-striped table-bordered">
                    <thead>
                    <tr>
                        <th width="10%">{#p_class_code#}</th>
                        <th width="15%">{#p_class_name#}</th>
                        <th width="10%">{#p_class_action#}</th>
                        <th width="37.5%">{#p_class_points#}</th>
                        <th width="10%">{#p_class_percentage#}</th>
                        <th width="15%">{#gnr_settings#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr is="class-component" @class-removed="removeClass($event)" :total="total_points_sum" v-for="(item, index) in classes" :item="item" :index="index" :key="item"></tr>
                    </tbody>
                    <tfoot>
                    <tr>
                        <th colspan="6">
                            <button type="button" @click="addClass" class="btn btn-sm btn-success" v-if=" ! classesIsEmpty"><i class="fa fa-plus"></i> {#p_add_new_class#}</button>
                        </th>
                    </tr>
                    </tfoot>
                </table>

                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" :class="submitted" class="btn btn-warning sharp">{#gnr_update#}</button>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}


{block name=footer}
    <script>
        // Shared data

        select_options = JSON.parse('{$ruleActionTypes}');
        let data = {
            from_text: '{#gnr_from#}',
            to_text: '{#gnr_to#}',
            to_less_text: '{#gnr_to_less_than#}',
            save_text: '{#gnr_save#}',
            first_class_text: '{#p_highest_value#}',
            first_class_code: '{#gnr_a_letter#}',
            select_options: select_options,
        };

        // Temporary use in funny situation :)
        let newClass = {  };

        let rule = JSON.parse('{$rule}');
        let classes = JSON.parse('{$ruleClasses}');

        {literal}
        Vue.component('class-component', {
            props: ['total', 'item', 'index'],
            template: `
            <tr>
                <td>
                    <input type="text" style="width: 60%" v-model="newClass.code" v-if=" ! saved">
                    <span v-if="saved">{{ newClass.code }}</span>
                </td>
                <td>
                    <input type="text" v-model="newClass.name" v-if=" ! saved">
                    <span v-if="saved">{{ newClass.name }}</span>
                </td>
                <td>
                    <select v-model="newClass.action" v-if=" ! saved">
                        <option v-for="option in exData.select_options" :value="option.id">{{ option.name }}</option>
                    </select>
                    <span v-if="saved">{{ newClass.action | actionName }}</span>
                </td>
                <td>
                    <table>
                        <tbody>
                            <td>
                                {{ exData.from_text }}:
                                <input type="number" min="0" style="width: 60%" v-model="newClass.points_min" v-if=" ! saved">
                                <span v-if="saved">{{ newClass.points_min }}</span>
                            </td>
                            <td>
                                 | <span v-if="index == 0">{{ exData.to_text }}</span><span v-else>{{ exData.to_less_text }}</span>:
                                <input type="number" min="0" style="width: 60%" v-model="newClass.points_max" v-if=" ! saved">
                                <span v-if="saved">{{ newClass.points_max }}</span>
                            </td>
                        </tbody>
                    </table>
                </td>
                <td>
                    {{ min }} - {{ max }}
                </td>
                <td class="text-center">
                    <button type="button" class="btn btn-success btn-sm" @click="saveClass" v-if="!save_button">{{ exData.save_text }}</button>
                    <button type="button" class="btn btn-sm btn-danger" @click="$emit('class-removed', newClass)" v-if="!save_button"><i class="fa fa-trash-o"></i></button>
                </td>
            </tr>
            `,
            data() {
                return {
                    newClass: {},
                    exData: data,
                    saved: false,
                    save_button: false,
                }
            },
            created() {
                if ( ! jQuery.isEmptyObject(newClass)) {
                    this.newClass = newClass;
                    newClass = {};
                } else if ( ! jQuery.isEmptyObject(this.item)) {
                    this.newClass = this.item
                } else {
                    this.newClass = {};
                }
            },
            computed: {
                min() {
                    if ( ! jQuery.isEmptyObject(this.newClass)) {
                        return (this.newClass.points_min * 100 / this.total).toFixed(1);
                    } else {
                        return 0;
                    }
                },
                max() {
                    if ( ! jQuery.isEmptyObject(this.newClass)) {
                        return (this.newClass.points_max * 100 / this.total).toFixed(1);
                    } else {
                        return 0;
                    }
                },
            },
            methods: {
                saveClass() {
                    this.saved = true;
                    this.save_button = true;
                }
            },
            filters: {
                actionName(action_id) {
                    let action = _.findWhere(select_options, {id: action_id.toString()});
                    if (jQuery.isEmptyObject(action)) {
                        return '';
                    } else {
                        return action.name;
                    }
                }
            }

        });

        new Vue({
            el: '#root',
            delimiters: ['@{', '}'],
            data: {
                classes: [],
                exData: data,
                total_points_sum: 0,
                name: '',
                button_update: false,
                submitted: ''
            },
            created() {
                this.name = rule.assist_exch_rules_name;
                this.total_points_sum = rule.assist_exch_rules_points_sum;
                this.classes = classes;
            },
            computed: {
                classesIsEmpty() {
                    return this.classes.length === 0;
                }
            },
            methods: {
                onSubmit() {
                    this.submitted = 'disabled';

                    let params = new URLSearchParams();
                    params.append('classes', JSON.stringify(this.classes));
                    params.append('name', this.name);
                    params.append('points_sum', this.total_points_sum);
                    console.log(params)
                    axios.put('/api.php/assist/rules/' + rule.assist_exch_rules_id, params)
                        .then(res => {
                            if (res.data.success) {
                                location.reload();
                            } else {
                                this.submitted = '';
                            }
                        });
                },
                addNewClass() {
                    this.button_update = true;
                    newClass = {
                        code: this.exData.first_class_code,
                        name: this.exData.first_class_text,
                        action: this.exData.select_options[0],
                        points_min: 0,
                        points_max: this.total_points_sum
                    };
                    this.classes.push(newClass);
                },
                addClass() {
                    newClass = {
                        points_max: this.classes[this.classes.length - 1].points_min,
                        points_min: 0
                    };
                    this.classes.push(newClass)
                },
                removeClass(item) {
                    let index = this.classes.indexOf(item);
                    this.classes.splice(index, 1);
                }
            }
        });
        {/literal}
    </script>
{/block}