{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
    <style>
        .modal-dialog {
            width: 90%;
            max-width: 1200px;
            min-height: 500px;
        }
    </style>
{/block}
{block name=title}{#p_add_exchange_rule#}{/block}
{block name=body}
    <div>
        <law-classes inline-template>
            <form method="post" ref="form" @submit.prevent="submit"
                  action='{url urltype="path" url_string="ben/P269/ExchangeRuleSettings/show/0/{$smarty.session.lang}/addNewRule/{$smarty.session.s_ExchangeRuleSettings_token}/{$rule->assist_exch_rules_id}"}'>
                <div class="row snsowraper">
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_exchange_rule_name#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <input type="text" name="name" class="form-control" :class="{ 'has-error':$v.law.name.$error }"
                               v-model="law.name"
                               >
                        {*<span v-if="law.classes.length > 0" v-text="law.name"></span>*}
                        <span v-if="$v.law.name.$error" class="has-error">
                            هذا الحقل مطلوب
                        </span>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_exchange_points_sum#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="form-group" v-show="law.classes.length == 0">
                            <input type="number" min="1" name="points_sum" v-model="law.pointsTotal">
                            <button class="btn btn-warning" @click="pushNewClass" type="button">{#gnr_update#}</button>
                        </div>
                        <span v-if="law.classes.length > 0" v-text="law.pointsTotal"></span>
                    </div>

                    <h4>{#p_exchange_classes#}</h4>
                    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                        <thead>
                        <tr>
                            <th>{#p_class_code#}</th>
                            <th>{#p_class_name#}</th>
                            <th>{#p_class_action#}</th>
                            <th>{#p_class_points#}</th>
                            <th>{#p_class_percentage#}</th>
                            <th>{#gnr_settings#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(row,index) in law.classes">
                            <td>
                                <input :name="'arrayOfValues['+index+'][code]'" v-show="row.edit && row == lastClass"
                                       type="text"
                                       v-model="row.code"
                                       :class="{ 'has-error':$v.law.classes.$each[index].code.$error }">
                                <span v-if="$v.law.classes.$each[index].code.$error"
                                      :class="{ 'has-error':$v.law.classes.$each[index].code.$error }">{#p_required_field#}</span>
                                <span v-if="!row.edit ||  row != lastClass" v-text="row.code"></span>
                            <td>
                                <input type="text" :name="'arrayOfValues['+index+'][name]'"
                                       v-show="row.edit && row == lastClass"
                                       v-model="row.name"
                                       :class="{ 'has-error':$v.law.classes.$each[index].name.$error }">
                                <span v-if="$v.law.classes.$each[index].name.$error"
                                      :class="{ 'has-error':$v.law.classes.$each[index].name.$error }">{#p_required_field#}</span>
                                <span v-if="!row.edit ||  row != lastClass" v-text="row.name"></span>
                            </td>
                            <td>

                                <selectize v-model="row.action" v-if="row.edit && row == lastClass"
                                           :name="'arrayOfValues['+index+'][action]'"
                                           :class="{ 'has-error':$v.law.classes.$each[index].action.$error }">
                                        <option v-for="type in ruleTypes" :value="type.id" v-text="type.name"></option>
                                </selectize>
                                <input v-if="!row.edit ||  row != lastClass" :name="'arrayOfValues['+index+'][action]'"
                                       type="hidden" :value="row.action">
                                <span v-if="$v.law.classes.$each[index].action.$error"
                                      :class="{ 'has-error':$v.law.classes.$each[index].action.$error }">{#p_required_field#}</span>
                                <span v-if="!row.edit ||  row != lastClass"  v-text="selectedAction(row)"></span>
                            </td>

                            <td>
                                {#gnr_from#}:
                                <input type="number" v-show="row.edit && row == lastClass"
                                       :name="'arrayOfValues['+index+'][minPoint]'"
                                       min="0" v-model="row.minPoint" @input="checkMin"
                                       :class="{ 'has-error':$v.law.classes.$each[index].minPoint.$error }">
                                <span v-if="$v.law.classes.$each[index].minPoint.$error"
                                      :class="{ 'has-error':$v.law.classes.$each[index].minPoint.$error }">{#p_required_field#}</span>
                                <span v-text="row.minPoint" v-if="!row.edit ||  row != lastClass"></span>
                                <span v-if="index == 0">{#gnr_to#}</span>
                                <span v-else>{#gnr_to_less_than#}</span>:
                                <input type="number" min="0" v-model="row.maxPoint"
                                       :name="'arrayOfValues['+index+'][maxPoint]'"
                                       v-show="row.edit && row == lastClass"
                                       @input="checkMax(row,index)"
                                       :class="{ 'has-error':$v.law.classes.$each[index].maxPoint.$error }">
                                <span v-if="$v.law.classes.$each[index].maxPoint.$error"
                                      :class="{ 'has-error':$v.law.classes.$each[index].maxPoint.$error }">{#p_required_field#}</span>
                                <span v-text="row.maxPoint" v-if="!row.edit ||  row != lastClass"></span>
                            </td>
                            <td>
                               {literal}{{ ((((row.minPoint / law.pointsTotal) * 100).toFixed(2))||0) + ' - ' + ((row.maxPoint / law.pointsTotal) * 100).toFixed(2) }}{/literal}
                            </td>
                            <td>
                                <button type="button" class="btn btn-warning btn-sm" v-if="row == lastClass"
                                        :disabled="$v.law.classes.$each[index].$invalid"
                                        @click="row.edit = !row.edit">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button type="button" v-if="row == lastClass"
                                        @click="removelastClass"
                                        class="btn btn-danger btn-sm">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="6">
                                <button type="button" @click="pushNewClass" class="btn btn-sm btn-success"
                                        v-if="law.classes.length > 0">
                                    <i class="fa fa-plus"></i> {#p_add_new_class#}
                                </button>
                                <span v-if="maxAllowedFields" class="has-error">{#max_allowed_fields#}</span>
                            </td>
                        </tr>
                        </tfoot>
                    </table>

                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                    </div>
                </div>
            </form>
        </law-classes>
    </div>
{/block}
{block name=back}{url urltype=path url_string="ben/P269/ExchangeRuleSettings/show/0/{$smarty.session.lang}"}{/block}
{block name=page_header}
    <script>
        var law = {$law}
        var ruleActionTypes = {$ruleActionTypes}
    </script>
{/block}
