{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_activation#}: {getname table=assist_exch_rules id=$standard->rule_id} ({t v=$standard->type})</h4>
    </div>
    <div class="modal-body">
        <form method="post" action='{url urltype="path" url_string="ben/P269/ExchangeRuleSettings/standards/0/{$smarty.session.lang}/changeStatus/{$smarty.session.s_ExchangeRuleSettings_token}/{$standard->id}"}'>
            <div class="row snsowraper">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_activation#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {foreach $activation_options as $option}
                        <div class="radio">
                            <label>
                                <input name="is_active" value="{$option->id}" type="radio" {if $option->id eq $standard->is_active}checked{/if}>
                                <span class="text">{$option->translatedName}</span>
                            </label>
                        </div>
                    {/foreach}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
{block name=footer}
    <script>
        resize_modal('lg');

    </script>
{/block}