{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_header}
    <script>
        var classes = {$classes}
            classes.map(function (standardClass) {
                standardClass.edit = false
            })
    </script>
    <script src="/templates/assets/resources/dist/app.js"></script>
{/block}
{block name=body}
    <div class="page-header">
        <h4 class="title">{#p_set_criterion#} {t v=$standard->type}</h4>
    </div>
        <standard-classes inline-template>
            <form method="post" ref="form" @submit.prevent="submit"
                  action='{url urltype="path" url_string="ben/P269/ExchangeRuleSettings/standards/0/{$smarty.session.lang}/updateStandardDynamicData/{$smarty.session.s_ExchangeRuleSettings_token}/{$standard->id}"}'>
                <div class="row snsowraper">

                    <h4>{#p_standard_classes#}</h4>
                    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                        <thead>
                        <tr>
                            <th>{t v=$standard->type}</th>
                            <th>{#p_class_points#}</th>
                            <th>{#gnr_settings#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(row,index) in standard.classes">
                            <td>
                                {#gnr_from#}:
                                <input type="number" v-show="row.edit && row == lastClass"
                                       :name="'arrayOfValues['+index+'][points_min]'"
                                       min="0" v-model="row.points_min" @input="checkMin"
                                       :class="{ 'has-error':$v.standard.classes.$each[index].points_min.$error }">
                                <span v-if="$v.standard.classes.$each[index].points_min.$error"
                                      :class="{ 'has-error':$v.standard.classes.$each[index].points_min.$error }">{#p_required_field#}</span>
                                <span v-text="row.points_min" v-if="!row.edit ||  row != lastClass"></span>
                                <span v-if="index == 0">{#gnr_to#}</span>
                                <span v-else>{#gnr_to_less_than#}</span>:
                                <input type="number" min="1" v-model="row.points_max"
                                       :name="'arrayOfValues['+index+'][points_max]'"
                                       @input="checkMax(row,index)"
                                       v-show="row.edit && row == lastClass"
                                       :class="{ 'has-error':$v.standard.classes.$each[index].points_max.$error }">
                                <span v-if="$v.standard.classes.$each[index].points_max.$error"
                                      :class="{ 'has-error':$v.standard.classes.$each[index].points_max.$error }">{#p_required_field#}</span>
                                <span v-text="row.points_max" v-if="!row.edit ||  row != lastClass"></span>
                            </td>
                            <td>
                                <input v-show="row.edit && row == lastClass"
                                       type="number" v-model="row.points"
                                       :class="{ 'has-error':$v.standard.classes.$each[index].points.$error }"
                                       :name="'arrayOfValues['+index+'][points]'">
                                <span v-if="$v.standard.classes.$each[index].points.$error"
                                      :class="{ 'has-error':$v.standard.classes.$each[index].points.$error }">{#p_required_field#}</span>

                                <span v-text="row.points" v-if="!row.edit ||  row != lastClass"></span>
                            </td>
                            <td>
                                <button type="button" class="btn btn-warning btn-sm" v-if="row == lastClass"
                                        :disabled="$v.standard.classes.$each[index].$invalid"
                                        @click="toggleEdit(row)">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button type="button" v-if="row == lastClass"
                                        @click="removelastClass"
                                        class="btn btn-danger btn-sm">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="6">
                                <button type="button" @click="newClass" class="btn btn-sm btn-success">
                                    <i class="fa fa-plus"></i> {#p_add_new_class#}
                                </button>
                                <span v-if="maxAllowedFields" class="has-error">{#max_allowed_fields#}</span>
                            </td>
                        </tr>
                        </tfoot>
                    </table>

                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                    </div>
                </div>
            </form>
        </standard-classes>
{/block}
{block name=back}{url urltype=path url_string="ben/P269/ExchangeRuleSettings/standards/0/{$smarty.session.lang}"}{/block}
