{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_view_case#}: {$case->name}</h4>
    </div>
    <div class="modal-body" id="root">
        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_exchange_rule_name#}</div>
        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
            @{ name }
        </div>

        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_exchange_points_sum#}</div>
        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
            @{ total_points_sum }
        </div>

        <h4>{#p_exchange_classes#}</h4>
        <table class="table table-striped table-bordered">
            <thead>
            <tr>
                <th width="10%">{#p_class_code#}</th>
                <th width="15%">{#p_class_name#}</th>
                <th width="15%">{#p_class_action#}</th>
                <th width="30%">{#p_class_points#}</th>
                <th width="15%">{#p_class_percentage#}</th>
            </tr>
            </thead>
            <tbody>
            <tr is="class-component" @class-removed="removeClass($event)" :total="total_points_sum" v-for="item in classes" :item="item" :key="item"></tr>
            </tbody>
        </table>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
{block name=footer}
    <script>
        resize_modal('lg');

        // Shared data

        select_options = JSON.parse('{$ruleActionTypes}');
        let data = {
            from_text: '{#gnr_from#}',
            to_text: '{#gnr_to#}',
            save_text: '{#gnr_save#}',
            first_class_text: '{#p_highest_value#}',
            first_class_code: '{#gnr_a_letter#}',
            select_options: select_options,
        };

        // Temporary use in funny situation :)
        let newClass = {  };

        let rule = JSON.parse('{$rule}');
        let classes = JSON.parse('{$ruleClasses}');

        {literal}
        Vue.component('class-component', {
            props: ['total', 'item'],
            template: `
            <tr>
                <td class="text-center">
                    <span>{{ newClass.code }}</span>
                </td>
                <td class="text-center">
                    <span>{{ newClass.name }}</span>
                </td>
                <td class="text-center">
                    <span>{{ newClass.action | actionName }}</span>
                </td>
                <td class="text-center">
                    <table>
                        <tbody>
                            <td class="text-center">
                                {{ exData.from_text }}:
                                <span>{{ newClass.points_min }}</span>
                            </td>
                            <td class="text-center">
                                 | {{ exData.to_text }}:
                                <span>{{ newClass.points_max }}</span>
                            </td>
                        </tbody>
                    </table>
                </td>
                <td class="text-center">
                    {{ min }} - {{ max }}
                </td>
            </tr>
            `,
            data() {
                return {
                    newClass: {},
                    exData: data,
                    saved: false,
                    save_button_class: '',
                }
            },
            created() {
                if ( ! jQuery.isEmptyObject(newClass)) {
                    this.newClass = newClass;
                    newClass = {};
                } else if ( ! jQuery.isEmptyObject(this.item)) {
                    this.newClass = this.item
                } else {
                    this.newClass = {};
                }
            },
            computed: {
                min() {
                    if ( ! jQuery.isEmptyObject(this.newClass)) {
                        return (this.newClass.points_min * 100 / this.total).toFixed(1);
                    } else {
                        return 0;
                    }
                },
                max() {
                    if ( ! jQuery.isEmptyObject(this.newClass)) {
                        return (this.newClass.points_max * 100 / this.total).toFixed(1);
                    } else {
                        return 0;
                    }
                },
            },
            methods: {
                saveClass() {
                    this.saved = true;
                    this.save_button_class = 'disabled';
                }
            },
            filters: {
                actionName(action_id) {
                    let action = _.findWhere(select_options, {id: action_id.toString()});
                    if (jQuery.isEmptyObject(action)) {
                        return '';
                    } else {
                        return action.name;
                    }
                }
            }

        });

        new Vue({
            el: '#root',
            delimiters: ['@{', '}'],
            data: {
                classes: [],
                exData: data,
                total_points_sum: 0,
                name: '',
                button_update: '',
                submitted: ''
            },
            created() {
                this.name = rule.assist_exch_rules_name;
                this.total_points_sum = rule.assist_exch_rules_points_sum;
                this.classes = classes;
            },
            computed: {
                classesIsEmpty() {
                    return this.classes.length === 0;
                }
            },
            methods: {
                onSubmit() {
                    this.submitted = 'disabled';

                    let params = new URLSearchParams();
                    params.append('classes', JSON.stringify(this.classes));
                    params.append('name', this.name);
                    params.append('points_sum', this.total_points_sum);

                    axios.put('/api.php/assist/rules/' + rule.assist_exch_rules_id, params)
                        .then(res => {
                            if (res.data.success) {
                                location.reload();
                            } else {
                                this.submitted = '';
                            }
                        });
                },
                addNewClass() {
                    this.button_update = 'disabled';
                    newClass = {
                        code: this.exData.first_class_code,
                        name: this.exData.first_class_text,
                        action: this.exData.select_options[0],
                        points_min: 0,
                        points_max: this.total_points_sum
                    };
                    this.classes.push(newClass);
                },
                addClass() {
                    newClass = {
                        points_max: this.classes[this.classes.length - 1].points_min,
                        points_min: 0
                    };
                    this.classes.push(newClass)
                },
                removeClass(item) {
                    let index = this.classes.indexOf(item);
                    this.classes.splice(index, 1);
                }
            }
        });
        {/literal}
    </script>
{/block}