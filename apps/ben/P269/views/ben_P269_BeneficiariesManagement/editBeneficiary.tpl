{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_beneficiary#}</h4>
    </div>
    <div class="modal-body">
        <form  method="post" action='{url urltype="path" url_string="ben/P269/BeneficiariesManagement/show/0/{$smarty.session.lang}/update/{$smarty.session.s_BeneficiariesManagement_token}/{$user_class}/{$user->id}"}'>
            <div class="row snsowraper">

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_first_name#}{sup}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control"
                                                                                    value="{$user->fr_name}"
                                                                                    name="fr_name" required>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_second_name#}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="secd_name" value="{$user->secd_name}"> </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_third_name#}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="thrd_name" value="{$user->thrd_name}"> </div>


                <div class="col-lg-1 col-md-1 col-sm-12"></div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_family_name#}{sup}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" value="{$user->fm_name}"
                                                                                    name="fm_name" required>
                </div>

                <div class="col-lg-1 col-md-1 col-sm-12"></div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_sex#}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                    <div class="control-group">
                        {foreach $sex_list as $srow}
                            {if $srow->id ne 53}
                                <div class="radio">
                                    <label>
                                        <input name="gender" value="{$srow->id}" {if $srow->id eq $user->gender} checked {/if}type="radio">
                                        <span class="text">{$srow->translatedName}</span>
                                    </label>
                                </div>
                            {/if}
                        {/foreach}
                    </div>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_category#}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                    <div class="control-group">
                        {foreach $classifications as $class}
                            <div class="checkbox">
                                <label>
                                    <input name="classification[]" class="checkboxoption" value="{$class->id}" type="checkbox" {if in_array($class->id, explode(',',$user->classification))} checked="checked" {/if}>
                                    <span class="text">{getname table=sh_userclasses id=$class->id}</span>
                                </label>
                            </div>
                        {/foreach}
                    </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button>
                    <span class="danger" id="submitMessage"></span>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
