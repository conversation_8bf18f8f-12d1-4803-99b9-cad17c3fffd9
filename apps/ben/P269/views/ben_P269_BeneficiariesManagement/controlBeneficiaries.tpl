{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}

    <h5 class="row-title before-darkorange">{$user->full_name}</h5>    

    <div class="tabbable">
        <ul class="nav nav-tabs nav-justified" id="myTab5">
            <li class="{if $smarty.session.s_control_beneficiaries_tab eq 'members'} active {/if}">
                <a data-toggle="tab" href="#members">{#p_individuals#}</a>
            </li>

            <li class="tab-red {if $smarty.session.s_control_beneficiaries_tab eq 'families'} active {/if}">
                <a data-toggle="tab" href="#families">{#p_families#}</a>
            </li>
        </ul>

        <div class="tab-content">
            <div id="members" class="tab-pane {if $smarty.session.s_control_beneficiaries_tab eq 'members'} in active {/if}">
                <table class="table table-striped table-bordered">
                    <thead>
                    <tr  style="background-color: #A0D468 !important;" role="row">
                        <th width="5%" style="background-color: #A0D468 !important;">
                            {url urltype=madd url_string="ben/P269/BeneficiariesManagement/add/0/{$smarty.session.lang}/{$user->id}"}
                        </th>
                        <th width="65%" style="background-color: #A0D468 !important;">{#gnr_name#}</th>
                        <th width="30%" style="background-color: #A0D468 !important;">{#gnr_settings#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $beneficiaries as $beneficiary}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td nowrap="nowrap">
                                <a data-toggle="modal" data-target="#modal" href="{url check=0 urltype="path" url_string="gnr/X000/resume/show/0/{$smarty.session.lang}/{$beneficiary->id}"}" class="btn btn-default btn-sm">
                                    <i class='fa fa-user'></i>
                                </a>
                                {$beneficiary->full_name}
                            </td>
                            <td align="center">
                                {url urltype=mdelete url_string="ben/P269/BeneficiariesManagement/confirm/0/{$smarty.session.lang}/{$beneficiary->id}"}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>

            <div id="families" class="tab-pane {if $smarty.session.s_control_beneficiaries_tab eq 'families'} in active {/if}">
                <table class="table table-striped table-bordered">
                    <thead>
                    <tr  style="background-color: #A0D468 !important;" role="row">
                        <th width="5%" style="background-color: #A0D468 !important;">{url urltype=madd url_string="ben/P269/BeneficiariesManagement/addFamily/0/{$smarty.session.lang}/{$user->id}"}</th>
                        <th width="25%" style="background-color: #A0D468 !important;">{#p_husband_name#}</th>
                        <th width="25%" style="background-color: #A0D468 !important;">{#p_wife_name#}</th>
                        <th width="25%" style="background-color: #A0D468 !important;">{#p_file_number#}</th>
                        <th width="20%" style="background-color: #A0D468 !important;">{#gnr_settings#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $families as $family}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td nowrap="nowrap">{getname table=sh_user id=$family->assist_families_husband_id}</td>
                            <td nowrap="nowrap">{getname table=sh_user id=$family->assist_families_wife_id}</td>
                            <td nowrap="nowrap">{$family->assist_families_file_number}</td>
                            <td align="center">
                                {url urltype=mdelete url_string="ben/P269/BeneficiariesManagement/confirmFamily/0/{$smarty.session.lang}/{$family->assist_families_id}"}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

{/block}
{block name=back}{url urltype=path url_string="ben/P269/BeneficiariesManagement/show/0/{$smarty.session.lang}"}{/block}