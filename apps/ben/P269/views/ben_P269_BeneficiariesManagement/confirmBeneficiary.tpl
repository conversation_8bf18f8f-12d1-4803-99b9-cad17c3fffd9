{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_archieve_user#}</h4>
    </div>
    <div class="modal-body">
        <div class="row text-center">
            <h6 class="danger text-center">{#p_delete_user_confirmation#}</h6>
            <br>
            {$user->full_name}<br><br>

            {if User::canDeleteUserOrDeActiveHim($user->id)}
                {url urltype="button" url_string="ben/P269/BeneficiariesManagement/show/0/{$smarty.session.lang}/archive/{$smarty.session.s_BeneficiariesManagement_token}/{$user_class}/{$user->id}" text_value="{#gnr_archive_user#}"}
            {else}
                {url urltype="button" url_string="ben/P269/BeneficiariesManagement/show/0/{$smarty.session.lang}/archive/{$smarty.session.s_BeneficiariesManagement_token}/{$user_class}/{$user->id}" text_value= "{#gnr_archive_user#}"}
            {/if}

        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}