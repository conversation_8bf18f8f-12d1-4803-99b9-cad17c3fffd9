{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#gnr_reset_password#}</h4>
	</div>
	<div class="modal-body">
		<div class="row">
			<form  method="post" action='{url urltype="path" url_string="ben/P269/BeneficiariesManagement/show/0/{$smarty.session.lang}/resendlogindata/{$user->id}/{$smarty.session.s_BeneficiariesManagement_token}/{$user_class}"}'>
				<div class="row snsowraper">
					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_first_name#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$user->fr_name}</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_family_name#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$user->fm_name}</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_appearance_name#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$user->show_name_text}</div>

					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_send_throw#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						<div class="control-group">

							<div class="radio">
								{if !empty($user->email)}
									<label>
										<input name="chanel_type" value="1" type="radio" checked>
										<span class="text">
											{#gnr_email#}
											&nbsp;->&nbsp;
											{$user->email}
										</span>
										</label>
								{else}
									<label class="gray">
										<i class="fa fa-ban" ></i>
										<span class="text">{#gnr_email#}</span>
									</label>
								{/if}
							</div>
							<div class="radio">
								{if !empty($user->tell)}
									<label>
										<input name="chanel_type" value="2" type="radio">
										<span class="text">
											{#gnr_mobile#}
											&nbsp;->&nbsp;
											{$user->tell}
										</span>
									</label>
								{else}
									<label class="gray">
										<i class="fa fa-ban" ></i>
										<span class="text">{#gnr_mobile#}</span>
									</label>
								{/if}

							</div>
						</div>
					</div>

					{if !empty($user->email) || !empty($user->tell)}
						<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
						<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp" >{#gnr_send#}</button></div>

						<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
						<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsolabel bg-darkorange white padding-5">{#p_password_will_be_sent_throw_sms_or_email_based_send_throw_value#}</div>
					{else}
						<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
						<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsolabel bg-danger white padding-5">{#p_no_user_email_or_phone_would_not_be_able_to_login#}</div>
					{/if}

				</div>
			</form>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}