{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=title}{#gnr_user#}{/block}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />{/block}
{block name=page_body}
    <div class="margin-bottom-20">
            {url urltype="mbutton" check=0 url_string="gnr/X000/mediacenter/createNewBeneficiary/0/{$smarty.session.lang}/formUrl/ben/P269/BeneficiariesManagement/show/0/{$smarty.session.lang}/createNewBeneficiary/{$smarty.session.s_BeneficiariesManagement_token}" text_value="{#p_add_new_beneficiary#}" style="btn btn-default shiny"}
    </div>
    <div class="widget">
        <div class="widget-header bg-blue">
            <i class="widget-icon fa fa-arrow-left"></i>
            <span class="widget-caption">{#gnr_search#}</span>
            <div class="widget-buttons">
                <div class="widget-buttons">
                    <a href="#" data-toggle="collapse">
                    </a>
                </div>
            </div>
        </div>
        <div class="widget-body">

            <form method="post"
                  action='{url urltype="path" url_string="ben/P269/BeneficiariesManagement/show/0/{$smarty.session.lang}/search"}'>

                <div class="row">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_sex#}</div>
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <div class="radio">
                                    <label>
                                        <input value="51" {if $smarty.session.searchArray['gender'] eq 51 } checked {/if} type="radio" name="gender">
                                        <span class="text">{#gnr_male#}</span>
                                    </label>
                                    <label>
                                        <input value="52" {if $smarty.session.searchArray['gender'] eq 52 } checked {/if} type="radio" name="gender">
                                        <span class="text">{#gnr_female#}</span>
                                    </label>
                                </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_social_state#}</div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                        <select name="social_state" placeholder="{#gnr_select_from_list_bellow#}">
                            <option></option>
                            {foreach key=id item=srow from=$socialList}
                                <option value="{$srow->id}" {if $smarty.session.searchArray['social_state'] eq $srow->id }selected{/if}>{$srow->translatedName}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>

                <div class="row">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_age#}</div>
                    <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="number" name="age" value="{$smarty.session.searchArray['age']}"
                                                                                    placeholder="{#gnr_age#}"></div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_housing#}</div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 pr-0 snsoinput">
                        <select name="housing_type" placeholder="{#gnr_select_from_list_bellow#}">
                            <option></option>
                            {foreach $housingTypeList as $key => $value}
                                <option value="{$key}" {if $smarty.session.searchArray['housing_type'] eq $key }selected{/if}>{$value}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_health_state#}</div>
                    <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 pr-0 snsoinput">
                        <select name="health_status" placeholder="{#gnr_select_from_list_bellow#}">
                            <option></option>
                            {foreach $healthStatus as $key => $value}
                                <option value="{$key}" {if  $smarty.session.searchArray['health_status'] eq $key} selected{/if}>{$value}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_life_state#}</div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">

                        <div class="control-group">
                            <div class="radio">
                                <label>
                                    <input value="1274"
                                           type="radio" {if $smarty.session.searchArray['live_status'] eq 1274} checked {/if}
                                           name="live_status">
                                    <span class="text">{#p_user_live#}</span>
                                </label>
                            </div>
                            <div class="radio">
                                <label>
                                    <input value="1275" type="radio" {if $smarty.session.searchArray['live_status'] eq 1275} checked {/if} name="live_status">
                                    <span class="text">{#p_user_dead#}</span>
                                </label>
                            </div>
                        </div>

                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_work#}</div>
                    <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 snsoinput">
                        <select name="work" placeholder="{#gnr_select_from_list_bellow#}">
                            <option></option>
                            {foreach $workStatus as $status}
                                <option value="{$status->id}" {if $smarty.session.searchArray['work'] eq $status->id} selected {/if}>{$status->translatedName}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_beneficiary_type#}</div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">

                        <div class="control-group">
                            <div class="radio">
                                <label>
                                    <input value="124"
                                           type="radio" {if $smarty.session.searchArray['beneficiary_type'] eq 124} checked {/if}
                                           name="beneficiary_type">
                                    <span class="text">{#p_independent_beneficiary#}</span>
                                </label>
                            </div>
                            <div class="radio">
                                <label>
                                    <input value="123" type="radio" {if $smarty.session.searchArray['beneficiary_type'] eq 123} checked {/if} name="beneficiary_type">
                                    <span class="text">{#p_beneficiay_in_family#}</span>
                                </label>
                            </div>
                        </div>

                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family_file#}</div>
                    <div class="col-lg-3 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="number" name="family_file" value="{$smarty.session.searchArray['family_file']}"
                                                                                        placeholder="{#p_family_file#}"></div>


                </div>
                <button type="submit" class="btn btn-success sharp">{#gnr_view#}</button>
                {if $smarty.session.searchArray}
                    {url check=0 urltype="button" style="btn btn-default shiny" url_string="ben/P269/BeneficiariesManagement/show/0/{$smarty.session.lang}/menu" text_value="{#gnr_cancel#} {#gnr_search#}"}
                {/if}
            </form>
        </div>
    </div>
        {if $users}
            <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
            <thead>
                <tr style="background-color: #A0D468 !important;" >
                    <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                    <th style="background-color: #A0D468 !important;" width="5%"></th>
                    <th style="background-color: #A0D468 !important;" width="30%">{#gnr_name#}</th>
                    <th style="background-color: #A0D468 !important;" width="15%">{#gnr_status#}</th>
                    <th style="background-color: #A0D468 !important;" width="45%">{#gnr_settings#}</th>
                </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $users as $user}
                <tr {if $user->activation_status eq 0}class="gray"{/if}>
                    <td align="center">{$i++}</td>
                    <td>
                        {if $user->family_id}
                            {url urltype="button" check=0 oprvtype=3 opr_code="employees" url_string="ben/P269/AssistanceFamiliesManagement/controlmembers/0/{$smarty.session.lang}/save_session/{$user->family_id}/" text_value='&nbsp;<i class="fa fa-users"></i>' modal="modal" style="btn btn-sky shiny"}
                            &nbsp;
                        {/if}
                    </td>
                    <td nowrap="nowrap">

                        {url urltype="button" check=0 oprvtype=3 opr_code="employees" url_string="gnr/X000/resume/edit/0/{$smarty.session.lang}/save_session/{$user->id}/ben/P269/BeneficiariesManagement/show/0/{$smarty.session.lang}"  text_value="<i class='fa fa-file-text'></i>&nbsp;{#gnr_data#}"}
                        &nbsp
                        {$user->full_name}
                    </td>
                    <td align="center">
                        {if $user->id neq 1 and $user->id neq 2}
                            {url check=0 oprvtype=3 opr_code='BeneficiariesManagement' urltype="mbutton" url_string="ben/P269/BeneficiariesManagement/activateuser/0/{$smarty.session.lang}/{$smarty.session.s_users_class_id}/{$user->id}" text_value="{#gnr_activation#}"}
                            {if $user->activation_status eq 1}
                                <i class="fa fa-check green" ></i>
                            {/if}
                            {if $user->activation_status eq 0}
                                <i class="fa fa-ban gray" ></i>
                            {/if}
                            {if $user->activation_status eq 1}{#gnr_active_male#}{/if}
                            {if $user->activation_status eq 0}{#gnr_not_active_male#}{/if}
                        {/if}
                    </td>
                    <td align="center">
                        {if $user->id neq 1 and $user->id neq 2}
                            {url check=0 urltype="mbutton" url_string="bsc/P052/users/contactsinfo/0/{$smarty.session.lang}/{$user->id}" text_value="<i class='fa fa-envelope-o'></i>&nbsp;<i class='fa fa-mobile'></i>"}
                            {url check=0 oprvtype=3 opr_code='BeneficiariesManagement' urltype="mbutton" url_string="ben/P269/BeneficiariesManagement/resetlogindata/0/{$smarty.session.lang}/employee/{$user->id}" text_value="{#gnr_adjust_login_data#}"}
                            {url urltype=button url_string="ben/P269/BeneficiariesManagement/constraints/0/{$smarty.session.lang}/save_session/{$user->id}" text_value="{#p_sponsership_constraints#}"}
                            {url check=1 urltype=medit opr_code='BeneficiariesManagement' url_string="ben/P269/BeneficiariesManagement/editBeneficiary/0/{$smarty.session.lang}/{$smarty.session.s_users_class_id}/{$user->id}"}
                            {url check=0 urltype="modal" opr_code='BeneficiariesManagement' url_string="ben/P269/BeneficiariesManagement/confirmBeneficiary/0/{$smarty.session.lang}/{$smarty.session.s_users_class_id}/{$user->id}" text_value= "{#gnr_archive_user#}"}
                        {/if}
                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>
        {/if}
        {*<table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">*}
            {*<thead>*}
            {*<tr style="background-color: #A0D468 !important;" >*}
                {*<th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>*}
                {*<th style="background-color: #A0D468 !important;" width="45%">{#gnr_name#}</th>*}
                {*<th style="background-color: #A0D468 !important;" width="25%">{#gnr_email#}/ {#gnr_mobile_number#}</th>*}
                {*<th style="background-color: #A0D468 !important;" width="25%">{#gnr_settings#}</th>*}
            {*</tr>*}
            {*</thead>*}
            {*<tbody>*}
            {*{$i=1}*}
            {*{foreach $unclassified_list as $xrow}*}
                {*<tr>*}
                    {*<td align="center">{$i++}</td>*}
                    {*<td nowrap="nowrap">*}
                        {*{$xrow->full_name}*}
                    {*</td>*}
                    {*<td>{$user->email} / {$user->tell}</td>*}
                    {*<td align="center">*}
                        {*{url urltype=mbutton url_string="ben/P269/BeneficiariesManagement/browseBeneficiaries/0/{$smarty.session.lang}/{$user->id}" text_value="{#p_view_dependencies#}"}*}
                        {*{url urltype=button url_string="ben/P269/BeneficiariesManagement/browse/0/{$smarty.session.lang}/save_session/{$user->id}" text_value="{#p_beneficiary_panel#}"}*}
                    {*</td>*}
                {*</tr>*}
            {*{/foreach}*}
            {*</tbody>*}
        {*</table>*}

{/block}
{block name=page_header}
    <script type="text/javascript" src="/templates/assets/js/loader.js"></script>
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
        function exportTo(ID, type) {
            $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
        }
        {/literal}
    </script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 20,
                        "oTableTools": {
                            "aButtons": [

                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                    $("tfoot input").keyup(function() {
                        /* Filter on the column (the index) of this element */
                        oTable.fnFilter(this.value, $("tfoot input").index(this));
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
    <script>
        (function () {
            window.events.$on('beneficiaryCreated',function () {
                window.location.reload()
            })
        })()
    </script>
{/block}
