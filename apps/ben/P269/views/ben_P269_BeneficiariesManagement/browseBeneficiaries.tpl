{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_beneficiaries_for_user#}: {$user->full_name}</h4>
    </div>
    <div class="modal-body">
            <div class="tabbable">
        <ul class="nav nav-tabs nav-justified" id="myTab5">
            <li class="{if $smarty.session.s_control_beneficiaries_tab eq 'members'} active {/if}">
                <a data-toggle="tab" href="#members">{#p_individuals#}</a>
            </li>

            <li class="tab-red {if $smarty.session.s_control_beneficiaries_tab eq 'families'} active {/if}">
                <a data-toggle="tab" href="#families">{#p_families#}</a>
            </li>
        </ul>

        <div class="tab-content">
            <div id="members" class="tab-pane {if $smarty.session.s_control_beneficiaries_tab eq 'members'} in active {/if}">
                <table class="table table-striped table-bordered">
                    <thead>
                    <tr  style="background-color: #A0D468 !important;" role="row">
                        <th width="5%" style="background-color: #A0D468 !important;"></th>
                        <th width="65%" style="background-color: #A0D468 !important;">{#gnr_name#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $beneficiaries as $beneficiary}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td nowrap="nowrap">{$beneficiary->full_name}</td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>

            <div id="families" class="tab-pane {if $smarty.session.s_control_beneficiaries_tab eq 'families'} in active {/if}">
                <table class="table table-striped table-bordered">
                    <thead>
                    <tr  style="background-color: #A0D468 !important;" role="row">
                        <th width="5%" style="background-color: #A0D468 !important;"></th>
                        <th width="25%" style="background-color: #A0D468 !important;">{#p_husband_name#}</th>
                        <th width="25%" style="background-color: #A0D468 !important;">{#p_wife_name#}</th>
                        <th width="25%" style="background-color: #A0D468 !important;">{#p_file_number#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $families as $family}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td nowrap="nowrap">{getname table=sh_user id=$family->assist_families_husband_id}</td>
                            <td nowrap="nowrap">{getname table=sh_user id=$family->assist_families_wife_id}</td>
                            <td nowrap="nowrap">{$family->assist_families_file_number}</td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}