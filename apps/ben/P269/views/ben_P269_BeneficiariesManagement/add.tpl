{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_add_beneficiary#}</h4>
    </div>
    <div class="modal-body">
        <form  method="post" action='{url urltype="path" url_string="ben/P269/BeneficiariesManagement/controlBeneficiaries/0/{$smarty.session.lang}/{$user->id}/insert/{$smarty.session.s_BeneficiariesManagement_token}"}'>
            <div class="row snsowraper">
                <div class='col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel'>{#gnr_name#}</div>
                <div class='col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput'>
                    <select name="user_id" required>
                        <option value="">{#gnr_unspecified#}</option>
                        {foreach $beneficiaries as $beneficiary}
                            {if empty($beneficiary->belongs_to_user)}
                                <option value="{$beneficiary->id}">{$beneficiary->full_name}&nbsp;&raquo;&nbsp;{$beneficiary->identity_number}</option>
                            {/if}
                        {/foreach}
                    </select>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
