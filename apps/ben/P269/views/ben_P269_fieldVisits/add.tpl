{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>
{/block}
{block name=page_body}
    {assign var="doNotUseAppJs" value=1}
    <div style="padding-right: 10%">
        <h4 class="alert alert-info" style="width: 87%">{#p_add_new_visit#}</h4>
    </div>
    <div >
        <form method="post"
              action='{url urltype="path" url_string="ben/P269/fieldVisits/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_FieldVisits_token}"}'>
            <div x-data="VisitCreate()" class="row snsowraper" style = "background-color: white; border: 1px solid grey; width:80%; margin-right: 9%;   box-shadow: 8px 9px 4px 1px  rgba(104, 103, 103, 0.253);">
                <div class="mt-3 row col-lg-12">
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_type#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <div class="radio">
                                <label>
                                    <input name="data" type="radio" x-model="type" value="{AssistVisit::VISIT_NEW_DATA}">
                                    <span class="text">{#p_add_new_beneficiaries#}</span>
                                </label>
                                <br>

                                <label>
                                    <input name="data" type="radio" x-model="type" value="{AssistVisit::VISIT_UPDATE_DATA}">
                                    <span class="text">{#p_update_beneficiaries#}</span>
                                </label>
                            </div>
                        </div>
                    </div>

                <template x-if="parseInt(type) == 0">
                    <div>
                        <div class=" row col-lg-12">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_first_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                class="form-control"
                                                                                                name="fr_name">
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_father_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                class="form-control"
                                                                                                name="secd_name">
                            </div>
                        </div>
                        <div class="row col-lg-12">
                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_grandfather_name#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                    class="form-control"
                                                                                                    name="thrd_name">
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family_name#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                            class="form-control"
                                                                                                            name="fm_name">
                                </div>

                        </div>
                        <div class=" row col-lg-12" >
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_marital_status#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                <select name="social_status">
                                    <option value="{$user->social_status}"
                                            selected>{getname table=st_setting id=$user->social_status}</option>
                                    {foreach key=id item=srow from=$social_list}
                                        <option value="{$srow->id}">{$srow->translatedName}</option>
                                    {/foreach}
                                </select>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_nationality#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                <select name="nationality">
                                    <option value="{$user->nationality}"
                                            selected>{getname table=st_country id=$user->nationality}</option>
                                    {foreach key=id item=nrow from=$nationality_list}
                                        <option value="{$nrow->id}">{getname table=st_country id=$nrow->id}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="row col-lg-12" >
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_job_status#}</div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                        <select name="work_status">
                            <option value="{$user->work_status}"
                                    selected>{getname table=st_setting id=$user->work_status}</option>
                            {foreach key=id item=wrow from=$work_status_list}
                                <option value="{$wrow->id}">{$wrow->translatedName}</option>
                            {/foreach}
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_location#}</div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                        <select name="neighborhood_id">
                            <option>{#p_not_choose#}</option>
                            {foreach $neighborhoods as $neighborhood}
                                <option value="{$neighborhood->id}">{$neighborhood->region->name}&nbsp;&raquo;&nbsp;{$neighborhood->city->name}&nbsp;&raquo;&nbsp;{$neighborhood->name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                    </div>
                </template>


                    <div class="row col-lg-12">
                        <template x-if="parseInt(type) == 1">
                            <div>
                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family#}</div>
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                    <select name="family_id">
                                        <option>{#p_choose_family#}</option>
                                        {foreach $families as $family}
                                            <option value="{$family->id}">{getname table=sh_user id=$family->head_of_the_family}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                        </template>
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_date#}</div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {getdate table=assist_visit col=date type=add}
                        </div>
                    </div>
                </div>


                <div class="row col-lg-12">
                    <div class=" col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_recommendations#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <textarea name="recommendations" class="form-control" id=""></textarea>
                        </div>
                    </div>
                </div>
                <div class="row col-lg-12">
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_comments#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <textarea name="comment" class="form-control" id="" cols="30" rows="5"></textarea>
                        </div>
                    </div>
                </div>
                <div class="mb-3 row col-lg-12">
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

{/block}
{block name=back}{url urltype="path" url_string="ben/P269/fieldVisits/show/0/{$smarty.session.lang}/{$smarty.session.s_ledger_id}"}{/block}
{block name=page_header}
    <script src="/templates/assets/resources/js/alpine/components/AssistVisitForm.js"></script>
{/block}
