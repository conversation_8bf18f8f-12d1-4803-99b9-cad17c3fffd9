{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}
    <div class="row">
        <div class="col-md-12">
            <table class="table table-striped table-bordered table-hover no-footer">
                <thead>
                    <tr>
                        <th class="text-center" width="20%">{#p_beneficiarey_name#}</th>
                        <th class="text-center" width="20%">{#p_nationality#}</th>
                        <th class="text-center" width="10%">{#p_location#}</th>
                        <th class="text-center" width="20%">{#p_job_status#}</th>
                        <th class="text-center" width="10%">{#p_marital_status#}</th>
                        <th class="text-center" width="10%">{#p_file_number#}</th>
                        <th class="text-center" width="5%"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-center">{AssistanceFamilies::getFamilyName($visit->family_id)}</td>
                        <td class="text-center">{AssistanceFamilies::getUserNationality($visit->family_id)}</td>
                        <td class="text-center">{AssistanceFamilies::getUserNeighborhood($visit->family_id)}</td>
                        <td class="text-center">{AssistanceFamilies::getWorkStatus($visit->family_id)}</td>
                        <td class="text-center">{AssistanceFamilies::getSocialStatus($visit->family_id)}</td>
                        <td class="text-center">{AssistanceFamilies::getFileNumber($visit->family_id)}</td>
                        <td><a target="_blank" data-target="#modal" href="{url check=0 urltype="path" url_string="ben/P269/fieldVisits/print/0/{$smarty.session.lang}/save_session/{$visit->family_id}/{$visit->id}"}" class="btn btn-default btn-sm">{#gnr_print#}
                        </a></td>
                        </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="horizontal-space"></div>
    <div class="well">
        <form method="post"
              action='{url urltype="path" url_string="ben/P269/fieldVisits/details/0/{$smarty.session.lang}/visit/{$smarty.session.s_FieldVisits_token}/{$visit->id}"}'>
            {foreach $visits as $visit}
                <div class="row">
                    <div class="col-lg-12">
                        <br>
                        <br>
                        <br>
                        <br>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_date#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=assist_visit col=date type=show row=$visit}</div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_execution_status#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            {foreach $execution_status_list as $status}
                                {if $status->id eq $visit->status}{getname table=st_setting id=$status->id}{/if}
                            {/foreach}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_recommendations#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            {$visit->recommendations}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_comment#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            {$visit->comment}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_excutor#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                {getname table=sh_user id=$visit->executor}
                                {$visit->executor}
                        </div>

                    </div>

                </div>
            {/foreach}
        </form>
        {* //{/foreach}*}
    </div>
{/block}
{block name=back}{url urltype="path" url_string="ben/P269/fieldVisits/show/0/{$smarty.session.lang}"}{/block}