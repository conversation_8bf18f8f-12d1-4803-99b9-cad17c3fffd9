{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}
    <div class="horizontal-space"></div>
    <div class="row">
        <div class="col-lg-12">
            <div class="widget-body" style="padding: 2rem;">

                <form action="{url urltype=path url_string="ben/P269/fieldVisits/show/0/{$smarty.session.lang}/search"}"
                      method="post">

                    <div class="row">

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_beneficiarey_name#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            <select name="family_id">
                                <option></option>
                                {foreach $allVisits as $visit}
                                    {*{add if visit_family id not equal null}*}
                                    <option value="{$visit->familyObject->id}" {if $visit->family_id eq $smarty.session.s_visit_data['family_id'] and !$visit->family_id eq null} selected {/if}>
                                        {AssistanceFamilies::getFamilyName($visit->family_id)}
                                    </option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_type#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            <select name="type">
                                <option></option>

                                <option value="{AssistVisit::VISIT_NEW_DATA}" {if 0 eq $smarty.session.s_visit_data['type']} checked {/if}>
                                        {#p_add_new_beneficiaries#}
                                </option>
                                <option value="AssistVisit::VISIT_UPDATE_DATA" {if 1 eq $smarty.session.s_visit_data['type']} checked {/if}>{#p_update_beneficiaries#}</option>

                            </select>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_from#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {getdate type=report row={$smarty.session.s_visit_data['from']|default:''} col=from}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_to#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            {getdate type=report row={$smarty.session.s_visit_data['to']|default:''} col=to}
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_executer#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                                <select name="executer">
                                    <option value="">{#p_choose_employee#}</option>
                                    {foreach $users as $user}
                                        <option value="{$user->id}" {if $user->id eq $smarty.session.s_visit_data['executer']} checked {/if}>{$user->full_name}</option>
                                    {/foreach}
                                </select>
                        </div>


                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 p-0">
                            <input type="submit" class="btn btn-success sharp mr-4-px" name="submit"
                                   value="{#gnr_view#}">

                            {if $visits}
                                <a href="{url urltype=path url_string="ben/P269/fieldVisits/show/0/{$smarty.session.lang}/menu"}" class="btn btn-default shiny">
                                    {#gnr_cancel_search#}
                                </a>
                            {/if}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {*{if $smarty.session.s_employee_id}*}
        {*<br>*}
        {*<div class="row">*}
            {*<div class="col-lg-12">*}
                {*{url urltype="mbutton" check=0 url_string="ben/P269/fieldVisits/add/0/{$smarty.session.lang}" text_value="{#p_add_new_visit#}" style="btn btn-default shiny"}*}
            {*</div>*}
        {*</div>*}
        {*<br>*}
        <table class="table table-striped table-bordered table-hover no-footer">
            <thead>
            <tr>
                <th style="background-color: #A0D468 !important;" width="5%">{url check=1 opr_code="fieldVisits" urltype=add url_string="ben/P269/fieldVisits/add/0/{$smarty.session.lang}" modal=modal}</th>
                <th style="background-color: #A0D468 !important;" width="40%">{#p_beneficiarey_name#}</th>
                <th style="background-color: #A0D468 !important;" width="10%">{#p_visit_type#}</th>
                <th style="background-color: #A0D468 !important;" width="10%">{#p_visit_date#}</th>
                <th style="background-color: #A0D468 !important;" width="15%">{#p_executer#}
                    {*/ {#execution_status#}</th>*}
                <th style="background-color: #A0D468 !important;" width="10%">{#p_visit_detials#}</th>
                <th width="10%">{#gnr_settings#}</th>
            </tr>
            </thead>
            {$i=1}
            <tbody>
            {foreach $visits as $visit}
                {if !$visit->family->HeadOfFamily eq null}
                <tr>

                    {*<td class="text-center">{$i++}</td>*}
                    {*<td class="text-center">{getname table=sh_user id=$visit->familyObject->husband_id}</td>*}
                    {*<td class="text-center">{$visit->familyObject->executor}</td>*}
                    {*<td class="text-center">{getdate table=assist_visit col=date type=show row=$visit}</td>*}
                    {*<td class="text-center">*}
                        {*{getname table=st_setting id=$visit->type}*}
                        {*{if $visit->status eq 1051}*}
                            {*<span class="badge badge-warning">{#p_not_executed#}</span>*}
                        {*{elseif $visit->status eq 1052}*}
                            {*<span class="badge badge-info">{#p_under_execution#}</span>*}
                        {*{else}*}
                            {*<span class="badge badge-success">{#p_executed#}</span>*}
                        {*{/if}*}
                    {*</td>*}
                    {*<td class="text-center">*}
                        {*{if $visit->executor eq $smarty.session.user->id}*}
                            {*{url urltype="button" check=0 url_string="ben/P269/fieldVisits/details/0/{$smarty.session.lang}/visitData/{$visit->id}" text_value="{#p_visit_details#}" style="btn btn-default shiny"}*}
                        {*{else}*}
                            {*{url urltype="mbutton" check=0 url_string="gnr/X000/mediacenter/visitDetails/0/{$smarty.session.lang}/{$visit->id}" text_value="{#p_visit_details#}" style="btn btn-default shiny"}*}
                        {*{/if}*}
                    {*</td>*}
                    {*<td class="text-center">*}
                        {*{if $visit->executor eq $smarty.session.user->id}*}
                            {*{url check=0 urltype="medit" url_string="ben/P269/fieldVisits/edit/0/{$smarty.session.lang}/{$visit->id}"}*}
                            {*{url check=0 urltype="mdelete" url_string="ben/P269/fieldVisits/confirm/0/{$smarty.session.lang}/{$visit->id}"}*}
                        {*{/if}*}
                    {*</td>*}
                    <td class="text-center">{$i++}</td>
                    <td class="text-center">{$visit->family->HeadOfFamily->full_name}</td>
                    {*<td class="text-center">{$user->fr_name}</td>*}
                    <td class="text-center">
                        {if $visit->data == AssistVisit::VISIT_NEW_DATA}
                            {#p_add_new_beneficiaries#}
                        {else}
                            {#p_update_beneficiaries#}
                        {/if}
                    </td>
                    <td class="text-center">{$visit->date}</td>
                    <td class="text-center">{$visit->executor->full_name}</td>

                    {*{if $user->id eq $smarty.session.s_visit_data['executer']} checked {/if}>{$user->full_name}*}

                    <td class="text-center">
                        {if $visit->executor->id eq $smarty.session.user->id}
                            {url check=0 urltype="new" opr_code='fieldVisit' url_string="ben/P269/fieldVisits/details/0/{$smarty.session.lang}/visitData/{$visit->id}" text_value="<i class='fa fa-file-o'></i>"}
                        {else}
                            {url urltype="modal" check=0 url_string="gnr/X000/mediacenter/visitDetails/0/{$smarty.session.lang}/{$visit->id}" text_value="{#p_visit_details#}" style="btn btn-default shiny"}
                        {/if}
                    </td>
                    <td class="text-center">
                        {*{if $visit->executor eq $smarty.session.user->id}*}
                            {*{if $visit->data == AssistVisit::VISIT_NEW_DATA}*}
                                {if $visit->isLastVisit()}
                                    {url check=0 urltype="medit" url_string="ben/P269/fieldVisits/edit/0/{$smarty.session.lang}/{$visit->id}"}
                                {/if}
                                {url check=0 urltype="mdelete" url_string="ben/P269/fieldVisits/confirm/0/{$smarty.session.lang}/{$visit->id}"}
                            {*{/if}*}
                        {*{/if}*}
                    </td>
                </tr>
                {/if}
            {/foreach}
            </tbody>
        </table>




    {*{else}*}
        {*<div class="alert alert-warning">*}
            {*{#p_please_select_employee_first#}*}
        {*</div>*}
    {*{/if}*}

{/block}