{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_visit#}</h4>
    </div>
    <div class="modal-body">
        <form method="post" action='{url urltype="path" url_string="ben/P269/fieldVisits/show/0/{$smarty.session.lang}/update/{$smarty.session.s_FieldVisits_token}/{$visit->id}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">

                    <div>
                        <div class=" row col-lg-12">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_first_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                class="form-control"
                                                                                                name="fr_name"
                                                                                                value="{$user->fr_name}">
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_father_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                class="form-control"
                                                                                                name="secd_name"
                                                                                                value="{$user->secd_name}">
                            </div>
                        </div>
                        <div class="row col-lg-12">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_grandfather_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                class="form-control"
                                                                                                name="thrd_name"
                                                                                                value="{$user->thrd_name}">
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family_name#}</div>
                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                class="form-control"
                                                                                                name="fm_name"
                                                                                                value="{$user->fm_name}">
                            </div>
                        </div>
                    </div>

                    <div class=" row col-lg-12" >
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_marital_status#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            <select name="social_status">
                                {foreach $social_list as  $social}
                                    <option value="{$social->id}" {if $social->id eq $user->social_status}selected{/if}>{$social->translatedName}</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_nationality#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            <select name="nationality">
                                {foreach $nationality_list as $nationality}
                                    <option value="{$nationality->id}" {if $nationality->id eq $user->nationality}selected{/if}>{$nationality->translatedName}</option>
                                {/foreach}
                            </select>

                        </div>
                    </div>
                    <div class="row col-lg-12" >
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_job_status#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            <select name="work_status">
                                {foreach $work_status_list as  $work}
                                    <option value="{$work->id}" {if $work->id eq $user->work_status}selected{/if}>{$work->translatedName}</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_location#}</div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput">
                            <select name="neighborhood_id">
                                {foreach $neighborhoods as $neighborhood}
                                    <option value="{$neighborhood->id}">{$neighborhood->region->name}&nbsp;&raquo;&nbsp;{$neighborhood->city->name}&nbsp;&raquo;&nbsp;{$neighborhood->name}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>

                    {*<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_family#}</div>*}
                    {*<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">*}
                        {*<select name="family_id" id="">*}
                            {*<option value=""></option>*}
                            {*{foreach $families as $family}*}
                                {*<option value="{$family->id}" {if $visit->family_id eq $family->id} selected {/if} >[&nbsp;{$family->file_number}&nbsp;]&nbsp;{getname table=sh_user id=$family->husband_id}&nbsp;&raquo;&nbsp;{getname table=sh_user id=$family->wife_id}</option>*}
                            {*{/foreach}*}
                        {*</select>*}
                    {*</div>*}

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_execution_status#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        {*<select name="status" class="form-control">*}
                        <div class="radio">
                            <div class="control-group">
                                {foreach $execution_status_list as $status}
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="execution"
                                                   value="{$status->id}" {if $status->id eq $visit->status} checked="checked" {/if}>
                                            <span class="text">
                                                {if $status->id eq 1051}
                                                    <font color="gray" size="2">{$status->translatedName}<i
                                                        class="fa fa-times-circle-o"></i></font>
                                                    <br>
                                                {/if}
                                                {if $status->id eq 1052}
                                                    <font color="orange"
                                                                  size="2">{$status->translatedName}<i
                                                                        class="fa fa-dot-circle-o"></i></font>
                                                    <br>
                                                {/if}
                                                {if $status->id eq 1053}
                                                    <font color="green"
                                                      size="2">{$status->translatedName}<i
                                                            class="fa fa-check-circle-o"></i></font>
                                                    <br>
                                                {/if}
                                            </span>
                                        </label>
                                    </div>
                                {/foreach}
                            </div>
                                {*<option value=""></option>*}
                                {*{foreach $execution_status_list as $status}*}
                                    {*<option value="{$status->id}" {if $status->id eq $visit->status} selected {/if}>{$status->translatedName}</option>*}
                                {*{/foreach}*}
                                {*</select>*}
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_date#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {getdate table=assist_visit col=date type=edit row=$visit}
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_recommendations#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <textarea name="recommendations" class="form-control" id="" cols="30" rows="5">{$visit->recommendations}</textarea>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_comments#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <textarea name="comment" class="form-control" id="" cols="30" rows="5">{$visit->comment}</textarea>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-success sharp">{#gnr_update#}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
