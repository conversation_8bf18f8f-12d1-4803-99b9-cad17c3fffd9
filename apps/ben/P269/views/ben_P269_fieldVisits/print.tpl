{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=title}

{/block}
{block name=body}
    <div class="row">
        <div class="col-md-12">
            <table class="table table-striped table-bordered table-hover no-footer">
                <thead>
                <tr>
                    <th class="text-center" width="20%">{#p_beneficiarey_name#}</th>
                    <th class="text-center" width="20%">{#p_nationality#}</th>
                    <th class="text-center" width="10%">{#p_location#}</th>
                    <th class="text-center" width="20%">{#p_job_status#}</th>
                    <th class="text-center" width="10%">{#p_marital_status#}</th>
                    <th class="text-center" width="10%">{#p_file_number#}</th>
                    {*<th class="text-center" width="5%"></th>*}
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td class="text-center">{AssistanceFamilies::getFamilyName($family_id)}</td>
                    <td class="text-center">{AssistanceFamilies::getUserNationality($family_id)}</td>
                    <td class="text-center">{AssistanceFamilies::getUserNeighborhood($family_id)}</td>
                    <td class="text-center">{AssistanceFamilies::getWorkStatus($family_id)}</td>
                    <td class="text-center">{AssistanceFamilies::getSocialStatus($family_id)}</td>
                    <td class="text-center">{AssistanceFamilies::getFileNumber($family_id)}</td>
                    {*<td><a target="_blank" data-target="#modal" href="{url check=0 urltype="path" url_string="ben/P269/fieldVisits/print/0/{$smarty.session.lang}/save_session/{$taskrow->id}/{$taskrow->project_id}"}" class="btn btn-default btn-sm">{#gnr_print#}*}
                        {*</a></td>*}
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <br>
    <br>
    <br>
    <h1>تفاصيل الزيارات</h1>
    <div class="horizontal-space"></div>
    <div class="well">
        {foreach $visits as $visit}
            <table class="table table-striped table-bordered table-hover no-footer">
                <thead>
                <tr>
                    <th class="text-center" width="20%">{#p_visit_date#}</th>
                    <th class="text-center" width="20%">{#p_visit_execution_status#}</th>
                    <th class="text-center" width="10%">{#p_recommendations#}</th>
                    <th class="text-center" width="20%">{#gnr_comment#}</th>
                    <th class="text-center" width="10%">{#p_excutor#}</th>
                    {*<th class="text-center" width="10%">{#p_file_number#}</th>*}
                    {*<th class="text-center" width="5%"></th>*}
                </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-center">{getdate table=assist_visit col=date type=show row=$visit}</td>
                        <td class="text-center">
                            {foreach $execution_status_list as $status}
                                {if $status->id eq $visit->status}{$status->translatedName}{/if}
                               {*>{$status->translatedName}*}
                            {/foreach}</td>
                        <td class="text-center">{$visit->recommendations}</td>
                        <td class="text-center">{$visit->comment}</td>
                        <td class="text-center">
                            {getname table=sh_user id=$visit->executor}
                            {$visit->executor}
                        </td>
                    </tr>
                    </tbody>
            </table>
        <br>
        <br>
        <br>
        {/foreach}
    </div>
        {* {foreach $visits as $visit}*}
        {*{foreach $visits as $visit}*}
                {*<div class="row">*}
                    {*<div class="col-lg-12">*}
                        {*<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"> : </div>*}
                        {*<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{getdate table=assist_visit col=date type=show row=$visit}</div>*}

                        {*<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_visit_execution_status#}</div>*}
                        {*<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">*}
                            {*<select name="status" class="form-control" disabled="true">*}
                                {*<option value=""></option>*}
                                {*{foreach $execution_status_list as $status}*}
                                    {*<option value="{$status->id}" {if $status->id eq $visit->status} selected {/if}>{$status->translatedName}</option>*}
                                {*{/foreach}*}
                            {*</select>*}
                        {*</div>*}

                        {*<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_recommendations#}</div>*}
                        {*<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">*}
                            {*<div class="control-group">*}
                                {*<textarea name="recommendations" class="form-control" id="" cols="30" rows="5" readonly>{$visit->recommendations}</textarea>*}
                            {*</div>*}
                        {*</div>*}

                        {*<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_comment#}</div>*}
                        {*<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><textarea name="comment"*}
                                                                                                 {*class="form-control"*}
                                                                                                 {*cols="30"*}
                                                                                                 {*rows="5" readonly>{$visit->comment}</textarea>*}
                        {*</div>*}

                        {*<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_excutor#}</div>*}
                        {*<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">*}
                            {*{getname table=sh_user id=$visit->executor}*}
                            {*{$visit->executor}*}
                        {*</div>*}
                    {*</div>*}
                {*</div>*}
            {*{/foreach}*}
    </div>
{/block}
