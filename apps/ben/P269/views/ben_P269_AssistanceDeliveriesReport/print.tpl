{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}
    <div class="container-fluid">
        <div class="text-center">
            <h1><u>{#p_delivery_data#}</u></h1>
        </div>
        <div class="panel panel-default">
            <div class="panel-body" style="display: inline-block;">
                {*{if !empty($filter['distributor'])}*}
                    {*{#p_distributor#} : {getname table=sh_user  id=$filter['distributor']}*}
                    {*<br>*}
                {*{/if}*}

                {if !empty($filter['beneficiary'])}
                {#p_asset_beneficiary#} :
                {foreach $filter['beneficiary'] as $beneficiary}
                    {getname table=sh_user  id=$beneficiary}
                    ,
                {/foreach}
                <br>
                {/if}

                {if !empty($filter['year'])}
                    {#p_assets_year#} : {getname table=assist_year id=$filter['year']}
                    <br>
                {/if}

                {if !empty($filter['ledger'])}
                    {#p_asset_ledger#} : {getname table=fin_petty_cash_ledger id=$filter['ledger']}
                    <br>
                {/if}

                {*{if !empty($filter['term'])}*}
                    {*{#p_asset_term#} : {t v=$filter['term']}*}
                    {*<br>*}
                {*{/if}*}
                {*{if !empty($filter['status'])}*}
                    {*{#p_delivery_status#} : {t v=$filter['status']}*}
                    {*<br>*}
                {*{/if}*}
                {if !empty($filter['assistance'])}
                    {#p_asset_type#} :
                    {foreach $filter['assistance'] as $assistance}
                        {getname table=assist_type id=$assistance}
                    {/foreach}
                    <br>
                {/if}
                {*{if !empty($filter['category'])}*}
                    {*{#p_asset_category#} :*}
                    {*{foreach $filter['category'] as $category}*}
                        {*{t v=$category}*}
                    {*{/foreach}*}
                    {*<br>*}
                {*{/if}*}

            </div>
        </div>
        <div class="row ">
            <div class="col-lg-12">
                <div class="widget-body">
                    <table class="table table-striped table-bordered">
                        <thead>
                        <tr>
                            <th width="5%">&nbsp;</th>
                            <th width="20%">{#gnr_beneficiary#}</th>
                            <th width="15%">{#p_distributor#}</th>
                            <th width="20%">{#p_assistance#}</th>
                            {*<th width="10%">{#gnr_value#}*}
                            {*&nbsp;/&nbsp;{#p_materials#}</th>*}
                            {*<th width="15%">{#p_delivery_status#}</th>*}
                            <th width="15%">{#p_date#}</th>
                            <th width="15%">{#p_amount#}</th>
                            <th width="15%">{#p_paid#}</th>
                            <th width="15%">{#p_remind#}</th>
                            {*<th width="15%">{#p_assets_year#}</th>*}
                        </tr>
                        </thead>
                        {$i=1}
                        <tbody>
                        {foreach $deliveries as $delivery}
                            <tr>
                                <td align="center">{$i++}</td>
                                <td align="right">
                                    {$delivery->beneficary_full_name}
                                </td>

                                <td align="right">
                                    {$delivery->donar_full_name}
                                </td>
                                <td align="right">{$delivery->assistance_name}</td>
                                {*<td align="center">*}
                                {*{if  $delivery->assistance_class_id eq AssistType::SETTING_ASSISTANCE_TYPE_CASH}{$delivery->value}{/if}*}
                                {*{if  $delivery->assistance_class_id eq AssistType::SETTING_ASSISTANCE_TYPE_MATERIAL}*}
                                {*{$x=1}*}
                                {*{foreach $delivery->materialsEntities as $material}*}
                                {*{$x++}&nbsp;-&nbsp;{getname table=assist_materials id=$material->assist_delivery_material_material_id}&nbsp;&nbsp;*}
                                {*{$material->assist_delivery_material_material_amount}&nbsp;{$material->assist_delivery_material_material_unit}*}

                                {*{if $material->assist_delivery_material_status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_NO}*}
                                {*<span style="color: red">&nbsp;&raquo;&nbsp;{t v=$material->assist_delivery_material_status}</span>*}
                                {*{/if}*}
                                {*{if $material->assist_delivery_material_status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_YES}*}
                                {*<span style="color: green">&nbsp;&raquo;&nbsp;{t v=$material->assist_delivery_material_status}</span>*}
                                {*{/if}*}
                                {*<br>*}
                                {*{/foreach}*}
                                {*{/if}*}
                                {*</td>*}
                                {*<td align="center">*}
                                {*{if $delivery->status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_NO}*}
                                {*<span style="color: red">{t v=$delivery->status}</span>*}
                                {*{/if}*}
                                {*{if $delivery->status eq AssistanceDelivery::ASSISTANCE_DELIVERY_STATUS_YES}*}
                                {*<span style="color: green">{t v=$delivery->status}&nbsp;{#gnr_riyal#}</span>*}
                                {*{/if}*}
                                {*</td>*}
                                {*<td>*}
                                {*{getname table=assist_year id=$delivery->year_id}*}
                                {*</td>*}
                                <td>
                                    {Carbon\Carbon::parse($delivery->record_date)->format('Y-m-d')}
                                </td>
                                <td>
                                    {$delivery->record_all}
                                </td>
                                <td>
                                    {$delivery->record_paid}
                                </td>
                                <td>
                                    {$delivery->record_all - $delivery->record_paid}
                                </td>
                                <td>
                                    {$delivery->record_received}
                                </td>
                                <td>
                                    {$delivery->record_paid - $delivery->record_received}
                                </td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{/block}
{block name=back}{url urltype="path" url_string="ben/P269/AssistanceReports/show/0/{$smarty.session.lang}/menu"}{/block}
