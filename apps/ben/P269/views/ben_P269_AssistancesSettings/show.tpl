{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}

    {if $assistanceYear}
        <h5 class="row-title before-blue">
            <i class="glyphicon glyphicon-list-alt blue"></i>
            {$assistanceYear->name}&nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}&nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
        </h5>
        {url check=0 urltype="mbutton" opr_code='AssistancesSettings' url_string="ben/P269/AssistancesSettings/add/0/{$smarty.session.lang}" text_value="{#p_adding_new_assistance#}"}
        <div class="tabbable tabs-left">
            <ul class="nav nav-tabs" id="myTab3">
                {$i=1}
                {foreach $categories as $category}
                    <li class="tab-sky {if $smarty.session.s_assistance_settings_tab eq $category->id} active {/if}">
                        <a aria-expanded="false" data-toggle="tab" href="#{$category->id}">
                            <span class="badge badge-sky badge-square">{$i++}</span>&nbsp;{$category->translatedName}
                        </a>
                    </li>
                {/foreach}
            </ul>

            <div class="tab-content">

                {foreach $categories as $category}

                    <div id="{$category->id}" class="tab-pane {if $smarty.session.s_assistance_settings_tab eq $category->id} in active {/if}">

                        <div class="horizontal-space"></div>

                        <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
                            <thead>
                            <tr>
                                <th width="5%">&nbsp;</th>
                                <th width="25%">{#gnr_name#}</th>
                                <th width="15%">{#gnr_type#}</th>
                                <th width="20%">{#p_term_of_selection#}</th>
                                <th width="15%">{#p_exchage_law#}</th>
                                <th width="10%">{#gnr_status#}</th>
                                <th width="10%">{#gnr_settings#}</th>
                            </tr>
                            </thead>
                            <tbody>
                            {$i=1}
                            {foreach $category->assitances as $assistance}
                                <tr>
                                    <td align="center">{$i++}</td>
                                    <td>{$assistance->name}</td>
                                    <td align="center">{getname table=assist_type id=$assistance->type_id}</td>
                                    <td align="center">
                                        {getname table=assist_terms_of_selection id=$assistance->term_of_selection_id}
                                        &nbsp;&raquo;&nbsp;
                                        {t v=$assistance->term_of_selection_type}</td>
                                    <td align="center">{url check=0 urltype="button" opr_code='AssistancesSettings' url_string="ben/P269/AssistancesSettings/exchangeLaw/0/{$smarty.session.lang}/save_session/{$assistance->id}" text_value="{#p_exchage_law#}"}</td>
                                    <td align="center">{t v=$assistance->activation_status}</td>
                                    <td align="center" nowrap>
                                        {if $assistance->requestNumber eq 0}
                                            {url check=0 urltype="medit" opr_code='AssistancesSettings' url_string="ben/P269/AssistancesSettings/edit/0/{$smarty.session.lang}/{$assistance->id}"}
                                            {url check=0 urltype="mdelete" opr_code='AssistancesSettings' url_string="ben/P269/AssistancesSettings/confirm/0/{$smarty.session.lang}/{$assistance->id}"}
                                        {/if}
                                    </td>
                                </tr>
                            {/foreach}
                            </tbody>
                        </table>

                    </div>

                {/foreach}

            </div>
        </div>
    {else}
        <span style="color: red">{#p_you_should_activate_assistance_year_first#}</span>
    {/if}

{/block}