{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <div class="row">
        <div class="col-lg-2"></div>
        <div class="col-lg-8">
            <div class="well with-header">
                <div class="header bordered-blue">{$assistance->name}
                    &nbsp;&raquo;&nbsp;{$assistance->criterion_sum_of_max_values}</div>
                <div class="buttons-preview">

                    <form method="post"
                          action='{url urltype="path" url_string="ben/P269/AssistancesSettings/configureFilteringIntervals/0/{$smarty.session.lang}/{$assistance->id}/insert/{$smarty.session.s_AssistancesSettings_token}"}'>

                        <div class="col-lg-12">

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_category#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text"
                                                                                                class="form-control"
                                                                                                name="name"
                                                                                                value="{$category->name}"
                                                                                                placeholder="{#p_category#}"
                                                                                                required="required">
                            </div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_from#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="number" min=0
                                                                                                class="form-control"
                                                                                                name="from"
                                                                                                value="{$category->from}"
                                                                                                placeholder="{#gnr_from#}"
                                                                                                required="required">
                            </div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_to#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="number" min=0
                                                                                                class="form-control"
                                                                                                name="to"
                                                                                                value="{$category->to}"
                                                                                                placeholder="{#gnr_to#}"
                                                                                                required="required">
                            </div>

                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                            </div>

                        </div>
                    </form>

                    <hr>

                    <table class="table table-striped table-bordered">
                        <thead>
                        <tr>
                            <th style="background-color: #A0D468 !important;" width="5%"></th>
                            <th style="background-color: #A0D468 !important;" width="25%">{#p_category#}</th>
                            <th style="background-color: #A0D468 !important;" width="25%">{#gnr_from#}
                                &nbsp;&nbsp;&nbsp;{#gnr_to#}</th>
                            <th style="background-color: #A0D468 !important;" width="15%">{#gnr_number#}</th>
                            <th style="background-color: #A0D468 !important;" width="15%">{#gnr_view#}</th>
                            <th style="background-color: #A0D468 !important;" width="15%">{#gnr_settings#}</th>
                        </tr>
                        </thead>
                        {$i=1}
                        <tbody>
                        {foreach $assistance->categories as $category}
                            <tr>
                                <td align="center">{$i++}</td>
                                <td align="center">{#gnr_from#}&nbsp;{$category->from}&nbsp;{#gnr_to#}
                                    &nbsp;{$category->to}</td>
                                <td align="center">{$category->name}</td>
                                <td align="center">{count($category->idsArray)}</td>
                                <td align="center">{url check=0 urltype="mbutton" url_string="ben/P269/AssistancesSettings/beneficiariesByCategory/0/{$smarty.session.lang}/{$category->id}" text_value="{#gnr_view#}"}</td>
                                <td align="center">
                                    {url check=0 urltype="medit" url_string="ben/P269/AssistancesSettings/editFilterEntry/0/{$smarty.session.lang}/{$category->id}"}
                                    {url check=0 urltype="mdelete" url_string="ben/P269/AssistancesSettings/confirmFilterEntry/0/{$smarty.session.lang}/{$category->id}"}
                                </td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>

                </div>
            </div>
        </div>
        <div class="col-lg-2"></div>
    </div>
{/block}
{block name=back}{url urltype="path" url_string="ben/P269/AssistancesSettings/exchangeLaw/0/{$smarty.session.lang}"}{/block}