{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_edit#}</h4>
    </div>
    <div class="modal-body">
        <form method="post" action='{url urltype="path" url_string="ben/P269/AssistancesSettings/exchangeLaw/0/{$smarty.session.lang}/updateAssistanceCriteria/{$smarty.session.s_AssistancesSettings_token}/{$assistance->id}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$assistance->name}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$assistance->class_id}
                        &nbsp;&raquo;&nbsp;{getname table=assist_type id=$assistance->type_id}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_activation_status#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{t v=$assistance->activation_status}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_term_of_selection#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$termOfSelection->name}</div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$termOfSelection->type}</div>

                    {if $assistance->term_of_selection_type eq 1229}
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_category#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            {foreach $termOfSelection->userClassesIds as $key => $class}
                                &nbsp;-&nbsp;{getname table=sh_userclasses id=$class}
                            {/foreach}
                        </div>
                    {/if}

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">المعايير</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $criteriaList as $criterion}
                                {if !in_array($criterion->id, [1231, 1232])}
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="criteriaIds[]" value="{$criterion->id}" {if in_array($criterion->id, $assistance->getAssistanceCriteriaAsArrayOfIds())} checked {/if}>
                                            <span class="text">{$criterion->translatedName}</span>
                                        </label>
                                    </div>
                                {/if}
                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
                </div>

            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
