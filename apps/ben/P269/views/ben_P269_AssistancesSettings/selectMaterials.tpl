{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <div class="widget collapsed">
        <div class="widget-header bg-blue">
            <i class="widget-icon"></i>
            <span class="widget-caption">
                {$assistanceYear->name}
                &nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
                &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
                &nbsp;&raquo;&nbsp;
                {$assistance->name}
            </span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                    <i class="fa fa-minus"></i>
                </a>
            </div><!--Widget Buttons-->
        </div><!--Widget Header-->
        <div class="widget-body" style="display: none;">
            <div class="row">

                <div class="col-lg-6">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$assistance->name}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$assistance->class_id}
                                &nbsp;&raquo;&nbsp;{getname table=assist_type id=$assistance->type_id}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_activation_status#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{t v=$assistance->activation_status}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_term_of_selection#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$termOfSelection->name}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$termOfSelection->type}</div>

                            {if $assistance->term_of_selection_type eq 1229}
                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_category#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                    {foreach $termOfSelection->userClassesIds as $key => $class}
                                        &nbsp;-&nbsp;{getname table=sh_userclasses id=$class}
                                    {/foreach}
                                </div>
                            {/if}
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="row">
                        <div class="col-lg-12">

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_criteria#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <tr>
                                        <th style="background-color: #A0D468 !important;" width="5%"></th>
                                        <th style="background-color: #A0D468 !important;"
                                            width="25%">{#gnr_criterion#}</th>
                                        <th style="background-color: #A0D468 !important;"
                                            width="70%">{#gnr_values#}</th>
                                    </tr>
                                    </thead>
                                    {$i=1}
                                    <tbody>
                                    {foreach $termOfSelection->getCriteriaList() as $criterion}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td>{t v=$criterion->type_id}</td>
                                            <td class="text-center">{$criterion->browseTermCriterionDetails()}</td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div><!--Widget Body-->
    </div>
    <form method="post"
          action='{url urltype="path" url_string="ben/P269/AssistancesSettings/exchangeLaw/0/{$smarty.session.lang}/updateMaterials/{$smarty.session.s_AssistancesSettings_token}/{$assistance->id}"}'>
        {foreach $materialsTypes as $materialType}
            <div class="well with-header">
                <div class="header bordered-pink">{$materialType->name}</div>
                <div class="buttons-preview">
                    <div class="control-group">
                        {foreach $materialType->materials as $material}
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="materials[]" value="{$material->assist_materials_id}" {if in_array($material->assist_materials_id, $assistance->materialsArray)} checked {/if}>
                                    <span class="text">{$material->assist_materials_name}</span>
                                </label>
                            </div>
                        {/foreach}
                    </div>
                </div>
            </div>
        {/foreach}

        <div class="row">
            <div class="col-lg-12"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
        </div>
    </form>
{/block}
{block name=back}{url urltype="path" url_string="ben/P269/AssistancesSettings/exchangeLaw/0/{$smarty.session.lang}"}{/block}