{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{$category->name}&nbsp;:&nbsp;{$category->from}&nbsp;&raquo;&nbsp;{$category->to}</h4>
    </div>
    <div class="modal-body" id="root">
        <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
            <thead>
            <tr>
                <th style="background-color: #A0D468 !important;" width="5%"></th>
                <th style="background-color: #A0D468 !important;" width="25%">{#p_husband_name#}</th>
                <th style="background-color: #A0D468 !important;" width="25%">{#p_wife_name#}</th>
            </tr>
            </thead>
            {$i=1}
            <tbody>
            {foreach $families as $family}
                <tr>
                    <td align="center">{$i++}</td>
                    <td>{getname table=sh_user id=$family->assist_families_husband_id}</td>
                    <td>{getname table=sh_user id=$family->assist_families_wife_id}</td>
                </tr>
            {/foreach}
            </tbody>
        </table>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
{block name=header}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}
