{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <div class="row">
        <div class="col-lg-2"></div>
        <div class="col-lg-8">
            <div class="well with-header">
                <div class="header bordered-blue">{t v=$criterion->type}</div>
                <div class="buttons-preview">

                    <form method="post" action='{url urltype="path" url_string="ben/P269/AssistancesSettings/configureCriterionIntervals/0/{$smarty.session.lang}/{$criterion->id}/insert/{$smarty.session.s_AssistancesSettings_token}"}'>
                        <input type="number" name="from" min="0" placeholder="{#gnr_from#}" required>
                        <input type="number" name="to" min="0" placeholder="{#gnr_to#}" required>
                        <input type="number" name="points" placeholder="{#p_points#}" required>
                        <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                    </form>

                    <hr>

                    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                        <thead>
                        <tr>
                            <th style="background-color: #A0D468 !important;" width="5%"></th>
                            <th style="background-color: #A0D468 !important;" width="50%">{#gnr_from#}&nbsp;&nbsp;&nbsp;{#gnr_to#}</th>
                            <th style="background-color: #A0D468 !important;" width="25%">{#p_points#}</th>
                            <th style="background-color: #A0D468 !important;" width="20%">{#gnr_settings#}</th>
                        </tr>
                        </thead>
                        {$i=1}
                        <tbody>
                            {foreach $criterion->valuesArray as $key => $value}
                                <tr>
                                    <td align="center">{$i++}</td>
                                    <td align="right">{#gnr_from#}&nbsp;{$value['from']}&nbsp;{#gnr_to#}&nbsp;{$value['to']}</td>
                                    <td align="center">{$value['points']}</td>
                                    <td align="right">
                                        {url check=0 urltype="medit" url_string="ben/P269/AssistancesSettings/editIntervalEntry/0/{$smarty.session.lang}/{$criterion->id}/{$key}"}
                                        {url check=0 urltype="mdelete" url_string="ben/P269/AssistancesSettings/confirmIntervalEntry/0/{$smarty.session.lang}/{$criterion->id}/{$key}"}
                                    </td>
                                </tr>
                            {/foreach}
                        </tbody>
                    </table>

                </div>
            </div>
        </div>
        <div class="col-lg-2"></div>
    </div>
{/block}
{block name=back}{url urltype="path" url_string="ben/P269/AssistancesSettings/exchangeLaw/0/{$smarty.session.lang}"}{/block}