{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_delete#}</h4>
    </div>
    <div class="modal-body">
        <div class="row snsowraper">
            <h5 class="text-center">
                {#gnr_delete_row_confirm#}
                <br><br>
                {t v=$criterion->type}
                <br><br>
                {#gnr_from#}&nbsp;{$criterion->valuesArray[$key]['from']}
                {#gnr_to#}&nbsp;{$criterion->valuesArray[$key]['to']}&nbsp;&raquo;&nbsp;{#p_points#}&nbsp;{$criterion->valuesArray[$key]['points']}
            </h5>
            <div class="text-center">
                {url urltype=delete url_string="ben/P269/AssistancesSettings/configureCriterionIntervals/0/{$smarty.session.lang}/{$criterion->id}/delete/{$smarty.session.s_AssistancesSettings_token}/{$key}"}
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}