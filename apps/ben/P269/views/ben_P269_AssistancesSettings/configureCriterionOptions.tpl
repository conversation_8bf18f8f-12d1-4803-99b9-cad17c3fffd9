{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{$assistance->name}</h4>
    </div>
    <div class="modal-body">
         <form method="post" action='{url urltype="path" url_string="ben/P269/AssistancesSettings/exchangeLaw/0/{$smarty.session.lang}/updateCriterion/{$smarty.session.s_TermsOfSelection_token}/{$criterion->id}"}'>
             <div class="row snsowraper">

                 <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                 <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{t v=$criterion->type}</div>

                 <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_values#}</div>
                 <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                     <div class="row">
                         {foreach $options as $option}
                             <div class="col-lg-12">{$option->translatedName} <input type="number" name="{$option->id}" value="{$criterion->valuesArray[$option->id]}"></div>
                         {/foreach}
                     </div>
                 </div>

                 <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>

            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
