{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <div class="widget collapsed">
        <div class="widget-header bg-blue">
            <i class="widget-icon"></i>
            <span class="widget-caption">
                {$assistanceYear->name}
                &nbsp;:&nbsp;{getdate table=fin_year col=start_date type=show row=$assistanceYear}
                &nbsp;&raquo;&nbsp;{getdate table=assist_year col=end_date type=show row=$assistanceYear}
                &nbsp;&raquo;&nbsp;
                {$assistance->name}
            </span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                    <i class="fa fa-minus"></i>
                </a>
            </div><!--Widget Buttons-->
        </div><!--Widget Header-->
        <div class="widget-body" style="display: none;">
            <div class="row">

                <div class="col-lg-6">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$assistance->name}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$assistance->class_id}
                                &nbsp;&raquo;&nbsp;{getname table=assist_type id=$assistance->type_id}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_activation_status#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{t v=$assistance->activation_status}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_term_of_selection#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$termOfSelection->name}</div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=st_setting id=$termOfSelection->type}</div>

                            {if $assistance->term_of_selection_type eq 1229}
                                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_category#}</div>
                                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                    {foreach $termOfSelection->userClassesIds as $key => $class}
                                        &nbsp;-&nbsp;{getname table=sh_userclasses id=$class}
                                    {/foreach}
                                </div>
                            {/if}
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="row">
                        <div class="col-lg-12">

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_term_of_selection#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <tr>
                                        <th style="background-color: #A0D468 !important;" width="5%"></th>
                                        <th style="background-color: #A0D468 !important;" width="25%">{#gnr_criterion#}</th>
                                        <th style="background-color: #A0D468 !important;" width="70%">{#gnr_values#}</th>
                                    </tr>
                                    </thead>
                                    {$i=1}
                                    <tbody>

                                    {foreach $termOfSelection->getCriteriaList() as $criterion}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td>{t v=$criterion->type_id}</td>
                                            <td class="text-center">{$criterion->browseTermCriterionDetails()}</td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div><!--Widget Body-->
    </div>
    <div class="row">
        <div class="col-lg-6">
            <div class="widget">
                <div class="widget-header bg-blue">
                    <i class="widget-icon"></i>
                    <span class="widget-caption">معايير الأولوية</span>
                </div>
                <!--Widget Header-->
                <div class="widget-body" style="display: block;">

                    {url check=0 urltype="mbutton" url_string="ben/P269/AssistancesSettings/selectAssistanceCriteria/0/{$smarty.session.lang}/{$assistance->id}" text_value="تحديد المعايير"}
                    <div class="horizontal-space"></div>
                    <table class="table table-striped table-bordered">
                        <thead>
                        <tr>
                            <th style="background-color: #A0D468 !important;" width="5%"></th>
                            <th style="background-color: #A0D468 !important;" width="40%">{#gnr_criterion#}</th>
                            <th style="background-color: #A0D468 !important;" width="55%">{#gnr_values#}</th>
                        </tr>
                        </thead>
                        {$i=1}
                        <tbody>
                        {foreach $assistance->exchCriteriaList as $criterion}
                            <tr>
                                <td align="center">{$i++}</td>
                                <td>
                                    {if in_array($criterion->type,TermsOfSelection::optionsCriteria())}
                                        {url check=0 urltype="mbutton" url_string="ben/P269/AssistancesSettings/configureCriterionOptions/0/{$smarty.session.lang}/{$criterion->id}" text_value="{#gnr_settings#}"}
                                    {/if}

                                    {if in_array($criterion->type,TermsOfSelection::intervalsCriteria())}
                                        {url check=0 urltype="button" url_string="ben/P269/AssistancesSettings/configureCriterionIntervals/0/{$smarty.session.lang}/{$criterion->id}" text_value="{#gnr_settings#}"}
                                    {/if}

                                    &nbsp;
                                    {t v=$criterion->type}
                                </td>
                                <td>{$criterion->getValuesAsString()}</td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>

                    <div class="horizontal-space"></div>

                    <h5 class="row-title before-darkorange"><i class="fa fa-reply-all darkorange"></i>{#p_criterion_sum_of_max_values#}&nbsp;&raquo;&nbsp;{$assistance->criterion_sum_of_max_values}</h5>

                    {if $assistance->criterion_sum_of_max_values eq 0}&nbsp;<span style="color: red">{#p_you_should_update_the_maximam_point#}</span>{/if}

                </div><!--Widget Body-->
            </div>
        </div>
        <div class="col-lg-6">
            <div class="widget">
                <div class="widget-header bg-blue">
                    <i class="widget-icon"></i>
                    <span class="widget-caption">فئات الأولوية</span>
                </div><!--Widget Header-->
                <div class="widget-body" style="display: block;">

                    {url check=0 urltype="mbutton" url_string="gnr/X000/mediacenter/browseBeneficiaries/0/{$smarty.session.lang}/termOfSelection/{$termOfSelection->id}" text_value="{#gnr_beneficiaries#}&nbsp;[&nbsp;{$termOfSelection->beneficiariesNumber()}&nbsp;]&nbsp;"}

                    <div class="horizontal-space"></div>

                    <table class="table table-striped table-bordered">
                        <thead>
                        <tr>
                            <th style="background-color: #A0D468 !important;" width="5%">{url check=0 urltype="madd" url_string="ben/P269/AssistancesSettings/addFilterEntry/0/{$smarty.session.lang}/{$assistance->id}"}</th>
                            <th style="background-color: #A0D468 !important;" width="20%">{#p_category#}</th>
                            <th style="background-color: #A0D468 !important;" width="20%">{#gnr_from#}&nbsp;&nbsp;&nbsp;{#gnr_to#}</th>
                            <th style="background-color: #A0D468 !important;" width="15%">{#gnr_number#}</th>
                            <th style="background-color: #A0D468 !important;" width="20%">{#gnr_view#}</th>
                            <th style="background-color: #A0D468 !important;" width="15%">{#gnr_settings#}</th>
                        </tr>
                        </thead>
                        {$i=1}
                        <tbody>
                        {foreach $assistance->categories as $category}
                            <tr>
                                <td align="center">{$i++}</td>
                                <td align="center">{$category->name}</td>
                                <td align="center">{#gnr_from#}&nbsp;{$category->from}&nbsp;{#gnr_to#}&nbsp;{$category->to}</td>
                                <td align="center">{count($category->idsArray)}</td>
                                <td align="center">{url check=0 urltype="mbutton" url_string="gnr/X000/mediacenter/browseBeneficiaries/0/{$smarty.session.lang}/category/{$category->id}" text_value="{#gnr_view#}"}</td>
                                <td align="center" nowrap="">
                                    {url check=0 urltype="medit" url_string="ben/P269/AssistancesSettings/editFilterEntry/0/{$smarty.session.lang}/{$category->id}"}
                                    {url check=0 urltype="mdelete" url_string="ben/P269/AssistancesSettings/confirmFilterEntry/0/{$smarty.session.lang}/{$category->id}"}
                                </td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>

                </div><!--Widget Body-->
            </div>

        </div>
    </div>

    {*{if $assistance->class_id eq 1163}*}

        {*<div class="row">*}
            {*<div class="col-lg-12">*}
                {*<div class="widget">*}
                    {*<div class="widget-header bg-blue">*}
                        {*<i class="widget-icon"></i>*}
                        {*<span class="widget-caption">ضبط الأعيان</span>*}
                    {*</div>*}
                    {*<!--Widget Header-->*}
                    {*<div class="widget-body" style="display: block;">*}

                        {*<table class="table table-striped table-bordered">*}
                            {*<thead>*}
                            {*<tr>*}
                                {*<th style="background-color: #A0D468 !important;" width="5%">{url check=0 urltype="madd" url_string="ben/P269/AssistancesSettings/addMaterial/0/{$smarty.session.lang}/{$assistance->id}"}</th>*}
                                {*<th style="background-color: #A0D468 !important;" width="55%">{#p_materials#}</th>*}
                                {*<th style="background-color: #A0D468 !important;" width="20%">{#gnr_values#}/{#gnr_unit#}</th>*}
                                {*<th style="background-color: #A0D468 !important;" width="20%">{#gnr_settings#}</th>*}
                            {*</tr>*}
                            {*</thead>*}
                            {*{$i=1}*}
                            {*<tbody>*}
                            {*{foreach $materials as $material}*}
                                {*<tr>*}
                                    {*<td align="center">{$i++}</td>*}
                                    {*<td>{$material->materialObject->materialType->assist_materials_name}&nbsp;&raquo;&nbsp;{$material->materialObject->name}</td>*}
                                    {*<td class="text-center">{$material->amount}&nbsp;{$material->unit}</td>*}
                                    {*<td class="text-center">*}
                                        {*{url check=0 urltype="medit" url_string="ben/P269/AssistancesSettings/editMaterial/0/{$smarty.session.lang}/{$assistance->id}/{$material->id}"}*}
                                        {*{url check=0 urltype="mdelete" url_string="ben/P269/AssistancesSettings/confirmMaterial/0/{$smarty.session.lang}/{$assistance->id}/{$material->id}"}*}
                                    {*</td>*}
                                {*</tr>*}
                            {*{/foreach}*}
                            {*</tbody>*}
                        {*</table>*}

                    {*</div><!--Widget Body-->*}
                {*</div>*}
            {*</div>*}
        {*</div>*}

    {*{/if}*}

{/block}
{block name=back}{url urltype="path" url_string="ben/P269/AssistancesSettings/show/0/{$smarty.session.lang}"}{/block}