{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#gnr_add#}</h4>
    </div>
    <div class="modal-body">
        <form method="post" action='{url urltype="path" url_string="ben/P269/AssistancesSettings/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_AssistancesSettings_token}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="name" placeholder="{#gnr_name#}" required="required"></div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_type#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="type_id">
                            {foreach $assistancesType as $type}
                                <option value="{$type->id}">{t v=$type->class_id}&nbsp;&raquo;&nbsp;{$type->name}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_term_of_selection#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="term_of_selection_id">
                            {foreach $termsOfSelection as $term}
                                <option value="{$term->id}">{$term->name}&nbsp;&raquo;&nbsp;{t v=$term->type}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_activation_status#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $activation_options as $option}
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="activation_status" value="{$option->id}" {if $option->id eq Setting::ACTIVE} checked {/if} required>
                                        <span class="text">{$option->translatedName}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp">{#gnr_add#}</button></div>
                </div>

            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
