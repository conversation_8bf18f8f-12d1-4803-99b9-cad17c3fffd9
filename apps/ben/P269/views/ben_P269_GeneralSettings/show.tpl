{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}
    <link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>

    <script type="text/javascript">
        $(document).ready(function () {

            if (!$("#activated").is(":checked")){
                $("#textFields").css("display", "none");
            }

        });

        function toggleDiv() {
            if ($("#activated").is(":checked")) {
                $("#textFields").show('fast');
            }else{
                $("#textFields").hide('fast');
            }
        }

    </script>
{/block}


{block name=page_body}
    <div class="row snsowraper">
        <div class="col-lg-12 widget-body">
            <form action="{url urltype=path url_string="ben/P269/GeneralSettings/show/0/{$smarty.session.lang}/saveSettings/{$smarty.session.s_GeneralSettings_token}"}"
                  method="post">

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_relatives_types_supported#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {foreach $relativesTypes as $relative}
                        <div class="checkbox">
                            <label>
                                <input name="relatives_types_supported[]" value="{$relative->id}"
                                       type="checkbox" {if in_array($relative->id, explode(',',$assistance->relatives_types_supported))} checked {/if}>
                                <span class="text">{$relative->translatedName}</span>
                            </label>
                        </div>
                    {/foreach}
                </div>
                {*<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_can_beneficiary_add_famliy_data#}</div>*}
                {*<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">*}
                    {*<div class="control-group">*}
                        {*<div class="radio">*}
                            {*<label>*}
                                {*<input type="radio" name="can_add_family_data" value="1" {if $assistance->can_beneficiary_add_family_data eq 1}checked{/if}>*}
                                {*<span class="text">{#gnr_yes#}</span>*}
                            {*</label>*}
                        {*</div>*}
                        {*<div class="radio">*}
                            {*<label>*}
                                {*<input type="radio" name="can_add_family_data" value="0" {if $assistance->can_beneficiary_add_family_data eq 0}checked{/if}>*}
                                {*<span class="text">{#gnr_no#}</span>*}
                            {*</label>*}
                        {*</div>*}
                    {*</div>*}
                {*</div>*}
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                </div>
            </form>
        </div>
    </div>

    <div class="row snsowraper">
        <div class="col-lg-12 widget-body">
            <form action="{url urltype=path url_string="ben/P269/GeneralSettings/show/0/{$smarty.session.lang}/saveSmsSettings/{$smarty.session.s_GeneralSettings_token}"}"
                  method="post">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_sms_settings_on#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <div class="radio">
                        <label>
                            <input id="activated" name="sms_activation" onchange="toggleDiv()" value="1"
                                   type="radio" {if $assistance->sms_activation} checked {/if}>
                            <span class="text">{#gnr_yes#}</span>
                        </label>
                    </div>
                    <div class="radio">
                        <label>
                            <input id="unactivated" onchange="toggleDiv()" name="sms_activation" value="0"
                                   type="radio" {if !$assistance->sms_activation} checked {/if}>
                            <span class="text">{#gnr_no#}</span>
                        </label>
                    </div>
                </div>

                <div id="textFields">
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_sms_addition_text#}
                     <span class="info"><i class="fa fa-question-circle tooltip-info" data-toggle="tooltip" data-placement="top" data-original-title="اكتب النص وضع علامه * حتى يتم مكانها طباعه اسم المستفيد"></i></span>
                    </div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <textarea name="addition" class="form-control" placeholder="{#p_sms_text#}">{$smsSettings->ADDITION}</textarea>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_sms_exclusion_text#}
                        <span class="info"><i class="fa fa-question-circle tooltip-info" data-toggle="tooltip" data-placement="top" data-original-title="اكتب النص وضع علامه * حتى يتم مكانها طباعه اسم المستفيد"></i></span>
                    </div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <textarea name="exclusion" class="form-control" placeholder="{#p_sms_text#}">{$smsSettings->EXCLUSION}</textarea>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_sms_delivery_text#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <textarea name="delivery" class="form-control" placeholder="{#p_sms_text#}">{$smsSettings->DELIVERY}</textarea>
                    </div>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                </div>



            </form>
        </div>
    </div>

    <div class="row snsowraper">
        <div class="col-lg-12 widget-body">
            <form action="{url urltype=path url_string="ben/P269/GeneralSettings/show/0/{$smarty.session.lang}/saveBeneficiariesSettings/{$smarty.session.s_GeneralSettings_token}"}"
                  method="post">

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_benefactionary_allowed#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {foreach $usersClasses as $class}
                        <div class="checkbox">
                            <label>
                                {*<input id="activated" name="sms_activation" onchange="toggleDiv()" value="1"*}
                                       {*type="radio" {if $assistance->sms_activation} checked {/if}>*}

                                <input type="radio" name="client_id" value="{$class->id}"
                                       {if $class->id eq $assistance->client_id}checked{/if}>
                                <span class="text">{$class->name}</span>
                            </label>
                        </div>
                    {/foreach}
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                    </div>
                </div>


            </form>
        </div>
    </div>


    <div class="row snsowraper">
        <div class="col-lg-12 widget-body">
            <form action="{url urltype=path url_string="ben/P269/GeneralSettings/show/0/{$smarty.session.lang}/saveAutoNumber/{$smarty.session.s_GeneralSettings_token}"}"
                  method="post">

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_family_auto_number#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="radio">
                            <label>
                                <input name="auto_number" value="1"
                                       type="radio" {if $assistance->auto_number} checked {/if}>
                                <span class="text">{#gnr_yes#}</span>
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input name="auto_number" value="0"
                                       type="radio" {if !$assistance->auto_number} checked {/if}>
                                <span class="text">{#gnr_no#}</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                    </div>
                </div>


            </form>
        </div>
    </div>
{/block}