{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#gnr_edit#}</h4>
	</div>
	<div class="modal-body">
        <form  method="post" action='{url urltype="path" url_string="ben/P252/ActCreating/show/0/{$smarty.session.lang}/update/{$smarty.session.s_ActCreating_token}/{$actrow->id}/{$season_id}"}'>
            <div class="row snsowraper">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_name#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="text" name="name" class="form-control" placeholder="{#p_name#}" value="{$actrow->name}" required>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_program#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="program_id" style="width:100%;" required>
                        <option value="" selected>{#gnr_unspecified#}</option>
                        {foreach $programs as $program}
                            <option value="{$program->id}" {if $program->id eq $actrow->program_id}selected{/if}>{$program->name}</option>
                        {/foreach}
                    </select>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_season#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {getname table=act_seasons id=$season_id}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_unit#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="unit_id" style="width:100%;" required>
                        {foreach $units_list as $row}
                            <option value="{$row->id}" {if $actrow->unit_id eq $row->id}selected{/if}>{getname table=sh_unt id=$row->id} >> {getname table=sh_user id=$row->manager_id}</option>
                        {/foreach}
                    </select>
                </div>


                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button>
                </div>
            </div>
        </form>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}