{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=title}{#p_templates#}{/block}
{block name=body}
<div class="row">
    <div class="col-md-12">
        <table class="table table-bordered table-hover table-striped searchable">
            <thead>
                <tr>
                    <th width="5%">
                        {url check=0 urltype="madd" style='modal' opr_code='basePrintTemplate'
                            url_string="adm/P001/basePrintTemplate/add/0/{$smarty.session.lang}"}
                    </th>
                    <th width="25%">{#p_template_name#}</th>
                    <th width="15%">{#p_template_code#}</th>
                    <th width="15%">{#p_variables#}</th>
                    <th width="45%">{#gnr_operations#}</th>
                </tr>
            </thead>
            <tbody>
                {$numbers = 1}
                {foreach $baseTemplates as $baseTemplate}
                    <tr>
                        <td>{$numbers}</td>
                        <td>{$baseTemplate->name}</td>
                        <td>{$baseTemplate->code}</td>
                        <td>
                            <a class="btn btn-default" data-toggle="modal" data-target="#modal"
                            href="{url check=0 urltype="path" style='modal' opr_code='basePrintTemplate'
                            url_string="adm/P001/basePrintTemplate/placeholders/0/{$smarty.session.lang}/{$baseTemplate->id}"}">
                                {#p_variables_view#}
                            </a>
                        </td>
                        <td>
                            {url check=0 urltype="medit" style='modal' opr_code='basePrintTemplate' url_string="adm/P001/basePrintTemplate/edit/0/{$smarty.session.lang}/{$baseTemplate->id}"}
                            {if !$baseTemplate->hasTemplates()}
                                <a data-href="{url check=1 urltype="path" opr_code='basePrintTemplate' oprvtype=2 url_string="adm/P001/basePrintTemplate/delete/0/{$smarty.session.lang}/{$baseTemplate->id}"}"
                                   data-toggle="confirm"
                                   data-msg="{#p_template_delete_msg#}"
                                   data-title="{#p_template_delete#}"
                                   class="btn btn-danger btn-sm shiny icon-only ">
                                    <i class="fa fa-trash-o"></i>
                                </a>
                            {/if}
                        </td>
                    </tr>
                    {$numbers = $numbers + 1}
                {/foreach}
            </tbody>
        </table>
    </div>
</div>
{/block}

