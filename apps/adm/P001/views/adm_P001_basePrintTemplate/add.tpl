{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}multi-page.tpl"}
{$backURLString = "adm/P001/basePrintTemplate/show/0/{$smarty.session.lang}"}
{$modalTitleError = "{#p_template_new#}"}
{block name=page_content}
    <form action="{url urltype='path' url_string="adm/P001/basePrintTemplate/add/0/{$smarty.session.lang}"}" method="post">

        <div class="form-group">
            <label for="name">{#p_template_name#}</label>
            <input type="text" class="form-control" name="name" id="name" placeholder="{#p_template_name#}" required="required">
        </div>

        <div class="form-group">
            <label for="code">{#p_template_code#}</label>
            <input type="text" class="form-control" name="code" id="code" placeholder="{#p_template_code#}" required="required">
        </div>

        <legend>{#p_variables#}
            <button type="button" class="btn btn-success btn-xs" id="newPlaceholder"><i class="fa fa-plus"></i> {#p_variable_add#}</button>
        </legend>

        <div class="form-group" id="placeholders">
            <div class="row">
                <div class="col-lg-5">
                    <input type="text" class="form-control" name="placeholdersCodes[]" placeholder="{#p_variables_code_hint#}">
                </div>
                <div class="col-lg-5">
                    <input type="text" class="form-control" name="placeholders[]" placeholder="{#p_variables_hint#}">
                </div>
            </div>
        </div>


        <div class="form-group">
            <button type="submit" class="btn btn-success">{#gnr_save#}</button>
        </div>
    </form>
    <script>
        // save a copy of an input
        var input = $('#placeholders').html();

        $(function () {
            $('#newPlaceholder').click(function () {
                $('#placeholders').append(input);
            });
        });
    </script>
{/block}
