{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=title}{getname table=sh_bnd id={$smarty.session.s_bnd_id}} &raquo; {$currentProgram->translatedName}{/block}
{block name=body}
	{if $Message eq "OperationCodeExist"}
		<div class="alert alert-danger fade in">{#OperationCodeExist#}</div>
	{/if}
	<div class="btn-group">
		<a class="btn btn-success " href="javascript:void(0);">
			{if !empty($smarty.session.s_opr_sec_id)}
				 {$currentSection->translatedName}
			{else}
				<span>{#p_choose_menu_from_list_above#} </span>
			{/if}
		</a>
		<a class="btn btn-success  dropdown-toggle" data-toggle="dropdown" href="javascript:void(0);" aria-expanded="false"><i class="fa fa-angle-down"></i></a>
		<ul class="dropdown-menu dropdown-success">
			{foreach $sections as $section}
				<li>
					<a href="{url urltype="path" url_string="adm/P001/opr/show/0/{$smarty.session.lang}/save_sec_session/{$section->id}"}">
						{$section->translatedName}
					</a>
				</li>
			{/foreach}
		</ul>
	</div>

	<div class="horizontal-space"></div>

<div class="row snsowraper">
	<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
		<thead>
		<tr>
			<th width="5%">
				{if isset($smarty.session.s_opr_sec_id)}
					{url check=0 urltype="madd" opr_code='opr' url_string="adm/P001/opr/add/0/{$smarty.session.lang}"}
				{else}
					{url urltype="disabled"}
				{/if}
			</th>
			<th width="15%"><b>{#gnr_element#}</b></th>
			<th width="15%"><b>{#gnr_code#}</b></th>
			<th width="20%"><b>{#gnr_permissions#}</b></th>
			<th width="10%"><b>{#gnr_order#}</b></th>
			<th width="5%"><b>{#gnr_display#}</b></th>
			<th width="5%"><b>WF</b></th>
			<th width="5%"><b>PRV</b></th>
			<th width="20%"><b>{#gnr_settings#}</b></th>
		</tr>
		</thead>
		<tbody>
		{$i=1}
		{foreach $operations as $operation}
			<tr>
				<td align="center">{$i++}</td>
				<td>
                    {if $operation->hirarchical_type neq 940 and $operation->hirarchical_type neq 941}<span style="color: red;"><i class="fa fa-circle"></i></span>{/if}
					{if $operation->hirarchical_type eq 940}<span style="color: saddlebrown;"><i class="fa fa-folder-open-o"></i></span>{/if}
					{if $operation->hirarchical_type eq 941}<span style="color: burlywood;"><i class="fa fa-file-o"></i></span>{/if}
                    {$operation->translatedName}
				</td>
				<td>[ {$operation->code} ]</td>
				<td>{getprv prv=$operation->prv}</td>
				<td align="center">[ {$operation->order} ]</td>
				<td align="center">[ {$operation->showable} ]</td>
				<td align="center"> [ {$operation->have_wf} ] </td>
				<td align="center">
					{if Privilege::validateOprPrivilege({$operation->id})}
						<i class="fa fa-circle green"></i>
					{else}
						<i class="fa fa-circle red"></i>
					{/if}
				</td>
				<td align="center">
					{if $operation->hirarchical_type eq 940 or $operation->hirarchical_type eq 941}
						{url urltype="mbutton" url_string="gnr/X000/chart/menuDiagram/0/{$smarty.session.lang}/opr/{$operation->id}" text_value='&nbsp;<i class="fa fa-sitemap"></i>' modal="modal" style="btn btn-sky shiny"}
					{/if}
					<a href="/future.php/system/operations/{$operation->id}/custom-permissions"
						class="btn btn-default">
						<i class="fa fa-lock"></i>
					</a>
					{url check=0 urltype="medit" opr_code='opr' url_string="adm/P001/opr/edit/0/{$smarty.session.lang}/{$operation->id}"}
					{if $operation->chaild eq 0}
						{url check=0 urltype="mdelete" opr_code='opr' url_string="adm/P001/opr/confirm/0/{$smarty.session.lang}/{$operation->id}"}
					{/if}
				</td>
			</tr>
		{/foreach}
		</tbody>
	</table>
</div>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/prg/show/0/{$smarty.session.lang}"}{/block}
