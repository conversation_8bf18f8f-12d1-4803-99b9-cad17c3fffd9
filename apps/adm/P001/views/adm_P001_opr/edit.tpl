{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
	<script type="text/javascript">
		$(document).ready(function () {

			$("#extra").css("display", "none");
			$("#isSupportPrivileges").css("display", "none");
			$("#notSupportPrivilegesDiv").css("display", "none");

			$("#940").click(function () {
				if ($("#940").is(":checked")) {
					$("#extra").hide('fast');
					$("#supportPrivilegesDiv").show('fast');
					$("#isSupportPrivileges").hide('fast');
					$("#notSupportPrivilegesDiv").hide('fast');
				}
			});

			$("#941").click(function () {
				if ($("#941").is(":checked")) {
					$("#extra").show('fast');
					$("#isSupportPrivileges").show('fast');
					$("#supportPrivilegesDiv").show('fast');
				}
			});

			$("#supportPrivileges").click(function () {
				if ($("#supportPrivileges").is(":checked")) {
					$("#supportPrivilegesDiv").show('fast');
					$("#notSupportPrivilegesDiv").hide('fast');
				}
			});

			$("#notSupportPrivileges").click(function () {
				if ($("#notSupportPrivileges").is(":checked")) {
					$("#supportPrivilegesDiv").hide('fast');
					$("#notSupportPrivilegesDiv").show('fast');
				}
			});
		});
	</script>
	{$jscode1}
	{$jscode2}
{/block}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">إضافة عملية</h4>
	</div>
	<div class="modal-body">
		<form  method="post" action='{url urltype="path" url_string="adm/P001/opr/show/0/{$smarty.session.lang}/update/{$smarty.session.s_operation_token}/{$row->id}"}'>
			<div class="row snsowraper">

				<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel'>{#gnr_name#}</div>
				<div class='col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput'><input type="text" class="form-control" name="translatedName" value="{$row->translatedName}"></div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">الرمز</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="code" value="{$row->code}" required></div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">الترتيب</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="order" value="{$row->order}" required></div>



				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">نوع الإرتباط</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
					<div class="control-group">
						{foreach $hirarchicalList as $hrrow}
							<div class="radio">
								<label>
									<input name="hirarchical_type" id="{$hrrow->id}" value="{$hrrow->id}" {if $hrrow->id eq $row->hirarchical_type} checked="checked" {/if} type="radio">
									<span class="text">{$hrrow->translatedName}</span>
								</label>
							</div>
						{/foreach}
					</div>
				</div>

				<div id="extra">
					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">فرعية من</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						<select name="hirarchical_prior_opr_id">
							{foreach $oprList as $opr}
								<option value="{$opr->id}" {if $opr->id eq $row->hirarchical_prior_opr_id} selected {/if}>{getname table=st_setting id=$opr->hirarchical_type} &nbsp;&raquo;&nbsp; {$opr->translatedName}&nbsp;&raquo;&nbsp;[{$opr->code}]</option>
							{/foreach}
						</select>
					</div>
				</div>

				<div id="isSupportPrivileges">
					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">تدعم الصلاحيات</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						<div class="control-group">
							<div class="radio">
								<label>
									<input name="show_in_prv_list" id="supportPrivileges" type="radio" checked value="1" {if $row->show_in_prv_list eq 1} checked {/if} required>
									<span class="text">تدعم الصلاحيات</span>
								</label>
							</div>
							<div class="radio">
								<label>
									<input name="show_in_prv_list" id="notSupportPrivileges" type="radio" value="0" {if $row->show_in_prv_list eq 0} checked {/if} required>
									<span class="text">لا تدعم الصلاحيات</span>
								</label>
							</div>
						</div>
					</div>
				</div>

				<div id="supportPrivilegesDiv">
					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">صلاحيات العملية</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						{foreach $oprvtype_list as $privilege}
							<div class="checkbox">
								<label>
									<input type="checkbox" name="prv[]" value="{$privilege->id}" {if in_array($privilege->id,','|explode:$row->prv)} checked="checked" {/if}>
									<span class="text">{$privilege->translatedName}</span>
								</label>
							</div>
						{/foreach}
					</div>
				</div>

				<div id="notSupportPrivilegesDiv">
					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">تعتمد في صلاحياتها على</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						<select name="forpreviligesdependon">
							{foreach $dependOnPrivilegeOn as $oprDependOn}
								<option value="{$oprDependOn->id}" {if $oprDependOn->id eq $row->forpreviligesdependon} selected {/if}>{t v=$oprDependOn->hirarchical_type} &nbsp;&raquo;&nbsp; {$oprDependOn->translatedName}&nbsp;&raquo;&nbsp;[{$oprDependOn->code}]</option>
							{/foreach}
						</select>
					</div>
				</div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">الظهور في القوائم</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
					<div class="control-group">
						<div class="radio">
							<label>
								<input name="showable" type="radio" value="1" {if $row->showable eq 1} checked {/if} required>
								<span class="text">{#gnr_yes#}</span>
							</label>

							<label>
								<input name="showable" type="radio" value="0" {if $row->showable eq 0} checked {/if} required>
								<span class="text">{#gnr_no#}</span>
							</label>
						</div>
					</div>
				</div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">لديها مرفقات</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
					<div class="control-group">
						<div class="radio">
							<label>
								<input name="have_doc" type="radio" value="1" {if $row->have_doc eq 1} checked {/if} required>
								<span class="text">{#gnr_yes#}</span>
							</label>

							<label>
								<input name="have_doc" type="radio" value="0" {if $row->have_doc eq 0} checked {/if} required>
								<span class="text">{#gnr_no#}</span>
							</label>
						</div>
					</div>
				</div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">لديها رسائل جوال</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
					<div class="control-group">
						<div class="radio">
							<label>
								<input name="have_sms" type="radio" value="1" {if $row->have_sms eq 1} checked {/if} required>
								<span class="text">{#gnr_yes#}</span>
							</label>

							<label>
								<input name="have_sms" type="radio" value="0" {if $row->have_sms eq 0} checked {/if} required>
								<span class="text">{#gnr_no#}</span>
							</label>
						</div>
					</div>
				</div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">تدعم منظومة الإجراءات</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
					<div class="control-group">
						<div class="radio">
							<label>
								<input name="have_wf" type="radio" value="1" {if $row->have_wf eq 1} checked {/if} required>
								<span class="text">{#gnr_yes#}</span>
							</label>

							<label>
								<input name="have_wf" type="radio" value="0" {if $row->have_wf eq 0} checked {/if} required>
								<span class="text">{#gnr_no#}</span>
							</label>
						</div>
					</div>
				</div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">تدعم منظومة الخدمات</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
					<div class="control-group">
						<div class="radio">
							<label>
								<input name="have_es" type="radio" value="1" {if $row->have_es eq 1} checked {/if} required>
								<span class="text">{#gnr_yes#}</span>
							</label>

							<label>
								<input name="have_es" type="radio" value="0" {if $row->have_es eq 0} checked {/if} required>
								<span class="text">{#gnr_no#}</span>
							</label>
						</div>
					</div>
				</div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">تدعم منظومة الفحص</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
					<div class="control-group">
						<div class="radio">
							<label>
								<input name="have_ut" type="radio" value="1" {if $row->have_ut eq 1} checked {/if} required>
								<span class="text">{#gnr_yes#}</span>
							</label>

							<label>
								<input name="have_ut" type="radio" value="0" {if $row->have_ut eq 0} checked {/if} required>
								<span class="text">{#gnr_no#}</span>
							</label>
						</div>
					</div>
				</div>


				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">تعتمد في ظهورها على البرنامج</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
					<select name="depends_on_program">
						<option value="">---</option>
                        {foreach $programs as $program}
							<option value="{$program->id}" {if $program->id eq $row->depends_on_program}selected{/if}>{$program->translatedName}</option>
                        {/foreach}
					</select>
				</div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">نموذج بيانات العملية</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
					<select name="form_id">
						{foreach $form_list as $frow}
							<option value="{$frow->id}" {if $frow->id eq $row->form_id} selected="selected" {/if}>{$frow->translatedName}</option>
						{/foreach}
					</select>
				</div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">نوع العملية</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
					<select name="type" required="required">
						{foreach $oprtype_list as $orow}
							<option value="{$orow->id}" {if $orow->id eq $row->type} selected="selected" {/if}>{$orow->translatedName}</option>
						{/foreach}
					</select>
				</div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">جدول العملية</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="tbl_name" value="{$row->tbl_name}"></div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">ظهور علي حسب نوع المنشأه</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
					<select name="org_type" required="required">
						<option value="0">الكل</option>
                        {foreach $organizationTypes as $type}
							<option value="{$type->id}" {if $type->id eq $row->org_type}selected="selected"{/if}>{$type->translatedName}</option>
                        {/foreach}
					</select>
				</div>

				<div id="notSupportPrivilegesDiv">
					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">نقلها الي قسم</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						<select name="section_id">
                            {foreach $sections as $section}
								<li>
									<option value="{$section->id}"
                                            {if $section->id eq $row->sec_id } selected {/if}>
											{$section->translatedName}
									</option>
								</li>
                            {/foreach}
						</select>
					</div>
				</div>

				<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">&nbsp;</div>
				<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>

			</div>
		</form>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}