{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=page_body}

	<div class="well with-header">
		<div class="header bordered-seashell"><h5 class="sky"><i class="fa fa-language" ></i>&nbsp;{$translation->languageObject->name}&nbsp;[{$translation->languageObject->code}]</h5></div>
		<div class="buttons-preview">
			<div class="row">
				<div class="col-lg-6">
                    {getname table=st_setting id=$translation->languageObject->direction}
				</div>
				<div class="col-lg-6">
                    {url check=1 urltype="mbutton" oprvtype=3 opr_code='adminTranslation' url_string="adm/P001/adminTranslation/import/0/{$smarty.session.lang}/{$session['token']}" text_value="<i class='fa fa-cog'></i>&nbsp;{#gnr_import#}"}
                    {url check=1 urltype="button" oprvtype=3 opr_code='adminTranslation' url_string="adm/P001/adminTranslation/export/0/{$smarty.session.lang}/{$session['token']}" text_value="<i class='fa fa-cog'></i>&nbsp;{#gnr_export#}"}
				</div>
			</div>
		</div>
	</div>

	<div class="row">

		<div class="col-lg-3">
			<div class="well well-sm">
				<h4 class="block sky">{#p_translate_dropdown_and_settings#}</h4>
				{url check=1 urltype="button" oprvtype=3 opr_code='adminTranslation' url_string="adm/P001/adminTranslation/dropDownTranslation/0/{$smarty.session.lang}/{$session['token']}" text_value="<i class='fa fa-cog'></i>&nbsp;{#gnr_translatetion#}"}
				&nbsp; <span class="gray">[&nbsp;{$translation->dropdown_translation_percentage}%&nbsp;]</span>
			</div>
		</div>

		<div class="col-lg-3">
			<div class="well well-sm">
				<h4 class="block sky">{#p_translate_general_translations_variables#}</h4>
                {url check=1 urltype="button" oprvtype=3 opr_code='adminTranslation' url_string="adm/P001/adminTranslation/generalTranslation/0/{$smarty.session.lang}/{$session['token']}" text_value="<i class='fa fa-cog'></i>&nbsp;{#gnr_translatetion#}"}
                &nbsp; <span class="gray">[&nbsp;{$translation->general_translation_percentage}%&nbsp;]</span>
			</div>
		</div>

        <div class="col-lg-3">
            <div class="well well-sm">
                <h4 class="block sky">{#p_translate_additional_translations_variables#}</h4>
                {url check=1 urltype="button" oprvtype=3 opr_code='adminTranslation' url_string="adm/P001/adminTranslation/additionalTranslation/0/{$smarty.session.lang}/{$session['token']}" text_value="<i class='fa fa-cog'></i>&nbsp;{#gnr_translatetion#}"}
                &nbsp; <span class="gray">[&nbsp;{$translation->additional_translation_percentage}%&nbsp;]</span>
            </div>
        </div>

        <div class="col-lg-3">
            <div class="well well-sm">
                <h4 class="block sky">{#p_translate_report_translations_variables#}</h4>
                {url check=1 urltype="button" oprvtype=3 opr_code='adminTranslation' url_string="adm/P001/adminTranslation/reportTranslation/0/{$smarty.session.lang}/{$session['token']}" text_value="<i class='fa fa-cog'></i>&nbsp;{#gnr_translatetion#}"}
                &nbsp; <span class="gray">[&nbsp;{$translation->reports_translation_percentage}%&nbsp;]</span>
            </div>
        </div>
	</div>

	<div class="row">
		{foreach $programs as $program}
			<div class="col-lg-6">
				<div class="well">
					<div class="row">

						<div class="col-lg-12">
							<div class="col-lg-8">
								<h5 class="sky"><i class="fa fa-language" ></i>&nbsp;{$program->translatedName}&nbsp;<br>
									<div class="gray" style="margin-top: 5px;">
										{$program->code}&nbsp;
										{assign var=transationPercentage value=json_decode($translation->programs_translation_percentage,true)}
										<span style="font-size: smaller"> [&nbsp;{$transationPercentage.{$program->code}}%&nbsp;]</span>
									</div>
								</h5>
							</div>
							<div class="col-lg-4">
                                {url check=1 urltype="button" oprvtype=3 opr_code='adminTranslation' url_string="adm/P001/adminTranslation/programTranslation/0/{$smarty.session.lang}/{$session['token']}/{$program->id}" text_value="<i class='fa fa-cog'></i>&nbsp;{#p_translate_program#}"}
							</div>
						</div>
					</div>
				</div>
			</div>
        {/foreach}
	</div>

{/block}
{block name=back}{url urltype="path" url_string="adm/P001/adminTranslation/show/0/{$smarty.session.lang}"}{/block}