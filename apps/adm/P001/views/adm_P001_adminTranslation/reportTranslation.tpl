{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=page_body}

    <div class="well well-sm">
        <h5 class="sky">
            <span><i class="fa fa-language" ></i>&nbsp;{$translationTo->languageObject->name}&nbsp;[{$translationTo->languageObject->code}]</span>
            &nbsp;|&nbsp;
            <span>{#p_translate_dropdown_and_settings#}</span>
            &nbsp;|&nbsp;
            <span>[&nbsp;{$translationTo->reports_translation_percentage}%&nbsp;]</span>
        </h5>
    </div>

	<form  method="post" action='{url urltype="path" url_string="adm/P001/adminTranslation/reportTranslation/0/{$smarty.session.lang}/{$session['token']}/update/{$session['DMToken']}"}'>

        {$i=3}
        {foreach $reportArray.Subject as $key => $Subjects}
            <div class="well well-sm">
                <div class="form-group has-info has-feedback">
                    <label class="control-label" for="inputTranslation"><span>[{$i++}]&nbsp;{$Subjects.label}&nbsp;<span style="font-size: smaller; color: maroon">`&nbsp;{$Subjects.name}&nbsp;`</span></span></label>
                    <input type="text" class="form-control" id="inputTranslation" name="{Translation::SUBJECT_TRANSLATION}[{$Subjects.name}]" value="{$Subjects.value}">
                </div>
            </div>
            {$j=1}
            {foreach $Subjects.Report as $Reports}

                <div class="row">
                    <div class="col-lg-12">
                        <div class="col-lg-1">&nbsp;</div>
                        <div class="col-lg-11">
                            <div class="well well-sm">
                                <div class="form-group has-info has-feedback">
                                    <label class="control-label" for="inputTranslation"><span>[{$j++}]&nbsp;{$Reports.label}&nbsp;<span style="font-size: smaller; color: maroon">`&nbsp;{$Reports.name}&nbsp;`</span></span></label>
                                    <input type="text" class="form-control" id="inputTranslation" name="{Translation::REPORT_TRANSLATION}[{$Reports.name}]" value="{$Reports.value}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-12">
                        <div class="col-lg-2">&nbsp;</div>
                        <div class="col-lg-10">
                            {$k=1}
                            {foreach $Reports.variables as $key => $value}
                                <div class="well well-sm">
                                    <div class="form-group has-info has-feedback">
                                        <label class="control-label" for="inputTranslation"><span>[{$k++}]&nbsp;{$value.label}&nbsp;<span style="font-size: smaller; color: maroon">`&nbsp;{$key}&nbsp;`</span></span></label>
                                        <input type="text" class="form-control" id="inputTranslation" name="{$Reports.name}[{$key}]" value="{$value.value}">
                                    </div>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                </div>

            {/foreach}
        {/foreach}

        <hr>

        {$x=1}
        {foreach $reportArray.embedded as $key => $var}
            <div class="well well-sm">
                <div class="form-group has-info has-feedback">
                    <label class="control-label" for="inputTranslation"><span>[{$x++}]&nbsp;{$var.label}&nbsp;<span style="font-size: smaller; color: maroon">`&nbsp;{$var.name}&nbsp;`</span></span></label>
                    <input type="text" class="form-control" id="inputTranslation" name="{Translation::IMPEDED_TRANSLATION}[{$var.name}]" value="{$var.value}">
                </div>
            </div>
        {/foreach}

		<hr>
		<div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
		</div>

	</form>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/adminTranslation/dashboard/0/{$smarty.session.lang}/setSession/{$session['token']}"}{/block}
