{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=page_body}

	<form  method="post" action='{url urltype="path" url_string="adm/P001/adminTranslation/programTranslation/0/{$smarty.session.lang}/{$session['token']}/{$program->id}/update/{$session['DMToken']}"}'>

		<div class="well well-sm">
			<h5 class="sky">
				<span><i class="fa {$program->icon}" ></i>
                {$program->code}
                &nbsp;|&nbsp;
                {$program->translatedName}</span>
				&nbsp;|&nbsp;
				<span><i class="fa fa-language" ></i>&nbsp;{$translationTo->languageObject->name}&nbsp;[{$translationTo->languageObject->code}]</span>
				&nbsp;|&nbsp;
				<span>{#p_translate_program#}&nbsp; <span class="gray">
                &nbsp;|&nbsp;
                {assign var=transationPercentage value=json_decode($translationTo->programs_translation_percentage,true)}
                [&nbsp;{$transationPercentage.{$program->code}}%&nbsp;]</span></span>
			</h5>
		</div>

        <div class="well well-sm">
            <div class="form-group has-info has-feedback">
                <label class="control-label" for="inputTranslation"><span>[1]&nbsp;{#gnr_name#}&nbsp;<span style="font-size: smaller; color: maroon">`&nbsp;{$programArray.Name_name}&nbsp;`</span></span></label>
                <div style="padding: 5px; background-color: #1b6d85; color: white">{$programArray.Name_label}</div>
                <input type="text" class="form-control" id="inputTranslation" name="{Translation::PROGRAMS_TRANSLATION}[{$programArray.Name_name}]" value="{$programArray.Name_value}">
            </div>

            <div class="form-group has-info has-feedback">
                <label class="control-label" for="inputTranslation"><span>[2]&nbsp;{#gnr_about#}&nbsp;<span style="font-size: smaller; color: maroon">`&nbsp;{$programArray.About_name}&nbsp;`</span></span></label>
                <div style="padding: 5px; background-color: #1b6d85; color: white">{$programArray.About_label}</div>
                <textarea type="text" class="form-control" id="inputTranslation" name="{Translation::PROGRAMS_TRANSLATION}[{$programArray.About_name}]">{$programArray.About_value}</textarea>
            </div>
        </div>


        {$i=3}
        {foreach $programArray.menus as $key => $menu}
            <div class="well well-sm">
                <div class="form-group has-info has-feedback">
                    <label class="control-label" for="inputTranslation"><span>[{$i++}]&nbsp;{$menu.label}&nbsp;<span style="font-size: smaller; color: maroon">`&nbsp;{$menu.name}&nbsp;`</span></span></label>
                    <input type="text" class="form-control" id="inputTranslation" name="{Translation::MENUS_TRANSLATION}[{$menu.name}]" value="{$menu.value}">
                </div>
            </div>
            {$j=1}
            {foreach $menu.operations as $operation}

                <div class="row">
                    <div class="col-lg-12">
                        <div class="col-lg-1">&nbsp;</div>
                        <div class="col-lg-11">
                            <div class="well well-sm">
                                <div class="form-group has-info has-feedback">
                                    <label class="control-label" for="inputTranslation"><span>[{$j++}]&nbsp;{$operation.label}&nbsp;<span style="font-size: smaller; color: maroon">`&nbsp;{$operation.name}&nbsp;`</span></span></label>
                                    <input type="text" class="form-control" id="inputTranslation" name="{Translation::OPERATION_TRANSLATION}[{$operation.name}]" value="{$operation.value}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-12">
                        <div class="col-lg-2">&nbsp;</div>
                        <div class="col-lg-10">
                            {$k=1}
                            {foreach $operation.variables as $key => $value}
                                <div class="well well-sm">
                                    <div class="form-group has-info has-feedback">
                                        <label class="control-label" for="inputTranslation"><span>[{$k++}]&nbsp;{$value.label}&nbsp;<span style="font-size: smaller; color: maroon">`&nbsp;{$key}&nbsp;`</span></span></label>
                                        <input type="text" class="form-control" id="inputTranslation" name="{$operation.name}[{$key}]" value="{$value.value}">
                                    </div>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                </div>

            {/foreach}
        {/foreach}

        <hr>

        {$x=1}
        {foreach $programArray.Impeded as $key => $var}
            <div class="well well-sm">
                <div class="form-group has-info has-feedback">
                    <label class="control-label" for="inputTranslation"><span>[{$x++}]&nbsp;{$var.label}&nbsp;<span style="font-size: smaller; color: maroon">`&nbsp;{$var.name}&nbsp;`</span></span></label>
                    <input type="text" class="form-control" id="inputTranslation" name="{Translation::IMPEDED_TRANSLATION}[{$var.name}]" value="{$var.value}">
                </div>
            </div>
        {/foreach}

		<hr>
		<div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
		</div>

	</form>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/adminTranslation/dashboard/0/{$smarty.session.lang}/setSession/{$session['token']}"}{/block}
