{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=page_body}

	<div class="well well-sm">
		<h5 class="sky">
			<span><i class="fa fa-language" ></i>&nbsp;{$translation->languageObject->name}&nbsp;[{$translation->languageObject->code}]</span>
			&nbsp;|&nbsp;
			<span>{#p_translate_dropdown_and_settings#}&nbsp; <span class="gray">[&nbsp;{$translation->dropdown_translation_percentage}%&nbsp;]</span></span>
		</h5>
	</div>

	<form  method="post" action='{url urltype="path" url_string="adm/P001/adminTranslation/dropDownTranslation/0/{$smarty.session.lang}/{$session['token']}/{Operation::UPDATE}/{$session['DMToken']}"}'>

        {$i=1}
        {foreach $dropDownLists as $dropDownList}

			<div class="well well-sm">
				<div class="row">
					<div class="col-lg-10">
						<div class="form-group has-info has-feedback">
							<label class="control-label" for="inputTranslation"><span>[{$i++}]&nbsp;{$dropDownList.label}</span>&nbsp;<span style="font-size: smaller; color: maroon">`&nbsp;{$dropDownList.name}&nbsp;`</span></label>
							<input class="form-control" id="inputTranslation" name="{Translation::DROPDOWN_TRANSLATION}[{$dropDownList.name}]" value="{$dropDownList.value}">
						</div>
					</div>
					<div class="col-lg-2">
						<div class="form-group has-info has-feedback">
							<br>
							{url check=1 urltype="mbutton" oprvtype=3 opr_code='adminTranslation' url_string="adm/P001/adminTranslation/settingTranslation/0/{$smarty.session.lang}/{$session['token']}/{$dropDownList.id}" text_value="{#p_config_translation#}"}

							&nbsp;
                            {if $dropDownList.percentage eq 100}
								<span style="color: blue;">{$dropDownList.percentage}%</span>
								{else}
								<span style="color: darkred;">{$dropDownList.percentage}%</span>
                            {/if}
						</div>
					</div>
				</div>
			</div>

        {/foreach}

		<hr>
		<div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
		</div>

	</form>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/adminTranslation/dashboard/0/{$smarty.session.lang}/setSession/{$session['token']}"}{/block}
