{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />{/block}
{block name=body}
<div class="row snsowraper">
	<table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
		<thead>
		<tr>
			<th width="5%"  style="background-color: #A0D468 !important;">{url check=0 urltype="madd" opr_code='socialmedia' url_string="adm/P001/socialmedia/add/0/{$smarty.session.lang}"}</th>
			<th width="30%" style="background-color: #A0D468 !important;"><b>{#gnr_name#}</b></th>
			<th width="15%" style="background-color: #A0D468 !important;"><b>{#gnr_id#}</b></th>
			<th width="15%" style="background-color: #A0D468 !important;"><b>{#gnr_settings#}</b></th>
		</tr>
		</thead>
		<tbody>
		{$i=1}
		{foreach $SocialMedia as $row}
			<tr>
				<td align="center">{$i++}</td>
				<td align="center">{$row->name}</td>
				<td align="center">{$row->code}</td>
				<td nowrap>
					{url check=0 urltype="medit" opr_code='socialmedia' url_string="adm/P001/socialmedia/edit/0/{$smarty.session.lang}/{$row->id}"}
					{url check=0 urltype="mdelete" opr_code='socialmedia' url_string="adm/P001/socialmedia/confirm/0/{$smarty.session.lang}/{$row->id}"}
				</td>
			</tr>
		{/foreach}
		</tbody>
	</table>
</div>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/prg/show/0/{$smarty.session.lang}"}{/block}
{block name=page_header}
	<script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
	<script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
	<script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
	<script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
	<script>
		var InitiateSimpleDataTable = function() {
			return {
				init: function() {
					//Datatable Initiating
					var oTable = $('.sortable-table').dataTable({
						"sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
						"iDisplayLength": 50,
						"oTableTools": {
							"aButtons": [
//								"copy", "csv", "xls", "pdf", "print"
							],
							"sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
						},
						"language": {
							"search": "",
							"sLengthMenu": "_MENU_",
							"oPaginate": {
								"sPrevious": "{#gnr_previous#}",
								"sNext": "{#gnr_next#}"
							}
						}
					});
				}

			};

		}();

		InitiateSimpleDataTable.init();
	</script>
{/block}