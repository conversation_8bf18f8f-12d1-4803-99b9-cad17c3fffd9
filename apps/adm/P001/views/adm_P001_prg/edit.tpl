{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#p_prg_edit#}</h4>
	</div>
	<div class="modal-body">
		<form  method="post" action='{url urltype="path" url_string="adm/P001/prg/show/0/{$smarty.session.lang}/update/{$smarty.session.s_program_token}/{$program->id}"}'>
			<div class="row snsowraper">

				<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel'>{#gnr_bundle#}</div>
				<div class='col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput'>
					<select name="bnd_id" id="">
						{foreach $bundles as $bundle}
							<option value="{$bundle->id}" {if $program->bnd_id eq $bundle->id} selected="selected" {/if}>{$bundle->translatedName}</option>
						{/foreach}
					</select>
				</div>

				<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel'>{#gnr_name#}</div>
				<div class='col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput'><input type="text" class="form-control" name="translatedName" value="{$program->translatedName}" required="required"></div>

				<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_code#}</div>
				<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="code" value="{$program->code}"></div>

				<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_icon#}</div>
				<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="icon" value="{$program->icon}" required></div>

				<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_prg_display_order#}</div>
				<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="order" value="{$program->order}" required></div>

				<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
				<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
			</div>
		</form>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}