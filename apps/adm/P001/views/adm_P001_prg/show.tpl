{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=body}

<div class="btn-group">
	<a class="btn btn-success " href="javascript:void(0);">
		{if !empty($smarty.session.s_bnd_id)}
			{$bundle->translatedName}
		{else}
			<span>{#p_choose_menu_from_list_above#} </span>
		{/if}
	</a>
	<a class="btn btn-success  dropdown-toggle" data-toggle="dropdown" href="javascript:void(0);" aria-expanded="false"><i class="fa fa-angle-down"></i></a>
	<ul class="dropdown-menu dropdown-success">
		{foreach $bundles as $bundleObject}
			<li>
				<a href="{url urltype="path" url_string="adm/P001/prg/show/0/{$smarty.session.lang}/save_session/{$bundleObject->id}"}">
					{$bundleObject->translatedName}
				</a>
			</li>
		{/foreach}
	</ul>
</div>

<div class="horizontal-space"></div>

<div class="row snsowraper">
	<div class="table-responsive" data-pattern="priority-columns">
		<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
			<thead>
				<tr>
					<th width="5%" align="center">
						{if $bundle}
							{url check=0 urltype="madd" opr_code='prg' url_string="adm/P001/prg/add/0/{$smarty.session.lang}"}
						{else}
							{url urltype="disabled"}
						{/if}
					</th>
					<th width="15%" align="right"><b>{#gnr_code#}</b></th>
					<th width="30%" align="right"><b>{#gnr_program#}</b></th>
					<th width="10%" align="right"><b>{#gnr_order#}</b></th>
					<th width="10%" align="right"><b>{#gnr_menues#}</b></th>
					<th width="10%" align="right"><b>{#gnr_elements#}</b></th>
					<th width="10%" align="center"><b>{#gnr_settings#}</b></th>
				</tr>
			</thead>
			<tbody>
			{$i=1}
			{foreach $programs as $program}
				<tr">
					<td align="center">{$i++}</td>
					<td align="right">{$program->code}</td>
					<td align="right">
						{url urltype="alinkn" url_string="gnr/X000/chart/prgDiagram/0/{$smarty.session.lang}/{$program->id}" text_value='&nbsp;<i class="fa fa-sitemap"></i>' modal="modal" style="btn btn-sky shiny"}
						&nbsp;
						{$program->translatedName}
					</td>
					<td align="right">{$program->order}</td>
					<td align="right">{url check=0 urltype="button" opr_code='sec' oprvtype=1 url_string="adm/P001/sec/show/0/{$smarty.session.lang}/save_session/{$program->id}" text_value="{#gnr_menues#}"}</td>
					<td align="right">{url check=0 urltype="button" opr_code='opr' oprvtype=1 url_string="adm/P001/opr/show/0/{$smarty.session.lang}/save_session/{$program->id}"  text_value="{#gnr_elements#}"}</td>
					<td align="center" nowrap>
						{url check=0 urltype="medit" opr_code='prg' url_string="adm/P001/prg/edit/0/{$smarty.session.lang}/{$program->id}"}
						{if $program->operationsNumber eq 0}
							{url check=0 urltype="mdelete" opr_code='prg' url_string="adm/P001/prg/confirm/0/{$smarty.session.lang}/{$program->id}"}
						{/if}
						&nbsp;[&nbsp;{$program->operationsNumber}&nbsp;]
					</td>
				</tr>
			{/foreach}
			</tbody>
		</table>
	</div>
</div>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/prg/show/0/{$smarty.session.lang}"}{/block}
