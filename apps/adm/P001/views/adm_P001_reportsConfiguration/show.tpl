{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />{/block}
{block name=page_body}
	<div class="row">
		<div class="col-lg-12">
            {url check=0 urltype="button" url_string="adm/P001/reportsConfiguration/show/0/{$smarty.session.lang}/subject/updateSubjectsList/{$smarty.session.s_reportsConfiguration_token}" text_value="{#p_update_subject_list#}"}
		</div>
	</div>
<br>

    {if $nonClassifiedReports}
        <div class="widget">
            <div class="widget-header bordered-bottom bordered-themesecondary">
                <i class="widget-icon fa fa-tags themesecondary"></i>
                <span class="widget-caption themesecondary">{#p_non_classified_reports#}</span>
            </div><!--Widget Header-->
            <div class="widget-body  no-padding">
                <div class="tickets-container">
                    <ul class="tickets-list">

                        {foreach $nonClassifiedReports as $subject}

                            <li class="ticket-item">

                                <div class="row">
                                    <div class="ticket-user col-lg-12 col-sm-12">
									<span class="user-name">
										{url check=1 urltype="medit" opr_code='reportsConfiguration' url_string="adm/P001/reportsConfiguration/editSubject/0/{$smarty.session.lang}/0/{$subject->id}"}
                                        &nbsp;
                                        {$subject->code}
									</span>
                                    </div>
                                </div>

                            </li>

                        {/foreach}
                    </ul>
                </div>
            </div>
        </div>
    {/if}

	<div class="tabbable tabs-left">
		<ul class="nav nav-tabs" id="myTab3">
			{$i=1}
			{foreach $categories as $category}
				<li class="{if $smarty.session.s_subject_category eq $category->id} active {/if} tab-sky">
					<a data-toggle="tab" href="#{$category->id}">
						<span class="badge badge-sky badge-square">{$i++}</span>
						<span>{$category->translatedName}</span>
					</a>
				</li>
			{/foreach}
		</ul>

		<div class="tab-content">

			{foreach $categories as $category}
				<div id="{$category->id}" class="tab-pane {if $smarty.session.s_subject_category eq $category->id} in active {/if}">
					<div class="tabbable">
						<ul class="nav nav-tabs nav-justified" id="myTab5">
							<li class="tab-sky {if $smarty.session.s_horizontal_tab eq {$category->id|cat:'subject'}} active {/if} {if $smarty.session.s_horizontal_tab neq {$category->id|cat:'subject'} and $smarty.session.s_horizontal_tab neq {$category->id|cat:'report'}} active {/if}">
								<a data-toggle="tab" href="#{$category->id}subject">
                                    {#p_subjects#}
								</a>
							</li>

							<li class="tab-red {if $smarty.session.s_horizontal_tab eq {$category->id|cat:'report'}} active {/if}">
								<a href="#{$category->id}report">
                                    {#p_reports#}
								</a>
							</li>
						</ul>

						<div class="tab-content">
							<div id="{$category->id}subject" class="tab-pane {if $smarty.session.s_horizontal_tab eq {$category->id|cat:'subject'}} in active {/if} {if $smarty.session.s_horizontal_tab neq {$category->id|cat:'subject'} and $smarty.session.s_horizontal_tab neq {$category->id|cat:'report'}} in active {/if}">
								<table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
									<thead>
									<tr>
                                        <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
                                        <th style="background-color: #A0D468 !important;" width="25%">{#gnr_name#}</th>
                                        <th style="background-color: #A0D468 !important;" width="25%">{#gnr_code#}</th>
                                        <th style="background-color: #A0D468 !important;" width="25%">{#gnr_status#}</th>
                                        <th style="background-color: #A0D468 !important;" width="20%">{#gnr_settings#}</th>
									</tr>
									</thead>
									<tbody>
									{$i=1}
									{foreach $category->subjects as $subject}
										<tr>
											<td align="center">{$subject->order}</td>
											<td align="center">{$subject->translatedName}</td>
											<td align="center">{$subject->code}</td>
											<td align="center">{t v=$subject->activation}</td>
											<td align="center" nowrap>
                                                {url check=0 urltype="button" opr_code='reportsConfiguration' url_string="adm/P001/reportsConfiguration/show/0/{$smarty.session.lang}/report/browseReports/{$category->id}/{$subject->id}" text_value="{#gnr_reports#}"}
                                                &nbsp;
                                                {url check=1 urltype="medit" opr_code='reportsConfiguration' url_string="adm/P001/reportsConfiguration/editSubject/0/{$smarty.session.lang}/{$category->id}/{$subject->id}"}
												&nbsp;&nbsp;
                                                {if $subject->reportsNumber eq 0 and $subject->activation eq Setting::NOT_ACTIVE}
                                                    {url check=1 urltype="mdelete" opr_code='reportsConfiguration' url_string="adm/P001/reportsConfiguration/confirmSubject/0/{$smarty.session.lang}/{$category->id}/{$subject->id}"}
                                                {/if}
											</td>
										</tr>
									{/foreach}
									</tbody>
								</table>
							</div>

							<div id="{$category->id}report" class="tab-pane {if $smarty.session.s_horizontal_tab eq {$category->id|cat:'report'}} in active {/if}">

								<h5 class="row-title blue">
									<i class="fa fa-circle blue"></i>
									{$category->translatedName}&nbsp;&raquo;&nbsp;{$currentSubject->translatedName}
								</h5>

								<table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
									<thead>
									<tr>
										 <th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
										<th style="background-color: #A0D468 !important;" width="25%">{#gnr_name#}</th>
										<th style="background-color: #A0D468 !important;" width="25%">{#gnr_code#}</th>
										 <th style="background-color: #A0D468 !important;" width="25%">{#gnr_status#}</th>
										 <th style="background-color: #A0D468 !important;" width="20%">{#gnr_settings#}</th>
									</tr>
									</thead>
									<tbody>
									{$i=1}
									{foreach $reports as $report}
										<tr>
											<td align="center">{$report->order}</td>
											<td align="center">{$report->translatedName}</td>
                                            <td align="center">{$report->code}</td>
                                            <td align="center">{t v=$report->activation}</td>
											<td align="center">
                                                {url  check=1 oprvtype=3 opr_code='reportsConfiguration' urltype="medit" url_string="adm/P001/reportsConfiguration/editReport/0/{$smarty.session.lang}/{$category->id}/{$report->subject_id}/{$report->id}"}
                                                {if $report->activation eq Setting::NOT_ACTIVE}
                                                    {url check=1 urltype="mdelete" opr_code='reportsConfiguration' url_string="adm/P001/reportsConfiguration/confirmReport/0/{$smarty.session.lang}/{$report->id}"}
                                                {/if}
											</td>
										</tr>
									{/foreach}
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			{/foreach}
		</div>
	</div>
{/block}

{block name=page_header}
	<script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
	<script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
	<script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
	<script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
	<script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
	<script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
	<script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
	<script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
	<script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
	<script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
	<script>
        {literal}
        function exportTo(ID, type) {
            $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
        }
        {/literal}
	</script>
	<script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
	</script>
{/block}