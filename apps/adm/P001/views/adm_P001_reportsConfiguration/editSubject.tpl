{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{$category->translatedname}&nbsp;&raquo;&nbsp;{#p_edit_report#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form method="post" action='{url urltype="path" url_string="adm/P001/reportsConfiguration/show/0/{$smarty.session.lang}/subject/update/{$category->id}/{$smarty.session.s_reportsConfiguration_token}/{$subject->id}"}'>
                <div class="col-lg-12">

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_code#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$subject->code}<input type="hidden" name="code" value="{$subject->code}"></div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="translatedName" value="{$subject->translatedName}" class="form-control" placeholder="{#gnr_name#}" required></div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_report_category#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        <select name="category_id" id="{$category->id}">
                            {foreach $categories as $category}
                                <option value="{$category->id}" {if $category->id eq $subject->category_id} selected="selected" {/if}>{$category->translatedName}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_activation#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        {foreach $activations as $activation}
                            <div class="radio">
                                <label>
                                    <input name="activation" id="{$activation->id}" value="{$activation->id}" {if $activation->id eq $subject->activation} checked="checked" {/if} type="radio">
                                    <span class="text">{$activation->translatedName}</span>
                                </label>
                            </div>
                        {/foreach}
                    </div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_programs#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        {foreach $programs as $program}
                            <div class="checkbox">
                                <label>
                                    <input name="programs_ids[]" id="{$program->id}" value="{$program->id}" {if in_array($program->id,$subject->programs_ids)} checked="checked" {/if} type="checkbox">
                                    <span class="text">{$program->translatedName}</span>
                                </label>
                            </div>
                        {/foreach}
                    </div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_order#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="number" name="order" value="{$subject->order}" class="form-control" placeholder="{#gnr_order#}"></div>

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>

                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}