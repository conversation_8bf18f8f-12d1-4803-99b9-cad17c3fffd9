{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{$category->translatedName}&nbsp;&raquo;&nbsp;{$subject->translatedName}&nbsp;&raquo;&nbsp;{#p_edit_report#}</h4>
	</div>
	<div class="modal-body">
		<div class="row">
			<form method="post" action='{url urltype="path" url_string="adm/P001/reportsConfiguration/show/0/{$smarty.session.lang}/report/update/{$smarty.session.s_reportsConfiguration_token}/{$report->id}"}'>
				<div class="col-lg-12">

					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_code#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$report->code} <input type="hidden" name="code" value="{$report->code}"></div>

					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="translatedName" value="{$report->translatedName}" class="form-control" placeholder="{#gnr_data_name#}" required></div>

					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_data_type#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						<select name="data_type">
                            {foreach $reportTypes as $type}
								<option value="{$type->id}" {if $type->id eq $report->category_id} selected="selected" {/if}>{$type->translatedName}</option>
                            {/foreach}
						</select>
					</div>

					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_report_type#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						<div class="radio">
							<label>
								<input name="type" value="{Report::MAIN_REPORT}" {if not $report->type or $report->type eq Report::MAIN_REPORT}checked="checked"{/if} type="radio">
								<span class="text">{#p_main_report#}</span>
							</label>
						</div>
						<div class="radio">
							<label>
								<input name="type" value="{Report::SUB_REPORT}" {if $report->type eq Report::SUB_REPORT}checked="checked"{/if} type="radio">
								<span class="text">{#p_sub_report#}</span>
							</label>
						</div>
					</div>

					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_activation#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                        {foreach $activations as $activation}
							<div class="radio">
								<label>
									<input name="activation" id="{$activation->id}" value="{$activation->id}" {if $activation->id eq $report->activation} checked="checked" {/if} type="radio">
									<span class="text">{$activation->translatedName}</span>
								</label>
							</div>
                        {/foreach}
					</div>

					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#gnr_order#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="number" name="order" value="{$report->order}" class="form-control" placeholder="{#gnr_order#}"></div>

					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>

				</div>
			</form>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}