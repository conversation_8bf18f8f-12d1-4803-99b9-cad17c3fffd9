{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=body}

<div class="row snsowraper pagetitle"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 ">{#p_sms_list#}</div></div>

<div class="row snsowraper">

  <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">

		<thead>
			<tr>
				<th width="5%">&nbsp;</th>
        <th width="15%">{#gnr_send_date#}</th>
				<th width="10%">{#p_sms_org_name#}</th>
				<th width="10%">{#p_sms_org#}</th>
        <th width="10%">{#gnr_program#}</th>
        <th width="10%">{#gnr_operation#}</th>
        <th width="10%">{#p_sms_to#}</th>
        <th width="10%">{#gnr_type#}</th>
        <th width="10%">{#gnr_phone_number#}</th>
        <th width="10%">{#gnr_status#}</th>
			</tr>
		</thead>

		<tbody>

		{$i=1}
		{foreach $sms_log as $row}
			<tr>
				<td align="center">{$i++}</td>
				<td>{$row.sms_log_created_date}</td>
				<td>{getname table=sh_org id=$row.sms_log_org_id}</td>
				<td>{getname table=sms_company id=$row.sms_log_sms_company_id}</td>
        <td>{getname table=sh_prg id=$row.sms_log_prg_id}</td>
				<td>{getname table=sh_opr id=$row.sms_log_opr_id}</td>
				<td>{getname table=sh_user id=$row.sms_log_sent_by}</td>
        <td>{$row.sms_log_type}</td>
				<td>{$row.sms_log_phone_number}</td>
        <td>{$row.sms_log_send_status}</td>
			</tr>
		{/foreach}

		</tbody>

	</table>

</div>

{/block}

