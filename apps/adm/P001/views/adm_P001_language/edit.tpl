{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_language#}</h4>
    </div>
    <div class="modal-body">
        <form  method="post" action='{url urltype="path" url_string="adm/P001/language/show/0/{$smarty.session.lang}/update/{$smarty.session.s_language_token}/{$language->id}"}'>
            <div class="row">
                <div class="col-lg-12">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="name" value="{$language->name}" required></div>

                    {gnr_activation snsolabel_classes="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel" edit_value=$language->activation snsoinput_classes="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"}

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_code#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="code" value="{$language->code}" required></div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_language_direction#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $directionsList as $direction}

                                <div class="radio">
                                    <label>
                                        <input name="direction" value="{$direction->id}" {if $direction->id eq $language->direction} checked {/if} type="radio" required="required">
                                        <span class="text">{$direction->translatedName}</span>
                                    </label>
                                </div>

                            {/foreach}
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>

                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}