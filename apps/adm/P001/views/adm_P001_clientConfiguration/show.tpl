{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=page_body}
<form  method="post" action='{url urltype="path" url_string="adm/P001/clientConfiguration/show/0/{$smarty.session.lang}/update/{$smarty.session.s_clientConfiguration_token}"}'>
    <div class="panel-group accordion" id="accordions">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordions" href="#collapseClient">
                        {#client_basic_configuration#}
                    </a>
                </h4>
            </div>
            <div id="collapseClient" class="panel-collapse collapse in">
                <div class="panel-body border-red">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_client_id#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$configurations.client_id}</div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_client_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$configurations.client_name}</div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_client_id#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$configurations.client_email}</div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_client_programs#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$configurations.client_prgs}</div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_client_system_status#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$configurations.client_system_status}</div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_client_max_user_number#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$configurations.client_max_user_number}&nbsp;{#gnr_user#}</div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_client_max_job_number#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$configurations.client_max_job_number}&nbsp;{#gnr_employee#}</div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_client_def_country#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getname table=st_country id=$configurations.client_def_country}</div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_client_def_region#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getname table=st_region id=$configurations.client_def_region}</div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_client_def_city#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{getname table=st_city id=$configurations.client_def_city}</div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_client_website_redirect_url#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">{$configurations.client_website_redirect_url}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="#accordions" href="#collapseAdditions">
                        {#client_additional_configuration#}
                    </a>
                </h4>
            </div>
            <div id="collapseAdditions" class="panel-collapse collapse">
                <div class="panel-body border-red">
                    <div class="col-lg-12">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_document_max_size_limit#}</div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" name="document[document_max_size_limit]" value="{$configurations.document_max_size_limit}" class="form-control"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="#accordions" href="#collapseEmail">
                        {#client_email_configuration#}
                    </a>
                </h4>
            </div>
            <div id="collapseEmail" class="panel-collapse collapse">
                <div class="panel-body border-red">
                    <div class="col-lg-12">

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_provider#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_provider]" value="{$configurations.email_provider}" class="form-control"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_verification_method#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_verification_method]" value="{$configurations.email_verification_method}" class="form-control"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_address#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_address]" value="{$configurations.email_address}" class="form-control"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_password#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_password]" value="{$configurations.email_password}" class="form-control"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_from_name#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_from_name]" value="{$configurations.email_from_name}" class="form-control"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_oauth_client_id#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_oauth_client_id]" value="{$configurations.email_oauth_client_id}" class="form-control"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_oauth_client_secret#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_oauth_client_secret]" value="{$configurations.email_oauth_client_secret}" class="form-control"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_oauth_redirect_uri#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_oauth_redirect_uri]" value="{$configurations.email_oauth_redirect_uri}" class="form-control"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_oauth_refresh_token#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_oauth_refresh_token]" value="{$configurations.email_oauth_refresh_token}" class="form-control"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_smtp_host#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_smtp_host]" value="{$configurations.email_smtp_host}" class="form-control"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_smtp_port#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_smtp_port]" value="{$configurations.email_smtp_port}" class="form-control"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_smtp_security#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_smtp_security]" value="{$configurations.email_smtp_security}" class="form-control"></div>

                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 ">{#p_email_smtp_security_type#}</div>
                        <div class="col-lg-8 col-md-8 col-sm12 col-xs-12 snsoinput"><input type="text" name="email[email_smtp_security_type]" value="{$configurations.email_smtp_security_type}" class="form-control"></div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 snsoinput">
        <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
    </div>
</form>
{/block}
