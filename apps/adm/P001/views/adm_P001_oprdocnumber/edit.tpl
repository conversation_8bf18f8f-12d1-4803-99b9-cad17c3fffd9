{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">
            {$operation->menuObject->programObject->translatedName}
            &nbsp;&raquo;&nbsp;
            {$operation->menuObject->translatedName}
            &nbsp;&raquo;&nbsp;
            {$operation->translatedName}
        </h4>
    </div>
    <div class="modal-body">
        <form  method="post" action='{url urltype="path" url_string="adm/P001/oprdocnumber/show/0/{$smarty.session.lang}/update/{$smarty.session.s_oprdocnumber_token}/{$operation->id}"}'>
            <div class="row snsowraper">

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_operation_document_number#}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="doc_number" value="{$operation->doc_number}" placeholder="{#p_operation_document_number#}" required></div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_operation_document_allowed_types#}</div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
                    {foreach $extensions as $extension}
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" name="allowed_extenstions[]" value="{$extension->id}" {if in_array($extension->id,explode(',',$operation->allowed_extenstions))} checked {/if}>
                                <span class="text">{$extension->translatedName}</span>
                            </label>
                        </div>
                    {/foreach}
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}