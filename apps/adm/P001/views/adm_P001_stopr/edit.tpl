{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#add_new_dropDown#}</h4>
    </div>
    <div class="modal-body">
        <form  method="post" action='{url urltype="path" url_string="adm/P001/stopr/show/0/{$smarty.session.lang}/update/{$smarty.session.s_DropDownList_token}/{$dropDown->id}"}'>
            <div class="row snsowraper">

                <div class='col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel'>{#gnr_name#}</div>
                <div class='col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput'><input type="text" class="form-control" name="translatedName" value="{$dropDown->translatedName}"></div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_code#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="code" value="{$dropDown->code}" required></div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_stopr_display_wf#}</div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" id="in_wf" name="in_wf" value="{$dropDown->in_wf}" placeholder="{#p_stopr_display_wf#}" required></div>

                <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}