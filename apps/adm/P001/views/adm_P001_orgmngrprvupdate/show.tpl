{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=body}

    <div class="row">
        <div class="col-lg-6 col-sm-6 col-xs-12">
            <div class="widget">
                <div class="widget-header separated">
                    <span class="widget-caption">{#gnr_client_general_manager#}&nbsp;&raquo;&nbsp;{$smarty.session.organization->clientManagerObject->full_name}</span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-minus blue "></i>
                        </a>
                    </div><!--Widget Buttons-->
                </div><!--Widget Header-->
                <div class="widget-body">
                    <p>
                        {#general_manager_purposes#}
                    </p>
                </div>
            </div><!--Widget-->
        </div>

        <div class="col-lg-6 col-sm-6 col-xs-12">
            <div class="widget">

                <div class="widget-header separated">
                    <span class="widget-caption">{#gnr_client_it_manager#}&nbsp;&raquo;&nbsp;{$smarty.session.organization->itManagerObject->full_name}</span>
                    <div class="widget-buttons">
                        <a href="#" data-toggle="collapse">
                            <i class="fa fa-minus blue "></i>
                        </a>
                    </div><!--Widget Buttons-->
                </div><!--Widget Header-->
                <div class="widget-body">

                    <p>
                        {#it_manager_purposes#}
                    </p>

                </div>
            </div><!--Widget-->
        </div>
    </div>

    <table cellspacing="0" id="tech-companies-1" class="table table-small-font table-bordered table-striped">
        <thead>
        <tr>
            <th width="5%">&nbsp;</th>
            <th width="95%" align="right">{#gnr_program#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $programs as $program}
            <tr>
                <td align="center">{$i++}</td>
                <td>{$program->translatedName}</td>
            </tr>
        {/foreach}
        </tbody>
    </table>
    <br>
    <form method="post"
          action="{url urltype="path" url_string="adm/P001/orgmngrprvupdate/show/0/{$smarty.session.lang}/update/{$smarty.session.s_orgmngrprvupdate_token}"}">
        <input type="submit" class="btn btn-warning sharp" value="{#gnr_update#}">
    </form>
{/block}
