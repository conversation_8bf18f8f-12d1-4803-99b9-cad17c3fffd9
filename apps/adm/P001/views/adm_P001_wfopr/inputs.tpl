{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=title}
	{getname table=sh_prg id=$smarty.session.s_wf_prg_id}
	&nbsp;&raquo;&nbsp;
	{getname table=sh_opr id=$smarty.session.s_wf_opr_id}
{/block}
{block name=body}
	{url check=0 urltype="button" opr_code='wfginput' oprvtype=1 url_string="adm/P001/wfopr/inputs/0/{$smarty.session.lang}/readallinput/{$smarty.session.s_wfopr_token}"  text_value="{#p_wfopr_read_inputs#}"}

	<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
		<thead>
		<tr>
			<th width="5%">&nbsp;</th>
			<th width="20%"><b>{#p_wfopr_input#}</b></th>
			<th width="20%"><b>{#p_wfopr_column#}</b></th>
			<th width="15%"><b>{#gnr_type#}</b></th>
			<th width="20%"><b>{#gnr_options#}</b></th>
			<th width="10%"><b>{#gnr_display#}</b></th>
			<th width="10%"><b>{#gnr_settings#}</b></th>
		</tr>
		</thead>
		<tbody>
		{$i=1}
		{foreach $inputs as $input}
			<tr>
				<td align="center">{$i++}</td>
				<td>{$input->translatedName}</td>
				<td>{$input->cname}</td>
				<td>{t v=$input->type}</td>
				<td>{getname table=st_stopr id=$input->stopr_id}</td>
				<td>{$input->include}</td>
				<td align="center">
					{url check=0 urltype="medit" opr_code='wfginput' url_string="adm/P001/wfopr/editinput/0/{$smarty.session.lang}/{$input->id}"}
				</td>
			</tr>
		{/foreach}
		</tbody>
	</table>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/wfopr/show/0/{$smarty.session.lang}"}{/block}