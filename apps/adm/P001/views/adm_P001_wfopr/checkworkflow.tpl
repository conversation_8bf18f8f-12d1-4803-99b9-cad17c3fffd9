{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">
			{getname table=sh_opr id=$oprRow.sh_opr_id}
			&nbsp;&raquo;&nbsp;
			{getname table=wf_grf id=$grfRow.wf_grf_id}
		</h4>
	</div>
	<div class="modal-body">
		<div class="row">
			<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_wfopr_table#}</div>
			<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
				{if $TableExist eq 1}
					<span><i class="fa fa-check-circle green"></i>&nbsp;{$oprRow.sh_opr_tbl_name}</span>
				{/if}
				{if $TableExist eq 0}
					<span><i class="fa fa-times-circle red"></i>&nbsp;{#gnr_unspecified#}</span>
				{/if}
			</div>
		</div>

		<div class="row">
			<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_wfopr_columns#}</div>
			<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
				{if $columnCheck.user_id_exist eq 1}
					<span><i class="fa fa-check-circle green"></i>&nbsp;user_id</span><br>
				{/if}
				{if $columnCheck.user_id_exist eq 0}
					<span><i class="fa fa-times-circle red"></i>&nbsp;user_id&nbsp;{#gnr_unspecified#}</span><br>
				{/if}

				{if $columnCheck.wf_send_status_exist eq 1}
					<span><i class="fa fa-check-circle green"></i>&nbsp;wf_send_status</span><br>
				{/if}
				{if $columnCheck.wf_send_status_exist eq 0}
					<span><i class="fa fa-times-circle red"></i>&nbsp;wf_send_status&nbsp;{#gnr_unspecified#}</span><br>
				{/if}

				{if $columnCheck.wf_grf_id_exist eq 1}
					<span><i class="fa fa-check-circle green"></i>&nbsp;wf_grf_id</span><br>
				{/if}
				{if $columnCheck.wf_grf_id_exist eq 0}
					<span><i class="fa fa-times-circle red"></i>&nbsp;wf_grf_id&nbsp;{#gnr_unspecified#}</span><br>
				{/if}

				{if $columnCheck.wf_stp_id_exist eq 1}
					<span><i class="fa fa-check-circle green"></i>&nbsp;wf_stp_id</span><br>
				{/if}
				{if $columnCheck.wf_stp_id_exist eq 0}
					<span><i class="fa fa-times-circle red"></i>&nbsp;wf_stp_id&nbsp;{#gnr_unspecified#}</span><br>
				{/if}
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}