{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=body}
<div class="btn-group">
	<a class="btn btn-success " href="javascript:void(0);">{getname table=sh_prg id=$smarty.session.s_wf_prg_id}</a>
	<a class="btn btn-success  dropdown-toggle" data-toggle="dropdown" href="javascript:void(0);" aria-expanded="false"><i class="fa fa-angle-down"></i></a>
	<ul class="dropdown-menu dropdown-success">
		{foreach $programs as $program}
			<li>
				<a href="{url urltype="path" url_string="adm/P001/wfopr/show/0/{$smarty.session.lang}/save_session/{$program->id}"}">{$program->translatedName}</a>
			</li>
		{/foreach}
	</ul>
</div>
<div class="horizontal-space"></div>
	{if !empty($smarty.session.s_wf_prg_id)}
		<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
			<thead>
			<tr>
				<th width="5%">&nbsp;</th>
				<th width="15%">{#gnr_operation#}</th>
				<th width="15%">{#gnr_code#}</th>
				<th width="15%">{#gnr_table#}</th>
				<th width="15%">{#gnr_menu#}</th>
				<th width="15%">{#p_wfopr_inputs#}</th>
				<th width="5%">{#gnr_settings#}</th>
			</tr>
			</thead>
			{$i=1}
			{foreach $operations as $operation}
				<tr>
					<td>{$i++}</td>
					<td>{$operation->translatedName}</td>
					<td>[ {$operation->code} ]</td>
					<td>[ {$operation->tbl_name} ]</td>
					<td>{$operation->menuObjct->translatedName}</td>
					<td style="text-align: center;">{if $operation->tbl_name eq ''}{#gnr_unspecified#}{/if} {if $operation->tbl_name neq ''}{url check=0 urltype="button" url_string="adm/P001/wfopr/inputs/0/{$smarty.session.lang}/save_session/{$operation->id}"  text_value="{#p_wfopr_columns#}"} {/if}</td>
					<td style="text-align: center;">{url check=0 urltype="medit" opr_code='opr' url_string="adm/P001/wfopr/edit/0/{$smarty.session.lang}/{$operation->id}"}</td>
				</tr>
			{/foreach}
			</tbody>
		</table>
	{else}
		<div class="widget flat">
			<div class="widget-body">
				<span class="small darkorange">{#p_wfopr_choose#}</span>
			</div><!--Widget Body-->
		</div>
	{/if}

{/block}


