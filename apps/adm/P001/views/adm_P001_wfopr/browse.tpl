{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=title}
	{getname table=sh_prg id=$smarty.session.s_wf_prg_id}
	&nbsp;&raquo;&nbsp;
	{getname table=sh_opr id=$smarty.session.s_wf_opr_id}
{/block}
{block name=body}
	<div class="row snsowraper">
		<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
			<thead>
			<tr>
				<th width="5%">&nbsp;</th>
				<th width="15%"><b>{#gnr_schematic#}</b></th>
				<th width="62%"><b>{#gnr_desciption#}</b></th>
				<th width="6%"><b>{#gnr_status#}</b></th>
				<th width="6%"><b>{#gnr_steps#}</b></th>
				<th width="6%"><b>{#gnr_settings#}</b></th>
			</tr>
			</thead>
			<tbody>
			{$i=1}
			{foreach $wf_grf_list as $row}
				<tr>
					<td align="center">{$i++}</td>
					<td>
						{url check=0 urltype="mbutton" url_string="adm/P001/wfopr/checkworkflow/0/{$smarty.session.lang}/{$row.wf_grf_id}"  text_value="<i class='fa fa-cog'></i>" modal=modal}
						{$row.wf_grf_name}</td>
					<td>{$row.wf_grf_description}</td>
					<td>{getname table=st_setting id=$row.wf_grf_status}</td>
					<td>{url check=0 urltype="button" opr_code='wfsteps' oprvtype=1 url_string="adm/P001/wfsteps/show/0/{$smarty.session.lang}/save_session/{$row.wf_grf_id}"  text_value="{#gnr_steps#}"}</td>
					<td>{url check=0 urltype="button" opr_code='wfsteps' oprvtype=1 url_string="adm/P001/wfsteps/browse/0/{$smarty.session.lang}/save_session/{$row.wf_grf_id}"  text_value="{#p_wfopr_build#}"}</td>
				</tr>
			{/foreach}
			</tbody>
		</table>
	</div>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/wfopr/show/0/{$smarty.session.lang}"}{/block}