{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#p_wfopr_edit_input#}</h4>
	</div>
	<div class="modal-body">
		<form  method="post" action='{url urltype="path" url_string="adm/P001/wfopr/inputs/0/{$smarty.session.lang}/update/{$smarty.session.s_wfopr_token}/{$input->id}"}'>

			<div class="row snsowraper">
				<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_wfopr_name#}</div>
				<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$input->cname}</div>

				<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_wfopr_title#}</div>
				<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" id="name" name="translatedName" value="{$input->translatedName}" placeholder="إسم المدخل" required></div>

				<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_wfopr_type#}</div>
				<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
					<div class="control-group">
						{foreach $inputList as $inputRow}
							<div class="radio">
								<label>
									<input type="radio" name="type" id="{$inputRow->id}" value="{$inputRow->id}" {if $inputRow->id eq $input->type} checked {/if}required>
									<span class="text">{$inputRow->translatedName}</span>
								</label>
							</div>

						{/foreach}
					</div>


				</div>

				<div id="extra1">
					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_menu#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<select name="stopr_id">
							<option value="{$input->stopr_id}" selected>{getname table=st_stopr id=$input->stopr_id}</option>
							<option value="0">{#gnr_unspecified#}</option>
							{foreach $stoprList as $dropDown}
								<option value="{$dropDown->id}">{$dropDown->translatedName}</option>
							{/foreach}
						</select>
					</div>
				</div>

				<div id="extra2">
					<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_wfopr_table_name#}</div>
					<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
						<input type="text" name="table_name" value="{$input->table_name}">
					</div>
				</div>

				<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_wfopr_allowed#}</div>
				<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" name="include" value="{$input->include}" placeholder="{#p_wfopr_allowed#}" required></div>

				<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
				<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
			</div>

		</form>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}
{block name=header}
	<script>
		$(document).ready(function(){

			$("#extra1").css("display","none");
			$("#extra2").css("display","none");

			$("#74").click(function(){
				if ($("#74").is(":checked"))
				{
					$("#extra1").show("fast");
					$("#extra2").hide("fast");
				}
			});

			$("#73").click(function(){
				if ($("#73").is(":checked"))
				{
					$("#extra1").hide("fast");
					$("#extra2").hide("fast");
				}
			});

			$("#72").click(function(){
				if ($("#72").is(":checked"))
				{
					$("#extra1").hide("fast");
					$("#extra2").hide("fast");
				}
			});

			$("#75").click(function(){
				if ($("#75").is(":checked"))
				{
					$("#extra1").hide("fast");
					$("#extra2").hide("fast");
				}
			});

			$("#76").click(function(){
				if ($("#76").is(":checked"))
				{
					$("#extra1").hide("fast");
					$("#extra2").hide("fast");
				}
			});

			$("#77").click(function(){
				if ($("#77").is(":checked"))
				{
					$("#extra1").hide("fast");
					$("#extra2").hide("fast");
				}
			});

			$("#623").click(function(){
				if ($("#623").is(":checked"))
				{
					$("#extra1").hide("fast");
					$("#extra2").show("fast");
				}
			});

			{$check_two}

		});
	</script>
{/block}