{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=body}
<div class="row snsowraper">
	<div class="table-responsive" data-pattern="priority-columns">
		<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
			<thead>
			<tr>
			<th width="5%">{url check=0 urltype="add" opr_code='prevachoice' url_string="adm/P001/prevachoice/add/{$smarty.session.lang}"}</th>
			<th width="35%"><b> {getname table=pm_preva id=$smarty.session.s_evalist_id} </b></th>
			<th width="10%">{#gnr_color#}</th>
			<th width="15%"><b>{#gnr_settings#}</b></th>
			</tr>
			</thead>
			<tbody>
			{$i=1}
			{foreach $priorities as $priority}
			<tr>
			<td align="center">{$i++}</td>
			<td>{$priority->name}</td>
			<td  style="background-color: {$priority->color};">{$priority->color}</td>
			<td nowrap>
			{url check=0 urltype="edit" opr_code='prevachoice' url_string="adm/P001/prevachoice/edit/{$smarty.session.lang}/{$priority->id}"}
			{url check=0 urltype="delete" opr_code='prevachoice' url_string="adm/P001/prevachoice/confirm/{$smarty.session.lang}/{$priority->id}"}
			</td>
			</tr>
			{/foreach}
			</tbody>
		</table>
	</div>
</div>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/pmevalists/show/0/{$smarty.session.lang}"}{/block}