{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=body}
	<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
		<thead>
		<tr>
			<th width="5%">{url check=0 urltype="madd" opr_code='bnd' url_string="adm/P001/bnd/add/0/{$smarty.session.lang}/{$smarty.session.s_bnd_token}"}</th>
			<th width="15%"><b> {#p_bnd_name#} </b></th>
			<th width="15%"><b> {#p_bnd_code#} </b></th>
			<th width="10%"><b> {#gnr_settings#} </b></th>
		</tr>
		</thead>
		<tbody>
        {$i=1}
        {foreach $bundles as $bundle}
			<tr>
				<td align="center">{$i++}</td>
				<td align="right" class="tdsh_bnd_name" id="delname">{$bundle->translatedName}</td>
				<td align="center" class="tdsh_bnd_code">{$bundle->code}</td>
				<td nowrap align="center">
                    {url check=0 urltype="medit" opr_code='bnd' url_string="adm/P001/bnd/edit/0/{$smarty.session.lang}/{$bundle->id}"}
					&nbsp;&nbsp;&nbsp;
					{if $bundle->programsNumber eq 0}
                        {url check=0 urltype="mdelete" opr_code='bnd' url_string="adm/P001/bnd/confirm/0/{$smarty.session.lang}/{$bundle->id}"}
                    {/if}
					&nbsp;&nbsp;&nbsp;
					[{$bundle->programsNumber}]
				</td>
			</tr>
        {/foreach}
		</tbody>
	</table>
{/block}
