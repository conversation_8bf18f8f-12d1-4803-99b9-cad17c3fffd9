{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#p_create_entry#}</h4>
	</div>
	<div class="modal-body">
		<div class="snsowraper">
			<form action="{url urltype=path url_string="adm/P001/FinanceEntriesCheck/show/0/{$smarty.session.lang}/createEntry/{$smarty.session.s_operation_token}/{$entry}"}" method="post">
				<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_account#}</div>
				<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
					<select name="year_id">
						<option value="">{#gnr_choose_from_menu#}</option>
                        {foreach $accounts as $account}
							<option value="{$account->fin_acc_id}">
                                {$account->fin_acc_code} - {$account->fin_acc_name}
							</option>
                        {/foreach}
					</select>
				</div>
				<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_date#}</div>
				<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
					{getdate type=add col=date}
				</div>

				<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
				<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
					<button type="submit" class="btn btn-success sharp">{#p_create_entry#}</button>
				</div>

			</form>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}