{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=body}
	<form method="post"
		  action='{url urltype="path" url_string="adm/P001/FinanceEntriesCheck/show/0/{$smarty.session.lang}/save_session"}'>

		<div class="row snsowraper">
			<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_fiscal_year#}</div>
			<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
				<select name="year_id">
					<option value="">{#gnr_choose_from_menu#}</option>
					{foreach $fiscalYears as $year}
						<option value="{$year->fin_year_id}" {if $year->fin_year_id eq $smarty.session.s_year_id}selected{/if}>
							{$year->fin_year_name} : {getdate type=show row=$year col=fin_year_start_date} &raquo; {getdate type=show row=$year col=fin_year_end_date}
						</option>
					{/foreach}
				</select>
			</div>

			<div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
			<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
				<button type="submit" class="btn btn-default sharp">{#gnr_view#}</button>
			</div>
		</div>
	</form>
	{if $smarty.session.s_year_id}
		<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
			<thead>
			<tr>
				<th>{#p_entry_number#}</th>
				<th>{#p_action#}</th>
			</tr>
			</thead>
			<tbody>
            {$i=1}
            {foreach $missingEntries as $entry}
				<tr>
					<td class="text-center">{$entry}</td>
					<td class="text-center">
                        {url urltype=mbutton url_string="adm/P001/FinanceEntriesCheck/confirm/0/{$smarty.session.lang}/{$entry}" text_value="{#p_create_entry#}" style="btn btn-success sharp"}
					</td>
				</tr>
            {/foreach}
			</tbody>
		</table>
	{/if}
{/block}
