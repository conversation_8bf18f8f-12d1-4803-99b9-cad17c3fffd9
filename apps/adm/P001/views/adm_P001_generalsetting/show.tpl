{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=body}
    <form  method="post" action='{url urltype="path" url_string="adm/P001/generalsetting/show/0/{$smarty.session.lang}/update/{$smarty.session.s_generalsetting_token}"}'>
        <div class="row">

            <div class="col-lg-12">

                {foreach $MediaCenterTypes as $types}

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" class="snso-checkbox"
                                           name="st_orgsetting_ids[]" value="{$types->id}"
                                            {if in_array($types->id,explode(',',$OrganizationConfiguration->ids)) } checked="checked"{/if} >
                                    <span class="text">{$types->translatedName}</span>
                                </label>
                                <br>
                            </div>
                        </div>
                    </div>
                {/foreach}
            </div>

            <div class="col-lg-12"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>

        </div>

    </form>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/generalsetting/show/0/{$smarty.session.lang}"}{/block}