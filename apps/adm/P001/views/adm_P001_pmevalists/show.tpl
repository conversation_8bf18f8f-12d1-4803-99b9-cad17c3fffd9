{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=body}
<div class="row snsowraper">
	<div class="table-responsive" data-pattern="priority-columns">
		<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
			<thead>
			<tr>
			<th width="5%">{url check=0 urltype="add" opr_code='pmevalists' url_string="adm/P001/pmevalists/add/0/{$smarty.session.lang}"}</th>
			<th width="45%"><b>{#gnr_menu#}</b></th>
			<th width="20%"><b>{#gnr_options#}</b></th>
			<th width="20%"><b>{#gnr_settings#}</b></th>
			</tr>
			</thead>
			<tbody>
			{$i=1}
			{foreach $pm_preva_list as $row}
			<tr id="tr_{$row.sh_opr_id}">
			<td align="center">{$i++}</td>
			<td>{$row.pm_preva_name}</td>
			<td nowrap>
			{url check=0 urltype='button' opr_code='prevachoice' oprvtype=1 url_string="adm/P001/prevachoice/show/0/{$smarty.session.lang}/save_session/{$row.pm_preva_id}"  text_value="{#gnr_options#}"}
			</td>
			<td nowrap>
			{url check=0 urltype="edit" opr_code='pmevalists' url_string="adm/P001/pmevalists/edit/0/{$smarty.session.lang}/{$row.pm_preva_id}"}
			{url check=0 urltype="delete" opr_code='pmevalists' url_string="adm/P001/pmevalists/confirm/0/{$smarty.session.lang}/{$row.pm_preva_id}"}
			</td>
			</tr>
			{/foreach}
			</tbody>
		</table>
	</div>
</div>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/prg/show/0/{$smarty.session.lang}"}{/block}