{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}
    <div class="row">
        <div class="col-lg-12">
            {url urltype="mbutton" check=0 url_string="adm/P001/versionsControl/add/0/{$smarty.session.lang}" text_value="{#p_add_new_version#}" style="btn btn-default shiny"}
        </div>
    </div>
    <br>
    <table class="table table-striped table-bordered table-hover no-footer">
        <thead>
        <tr>
            <th width="5%"></th>
            <th width="20%">{#p_version_code#}</th>
            <th width="20%">{#p_version_date#}</th>
            <th width="20%">{#p_version_details#}</th>
            <th width="20%">{#p_version_report#}</th>
            <th width="15%">{#gnr_settings#}</th>
        </tr>
        </thead>
        {$i=1}
        <tbody>
        {foreach $versions as $version}
            <tr>
                <td class="text-center">{$i++}</td>
                <td class="text-center">{$version->code}</td>
                <td class="text-center">{getdate table=sh_version col=date type=show row=$version}</td>
                <td class="text-center">
                    {url urltype="button" check=0 url_string="adm/P001/versionsControl/details/0/{$smarty.session.lang}/versionData/{$version->id}" text_value="{#p_version_details#}" style="btn btn-default shiny"}
                </td>
                <td class="text-center">
                    <a {if $version->hasDetails} href="index.php?adm/P001/versionsControl/report/0/{$smarty.session.lang}/{$version->id}" {/if} class="btn btn-default shiny" {if !$version->hasDetails} disabled="disabled" {/if}>{#p_version_report#}</a>
                </td>
                <td class="text-center">
                    {url check=0 urltype="medit" url_string="adm/P001/versionsControl/edit/0/{$smarty.session.lang}/{$version->id}"}
                    {if !$version->hasDetails}
                        {url check=0 urltype="mdelete" url_string="adm/P001/versionsControl/confirm/0/{$smarty.session.lang}/{$version->id}"}
                    {/if}
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
{/block}