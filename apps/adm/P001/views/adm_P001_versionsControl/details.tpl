{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-list-alt blue"></i>
        {#p_version_code#}&nbsp;:&nbsp;{$version->code}
    </h5>
    <div class="widget collapsed">
        <div class="widget-header bg-blue">
            <i class="widget-icon fa fa-arrow-left"></i>
            <span class="widget-caption">{#p_version_data#}</span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                    <i class="fa fa-plus"></i>
                </a>
            </div><!--Widget Buttons-->
        </div><!--Widget Header-->
        <div class="widget-body">

            <form action="{url urltype=path url_string="adm/P001/versionsControl/details/0/{$smarty.session.lang}/update/{$smarty.session.s_versions_details_token}/{$version->id}"}" method="post">

                <div class="row">
                    <div class="col-lg-12">

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_code#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <input type="text" name="code" class="form-control" required value="{$version->code}">
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_date#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getdate table=sh_version col=date type=edit row=$version}</div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_note#}</div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                <textarea name="note" class="form-control" id="" cols="30"
                                          rows="5">{$version->note}</textarea>
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                            <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                        </div>
                    </div>
                </div>

            </form>
        </div><!--Widget Body-->
    </div>

    <div class="horizontal-space"></div>

    <div class="well" style="margin-bottom: 0">
        <div class="row">
            <div class="col-lg-6"><h4><i class="typcn typcn-th-menu blueberry"></i>&nbsp;{#p_version_details#}</h4></div>
        </div>

        <div class="row">
            <br>
            <div class="col-lg-12">
                <form method="post"
                      action='{url urltype="path" url_string="adm/P001/versionsControl/details/0/{$smarty.session.lang}/insert/{$smarty.session.s_versions_details_token}/{$version->id}"}'>
                    <div class="row snsowraper">
                        <div class="col-lg-12">

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_title#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <input type="text" name="title" class="form-control" required>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_feature#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <textarea name="details" class="form-control" required cols="30" rows="3"></textarea>

                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_path#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <div class="control-group">
                                    <input type="text" class="form-control" name="path">
                                </div>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_client#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <div class="control-group">
                                    <input type="text" class="form-control" name="client">
                                </div>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_prg#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <select name="prg_id" required>
                                    <option value=""></option>
                                    {foreach $prgs as $prg}
                                        <option value="{$prg->sh_prg_id}">{getname table=sh_prg id=$prg->sh_prg_id}</option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_executor#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <select name="executor">
                                    <option value=""></option>
                                    {foreach $team as $member}
                                        <option value="{$member->id}">{$member->translatedName}</option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_tester#}</div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <select name="tester">
                                    <option value=""></option>
                                    {foreach $team as $member}
                                        <option value="{$member->id}">{$member->translatedName}</option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                            <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <button type="submit" class="btn btn-success sharp">{#gnr_add#}</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="horizontal-space"></div>

    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-arrow-left blue"></i>
        {#p_version_summary#}
    </h5>

    <table class="table table-striped table-bordered table-hover no-footer">
        <thead>
        <tr>
            <th class="text-center" width="10%"></th>
            <th class="text-center" width="60%">{#p_version_details_prg#}</th>
            <th class="text-center" width="20%">{#p_version_details_feature#}</th>
            <th class="text-center" width="10%">{#gnr_details#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $summary as $data}
            <tr>
                <td class="text-center">{$i++}</td>
                <td class="text-center">{getname table=sh_prg id=$data->sh_version_details_prg_id}</td>
                <td class="text-center">{$data->count}</td>
                <td class="text-center"><a id="scroll" href="#{$data->sh_version_details_prg_id}" class="btn btn-default shiny">{#gnr_view#}</a></td>
                {assign var="count_sum" value=($count_sum+$data->count)}
            </tr>
        {/foreach}
        </tbody>
        <tfoot>
        <tr class="bg-gray">
            <td class="text-center">{#gnr_total#}</td>
            <td></td>
            <td class="text-center">{$count_sum}</td>
            <td></td>
        </tr>
        </tfoot>
    </table>

    <div class="horizontal-space"></div>

    {foreach $version->programs as $key => $details}
        <div class="mt-2" id="{$key}">
            <h5 class="row-title before-blue">
                <i class="glyphicon glyphicon-arrow-left blue"></i>
                {$programs.$key}
            </h5>
        </div>
        <table class="table table-striped table-bordered table-hover no-footer">
            <thead>
            <tr>
                <th class="text-center" width="5%">&nbsp;</th>
                <th class="text-right" width="60%">
                    {#p_version_details_title#}
                    &nbsp;/&nbsp;
                    {#p_version_details#}
                </th>
                <th class="text-center" width="25%">
                    {#p_version_details_executor#}
                    &nbsp;/&nbsp;
                    {#p_version_details_tester#}

                </th>
                <th class="text-center" width="10%">{#gnr_setting#}</th>
            </tr>
            </thead>
            <tbody>
            {$i=1}
            {foreach $details as $detail}
                <tr>
                    <td class="text-center">{$i++}</td>
                    <td class="text-right">
                        <span style="color: mediumseagreen">{$detail->sh_version_details_title}</span><br>
                        {($detail->sh_version_details_details|urldecode)|nl2br}
                        {if $detail->sh_version_details_path}<br><span style="font-size: small; color: lightsteelblue">{$detail->sh_version_details_path}</span>{/if}
                    </td>
                    <td class="text-center">
                        {t v=$detail->sh_version_details_executor}<br>
                        {t v=$detail->sh_version_details_tester}
                    </td>
                    <td class="text-center">
                        {url check=0 urltype="medit" url_string="adm/P001/versionsControl/detailsEdit/0/{$smarty.session.lang}/{$detail->sh_version_details_id}"}
                        {url check=0 urltype="mdelete" url_string="adm/P001/versionsControl/detailsConfirm/0/{$smarty.session.lang}/{$detail->sh_version_details_id}"}
                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>
    {/foreach}

    <script>
        $(document).ready(function(){
            // Add smooth scrolling to all links
            $("a").on('click', function(event) {

                // Make sure this.hash has a value before overriding default behavior
                if (this.hash !== "") {
                    // Prevent default anchor click behavior
                    event.preventDefault();

                    // Store hash
                    var hash = this.hash;

                    // Using jQuery's animate() method to add smooth page scroll
                    // The optional number (800) specifies the number of milliseconds it takes to scroll to the specified area
                    $('html, body').animate({
                        scrollTop: $(hash).offset().top
                    }, 800, function(){

                        // Add hash (#) to URL when done scrolling (default click behavior)
                        window.location.hash = hash;
                    });
                } // End if
            });
        });
    </script>


{/block}
{block name=back}{url urltype="path" url_string="adm/P001/versionsControl/show/0/{$smarty.session.lang}"}{/block}