{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}prnt.tpl"}
{block name=body}

    <div class="row">
        <h4 class="text-center">
            {#p_version_report#}&nbsp;:&nbsp;{$version->code}
        </h4>
    </div>
    <div class="row">
        <div class="col-lg-12">

            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_version_code#}</div>
            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">{$version->code}</div>

            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_version_date#}</div>
            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">{getdate table=sh_version col=date type=show row=$version}</div>

        </div>
    </div>

    <div class="horizontal-space"></div>

    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-arrow-left blue"></i>
        {#p_version_summary#}
    </h5>

    <table class="table table-striped table-bordered table-hover no-footer">
        <thead>
        <tr>
            <th class="text-center" width="10%"></th>
            <th class="text-center" width="70%">{#p_version_details_prg#}</th>
            <th class="text-center" width="20%">{#p_version_details_feature#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $summary as $data}
            <tr>
                <td class="text-center">{$i++}</td>
                <td class="text-center">{getname table=sh_prg id=$data->sh_version_details_prg_id}</td>
                <td class="text-center">{$data->count}</td>
            </tr>
        {/foreach}
        </tbody>
    </table>

    <div class="horizontal-space"></div>

    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-arrow-left blue"></i>
        {#p_version_execution_summary#}
    </h5>

    <table class="table table-striped table-bordered table-hover no-footer">
        <thead>
        <tr>
            <th class="text-center" width="10%"></th>
            <th class="text-center" width="60%">{#p_version_details_executor#}</th>
            <th class="text-center" width="20%">{#p_version_details_tasks_count#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $executionSummary as $data}
            <tr>
                <td class="text-center">{$i++}</td>
                <td class="text-center">{t v=$data->sh_version_details_executor}</td>
                <td class="text-center">{$data->count}</td>
            </tr>
        {/foreach}
        </tbody>
    </table>

    <div class="horizontal-space"></div>

    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-arrow-left blue"></i>
        {#p_version_testing_summary#}
    </h5>

    <table class="table table-striped table-bordered table-hover no-footer">
        <thead>
        <tr>
            <th class="text-center" width="10%"></th>
            <th class="text-center" width="60%">{#p_version_details_tester#}</th>
            <th class="text-center" width="20%">{#p_version_details_tasks_count#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $testingSummary as $data}
            <tr>
                <td class="text-center">{$i++}</td>
                <td class="text-center">{t v=$data->sh_version_details_tester}</td>
                <td class="text-center">{$data->count}</td>
            </tr>
        {/foreach}
        </tbody>
    </table>


{/block}
{block name=back}{url urltype="path" url_string="bsc/P052/systemVersions/show/0/{$smarty.session.lang}"}{/block}