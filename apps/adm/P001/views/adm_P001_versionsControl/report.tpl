{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-list-alt blue"></i>
        {#p_version_report#}&nbsp;:&nbsp;{$version->code}
    </h5>
    <div class="widget">
        <div class="widget-header bg-blue">
            <i class="widget-icon fa fa-arrow-left"></i>
            <span class="widget-caption">{#p_version_data#}</span>
            <div class="widget-buttons">
                <a href="#" data-toggle="collapse">
                    <i class="fa fa-minus"></i>
                </a>
            </div><!--Widget Buttons-->
        </div><!--Widget Header-->
        <div class="widget-body">

            <div class="row">
                <div class="col-lg-12">

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_version_code#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">{$version->code}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_version_date#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">{getdate table=sh_version col=date type=show row=$version}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-3 snsolabel">{#p_version_note#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-8 snsoinput">{$version->note}</div>

                </div>
            </div>

        </div><!--Widget Body-->
    </div>

    <div class="horizontal-space"></div>

    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-arrow-left blue"></i>
        {#p_version_summary#}
    </h5>

    {url check=0 urltype="print" url_string="adm/P001/versionsControl/print/0/{$smarty.session.lang}/{$version->id}" style="btn btn-default sharp shiny mr-1" text_value="<i class='fa fa-print'></i>&nbsp;{#gnr_print#}"}

    <table class="table table-striped table-bordered table-hover no-footer">
        <thead>
        <tr>
            <th class="text-center" width="10%"></th>
            <th class="text-center" width="20%">{#p_version_details_prg#}</th>
            <th class="text-center" width="70%">{#p_version_details_feature#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $summary as $data}
            <tr>
                <td class="text-center">{$i++}</td>
                <td class="text-center">{getname table=sh_prg id=$data->sh_version_details_prg_id}</td>
                <td class="text-right">{$data->count}</td>
                {assign var="count_sum" value=($count_sum+$data->count)}
            </tr>
        {/foreach}
        </tbody>
        <tfoot>
        <tr class="bg-gray">
            <td></td>
            <td class="text-center">{#gnr_total#}</td>
            <td class="text-right">{$count_sum}</td>
            <td></td>
        </tr>
        </tfoot>
    </table>

    <div class="horizontal-space"></div>

    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-arrow-left blue"></i>
        {#p_version_execution_summary#}
    </h5>

    <table class="table table-striped table-bordered table-hover no-footer">
        <thead>
        <tr>
            <th class="text-center" width="10%"></th>
            <th class="text-center" width="20%">{#gnr_employee#}</th>
            <th class="text-right" width="70%">{#p_version_details_tasks_count#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $executionSummary as $data}
            <tr>
                <td class="text-center">{$i++}</td>
                <td class="text-center">{t v=$data->sh_version_details_executor}</td>
                <td class="text-right">{$data->count}</td>
            </tr>
        {/foreach}
        </tbody>
    </table>

    <div class="horizontal-space"></div>

    <h5 class="row-title before-blue">
        <i class="glyphicon glyphicon-arrow-left blue"></i>
        {#p_version_testing_summary#}
    </h5>

    <table class="table table-striped table-bordered table-hover no-footer">
        <thead>
        <tr>
            <th class="text-center" width="10%"></th>
            <th class="text-center" width="20%">{#gnr_employee#}</th>
            <th class="text-center" width="70%">{#p_version_details_tasks_count#}</th>
        </tr>
        </thead>
        <tbody>
        {$i=1}
        {foreach $testingSummary as $data}
            <tr>
                <td class="text-center">{$i++}</td>
                <td class="text-center">{t v=$data->sh_version_details_tester}</td>
                <td class="text-right">{$data->count}</td>
            </tr>
        {/foreach}
        </tbody>
    </table>

{/block}
{block name=back}{url urltype="path" url_string="adm/P001/versionsControl/show/0/{$smarty.session.lang}"}{/block}