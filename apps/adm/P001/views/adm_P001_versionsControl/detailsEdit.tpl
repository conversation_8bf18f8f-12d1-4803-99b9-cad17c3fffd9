{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_version_details#}</h4>
    </div>
    <div class="modal-body">
        <form method="post"
              action='{url urltype="path" url_string="adm/P001/versionsControl/details/0/{$smarty.session.lang}/updateDetails/{$smarty.session.s_versions_details_token}/{$detail->id}"}'>
            <div class="row snsowraper">
                <div class="col-lg-12">

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_title#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <input type="text" class="form-control" name="title" required value="{$detail->title}">
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <textarea name="details" class="form-control" required cols="30" rows="5">{($detail->details|urldecode)}</textarea>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_path#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <input type="text" class="form-control" name="path" value="{$detail->path}">
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_client#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            <input type="text" class="form-control" name="client" value="{$detail->client}">
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_prg#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="prg_id" required>
                            <option value=""></option>
                            {foreach $prgs as $prg}
                                <option value="{$prg->sh_prg_id}" {if $prg->sh_prg_id eq $detail->prg_id} selected {/if}>{getname table=sh_prg id=$prg->sh_prg_id}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_executor#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="executor">
                            <option value=""></option>
                            {foreach $team as $member}
                                <option value="{$member->id}" {if $member->id eq $detail->executor} selected {/if}>{$member->translatedName}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_version_details_tester#}</div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <select name="tester">
                            <option value=""></option>
                            {foreach $team as $member}
                                <option value="{$member->id}" {if $member->id eq $detail->tester} selected {/if}>{$member->translatedName}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
