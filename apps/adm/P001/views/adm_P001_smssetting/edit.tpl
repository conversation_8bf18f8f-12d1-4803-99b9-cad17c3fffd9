{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=page_header}
<script type="text/javascript">
  $(document).ready(function() {

  	$("#sms_company_country").change(function() {
  		$.get('/framework/core/functions/ajax/country_change.php?country_id=' + $(this).val(), function(data) {
  			$("#sms_company_region").html(data);
  		});

      // added here to also set correct city along with region select above
      $.get('/framework/core/functions/ajax/region_change.php?region_id=' + $(this).val(), function(data) {
  		    $("#sms_company_city").html(data);
  		});
  	});

  	$("#sms_company_region").change(function() {
  		$.get('/framework/core/functions/ajax/region_change.php?region_id=' + $(this).val(), function(data) {
  		    $("#sms_company_city").html(data);
  		});
  	});

    $("#sms_company_phone_number").keypress(function (e) {
     //if the letter is not digit then display error and don't type anything
     if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which> 57) && e.which != 13) {
        //display error message
        $("#ph_no_err_msg").html("{#p_smssettings_error#}").show().fadeOut(2000);
      }
    });

  });
</script>
{/block}
{block name=body}

<form  method="post" action='{url urltype="path" url_string="adm/P001/smssetting/show/0/{$smarty.session.lang}/update/{$sms_company_data.sms_company_id}"}' enctype='multipart/form-data'>

  <div class="row snsowraper pagetitle"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 ">{#p_smssettings_edit#}</div></div>
  <div class="row snsowraper">

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
  	<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" id="sms_company_name" name="sms_company_name" placeholder="{#gnr_name#}" value="{$sms_company_data.sms_company_name}" required></div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_country#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$country_selector}</div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_region#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$region_selector}</div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_city#}</div>
  	<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$city_selector}</div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_smsettings_coverage#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$coverage_selector}</div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_smssettings_address#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" id="sms_company_address" name="sms_company_address" placeholder="{#p_smssettings_address#}" value="{$sms_company_data.sms_company_address}" required></div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_smssettings_phones#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control" id="sms_company_phone_number" name="sms_company_phone_number" placeholder="{#p_smssettings_phones#}" required>{$sms_company_data.sms_company_phone_number}</textarea><div id="ph_no_err_msg"></div></div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_website#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" id="sms_company_website" name="sms_company_website" placeholder="{#gnr_website#}"value="{$sms_company_data.sms_company_website}" required></div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_email#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" id="sms_company_email" name="sms_company_email" placeholder="{#gnr_email#}"value="{$sms_company_data.sms_company_email}"  required></div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_smssettings_settings#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$setting_elements_selector}</div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning" >{#gnr_update#}</button></div>
  </div>

</form>

{/block}
{block name=back}{url urltype="path" url_string="adm/P001/smssetting/show/0/{$smarty.session.lang}"}{/block}
