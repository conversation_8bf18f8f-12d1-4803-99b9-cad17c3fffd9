{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=body}

{if isset($error_message)}
	<div class="row snsowraper pagetitle"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 ">{$error_message}</div></div>
{/if}

<div class="row snsowraper">

	<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">

		<thead>
			<tr>
				<th width="5%">{url check=0 urltype="add" url_string="adm/P001/smssetting/add/0/{$smarty.session.lang}"}</th>
				<th width="22%">{#gnr_name#}</th>
				<th width="21%">{#gnr_country#}</th>
        <th width="7%">{#p_smssettings_coverage#}</th>
        <th width="22%">{#p_smssettings_contact_data#}</th>
        <th width="12%">{#gnr_website#}</th>
				<th width="11%">{#gnr_settings#}</th>
			</tr>
		</thead>

		<tbody>

		{$i=1}
		{foreach $sms_company_list as $row}
			<tr>
				<td align="center">{$i++}</td>
				<td>{$row.sms_company_name}</td>
				<td>{$row.sms_company_country_name}</td>
				<td>{$row.sms_company_coverage}</td>
				<td>{$row.sms_company_address}</td>
				<td>{url urltype="link" url_string="{$row.sms_company_website}"}</td>
				<td align="center" nowrap>
					{url check=0 urltype="edit" url_string="adm/P001/smssetting/edit/0/{$smarty.session.lang}/{$row.sms_company_id}"}
					&nbsp;&nbsp;
					{url check=0 urltype="delete" url_string="adm/P001/smssetting/confirm/0/{$smarty.session.lang}/{$row.sms_company_id}"}
				</td>
			</tr>
		{/foreach}

		</tbody>

	</table>

</div>

{/block}

