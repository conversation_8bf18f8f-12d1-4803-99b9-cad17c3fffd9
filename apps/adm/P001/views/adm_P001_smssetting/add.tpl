{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=page_header}
{literal}
    <script type="text/javascript">
        $(document).ready(function() {
            var countryElem = $("#sms_company_country.selectized").selectize({});
            var regionElem = $('#sms_company_region.selectized').selectize({});
            var townElem   = $('#sms_company_city.selectized').selectize({});
            var regionElemInst = $('#sms_company_region.selectized')[0].selectize;
            var townElemInst = $('#sms_company_city.selectized')[0].selectize;
            countryElem.on('change', function()
            {
                $.get('/framework/core/functions/ajax/country_json.php?country_selected_id=' + countryElem[0].value, function(data)
                {
                    var options = [];
                    regionElemInst.clear();
                    console.log('after');
                    $.each(data,  function (i, item) {
                        regionElemInst.addOption({
                                 'value': item.id,
                                 'text': item.name
                             });
                    });
                });
            });

            regionElem.on('change', function()
            {
                $.get('/framework/core/functions/ajax/region_json.php?region_selected_id=' + regionElem[0].value, function(data) {
                    townElemInst.clear();
                    $.each(data,  function (i, item) {
                        townElemInst.addOption({
                            'value': item.id,
                            'text': item.name
                        });
                    });
                });
            });
        });
    </script>
{/literal}
{/block}
{block name=body}

<form  method="post" action='{url urltype="path" url_string="adm/P001/smssetting/show/0/{$smarty.session.lang}/insert/{$smarty.session.s_smssetting_rand_num}"}' enctype='multipart/form-data'>

  <div class="row snsowraper pagetitle"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 ">{#p_smssettings_add#}</div></div>
  <div class="row snsowraper">

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_name#}</div>
  	<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" id="sms_company_name" name="sms_company_name" placeholder="{#gnr_name#}" required></div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_country#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$country_selector}</div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_region#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
      <select name="sms_company_region" id="sms_company_region" required>
        <option value="" selected></option>
      </select>
    </div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_city#}</div>
  	<div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
  		<select name="sms_company_city" id="sms_company_city" required>
  			<option value="" selected></option>
  		</select>
  	</div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_smssettings_coverage#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$coverage_selector}</div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_smssettings_address#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" id="sms_company_address" name="sms_company_address" placeholder="{#p_smssettings_address#}" required></div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_smssettings_phones#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control" id="sms_company_phone_number" name="sms_company_phone_number" placeholder="{#p_smssettings_phones#}" required></textarea><div id="ph_no_err_msg"></div></div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_website#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" id="sms_company_website" name="sms_company_website" placeholder="{#gnr_website#}" required></div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_email#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" id="sms_company_email" name="sms_company_email" placeholder="{#gnr_email#}" required></div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_smssettings_settings#}</div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
      <input type="checkbox" name="sms_company_setting_elements[]" value="api_key" style="opacity: 1;position: relative;right: 0;"> API Key<br/>
      <input type="checkbox" name="sms_company_setting_elements[]" value="sender_id" style="opacity: 1;position: relative;right: 0;"> {#gnr_sender_name#}<br/>
      <input type="checkbox" name="sms_company_setting_elements[]" value="username" style="opacity: 1;position: relative;right: 0;"> {#gnr_user_name#}<br/>
      <input type="checkbox" name="sms_company_setting_elements[]" value="password" style="opacity: 1;position: relative;right: 0;"> {#gnr_password#}<br/>
    </div>

    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
    <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success" >{#gnr_add#}</button></div>
  </div>

</form>

{/block}
{block name=back}{url urltype="path" url_string="adm/P001/smssetting/show/0/{$smarty.session.lang}"}{/block}
