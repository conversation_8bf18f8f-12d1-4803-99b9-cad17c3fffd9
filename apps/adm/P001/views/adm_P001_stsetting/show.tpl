{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=title}
	{$dropdown->translatedName}
{/block}
{block name=body}
	<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
		<thead>
		<tr>
			<th width="5%">&nbsp;
				{url check=0 urltype="madd" opr_code='stsetting' url_string="adm/P001/stsetting/add/0/{$smarty.session.lang}"}
			</th>
			<th width="40%">{#gnr_name#}</th>
			<th width="10%">{#gnr_code#}</th>
			<th width="10%">{#gnr_id#}</th>
			<th width="10%">{#p_extra_first#}</th>
			<th width="10%">{#p_extra_second#}</th>
			<th width="10%">{#gnr_order#}</th>
			<th width="15%">{#gnr_settings#}</th>
		</tr>
		</thead>
		<tbody>
        {$i=1}
        {foreach $settings as $setting}
			<tr>
				<td align="center">{$i++}</td>
				<td>{$setting->translatedName}</td>
				<td>{$setting->id}</td>
				<td>{$setting->code}</td>
				<td>{$setting->num1}</td>
				<td>{$setting->num2}</td>
				<td>{$setting->order}</td>
				<td align="center">
                    {url check=0 urltype="medit" opr_code='stsetting' url_string="adm/P001/stsetting/edit/0/{$smarty.session.lang}/{$setting->id}"}
                    {url check=0 urltype="mdelete" opr_code='stsetting' url_string="adm/P001/stsetting/confirm/0/{$smarty.session.lang}/{$setting->id}"}
				</td>
			</tr>
        {/foreach}
		</tbody>
	</table>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/stopr/show/0/{$smarty.session.lang}"}{/block}