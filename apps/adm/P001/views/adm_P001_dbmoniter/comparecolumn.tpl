{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>{/block}
{block name=body}
	<div style="direction: ltr">
		<div class="row">
			<div class="col-lg-12">
				<div class="col-lg-6 snsoinput">In Current (Not In Exported)</div>
				<div class="col-lg-6 snsoinput">In Exported (Not In Current)</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-12">
				<div class="col-lg-6">
					{foreach $LocalColumnList as $key => $col}
						<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput" style="direction: ltr">
							{if $col.Existence eq 0}
								{url check=0 urltype="button" url_string="adm/P001/dbmoniter/comparecolumn/0/{$smarty.session.lang}/dropColumn/{$smarty.session.s_moniter_token}/{$col.Name}" text_value="Drop Column"}
							{/if}
						</div>
						<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput" style="direction: ltr">
							{url check=0 urltype="button" url_string="adm/P001/dbmoniter/columnfeaturesReview/0/{$smarty.session.lang}/{$col.Name}" text_value="Check" style="btn btn-default shiny small"}
							{if $col.Equality eq 0}<span class="bigger red">X</span>{/if}
							{if $col.Existence eq 1}
								<span class="small green"><i class="fa fa-circle"></i></span>
							{else}
								<span class="small red"><i class="fa fa-circle"></i></span>
							{/if}
							{$col.Name}

						</div>
					{/foreach}
				</div>
				<div class="col-lg-6">
					{foreach $ExportedColumnList as $key => $col}
						<div class="row">
							<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsoinput" style="direction: ltr">
								{if $col.Existence eq 0}
									{url check=0 urltype="button" url_string="adm/P001/dbmoniter/comparecolumn/0/{$smarty.session.lang}/createColumn/{$smarty.session.s_moniter_token}/{$col.Name}" text_value="Create Column"}
								{/if}
							</div>
							<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput" style="direction: ltr">
								<a href="#" class="btn btn-default shiny small">.</a>
								{if $col.Existence eq 1}
									<span class="small green"><i class="fa fa-circle"></i></span>
								{else}
									<span class="small red"><i class="fa fa-circle"></i></span>
								{/if}
								<span style="line-height: normal; padding-top: 2px; margin: 2px;">{$col.Name}</span>
							</div>
						</div>
					{/foreach}
				</div>
			</div>
		</div>
	</div>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/dbmoniter/compare/0/{$smarty.session.lang}"}{/block}
