{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=body}
	<div class="row">
		<div class="col-lg-12">

			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 snsoinput" style="text-align: center;">
				<div class="row">
					In Current (Not In Exported)
				</div>
			</div>

			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 snsoinput" style="text-align: center;">
				<div class="row">
					In Exported (Not In Current)
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-lg-12">
			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12" style="text-align: left; direction: ltr;">
				<div class="row">
					<div class="row">
						<div class="col-lg-12"><span class="blue">Field: </span>{$column2.Field}</div>
						<div class="col-lg-12">
							{if $column1.Type neq $column2.Type}
								{url check=0 urltype="button" url_string="adm/P001/dbmoniter/columnfeaturesReview/0/{$smarty.session.lang}/{$ColumnName}/alterFeature/{$smarty.session.s_moniter_token}/{$smarty.session.s_table_to_compare}/{$column1.Field}/Type" text_value="Alter Feature"}
							{/if}
							<span class="blue">Type: </span>{$column2.Type}
						</div>
						<div class="col-lg-12">
							{if $column1.Collation neq $column2.Collation}
								{url check=0 urltype="button" url_string="adm/P001/dbmoniter/columnfeaturesReview/0/{$smarty.session.lang}/{$ColumnName}/alterFeature/{$smarty.session.s_moniter_token}/{$smarty.session.s_table_to_compare}/{$column1.Field}/Collation" text_value="Alter Feature"}
							{/if}
							<span class="blue">Collation: </span>{$column2.Collation}
						</div>
						<div class="col-lg-12">
							{if $column1.Null neq $column2.Null}
								{url check=0 urltype="button" url_string="adm/P001/dbmoniter/columnfeaturesReview/0/{$smarty.session.lang}/{$ColumnName}/alterFeature/{$smarty.session.s_moniter_token}/{$smarty.session.s_table_to_compare}/{$column1.Field}/Null" text_value="Alter Feature"}
							{/if}
							<span class="blue">Null: </span>{$column2.Null}
						</div>
						<div class="col-lg-12">
							<span class="blue">Key: </span>{$column2.Key}
							{if $column1.Key neq $column2.Key}
								{url check=0 urltype="button" url_string="adm/P001/dbmoniter/columnfeaturesReview/0/{$smarty.session.lang}/{$ColumnName}/alterFeature/{$smarty.session.s_moniter_token}/{$smarty.session.s_table_to_compare}/{$column1.Field}/Key" text_value="Alter Feature"}
							{/if}
						</div>
						<div class="col-lg-12">
							{if $column1.Default neq $column2.Default}
								{url check=0 urltype="button" url_string="adm/P001/dbmoniter/columnfeaturesReview/0/{$smarty.session.lang}/{$ColumnName}/alterFeature/{$smarty.session.s_moniter_token}/{$smarty.session.s_table_to_compare}/{$column1.Field}/Default" text_value="Alter Feature"}
							{/if}
							<span class="blue">Default: </span>{$column2.Default}
						</div>
						<div class="col-lg-12">
							{if $column1.Extra neq $column2.Extra}
								{url check=0 urltype="button" url_string="adm/P001/dbmoniter/columnfeaturesReview/0/{$smarty.session.lang}/{$ColumnName}/alterFeature/{$smarty.session.s_moniter_token}/{$smarty.session.s_table_to_compare}/{$column1.Field}/Extra" text_value="Alter Feature"}
							{/if}
							<span class="blue">Extra: </span>{$column2.Extra}
						</div>
						<div class="col-lg-12">
							{if $column1.Privileges neq $column2.Privileges}
								{url check=0 urltype="button" url_string="adm/P001/dbmoniter/columnfeaturesReview/0/{$smarty.session.lang}/{$ColumnName}/alterFeature/{$smarty.session.s_moniter_token}/{$smarty.session.s_table_to_compare}/{$column1.Field}/Privileges" text_value="Alter Feature"}
							{/if}
							<span class="blue">Privileges: </span>{$column2.Privileges}
						</div>
						<div class="col-lg-12">
							{if $column1.Comment neq $column2.Comment}
								{url check=0 urltype="button" url_string="adm/P001/dbmoniter/columnfeaturesReview/0/{$smarty.session.lang}/{$ColumnName}/alterFeature/{$smarty.session.s_moniter_token}/{$smarty.session.s_table_to_compare}/{$column1.Field}/Comment" text_value="Alter Feature"}
							{/if}
							<span class="blue">Comment: </span>{$column2.Comment}
						</div>
					</div>

				</div>
			</div>
			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12" style="text-align: left; direction: ltr;">
				<div class="row">
					<div class="col-lg-12"><span class="blue">Field: </span>{$column1.Field}</div>
					<div class="col-lg-12"><span class="blue">Type: </span>{$column1.Type}</div>
					<div class="col-lg-12"><span class="blue">Collation: </span>{$column1.Collation}</div>
					<div class="col-lg-12"><span class="blue">Null: </span>{$column1.Null}</div>
					<div class="col-lg-12"><span class="blue">Key: </span>{$column1.Key}</div>
					<div class="col-lg-12"><span class="blue">Default: </span>{$column1.Default}</div>
					<div class="col-lg-12"><span class="blue">Extra: </span>{$column1.Extra}</div>
					<div class="col-lg-12"><span class="blue">Privileges: </span>{$column1.Privileges}</div>
					<div class="col-lg-12"><span class="blue">Comment: </span>{$column1.Comment}</div>
				</div>
			</div>
		</div>
	</div>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/dbmoniter/comparecolumn/0/{$smarty.session.lang}"}{/block}

