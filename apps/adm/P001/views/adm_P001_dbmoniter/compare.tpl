{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>{/block}
{block name=page_body}
	<div style="direction: ltr">
		<table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
			<thead>
			<tr>
				<th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
				<th style="background-color: #A0D468 !important;" width="25%">Table</th>
				<th style="background-color: #A0D468 !important;" width="30%">Table Exist Status</th>
				<th style="background-color: #A0D468 !important;" width="40%">Column Compare Status</th>
			</tr>
			</thead>
			{$i=1}
			{foreach $tableList as $row}
				<tr {if $row.tableExist eq 0} style="color: red;"{/if}>
					<td align="center">{$i++}</td>
					<td>{$row.tableName}</td>
					<td align="center">
                        {if $row.tableExist eq 1}
                            <span class="small green">Exist</span>
                        {/if}
                        {if $row.tableExist eq 0}
							{url check=0 urltype="button" url_string="adm/P001/dbmoniter/compare/0/{$smarty.session.lang}/createTable/{$smarty.session.s_moniter_token}/{$row.tableName}" text_value="Create Table"}
                        {/if}
                    </td>
					<td align="center">
						{if $row.tableColumnIdentical eq 1}
                            <span class="small green">Identical</span>
						{/if}
                        {if $row.tableExist eq 1}
                            {if $row.tableColumnIdentical eq 0}
                                {url check=0 urltype="button" url_string="adm/P001/dbmoniter/comparecolumn/0/{$smarty.session.lang}/save_session/{$row.tableName}" text_value="Alter Columns"}
                            {/if}
                        {/if}
					</td>
				</tr>
			{/foreach}
		</table>
	</div>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/dbmoniter/show/0/{$smarty.session.lang}"}{/block}
{block name=page_header}

	<script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
	<script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
	<script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
	<script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
	<script>
		var InitiateSimpleDataTable = function() {
			return {
				init: function() {
					//Datatable Initiating
					var oTable = $('.sortable-table').dataTable({
						"sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
						"iDisplayLength": 50,
						"oTableTools": {
							"aButtons": [

							],
							"sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
						},
						"language": {
							"search": "",
							"sLengthMenu": "_MENU_",
							"oPaginate": {
								"sPrevious": "السابق",
								"sNext": "التالي"
							}
						}
					});
				}

			};

		}();

		InitiateSimpleDataTable.init();
	</script>
{/block}


