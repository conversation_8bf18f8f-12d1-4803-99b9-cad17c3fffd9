{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet"/>{/block}
{block name=page_body}
	<div style="direction: ltr">
		<table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
			<thead>
			<tr>
				<th style="background-color: #A0D468 !important;" width="5%">&nbsp;</th>
				<th style="background-color: #A0D468 !important;" width="20%">Table</th>
				<th style="background-color: #A0D468 !important;" width="55%">Table Column</th>
				<th style="background-color: #A0D468 !important;" width="55%">Create Primary Key</th>
			</tr>
			</thead>
			{$i=1}
			{foreach $db_tables as $row}
				<tr>
					<td align="center">{$i++}</td>
					<td>{$row.table_name}</td>
					<td align="center">{url check=0 urltype="mbutton" opr_code='opr' url_string="adm/P001/dbmoniter/column/0/{$smarty.session.lang}/{$row.table_name}" text_value="Column"}</td>
					<td align="center">{url check=0 urltype="button" opr_code='opr' url_string="adm/P001/dbmoniter/checkPrimaryKeys/0/{$smarty.session.lang}/resolvePrimaryKey/{$smarty.session.s_moniter_token}/{$row.table_name}" text_value="Resolve Primary Key"}</td>
				</tr>
			{/foreach}
		</table>
	</div>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/dbmoniter/show/0/{$smarty.session.lang}"}{/block}
{block name=page_header}

	<script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
	<script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
	<script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
	<script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
	<script>
		var InitiateSimpleDataTable = function() {
			return {
				init: function() {
					//Datatable Initiating
					var oTable = $('.sortable-table').dataTable({
						"sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
						"iDisplayLength": 50,
						"oTableTools": {
							"aButtons": [

							],
							"sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
						},
						"language": {
							"search": "",
							"sLengthMenu": "_MENU_",
							"oPaginate": {
								"sPrevious": "السابق",
								"sNext": "التالي"
							}
						}
					});
				}

			};

		}();

		InitiateSimpleDataTable.init();
	</script>
{/block}


