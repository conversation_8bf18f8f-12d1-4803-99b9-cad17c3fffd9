{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}support.tpl"}
{block name=title}{$program->translatedName}{/block}
{block name=body}

	{if $ErrorMessage eq 'CodeExist'}
		<div class="alert alert-danger fade in">
			{#gnr_code_exist#}
		</div>
    {/if}

	<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
		<thead>
		<tr>
			<th width="5%">&nbsp;
				{url check=0 urltype="madd" opr_code='sec' url_string="adm/P001/sec/add/0/{$smarty.session.lang}"}
			</th>
			<th width="35%"><b>{#p_sec#}</b></th>
			<th width="20%"><b>{#gnr_order#}</b></th>
			<th width="20%"><b>{#gnr_settings#}</b></th>
		</tr>
		</thead>
		<tbody>
        {$i=1}
        {foreach $sections as $section}
			<tr>
				<td align="center">{$i++}</td>
				<td>{$section->translatedName}</td>
				<td align="center">{$section->order}</td>
				<td align="center">
                    {url check=0 urltype="medit" opr_code='sec' url_string="adm/P001/sec/edit/0/{$smarty.session.lang}/{$section->id}"}
					&nbsp;[&nbsp;{$section->operationCount}&nbsp;]&nbsp;
                    {if $section->operationCount eq 0}
                        {url check=0 urltype="mdelete" opr_code='sec' url_string="adm/P001/sec/confirm/0/{$smarty.session.lang}/{$section->id}"}
                    {/if}
				</td>
			</tr>
        {/foreach}
		</tbody>
	</table>
{/block}
{block name=back}{url urltype="path" url_string="adm/P001/prg/show/0/{$smarty.session.lang}"}{/block}