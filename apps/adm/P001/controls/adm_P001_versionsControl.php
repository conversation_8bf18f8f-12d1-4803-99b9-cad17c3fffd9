<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class adm_P001_versionsControl extends Controller
{

    public function show($parm, $post)
    {

        switch ($parm[0]) {

            case Operation::INSERT:

                if ($_SESSION['s_versions_token'] === $parm[1]) {

                    $version = new Version();
                    $version->bindProperties($post);
                    $version->created_by = (int)$_SESSION['user']->id;
                    $version->created_date = \Carbon\Carbon::now()->toDateString();

                    if ($version->save())
                        Notification::createdAlert();

                }

                break;

            case Operation::UPDATE:

                if ($_SESSION['s_versions_token'] === $parm[1]) {

                    try {
                        $version = Version::readID((int)$parm[2]);

                        $version->bindProperties($post);

                        $version->date = $this->Date->get_date('ad', (new DateTime($post['date']))->format('Y-m-d'));

                        if ($version->update())
                            Notification::updatedAlert();

                    } catch (VersionException $e) {

                    }

                }

                break;

            case Operation::DELETE:

                if ($_SESSION['s_versions_token'] === $parm[1]) {

                    try {
                        $version = Version::readID((int)$parm[2]);

                        if ($version->delete())
                            Notification::deletedAlert();

                    } catch (VersionException $e) {

                    }

                }

                break;

        }


        try {

            $this->Smarty->assign('versions', Version::read([]));
        } catch (VersionException $e) {
            $this->Smarty->assign('versions', []);
        }

        $_SESSION['s_versions_token'] = Helper::generateToken();

    }

    public function add($parm, $post)
    {

    }

    public function edit($parm, $post)
    {

        try {
            $this->Smarty->assign('version', Version::readID($parm[0]));
        } catch (AssistVisitException $e) {
            $this->Smarty->assign('version', []);
        }

    }

    public function confirm($parm, $post)
    {

        try {
            $this->Smarty->assign('version', Version::readID($parm[0]));
        } catch (VersionException $e) {
            $this->Smarty->assign('version', null);
        }

    }

    public function details($parm, $post)
    {

        switch ($parm[0]) {

            case 'versionData':
                $_SESSION['versionID'] = $parm[1];
                break;

            case Operation::INSERT:

                if ($_SESSION['s_versions_details_token'] === $parm[1]){

                    try{
                        $version = Version::readID($parm[2] ?? 0);
                    }catch (VersionException $e){
                        $version = null;
                    }
                    
                    if ($version){

                        $versionDetail = new VersionDetails();
                        $versionDetail->bindProperties($post);
                        $versionDetail->version_id = $version->id;

                        if ($versionDetail->save())
                            Notification::createdAlert();

                    }

                }

                break;

            case Operation::UPDATE:

                if ($_SESSION['s_versions_details_token'] === $parm[1]){

                    try {
                        $version = Version::readID((int)$parm[2]);

                        $version->bindProperties($post);

                        $version->date = $this->Date->get_date('ad', (new DateTime($post['date']))->format('Y-m-d'));

                        if ($version->update())
                            Notification::updatedAlert();

                    } catch (VersionException $e) {

                    }


                }

                break;

            case Operation::DELETE:

                if ($_SESSION['s_versions_details_token'] === $parm[1]){
                    try{

                        $detail = VersionDetails::readID($parm[2]);

                        if ($detail->delete())
                            Notification::deletedAlert();

                    }catch (VersionDetailsException $e){

                    }
                }

                break;

            case 'updateDetails':

                if ($_SESSION['s_versions_details_token'] === $parm[1]){
                    try{
                        $detail = VersionDetails::readID($parm[2]);
                        $detail->bindProperties($post);

                        if ($detail->update())
                            Notification::updatedAlert();

                    }catch (VersionDetailsException $e){

                    }
                }

                break;

        }

        try{
            $version = Version::readID($_SESSION['versionID'] ?? 0);
        }catch (VersionException $e){
            $version = null;
        }

        $this->Smarty->assign('version', $version);

        $programs = [];
        try{
            $programs = collect(Program::read([]))->pluck('translatedName', 'id');
        }catch (ProgramException $e){

        }

        $this->Smarty->assign('programs', $programs);

        try{
            $summary = DB::table(sh_version_details::class)
                ->selectRaw('sh_version_details_prg_id, sh_version_details_id, COUNT(sh_version_details_id) as count')
                ->where(VersionDetails::VERSION_ID, $version->id)
                ->groupBy(VersionDetails::PRG_ID)
                ->get();
        }catch (ModelException $e){
            $summary = null;
        }

        $this->Smarty->assign('summary', $summary);

        try{
            $this->Smarty->assign('prgs', sh_prg::readAll());
        }catch (ModelException $e){
            $this->Smarty->assign('prgs', []);
        }

        try{
            $this->Smarty->assign('team', Setting::getList(290));
        }catch (SettingException $e){
            $this->Smarty->assign('team', []);
        }


        $_SESSION['s_versions_details_token'] = Helper::generateToken();

    }
    
    public function detailsEdit($parm, $post)
    {
        
        try{
            $this->Smarty->assign('detail', VersionDetails::readID($parm[0]));
        }catch (VersionDetailsException $e){
            $this->Smarty->assign('detail', []);
        }

        try{
            $this->Smarty->assign('prgs', sh_prg::readAll());
        }catch (ModelException $e){
            $this->Smarty->assign('prgs', []);
        }

        try{
            $this->Smarty->assign('team', Setting::getList(290));
        }catch (SettingException $e){
            $this->Smarty->assign('team', []);
        }

    }
    
    public function detailsConfirm($parm, $post)
    {

        $this->detailsEdit($parm, $post);

    }
    
    public function report($parm, $post)
    {

        try{
            $this->Smarty->assign('version', Version::readID($parm[0] ?? 0));
        }catch (VersionException $e){
            $this->Smarty->assign('version', []);
        }

        try{
            $summary = DB::table(sh_version_details::class)
                ->selectRaw('sh_version_details_prg_id, sh_version_details_id, COUNT(sh_version_details_id) as count')
                ->where(VersionDetails::VERSION_ID, $parm[0])
                ->groupBy(VersionDetails::PRG_ID)
                ->get();
        }catch (ModelException $e){
            $summary = null;
        }

        try{
            $executionSummary = DB::table(sh_version_details::class)
                ->selectRaw('sh_version_details_executor, sh_version_details_id, COUNT(sh_version_details_id) as count')
                ->where('sh_version_details_version_id', $parm[0])
                ->groupBy('sh_version_details_executor')
                ->get();
        }catch (ModelException $e){
            $executionSummary = null;
        }

        try{
            $testingSummary = DB::table(sh_version_details::class)
                ->selectRaw('sh_version_details_tester, sh_version_details_id, COUNT(sh_version_details_id) as count')
                ->where('sh_version_details_version_id', $parm[0])
                ->groupBy('sh_version_details_tester')
                ->get();
        }catch (ModelException $e){
            $testingSummary = null;
        }

        $this->Smarty->assign('summary', $summary);
        $this->Smarty->assign('executionSummary', $executionSummary);
        $this->Smarty->assign('testingSummary', $testingSummary);

    }

    public function print($parm, $post)
    {

        $this->report($parm, $post);

        DocumentProcessor::outputPDF($this->Smarty->fetch(DocumentProcessor::setSmartyFetchConfig()));

    }
}