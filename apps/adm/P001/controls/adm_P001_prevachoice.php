<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_prevachoice extends Controller
{

    // TODO this should be replaced with new better way
    public function show($parm, $post)
    {

        switch ($parm[0]){
            case '':
                $_SESSION['s_evalist_id'] = $parm[1];
                break;

            case 'insert':

                if($_SESSION['s_prevachoice_token'] === $parm[1]){

                    $priority = new ProjectPriority();
                    $priority->bindProperties($post);
                    $priority->created_by = $_SESSION['user']->id;
                    $priority->created_date = date('Y-m-d');
                    $priority->save();

                    Notification::createdAlert();

                }

                break;

            case 'update':

                if($_SESSION['s_prevachoice_token'] === $parm[1]){

                    try{

                        $priority = ProjectPriority::readID((int)$parm[2]);
                        $priority->bindProperties($post);
                        $priority->save();

                        Notification::updatedAlert();

                    }catch (ProjectPriorityException $e){

                    }
                }

                break;

            case 'delete':

                if($_SESSION['s_prevachoice_token'] === $parm[1]){

                    try{

                        $priority = ProjectPriority::readID((int)$parm[2]);
                        $priority->delete();

                        Notification::deletedAlert();

                    }catch (ProjectPriorityException $e){

                    }
                }

                break;
        }


        try{

            $this->Smarty->assign('priorities', ProjectPriority::read());

        }catch (ProjectPriorityException $e){

            $this->Smarty->assign('priorities',[]);

        }

        $_SESSION['s_prevachoice_token'] = md5(rand(0000,9999));

    }

    public function add($parm, $post)
    {

        $_SESSION['s_prevachoice_token'] = md5(rand(0000,9999));
    }

    public function edit($parm, $post)
    {

        try{

            $this->Smarty->assign('priority', ProjectPriority::readID((int)$parm[0]));

        }catch (ProjectPriorityException $e){

            $this->Smarty->assign('priority',[]);

        }

        $_SESSION['s_prevachoice_token'] = md5(rand(0000,9999));

    }

    public function confirm($parm, $post)
    {

        try{

            $this->Smarty->assign('priority', ProjectPriority::readID((int)$parm[0]));

        }catch (ProjectPriorityException $e){

            $this->Smarty->assign('priority',[]);

        }

        $_SESSION['s_prevachoice_token'] = md5(rand(0000,9999));

    }
}