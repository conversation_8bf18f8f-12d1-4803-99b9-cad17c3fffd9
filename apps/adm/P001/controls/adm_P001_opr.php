<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_opr extends Controller
{

    function show($parm, $post)
    {

        switch ($parm[0]){

            case 'save_session':
                $_SESSION['s_current_prg_id'] = $parm[1];
                $_SESSION['s_opr_sec_id'] = 0;
                break;

            case 'save_sec_session':
                $_SESSION['s_opr_sec_id'] = $parm[1];
                break;

            case 'insert':

                if($_SESSION['s_operation_token']==$parm[1]){

                    try{

                        $operation = new Operation();
                        $operation->bindProperties($post);
                        $operation->code = Helper::refineCode($post['code']);
                        $operation->bnd_id = $_SESSION['s_bnd_id'];
                        $operation->prg_id = $_SESSION['s_current_prg_id'];
                        $operation->sec_id = $_SESSION['s_opr_sec_id'];
                        $operation->created_by = $_SESSION['user']->id;
                        $operation->prv = implode(',', $post['prv']);
                        //$operation->org_type = null;
                        $operation->create();

                        Notification::createdAlert();

                    }catch (OperationException $e){
                        if($e->getMessage() == "OperationCodeExist"){
                            Notification::alertMessage(Notification::ERROR,"OperationCodeExist");
                        }
                    }

                }

                break;

            case 'update':
//                return Operation::readID($post['new_hirarchical_prior_opr_id']);
                if($_SESSION['s_operation_token']==$parm[1]){

                    try{

                        $operation = Operation::readID($parm[2]);
                        $operation->sec_id = $post['section_id'];
                        $operation->bindProperties($post);
                        $operation->code = Helper::refineCode($post['code']);
                        $operation->prv = implode(',', $post['prv']);

                        $operation->update();

                        Notification::updatedAlert();

                    }catch (OperationException $e){
                        if($e->getMessage() == "OperationCodeExist"){
                            Notification::alertMessage(Notification::ERROR,"OperationCodeExist");
                        }
                    }


                }

                break;

            case 'delete':

                if($_SESSION['s_operation_token']==$parm[1]){

                    try{

                        $operation = Operation::readID((int) $parm[2] ?? 0);
                        $operation->delete();

                        Notification::deletedAlert();

                    }catch (OperationException $e){}

                }
                break;

        }

        try{
            $this->Smarty->assign('currentProgram',Program::readID((int) $_SESSION['s_current_prg_id'] ?? 0));
        }catch (ProgramException $e){
            $this->Smarty->assign('currentProgram',[]);
        }

        try{
            $this->Smarty->assign('currentSection',Menu::readID((int) $_SESSION['s_opr_sec_id'] ?? 0));
        }catch (MenuException $e){
            $this->Smarty->assign('currentSection',[]);
        }

        try{
            $this->Smarty->assign('sections',Menu::read([Menu::PRG_ID => $_SESSION['s_current_prg_id']],[0=>['property'=>Menu::ORDER,'sort'=>'ASC']]));
        }catch (MenuException $e){
            $this->Smarty->assign('sections',[]);
        }

        try{
            $this->Smarty->assign('operations', Operation::getOperationList((int) $_SESSION['s_opr_sec_id'] ?? 0));
        }catch (OperationException $e){
            $this->Smarty->assign('operations',[]);
        }

        $_SESSION['s_operation_token'] = md5(rand(0000,9999));
    }

    public function add($parm, $post)
    {
        try{
            $this->Smarty->assign('oprtype_list',Setting::getList(183));
        }catch (SettingException $e){
            $this->Smarty->assign('oprtype_list',[]);
        }

        try{
            $this->Smarty->assign('hirarchicalList',Setting::getList(213));
        }catch (SettingException $e){
            $this->Smarty->assign('hirarchicalList',[]);
        }

        try{
            $this->Smarty->assign('oprList', Operation::getOperationList((int) $_SESSION['s_opr_sec_id'] ?? 0));
        }catch (OperationException $e){
            $this->Smarty->assign('oprList',[]);
        }

        try{
            $this->Smarty->assign('oprvtype_list', PrivilegeType::read([]));
        }catch (PrivilegeTypeException $e){
            $this->Smarty->assign('oprvtype_list',[]);
        }

        try{

            $this->Smarty->assign('form_list', Operation::read([Operation::TYPE =>825]));
        }catch (OperationException $e){
            $this->Smarty->assign('form_list',[]);
        }

        try{
            $this->Smarty->assign('dependOnPrivilegeOn', Operation::read([
                Operation::SEC_ID => (int) $_SESSION['s_opr_sec_id'] ?? 0,
                Operation::HIRARCHICAL_TYPE => 940,
                Operation::SHOWABLE =>1
            ]));
        }catch ( OperationException $e){
            $this->Smarty->assign('dependOnPrivilegeOn', []);
        }

        $this->Smarty->assign('programs', Program::getClientPrograms());

        $_SESSION['s_operation_token'] = md5(rand(0000,9999));
    }

    public function edit($parm, $post)
    {
        try{
            $sections = Menu::read([Menu::PRG_ID => $_SESSION['s_current_prg_id']],
                                    [0=>['property'=>Menu::ORDER,'sort'=>'ASC']]);

            $this->Smarty->assign('sections', $sections);
        }catch (MenuException $exception){
            $this->Smarty->assign('sections', []);
        }

        try{
            $this->Smarty->assign('oprtype_list',Setting::getList(183));
        }catch (SettingException $e){
            $this->Smarty->assign('oprtype_list',[]);
        }

        try{
            $this->Smarty->assign('hirarchicalList',Setting::getList(213));
        }catch (SettingException $e){
            $this->Smarty->assign('hirarchicalList',[]);
        }

        try{
            $this->Smarty->assign('oprList', Operation::getOperationList((int) $_SESSION['s_opr_sec_id'] ?? 0));
        }catch (OperationException $e){
            $this->Smarty->assign('oprList',[]);
        }

        try{
            $this->Smarty->assign('oprvtype_list', PrivilegeType::read([]));
        }catch (PrivilegeTypeException $e){
            $this->Smarty->assign('oprvtype_list',[]);
        }

        try{

            $this->Smarty->assign('form_list', Operation::read([Operation::TYPE =>825]));
        }catch (OperationException $e){
            $this->Smarty->assign('form_list',[]);
        }

        try{
            $this->Smarty->assign('dependOnPrivilegeOn', Operation::read([
                Operation::SEC_ID => (int) $_SESSION['s_opr_sec_id'] ?? 0,
                Operation::HIRARCHICAL_TYPE => 940,
                Operation::SHOWABLE =>1
            ]));
        }catch ( OperationException $e){
            $this->Smarty->assign('dependOnPrivilegeOn', []);
        }

        $operation = [];

        try{
            $operation = Operation::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('row',$operation);
        }catch (OperationException $e){
            $this->Smarty->assign('row',[]);
        }

        switch ($operation->hirarchical_type){

            case 940:
                $this->Smarty->assign('jscode1','<script type="text/javascript">$(document).ready(function () {
                    $("#extra").hide("fast");
                    $("#supportPrivilegesDiv").show("fast");
                    $("#isSupportPrivileges").hide("fast");
                    $("#notSupportPrivilegesDiv").hide("fast");
                });</script>');
                break;

            case 941:
                $this->Smarty->assign('jscode1','<script type="text/javascript">$(document).ready(function () { 
                    $("#extra").show("fast");
					$("#isSupportPrivileges").show("fast");
					$("#supportPrivilegesDiv").show("fast");
                });</script>');
                break;
        }

        switch ($operation->show_in_prv_list){

            case 1:
                $this->Smarty->assign('jscode2','<script type="text/javascript">$(document).ready(function () {
                    $("#supportPrivilegesDiv").show("fast");
					$("#notSupportPrivilegesDiv").hide("fast");
                });</script>');
                break;

            case 0:
                $this->Smarty->assign('jscode2','<script type="text/javascript">$(document).ready(function () { 
                    $("#supportPrivilegesDiv").hide("fast");
					$("#notSupportPrivilegesDiv").show("fast");
                });</script>');
                break;
        }

        $this->Smarty->assign('programs', Program::getClientPrograms());

        $this->Smarty->assign('organizationTypes', Setting::getList(289));

        $_SESSION['s_operation_token'] = md5(rand(0000,9999));
    }

    public function confirm($parm, $post)
    {
        try{
            $this->Smarty->assign('row',Operation::readID((int) $parm[0] ?? 0));
        }catch (OperationException $e){
            $this->Smarty->assign('row',[]);
        }

        $_SESSION['s_operation_token'] = md5(rand(0000,9999));
    }

}