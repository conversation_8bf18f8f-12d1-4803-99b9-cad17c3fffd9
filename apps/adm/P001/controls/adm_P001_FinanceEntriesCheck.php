<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_FinanceEntriesCheck extends Controller
{

    public function show($parm, $post)
    {
        switch ($parm[0]) {
            case 'menu':
                $_SESSION['s_year_id'] = 0;
                break;
            case 'save_session':
                $_SESSION['s_year_id'] = $post['year_id'];
                break;
            case 'createEntry':
                if ($parm[1] === $_SESSION['s_operation_token']) {
                    $entryNumber = (int)$parm[2];
                    $accountId = $post['account'];
                    $date = $this->Date->get_date('ad', $post['date']);

                    $missingEntriesFinder = new FinanceMissingEntries(
                        $_SESSION['organization'],
                        $_SESSION['s_year_id']
                    );
                    $missingEntriesFinder->createEntryWithNumber($entryNumber, $accountId, $date);

                    Notification::alertMessage(Notification::SUCCESS, 'message_created_successfully');
                }
                break;
        }
        if ($_SESSION['s_year_id']) {
            $missingEntriesFinder = new FinanceMissingEntries($_SESSION['organization'], $_SESSION['s_year_id']);
            $this->Smarty->assign('missingEntries', $missingEntriesFinder->getMissingEntriesNumbers());
        }
        try {
            $this->Smarty->assign('fiscalYears', fin_year::simpleReadByProperty([
                FinYear::ORG_ID => $_SESSION['organization']->id,
            ]));
        } catch (ModelException $e) {
            $this->Smarty->assign('fiscalYears', []);
        }
        $_SESSION['s_operation_token'] = Helper::generateToken();
    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('entry', $parm[0]);
        $accounts = DB::table(fin_acc::class)
            ->where([
                FinAccount::ORG_ID    => $_SESSION['organization']->id,
                FinAccount::YEAR_ID   => $_SESSION['s_year_id'],
                FinAccount::BRANCHING => FinAccount::SETTING_SUB_ACCOUNT
            ])->get();
        $this->Smarty->assign('accounts', $accounts);
//        $this->Smarty->assign('accounts', FinAccount::getSubAccountsByEntity($_SESSION['organization']->id, $_SESSION['s_year_id']));
    }
}
