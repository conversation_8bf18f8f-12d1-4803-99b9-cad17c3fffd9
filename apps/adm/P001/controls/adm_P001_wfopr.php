<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_wfopr extends Controller
{

    public function show($parm, $post)
    {

        switch ($parm[0]) {

            case 'save_session':

                $_SESSION['s_wf_prg_id'] = (int)$parm[1] ?? 0;

                break;

            case 'update':

                if($_SESSION['s_wfopr_token'] == $parm[1]){

                    try{

                        $npt = Operation::readID((int)$parm[2]);
                        $npt->bindProperties($post);
                        $npt->save();

                        Notification::updatedAlert();

                    }catch (OperationException $e){

                    }
                }

                break;
        }

        try{

            $this->Smarty->assign('operations', Operation::read([
                Operation::PRG_ID => $_SESSION['s_wf_prg_id'],
                Operation::HAVE_WF => 1
            ],
                [
                    0=> [
                        'property'=>Operation::ORDER,
                        'sort'=>'ASC'
                    ]
                ]));

        }catch (OperationException $e){

            $this->Smarty->assign('operations',[]);

        }

        try{

            $this->Smarty->assign('programs', Program::read([],[
                0=> [
                    'property'=>Program::ORDER,
                    'sort'=>'ASC'
                ]
            ]));

        }catch (ProjectException $e){

            $this->Smarty->assign('programs',[]);

        }

        $_SESSION['s_wfopr_token'] = md5(rand(0000,9999));

    }

    public function edit($parm, $post)
    {

        try{

            $this->Smarty->assign('operation', Operation::readID((int)$parm[0]));

        }catch (OperationException $e){

            $this->Smarty->assign('operation', []);

        }

        $_SESSION['s_wfopr_token'] = md5(rand(0000,9999));
    }

    public function inputs($parm, $post)
    {

        switch ($parm[0]) {

            case 'save_session':

                try{

                    $operation = Operation::readID((int)$parm[1]);
                    $_SESSION['s_wf_opr_id'] = $operation->id;
                    $_SESSION['s_wf_opr_tbl_name'] = $operation->tbl_name;

                }catch (OperationException $e){

                }

                break;

            case 'update':

                if ($_SESSION['s_wfopr_token'] == $parm[1]) {

                    try{

                        $npt = WorkflowStepInput::readID((int)$parm[2]);
                        $npt->bindProperties($post);
                        $npt->name = $post['translatedName'];
                        $npt->save();

                        Notification::updatedAlert();

                    }catch (WorkflowStepInputException $e){

                    }

                }
                break;

            case 'readallinput':

                if ($_SESSION['s_wfopr_token'] == $parm[1]) {

                    try{

                        $oldInputs = WorkflowStepInput::read([
                            WorkflowStepInput::OPR_ID => $_SESSION['s_wf_opr_id'],
                            WorkflowStepInput::STP_ID => 0
                        ]);

                    }catch (WorkflowStepInputException $e){

                    }

                    if($oldInputs){

                        foreach ($oldInputs as $oldInput) {
                            $oldInput->delete();
                        }

                    }

                    $columns = WorkflowStepInput::getColumns($_SESSION['s_wf_opr_tbl_name']);
                    foreach ($columns as $column) {

                        if (WorkflowStepInput::count([
                            WorkflowStepInput::OPR_ID => $_SESSION['s_wf_opr_id'],
                            WorkflowStepInput::STP_ID => 0,
                            WorkflowStepInput::CNAME => $column->Field
                        ])==0) {

                            $npt = new WorkflowStepInput();
                            $npt->prg_id = $_SESSION['s_wf_prg_id'];
                            $npt->opr_id = $_SESSION['s_wf_opr_id'];
                            $npt->grf_id = 0;
                            $npt->stp_id = 0;
                            $npt->name = $column->Comment;
                            $npt->cname = $column->Field;
                            $npt->save();

                        }
                    }
                    $_SESSION['s_wfopr_token'] = md5(rand(0000, 9999));
                }
                break;
        }

        try{

            $this->Smarty->assign('inputs', WorkflowStepInput::read([
                WorkflowStepInput::OPR_ID => $_SESSION['s_wf_opr_id'],
                WorkflowStepInput::STP_ID => 0
            ],[0=>['property'=>WorkflowStepInput::INCLUDE,'sort'=>'DESC']]));

        }catch (WorkflowStepInputException $e){

            $this->Smarty->assign('inputs',[]);

        }

        $_SESSION['s_wfopr_token'] = md5(rand(0000, 9999));

    }

    public function editinput($parm, $post)
    {
        try{

            $input = WorkflowStepInput::readID((int)$parm[0] ?? 0);
            $this->Smarty->assign('input', $input);

        }catch (WorkflowStepInputException $e){

        }

        if($input){

            switch ($input->type){

                case 74:

                    $this->Smarty->assign('check_two', "$('#extra1').show('fast'); $('#extra2').hide('fast');");

                    break;

                case 623:

                    $this->Smarty->assign('check_two', "$('#extra2').show('fast'); $('#extra1').hide('fast');");

                    break;

            }
        }


        $this->Smarty->assign('inputList',Setting::getList(32));
        $this->Smarty->assign('stoprList',DropDownList::read());

        $_SESSION['s_wfopr_token'] = md5(rand(0000, 9999));

    }
}
