<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_language extends Controller
{

    public function show($parm, $post)
    {

        switch ($parm[0]){

            case Operation::INSERT:

                if($_SESSION['s_language_token'] === $parm[1]){

                    try {

                        $language = new Language();
                        $language->bindProperties($post);
                        $language->create();

                        Notification::createdAlert();

                    }catch(LanguageException $e){

                    }

                    $_SESSION['s_language_token'] = md5(rand(0000, 9999));
                }

                break;

            case Operation::UPDATE:

                if($_SESSION['s_language_token'] === $parm[1]){

                    try {

                        $language = Language::readID((int) $parm[2] ?? 0);
                        $language->bindProperties($post);
                        $language->update();

                        Notification::updatedAlert();

                    }catch(LanguageException $e){

                    }

                    $_SESSION['s_language_token'] = md5(rand(0000, 9999));
                }

                break;

            case Operation::DELETE:

                if($_SESSION['s_language_token'] === $parm[1]){

                    try {

                        $language = Language::readID((int) $parm[2] ?? 0);
                        $language->delete();

                        Notification::deletedAlert();

                    }catch(LanguageException $e){

                    }

                    $_SESSION['s_language_token'] = md5(rand(0000, 9999));
                }

                break;

        }

        try{
            $this->Smarty->assign('languages', Language::read([]));
        }catch (LanguageException $e){
            $this->Smarty->assign('languages',[]);
        }
    }

    public function add($parm, $post)
    {

        $this->Smarty->assign('directionsList',Setting::getList(222));

        $_SESSION['s_language_token'] = md5(rand(0000, 9999));
    }

    public function edit($parm, $post)
    {

        try{
            $this->Smarty->assign('language', Language::readID((int) $parm[0] ?? 0));
        }catch (LanguageException $e){
            $this->Smarty->assign('language', []);
        }

        $this->Smarty->assign('directionsList',Setting::getList(222));
        $_SESSION['s_language_token'] = Helper::generateToken();
    }

    public function confirm($parm, $post)
    {

        try{
            $this->Smarty->assign('language', Language::readID((int) $parm[0] ?? 0));
        }catch (LanguageException $e){
            $this->Smarty->assign('language', []);
        }

        $_SESSION['s_language_token'] = Helper::generateToken();

    }
}