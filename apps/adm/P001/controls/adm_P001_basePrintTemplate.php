<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_basePrintTemplate extends Controller
{
    public function show($parm, $post)
    {
        try {
            $baseTemplates = BasePrintTemplate::read();
        } catch (BasePrintTemplateException $e) {
            $baseTemplates = [];
        }

        $this->Smarty->assign('baseTemplates', $baseTemplates);
    }

    public function placeholders($parm, $post)
    {
        try {
            $baseTemplate = BasePrintTemplate::readID((int)$parm[0] ?? 0);
        } catch (BasePrintTemplateException $e) {
            $baseTemplate = null;
        }

        $this->Smarty->assign('baseTemplate', $baseTemplate);
    }

    public function add($parm, $post)
    {
        // Process Insertion
        $this->processForm();
    }

    public function edit($parm, $post)
    {
        if ($this->processForm((int)$parm[0])) {
            return;
        }

        try {
            $baseTemplate = BasePrintTemplate::readID((int)$parm[0]);
        } catch (BasePrintTemplateException $e) {
            $baseTemplate = null;
        }

        $this->Smarty->assign('baseTemplate', $baseTemplate);
    }

    public function delete($parm, $post)
    {
        try {
            BasePrintTemplate::readID((int)$parm[0])->delete();
        } catch (BasePrintTemplateException $e) {
        }

        $this->redirect();
    }

    private function processForm(int $baseTemplateID = 0)
    {
        if (strtolower($_SERVER['REQUEST_METHOD']) !== "post") {
            return false;
        }

        try {

            $baseTemplate = empty($baseTemplateID) ?
                new BasePrintTemplate() :
                BasePrintTemplate::readID($baseTemplateID);

            $baseTemplate->bindProperties($_POST);
            $baseTemplate->placeholders = collect($_POST['placeholdersCodes'])->filter()->combine(array_filter($_POST['placeholders']))->all();
            $baseTemplate->save();

            Notification::updatedAlert();
        } catch (BasePrintTemplateException $e) {

        } catch (PDOException $e) {
            if ($e->getCode() == 23000) {
                // possible code duplicate!
            }
        }

        redirect('basePrintTemplate/show');
        return true;
    }

    private function redirect()
    {
        $this->URL->redirect("adm/P001/basePrintTemplate/show/0/{$_SESSION['lang']}/", 1, 1);
    }
}