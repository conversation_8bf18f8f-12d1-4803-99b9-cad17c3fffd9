<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_reportsConfiguration extends Controller
{
    
    public function show($parm, $post)
    {

        switch ($parm[0]){

            case 'menu':

                $_SESSION['s_subject_category'] = Subject::SUBJECT_TYPE_MISSION;
                $_SESSION['s_horizontal_tab'] = Subject::SUBJECT_TYPE_MISSION . 'subject';  // reports, data

                break;

            case 'subject':

                $_SESSION['s_subject_category'] = $parm[2];
                $_SESSION['s_horizontal_tab'] = $parm[2] . 'reports';  // reports, data

                switch ($parm[1]){

                    case Operation::UPDATE:

                        $_SESSION['s_subject_category'] = $post['category_id'];
                        $_SESSION['s_horizontal_tab'] = $post['category_id'] . 'subject';  // reports, data

                        if($_SESSION['s_reportsConfiguration_token'] == $parm[3]){

                            try{

                                $subject = Subject::readID((int) $parm[4] ?? 0);
                                $subject->bindProperties($post);
                                $subject->code = Helper::refineCode($post['code']);
                                $subject->save();

                                Notification::updatedAlert();

                            }catch (SubjectException $e){

                                if($e->getMessage() == "CodeExist"){
                                    $this->Smarty->assign('ErrorMessage','CodeExist');
                                }

                            }

                        }

                        break;

                    case Operation::DELETE:

                        if($_SESSION['s_reportsConfiguration_token'] == $parm[3]){

                            try{

                                $subject = Subject::readID((int) $parm[4] ?? 0);
                                $subject->delete();

                                Notification::deletedAlert();

                            }catch (SubjectException $e){}

                        }

                        break;

                    case 'updateSubjectsList':

                        $_SESSION['s_subject_category'] = Subject::SUBJECT_TYPE_MISSION;
                        $_SESSION['s_horizontal_tab'] = Subject::SUBJECT_TYPE_MISSION . 'subject';  // reports, data

                        if($_SESSION['s_reportsConfiguration_token'] == $parm[2]){

                            $subjects = (new \SNSO\Core\Reporter\Client('Resources','all'))->get()->fetchData();

                            try{

                                $dbSubjects = Subject::read();

                            }catch (SubjectException $e){

                                $dbSubjects = [];

                            }

                            if($dbSubjects){

                                foreach ($dbSubjects as $dbSubject) {
                                    if(!in_array($dbSubject->code,$subjects)){
                                        $dbSubject->delete();
                                    }

                                }

                            }

                            foreach ($subjects as $subjectCode) {
                                $subjectCodeArray[] = $subjectCode;
                                if(Subject::count([Subject::CODE => $subjectCode]) == 0 && $subjectCode != 'Resources'){

                                    $subject = new Subject();
                                    $subject->code = $subjectCode;
                                    $subject->programs_ids = '';
                                    $subject->category_id = 0;
                                    $subject->activation = Setting::ACTIVE;
                                    $subject->order = 0;
                                    $subject->save();

                                    $subjectReports = (new \SNSO\Core\Reporter\Client($subject->code,'abilities'))->get()->fetchData();

                                    foreach ($subjectReports as $subjectReport) {

                                        if(Report::count([Report::CODE => $subjectReport]) == 0 && !in_array($subjectReport,['all','abilities','abilityOptions'])){

                                            if(substr($subjectReport,-strlen('HTML')) == 'HTML'){

                                                $report = new Report();
                                                $report->subject_id = $subject->id;
                                                $report->type = Report::MAIN_REPORT;
                                                $report->data_type = 0;
                                                $report->code = $subjectReport;
                                                $report->activation = Setting::ACTIVE;
                                                $report->order = 0;
                                                $report->save();

                                            }

                                        }

                                    }

                                    try{

                                        $reports = Report::read([Report::SUBJECT_ID => $subject->id]);

                                    }catch (ReportException $e){
                                        $reports = [];
                                    }

                                    if($reports){

                                        foreach ($reports as $report) {
                                            if(!in_array($report->code,$subjectReports)){
                                                $report->delete();
                                            }

                                        }

                                    }

                                }

                            }



                        }

                        break;
                }

                break;

            case 'report':

                switch ($parm[1]){

                    case 'browseReports':

                        $_SESSION['s_subject_category'] = $parm[2];
                        $_SESSION['s_horizontal_tab'] = $parm[2] . 'report';  // reports, data
                        $_SESSION['s_subject_id'] = $parm[3];

                        break;

                    case Operation::UPDATE:

                        if($_SESSION['s_reportsConfiguration_token'] == $parm[2]){

                            $report = null;
                            try{

                                $report = Report::readID((int) $parm[3] ?? 0);
                                $report->bindProperties($post);
                                $report->subject_id = $_SESSION['s_subject_id'];
                                $report->code = Helper::refineCode($post['code']);
                                $report->update();

                                Notification::updatedAlert();

                            }catch (ReportException $e){

                            }

                        }

                        break;
                        case Operation::DELETE:

                        if($_SESSION['s_reportsConfiguration_token'] == $parm[2]){

                            $report = null;
                            try{

                                $report = Report::readID((int) $parm[3] ?? 0);
                                $report->delete();

                                Notification::deletedAlert();

                            }catch (ReportException $e){

                            }

                        }

                        break;

                }

                break;
        }

        try{

            $this->Smarty->assign('nonClassifiedReports',Subject::read([
                Subject::CATEGORY_ID => 0
            ]));

        }catch (SubjectException $e){

            $this->Smarty->assign('nonClassifiedReports',[]);

        }

        $this->Smarty->assign('categories',Subject::getSubjectsByCategories());

        if(!empty($_SESSION['s_subject_id'])){

            try{

                $currentSubject = Subject::readID((int) $_SESSION['s_subject_id'] ?? 0);
                $this->Smarty->assign('currentSubject', $currentSubject);

                $reports = (new \SNSO\Core\Reporter\Client($currentSubject->code,'abilities'))->get()->fetchData();

                foreach ($reports as $reportCode) {

                    if(Report::count([Report::CODE => $reportCode]) == 0 && !in_array($reportCode,['all','abilities','abilityOptions'])){

                        if(substr($reportCode,-strlen('HTML')) == 'HTML'){

                            $report = new Report();
                            $report->subject_id = $currentSubject->id;
                            $report->data_type = 0;
                            $report->type = Report::MAIN_REPORT;
                            $report->code = $reportCode;
                            $report->activation = Setting::ACTIVE;
                            $report->order = 0;
                            $report->save();

                        }

                    }

                }

                try{

                    $this->Smarty->assign('reports',Report::read([Report::SUBJECT_ID => $currentSubject->id]));

                }catch (ReportException $e){

                    $this->Smarty->assign('reports',[]);

                }

            }catch (SubjectException $e){

                $this->Smarty->assign('Subject',[]);

            }

        }

        $_SESSION['s_reportsConfiguration_token'] = md5(rand(0000,9999));

    }

    public function editSubject($parm, $post)
    {
        try{

            $this->Smarty->assign('category',Setting::readID((int)$parm[0] ?? 0));

        }catch (SettingException $e){

            $this->Smarty->assign('category',[]);

        }

        try{

            $this->Smarty->assign('subject', Subject::readID((int) $parm[1] ?? 0));

        }catch (ReportException $e){

            $this->Smarty->assign('subject',[]);

        }

        try{
            $this->Smarty->assign('programs',Program::read());
        }catch (ProjectException $e){
            $this->Smarty->assign('programs',[]);
        }

        $this->Smarty->assign('categories',Setting::getList(240));
        $this->Smarty->assign('activations',Setting::getList(19));

        $_SESSION['s_reportsConfiguration_token'] = md5(rand(0000,9999));

    }

    public function confirmSubject($parm, $post)
    {
        try{

            $this->Smarty->assign('category',Setting::readID((int)$parm[0] ?? 0));

        }catch (ReportException $e){

        }

        try{
            $this->Smarty->assign('subject', Subject::readID((int) $parm[1] ?? 0));
        }catch (ReportException $e){
            $this->Smarty->assign('subject', []);
        }

        $_SESSION['s_reportsConfiguration_token'] = md5(rand(0000,9999));

    }

    public function editReport($parm, $post)
    {

        try{

            $this->Smarty->assign('category',Setting::readID((int)$parm[0] ?? 0));

        }catch (ReportException $e){

        }

        try{

            $this->Smarty->assign('subject', Subject::readID((int)$parm[1] ?? 0));

        }catch (ReportException $e){

            $this->Smarty->assign('subject', []);
        }

        try{
            $this->Smarty->assign('report', Report::readID((int) $parm[2] ?? 0));
        }catch (ReportException $e){
            $this->Smarty->assign('report', []);
        }

        $this->Smarty->assign('reportTypes',Setting::getList(241));
        $this->Smarty->assign('activations',Setting::getList(19));
        $_SESSION['s_reportsConfiguration_token'] = md5(rand(0000,9999));

    }

    public function confirmReport($parm, $post)
    {

        try{
            $this->Smarty->assign('report', Report::readID((int) $parm[0] ?? 0));
        }catch (ReportException $e){
            $this->Smarty->assign('report',[]);
        }

        $_SESSION['s_reportsConfiguration_token'] = md5(rand(0000,9999));

    }

}