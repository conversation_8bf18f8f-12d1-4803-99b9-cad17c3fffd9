<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_sec extends Controller
{
    public function show($parm, $post)
    {

        if ($parm[0] == 'save_session') {
            $_SESSION['s_prg_id_is'] = $parm[1];
        }

        switch ($parm[0]){

            case Operation::INSERT:

                if($_SESSION['s_menu_token'] == $parm[1]){

                    try{

                        $menu = new Menu();
                        $menu->bindProperties($post);
                        $menu->prg_id = $_SESSION['s_prg_id_is'];
                        $menu->bnd_id = $_SESSION['s_bnd_id'];
                        $menu->code = Helper::refineCode($post['code']);
                        $menu->create();

                        Notification::createdAlert();

                    }catch (MenuException $e){

                        if($e->getMessage() == "CodeExist"){

                            Notification::alertMessage(Notification::WARNING,Translation::translate(Program::readID(Program::PROGRAM_SUPPORT_MANAGER_P001),'CodeExist'));

                        }

                    }

                }

                break;

            case Operation::UPDATE:

                if($_SESSION['s_menu_token'] == $parm[1]){

                    try{

                        $menu = Menu::readID((int) $parm[2] ?? 0);
                        $menu->bindProperties($post);
                        $menu->code = Helper::refineCode($post['code']);
                        $menu->update();

                        Notification::updatedAlert();

                    }catch (MenuException $e){

                        if($e->getMessage() == "CodeExist"){

                            Notification::alertMessage(Notification::WARNING,Translation::translate(Program::readID(Program::PROGRAM_SUPPORT_MANAGER_P001),'CodeExist'));

                        }

                    }

                }

                break;

            case Operation::DELETE:

                if($_SESSION['s_menu_token'] == $parm[1]){

                    try{

                        $menu = Menu::readID((int) $parm[2] ?? 0);
                        $menu->delete();

                        Notification::deletedAlert();

                    }catch (MenuException $e){}

                }

                break;
        }


        try{
            $this->Smarty->assign('program',Program::readID((int) $_SESSION['s_prg_id_is'] ?? 0));
        }catch (ProgramException $e){
            $this->Smarty->assign('program',[]);
        }

        try{
            $this->Smarty->assign('sections',Menu::read([Menu::PRG_ID => $_SESSION['s_prg_id_is']],[0=>['property'=>Menu::ORDER,'sort'=>'ASC']]));
        }catch (MenuException $e){
            $this->Smarty->assign('sections',[]);
        }

        $_SESSION['s_menu_token'] = md5(rand(0000,9999));

    }

    public function add($parm, $post)
    {
        $_SESSION['s_menu_token'] = md5(rand(0000,9999));
    }

    public function edit($parm, $post)
    {
        try{
            $this->Smarty->assign('section', Menu::readID((int) $parm[0] ?? 0));
        }catch (MenuException $e){}

        try{
            $this->Smarty->assign('program',Program::readID((int) $_SESSION['s_prg_id_is'] ?? 0));
        }catch (ProgramException $e){
            $this->Smarty->assign('program',[]);
        }

        $_SESSION['s_menu_token'] = md5(rand(0000,9999));

    }

    public function confirm($parm, $post)
    {
        try{
            $this->Smarty->assign('section', Menu::readID((int) $parm[0] ?? 0));
        }catch (MenuException $e){}

        $_SESSION['s_menu_token'] = md5(rand(0000,9999));

    }
}
