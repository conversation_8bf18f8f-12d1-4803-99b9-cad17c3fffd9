<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_dbmoniter extends Controller
{

    public function show($parm, $post)
    {

        switch ($parm[0]) {
            case 'save_session':
                $_SESSION['s_wf_prg_id'] = $parm[1] ?? 0;
                break;
            case 'update':
                $this->DB->update('sh_opr', $post, $parm[1]);
                break;
        }

        $databaseName = snso::_config('db_name');
        $this->Smarty->assign(
            'db_tables',
            $this->DB->g_query("SELECT `TABLE_NAME` AS 'table_name', `TABLE_ROWS` AS 'rowsNum' FROM `information_schema`.`tables` WHERE `table_schema` = ?",
                ['table_schema' => $databaseName]
            )
        );

    }

    public function compare($parm, $post, $FILE)
    {

        switch ($parm[0]) {
            case 'createTable':
                if ($_SESSION['s_moniter_token'] == $parm[1]) {
                    $_SESSION['s_moniter_token'] = md5(rand(0000, 9999));
                }
                break;
        }

        $tableListArr = array();
        $databaseName = snso::_config('db_name');
        // select * from information_schema.columns where table_schema = 'snso_schema_hosted' order by table_name
        $tableList = $this->DB->g_query("SELECT table_name FROM information_schema.tables WHERE table_type = 'base table' AND table_schema=?",
            array('table_schema' => $databaseName));
        foreach ($tableList as $key => $table) {
            $carr = array();
            $columnList = $this->DB->g_query("SHOW FULL COLUMNS FROM " . $table['table_name'], array());
            foreach ($columnList as $ckey => $column) {
                $carr[$column['Field']] = $column;
            }

            $tableListArr[$table['table_name']] = $carr;
        }
        $_SESSION['s_db_schem_local'] = $tableListArr;

        //**************************************************************************************************************

        $i = 0;
        $arr = array();
        $TablesList = DatabaseMonitor::readDBFile();
        foreach ($TablesList as $key => $cols) {

            $arr[$i]['tableName'] = $key;
            $arr[$i]['tableExist'] = array_key_exists($key, $_SESSION['s_db_schem_local']);
            if ($TablesList[$key] == $_SESSION['s_db_schem_local'][$key]) {
                $arr[$i]['tableColumnIdentical'] = true;
            } else {
                $arr[$i]['tableColumnIdentical'] = false;
            }
            $i++;
        }
        $this->Smarty->assign('tableList', $arr);
        $_SESSION['s_moniter_token'] = md5(rand(0000, 9999));

    }

    public function column($parm, $post)
    {
        $tablecolumn = $this->DB->g_query("SHOW FULL COLUMNS FROM " . $parm[0], array());
        $this->Smarty->assign('columnList', $tablecolumn);
    }

    public function comparecolumn($parm, $post)
    {
        switch ($parm[0]) {
            case 'save_session':
                $_SESSION['s_table_to_compare'] = $parm[1];
                break;

            case 'createColumn':
                if ($_SESSION['s_moniter_token'] == $parm[1]) {
                    DatabaseMonitor::createColumn($_SESSION['s_table_to_compare'], $parm[2]);
                    $_SESSION['s_moniter_token'] = md5(rand(0000, 9999));
                }
                break;

            case 'dropColumn':
                if ($_SESSION['s_moniter_token'] == $parm[1]) {
                    $this->DB->dropColumn($_SESSION['s_table_to_compare'], $parm[2]);
                    $_SESSION['s_moniter_token'] = md5(rand(0000, 9999));
                }
                break;
        }

        // LOCAL


        $LocalColumnList = DatabaseMonitor::getTableInfo($_SESSION['s_table_to_compare'],
            DatabaseMonitor::LOCAL_DATABASE);
        $ExportedColumnList = DatabaseMonitor::getTableInfo($_SESSION['s_table_to_compare'],
            DatabaseMonitor::EXPORTED_DATABASE);

        $i = 0;
        $LocalArr = array();
        foreach ($LocalColumnList as $key => $column) {
            $LocalArr[$i]['Name'] = $column['Field'];
            $LocalArr[$i]['Feture'] = json_encode($column);
            $LocalArr[$i]['Existence'] = (!empty($ExportedColumnList[$key])) ? 1 : 0;
            $LocalArr[$i]['Equality'] = ($ExportedColumnList[$key] == $column) ? 1 : 0;
            $i++;
        }
        $this->Smarty->assign('LocalColumnList', $LocalArr);

        // Exported
        $i = 0;
        $ExportedArr = array();
        foreach ($ExportedColumnList as $key => $column) {
            $ExportedArr[$i]['Name'] = $column['Field'];
            $ExportedArr[$i]['Feture'] = json_encode($column);
            $ExportedArr[$i]['Existence'] = (!empty($LocalColumnList[$key])) ? 1 : 0;
            $ExportedArr[$i]['Equality'] = ($LocalColumnList[$key] == $column) ? 1 : 0;
            $i++;
        }
        $this->Smarty->assign('ExportedColumnList', $ExportedArr);
        $_SESSION['s_moniter_token'] = md5(rand(0000, 9999));
    }

    public function columnfeaturesReview($parm, $post)
    {
        if (!empty($parm[1]) && $parm[1] == 'alterFeature' && $_SESSION['s_moniter_token'] == $parm[2]) {
            DatabaseMonitor::alterColumnFeature($parm[3], $parm[4]);
        }
        $result = DatabaseMonitor::getColumnInfo($_SESSION['s_table_to_compare'], $parm[0]);
        $this->Smarty->assign('column1', $result[0]);
        $this->Smarty->assign('column2', $result[1]);
        $this->Smarty->assign('ColumnName', $parm[0]);
        $_SESSION['s_moniter_token'] = md5(rand(0000, 9999));
    }

    public function export($parm, $post)
    {
        $tableListArr = array();
        $databaseName = snso::_config('db_name');
        // select * from information_schema.columns where table_schema = 'snso_schema_hosted' order by table_name
        $tableList = $this->DB->g_query("SELECT table_name FROM information_schema.tables WHERE table_type = 'base table' AND table_schema=?",
            array('table_schema' => $databaseName));
        foreach ($tableList as $key => $table) {
            $carr = array();
            $columnList = $this->DB->g_query("SHOW FULL COLUMNS FROM " . $table['table_name'], array());
            foreach ($columnList as $ckey => $column) {
                $carr[$column['Field']] = $column;
            }

            $tableListArr[$table['table_name']] = $carr;
        }

        $encrypted = Helper::encoding(json_encode($tableListArr));

        header('Content-Description: File Transfer');
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename= ' . str_replace(' ', '_', CLIENT_NAME) . '.txt');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header("Content-Length: " . strlen($encrypted));
        ob_clean();
        flush();
        echo $encrypted;
        exit;
    }

    public function createtable($parm, $post)
    {
        $this->Smarty->assign('tableName', $parm[0]);
        $_SESSION['s_moniter_token'] = md5(rand(0000, 9999));
    }

    public function createcolumn($parm, $post)
    {
        $this->Smarty->assign('columnName', $parm[0]);
        $_SESSION['s_moniter_token'] = md5(rand(0000, 9999));
    }

    public function dropcolumn($parm, $post)
    {
        $this->Smarty->assign('columnName', $parm[0]);
        $_SESSION['s_moniter_token'] = md5(rand(0000, 9999));
    }

    public function checkPrimaryKeys($parm, $post)
    {
        switch ($parm[0]) {
            case 'resolvePrimaryKey':
                if ($_SESSION['s_moniter_token'] === $parm[1]) {
                    try {
                        DatabaseMonitor::resolvePrimaryKey($parm[2]);
                    } catch (ModelException $e) {
                    }
                }
                break;
        }
        $this->Smarty->assign('db_tables', DatabaseMonitor::PrimaryKeyChecklist());
        $_SESSION['s_moniter_token'] = md5(rand(0000, 9999));
    }
}
