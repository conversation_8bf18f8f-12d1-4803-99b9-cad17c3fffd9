<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_pmevalists extends Controller
{
    // TODO this should be replaced with new better way
    public function show($parm, $post)
    {
        if ($parm[0] == 'insert') {
            $arr = array();
            $arr['pm_preva_org_id'] = 1;
            $arr['pm_preva_name'] = $post['pm_preva_name'];
            $this->DB->insert('pm_preva', $arr);
        }

        if ($parm[0] == 'update') {
            $arr = array();
            $arr['pm_preva_name'] = $post['pm_preva_name'];
            $this->DB->update('pm_preva', $arr, $parm[1]);
        }

        if ($parm[0] == 'delete') {
            $this->DB->delete('pm_preva', $parm[1]);
        }
        $this->Smarty->assign('pm_preva_list', $this->DB->get_all('pm_preva', 'order by BINARY pm_preva_id DESC'));

    }

    public function add($parm, $post)
    {

    }

    public function edit($parm, $post)
    {
        $this->Smarty->assign('record_id', $parm[0]);
        $this->Smarty->assign('row', $this->DB->find_with_id('pm_preva', $parm[0]));

    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('record_id', $parm['0']);
        $this->Smarty->assign('record_name', $this->DB->get_name('pm_preva', $parm[0]));

    }
}
