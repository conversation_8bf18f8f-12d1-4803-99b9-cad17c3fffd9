<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_oprdocnumber extends Controller
{
    public function show($parm, $post)
    {

        switch ($parm[0]){

            case 'update':

                if($_SESSION['s_oprdocnumber_token'] == $parm[1]){

                    try{

                        $operation = Operation::readID((int) $parm[2] ?? 0);
                        $operation->doc_number = $post["doc_number"] ? $post["doc_number"] : 1;
                        $operation->allowed_extenstions = implode(",", $post["allowed_extenstions"]);
                        $operation->save();

                        Notification::updatedAlert();

                    }catch (OperationException $e){

                    }
                }

                break;
        }

        try{

            $this->Smarty->assign('operations', Operation::read([
                Operation::HAVE_DOC => 1
            ],[0=>['property'=>Operation::PRG_ID,'sort'=>'ASC']]));

        }catch (OperationException $e){

            $this->Smarty->assign('operations',[]);

        }

        $_SESSION['s_oprdocnumber_token'] = Helper::generateToken();

    }

    public function edit($parm, $post)
    {

        try{
            $this->Smarty->assign('operation', Operation::readID((int) $parm[0] ?? 0));
        }catch (OperationException $e){
            $this->Smarty->assign('operation',[]);
        }

        $this->Smarty->assign('extensions', Setting::getList(28));
        $_SESSION['s_oprdocnumber_token'] = Helper::generateToken();

    }
}