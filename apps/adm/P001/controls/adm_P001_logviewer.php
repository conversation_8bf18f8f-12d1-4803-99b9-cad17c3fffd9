<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_logviewer extends Controller
{

    public function show($parm, $post)
    {
        $logPath = LOG_DIR . DIRECTORY_SEPARATOR;
        $logFile = $logPath . date("dmY") . ".log";

// how often it checks the log file for changes, min 100
        $interval = 10000;

//use CSS color
        $textColor = "";

        if (!$textColor) {
            $textColor = "white";
        }
        if ($interval < 100) {
            $interval = 100;
        }
        function display($level)
        {
            // Validate if level is present in GET
            if (isset($_GET[$level])) {
                if ($_GET[$level] === 'true') {
                    return true;
                } else {
                    return false;
                }
            } else {
                // display it any way!
                return true;
            }
        }

        if ($_GET['getLog']) {
            if (isset($_GET['file']) && $_GET['file'] !== 'today') {
                $logFile = $logPath . $_GET['file'];
            }

            if (isset($_GET['clear']) && $_GET['clear'] === 'true') {
                $file = fopen($logFile, 'w');
                fwrite($file, '');
                fclose($file);
            }

            $log = file_get_contents($logFile);
            $logArray = explode(PHP_EOL, $log);

            $counter['notice'] = 0;
            $counter['error'] = 0;
            $counter['debug'] = 0;
            $counter['alert'] = 0;
            $counter['emergency'] = 0;
            $counter['critical'] = 0;
            $counter['warning'] = 0;
            $counter['info'] = 0;
            $counter['other'] = 0;
            $notificationsToasted = 0;

            echo "<ul>";
            foreach ($logArray as $line) {
                if (strpos($line, '<notice>')) {
                    $counter['notice']++;
                    if (display('notice')) {
                        echo "<li style='color:yellow'>{$line}</li>";
                    }
                    continue;
                } elseif (strpos($line, '<error>')) {
                    $counter['error']++;
                    if (display('error')) {
                        echo "<li style='color:lightcoral'>{$line}</li>";
                    }
                    continue;
                } elseif (strpos($line, '<debug>')) {
                    $counter['debug']++;
                    if (display('debug')) {
                        echo "<li style='color:lightskyblue'>{$line}</li>";
                    }
                    continue;
                } elseif (strpos($line, '<alert>')) {
                    $counter['alert']++;
                    if (display('alert')) {
                        echo "<li style='color:sandybrown'>{$line}</li>";
                    }
                    continue;
                } elseif (strpos($line, '<emergency>')) {
                    $counter['emergency']++;
                    if (display('error')) {
                        echo "<li style='color:red'><strong>{$line}</strong></li>";
                    }
                    continue;
                } elseif (strpos($line, '<critical>')) {
                    $counter['critical']++;
                    if (display('error')) {
                        echo "<li style='color:orange'><strong>{$line}</strong></li>";
                    }
                    continue;
                } elseif (strpos($line, '<warning>')) {
                    $counter['warning']++;
                    if (display('alert')) {
                        echo "<li style='color:khaki'>{$line}</li>";
                    }
                    continue;
                } elseif (strpos($line, '<info>')) {
                    $counter['info']++;
                    if (display('info')) {
                        echo "<li style='color:palegreen'>{$line}</li>";
                    }
                    continue;
                } else {
                    if ($line == '') {
                        continue;
                    }
                    $counter['other']++;
                    if (display('other')) {
                        echo "<li style='color:white'>{$line}</li>";
                    }
                    continue;
                }
            }
            echo "</ul>";
            echo "<div id='stats'>";
            echo ">_ \${$logFile}: ";
            echo "<span class=\"sample-box\" style='background-color:red'></span><span style=\"margin-left:3px; font-size:12px\"> Emergency [{$counter['emergency']}]</span>";
            echo "<span class=\"sample-box\" style='background-color:orange'></span><span style=\"margin-left:3px; font-size:12px\"> Critical [{$counter['critical']}]</span>";
            echo "<span class=\"sample-box\" style='background-color:lightcoral'></span><span style=\"margin-left:3px; font-size:12px\"> Error [{$counter['error']}]</span>";
            echo "<span class=\"sample-box\" style='background-color:sandybrown'></span><span style=\"margin-left:3px; font-size:12px\"> Alert [{$counter['alert']}]</span>";
            echo "<span class=\"sample-box\" style='background-color:khaki'></span><span style=\"margin-left:3px; font-size:12px\"> Warning [{$counter['warning']}]</span>";
            echo "<span class=\"sample-box\" style='background-color:yellow'></span><span style=\"margin-left:3px; font-size:12px\"> Notice [{$counter['notice']}]</span>";
            echo "<span class=\"sample-box\" style='background-color:palegreen'></span><span style=\"margin-left:3px; font-size:12px\"> Info [{$counter['info']}]</span>";
            echo "<span class=\"sample-box\" style='background-color:lightskyblue'></span><span style=\"margin-left:3px; font-size:12px\"> Debug [{$counter['debug']}]</span>";
            echo "<span class=\"sample-box\" style='background-color:white; border:solid 1px #000'></span><span style=\"margin-left:3px; font-size:12px\"> Other [{$counter['other']}]</span>";
            echo "</div>";
        } else {
            ?>
            <!DOCTYPE html>
            <html>
            <title>SNSO Log Viewer</title>
            <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.min.css">
            <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
            <link rel="shortcut icon" href="assets/img/logView.ico" type="image/x-icon">
            <style>
                @import url(https://fonts.googleapis.com/css?family=Ubuntu);

                body {
                    background-color: black;
                    color: <?php echo $textColor; ?>;
                    font-family: 'Ubuntu', sans-serif;
                    font-size: 16px;
                    line-height: 20px;

                }

                h4 {
                    font-size: 18px;
                    line-height: 22px;
                    color: #353535;
                }

                li:before {
                    font-family: 'FontAwesome';
                    content: '\f121';
                    display: inline-block;
                    margin-left: -2em;
                    width: 2em

                }

                li {
                    list-style: none;
                    padding: 5px;
                    border: dotted #fff 1px;
                    -o-transition: .5s;
                    -ms-transition: .5s;
                    -moz-transition: .5s;
                    -webkit-transition: .5s;
                    /* ...and now for the proper property */
                    transition: .5s;
                }

                li:hover {
                    background-color: #494949;

                }

                header {
                    position: fixed;
                    background-color: white;
                    z-index: 10;
                    color: black;
                    margin: 0;
                    left: 0;
                    right: 0;
                    top: 0;
                    min-height: 1em;
                    padding: 10px;
                    bottom: auto;
                    max-height: 3em;
                    overflow: hidden;
                    font-size: 14px;
                }

                #log {
                    position: relative;
                    top: 5em;
                    letter-spacing: 1px;
                    font-size: 12px;
                    padding-bottom: 3em;
                }

                #stats {
                    position: fixed;
                    background-color: lightgrey;
                    color: #000;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    height: 3em;
                    padding: 0 10px;
                    padding-top: 5px;

                }

                #scrollLock {
                    width: 2px;
                    height: 2px;
                    overflow: visible;
                }

                .sample-box {
                    background-color: black;
                    margin-left: 10px;
                    padding: 1px 3px
                }

                notice, error, emergency, critical, alert, debug {

                }
            </style>

            <body>
            <header>
                <input id="errors" type="checkbox" name="options" value="1" checked="checked">
                <label for="errors">Errors - Critical - Emergencies</label>

                <input id="alerts" type="checkbox" name="options" value="2" checked="checked">
                <label for="alerts">Alerts - Warnings</label>

                <input id="notices" type="checkbox" name="options" value="3" checked="checked">
                <label for="notices">Notices</label>

                <input id="information" type="checkbox" name="options" value="4" checked="checked">
                <label for="information">Information</label>

                <input id="debugs" type="checkbox" name="options" value="5" checked="checked">
                <label for="debugs">Debugs</label>

                <input id="other" type="checkbox" name="options" value="6" checked="checked">
                <label for="other">Others</label>

                <input class="disableScrollLock btn btn-info btn-xs" type="button" value="Disable Auto Scroll"/>
                <input class="enableScrollLock btn btn-warning btn-xs" style="display: none;" type="button"
                       value="Enable Auto Scroll"/>

                <select id="file">
                    <option value="today"> -- Today --</option>
                    <?php

                    foreach (new DirectoryIterator($logPath) as $fileInfo) {
                        if ($fileInfo->isDot()) {
                            continue;
                        }
                        if ($fileInfo->getFilename() == '.htaccess') {
                            continue;
                        }
                        if ($fileInfo->getFilename() == date("dmY") . '.log') {
                            continue;
                        }
                        $fileName = str_replace('.log', '', $fileInfo->getFilename());
                        $dateLabel = $fileName[0] . $fileName[1] . '-' . $fileName[2] . $fileName[3] . '-' . $fileName[4] . $fileName[5] . $fileName[6] . $fileName[7];
                        echo "<option value='{$fileInfo->getFilename()}'>" .
                            $dateLabel
                            . "</option>
                    ";
                    }
                    ?>
                </select>
                <button class="btn btn-danger btn-xs" id="clear">Clear</button>
                <a class="btn btn-default btn-xs" href="index.php?adm/P001/index/show/0/ar/menu">Back</a>
            </header>
            <div id="log">

            </div>
            <div id="scrollLock"></div>
            <script src="https://code.jquery.com/jquery-2.2.2.min.js" type="text/javascript"></script>
            <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"
                    type="text/javascript"></script>
            <script>
                setInterval(readLogFile, <?php echo $interval; ?>);
                window.onload = readLogFile;

                var pathname = window.location.pathname + '?adm/P001/logviewer/show/0/ar/menu';
                var scrollLock = true;
                var errors, alerts, notices, info, debugs, others, file;
                var clear = 'false';

                if (typeof(Storage) !== "undefined") {
                    // check if item is set then retrieve it or set Defaults
                    if (localStorage.getItem("errors") === null) {
                        localStorage.setItem("errors", true);
                    } else {
                        if (localStorage.getItem('errors') !== 'true') {
                            $('#errors').removeAttr('checked');
                        }
                    }
                    if (localStorage.getItem("alerts") === null) {
                        localStorage.setItem("alerts", true);
                    } else {
                        if (localStorage.getItem('alerts') !== 'true') {
                            $('#alerts').removeAttr('checked');
                        }
                    }

                    if (localStorage.getItem("notices") === null) {
                        localStorage.setItem("notices", true);
                    } else {
                        if (localStorage.getItem('notices') !== 'true') {
                            $('#notices').removeAttr('checked');
                        }
                    }

                    if (localStorage.getItem("info") === null) {
                        localStorage.setItem("info", true);
                    } else {
                        if (localStorage.getItem('info') !== 'true') {
                            $('#information').removeAttr('checked');
                        }
                    }

                    if (localStorage.getItem("debugs") === null) {
                        localStorage.setItem("debugs", true);
                    } else {
                        if (localStorage.getItem('debugs') !== 'true') {
                            $('#debugs').removeAttr('checked');
                        }
                    }

                    if (localStorage.getItem("others") === null) {
                        localStorage.setItem("others", true);
                    } else {
                        if (localStorage.getItem('others') !== 'true') {
                            $('#other').removeAttr('checked');
                        }
                    }
                }

                $(document).ready(function () {
                    $('.disableScrollLock').click(function () {
                        $("html,body").clearQueue();
                        $(".disableScrollLock").hide();
                        $(".enableScrollLock").show();
                        scrollLock = false;
                    });
                    $('.enableScrollLock').click(function () {
                        $("html,body").clearQueue();
                        $(".enableScrollLock").hide();
                        $(".disableScrollLock").show();
                        scrollLock = true;
                    });
                    $("#errors").change(function () {
                        localStorage.setItem('errors', $('#errors').prop('checked'));
                        readLogFile();
                    });
                    $("#alerts").change(function () {
                        localStorage.setItem('alerts', $('#alerts').prop('checked'));
                        readLogFile();
                    });
                    $("#notices").change(function () {
                        localStorage.setItem('notices', $('#notices').prop('checked'));
                        readLogFile();
                    });
                    $("#information").change(function () {
                        localStorage.setItem('info', $('#information').prop('checked'));
                        readLogFile();
                    });
                    $("#debugs").change(function () {
                        localStorage.setItem('debugs', $('#debugs').prop('checked'));
                        readLogFile();
                    });
                    $("#other").change(function () {
                        localStorage.setItem('others', $('#other').prop('checked'));
                        readLogFile();
                    });
                    $('#file').change(function () {
                        readLogFile();
                    });
                    $('#clear').click(function () {
                        if (confirm('Really???')) {
                            clear = 'true';
                            readLogFile();
                        }
                    });
                });

                function readLogFile() {

                    errors = $('#errors').prop('checked');
                    alerts = $('#alerts').prop('checked');
                    notices = $('#notices').prop('checked');
                    info = $('#information').prop('checked');
                    debugs = $('#debugs').prop('checked');
                    others = $('#other').prop('checked');
                    file = $('#file').val();

                    $.get(pathname, {
                        getLog: "true",
                        error: errors,
                        alert: alerts,
                        notice: notices,
                        info: info,
                        debug: debugs,
                        other: others,
                        file: file,
                        clear: clear
                    }, function (data) {
                        data = data.replace(new RegExp("\n", "g"), "<br />");
                        $("#log").html(data);

                        if (scrollLock == true) {
                            $('html,body').animate({scrollTop: $("#scrollLock").offset().top}, <?php echo 1000; ?>)
                        }
                    });
                    clear = 'false';
                }
            </script>
            </body>
            </html>
        <?php }
    }
}