<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_generalsetting extends Controller
{
    public function show($parm, $post)
    {

        switch ($parm[0]) {

            case 'update':

                if($_SESSION['s_generalsetting_token'] == $parm[1]){

                    try{

                        $setting = ClientConfiguration::read([
                            ClientConfiguration::ORG_ID => $_SESSION['organization']->id,
                            ClientConfiguration::OPR_ID => 136])[0];

                        $setting->ids = implode(",", $post['st_orgsetting_ids']);
                        $setting->save();

                        Notification::updatedAlert();

                    }catch (ClientConfigurationException $e){

                        if($e->getCode() === 202020){

                            $setting = new ClientConfiguration();
                            $setting->org_id = $_SESSION['organization']->id;
                            $setting->opr_id = 136;
                            $setting->ids = implode(",", $post['st_orgsetting_ids']);
                            $setting->created_by = $_SESSION['user']->id;
                            $setting->save();

                        }

                    }

                }

            break;

        }

        try{

            $this->Smarty->assign('OrganizationConfiguration',array_shift(ClientConfiguration::read([
                ClientConfiguration::ORG_ID => $_SESSION['organization']->id,
                ClientConfiguration::OPR_ID => 136])));

        }catch (ClientConfigurationException $e){
            $this->Smarty->assign('OrganizationConfiguration',[]);
        }

        $this->Smarty->assign("MediaCenterTypes",Setting::getList(136));

        $_SESSION['s_generalsetting_token'] = Helper::generateToken();

    }

    public function edit($parm, $post)
    {

        try{

            $this->Smarty->assign('OrganizationConfiguration',array_shift(ClientConfiguration::read([
                ClientConfiguration::ORG_ID => $_SESSION['organization']->id,
                ClientConfiguration::OPR_ID => 136])));

        }catch (ClientConfigurationException $e){

        }

        $this->Smarty->assign("MediaCenterTypes",Setting::getList(136));
        $_SESSION['s_generalsetting_token'] = Helper::generateToken();

    }
}