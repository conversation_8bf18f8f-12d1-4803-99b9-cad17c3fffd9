<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_clientConfiguration extends Controller
{
    public function show($parm, $post)
    {

        switch ($parm[0]) {
            case 'update':

                if ($_SESSION['s_clientConfiguration_token'] === $parm[1]) {

                    try {

                        $configurations = new ConfigurationParser(CLIENT_CONFIG);
                        foreach ($post as $section => $variables) {
                            $configurations->setSection($section, $variables);
                            $configurations->save();
                        }

                        Notification::updatedAlert();

                    } catch (ConfigurationParserException $e) {
                    }
                }
                break;
        }

        try {

            $configurations = (new ConfigurationParser(CLIENT_CONFIG))->getVariables();
            $this->Smarty->assign('configurations', $configurations);

        } catch (ConfigurationParserException $e) {

            $this->Smarty->assign('configurations', []);

        }

        $_SESSION['s_clientConfiguration_token'] = Helper::generateToken();
    }

    public function edit($parm, $post)
    {

        try {

            $configurations = (new ConfigurationParser(CLIENT_CONFIG))->getVariables();
            $this->Smarty->assign('configurations', $configurations);

        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('configurations', []);
        }

    }
}