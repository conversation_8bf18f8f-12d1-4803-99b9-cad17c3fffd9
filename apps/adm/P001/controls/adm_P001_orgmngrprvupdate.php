<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_orgmngrprvupdate extends Controller
{
    public function show($parm, $post)
    {

        switch ($parm[0]){

            case Operation::UPDATE:

                if ($_SESSION['s_orgmngrprvupdate_token'] === $parm[1]) {

                    Privilege::updateManagersPrivileges($_SESSION['organization']);
                    ( new SnsoMainMenu($_SESSION['organization'], $_SESSION['user']) )->setMainMenuSession();

                    Notification::updatedAlert();

                }

                break;
        }

        try{
            $this->Smarty->assign('programs', Program::getClientPrograms('all',[58],[Program::PROGRAM_CLIENT_MANAGER_P056]));
        }catch (ProgramException $e){}

        $_SESSION['s_orgmngrprvupdate_token'] = Helper::generateToken();
    }
}
