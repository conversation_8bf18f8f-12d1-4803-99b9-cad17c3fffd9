<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_socialmedia extends Controller
{
    public function show($parm, $post)
    {

        //DropDownList::getDropDownStructure();

        switch ($parm[0]){

            case Operation::INSERT:

               if($_SESSION['s_SocialMedia_token'] == $parm[1]){

                    try{

                        $sm=new SocialMedia();
                        $sm->bindProperties($post);
                        $sm->save();

                        Notification::createdAlert();

                    }catch (SocialMediaException $e){

                        if($e->getMessage() == "CodeExist"){

                            Notification::alertMessage(Notification::WARNING,Translation::translate(Program::readID(Program::PROGRAM_SUPPORT_MANAGER_P001),'CodeExist'));

                        }

                    }

                }

                break;

            case Operation::UPDATE:

                if($_SESSION['s_SocialMedia_token'] == $parm[1]){

                    try{

                        $sm = SocialMedia::readID((int) $parm[2] ?? 0);
                        $sm->bindProperties($post);
                        $sm->update();

                        Notification::updatedAlert();

                    }catch (SocialMediaException $e){

                        if($e->getMessage() == "CodeExist"){

                            Notification::alertMessage(Notification::WARNING,Translation::translate(Program::readID(Program::PROGRAM_SUPPORT_MANAGER_P001),'CodeExist'));

                        }

                    }

                }

                break;

            case Operation::DELETE:

                if($_SESSION['s_SocialMedia_token'] == $parm[1]){

                    try{

                        $sm = SocialMedia::readID((int) $parm[2] ?? 0);
                        $sm->delete();

                        Notification::deletedAlert();

                    }catch (SocialMediaException $e){}

                }

                break;
        }

        try{
            $this->Smarty->assign('SocialMedia', SocialMedia::read());
        }catch (SocialMediaException $e){
            $this->Smarty->assign('SocialMedia',[]);
        }

        $_SESSION['s_SocialMedia_token'] = md5(rand(0000,9999));

    }

    public function add($parm, $post)
    {
        $_SESSION['s_SocialMedia_token'] = md5(rand(0000,9999));
    }

    public function edit($parm, $post)
    {
        try{

            $this->Smarty->assign('SocialMedia', SocialMedia::readID((int) $parm[0] ?? 0));
        }catch (SocialMediaException $e){
            $this->Smarty->assign('SocialMedia',[]);
        }

        $_SESSION['s_SocialMedia_token'] = md5(rand(0000,9999));

    }

    public function confirm($parm, $post)
    {
        try{
            $this->Smarty->assign('SocialMedia', SocialMedia::readID((int) $parm[0] ?? 0));
        }catch (SocialMediaException $e){
            $this->Smarty->assign('SocialMedia',[]);
        }

        $_SESSION['s_SocialMedia_token'] = md5(rand(0000,9999));

    }
}
