<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_bnd extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]){

            case 'insert':

                if($_SESSION['s_bnd_token'] === $parm[1]){

                    try{

                        $bundle = new Bundle();
                        $bundle->bindProperties($post);
                        $bundle->save();

                        Notification::createdAlert();

                    }catch (BundleException $e){

                    }
                }

                break;

            case 'update':

                if($_SESSION['s_bnd_token'] === $parm[1]){

                    try{

                        $bundle = Bundle::readID((int) $parm[2] ?? 0);
                        $bundle->bindProperties($post);
                        $bundle->save();

                        Notification::updatedAlert();

                    }catch (BundleException $e){

                    }

                }

                break;

            case 'delete':

                if($_SESSION['s_bnd_token'] === $parm[1]){

                    try{

                        $bundle = Bundle::readID((int) $parm[2] ?? 0);
                        $bundle->delete();

                        Notification::deletedAlert();

                    }catch (BundleException $e){

                    }

                }

                break;
        }

        try{

            $this->Smarty->assign('bundles', Bundle::read());

        }catch (BundleException $e){
            $this->Smarty->assign('bundles', []);
        }

        $_SESSION['s_bnd_token'] = md5(rand(0000,9999));

    }

    public function add($parm, $post)
    {
        $_SESSION['s_bnd_token'] = md5(rand(0000,9999));
    }

    public function edit($parm, $post)
    {

        try{
            $this->Smarty->assign('bundle', Bundle::readID((int) $parm[0] ?? 0));
        }catch (BundleException $e){
            $this->Smarty->assign('bundle', []);
        }

        $_SESSION['s_bnd_token'] = md5(rand(0000,9999));

    }

    public function confirm($parm, $post)
    {

        try{
            $this->Smarty->assign('bundle', Bundle::readID((int) $parm[0] ?? 0));
        }catch (BundleException $e){
            $this->Smarty->assign('bundle', []);
        }

        $_SESSION['s_bnd_token'] = md5(rand(0000,9999));

    }
}
