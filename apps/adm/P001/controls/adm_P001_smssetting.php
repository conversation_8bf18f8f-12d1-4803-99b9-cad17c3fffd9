<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_smssetting extends Controller
{

    public function show($parm, $post, $files)
    {

        // Insert new sms company data
        if ($parm[0] == 'insert' && $_SESSION['s_smssetting_rand_num'] === $parm[1]) {

            // check for sms_company_setting_elements at least one element needed
            if ($post['sms_company_setting_elements'] != null) {

                // generate random number to prevent add multiple item while refreshing page
                $_SESSION['smssetting_rand_num'] = md5(rand(0, 10000000));

                $sms_company_data_array = array();
                $sms_company_data_array['sms_company_name'] = $post['sms_company_name'];
                $sms_company_data_array['sms_company_country'] = $post['sms_company_country'];
                $sms_company_data_array['sms_company_region'] = $post['sms_company_region'];
                $sms_company_data_array['sms_company_city'] = $post['sms_company_city'];
                $sms_company_data_array['sms_company_coverage'] = $post['sms_company_coverage'];
                $sms_company_data_array['sms_company_address'] = $post['sms_company_address'];
                $sms_company_data_array['sms_company_phone_number'] = $post['sms_company_phone_number'];
                $sms_company_data_array['sms_company_website'] = $post['sms_company_website'];
                $sms_company_data_array['sms_company_email'] = $post['sms_company_email'];
                $sms_company_data_array['sms_company_created_by'] = $_SESSION['user']->id;
                $sms_company_data_array['sms_company_setting_elements'] = json_encode($post['sms_company_setting_elements']);

                $this->DB->insert('sms_company', $sms_company_data_array);

            } else {
                $this->Smarty->assign('error_message',
                    'لم تتم إضافة بيانات الشركة ، لابد من اختيار عنصر واحد للاعدادات على الأقل');
            }

        }

        // Update sms company data
        if ($parm[0] == 'update') {

            $sms_company_id = $parm[1];

            // make sure that sms_company_id is passed
            if ($sms_company_id != null) {

                // check for sms_company_setting_elements at least one element needed
                if ($post['sms_company_setting_elements'] != null) {
                    $sms_company_data_array['sms_company_name'] = $post['sms_company_name'];
                    $sms_company_data_array['sms_company_country'] = $post['sms_company_country'];
                    $sms_company_data_array['sms_company_region'] = $post['sms_company_region'];
                    $sms_company_data_array['sms_company_city'] = $post['sms_company_city'];
                    $sms_company_data_array['sms_company_coverage'] = $post['sms_company_coverage'];
                    $sms_company_data_array['sms_company_address'] = $post['sms_company_address'];
                    $sms_company_data_array['sms_company_phone_number'] = $post['sms_company_phone_number'];
                    $sms_company_data_array['sms_company_website'] = $post['sms_company_website'];
                    $sms_company_data_array['sms_company_email'] = $post['sms_company_email'];
                    $sms_company_data_array['sms_company_setting_elements'] = json_encode($post['sms_company_setting_elements']);

                    $this->DB->update('sms_company', $sms_company_data_array, $sms_company_id);
                } else {
                    $this->Smarty->assign('error_message',
                        'لم يتم تعديل بيانات الشركة ، لابد من اختيار عنصر واحد للاعدادات على الأقل');
                }

            } else {
                //TODO:// log this operation
            }

        }

        // Delete sms company data
        if ($parm[0] == 'delete') {

            $sms_company_id = $parm[1];

            if ($sms_company_id != null) {
                $this->DB->delete('sms_company', $sms_company_id);
            } else {
                //TODO:// log this operation
            }

        }

        // retrieve sms companies list
        $sms_company_list = $this->DB->get_all('sms_company', 'order by BINARY sms_company_name');

        // disassemble sms_company_list array to inject country, region & city names along with their ids
        $i = 0;
        $array = array();
        foreach ($sms_company_list as $sms_company) {
            for ($j = count($sms_company_list) - 1; $j < count($sms_company_list); $j++) {

                $array[$i]['sms_company_id'] = $sms_company['sms_company_id'];

                $array[$i]['sms_company_name'] = $sms_company['sms_company_name'];

                // get country name using its id
                $array[$i]['sms_company_country'] = $sms_company['sms_company_country'];
                $country_row = $this->DB->find_with_id('st_country', $sms_company['sms_company_country']);
                $sms_company_country_name = $country_row['st_country_name'];
                $array[$i]['sms_company_country_name'] = $sms_company_country_name;

                // get region name using its id
                $array[$i]['sms_company_region'] = $sms_company['sms_company_region'];
                $region_row = $this->DB->find_with_id('st_region', $sms_company['sms_company_region']);
                $sms_company_region_name = $region_row['st_region_name'];
                $array[$i]['sms_company_region_name'] = $sms_company_region_name;

                // get city name using its id
                $array[$i]['sms_company_city'] = $sms_company['sms_company_city'];
                $city_row = $this->DB->find_with_id('st_city', $sms_company['sms_company_city']);
                $sms_company_city_name = $city_row['st_city_name'];
                $array[$i]['sms_company_city_name'] = $sms_company_city_name;

                if ($sms_company['sms_company_coverage'] === 'local') {
                    $array[$i]['sms_company_coverage'] = 'محلي';
                } elseif ($sms_company['sms_company_coverage'] === 'regional') {
                    $array[$i]['sms_company_coverage'] = 'إقليمي';
                } elseif ($sms_company['sms_company_coverage'] === 'global') {
                    $array[$i]['sms_company_coverage'] = 'دولي';
                }

                $array[$i]['sms_company_address'] = $sms_company['sms_company_address'];
                $array[$i]['sms_company_phone_number'] = $sms_company['sms_company_phone_number'];
                $array[$i]['sms_company_website'] = $sms_company['sms_company_website'];
                $array[$i]['sms_company_email'] = $sms_company['sms_company_email'];

                $array[$i]['sms_company_created_by'] = $sms_company['sms_company_created_by'];
                $array[$i]['sms_company_created_date'] = $sms_company['sms_company_created_date'];

                $i++;
            }
        }

        $this->Smarty->assign('sms_company_list', $array);

    }

    public function add($parm, $post, $files)
    {

        // generate random number to prevent add multiple item while refreshing page
        $_SESSION['s_smssetting_rand_num'] = md5(rand(0, 10000000));

        $country_list = $this->DB->get_all('st_country', 'order by BINARY st_country_name');

        $country_selector = "";
        $country_selector .= '<select name="sms_company_country" id="sms_company_country" required>' . "\n";
        $country_selector .= '<option value="" selected></option>';
        foreach ($country_list as $country) {
            $country_selector .= "\t" . '<option value="' . $country['st_country_id'] . '">' . $country['st_country_name'] . '</option>' . "\n";
        }
        $country_selector .= '</select>';


        $coverage_selector = "";
        $coverage_selector .= '<select name="sms_company_coverage" id="sms_company_coverage" required>' . "\n";
        $coverage_selector .= "\t" . '<option value="local">محلي</option>' . "\n";
        $coverage_selector .= "\t" . '<option value="regional">إقليمي</option>' . "\n";
        $coverage_selector .= "\t" . '<option value="global">دولي</option>' . "\n";
        $coverage_selector .= '</select>';

        $this->Smarty->assign('country_selector', $country_selector);
        $this->Smarty->assign('coverage_selector', $coverage_selector);

    }

    public function edit($parm, $post, $files)
    {

        if ($parm[0] != null) {

            $sms_company_id = $parm[0];
            $sms_company_data = $this->DB->find_with_id('sms_company', $sms_company_id);

            $stored_country_id = $sms_company_data['sms_company_country'];
            $country_list = $this->DB->get_all('st_country', 'order by BINARY st_country_name');
            $country_selector = "";
            $country_selector .= '<select name="sms_company_country" id="sms_company_country" required>' . "\n";
            foreach ($country_list as $country) {
                if ($country['st_country_id'] == $stored_country_id) {
                    $country_selector .= "\t" . '<option value="' . $country['st_country_id'] . '" selected>' . $country['st_country_name'] . '</option>' . "\n";
                } else {
                    $country_selector .= "\t" . '<option value="' . $country['st_country_id'] . '">' . $country['st_country_name'] . '</option>' . "\n";
                }
            }
            $country_selector .= '</select>';

            $stored_region_id = $sms_company_data['sms_company_region'];
            $region_list = $this->DB->get_all('st_region', 'order by BINARY st_region_name');
            $region_selector = "";
            $region_selector .= '<select name="sms_company_region" id="sms_company_region" required>' . "\n";
            foreach ($region_list as $region) {
                if ($region['st_region_id'] == $stored_region_id) {
                    $region_selector .= "\t" . '<option value="' . $region['st_region_id'] . '" selected>' . $region['st_region_name'] . '</option>' . "\n";
                } else {
                    $region_selector .= "\t" . '<option value="' . $region['st_region_id'] . '">' . $region['st_region_name'] . '</option>' . "\n";
                }
            }
            $region_selector .= '</select>';

            $stored_city_id = $sms_company_data['sms_company_city'];
            $city_list = $this->DB->get_all('st_city', 'order by BINARY st_city_name');
            $city_selector = "";
            $city_selector .= '<select name="sms_company_city" id="sms_company_city" required>' . "\n";
            foreach ($city_list as $city) {
                if ($city['st_city_id'] == $stored_city_id) {
                    $city_selector .= "\t" . '<option value="' . $city['st_city_id'] . '" selected>' . $city['st_city_name'] . '</option>' . "\n";
                } else {
                    $city_selector .= "\t" . '<option value="' . $city['st_city_id'] . '">' . $city['st_city_name'] . '</option>' . "\n";
                }
            }
            $city_selector .= '</select>';

            $stored_city_coverage = $sms_company_data['sms_company_coverage'];
            $coverage_selector = "";
            $coverage_selector .= '<select name="sms_company_coverage" id="sms_company_coverage" required>' . "\n";
            if ($stored_city_coverage == 'local') {
                $coverage_selector .= "\t" . '<option value="local" selected>محلي</option>' . "\n";
                $coverage_selector .= "\t" . '<option value="regional">إقليمي</option>' . "\n";
                $coverage_selector .= "\t" . '<option value="global">دولي</option>' . "\n";
            } elseif ($stored_city_coverage == 'regional') {
                $coverage_selector .= "\t" . '<option value="local">محلي</option>' . "\n";
                $coverage_selector .= "\t" . '<option value="regional" selected>إقليمي</option>' . "\n";
                $coverage_selector .= "\t" . '<option value="global">دولي</option>' . "\n";
            } elseif ($stored_city_coverage == 'global') {
                $coverage_selector .= "\t" . '<option value="local">محلي</option>' . "\n";
                $coverage_selector .= "\t" . '<option value="regional">إقليمي</option>' . "\n";
                $coverage_selector .= "\t" . '<option value="global" selected>دولي</option>' . "\n";
            }
            $coverage_selector .= '</select>';

            $sms_company_setting_elements = $sms_company_data['sms_company_setting_elements'];
            $sms_company_setting_elements_array = json_decode($sms_company_setting_elements);
            $setting_elements_selector = "";

            $setting_elements_selector .= '<div class="checkbox"><label>';
            if (in_array('api_key', $sms_company_setting_elements_array)) {
                $setting_elements_selector .= '<input type="checkbox" name="sms_company_setting_elements[]" value="api_key" checked>';

            } else {
                $setting_elements_selector .= '<input type="checkbox" name="sms_company_setting_elements[]" value="api_key">';
            }
            $setting_elements_selector .= '<span class="text">&nbsp;API Key</span></label></div>';

            $setting_elements_selector .= '<div class="checkbox"><label>';
            if (in_array('sender_id', $sms_company_setting_elements_array)) {
                $setting_elements_selector .= '<input type="checkbox" name="sms_company_setting_elements[]" value="sender_id" checked>';
            } else {
                $setting_elements_selector .= '<input type="checkbox" name="sms_company_setting_elements[]" value="sender_id">';
            }
            $setting_elements_selector .= '<span class="text">&nbsp;إسم المرسل</span></label></div>';

            $setting_elements_selector .= '<div class="checkbox"><label>';
            if (in_array('username', $sms_company_setting_elements_array)) {
                $setting_elements_selector .= '<input type="checkbox" name="sms_company_setting_elements[]" value="username" checked>';
            } else {
                $setting_elements_selector .= '<input type="checkbox" name="sms_company_setting_elements[]" value="username">';
            }
            $setting_elements_selector .= '<span class="text">&nbsp;إسم المستخدم</span></label></div>';

            $setting_elements_selector .= '<div class="checkbox"><label>';
            if (in_array('password', $sms_company_setting_elements_array)) {
                $setting_elements_selector .= '<input type="checkbox" name="sms_company_setting_elements[]" value="password" checked>';
            } else {
                $setting_elements_selector .= '<input type="checkbox" name="sms_company_setting_elements[]" value="password">';
            }
            $setting_elements_selector .= '<span class="text">&nbsp;كلمة المرور</span></label></div>';

            $this->Smarty->assign('sms_company_data', $sms_company_data);
            $this->Smarty->assign('country_selector', $country_selector);
            $this->Smarty->assign('region_selector', $region_selector);
            $this->Smarty->assign('city_selector', $city_selector);
            $this->Smarty->assign('coverage_selector', $coverage_selector);
            $this->Smarty->assign('setting_elements_selector', $setting_elements_selector);

        } else {
            //TODO: log this operation
        }

    }

    public function confirm($parm, $post, $files)
    {

        if ($parm[0] != null) {

            $sms_company_id = $parm[0];
            $sms_company_data = $this->DB->find_with_id('sms_company', $sms_company_id);
            $this->Smarty->assign('sms_company_data', $sms_company_data);

        } else {
            //TODO: log this operation
        }


    }

}
