<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_supportDashboard extends Controller
{

    public function show($parm, $post)
    {

        switch ($parm[0]) {

            case 'changeDataAndLanguageSetting':

                try {

                    global $snso;
                    User::changeLanguageAndDateSetting($post);
                    ( new SnsoMainMenu($_SESSION['organization'], $_SESSION['user']) )->setMainMenuSession();
                    $snso->URL->redirectToSupportDashboard();

                } catch (UserException $e) {

                }

                break;

            case 'updateCoreEntities':
                Database::coreEntitiesSeed();
                break;

        }

        $_SESSION['s_supportDashboard_token'] = md5(rand(0000, 9999));
    }

    public function confirm($parm,$post)
    {

    }

}