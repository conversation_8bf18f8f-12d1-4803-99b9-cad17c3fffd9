<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_stsetting extends Controller
{

    public function show($parm, $post)
    {

        switch ($parm[0]){

            case Operation::SAVE_SESSION:
                $_SESSION['s_setting_opr_id'] = (int) $parm[1] ?? 0;
                break;

            case Operation::INSERT:

                if($_SESSION['s_setting_token'] == $parm[1]){

                    try{

                        $setting = new Setting();
                        $setting->bindProperties($post);
                        $setting->opr_id = $_SESSION['s_setting_opr_id'];
                        $setting->code = Helper::refineCode($post['code']);
                        $setting->save();

                        Notification::createdAlert();

                    }catch (SettingException $e){
                        if($e->getMessage() == "CodeExist"){
                            $this->Smarty->assign('ErrorMessage','CodeExist');
                        }
                    }

                }

                break;

            case Operation::UPDATE:

                if($_SESSION['s_setting_token'] == $parm[1]){

                    try{

                        $setting = Setting::readID((int) $parm[2] ?? 0);
                        $setting->bindProperties($post);
                        $setting->code = Helper::refineCode($post['code']);
                        $setting->update();

                        Notification::updatedAlert();

                    }catch (SettingException $e){
                        if($e->getMessage() == "CodeExist"){
                            $this->Smarty->assign('ErrorMessage','CodeExist');
                        }
                    }
                }

                break;

            case Operation::DELETE:

                if($_SESSION['s_setting_token'] == $parm[1]){

                    try{

                        $setting = Setting::readID((int) $parm[2] ?? 0);
                        $setting->delete();

                        Notification::deletedAlert();

                    }catch (SettingException $e){}

                }

                break;
        }

        try{
            $this->Smarty->assign('settings', Setting::getList($_SESSION['s_setting_opr_id']));
        }catch (SettingException $e){
            $this->Smarty->assign('settings',[]);
        }

        try{
            $this->Smarty->assign('dropdown',DropDownList::readID((int) $_SESSION['s_setting_opr_id'] ?? 0));
        }catch (DropDownListException $e){
            $this->Smarty->assign('dropdown',[]);
        }

        $_SESSION['s_setting_token'] = md5(rand(0000,9999));

    }

    public function add($parm, $post)
    {
        $_SESSION['s_setting_token'] = md5(rand(0000,9999));
    }

    public function edit($parm, $post)
    {

        try{
            $this->Smarty->assign('setting', Setting::readID((int) $parm[0] ?? 0));
        }catch (SettingException $e){
            $this->Smarty->assign('setting',[]);
        }

        $_SESSION['s_setting_token'] = md5(rand(0000,9999));

    }

    public function confirm($parm, $post)
    {
        try{
            $this->Smarty->assign('setting', Setting::readID((int) $parm[0] ?? 0));
        }catch (SettingException $e){
            $this->Smarty->assign('setting',[]);
        }

        $_SESSION['s_setting_token'] = md5(rand(0000,9999));

    }
}