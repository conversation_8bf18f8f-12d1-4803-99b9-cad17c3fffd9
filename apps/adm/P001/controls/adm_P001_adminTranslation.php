<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_adminTranslation extends Controller
{

    public function show($parm, $post)
    {

        switch ($parm[0]) {

            case Operation::INSERT:

                if ($_SESSION['s_translation_token'] === $parm[1]) {

                    try {

                        $translation = new Translation();
                        $translation->bindProperties($post);
                        $translation->save();

                        Notification::createdAlert();

                    } catch (LanguageException $e) {

                    }

                }

                break;

            case Operation::UPDATE:

                if ($_SESSION['s_translation_token'] === $parm[1]) {

                    try {

                        $translation = Translation::readID((int)$parm[2] ?? 0);
                        $translation->bindProperties($post);
                        $translation->save();

                        Notification::updatedAlert();

                    } catch (LanguageException $e) {

                    }

                }

                break;

            case Operation::DELETE:

                if ($_SESSION['s_translation_token'] === $parm[1]) {

                    try {

                        $translation = Translation::readID((int)$parm[2] ?? 0);
                        $translation->delete();

                        Notification::deletedAlert();

                    } catch (LanguageException $e) {

                    }

                }

                break;

        }

        Translation::updateTranslationList();

        try {
            $this->Smarty->assign('translations', Translation::read([]));
        } catch (TranslationException $e) {
            $this->Smarty->assign('translations', []);
        }

        $_SESSION['s_translation_token'] = Helper::generateToken();
    }

    public function dashboard($parm, $post, $file)
    {

        switch ($parm[0]){

            case 'menu':

                $session = new SessionManager(md5(rand(0000, 9999)));
                $session->set('translation_id', $parm[1]);
                $session->set('language_code', $parm[2]);
                $session->updateDataManipulationToken();
                $session->save();

                break;

            case 'setSession':

                $session = new SessionManager($parm[1]);

                break;
        }

        switch ($parm[2]) {

            case Operation::IMPORT:

                if ($session->getDataManipulationToken() === $parm[3]) {

                    try {

                        Translation::importTranslation($file);
                        Notification::updatedAlert();

                    } catch (TranslationException $e) {
                    }

                }

                break;

        }

        try {
            $this->Smarty->assign('translation', Translation::readID((int)$session->get('translation_id') ?? 0));
        } catch (TranslationException $e) {
            $this->Smarty->assign('translation', []);
        }

        try {
            $this->Smarty->assign('programs', Program::getAdminPrograms());
        } catch (ProgramException $e) {
            $this->Smarty->assign('programs', []);
        }

        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);

    }

    public function additionalTranslation($parm, $post)
    {

        $session = new SessionManager($parm[0]);

        switch ($parm[1]) {

            case Operation::UPDATE:

                if ($session->getDataManipulationToken() === $parm[2]) {

                    try {
                        Translation::additionalTranslationUpdate($post, Translation::readID((int)$session->get('translation_id') ?? 0));
                        Notification::updatedAlert();
                    } catch (TranslationException $e) {

                    }
                }

                break;

            case 'updateDetails':

                if ($session->getDataManipulationToken() === $parm[2]) {

                    try {
                        Translation::additionalDetailsTranslationUpdate($post, Translation::readID((int)$session->get('translation_id') ?? 0));
                        Notification::updatedAlert();
                    } catch (TranslationException $e) {

                    }
                }

                break;

        }

        try {
            $translation = Translation::readID((int)$session->get('translation_id') ?? 0);
            $this->Smarty->assign('translation', $translation);
        } catch (TranslationException $e) {
            $translation = [];
            $this->Smarty->assign('translation', []);
        }

        try {
            $this->Smarty->assign('additionalLists', Translation::getAdditionalTranslationViewArray($translation,$session->get('language_code')));
        } catch (SettingException $e) {
            $this->Smarty->assign('additionalLists', []);
        }

        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function additionalDetailsTranslation($parm, $post)
    {

        $session = new SessionManager($parm[0]);

        $translation = [];
        try {
            $translation = Translation::readID((int)$session->get('translation_id') ?? 0);
            $this->Smarty->assign('translation', $translation);
        } catch (TranslationException $e) {
            $this->Smarty->assign('translation', []);
        }

        try {
            $this->Smarty->assign('AdditionalTypeEntityName',$parm[1]);
            $this->Smarty->assign('AdditionalTypeLabel',(((new ConfigurationParser())->read(ADDITIONAL_TRANSLATIONS_DIR . DS . $_SESSION['lang'] . '.conf'))->get(Translation::ADDITIONAL_TRANSLATION))[$parm[1]]);
        } catch (ConfigurationParserException $e) {
            $this->Smarty->assign('AdditionalTypeLabel', []);
            $this->Smarty->assign('AdditionalTypeEntityName', []);
        }

        try {
            $this->Smarty->assign('details', Translation::getAdditionalDetailsTranslationViewArray($session->get('language_code'),$translation,$parm[1]));
        } catch (SettingException $e) {
            $this->Smarty->assign('details', []);
        }

        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function dropDownTranslation($parm, $post)
    {

        $session = new SessionManager($parm[0]);

        switch ($parm[1]) {

            case Operation::UPDATE:

                if ($session->getDataManipulationToken() === $parm[2]) {

                    try {
                        Translation::dropDownsTranslationUpdate($post, Translation::readID((int)$session->get('translation_id') ?? 0));
                        Notification::updatedAlert();
                    } catch (TranslationException $e) {

                    }
                }

                break;

            case 'updateSetting':

                if ($session->getDataManipulationToken() === $parm[2]) {

                    try {
                        Translation::settingsTranslationUpdate($post, Translation::readID((int)$session->get('translation_id') ?? 0));
                        Notification::updatedAlert();
                    } catch (TranslationException $e) {

                    }
                }

                break;
        }

        try {

            $translation = Translation::readID((int)$session->get('translation_id') ?? 0);
            $this->Smarty->assign('translation', $translation);

        } catch (TranslationException $e) {

            $translation = [];
            $this->Smarty->assign('translation', []);

        }

        try {
            $this->Smarty->assign('dropDownLists', Translation::getDropDownsTranslationViewArray($translation));
        } catch (SettingException $e) {
            $this->Smarty->assign('dropDownLists', []);
        }

        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function settingTranslation($parm, $post)
    {

        $session = new SessionManager($parm[0]);

        $translation = [];
        $dropdown = [];
        try {
            $translation = Translation::readID((int)$session->get('translation_id') ?? 0);
            $this->Smarty->assign('translation', $translation);
        } catch (TranslationException $e) {
            $this->Smarty->assign('translation', []);
        }

        try {
            $dropdown = DropDownList::readID((int) $parm[1] ?? 0);
            $this->Smarty->assign('dropDown',$dropdown);
        } catch (SettingException $e) {
            $this->Smarty->assign('dropDown', []);
        }

        try {
            $this->Smarty->assign('settings', Translation::getSettingTranslationViewArray($translation,$dropdown));
        } catch (SettingException $e) {
            $this->Smarty->assign('settings', []);
        }

        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function generalTranslation($parm, $post)
    {

        $session = new SessionManager($parm[0]);

        switch ($parm[1]) {

            case Operation::UPDATE:

                if ($session->getDataManipulationToken() === $parm[2]) {

                    try {
                        Translation::generalTranslationUpdate($post, Translation::readID((int)$session->get('translation_id') ?? 0));
                        Notification::updatedAlert();
                    } catch (TranslationException $e) {

                    }
                }

                break;
        }

        $translationTo = [];

        try {
            $translationTo = Translation::readID((int)$session->get('translation_id') ?? 0);
            $this->Smarty->assign('translationTo', $translationTo);
        } catch (TranslationException $e) {
            $this->Smarty->assign('translationTo', []);
        }

        try {
            $this->Smarty->assign('TranslationVariables', Translation::getGeneralTranslationViewArray($session->get('language_code'),$translationTo->languageObject->code));
        } catch (TranslationException $e) {
            $this->Smarty->assign('TranslationVariables', []);
        }

        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function programTranslation($parm, $post)
    {

        $session = new SessionManager($parm[0]);

        $program = [];
        $translationTo = [];

        try {
            $program = Program::readID((int)$parm[1] ?? 0);
            $this->Smarty->assign('program', $program);
        } catch (TranslationException $e) {
            $this->Smarty->assign('program', []);
        }

        switch ($parm[2]) {

            case Operation::UPDATE:

                if ($session->getDataManipulationToken() === $parm[3]) {

                    try {
                        Translation::programTranslationUpdate($post, $program, Translation::readID((int)$session->get('translation_id') ?? 0));
                        Notification::updatedAlert();
                    } catch (TranslationException $e) {

                    }
                }

                break;
        }

        try {
            $translationTo = Translation::readID((int)$session->get('translation_id') ?? 0);
            $this->Smarty->assign('translationTo', $translationTo);
        } catch (TranslationException $e) {
            $this->Smarty->assign('translationTo', []);
        }

        try {
            $this->Smarty->assign('programArray', Translation::getProgramTranslationViewArray($program, $session->get('language_code'), $translationTo->languageObject->code));
        } catch (TranslationException $e) {
            $this->Smarty->assign('programArray', []);
        }

        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function reportTranslation($parm, $post)
    {

        $session = new SessionManager($parm[0]);

        switch ($parm[1]) {

            case Operation::UPDATE:

                if ($session->getDataManipulationToken() === $parm[2]) {

                    try {
                        Translation::reportsTranslationUpdate($post, Translation::readID((int)$session->get('translation_id') ?? 0));
                        Notification::updatedAlert();
                    } catch (TranslationException $e) {

                    }
                }

                break;
        }

        try {
            $translationTo = Translation::readID((int)$session->get('translation_id') ?? 0);
            $this->Smarty->assign('translationTo', $translationTo);
        } catch (TranslationException $e) {
            $this->Smarty->assign('translationTo', []);
        }

        try {
            $this->Smarty->assign('reportArray', Translation::getReportTranslationViewArray($session->get('language_code'),$translationTo->languageObject->code));
        } catch (TranslationException $e) {
            $this->Smarty->assign('reports', []);
        }

        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);
    }

    public function export($parm, $post)
    {
        $session = new SessionManager($parm[0]);

        $translation = Translation::readID((int)$session->get('translation_id') ?? 0);
        $translationAsJson = Translation::exportTranslation($translation);

        header('Content-Description: File Transfer');
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename= ' . str_replace(' ', '_', $translation->languageObject->name) . '.txt');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header("Content-Length: " . strlen($translationAsJson));
        ob_clean();
        flush();
        echo $translationAsJson;
        exit;
    }

    public function import($parm, $post)
    {

        $session = new SessionManager($parm[0]);

        try {
            $this->Smarty->assign('translation', Translation::readID((int)$session->get('translation_id') ?? 0));
        } catch (TranslationException $e) {
            $this->Smarty->assign('translation', []);
        }

        $session->updateDataManipulationToken();
        $session->save();
        $this->Smarty->assign('session', $session->array);

    }

}