<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_stopr extends Controller
{
    public function show($parm, $post)
    {


        switch ($parm[0]){

            case Operation::INSERT:

                if($_SESSION['s_DropDownList_token'] == $parm[1]){

                    try{

                        $DropDownList = new DropDownList();
                        $DropDownList->bindProperties($post);
                        $DropDownList->code = Helper::refineCode($post['code']);
                        $DropDownList->save();

                        Notification::createdAlert();

                    }catch (DropDownListException $e){

                        if($e->getMessage() == "CodeExist"){

                            Notification::alertMessage(Notification::WARNING,Translation::translate(Program::readID(Program::PROGRAM_SUPPORT_MANAGER_P001),'CodeExist'));

                        }

                    }


                }

                break;

            case Operation::UPDATE:

                if($_SESSION['s_DropDownList_token'] == $parm[1]){

                    try{

                        $DropDownList = DropDownList::readID((int) $parm[2] ?? 0);
                        $DropDownList->bindProperties($post);
                        $DropDownList->code = Helper::refineCode($post['code']);
                        $DropDownList->update();

                        Notification::updatedAlert();

                    }catch (DropDownListException $e){

                        if($e->getMessage() == "CodeExist"){

                            Notification::alertMessage(Notification::WARNING,Translation::translate(Program::readID(Program::PROGRAM_SUPPORT_MANAGER_P001),'CodeExist'));

                        }

                    }

                }

                break;

            case Operation::DELETE:

                if($_SESSION['s_DropDownList_token'] == $parm[1]){

                    try{

                        $DropDownList = DropDownList::readID((int) $parm[2] ?? 0);
                        $DropDownList->delete();

                        Notification::deletedAlert();

                    }catch (DropDownListException $e){}

                }

                break;
        }
        

        try{
            $this->Smarty->assign('dropDowns', DropDownList::toggleNestedRetrieve(false)::read([]));
        }catch (DropDownListException $e){
            $this->Smarty->assign('dropDowns',[]);
        }

        $_SESSION['s_DropDownList_token'] = md5(rand(0000,9999));

    }

    public function add($parm, $post)
    {
        $_SESSION['s_DropDownList_token'] = md5(rand(0000,9999));
    }

    public function edit($parm, $post)
    {
        try{
            $this->Smarty->assign('dropDown', DropDownList::readID((int) $parm[0] ?? 0));
        }catch (DropDownListException $e){
            $this->Smarty->assign('dropDown',[]);
        }

        $_SESSION['s_DropDownList_token'] = md5(rand(0000,9999));

    }

    public function confirm($parm, $post)
    {
        try{
            $this->Smarty->assign('dropDown', DropDownList::readID((int) $parm[0] ?? 0));
        }catch (DropDownListException $e){
            $this->Smarty->assign('dropDown',[]);
        }

        $_SESSION['s_DropDownList_token'] = md5(rand(0000,9999));

    }
}
