<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P001_prg extends Controller
{

    public function show($parm, $post)
    {

        switch ($parm[0]){

            case 'save_session':
                $_SESSION['s_bnd_id'] = $parm[1];
                break;

            case 'insert':

                    if($_SESSION['s_program_token'] == $parm[1]){

                        try{

                            $program = new Program();
                            $program->bindProperties($post);
                            $program->bnd_id = $_SESSION['s_bnd_id'];
                            $program->type = 1;
                            $program->create();

                            Notification::createdAlert();

                        }catch (ProgramException $e){

                        }

                    }

                break;

            case 'update':

                if($_SESSION['s_program_token'] == $parm[1]){

                    $program = null;
                    try{

                        $program = Program::readID( (int) $parm[2] ?? 0);
                        $program->bindProperties($post);
                        $program->update();

                        Notification::updatedAlert();

                    }catch (ProgramException $e){

                    }

                }

                break;

            case 'delete':

                if($_SESSION['s_program_token'] == $parm[1]){

                    try{

                        $program = Program::readID( (int) $parm[2] ?? 0);
                        $program->delete();

                    }catch (ProgramException $e){

                    }

                }

                break;
        }

        try{
            $this->Smarty->assign('bundles', Bundle::read());
        }catch (BundleException $e){
            $this->Smarty->assign('bundles',[]);
        }

        try{
            $this->Smarty->assign('bundle', Bundle::readID((int) $_SESSION['s_bnd_id'] ?? 0));
        }catch (BundleException $e){
            $this->Smarty->assign('bundle',[]);
        }

        try{

            $this->Smarty->assign('programs', Program::read([
                Program::BND_ID => $_SESSION['s_bnd_id']
            ],[0=>['property'=>Program::ORDER,'sort'=>'ASC']]));

        }catch (ProgramException $e){
            $this->Smarty->assign('programs', []);
        }

        $_SESSION['s_program_token'] = md5(rand(0000,9999));

    }

    public function add($parm, $post)
    {
        try{
            $this->Smarty->assign('bundle', Bundle::readID((int) $_SESSION['s_bnd_id'] ?? 0));
        }catch (BundleException $e){
            $this->Smarty->assign('bundle',[]);
        }

        $_SESSION['s_program_token'] = md5(rand(0000,9999));
    }

    public function edit($parm, $post)
    {
        try{
            $this->Smarty->assign('bundles', Bundle::read());
        }catch (BundleException $e){
            $this->Smarty->assign('bundles',[]);
        }

        try{
            $this->Smarty->assign('program', Program::readID((int) $parm[0] ?? 0));
        }catch (ProgramException $e){
            $this->Smarty->assign('program', []);
        }

        $_SESSION['s_program_token'] = md5(rand(0000,9999));
    }

    public function confirm($parm, $post)
    {
        try{
            $this->Smarty->assign('bundle', Bundle::readID((int) $_SESSION['s_bnd_id'] ?? 0));
        }catch (BundleException $e){
            $this->Smarty->assign('bundle',[]);
        }

        try{
            $this->Smarty->assign('program', Program::readID((int) $parm[0] ?? 0));
        }catch (ProgramException $e){
            $this->Smarty->assign('program', []);
        }

        $_SESSION['s_program_token'] = md5(rand(0000,9999));
    }
}
