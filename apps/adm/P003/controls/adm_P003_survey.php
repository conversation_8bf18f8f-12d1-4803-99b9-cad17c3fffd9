<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P003_survey extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {
            case 'insert':
                break;
        }

        $surveyList = $this->DB->find_with_parm('ut_survey', array('ut_survey_org_id' => $_SESSION['organization']->id), 2,
            'order by ut_survey_created_date DESC');
        foreach ($surveyList as $survey) {
            if (in_array($_SESSION['user']->id, explode(',', $survey['ut_survey_examinars']))) {
                $serveyIDS[] = $survey['ut_survey_id'];
            }
        }

        if (count($serveyIDS) != 0) {
            $serveyIDString = implode(',', $serveyIDS);
        } else {
            $serveyIDString = '0';
        }

        $surveyList = $this->DB->find_with_parm('ut_survey',
            array('ut_survey_org_id' => $_SESSION['organization']->id, 'ut_survey_status' => 1), 2,
            " and ut_survey_id in ({$serveyIDString}) ORDER BY ut_survey_created_date DESC");
        foreach ($surveyList as $key => $survey) {
            $surveyList[$key]['successPercentage'] = $this->surveyStatistic('survey', 'success', array(
                'prgIDs' => $survey['ut_survey_component'],
                'surveyID' => $survey['ut_survey_id'],
                'userID' => $_SESSION['user']->id
            ));
        }
        $this->Smarty->assign('SurveyList', $surveyList);
    }

    public function surveycontrol($parm, $post)
    {
        switch ($parm[0]) {
            case 'save_session':
                $_SESSION['s_inspecting_survey_id'] = $parm[1] ?? 0;
                $_SESSION['s_inspecting_component_id'] = $parm[2] ?? 0;
                $_SESSION['s_inspecting_opr_id'] = 0;
                break;

            case 'save_unit_test_session':
                $_SESSION['s_inspecting_opr_id'] = $parm[1] ?? 0;
                $this->oprFeatures($parm[2]);
                break;

            case 'report':
                if ($_SESSION['s_surveycontrol_token'] == $parm[1]) {
                    $this->updateReport($post, $parm[2], $parm[3], $parm[4], $parm[5]);
                }
                break;
        }

        $Survey = $this->DB->find_with_id('ut_survey', (int)$_SESSION['s_inspecting_survey_id'] ?? 0);
        $this->Smarty->assign('Survey', $Survey);
        $untList = $this->DB->find_with_parm('sh_opr',
            array('sh_opr_prg_id' => $_SESSION['s_inspecting_component_id'], 'sh_opr_have_ut' => 1), 2,
            'ORDER BY sh_opr_id');
        foreach ($untList as $key => $unit) {
            $untList[$key]['percentage'] = $this->surveyStatistic('unit', 'success', [
                'oprID' => $unit['sh_opr_id'],
                'surveyID' => (int)$_SESSION['s_inspecting_survey_id'] ?? 0,
                'userID' => $_SESSION['user']->id
            ]);
        }
        $this->Smarty->assign('UnitTestList', $untList);
        $FeaturesList = $this->DB->find_with_parm('sh_feature', array(
            'sh_feature_prg_id' => $_SESSION['s_inspecting_component_id'],
            'sh_feature_opr_id' => $_SESSION['s_inspecting_opr_id'],
            'sh_feature_show' => 1
        ), 2, ' ORDER BY sh_feature_order');
        foreach ($FeaturesList as $key => $FT) {
            $inspect = $this->DB->find_with_parm('ut_inspect', array(
                'ut_inspect_survey_id' => (int)$_SESSION['s_inspecting_survey_id'] ?? 0,
                'ut_inspect_opr_id' => $FT['sh_feature_opr_id'],
                'ut_inspect_feature_id' => $FT['sh_feature_id'],
                'ut_inspect_user_id' => $_SESSION['user']->id
            ), 1, '');
            $FeaturesList[$key]['Status'] = $inspect['ut_inspect_status'];
        }
        $this->Smarty->assign('FeaturesList', $FeaturesList);
        $this->Smarty->assign('ComponentPercentage', $this->surveyStatistic('component', 'success', [
            'prgID' => $_SESSION['s_inspecting_component_id'],
            'surveyID' => (int)$_SESSION['s_inspecting_survey_id'] ?? 0,
            'userID' => $_SESSION['user']->id
        ]));
        $this->Smarty->assign('UnitPercentage', $this->surveyStatistic('unit', 'success', [
            'oprID' => $_SESSION['s_inspecting_opr_id'],
            'surveyID' => (int)$_SESSION['s_inspecting_survey_id'] ?? 0,
            'userID' => $_SESSION['user']->id
        ]));

//        $total = $this->DB->num_rows_as_int('sh_opr',array('sh_opr_prg_id'=>$_SESSION['s_inspecting_component_id']));
//        $Success = $this->DB->num_rows_as_int('sh_opr',array('sh_opr_prg_id'=>$_SESSION['s_inspecting_component_id'],'sh_opr_unit_test_report'=>938));
//
//        $this->Smarty->assign('Percentage', number_format(($Success/$total) * 100,1,'.',','));
    }

    public function components($parm, $post)
    {
        $row = $this->DB->find_with_id('ut_survey', (int)$parm[0] ?? 0);
        $this->Smarty->assign('row', $row);
        $compoList = explode(',', $row['ut_survey_component']);
        $i = 0;
        $arr = array();
        foreach ($compoList as $comp) {
            $arr[$comp] = $this->surveyStatistic('component', 'success',
                ['prgID' => $comp, 'surveyID' => (int)$parm[0] ?? 0, 'userID' => $_SESSION['user']->id]);
        }
        $this->Smarty->assign('Components', $arr);
        $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
    }

    public function report($parm, $post)
    {
        $row = $this->DB->find_with_id('sh_feature', (int)$parm[0] ?? 0);
        $inspect = $this->DB->find_with_parm('ut_inspect', array(
            'ut_inspect_survey_id' => (int)$_SESSION['s_inspecting_survey_id'] ?? 0,
            'ut_inspect_opr_id' => $row['sh_feature_opr_id'],
            'ut_inspect_feature_id' => $row['sh_feature_id'],
            'ut_inspect_user_id' => $_SESSION['user']->id
        ), 1, '');

        $this->Smarty->assign('row', $row);
        $this->Smarty->assign('inspect', $inspect);
        $this->Smarty->assign('FeaturesStatusList',
            $this->DB->find_with_parm('st_setting', array('st_setting_opr_id' => 212), 2, 'order by st_setting_order'));

        if ($inspect['ut_inspect_status'] == 939) {
            $this->Smarty->assign('jscode1',
                '<script>$(document).ready(function(){$("#report").show("fast");});</script>');
        } else {
            $this->Smarty->assign('jscode1',
                '<script>$(document).ready(function(){$("#report").hide("fast");});</script>');
        }
        $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
    }

    public function info($parm, $post)
    {
        $this->Smarty->assign('row', $this->DB->find_with_id('sh_feature', $parm[0] ?? 0));
    }

    public function faillist($parm, $post)
    {
        switch ($parm[0]) {
            case 'save_session':
                $_SESSION['s_inspecting_survey_id'] = (int)$parm[1] ?? 0;
                break;

            case 'report':
                if ($_SESSION['s_surveycontrol_token'] == $parm[1]) {
                    $this->updateReport($post, $parm[2], $parm[3], $parm[4], $parm[5]);
                }
                break;
        }

        $failList = $this->DB->find_with_parm('ut_inspect', array(
            'ut_inspect_survey_id' => (int)$_SESSION['s_inspecting_survey_id'] ?? 0,
            'ut_inspect_user_id' => (int)$_SESSION['user']->id ?? 0,
            'ut_inspect_status' => 939
        ), 2, ' ORDER BY ut_inspect_created_date');
        foreach ($failList as $key => $fail) {
            $failList[$key]['task'] = $this->DB->find_with_id('pm_prtasks', $fail['ut_inspect_task_id'] ?? 0);
        }
        $this->Smarty->assign('failList', $failList);
        $Survey = $this->DB->find_with_id('ut_survey', (int)$_SESSION['s_inspecting_survey_id'] ?? 0);
        $this->Smarty->assign('Survey', $Survey);
        $this->Smarty->assign('SurveyPercentage', $this->surveyStatistic('survey', 'success', array(
            'prgIDs' => (int)$Survey['ut_survey_component'] ?? 0,
            'surveyID' => $Survey['ut_survey_id'],
            'userID' => $_SESSION['user']->id
        )));
    }

    public function failreport($parm, $post)
    {
        $row = $this->DB->find_with_id('sh_feature', (int)$parm[0] ?? 0);
        $inspect = $this->DB->find_with_parm('ut_inspect', array(
            'ut_inspect_survey_id' => (int)$_SESSION['s_inspecting_survey_id'] ?? 0,
            'ut_inspect_opr_id' => $row['sh_feature_opr_id'],
            'ut_inspect_feature_id' => $row['sh_feature_id'],
            'ut_inspect_user_id' => $_SESSION['user']->id
        ), 1, '');

        $this->Smarty->assign('row', $row);
        $this->Smarty->assign('inspect', $inspect);
        $this->Smarty->assign('FeaturesStatusList',
            $this->DB->find_with_parm('st_setting', array('st_setting_opr_id' => 212), 2, 'order by st_setting_order'));

        if ($inspect['ut_inspect_status'] == 939) {
            $this->Smarty->assign('jscode1',
                '<script>$(document).ready(function(){$("#report").show("fast");});</script>');
        } else {
            $this->Smarty->assign('jscode1',
                '<script>$(document).ready(function(){$("#report").hide("fast");});</script>');
        }
        $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
    }

    public function updateReport($post, $prgID, $oprID, $surveyID, $Featureid)
    {

        $num = $this->DB->num_rows_as_int('ut_inspect', array(
            'ut_inspect_survey_id' => $surveyID,
            'ut_inspect_opr_id' => $oprID,
            'ut_inspect_feature_id' => $Featureid,
            'ut_inspect_user_id' => $_SESSION['user']->id
        ));
        if ($num == 0) {
            $arr = array();
            $arr['ut_inspect_org_id'] = $_SESSION['organization']->id;
            $arr['ut_inspect_prg_id'] = $prgID;
            $arr['ut_inspect_opr_id'] = $oprID;
            $arr['ut_inspect_survey_id'] = $surveyID;
            $arr['ut_inspect_feature_id'] = $Featureid;
            $arr['ut_inspect_user_id'] = $_SESSION['user']->id;
            $arr['ut_inspect_status'] = $post['ut_inspect_status'];
            $arr['ut_inspect_report'] = $post['ut_inspect_report'];
            $this->DB->insert('ut_inspect', $arr);
        } else {
            $arr = array();
            $arr['ut_inspect_status'] = $post['ut_inspect_status'];
            $arr['ut_inspect_report'] = $post['ut_inspect_report'];

            $this->DB->update_with_parm('ut_inspect', $arr, array(
                'ut_inspect_survey_id' => $surveyID,
                'ut_inspect_opr_id' => $oprID,
                'ut_inspect_feature_id' => $Featureid,
                'ut_inspect_user_id' => $_SESSION['user']->id
            ));
        }

        $inspectRow = $this->DB->find_with_parm('ut_inspect', array(
            'ut_inspect_survey_id' => $surveyID,
            'ut_inspect_opr_id' => $oprID,
            'ut_inspect_feature_id' => $Featureid,
            'ut_inspect_user_id' => $_SESSION['user']->id
        ), 1, '');

        if ($post['ut_inspect_status'] == 939) {
            if ($inspectRow['ut_inspect_task_id'] == 0) {
                $this->createConnectedTask($oprID, $surveyID, $Featureid, $inspectRow['ut_inspect_id'],
                    $inspectRow['ut_inspect_report']);
            } else {
                $this->updateTask($inspectRow, $inspectRow['ut_inspect_task_id']);
            }
        }

        if ($post['ut_inspect_status'] == 938) {
            if ($inspectRow['ut_inspect_task_id'] >= 1) {
                $this->updateTask($inspectRow, $inspectRow['ut_inspect_task_id']);
            }
        }
    }

    public function createConnectedTask($oprID, $surveyID, $Featureid, $inspectID, $report)
    {

        $today = date('Y-m-d');
        $oprRow = $this->DB->find_with_id('sh_opr', $oprID);
        $surveyRow = $this->DB->find_with_id('ut_survey', $surveyID);
        $featureRow = $this->DB->find_with_id('sh_feature', $Featureid);
        $intervalRow = $this->DB->find_with_parm('pm_prdiv', array(
            'pm_prdiv_project_id' => $surveyRow['ut_survey_project_id'] ?? 0,
            'pm_prdiv_survey_prg_id' => $featureRow['sh_feature_prg_id']
        ), 1, '');
        $prrow = $this->DB->find_with_id('pm_project', $surveyRow['ut_survey_project_id'] ?? 0);
        $arr = array();
        $arr['pm_prtasks_org_id'] = $_SESSION['organization']->id;
        $arr['pm_prtasks_opr_id'] = $oprID;
        $arr['pm_prtasks_project_id'] = $surveyRow['ut_survey_project_id'];
        $arr['pm_prtasks_unit_id'] = $prrow['pm_project_unit_id'];
        $arr['pm_prtasks_type'] = $prrow['pm_project_type'];
        $arr['pm_prtasks_activity_status'] = 0;
        $arr['pm_prtasks_name'] = $oprRow['sh_opr_name_ar'] . ' / ' . $featureRow['sh_feature_name'];
        $arr['pm_prtasks_about'] = $report;
        $arr['pm_prtasks_provstatus'] = 420;
        $arr['pm_prtasks_connect_status'] = 819;
        $arr['pm_prtasks_connectedto'] = 0;
        $arr['pm_prtasks_start_date'] = $this->Date->get_date('ad', date('Y-m-d'));
        $arr['pm_prtasks_duration'] = 3;
        $arr['pm_prtasks_end_date'] = date("Y-m-d", strtotime($today . "+ 3 day"));
        $arr['pm_prtasks_cost_high'] = 0;
        $arr['pm_prtasks_cost_normal'] = 0;
        $arr['pm_prtasks_cost_low'] = 0;
        $arr['pm_prtasks_planned_value'] = 0;
        $arr['pm_prtasks_priority'] = 16;
        $arr['pm_prtasks_status'] = 395;
        $arr['pm_prtasks_assignto'] = $prrow['pm_project_manager_user_id'];
        $arr['pm_prtasks_div_id'] = $intervalRow['pm_prdiv_id'];
        $arr['pm_prtasks_indic_id'] = 0;
        $arr['pm_prtasks_execution'] = 397;
        $arr['pm_prtasks_created_by'] = $_SESSION['user']->id;
        $sglid = $this->DB->insert('pm_prtasks', $arr);
        $this->DB->update('ut_inspect', array('ut_inspect_task_id' => $sglid), $inspectID);

        Notification::sendNotification(397, 0, 'pm_prtasks', $sglid, $_SESSION['user']->id,
            $prrow['pm_project_manager_user_id'], 438, []);

    }

    public function updateTask($row, $id)
    {

        $exectutionType = 397;

        if ($row['ut_inspect_status'] == 938) {
            $exectutionType = 399;
        }

        $arr = array();
        $surveyRow = $this->DB->find_with_id('ut_survey', $row['ut_inspect_survey_id']);
        $intervalRow = $this->DB->find_with_parm('pm_prdiv', array(
            'pm_prdiv_project_id' => $surveyRow['ut_survey_project_id'] ?? 0,
            'pm_prdiv_survey_prg_id' => $row['ut_inspect_prg_id']
        ), 1, '');
        $arr['pm_prtasks_about'] = $row['ut_inspect_report'];
        $arr['pm_prtasks_div_id'] = $intervalRow['pm_prdiv_id'];
        $arr['pm_prtasks_execution'] = $exectutionType;
        $this->DB->update('pm_prtasks', $arr, $id);
    }

    public function surveyStatistic($type, $ptype, $parm)
    {
        $num = 0;
        switch ($type) {

            case 'survey':
                $featureNum = count($this->DB->find_with_parm('sh_feature', array('sh_feature_none' => 0), 2,
                    ' AND sh_feature_prg_id in (' . $parm['prgIDs'] . ')'));
                switch ($ptype) {
                    case 'success':
                        $num = $this->DB->num_rows_as_int('ut_inspect', array(
                            'ut_inspect_survey_id' => $parm['surveyID'],
                            'ut_inspect_user_id' => $parm['userID'],
                            'ut_inspect_status' => 938
                        ));
                        break;
                    case 'gray':
                        $num = $this->DB->num_rows_as_int('ut_inspect', array(
                            'ut_inspect_survey_id' => $parm['surveyID'],
                            'ut_inspect_user_id' => $parm['userID'],
                            'ut_inspect_status' => 937
                        ));
                        break;

                    case 'fail':
                        $num = $this->DB->num_rows_as_int('ut_inspect', array(
                            'ut_inspect_survey_id' => $parm['surveyID'],
                            'ut_inspect_user_id' => $parm['userID'],
                            'ut_inspect_status' => 939
                        ));
                        break;
                }
                return number_format(($num / $featureNum) * 100, 1, '.', ',');
                break;

            case 'component':
                $featureNum = $this->DB->num_rows_as_int('sh_feature', array('sh_feature_prg_id' => $parm['prgID']));
                switch ($ptype) {
                    case 'success':
                        $num = $this->DB->num_rows_as_int('ut_inspect', array(
                            'ut_inspect_prg_id' => $parm['prgID'],
                            'ut_inspect_survey_id' => $parm['surveyID'],
                            'ut_inspect_user_id' => $parm['userID'],
                            'ut_inspect_status' => 938
                        ));
                        break;
                    case 'gray':
                        $num = $this->DB->num_rows_as_int('ut_inspect', array(
                            'ut_inspect_prg_id' => $parm['prgID'],
                            'ut_inspect_survey_id' => $parm['surveyID'],
                            'ut_inspect_user_id' => $parm['userID'],
                            'ut_inspect_status' => 937
                        ));
                        break;

                    case 'fail':
                        $num = $this->DB->num_rows_as_int('ut_inspect', array(
                            'ut_inspect_prg_id' => $parm['prgID'],
                            'ut_inspect_survey_id' => $parm['surveyID'],
                            'ut_inspect_user_id' => $parm['userID'],
                            'ut_inspect_status' => 939
                        ));
                        break;
                }

                return number_format(($num / $featureNum) * 100, 1, '.', ',');
                break;

            case 'unit':
                $featureNum = $this->DB->num_rows_as_int('sh_feature', array('sh_feature_opr_id' => $parm['oprID']));
                switch ($ptype) {
                    case 'success':
                        $num = $this->DB->num_rows_as_int('ut_inspect', array(
                            'ut_inspect_opr_id' => $parm['oprID'],
                            'ut_inspect_survey_id' => $parm['surveyID'],
                            'ut_inspect_user_id' => $parm['userID'],
                            'ut_inspect_status' => 938
                        ));
                        break;
                    case 'gray':
                        $num = $this->DB->num_rows_as_int('ut_inspect', array(
                            'ut_inspect_opr_id' => $parm['oprID'],
                            'ut_inspect_survey_id' => $parm['surveyID'],
                            'ut_inspect_user_id' => $parm['userID'],
                            'ut_inspect_status' => 937
                        ));
                        break;

                    case 'fail':
                        $num = $this->DB->num_rows_as_int('ut_inspect', array(
                            'ut_inspect_opr_id' => $parm['oprID'],
                            'ut_inspect_survey_id' => $parm['surveyID'],
                            'ut_inspect_user_id' => $parm['userID'],
                            'ut_inspect_status' => 939
                        ));
                        break;
                }
                return number_format(($num / $featureNum) * 100, 1, '.', ',');
                break;
        }
    }

    public function oprFeatures($oprCode)
    {

        $oprRow = $this->snso->Database->find_with_id('sh_opr',
            $this->snso->Database->get_id_from_code('sh_opr', $oprCode));

        // Auto Features List
        $FL = [
            'Show' => 926,
            'Add' => 927,
            'Edit' => 928,
            'Delete' => 929,
            'Showable' => 930,
            'ShowInPrivilegeList' => 931,
            'DocSupport' => 932,
            'WorkFlowSupport' => 933,
            'ServiceSupport' => 934,
            'Help' => 935
        ];

        $result = explode(',', $oprRow['sh_opr_prv']);
        in_array(1, $result) ? $showStatus = 1 : $showStatus = 0;
        in_array(2, $result) ? $addStatus = 1 : $addStatus = 0;
        in_array(3, $result) ? $editStatus = 1 : $editStatus = 0;
        in_array(4, $result) ? $deleteStatus = 1 : $deleteStatus = 0;

        $this->createFeature($oprRow['sh_opr_prg_id'], $oprRow['sh_opr_id'], $oprCode, $FL['Show'], $showStatus, 1, []);
        $this->createFeature($oprRow['sh_opr_prg_id'], $oprRow['sh_opr_id'], $oprCode, $FL['Add'], $addStatus, 2, []);
        $this->createFeature($oprRow['sh_opr_prg_id'], $oprRow['sh_opr_id'], $oprCode, $FL['Edit'], $editStatus, 3, []);
        $this->createFeature($oprRow['sh_opr_prg_id'], $oprRow['sh_opr_id'], $oprCode, $FL['Delete'], $deleteStatus, 4,
            []);
        $this->createFeature($oprRow['sh_opr_prg_id'], $oprRow['sh_opr_id'], $oprCode, $FL['Showable'],
            $oprRow['sh_opr_showable'], 5, []);
        $this->createFeature($oprRow['sh_opr_prg_id'], $oprRow['sh_opr_id'], $oprCode, $FL['ShowInPrivilegeList'],
            $oprRow['sh_opr_show_in_prv_list'], 6, []);
        $this->createFeature($oprRow['sh_opr_prg_id'], $oprRow['sh_opr_id'], $oprCode, $FL['DocSupport'],
            $oprRow['sh_opr_have_doc'], 7, []);
        $this->createFeature($oprRow['sh_opr_prg_id'], $oprRow['sh_opr_id'], $oprCode, $FL['WorkFlowSupport'],
            $oprRow['sh_opr_have_wf'], 8, []);
        $this->createFeature($oprRow['sh_opr_prg_id'], $oprRow['sh_opr_id'], $oprCode, $FL['ServiceSupport'],
            $oprRow['sh_opr_have_es'], 9, []);
        $this->createFeature($oprRow['sh_opr_prg_id'], $oprRow['sh_opr_id'], $oprCode, $FL['Help'], 1, 10, []);
    }

    public function createFeature($prgID, $oprID, $oprCode, $typeID, $featureExist, $order, $post)
    {

        switch ($typeID) {
            case 936:
                $arr = array();
                $arr['sh_feature_prg_id'] = $prgID;
                $arr['sh_feature_opr_id'] = $oprID;
                $arr['sh_feature_opr_code'] = $oprCode;
                $arr['sh_feature_type'] = 936;
                $arr['sh_feature_name'] = $post['sh_feature_name'];
                $arr['sh_feature_describe'] = $post['sh_feature_describe'];
                $arr['sh_feature_order'] = $post['sh_feature_order'];
                $arr['sh_feature_show'] = $post['sh_feature_show'];
                $arr['sh_feature_success'] = 937;
                $this->snso->Database->insert('sh_feature', $arr);
                break;
            default:
                switch ($featureExist) {
                    case 1:
                        if ($this->snso->Database->num_rows_as_int('sh_feature', array(
                                'sh_feature_prg_id' => $prgID,
                                'sh_feature_opr_code' => $oprCode,
                                'sh_feature_type' => $typeID
                            )) == 0
                        ) {
                            $arr = array();
                            $arr['sh_feature_prg_id'] = $prgID;
                            $arr['sh_feature_opr_id'] = $oprID;
                            $arr['sh_feature_opr_code'] = $oprCode;
                            $arr['sh_feature_type'] = $typeID;
                            $arr['sh_feature_name'] = $this->snso->Database->get_name('st_setting', (int)$typeID ?? 0);
                            $arr['sh_feature_describe'] = $this->snso->Database->get_name('st_setting',
                                (int)$typeID ?? 0);
                            $arr['sh_feature_order'] = $order;
                            $arr['sh_feature_show'] = $post['sh_feature_show'];
                            $arr['sh_feature_success'] = 937;
                            $this->snso->Database->insert('sh_feature', $arr);
                        }
                        break;
                    case 0:
                        $this->deleteFeatureWithParm($prgID, $oprCode, $typeID);
                        break;
                }
                break;
        }
    }

    public function deleteFeatureWithParm($prgID, $oprCode, $typeID)
    {
        $this->snso->Database->delete_with_parm('sh_feature',
            array('sh_feature_prg_id' => $prgID, 'sh_feature_opr_code' => $oprCode, 'sh_feature_type' => $typeID));
    }
}
