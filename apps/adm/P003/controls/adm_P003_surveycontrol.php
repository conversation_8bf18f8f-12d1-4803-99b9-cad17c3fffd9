<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class adm_P003_surveycontrol extends Controller
{
    public function show($parm, $post)
    {
        switch ($parm[0]) {
            case 'insert':
                if ($_SESSION['s_surveycontrol_token'] == $parm[1]) {
                    $this->createSurvey($post);
                    $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
                }
                break;

            case 'update':
                if ($_SESSION['s_surveycontrol_token'] == $parm[1]) {
                    $this->updateSurvey($post, $parm[2]);
                    $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
                }
                break;

            case 'delete':
                if ($_SESSION['s_surveycontrol_token'] == $parm[1]) {
                    $this->deleteSurvey($parm[2]);
                    $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
                }
                break;

            case 'components':
                if ($_SESSION['s_surveycontrol_token'] == $parm[1]) {
                    $this->updateComponents($post, $parm[2]);
                    $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
                }
                break;

            case 'examiners':
                if ($_SESSION['s_surveycontrol_token'] == $parm[1]) {
                    $this->updateExaminers($post, $parm[2]);
                    $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
                }
                break;

            case 'status':
                if ($_SESSION['s_surveycontrol_token'] == $parm[1]) {
                    $this->updateStatus($post, $parm[2], $parm[3]);
                    $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
                }
                break;

        }

        $this->Smarty->assign('SurveyList',
            $this->DB->find_with_parm('ut_survey', array('ut_survey_org_id' => $_SESSION['organization']->id), 2,
                'order by ut_survey_created_date DESC'));
    }

    public function add($parm, $post)
    {
        $this->Smarty->assign('projectList', $this->DB->find_with_parm('pm_project',
            array('pm_project_org_id' => $_SESSION['organization']->id, 'pm_project_request_success' => 1), 2,
            'order by pm_project_id'));
        $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
    }

    public function edit($parm, $post)
    {
        $this->Smarty->assign('projectList', $this->DB->find_with_parm('pm_project',
            array('pm_project_org_id' => $_SESSION['organization']->id, 'pm_project_request_success' => 1), 2,
            'order by pm_project_id'));
        $this->Smarty->assign('row', $this->DB->find_with_id('ut_survey', (int)$parm[0] ?? 0));
        $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
    }

    public function confirm($parm, $post)
    {
        $this->Smarty->assign('row', $this->DB->find_with_id('ut_survey', (int)$parm[0] ?? 0));
        $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
    }

    public function components($parm, $post)
    {
        $this->Smarty->assign('prgList',
            $this->DB->find_with_parm('sh_prg', array('sh_prg_activation' => 1), 2, 'ORDER BY sh_prg_order'));
        $this->Smarty->assign('row', $this->DB->find_with_id('ut_survey', (int)$parm[0] ?? 0));
        $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
    }

    public function examiners($parm, $post)
    {
        $row = $this->DB->find_with_id('ut_survey', $parm[0] ?? 0);
        $this->Smarty->assign('row', $row);
        $this->Smarty->assign('memberList', $this->DB->find_with_parm('pm_prmem',
            array('pm_prmem_org_id' => $_SESSION['organization']->id, 'pm_prmem_project_id' => $row['ut_survey_project_id']), 2,
            'order by pm_prmem_user_id'));
        $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
    }

    public function status($parm, $post)
    {
        $this->Smarty->assign('row', $this->DB->find_with_id('ut_survey', (int)$parm[0] ?? 0));
        $_SESSION['s_surveycontrol_token'] = md5(rand(0000, 9999));
    }

    /*
     * Helping functions
     */

    public function createSurvey($post)
    {
        if (!empty($post['ut_survey_project_id'])) {
//            $projectRow = $this->DB->find_with_id('pm_project',$post['ut_survey_project_id']);
            $arr = array();
            $arr['ut_survey_org_id'] = $_SESSION['organization']->id;
            $arr['ut_survey_project_id'] = $post['ut_survey_project_id'];
            $arr['ut_survey_component'] = '';
            $arr['ut_survey_examinars'] = '';
            $arr['ut_survey_status '] = 0;
            $arr['ut_survey_created_by'] = $_SESSION['user']->id;
            $this->DB->insert('ut_survey', $arr);
        }
    }

    public function updateSurvey($post, $id)
    {
        if (!empty($post['ut_survey_project_id'])) {
            $arr = array();
            $arr['ut_survey_project_id'] = $post['ut_survey_project_id'];
            $this->DB->update('ut_survey', $arr, $id);
        }
    }

    public function deleteSurvey($id)
    {
        $this->DB->delete('ut_survey', $id);
    }

    public function updateComponents($post, $id)
    {

        $arr = array();
        $arr['ut_survey_component'] = implode(',', $post['ut_survey_component']);
        $this->DB->update('ut_survey', $arr, $id);

        // compare survey component with project intervals
        $row = $this->DB->find_with_id('ut_survey', $id ?? 0);
        $div_result = $this->DB->find_with_parm('pm_prdiv',
            array('pm_prdiv_org_id' => $_SESSION['organization']->id, 'pm_prdiv_project_id' => $row['ut_survey_project_id']), 2,
            'ORDER BY pm_prdiv_id');

        // delete un needed div
        foreach ($div_result as $div) {

            if (in_array($div['pm_prdiv_survey_prg_id'], $post['ut_survey_component'])) {

            } else {
                $this->deleteInterval($div['pm_prdiv_id']);
            }
        }

        // add new div
        foreach ($post['ut_survey_component'] as $comp) {

            $num = $this->DB->num_rows_as_int('pm_prdiv', array(
                'pm_prdiv_org_id' => $_SESSION['organization']->id,
                'pm_prdiv_project_id' => $row['ut_survey_project_id'],
                'pm_prdiv_survey_prg_id' => $comp
            ));
            if ($num == 0) {
                $this->insertInterval($row['ut_survey_project_id'], $this->DB->get_name('sh_prg', $comp), $comp);
            }
        }
    }

    public function updateExaminers($post, $id)
    {
        $arr = array();
        $arr['ut_survey_examinars'] = implode(',', $post['ut_survey_examinars']);
        $this->DB->update('ut_survey', $arr, $id);
    }

    public function updateStatus($post, $id, $status)
    {
        $arr = array();
        $arr['ut_survey_status'] = $status;
        $this->DB->update('ut_survey', $arr, $id);
    }

    public function insertInterval($projectID, $intervalName, $comp)
    {
        $arr = array();
        $arr['pm_prdiv_org_id'] = $_SESSION['organization']->id;
        $arr['pm_prdiv_project_id'] = $projectID;
        $arr['pm_prdiv_name'] = $intervalName;
        $arr['pm_prdiv_survey_prg_id'] = $comp;
        $arr['pm_prdiv_description'] = $intervalName;
        $arr['pm_prdiv_created_by'] = $_SESSION['user']->id;
        $this->DB->insert('pm_prdiv', $arr);
    }

    public function deleteInterval($id)
    {
        $this->DB->delete('pm_prdiv', $id);
    }
}