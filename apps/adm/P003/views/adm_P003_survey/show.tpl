{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=body}
<div class="row snsowraper">
	<table id="DataTable" dir="rtl" class="table table-hover">
	<thead>
		<tr>
			<th width="5%">&nbsp;</th>
			<th width="20%">{#gnr_check#}</th>
			<th width="40%">{#gnr_report#}</th>
			<th width="10%">{#gnr_settings#}</th>
		</tr>
	</thead>
	<tbody>
	{$i=1}
	{foreach $SurveyList as $row}
		<tr>
			<td align="center">{$i++}</td>
			<td>{getname table=pm_project id=$row.ut_survey_project_id}</td>
			<td align="center">
				{*<span class="label label-yellow" style="color: #333;"> غير محدد {$row.grayPercentage} %</span>*}
				<span class="label label-yellow" style="color: #333;"> {#gnr_success#} {$row.successPercentage} %</span>
				{*<span class="label label-yellow" style="color: #333;"> فشل {$row.failPercentage} %</span>*}
			</td>
			<td nowrap>
				{url check=0 urltype='mbutton' url_string="adm/P003/survey/components/0/{$smarty.session.lang}/{$row.ut_survey_id}" text_value="{#gnr_check#}"}
				{url check=0 urltype='button' url_string="adm/P003/survey/faillist/0/{$smarty.session.lang}/save_session/{$row.ut_survey_id}" text_value="{#p_survey_fail#}"}
			</td>
		</tr>
	{/foreach}
	</tbody>
	</table>
</div>
{/block}
