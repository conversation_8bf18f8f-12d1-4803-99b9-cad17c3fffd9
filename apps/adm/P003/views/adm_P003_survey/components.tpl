{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_survey_components#}</h4>
    </div>
    <div class="modal-body">
        <div class="row snsowraper">
            <div class="col-lg-12">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_survey_project#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getname table=pm_project id=$row.ut_survey_project_id}</div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_components#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    {foreach $Components as $key => $Percentage}
                        {url check=0 urltype='button' url_string="adm/P003/survey/surveycontrol/0/{$smarty.session.lang}/save_session/{$row.ut_survey_id}/{$key}" text_value="{getname table=sh_prg id=$key}&nbsp;<span class='label label-yellow' style='color: #333;'> {#gnr_success#} {$Percentage} %</span>" }
                        <div class="horizontal-space"></div>
                    {/foreach}
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}