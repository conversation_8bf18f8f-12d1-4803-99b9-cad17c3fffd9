{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=page_body}
	<h5 class="row-title before-gold"><i class="fa fa-check-square-o gold"></i>
		{getname table=pm_project id=$Survey.ut_survey_project_id}
		&nbsp;&raquo;&nbsp;
		{getname table=sh_prg id=$smarty.session.s_inspecting_component_id}
        &nbsp;&raquo;&nbsp;
		<span class="label label-yellow" style="color: #333;">{$ComponentPercentage} %</span>
	</h5>
	<div class="row">
		<div class="col-lg-6">
			<div class="row snsowraper">
				<table id="DataTable" dir="rtl" class="table table-hover">
					<thead>
					<tr>
						<th width="5%">&nbsp;</th>
						<th width="25%">{#gnr_menu#}</th>
						<th width="25%">{#gnr_operation#}</th>
						<th width="25%">{#gnr_status#}</th>
						<th width="20%">{#gnr_settings#}</th>
					</tr>
					</thead>
					<tbody>
					{$i=1}
					{foreach $UnitTestList as $row}
						<tr>
							<td align="center">{$i++}</td>
							<td align="center">{getname table=sh_sec id=$row.sh_opr_sec_id}</td>
							<td>{getname table=sh_opr id=$row.sh_opr_id}</td>
							<td><span class="label label-yellow" style="color: #333;">{$row.percentage}&nbsp;%</span></td>
							<td align="center">{url check=0 urltype='button' url_string="adm/P003/survey/surveycontrol/0/{$smarty.session.lang}/save_unit_test_session/{$row.sh_opr_id}/{$row.sh_opr_code}" text_value="{#gnr_view#}"}</td>
						</tr>
					{/foreach}
					</tbody>
				</table>
			</div>
		</div>
		<div class="col-lg-6">
			<div class="widget flat radius-bordered">
				<div class="widget-header bordered-bottom bordered-blue">
					<span class="widget-caption">{getname table=sh_opr id=$smarty.session.s_inspecting_opr_id} &nbsp; <span class="label label-yellow" style="color: #333;">{$UnitPercentage} %</span></span>
				</div>
				<div class="widget-body">
					<table id="DataTable" dir="rtl" class="table table-hover">
						<thead>
						<tr>
							<th width="5%">&nbsp;</th>
							<th width="55%">{#gnr_operation#}</th>
							<th width="20%">{#gnr_status#}</th>
							<th width="20%">{#gnr_settings#}</th>
						</tr>
						</thead>
						<tbody>
						{$i=1}
						{foreach $FeaturesList as $row}
							<tr>
								<td align="center">{$i++}</td>
								<td align="right">
									{url check=0 urltype='mbutton' url_string="adm/P003/survey/info/0/{$smarty.session.lang}/{$row.sh_feature_id}" text_value='<i class="fa fa-question-circle"></i>'}
									{$row.sh_feature_name}
								</td>
								<td align="center">
									{if $row.Status eq 938}<span style="color: green;"><i class="fa fa-circle"></i></span>{/if}
									{if $row.Status eq 939}<span style="color: red;"><i class="fa fa-circle"></i></span>{/if}
									{if $row.Status eq 937}<span style="color: gray;"><i class="fa fa-circle"></i></span>{/if}
									{getname table=st_setting id=$row.Status}
								</td>
								<td nowrap>{url check=0 urltype='mbutton' url_string="adm/P003/survey/report/0/{$smarty.session.lang}/{$row.sh_feature_id}" text_value="{#gnr_report#}"}</td>
							</tr>
						{/foreach}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
{/block}
{block name=back}{url urltype="path" url_string="adm/P003/survey/show/0/{$smarty.session.lang}"}{/block}


{*sh_feature_id*}
{*sh_feature_prg_id*}
{*sh_feature_opr_id*}
{*sh_feature_opr_code*}
{*sh_feature_type*}
{*sh_feature_name*}
{*sh_feature_describe*}
{*sh_feature_order*}