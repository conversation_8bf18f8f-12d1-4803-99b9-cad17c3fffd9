{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
<script>
    $(document).ready(function(){

        $("#937").click(function(){
            if ($("#937").is(":checked"))
            {
                $("#report").hide("fast");
            }
        });

        $("#938").click(function(){
            if ($("#938").is(":checked"))
            {
                $("#report").hide("fast");
            }
        });

        $("#939").click(function(){
            if ($("#939").is(":checked"))
            {
                $("#report").show("fast");
            }
        });
    });
</script>
    {$jscode1}
{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">
            {getname table=sh_prg id=$row.sh_feature_prg_id}
            &nbsp;&raquo;&nbsp;
            {getname table=sh_opr id=$row.sh_feature_opr_id}
            &nbsp;&raquo;&nbsp;
            {$row.sh_feature_name}
        </h4>
    </div>
    <div class="modal-body">
        <div class="row snsowraper">
            <div class="col-lg-12">
                <form  method="post" action='{url urltype="path" url_string="adm/P003/survey/surveycontrol/0/{$smarty.session.lang}/report/{$smarty.session.s_surveycontrol_token}/{$row.sh_feature_prg_id}/{$row.sh_feature_opr_id}/{$smarty.session.s_inspecting_survey_id}/{$row.sh_feature_id}"}'>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_property#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$row.sh_feature_name}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_description#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{$row.sh_feature_describe}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_decision#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <div class="control-group">
                            {foreach $FeaturesStatusList as $status}
                                <div class="radio">
                                    <label>
                                        <input name="ut_inspect_status" value="{$status.st_setting_id}" id="{$status.st_setting_id}" {if $status.st_setting_id eq $inspect.ut_inspect_status } checked="checked" {/if} type="radio">
                                        <span class="text">{getname table=st_setting id=$status.st_setting_id}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div id="report">
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_report#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <textarea name="ut_inspect_report" rows=15 class="form-control">{$inspect.ut_inspect_report}</textarea>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
                </form>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}