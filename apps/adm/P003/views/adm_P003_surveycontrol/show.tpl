{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=body}
<div class="row snsowraper">
	<table id="DataTable" dir="rtl" class="table table-hover">
	<thead>
		<tr>
			<th width="5%">{url check=1 urltype="madd" opr_code='surveycontrol' url_string="adm/P003/surveycontrol/add/0/{$smarty.session.lang}"}</th>
			<th width="20%">{#gnr_check#}</th>
			<th width="40%">{#gnr_description#}</th>
			<th width="10%">{#gnr_components#}</th>
			<th width="10%">{#p_surveyc_examiners#}</th>
			<th width="10%">{#gnr_satuts#}</th>
			<th width="10%">{#gnr_settings#}</th>
		</tr>
	</thead>
	<tbody>
	{$i=1}
	{foreach $SurveyList as $row}
		<tr>
			<td align="center">{$i++}</td>
			<td>{getname table=pm_project id=$row.ut_survey_project_id}</td>
			<td>{getcolumn table=pm_project column=pm_project_about id=$row.ut_survey_project_id}</td>
			<td nowrap>
				{if $row.ut_survey_status eq 0}
					{url check=0 urltype='mbutton' url_string="adm/P003/surveycontrol/components/0/{$smarty.session.lang}/{$row.ut_survey_id}" text_value="{#gnr_components#}"}
				{/if}
			</td>
			<td nowrap>
				{if $row.ut_survey_status eq 0}
					{url check=0 urltype='mbutton' url_string="adm/P003/surveycontrol/examiners/0/{$smarty.session.lang}/{$row.ut_survey_id}" text_value="{#p_surveyc_examiners#}"}
				{/if}
			</td>
			<td nowrap>
				{url check=0 urltype='mbutton' url_string="adm/P003/surveycontrol/status/0/{$smarty.session.lang}/{$row.ut_survey_id}" text_value="{#gnr_status#}"}
				{if $row.ut_survey_status eq 1}فعال{/if}
				{if $row.ut_survey_status eq 0}غير فعال{/if}
			</td>
			<td nowrap>
				{if $row.ut_survey_status eq 0}
					{url check=1 urltype="medit" opr_code='surveycontrol' url_string="adm/P003/surveycontrol/edit/0/{$smarty.session.lang}/{$row.ut_survey_id}"}
					{url check=1 urltype="mdelete" opr_code='surveycontrol' url_string="adm/P003/surveycontrol/confirm/0/{$smarty.session.lang}/{$row.ut_survey_id}"}
				{/if}
			</td>
		</tr>
	{/foreach}
	</tbody>
	</table>
</div>
{/block}
