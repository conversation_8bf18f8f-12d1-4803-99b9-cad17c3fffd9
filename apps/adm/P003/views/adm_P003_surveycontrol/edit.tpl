{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_surveyc_edit#}</h4>
    </div>
    <div class="modal-body">
        <form  method="post" action='{url urltype="path" url_string="adm/P003/surveycontrol/show/0/{$smarty.session.lang}/update/{$smarty.session.s_surveycontrol_token}/{$row.ut_survey_id}"}'>
            <div class="row snsowraper">
                <div class="col-12">
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_surveyc_project#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <select name="ut_survey_project_id">
                            <option value="{$row.ut_survey_project_id}" selected>{getname table=pm_project id=$row.ut_survey_project_id}</option>
                            {foreach $projectList as $project}
                                <option value="{$project.pm_project_id}">{$project.pm_project_name}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}