{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script type="text/javascript">
        //dom ready handler
        jQuery(function ($) {
//form submit handler
            $('.checkboxrequired').submit(function (e) {

                if (!$('.checkboxoption').is(':checked')) {
                    $('#submitMessage').html("{#p_surveyc_project#}");

                    return false;
                }
            })
        });
    </script>
{/block}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_surveyc_examiners#}</h4>
    </div>
    <div class="modal-body">
        <form  method="post" action='{url urltype="path" url_string="adm/P003/surveycontrol/show/0/{$smarty.session.lang}/examiners/{$smarty.session.s_surveycontrol_token}/{$row.ut_survey_id}"}' class="checkboxrequired">
            <div class="row snsowraper">
                <div class="col-lg-12">
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_surveyc_project#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getname table=pm_project id=$row.ut_survey_project_id}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_surveyc_examiners#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        {foreach $memberList as $memp}
                            <div class="checkbox">
                                <label>
                                    <input name="ut_survey_examinars[]" type="checkbox" class="checkboxoption" value="{$memp.pm_prmem_user_id}" {if in_array($memp.pm_prmem_user_id,','|explode:$row.ut_survey_examinars)} checked="checked" {/if}>
                                    <span class="text">{getname table=sh_user id=$memp.pm_prmem_user_id}</span>
                                </label>
                            </div>
                        {/foreach}
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                        <span class="danger" id="submitMessage"></span>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}