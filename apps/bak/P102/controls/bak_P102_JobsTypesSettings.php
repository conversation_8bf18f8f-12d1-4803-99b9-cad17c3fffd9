<?php
/**
 * Deny Direct Script Access
 *
 * @category Controller
 * <AUTHOR> <<EMAIL>>
 * @SuppressWarnings(PHPMD.CamelCaseClassName)
 */
class bak_P102_JobsTypesSettings extends Controller
{
    const SESSION_TOKEN = 's_crmJobNames_token';
    const CRUDS = ['insert', 'update', 'delete'];
    const OPR_CODE = 'JobsTypesSettings';
    /**
     * شاشة عرض أنواع الوظائف
     *
     * @param Request $parm fsfdgd
     * @param mixed $post
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    public function show($parm, $post)
    {
        if (is_callable([$this, $parm[0]]) && in_array($parm[0], self::CRUDS)) {
            call_user_func_array([$this, $parm[0]], [$parm, $post, new CsrfManager()]);
        }

        try{
            $this->Smarty->assign('rows', CR<PERSON><PERSON>obNames::read([
                CRMJobNames::ORG_ID => $_SESSION['organization']->id
            ]));
        }catch (CRMJobNamesException $e){
            $this->Smarty->assign('rows',[]);
        }
        $this->Smarty->assign('opr_code', self::OPR_CODE);
        $linkedTypes = [];
        try {
            foreach (CRMCustomerStaff::read() as $customerStaff) {
                array_push($linkedTypes, (int) $customerStaff->job);
            }
        } catch (CRMCustomerStaffException $e) {}
        $this->Smarty->assign('linked_types', $linkedTypes);
    }
    /**
     * Add JobType
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.UnusedLocalParameters)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function insert($parm, $post, $csrf)
    {
        try{
            $csrf->check('token', $post['csrf_input']);
            $crmJobNames=new CRMJobNames();
            $crmJobNames->bindProperties($post);
            $crmJobNames->org_id = $_SESSION['organization']->id;
            $crmJobNames->created_by = $_SESSION['user']->id;
            // إجعله غير فعال في حالة عدم تفعيله إفتراضيا
            $crmJobNames->activation = $post['activation']?? 653;
            $crmJobNames->save();

            Notification::createdAlert();
        }catch (CRMJobNamesException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// csrf exceptions
        }
    }
    /**
     * Update JobType
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function update($parm, $post, $csrf)
    {
        try{
            $csrf->check('update_token', $post['csrf_input']);
            $crmJobNames = CRMJobNames::readID((int) $parm[1] ?? 0);
            $crmJobNames->bindProperties($post);
            $crmJobNames->update();

            Notification::updatedAlert();
        }catch (CRMJobNamesException $e){
            //// Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle csrf exceptions
        }
    }
    /**
     * Delete JobType
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    private function delete($parm, $post, $csrf)
    {
        try{
            $csrf->check('delete_token', $post['csrf_input']);
            $crmJobNames = CRMJobNames::readID((int) $parm[1] ?? 0);
            $crmJobNames->delete();

            Notification::deletedAlert();
        }catch (CRMJobNamesException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle some csrf exceptions
        }
    }
    /**
     * Add Job Type
     * @param mixed $parm , $post
     * @return void
     */
    public function add()
    {
        $this->assignURI('insert');
    }

    /**
     * Edit Job Type
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function edit($parm)
    {
        try{
            $this->Smarty->assign('raw', CRMJobNames::readID((int) $parm[0] ?? 0));
        }catch (CRMJobNamesException $e){
            $this->Smarty->assign('raw',[]);
        }
        $this->assignURI('update');
    }
    /**
     * confirm deletion
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function confirm($parm)
    {
        try{
            $this->Smarty->assign('raw', CRMJobNames::readID((int) $parm[0] ?? 0));
        }catch (CRMJobNamesException $e){
            $this->Smarty->assign('raw',[]);
        }
        $this->assignURI('delete');
    }
    /**
     * assign URI
     *
     * @param string $pageName
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     */
    private function assignURI($actionName, $pageName = 'show')
    {
        $this->Smarty->assign('base_uri', "{$_SESSION['bnd']}/{$_SESSION['prg']}/{$_SESSION['oprname']}/{$pageName}/0/{$_SESSION['lang']}/{$actionName}");
    }
}