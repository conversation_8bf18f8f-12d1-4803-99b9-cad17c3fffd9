<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON>
 * Date: 7/4/17
 * Time: 12:28 PM
 */

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');
class bak_P102_CrmDocumentProject extends Controller
{
    const S_TOKEN = 's_project_token';
    const S_PROJECT_ID = 's_project_id';

    const CRM_NOTIFICATION_ID = 1022;

    const S_PRODUCT_TAB = 's_prepare_tab';

    public function show($parm, $post)
    {
        $_SESSION[self::S_PRODUCT_TAB] = 1;
        // Clear the post data from session if no errors
        $_SESSION['post'] = null;

        $actrow = null;
        if (isset($_SESSION[ self::S_PROJECT_ID ])) {
            try {
                $actrow = CRMProjects::readID((int)$_SESSION[ self::S_PROJECT_ID ]);
            } catch (CRMProjectsException $e) {
            }
        }

        switch ($parm[0]) {
            case 'save_session':
                $_SESSION[ self::S_PROJECT_ID ] = $parm[ 1];
                $_SESSION['s_back_path'] = implode('/', array_slice(explode('/', $this->URL->currentUrlPath()), 8));
                break;
            case 'document':
                if ($_SESSION[ self::S_TOKEN ] == $parm[ 1]) {
                    try {
                        $product = CRMProducts::readID((int)$actrow->product);
                    } catch (CRMProductsException $e) {
                        $product = [];
                    }
                    $start_date = $this->Date->get_date($_SESSION['user']->def_datetype,
                        (new DateTime($product->start_date))->sub(new DateInterval("P1D"))->format('Y-m-d'));
                    $end_date = $this->Date->get_date($_SESSION['user']->def_datetype,
                        (new DateTime($product->end_date))->add(new DateInterval("P1D"))->format('Y-m-d'));
                    // Check if the validation passes
                    $this->updateDoc($post);
                }
                $_SESSION[self::S_PRODUCT_TAB] = 1;
                break;
            case 'team':
                switch ($parm[1]) {
                    case Operation::INSERT:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // Set validation rules
                            Validation::rules($post, [
                                'user_id' => 'required',
                                'task_id' => 'required',
                                'prvs' => 'array',
                            ]);


                            // Check if the validation passes
                            if (Validation::check()) {
                                $this->insertTeamMember($post);
                            }
                        }
                        break;
                    case Operation::UPDATE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
	                        $this->updateTeamMember($parm, $post);
                        }
                        break;
                    case Operation::DELETE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            $this->deleteTeamMember($parm);
                        }
                        break;
                }
	            $_SESSION[ self::S_TOKEN ] = md5(rand(0, 9999));
                $_SESSION[self::S_PRODUCT_TAB] = 2;
                break;
            case 'risk':
                switch ($parm[1]) {
                    case Operation::INSERT:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // Set the validation rules
                            Validation::rules($post, [
                                'risk' => 'required',
                                'mitigation_strategy' => 'required',
                            ]);

                            // Check if the validation passes
                            if (Validation::check()) {
                                $this->insertRisk($post);
                            }
                        }
                        break;
                    case Operation::UPDATE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // Set the validation rules
                            Validation::rules($post, [
                                'risk' => 'required',
                                'mitigation_strategy' => 'required',
                            ]);

                            // Check if the validation passes
                            if (Validation::check()) {
                                $this->updateRisk($parm, $post);
                            }
                        }
                        break;
                    case Operation::DELETE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            $this->deleteRisk($parm);
                        }
                        break;
                }
                $_SESSION[ self::S_TOKEN ] = md5(rand(0, 9999));
                $_SESSION[self::S_PRODUCT_TAB] = 5;
                break;
            case 'interval':
                switch ($parm[1]) {
                    case Operation::INSERT:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // Set the validation rules
                            Validation::rules($post, [
                                'name' => 'required',
                                'desc' => 'required'
                            ]);

                            if (Validation::check()) {
                                $this->insertInterval($post);
                                $this->Smarty->assign('message', Operation::INSERT);
                            }
                        }
                        break;
                    case Operation::UPDATE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // Set the validation rules
                            Validation::rules($post, [
                                'name' => 'required',
                                'desc' => 'required'
                            ]);

                            if (Validation::check()) {
                                $this->updateInterval($parm, $post);
                                $this->Smarty->assign('message', Operation::INSERT);
                            }
                        }
                        break;
                    case Operation::DELETE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            $this->deleteInterval($parm);
                        }
                        break;
                }
                $_SESSION[ self::S_TOKEN ] = md5(rand(0, 9999));
                $_SESSION[self::S_PRODUCT_TAB] = 3;
                break;
            case 'activity':
                switch ($parm[1]) {
                    case Operation::INSERT:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // Set the validation rules
                            $check_date = $this->Date->get_date($_SESSION['user']->def_datetype, (new DateTime
                            ($actrow->start_date))->sub(new DateInterval("P1D"))->format('Y-m-d'));
                            $this->insertTask($post);
                            // Check if the validation passes
//                            if (Validation::check()) {
//                            } else {
//                                $_SESSION['post'] = $post;
//                            }
                        }
                        break;
                    case Operation::UPDATE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // $check_date = $this->Date->get_date($_SESSION['user']->def_datetype, (new DateTime
                            // ($actrow->start_date))->sub(new DateInterval("P1D"))->format('Y-m-d'));
	                        $this->updateTask($parm, $post);
                        }
                        break;
                    case Operation::DELETE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            $this->deleteTask($parm);
                        }
                        break;
                }
                $_SESSION[ self::S_TOKEN ] = md5(rand(0, 9999));
                $_SESSION[self::S_PRODUCT_TAB] = 3;
                break;
            case 'indicator':
                switch ($parm[1]) {
                    case Operation::INSERT:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            if (CRMIndicator::count([
                                                        CRMIndicator::ORG_ID => $_SESSION['organization']->id,
                                                        CRMIndicator::PROJECT_ID => $_SESSION[ self::S_PROJECT_ID ],
                                                        CRMIndicator::ACTIVITY_ID => $post['project_id']
                            ]) == 0) {
                                $indicator = new CRMIndicator();
                                $indicator->bindProperties($post);
                                $indicator->org_id = $_SESSION['organization']->id;
                                $indicator->project_id = $_SESSION[ self::S_PROJECT_ID ];
                                $indicator->created_by = $_SESSION['user']->id;
                                $indicator->created_date = date('Y-m-d', time());
                                $indicator->save();

                                Notification::createdAlert();

                            } else {

                                Notification::alertMessage(Notification::WARNING,'gnr_indicator_is_duplicate');

                            }
                        }
                        break;
                    case Operation::UPDATE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            try {
                                $indicator = CRMIndicator::readID((int)$parm[3]);
                                $indicator->bindProperties($post);
                                $indicator->save();

                                Notification::updatedAlert();

                            } catch (CRMIndicatorException $e) {
                            }
                        }
                        break;
                    case Operation::DELETE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            try {
                                CRMIndicator::readID((int)$parm[3])->delete();

                                Notification::deletedAlert();

                            } catch (CRMIndicatorException $e) {
                            }

                        }
                }
                $_SESSION[self::S_PRODUCT_TAB] = 4;
                break;
        }
        $actrow = null;
        if (isset($_SESSION[ self::S_PROJECT_ID ])) {
            try {
                $actrow = CRMProjects::readID((int)$_SESSION[ self::S_PROJECT_ID ]);
            } catch (CRMProjectsException $e) {
            }
        }
        $this->Smarty->assign('actrow', $actrow);
        try {
            $product = CRMProducts::readID((int) $actrow->product);
        } catch (CRMProductsException $e) {
            $product = [];
        }
        $this->Smarty->assign('product', $product);
        try {
            $this->Smarty->assign('support_unit_list', Unit::supportUnitsExceptCurrent($actrow->unit_id));
        } catch (UnitException $e) {
        }

        try {
            $this->Smarty->assign('members_list', CRMTeam::read([
                CRMTeam::PROJECT_ID => $actrow->id
            ]));
        } catch (CRMTeamException $e) {
        }

        $this->Smarty->assign('evalist', pm_preva::simpleReadByProperty([]));
        $this->Smarty->assign('provlist', Setting::read([
            Setting::OPR_ID => 95
        ], [
            0 => [
                'property' => Setting::ORDER,
                'sort' => 'ASC'
            ]
        ]));
        $this->Smarty->assign('week_days', Setting::read([
            Setting::OPR_ID => 25
        ], [
            0 => [
                'property' => Setting::ORDER,
                'sort' => 'ASC'
            ]
        ]));
        try {
            $this->Smarty->assign('risks_list', CRMRisks::read([
                CRMRisks::PROJECT_ID => $_SESSION[ self::S_PROJECT_ID ]
            ]));
        } catch (CRMRisksException $e) {
        }

        try {
            $this->Smarty->assign('indicators', CRMIndicator::read([
                CRMIndicator::ORG_ID => $_SESSION['organization']->id,
                CRMIndicator::PROJECT_ID => $_SESSION[ self::S_PROJECT_ID ]
            ]));
        } catch (CRMIndicatorException $e) {
        }

        $_SESSION[ self::S_TOKEN ] = md5(rand(0, 9999));
    }
    
    public function browse($parm, $post)
    {
        $_SESSION[self::S_PRODUCT_TAB] = 1;
        // Clear the post data from session if no errors
        $_SESSION['post'] = null;
    
        $actrow = null;
        if (isset($_SESSION[ self::S_PROJECT_ID ])) {
            try {
                $actrow = CRMProjects::readID((int)$_SESSION[ self::S_PROJECT_ID ]);
            } catch (CRMProjectsException $e) {
            }
        }
    
        switch ($parm[0]) {
            case 'save_session':
                $_SESSION[ self::S_PROJECT_ID ] = $parm[ 1];
                break;
            case 'document':
                if ($_SESSION[ self::S_TOKEN ] == $parm[ 1]) {
                    try {
                        $product = CRMProducts::readID((int)$actrow->product);
                    } catch (CRMProductsException $e) {
                        $product = [];
                    }
                    $start_date = $this->Date->get_date($_SESSION['user']->def_datetype,
                        (new DateTime($product->start_date))->sub(new DateInterval("P1D"))->format('Y-m-d'));
                    $end_date = $this->Date->get_date($_SESSION['user']->def_datetype,
                        (new DateTime($product->end_date))->add(new DateInterval("P1D"))->format('Y-m-d'));
                    // Check if the validation passes
                    $this->updateDoc($post);
                }
                $_SESSION[self::S_PRODUCT_TAB] = 1;
                break;
            case 'team':
                switch ($parm[1]) {
                    case Operation::INSERT:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // Set validation rules
                            Validation::rules($post, [
                                'user_id' => 'required',
                                'task_id' => 'required',
                                'prvs' => 'array',
                            ]);
                        
                        
                            // Check if the validation passes
                            if (Validation::check()) {
                                $this->insertTeamMember($post);
                            }
                        }
                        break;
                    case Operation::UPDATE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            $this->updateTeamMember($parm, $post);
                        }
                        break;
                    case Operation::DELETE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            $this->deleteTeamMember($parm);
                        }
                        break;
                }
                $_SESSION[ self::S_TOKEN ] = md5(rand(0, 9999));
                $_SESSION[self::S_PRODUCT_TAB] = 2;
                break;
            case 'risk':
                switch ($parm[1]) {
                    case Operation::INSERT:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // Set the validation rules
                            Validation::rules($post, [
                                'risk' => 'required',
                                'mitigation_strategy' => 'required',
                            ]);
                        
                            // Check if the validation passes
                            if (Validation::check()) {
                                $this->insertRisk($post);
                            }
                        }
                        break;
                    case Operation::UPDATE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // Set the validation rules
                            Validation::rules($post, [
                                'risk' => 'required',
                                'mitigation_strategy' => 'required',
                            ]);
                        
                            // Check if the validation passes
                            if (Validation::check()) {
                                $this->updateRisk($parm, $post);
                            }
                        }
                        break;
                    case Operation::DELETE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            $this->deleteRisk($parm);
                        }
                        break;
                }
                $_SESSION[ self::S_TOKEN ] = md5(rand(0, 9999));
                $_SESSION[self::S_PRODUCT_TAB] = 5;
                break;
            case 'interval':
                switch ($parm[1]) {
                    case Operation::INSERT:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // Set the validation rules
                            Validation::rules($post, [
                                'name' => 'required',
                                'desc' => 'required'
                            ]);
                        
                            if (Validation::check()) {
                                $this->insertInterval($post);
                                $this->Smarty->assign('message', Operation::INSERT);
                            }
                        }
                        break;
                    case Operation::UPDATE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // Set the validation rules
                            Validation::rules($post, [
                                'name' => 'required',
                                'desc' => 'required'
                            ]);
                        
                            if (Validation::check()) {
                                $this->updateInterval($parm, $post);
                                $this->Smarty->assign('message', Operation::INSERT);
                            }
                        }
                        break;
                    case Operation::DELETE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            $this->deleteInterval($parm);
                        }
                        break;
                }
                $_SESSION[ self::S_TOKEN ] = md5(rand(0, 9999));
                $_SESSION[self::S_PRODUCT_TAB] = 3;
                break;
            case 'activity':
                switch ($parm[1]) {
                    case Operation::INSERT:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // Set the validation rules
                            $check_date = $this->Date->get_date($_SESSION['user']->def_datetype, (new DateTime
                            ($actrow->start_date))->sub(new DateInterval("P1D"))->format('Y-m-d'));
                            $this->insertTask($post);
                            // Check if the validation passes
//                            if (Validation::check()) {
//                            } else {
//                                $_SESSION['post'] = $post;
//                            }
                        }
                        break;
                    case Operation::UPDATE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            // $check_date = $this->Date->get_date($_SESSION['user']->def_datetype, (new DateTime
                            // ($actrow->start_date))->sub(new DateInterval("P1D"))->format('Y-m-d'));
                            $this->updateTask($parm, $post);
                        }
                        break;
                    case Operation::DELETE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            $this->deleteTask($parm);
                        }
                        break;
                }
                $_SESSION[ self::S_TOKEN ] = md5(rand(0, 9999));
                $_SESSION[self::S_PRODUCT_TAB] = 3;
                break;
            case 'indicator':
                switch ($parm[1]) {
                    case Operation::INSERT:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            if (CRMIndicator::count([
                                    CRMIndicator::ORG_ID => $_SESSION['organization']->id,
                                    CRMIndicator::PROJECT_ID => $_SESSION[ self::S_PROJECT_ID ],
                                    CRMIndicator::ACTIVITY_ID => $post['project_id']
                                ]) == 0) {
                                $indicator = new CRMIndicator();
                                $indicator->bindProperties($post);
                                $indicator->org_id = $_SESSION['organization']->id;
                                $indicator->project_id = $_SESSION[ self::S_PROJECT_ID ];
                                $indicator->created_by = $_SESSION['user']->id;
                                $indicator->created_date = date('Y-m-d', time());
                                $indicator->save();
                            
                                Notification::createdAlert();
                            
                            } else {
                            
                                Notification::alertMessage(Notification::WARNING,'gnr_indicator_is_duplicate');
                            
                            }
                        }
                        break;
                    case Operation::UPDATE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            try {
                                $indicator = CRMIndicator::readID((int)$parm[3]);
                                $indicator->bindProperties($post);
                                $indicator->save();
                            
                                Notification::updatedAlert();
                            
                            } catch (CRMIndicatorException $e) {
                            }
                        }
                        break;
                    case Operation::DELETE:
                        if ($parm[2] === $_SESSION[ self::S_TOKEN ]) {
                            try {
                                CRMIndicator::readID((int)$parm[3])->delete();
                            
                                Notification::deletedAlert();
                            
                            } catch (CRMIndicatorException $e) {
                            }
                        
                        }
                }
                $_SESSION[self::S_PRODUCT_TAB] = 4;
                break;
        }
        $actrow = null;
        if (isset($_SESSION[ self::S_PROJECT_ID ])) {
            try {
                $actrow = CRMProjects::readID((int)$_SESSION[ self::S_PROJECT_ID ]);
            } catch (CRMProjectsException $e) {
            }
        }
        $this->Smarty->assign('actrow', $actrow);
        try {
            $product = CRMProducts::readID((int)$actrow->product);
            $this->Smarty->assign('product', $product);
        } catch (CRMProductsException $e) {
        }
        try {
            $this->Smarty->assign('support_unit_list', Unit::supportUnitsExceptCurrent($actrow->unit_id));
        } catch (UnitException $e) {
        }
    
        try {
            $this->Smarty->assign('members_list', CRMTeam::read([
                CRMTeam::PROJECT_ID => $actrow->id
            ]));
        } catch (CRMTeamException $e) {
        }
    
        $this->Smarty->assign('evalist', pm_preva::simpleReadByProperty([]));
        $this->Smarty->assign('provlist', Setting::read([
            Setting::OPR_ID => 95
        ], [
            0 => [
                'property' => Setting::ORDER,
                'sort' => 'ASC'
            ]
        ]));
        $this->Smarty->assign('week_days', Setting::getList(25));
        try {
            $this->Smarty->assign('risks_list', CRMRisks::read([
                CRMRisks::PROJECT_ID => $_SESSION[ self::S_PROJECT_ID ]
            ]));
        } catch (CRMRisksException $e) {
        }
    
        try {
            $this->Smarty->assign('indicators', CRMIndicator::read([
                CRMIndicator::ORG_ID => $_SESSION['organization']->id,
                CRMIndicator::PROJECT_ID => $_SESSION[ self::S_PROJECT_ID ]
            ]));
        } catch (CRMIndicatorException $e) {
        }
    
        $_SESSION[ self::S_TOKEN ] = md5(rand(0, 9999));
    }

    public function teamadd($parm, $post)
    {
        $actrow = null;
        try {
            $actrow = CRMProjects::readID((int)$_SESSION[ self::S_PROJECT_ID ]);
            $this->Smarty->assign('actrow', $actrow);
        } catch (CRMProjectsException $e) {
            $this->Smarty->assign('actrow', []);

        }
        $allUnitsString = $actrow->unit_id;
        if ($actrow->supply_unit != "") {
            $allUnitsString .= ',' . $actrow->supply_unit;
        }
        try {
            $aunits = $allUnitsString ?? 0;
            $this->Smarty->assign('users_list', Vacant::usersInUnits($aunits));
        } catch (ModelException $e) {
            $this->Smarty->assign('users_list', []);
        }
        try {
            $this->Smarty->assign('prvs_list', Setting::getList(90, true));
        } catch(SettingException $e) {
            $this->Smarty->assign('prvs_list', []);
        }
        try {
            $this->Smarty->assign('tasks_types', Setting::getList(CRMTeam::TEAM_TYPES, true));
        } catch (SettingException $e){
            $this->Smarty->assign('tasks_types', []);
        }
    }

    public function teamedit($parm, $post)
    {
        $actrow = null;
        try {
            $actrow = CRMProjects::readID((int)$_SESSION[ self::S_PROJECT_ID ]);
        } catch (CRMProjectsException $e) {
        }
        try {
	        $memrow = CRMTeam::readID((int)$parm[0]);
        } catch (CRMTeamException $e) {
        	$memrow = [];
        }
	    $this->Smarty->assign('memrow', $memrow);
	    $allUnitsString = $actrow->unit_id;
	    if ($actrow->supply_unit != "") {
		    $allUnitsString .= ',' . $actrow->supply_unit;
	    }
	    try {
	        $aunits = $allUnitsString ?? 0;
            $this->Smarty->assign('users_list', Vacant::usersInUnits($aunits));
        } catch(ModelException $e){
            $this->Smarty->assign('users_list', []);
        }
        try {
            $this->Smarty->assign('prvs_list', Setting::getList(90, true));
        } catch (SettingException $e){
            $this->Smarty->assign('prvs_list', []);
        }
        try {
            $this->Smarty->assign('tasks_types', Setting::read([
                Setting::OPR_ID => CRMTeam::TEAM_TYPES
            ]));
        } catch (SettingException $e) {
            $this->Smarty->assign('tasks_types', []);
        }
        $this->Smarty->assign('actrow', $actrow);
    }

    public function teamconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', CRMTeam::readID((int)$parm[0]));
        } catch (CRMTeamException $e) {
        }
    }

    public function projectmanagerprivileges($parm, $post)
    {
        try {
            $this->Smarty->assign('memrow', CRMTeam::readID((int)$parm[0]));
        } catch (CRMTeamException $e) {
        }
        $this->Smarty->assign('prvs_list', Setting::read([
            Setting::OPR_ID => 90
        ], [
            0 => [
                'property' => Setting::ID,
                'sort' => 'DESC'
            ]
        ]));
    }

    public function intervaladd($parm, $post)
    {
        try {
            $actrow = CRMProjects::readID((int)$_SESSION[ self::S_PROJECT_ID ]);
            $this->Smarty->assign('actrow', $actrow);
        } catch (CRMProjectsException $e) {
        }
    }

    public function intervaledit($parm, $post)
    {
        try {
            $this->Smarty->assign('interval', CRMInterval::readID((int)$parm[0]));
        } catch (CRMIntervalException $e) {
        }
    }

    public function intervalconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('interval', CRMInterval::readID((int)$parm[0]));
        } catch (CRMIntervalException $e) {
        }
    }

    public function activityadd($parm, $post)
    {
        $actrow = null;
        try {
            $actrow = CRMProjects::readID((int)$_SESSION[ self::S_PROJECT_ID ]);
        } catch (CRMProjectsException $e) {
        }
        $this->Smarty->assign('actrow', $actrow);
        try {
            $this->Smarty->assign('priority_list', ProjectPriority::getPriorityList($actrow->eva_list_id));
        } catch (ProjectPriorityException $e) {
        }
        $this->Smarty->assign('connection_list', Setting::read([
            Setting::OPR_ID => 180
        ]));
        $this->Smarty->assign('gender_list', Setting::read([
            Setting::OPR_ID => 27
        ]));
	    try {
		    $activity_options = CRMActivities::read([CRMActivities::ACTIVATION => Setting::ACTIVE_CASE]);
	    } catch (CRMActivitiesException $e) {
	    	$activity_options = [];
	    }
	    $this->Smarty->assign('activity_options', $activity_options);
	    
	    try {
		    $customerStaff = CRMCustomerStaff::read([
		        CRMCustomerStaff::CUST => $actrow->cust
            ]);
	    } catch (CRMCustomerStaffException $e) {
	    	$customerStaff = [];
	    }
	    $this->Smarty->assign('customer_staff', $customerStaff);
    }

    public function activityedit($parm, $post)
    {
        $actrow = null;
        try {
            $actrow = CRMProjects::readID((int)$_SESSION[ self::S_PROJECT_ID ]);
        } catch (CRMProjectsException $e) {
        }
        $this->Smarty->assign('actrow', $actrow);
        try {
            $this->Smarty->assign('priority_list', ProjectPriority::getPriorityList($actrow->eva_list_id));
        } catch (ProjectPriorityException $e) {
        }
        $this->Smarty->assign('connection_list', Setting::read([
            Setting::OPR_ID => 180
        ]));
        $this->Smarty->assign('gender_list', Setting::read([
            Setting::OPR_ID => 27
        ]));

        try {
            $task_row = CRMTasks::readID((int)$parm[0]);
            $task_row->benefic_count = (int) ( $task_row->benefic_count ?? 0 );
        } catch (CRMTasksException $e) {
        	$task_row = [];
        }
	    try {
		    $activity_options = CRMActivities::read([CRMActivities::ACTIVATION => Setting::ACTIVE_CASE]);
	    } catch (CRMActivitiesException $e) {
		    $activity_options = [];
	    }
	    $this->Smarty->assign('activity_options', $activity_options);
	    try {
		    $customerStaff = CRMCustomerStaff::read([
                CRMCustomerStaff::CUST => $actrow->cust
            ]);
	    } catch (CRMCustomerStaffException $e) {
		    $customerStaff = [];
	    }
	    $this->Smarty->assign('taskRow', $task_row);
	    $this->Smarty->assign('customer_staff', $customerStaff);
    }

    public function activityconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', CRMTasks::readID((int)$parm[0]));
        } catch (CRMTasksException $e) {
        }
    }

    public function riskadd($parm, $post)
    {
    }

    public function riskedit($parm, $post)
    {
        try {
            $this->Smarty->assign('row', CRMRisks::readID((int)$parm[0]));
        } catch (CRMRisksException $e) {
        }
    }

    public function riskconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', CRMRisks::readID((int)$parm[0]));
        } catch (CRMRisksException $e) {
        }
    }

    public function indicatorAdd($parm, $post, $files)
    {
	    try {
		    $activity_options = CRMActivities::read([CRMActivities::ACTIVATION => Setting::ACTIVE_CASE]);
	    } catch (CRMActivitiesException $e) {
		    $activity_options = [];
	    }
	    $this->Smarty->assign('activities', $activity_options);
    }

    public function indicatorEdit($parm, $post, $files)
    {
        try {
            $this->Smarty->assign('indicator', CRMIndicator::readID((int)$parm[0]));
        } catch (CRMIndicatorException $e) {
            $this->Smarty->assign('indicator', null);
        }
	
	    try {
		    $activity_options = CRMActivities::read([CRMActivities::ACTIVATION => Setting::ACTIVE_CASE]);
	    } catch (CRMActivitiesException $e) {
		    $activity_options = [];
	    }
	    $this->Smarty->assign('activities', $activity_options);
    }

    public function indicatorConfirm($parm, $post, $files)
    {
        try {
            $this->Smarty->assign('indicator', CRMIndicator::readID((int)$parm[0]));
        } catch (CRMIndicatorException $e) {
            $this->Smarty->assign('indicator', null);
        }
    }


    /***********************************************************
     * Extracted methods (Helping methods)
     ***********************************************************/
    /**
     * @param $post
     */
    public function updateDoc($post)
    {
        try {
            $project = CRMProjects::readID((int)$_SESSION[ self::S_PROJECT_ID ]);
            $project->bindProperties($post);
            $project->start_date = $this->Date->get_date('ad', (new DateTime($post[    'start_date']))->format('Y-m-d'));
            $project->end_date = $this->Date->get_date('ad', (new DateTime($post['end_date']))->format('Y-m-d'));
            $supply_units = [];
            $weekends_list = [];
            $product_ids = [];
            foreach ($post['product_ids'] as $product) {
                $product_ids[] = $product;
            }
            foreach ($post['supply_units'] as $unit) {
                $supply_units[] = $unit;
            }
            foreach ($post['weekends_list'] as $day) {
                $weekends_list[] = $day;
            }
            $project->product_ids = implode(',', $product_ids);
            $project->supply_unit = implode(',', $supply_units);
            $project->weekends_list = implode(',', $weekends_list);
            $project->save();

            Notification::updatedAlert();
        } catch (CRMProjectsException $e) {
        }
    }

    /**
     * @param $post
     */
    public function insertTeamMember($post)
    {
        $member = new CRMTeam();
        $member->bindProperties($post);
        $member->org_id = $_SESSION['organization']->id;
        $member->project_id = $_SESSION[ self::S_PROJECT_ID ];
        $prvs = [];
        foreach ($post['prvs'] as $prv) {
            $prvs[] = $prv;
        }
        $member->prvs = implode(',', $prvs);
        $member->created_by = $_SESSION['user']->id;
        $member->created_date = date('Y-m-d', time());
        if (
            CRMTeam::count([
                CRMTeam::USER_ID => $member->user_id,
                CRMTeam::PROJECT_ID => $_SESSION[ self::S_PROJECT_ID ]
            ]) == 0
        ) {
            $member->save();
            Notification::createdAlert();
        } else {
	        Notification::alertMessage('warning', 'p_member_already_in');
        }
    }

    /**
     * @param $parm
     * @param $post
     */
    public function updateTeamMember($parm, $post)
    {
        try {
            $member = CRMTeam::readID((int)$parm[3]);
            $member->bindProperties($post);
            $prvs = [];
            foreach ($post['prvs'] as $prv) {
                $prvs[] = $prv;
            }
            $member->prvs = implode(',', $prvs);
            $member->save();

            Notification::updatedAlert();

        } catch (CRMTeamException $e) {
        }
    }

    /**
     * @param $parm
     */
    public function deleteTeamMember($parm)
    {
        try {
            $member = CRMTeam::readID((int)$parm[3]);
            $member->delete();

            Notification::deletedAlert();

        } catch (CRMTeamException $e) {
        }
    }

    /**
     * @param $post
     */
    public function insertRisk($post)
    {
        $risk = new CRMRisks();
        $risk->bindProperties($post);
        $risk->org_id = $_SESSION['organization']->id;
        $risk->project_id = $_SESSION[ self::S_PROJECT_ID ];
        $risk->created_by = $_SESSION['user']->id;
        $risk->created_date = date('Y-m-d', time());
        $risk->save();

        Notification::createdAlert();

    }

    /**
     * @param $parm
     * @param $post
     */
    public function updateRisk($parm, $post)
    {
        try {
            $risk = CRMRisks::readID((int)$parm[3]);
            $risk->bindProperties($post);
            $risk->save();

            Notification::updatedAlert();

        } catch (CRMRisksException $e) {
        }
    }

    /**
     * @param $parm
     */
    public function deleteRisk($parm)
    {
        try {
            $risk = CRMRisks::readID((int)$parm[3]);
            $risk->delete();

            Notification::deletedAlert();

        } catch (CRMRisksException $e) {
        }
    }

    /**
     * @param $post
     */
    public function insertInterval($post)
    {
        try {
            $activity = CRMProjects::readID((int)$_SESSION[ self::S_PROJECT_ID ]);
        } catch (CRMProjectsException $e) {
            $activity = [];
        }
        $interval = new CRMInterval();
        $interval->bindProperties($post);
        $interval->org_id = $_SESSION['organization']->id;
        $interval->project_id = $activity->id;
        $interval->start_date = $activity->start_date;
        $interval->end_date = $activity->start_date;
        $interval->created_by = $_SESSION['user']->id;
        $interval->created_date = date('Y-m-d', time());
        $interval->save();

        Notification::createdAlert();

    }

    /**
     * @param $parm
     * @param $post
     */
    public function updateInterval($parm, $post)
    {
        try {
            $interval = CRMInterval::readID((int)$parm[3]);
            $interval->bindProperties($post);
            $interval->save();

            Notification::updatedAlert();

        } catch (CRMIntervalException $e) {
        }
    }

    /**
     * @param $parm
     */
    public function deleteInterval($parm)
    {
        try {
            CRMInterval::readID((int)$parm[3])->delete();

            Notification::deletedAlert();

        } catch (CRMIntervalException $e) {
        }
    }

    /**
     * @param $post
     */
    public function insertTask($post)
    {
        CRMTasks::createNewTask($_SESSION['organization'], $_SESSION['user'], (int)$_SESSION[ self::S_PROJECT_ID ], $post, 1);
    }

    /**
     * @param $parm
     * @param $post
     */
    public function updateTask($parm, $post)
    {
        try {
            $task = CRMTasks::readID((int)$parm[3]);
            $task->bindProperties($post);
	        $task->targeted_employees = implode(",", $task->targeted_employees);
            $task->start_date = $this->Date->get_date('ad', (new DateTime($post['start_date']))
                ->format('Y-m-d'));
            $task->end_date = (new DateTime($post['start_date']))->add(new DateInterval('P' .
                $task->duration . 'D'))->format('Y-m-d');
            $task->save();

            Notification::updatedAlert();

        } catch (CRMTasksException $e) {
        }
    }

    /**
     * @param $parm
     */
    public function deleteTask($parm)
    {
        try {
            CRMTasks::readID((int)$parm[3])->delete();

            Notification::deletedAlert();

        } catch (CRMTasksException $e) {
        }
    }
}
