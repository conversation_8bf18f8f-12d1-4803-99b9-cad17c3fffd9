<?php
/**
 * Deny Direct Script Access
 *
 * @category Controller
 * <AUTHOR> <<EMAIL>>
 * @SuppressWarnings(PHPMD.CamelCaseClassName)
 */
class bak_P102_CustomersDataMenu extends Controller
{
    const SESSION_TOKEN = 's_CustomersDataMenu_token';
    const CRUDS = ['insert', 'update', 'delete'];
    const CUST_STAFF_CRUDS = [
        'updateCustomerContact',
        'addCustomerStaff',
        'storeEnvAnalysis',
        'deleteCustomerStaff',
        'updateCustomerStaff',
        'deleteCustomerEnv',
        'updateCustomerEnv',
        'browseEnv',
        'browseStaff'
    ];
    const OPR_CODE = 'CustomersDataMenu';
    /**
     * show Customers Data
     *
     * @param Request $parm
     * @param mixed $post
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    public function show($parm, $post)
    {
        if (is_callable([$this, $parm[0]]) && in_array($parm[0], self::CRUDS)) {
            call_user_func_array([$this, $parm[0]], [$parm, $post, new CsrfManager()]);
        }

        try {
            $this->Smarty->assign('rows', CRMCustomers::read([
                CRMCustomers::ORG_ID => $_SESSION['organization']->id,
                CRMCustomers::ACTIVATION => Setting::ACTIVE_CASE
            ]));
        } catch (CRMCustomersException $e) {
            $this->Smarty->assign('rows', []);
        }
        $this->Smarty->assign('opr_code', self::OPR_CODE);
    }
    /**
     * Update Customer Data
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function update($parm, $post, $csrf)
    {
        try {
            $csrf->check('update_token', $post['csrf_input']);
            $customerId = (int) $parm[1];
            $customerProfile = CRMCustomers::readID( $customerId ?? 0);
            $customerProfile->bindProperties($post);
            $customerProfile->update();
            Notification::updatedAlert();
        } catch (CRMJobNamesException $e) {} catch (Exception $e) {
            //// handle csrf exceptions
        }
    }

    /**
     * Edit
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function edit($parm)
    {
        try {
            $this->Smarty->assign('customer_types', CRMCustomersTypes::read([
                CRMCustomersTypes::ORG_ID => $_SESSION['organization']->id,
                CRMCustomersTypes::ACTIVATION => Setting::ACTIVE_CASE
            ]));
        } catch (CRMCustomersTypesException $e) {
            $this->Smarty->assign('customer_types', []);
        }
        try {
            $this->Smarty->assign('customer_accesses', CRMCustomerAccessablity::read([
                CRMCustomerAccessablity::ORG_ID => $_SESSION['organization']->id,
                CRMCustomerAccessablity::ACTIVATION => Setting::ACTIVE_CASE
            ]));
        } catch (CRMCustomerAccessablityException $e) {
            $this->Smarty->assign('customer_accesses', []);
        }
        try {
            $customerId = (int) $parm[0];
            $this->Smarty->assign('row', CRMCustomers::readID( $customerId ?? 0));
        } catch (CRMCustomersException $e) {
            $this->Smarty->assign('row', []);
        }
        $this->assignURI('update');
    }
    public function browseEnv($parm, $post, $csrf)
    {
	    try {
		    $analysis_priority = Setting::read([Setting::OPR_ID => Setting::ANALYSIS_PRIORITY]);
	    } catch (SettingException $e) {
		    $analysis_priority = [];
	    }
	    
	    try {
	        $customerEnvId = (int) $parm[0];
		    $custEnv = CRMCustomerEnv::readID($customerEnvId ?? 0);
//		    $custEnv = CRMCustomerEnv::read([CRMCustomerEnv::CUST => ((int) $parm[0] ?? 0)]);
	    } catch (CRMCustomerEnvException $e) {
		    $custEnv = [];
	    }
	    try {
		    $customer = CRMCustomers::readID((int) $custEnv->cust);
	    } catch (CRMCustomersException $e) {
	    	$customer = [];
	    }
	    $this->Smarty->assign('customer', $customer);
	    $this->Smarty->assign('env', $custEnv);
	    $this->Smarty->assign('env_pr', $analysis_priority );
	    $backURLString = "{$_SESSION['bundle']->code}/{$_SESSION['program']->code}/{$_SESSION['oprname']}/customerPanel/0/{$_SESSION['lang']}/{$customer->id}";
	    $this->Smarty->assign('backURL', $backURLString);
    }
	public function browseStaff($parm, $post, $csrf)
	{
		try {
			$analysis_priority = Setting::read([Setting::OPR_ID => Setting::ANALYSIS_PRIORITY]);
		} catch (SettingException $e) {
			$analysis_priority = [];
		}
		
		try {
		    $customerStaffId = (int) $parm[0];
			$custStaff = CRMCustomerStaff::readID($customerStaffId ?? 0);
		} catch (CRMCustomerStaffException $e) {
			$custStaff = [];
		}
		try {
			$customer = CRMCustomers::readID((int) $custStaff->cust);
		} catch (CRMCustomersException $e) {
			$customer = [];
		}
		$backURLString = "{$_SESSION['bundle']->code}/{$_SESSION['program']->code}/{$_SESSION['oprname']}/customerPanel/0/{$_SESSION['lang']}/{$customer->id}";
		$this->Smarty->assign('backURL', $backURLString);
		$this->Smarty->assign('customer', $customer);
		$this->Smarty->assign('staff', $custStaff);
		$this->Smarty->assign('staff_pr', $analysis_priority );
	}
    public function customerPanel($parm, $post)
    {
        if (is_callable([$this, $parm[0]]) && in_array($parm[0], self::CUST_STAFF_CRUDS)) {
            call_user_func_array([$this, $parm[0]], [$parm, $post, new CsrfManager()]);
        }
        try {
            $customerEnvId = (int) $parm[1];
            $custEnv = CRMCustomerEnv::read([CRMCustomerEnv::CUST => ($customerEnvId ?? 0)]);
            $this->Smarty->assign('cust_env', $custEnv);
        } catch (CRMCustomerEnvException $e) {
            try {
                $customerId = (int) $parm[0];
                $custEnv = CRMCustomerEnv::read([CRMCustomerEnv::CUST => ($customerId ?? 0)]);
                $this->Smarty->assign('cust_env', $custEnv);
            } catch (CRMCustomerEnvException $e) {
                $this->Smarty->assign('cust_env', []);
            }
        }
        $cust = [];
        try {
            $customerId = (int) $parm[0];
            $cust =CRMCustomers::readID($customerId ?? 0);
            $this->Smarty->assign('row', $cust);
            $custStaff = CRMCustomerStaff::read([CRMCustomerStaff::CUST => $customerId ?? 0 ]);
            $this->Smarty->assign('cust_staff', $custStaff);
        } catch (CRMCustomersException $e) {
            try {
                $customerId = (int) $parm[1];
                $cust =CRMCustomers::readID( $customerId ?? 0);
                $this->Smarty->assign('row', $cust);
                try {
                    $custStaff = CRMCustomerStaff::read([CRMCustomerStaff::CUST =>($customerId ?? 0)]);
                    $this->Smarty->assign('cust_staff', $custStaff);
                } catch (CRMCustomerStaffException $e) {
                    $this->Smarty->assign('cust_staff', []);
                }
            } catch (CRMCustomersException $e) {
                $this->Smarty->assign('row', []);
            }
            
        } catch (CRMCustomerStaffException $e) {
            $this->Smarty->assign('cust_staff', []);
        } catch (Exception $e) {
        }
        try {
            $countries_list = Country::read([]);
            $this->Smarty->assign('countries', $countries_list);
        } catch (CountryException $e) {
            $this->Smarty->assign('countries', []);
        }
        try {
            $region_list = Region::read([ Region::COUNTRY_ID => $cust->country]);
            $this->Smarty->assign('region_list', $region_list);
        } catch (RegionException $e) {
            $this->Smarty->assign('region_list', []);
        }
        try {
            $cities_list = City::read([ City::COUNTRY_ID => $cust->country, City::REGION_ID => $cust->state ]);
            $this->Smarty->assign('cities_list', $cities_list);
        } catch (CityException $e) {
            $this->Smarty->assign('cities_list', []);
        }
        $this->Smarty->assign('opr_code', self::OPR_CODE);
        $this->assignURI('updateCustomerContact', 'customerPanel', 'updateCustomerContact');
    }

    /**
     * @param $parm
     * @param $post
     * @param $csrf CsrfManager
     */
    public function updateCustomerContact($parm, $post, $csrf)
    {
        try {
            $csrf->check('update_customer_contact', $post['csrf_input'], null, true);
            $customerId = (int) $parm[1];
            $customer = CRMCustomers::readID($customerId ?? 0);
            $post['country'] = (int) $post['country'];
            $customer->bindProperties($post);
            $customer->org_id = (int) $customer->org_id;
            $customer->activation = (int) $customer->activation;
            $customer->access = (int) $customer->access;
            $customer->type = (int) $customer->type;
            $customer->created_by = (int) $customer->created_by;
            $customer->save();
            Notification::updatedAlert();
        } catch (CRMCustomersException $e) {
//            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            // handle invalid csrf token calls.
        }
    }
    public function confirmRemoveEmployee($parm, $post, $csrf)
    {
        try{
            $staffId = (int) $parm[0];
            $row = CRMCustomerStaff::readID( $staffId ?? 0);
        }catch (CRMCustomerStaffException $e){
            $row = [];
        }
        $this->Smarty->assign('raw', $row);
        $this->assignURI('deleteCustomerStaff', 'customerPanel');
        $this->Smarty->assign('cust_id', $row->cust);
    }

    /**
     * @param $parm
     * @param $post
     * @param $csrf CsrfManager
     */
    public function deleteCustomerStaff($parm, $post, $csrf)
    {
        try {
            $csrf->check('delete_token', $post['csrf_input']);
            $staffId = (int) $post['staffToDelete'];
            $staff = CRMCustomerStaff::readID($staffId ?? 0);
            $staff->delete();
            Notification::deletedAlert();
        } catch (CRMCustomerStaffException $e) {} catch (Exception $e) {}
    }
    public function confirmRemoveEnv($parm, $post, $csrf)
    {
        try{
            $customerEnvId = (int) $parm[0];
            $row = CRMCustomerEnv::readID( $customerEnvId ?? 0);
        }catch (CRMCustomerEnvException $e){
            $row = [];
        }
        $this->Smarty->assign('row', $row);
        $this->assignURI('deleteCustomerEnv', 'customerPanel');
        $this->Smarty->assign('cust_id', $row->cust);
    }
    public function deleteCustomerEnv($parm, $post, $csrf)
    {
        try {
            $csrf->check('delete_token', $post['csrf_input']);
            $customerEnvId = (int) $post['envToDelete'];
            $staff = CRMCustomerEnv::readID( $customerEnvId ?? 0);
            $staff->delete();
            Notification::deletedAlert();
        } catch (CRMCustomerEnvException $e) {} catch (Exception $e) {}
    }
    public function editCustomerEnv($parm, $post, $csrf)
    {
        try{
            $customerEnvId = (int) $parm[0] ;
            $row = CRMCustomerEnv::readID( $customerEnvId ?? 0);
        }catch (CRMCustomerEnvException $e){
            $row = [];
        }
        $analysisType = $analysisPriorty = [];
        try {
            $analysisType = Setting::read([Setting::OPR_ID => Setting::ANALYSIS_TYPES]);
            $analysisPriorty = Setting::read([Setting::OPR_ID => Setting::ANALYSIS_PRIORITY]);
        } catch (SettingException $e) {
            // do nothing
        }
        $this->Smarty->assign('row', $row);
        $this->Smarty->assign('analysis_type', $analysisType);
        $this->Smarty->assign('analysis_priorty', $analysisPriorty);
        $this->assignURI('updateCustomerEnv', 'customerPanel');
        $this->Smarty->assign('cust_id', $row->cust);
    }

    /**
     * @param $parm
     * @param $post
     * @param $csrf CsrfManager
     */
    public function updateCustomerEnv($parm, $post, $csrf)
    {
        try {
            $csrf->check('cola', $post['update_csrf']);
            $customerEnvId = (int) $parm[1] ;
            $aEnv = CRMCustomerEnv::readID( $customerEnvId ?? 0);
            $aEnv->bindProperties($post);
            $aEnv->org_id = $_SESSION['organization']->id;
            $aEnv->save();
            Notification::updatedAlert();
        } catch (CRMCustomerEnvException $e) {} catch (Exception $e) {}
    }
    public function editCustomerStaff($parm, $post, $csrf)
    {
        try{
            $customerStaffId =(int) $parm[0];
            $row = CRMCustomerStaff::readID( $customerStaffId ?? 0);
        }catch (CRMCustomerStaffException $e){
            $row = [];
        }
        try {
            $cust = CRMJobNames::read(
                [
                    CRMJobNames::ORG_ID => $_SESSION['organization']->id,
                    CRMJobNames::ACTIVATION => Setting::ACTIVE_CASE
                ]
            );
            $this->Smarty->assign('job_types', $cust);
        } catch (CRMJobNamesException $e) {
//            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        }
        $this->Smarty->assign('row', $row);
        $this->assignURI('updateCustomerStaff', 'customerPanel');
        $this->Smarty->assign('cust_id', $row->cust);
    }
    public function updateCustomerStaff($parm, $post, $csrf)
    {
        try {
            $csrf->check('update_token', $post['csrf_input']);
            $customerStaffId = (int) $parm['1'];
            $staff = CRMCustomerStaff::readID( $customerStaffId ?? 0);
            $staff->bindProperties($post);
            $staff->org_id = $_SESSION['organization']->id;
            $staff->save();
            Notification::updatedAlert();
        } catch (CRMCustomerStaffException $e) {
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
        	$f = 'f';
            //// handle some csrf exception
        }
    }
    public function attachEmployeeToCustomer($parm, $post, $csrf)
    {
        try {
            $cust = CRMJobNames::read(
                [
                    CRMJobNames::ORG_ID => $_SESSION['organization']->id,
                    CRMJobNames::ACTIVATION => Setting::ACTIVE_CASE
                ]
            );
            $this->Smarty->assign('job_types', $cust);
        } catch (CRMJobNamesException $e) {
//            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        }
        $this->assignURI('addCustomerStaff', 'customerPanel');
        $this->Smarty->assign('cust_id', $parm[0]);
    }
    public function addCustomerStaff($parm, $post, $csrf)
    {
        try {
            $csrf->check('token', $post['csrf_input']);
            $staff = new CRMCustomerStaff();
            $staff->bindProperties($post);
            $staff->org_id = $_SESSION['organization']->id;
            $staff->save();
            Notification::createdAlert();
        } catch (CRMCustomerStaffException $e) {
//            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle some csrf exception
        }
    }
    public function addCustomerEnvAnalysis($parm, $post, $csrf)
    {
        $analysisType = $analysisPriorty = [];
        try {
            $analysisType = Setting::read([Setting::OPR_ID => Setting::ANALYSIS_TYPES]);
        } catch (SettingException $e) {
            $analysisType = [];
        }
        try {
            $analysisPriorty = Setting::read([Setting::OPR_ID => Setting::ANALYSIS_PRIORITY]);
        } catch (SettingException $e) {
            $analysisPriorty = [];
        }
        $this->Smarty->assign('analysis_type', $analysisType);
        $this->Smarty->assign('analysis_priorty', $analysisPriorty);
        $this->assignURI('storeEnvAnalysis', 'customerPanel');
        $this->Smarty->assign('cust_id', $parm[0]);
    }
    /**
    * @SuppressWarnings(PHPMD)
    **/
    public function storeEnvAnalysis($parm, $post, $csrf)
    {
        try {
            $csrf->check('token', $post['csrf_input']);
            $aEnv = new CRMCustomerEnv();
            $aEnv->bindProperties($post);
            $aEnv->org_id = $_SESSION['organization']->id;
            $aEnv->save();

            Notification::createdAlert();
        } catch (CRMCustomerEnvException $e) {
//            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle some csrf exception
        }
    }
    /**
     * assign URI
     *
     * @param string $pageName
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     */
    private function assignURI($actionName, $pageName = 'show', $smarty_name = 'base_uri')
    {
        $this->Smarty->assign($smarty_name, "{$_SESSION['bnd']}/{$_SESSION['prg']}/{$_SESSION['oprname']}/{$pageName}/0/{$_SESSION['lang']}/{$actionName}");
    }
    public function print($parm, $post)
    {
	    try {
		    $this->Smarty->assign('rows', CRMCustomers::read([
			                                                     CRMCustomers::ORG_ID => $_SESSION['organization']->id,
			                                                     CRMCustomers::ACTIVATION => Setting::ACTIVE_CASE
		                                                     ]));
	    } catch (CRMCustomersException $e) {
		    $this->Smarty->assign('rows', []);
	    }
	    $this->Smarty->assign('opr_code', self::OPR_CODE);
        
        // Output as PDF
        DocumentProcessor::outputPDF(
            $this->Smarty->fetch(
                DocumentProcessor::setSmartyFetchConfig()
            )
        );
    }
}
