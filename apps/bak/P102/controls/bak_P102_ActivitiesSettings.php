<?php
// Deny Direct Script Access
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class bak_P102_ActivitiesSettings extends Controller
{
    const SESSION_TOKEN = 's_activities_token';
    const CRUDS = ['insert', 'update', 'delete'];
    const OPR_CODE = 'ActivitiesSettings';
    const ACT_SEEDING = [
        'مكالمة هاتفية',
        'رسالة هاتفية',
        'بريد إلكتروني',
        'زيارة العميل',
        'إجتماع'
    ];
    /**
     * شاشة عرض ضبط الأنشطة
     * @param Request $parm fsfdgd
     * @param mixed $post
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    public function show($parm, $post)
    {
        if (is_callable([$this, $parm[0]]) && in_array($parm[0], self::CRUDS)) {
            call_user_func_array([$this, $parm[0]], [$parm, $post, new CsrfManager()]);
        }
        try {
            $activitiesCount = CRMActivities::count([]);
        } catch (CRMActivitiesException $e) {
            $activitiesCount = 0;
        }
        if ($activitiesCount < 1){
            foreach (self::ACT_SEEDING as $activityName) {
                $activity = new CRMActivities();
                $activity->name = $activityName;
                $activity->activation = Setting::ACTIVE_CASE;
                $activity->activity_type = CRMActivities::ACT_TYPE;

                try {
                    $activity->save();
                } catch (CRMActivitiesException $e) {}
            }
        }
        try{
            $activities = CRMActivities::read();
            $this->Smarty->assign('raws', $activities);
            $this->Smarty->assign('raws_count', $activities);
        }catch (CRMActivitiesException $e){
            $this->Smarty->assign('raws',[]);
        }
        $linkedTypes = [];
        try {
            foreach (CRMTasks::read([]) as $task) {
                array_push($linkedTypes, (int) $task->actsettings_id);
            }
        } catch (CRMTasksException $e) {}

        $this->Smarty->assign('linked_types', $linkedTypes);
        $this->Smarty->assign('opr_code', self::OPR_CODE);
    }
     /**
     * Add Product
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.UnusedLocalParameters)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function insert($parm, $post, $csrf)
    {
        try{
            $csrf->check('token', $post['csrf_input']);
            $customerType= new CRMActivities();
            $customerType->bindProperties($post);
            $customerType->org_id = $_SESSION['organization']->id;
            $customerType->created_by = $_SESSION['user']->id;
            $customerType->save();
            Notification::createdAlert();
        }catch (CRMActivitiesException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// csrf exceptions
        }
    }
    /**
     * Update JobType
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function update($parm, $post, $csrf)
    {
        try{
            $csrf->check('update_token', $post['csrf_input']);
            $customerType = CRMActivities::readID((int) $parm[1] ?? 0);
            $customerType->bindProperties($post);
            $customerType->update();
            Notification::updatedAlert();
        } catch (CRMActivitiesException $e){
//            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle csrf exceptions
        }
    }
    /**
     * Delete Activity
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    private function delete($parm, $post, $csrf)
    {
        try {
            $csrf->check('delete_token', $post['csrf_input']);
            $customerType = CRMActivities::readID((int) $parm[1] ?? 0);
            $customerType->delete();
            Notification::deletedAlert();
        } catch (CRMActivitiesException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle some csrf exceptions
        }
    }

    public function add($parm, $post)
    {
        $this->assignURI('insert');
    }

    public function edit($parm, $post)
    {
        try{
            $this->Smarty->assign('raw', CRMActivities::readID((int) $parm[0] ?? 0));
        }catch (CRMActivitiesException $e){
            $this->Smarty->assign('raw',[]);
        }
        $this->assignURI('update');
    }

    public function confirm($parm, $post)
    {
        try{
            $this->Smarty->assign('raw', CRMActivities::readID((int) $parm[0] ?? 0));
        }catch (CRMActivitiesException $e){
            $this->Smarty->assign('raw',[]);
        }
        $this->assignURI('delete');
    }
    /**
     * assign URI
     *
     * @param string $pageName
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     */
    private function assignURI($actionName, $pageName = 'show')
    {
        $this->Smarty->assign('base_uri', "{$_SESSION['bnd']}/{$_SESSION['prg']}/{$_SESSION['oprname']}/{$pageName}/0/{$_SESSION['lang']}/{$actionName}");
    }
}
