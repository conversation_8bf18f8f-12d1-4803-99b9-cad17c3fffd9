<?php
/**
 * Deny Direct Script Access
 *
 * @category Controller
 * <AUTHOR> <<EMAIL>>
 * @SuppressWarnings(PHPMD.CamelCaseClassName)
 */
class bak_P102_CustomersAccessibilitySettings extends Controller
{
    const SESSION_TOKEN = 's_crmCustomerAccessiblity_token';
    const CRUDS = ['insert', 'update', 'delete'];
    const OPR_CODE = 'CustomersAccessibilitySettings';
    /**
     * شاشة عرض ضبط الوصول للعميل
     *
     * @param Request $parm fsfdgd
     * @param mixed $post
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    public function show($parm, $post)
    {
        if (is_callable([$this, $parm[0]]) && in_array($parm[0], self::CRUDS)) {
            call_user_func_array([$this, $parm[0]], [$parm, $post, new CsrfManager()]);
        }

        try{
            $this->Smarty->assign('raws', CRMCustomerAccessablity::read([
                CRMCustomerAccessablity::ORG_ID => $_SESSION['organization']->id
            ]));
        }catch (CRMCustomerAccessablityException $e){
            $this->Smarty->assign('raws',[]);
        }
        $this->Smarty->assign('opr_code', self::OPR_CODE);
        $linkedTypes = [];
        try {
            foreach (CRMCustomers::read() as $customer) {
                array_push($linkedTypes, (int) $customer->access);
            }
        } catch (CRMCustomersException $e) {}
        $this->Smarty->assign('linked_types', $linkedTypes);
    }
    /**
     * Add JobType
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.UnusedLocalParameters)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function insert($parm, $post, $csrf)
    {
        try{
            $csrf->check('token', $post['csrf_input']);
            $crmCustAcc=new CRMCustomerAccessablity();
            $crmCustAcc->bindProperties($post);
            $crmCustAcc->org_id = $_SESSION['organization']->id;
            $crmCustAcc->created_by = $_SESSION['user']->id;
            // إجعله غير فعال في حالة عدم تفعيله إفتراضيا
            $crmCustAcc->activation = $post['activation'] ?? 653;
            $crmCustAcc->save();
            Notification::createdAlert();
        }catch (CRMCustomerAccessablityException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// csrf exceptions
        }
    }
    /**
     * Update JobType
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function update($parm, $post, $csrf)
    {
        try{
            $csrf->check('update_token', $post['csrf_input']);
            $crmCustAcc = CRMCustomerAccessablity::readID((int) $parm[1] ?? 0);
            $crmCustAcc->bindProperties($post);
            $crmCustAcc->update();
            Notification::updatedAlert();
        }catch (CRMCustomerAccessablityException $e){
////            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle csrf exceptions
        }
    }
    /**
     * Delete JobType
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    private function delete($parm, $post, $csrf)
    {
        try{
            $csrf->check('delete_token', $post['csrf_input']);
            $crmCustAcc = CRMCustomerAccessablity::readID((int) $parm[1] ?? 0);
            $crmCustAcc->delete();
            Notification::deletedAlert();
        }catch (CRMCustomerAccessablityException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle some csrf exceptions
        }
    }
    /**
     * Add Job Type
     * @param mixed $parm , $post
     * @return void
     */
    public function add()
    {
        $this->assignURI('insert');
    }

    /**
     * Edit Job Type
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function edit($parm)
    {
        try{
            $this->Smarty->assign('raw', CRMCustomerAccessablity::readID((int) $parm[0] ?? 0));
        }catch (CRMCustomerAccessablityException $e){
            $this->Smarty->assign('raw',[]);
        }
        $this->assignURI('update');
    }
    /**
     * confirm deletion
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function confirm($parm)
    {
        try{
            $this->Smarty->assign('raw', CRMCustomerAccessablity::readID((int) $parm[0] ?? 0));
        }catch (CRMCustomerAccessablityException $e){
            $this->Smarty->assign('raw',[]);
        }
        $this->assignURI('delete');
    }
    /**
     * assign URI
     *
     * @param string $pageName
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     */
    private function assignURI($actionName, $pageName = 'show')
    {
        $this->Smarty->assign('base_uri', "{$_SESSION['bnd']}/{$_SESSION['prg']}/{$_SESSION['oprname']}/{$pageName}/0/{$_SESSION['lang']}/{$actionName}");
    }
}