<?php
/**
 * Deny Direct Script Access
 *
 * @category Controller
 * <AUTHOR> <<EMAIL>>
 * @SuppressWarnings(PHPMD.CamelCaseClassName)
 */
defined('ENVIRONMENT') or exit('No Direct Access Allowed!');
class bak_P102_CustomersTypesSettings extends Controller
{
    const SESSION_TOKEN = 's_customerTypes_token';
    const CRUDS = ['insert', 'update', 'delete'];
    const OPR_CODE = 'CustomersTypesSettings';
    /**
     * Show Customers Types Page
     *
     * @param  $parm $post
     *
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     * @SuppressWarnings(PHPMD.Superglobals)
     */
    public function show($parm, $post)
    {
        if (is_callable([$this, $parm[0]]) && in_array($parm[0], self::CRUDS)) {
            call_user_func_array([$this, $parm[0]], [$parm, $post, new CsrfManager()]);
        }
        try {
            $this->Smarty->assign('customerTypes', CRMCustomersTypes::read());
        } catch (CRMCustomersTypesException $e) {
            $this->Smarty->assign('customerTypes', []);
        }

        $this->Smarty->assign('opr_code', self::OPR_CODE);
        $linkedTypes = [];
        try {
            foreach (CRMCustomers::read() as $customer) {
                array_push($linkedTypes, (int) $customer->type);
            }
        } catch (CRMCustomersException $e) {}
        $this->Smarty->assign('linked_types', $linkedTypes);
    }
    /**
     * Add CustomerType
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     */
    private function insert($parm, $post, $csrf)
    {
        try {
            $csrf->check('token', $post['csrf_input']);
            $customerType=new CRMCustomersTypes();
            $customerType->bindProperties($post);
            $customerType->org_id     = $_SESSION['organization']->id;
            $customerType->created_by = $_SESSION['user']->id;
            // @default is Not Activated
            $customerType->activation = $post['activation'] ?? 653;
            $customerType->save();

            Notification::createdAlert();
        } catch (CRMCustomersTypesException $e) {
            // Todo user feedback
        } catch (Exception $e) {
            //// csrf exceptions
        }
    }
    /**
     * Update CustomerType
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function update($parm, $post, $csrf)
    {
        try {
            $csrf->check('update_token', $post['csrf_input']);
            $customerType = CRMCustomersTypes::readID((int) $parm[1] ?? 0);
            $customerType->bindProperties($post);
            $customerType->update();

            Notification::updatedAlert();
        } catch (CRMCustomersTypesException $e) {
        } catch (Exception $e) {
            //// handle csrf exceptions
        }
    }
    /**
     * Delete CustomerType
     *
     * @param array $parm
     * @param array $post
     * @param CsrfManager $csrf
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    private function delete($parm, $post, $csrf)
    {
        try {
            $csrf->check('delete_token', $post['csrf_input']);
            $customerType = CRMCustomersTypes::readID((int) $parm[1] ?? 0);
            $customerType->delete();

            Notification::deletedAlert();
        } catch (CRMCustomersTypesException $e) {
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle some csrf exceptions
        }
    }
    /**
     * Add Customer Type
     * @param mixed $parm , $post
     * @return void
     */
    public function add()
    {
        $linkedTypes = [];
        try {
            foreach (CRMCustomers::read() as $customer) {
                array_push($linkedTypes, $customer->type);
            }
        } catch (CRMCustomersException $e) {}
        $this->Smarty->assign('linked_types', $linkedTypes);
//        $this->Smarty->assign('is_customer_type_linked', self::isExistInArray($id, $arr));
        $this->assignURI('insert');
    }

    private static function isExistInArray($id, $arr){
        return in_array($id, $arr);
    }
    /**
     * Edit Customer Type
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function edit($parm)
    {
        try {
            $custTypes = CRMCustomersTypes::readID((int) $parm[0] ?? 0);
            $this->Smarty->assign('customerTypes', $custTypes);
        } catch (CRMCustomersTypesException $e) {
            $this->Smarty->assign('customerTypes', []);
        }
        $linkedTypes = [];
        try {
            foreach (CRMCustomers::read() as $customer) {
                array_push($linkedTypes, (int) $customer->type);
            }
        } catch (CRMCustomersException $e) {}
        $this->Smarty->assign('linked_types', $linkedTypes);
        $this->assignURI('update');
    }
    /**
     * confirm deletion
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function confirm($parm)
    {
        try {
            $this->Smarty->assign('raw', CRMCustomersTypes::readID((int) $parm[0] ?? 0));
        } catch (CRMCustomersTypesException $e) {
            $this->Smarty->assign('raw', []);
        }
        $this->assignURI('delete');
    }
    /**
     * assign URI
     *
     * @param string $pageName
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     */
    private function assignURI($actionName, $pageName = 'show')
    {
        $this->Smarty->assign('base_uri', "{$_SESSION['bnd']}/{$_SESSION['prg']}/{$_SESSION['oprname']}/{$pageName}/0/{$_SESSION['lang']}/{$actionName}");
    }
}
