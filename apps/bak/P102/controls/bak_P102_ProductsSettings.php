<?php
/**
 * Deny Direct Script Access
 *
 * @category Controller
 * <AUTHOR> <<EMAIL>>
 * @SuppressWarnings(PHPMD.CamelCaseClassName)
 */
class bak_P102_ProductsSettings extends Controller
{
    const SESSION_TOKEN = 's_crmProducts_token';
    const CRUDS = ['insert', 'update', 'delete', 'updateProduct', 'storeProductRow', 'editproductrow', 'confirmproductrow', 'updateProductRow', 'deleteProductRow'];
    const CRUDS_PR = ['addproductrow', 'editproductrow'];
    const OPR_CODE = 'ProductsSettings';
    /**
     * @var string
     */
    const S_SCOPE_TAB = 's_scope_tab';
    /**
     * @var string
     */
    const S_SCOPE_ID = 's_scope_id';
    /**
     * شاشة عرض ضبط المنتجات
     *
     * @param Request $parm fsfdgd
     * @param mixed $post
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    public function show($parm, $post)
    {
        $_SESSION[self::S_SCOPE_TAB] = ($parm[0] == 'menu') ? 'scope_tab' : 'products_tab';
        if ($parm[0] == 'save_session'){
            $_SESSION[self::S_SCOPE_ID] = $parm[1];
            $_SESSION[self::S_SCOPE_TAB] = 'products_tab';
        }
        if (is_callable([$this, $parm[0]]) && in_array($parm[0], self::CRUDS)) {
            call_user_func_array([$this, $parm[0]], [$parm, $post, new CsrfManager()]);
        }

        try{
            $this->Smarty->assign('raws', CRMProducts::read([
                CRMProducts::ORG_ID => $_SESSION['organization']->id
            ]));
        }catch (CRMProductsException $e){
            $this->Smarty->assign('raws',[]);
        }
        try {
            $this->Smarty->assign('productsRowList', CRMProductsRow::read([ CRMProductsRow::PRODUCTR => $_SESSION[self::S_SCOPE_ID]]));
        } catch (CRMProductsRowException $e) {
            $this->Smarty->assign('productsRowList', []);
        }
        $this->Smarty->assign('opr_code', self::OPR_CODE);
        $this->assignURIV2('products_uri', 'show', "/save_session");
        $this->assignURIV2('add_productrow_uri', 'addproductrow');
        $this->assignURIV2('edit_productrow_uri', 'editproductrow');
        $this->assignURIV2('confirm_productrow_uri', 'confirmproductrow');
    }

    public function productRowCRUDS($parm, $post)
    {
        if (is_callable([$this, $parm[0]]) && in_array($parm[0], self::CRUDS_PR)) {
            call_user_func_array([$this, $parm[1]], [$parm, $post, new CsrfManager()]);
        }
    }
    /**
     * Add Product
     *
     * @param array $parm
     * @param array $post
     * @param CsrfManager $csrf
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.UnusedLocalParameters)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function insert($parm, $post, $csrf)
    {
        try{
            $csrf->check('token', $post['csrf_input']);
            $crmProduct = new CRMProducts();
            $crmProduct->bindProperties($post);
            $crmProduct->org_id = $_SESSION['organization']->id;
            $crmProduct->created_by = $_SESSION['user']->id;
            // إجعله غير فعال في حالة عدم تفعيله إفتراضيا
            $crmProduct->activation = $post['activation'] ?? 653;
            $crmProduct->save();

            Notification::createdAlert();
        }catch (CRMProductsException $e){} catch (Exception $e) {}
        $_SESSION[self::S_SCOPE_TAB] =  'scope_tab';
    }
    /**
     * Update JobType
     *
     * @param array $parm
     * @param array $post
     * @param CsrfManager $csrf
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function update($parm, $post, $csrf)
    {
        try{
            $csrf->check('update_token', $post['csrf_input']);
            $crmProduct = CRMProducts::readID((int) $parm[1] ?? 0);
            $crmProduct->bindProperties($post);
            $crmProduct->update();
        }catch (CRMProductsException $e){} catch (Exception $e) {}
        $_SESSION[self::S_SCOPE_TAB] =  'scope_tab';
    }
    /**
     * Update Product Object
     *
     * @param array $parm
     * @param array $post
     * @param CsrfManager $csrf
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    private function updateProduct($parm, $post, $csrf)
    {
        try{
            $csrf->check('update_product_token', $post['csrf_input']);
            $post['activation'] = ($post['activation'] == 652 ) ? '652': '653';
            $post['objectivePurchaseable'] = isset($post['objectivePurchaseable']) ? CRMProducts::OBJECTIVE_PURCHASEABLE :  0;
            $post['objectiveForSale'] = isset($post['objectiveForSale']) ? CRMProducts::OBJECTIVE_FOR_SALE :  0;

            unset($post['csrf_input']);
            $productObject = json_encode($post, true);
            $crmProduct = CRMProducts::readID((int) $parm[1] ?? 0);
            $crmProduct->obj = $productObject;
            $crmProduct->update();

            Notification::updatedAlert();
        }catch (CRMProductsException $e){} catch (Exception $e) {}
    }
    /**
     * Delete Product
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    private function delete($parm, $post, $csrf)
    {
        try{
            $csrf->check('delete_token', $post['csrf_input']);
            $crmProduct = CRMProducts::readID((int) $parm[1] ?? 0);
            $crmProduct->delete();

            Notification::deletedAlert();
        }catch (CRMProductsException $e){} catch (Exception $e) {}
        $_SESSION[self::S_SCOPE_TAB] =  'scope_tab';
    }
    /**
     * update scope with product object
     *
     * @param array $parm
     * @param array $post
     * @param CsrfManager $csrf
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    public function productsView($parm, $post, $csrf)
    {
        try{
            $products = CRMProductsRow::read([ CRMProductsRow::PRODUCTR => (int) $parm[0] ?? 0]);
        }catch (CRMProductsRowException $e){
            $products = [];
        }
        $this->Smarty->assign('raw', $products);
        $this->assignURI('updateProduct');
    }
    public function addproductrow($parm, $post, $csrf)
    {
        try {
            $this->Smarty->assign('raw', CRMProducts::readID((int)$_SESSION[ self::S_SCOPE_ID ]));
        } catch (CRMProductsException $e) {
            $this->Smarty->assign('raw', []);
        }
        try {
            $this->Smarty->assign('obj_options', Setting::read([ Setting::OPR_ID => CRMProducts::OBJ_OPTIONS ]));
        } catch (SettingException $e) {
            $this->Smarty->assign('obj_options', []);
        }
        $this->assignURIV2('base_uri', 'show');
    }
    public function editproductrow($parm, $post, $csrf)
    {
        try {
            $this->Smarty->assign('praw', CRMProductsRow::readID((int) $parm[0]));
        } catch (CRMProductsRowException $e) {
            $this->Smarty->assign('praw', []);
        }
//        try {
//            $this->Smarty->assign('raw', CRMProducts::readID((int)$_SESSION[ self::S_SCOPE_ID ]));
//        } catch (CRMProductsException $e) {
//            $this->Smarty->assign('raw', []);
//        }
        try {
            $this->Smarty->assign('obj_options', Setting::read([ Setting::OPR_ID => CRMProducts::OBJ_OPTIONS ]));
        } catch (SettingException $e) {
            $this->Smarty->assign('obj_options', []);
        }
        $this->assignURIV2('base_uri', 'show');
    }
    public function confirmproductrow($parm, $post, $csrf)
    {
        try {
            $this->Smarty->assign('raw', CRMProductsRow::readID((int) $parm[0]));
        } catch (CRMProductsRowException $e) {
            $this->Smarty->assign('raw', []);
        }
        $this->assignURIV2('base_uri', 'show');
    }
    public function storeProductRow($parm, $post, $csrf)
    {
        try{
            $csrf->check('update_product_row_token', $post['csrf_input']);
//            $crmProduct = CRMProducts::readID((int) $parm[1] ?? 0);
//            $crmProduct->delete();
            $productRow = new CRMProductsRow();
            $productRow->bindProperties($post);
            $productRow->save();
            Notification::createdAlert();
            $_SESSION[self::S_SCOPE_TAB] =  'products_tab';
        }catch (CRMProductsRowException $e){} catch (Exception $e) {}
    }
    public function updateProductRow($parm, $post, $csrf)
    {
        try{
            $csrf->check('update_product_row_token', $post['csrf_input']);
//            $crmProduct = CRMProducts::readID((int) $parm[1] ?? 0);
//            $crmProduct->delete();
            $productRow = CRMProductsRow::readID((int) $parm[1] ?? 0);
            $productRow->bindProperties($post);
            $productRow->update();
            Notification::updatedAlert();
//            $_SESSION[self::S_SCOPE_TAB] =  'products_tab';

        }catch (CRMProductsRowException $e){} catch (Exception $e) {}
    }
    public function deleteProductRow($parm, $post, $csrf)
    {
        try{
            $csrf->check('delete_token', $post['csrf_input']);
            $productRow = CRMProductsRow::readID((int) $parm[1] ?? 0);
            $productRow->delete();
            Notification::deletedAlert();
            $_SESSION[self::S_SCOPE_TAB] =  'products_tab';
        }catch (CRMProductsRowException $e){} catch (Exception $e) {}
    }
    /**
     * Add Product
     * @param mixed $parm , $post
     * @return void
     */
    public function add()
    {
        $this->assignURI('insert');
        $_SESSION[self::S_SCOPE_TAB] = 'scope_tab';
    }

    /**
     * Edit Job Type
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function edit($parm)
    {
        try{
            $this->Smarty->assign('raw', CRMProducts::readID((int) $parm[0] ?? 0));
        }catch (CRMProductsException $e){
            $this->Smarty->assign('raw',[]);
        }
        $this->assignURI('update');
    }
    /**
     * confirm deletion
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function confirm($parm)
    {
        try{
            $this->Smarty->assign('raw', CRMProducts::readID((int) $parm[0] ?? 0));
        }catch (CRMProductsException $e){
            $this->Smarty->assign('raw',[]);
        }
        $this->assignURI('delete');
    }
    /**
     * assign URI
     *
     * @param string $pageName
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     */
    private function assignURI($actionName, $pageName = 'show')
    {
        $this->Smarty->assign('base_uri', "{$_SESSION['bnd']}/{$_SESSION['prg']}/{$_SESSION['oprname']}/{$pageName}/0/{$_SESSION['lang']}/{$actionName}");
    }
    /**
     * @param string $smVarName
     * @param string $method
     * @param        $prefix
     */
    private function assignURIV2(string $smVarName, string $method, $prefix = null)
    {
        $this->Smarty->assign($smVarName, "{$_SESSION['bnd']}/{$_SESSION['prg']}/{$_SESSION['oprname']}/{$method}/0/{$_SESSION['lang']}{$prefix}");
    }
}
