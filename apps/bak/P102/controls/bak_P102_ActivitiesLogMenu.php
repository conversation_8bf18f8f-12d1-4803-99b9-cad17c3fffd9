<?php
/**
 * Deny Direct Script Access
 *
 * @category Controller
 * <AUTHOR> <<EMAIL>>
 * @SuppressWarnings(PHPMD.CamelCaseClassName)
 */
class bak_P102_ActivitiesLogMenu extends Controller
{
    const SESSION_TOKEN = 's_ActivitiesLogMenu_token';
    const CRUDS = ['save_session', ''];
    const OPR_CODE = 'ActivitiesLogMenu';
    /**
     * show created customers
     *
     * @param array $parm
     * @param mixed $post
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    public function show($parm, $post)
    {
        if (is_callable([$this, $parm[0]]) && in_array($parm[0], self::CRUDS)) {
            call_user_func_array([$this, $parm[0]], [$parm, $post, new CsrfManager()]);
        }

        try{
            $this->Smarty->assign('customers', CRMCustomers::read([
                CRMCustomers::ACTIVATION => 652
            ]));
        }catch (CR<PERSON><PERSON>omersException $e){
            $this->Smarty->assign('customers',[]);
        }

        try{
            $this->Smarty->assign('customer', CRMCustomers::readID((int) $parm[1] ?? 0));
        }catch (CRMCustomersException $e){
            $this->Smarty->assign('customer',[]);
        }

        $userProjects = CRMProjects::userProjectsByCustomer((int) $parm[1] ?? 0);
        try{
            $this->Smarty->assign('projects', $userProjects);
        }catch (CRMCustomersException $e){
            $this->Smarty->assign('projects', []);
        }

        $this->Smarty->assign('opr_code', self::OPR_CODE);
        $this->assignURI('save_session');
    }
    public function save_session($parm, $post, $csrf)
    {
        $_SESSION[self::SESSION_TOKEN] = $parm[1];
        try{
            $this->Smarty->assign('customer', CRMCustomers::readID((int) $parm[1] ?? 0));
        }catch (CRMCustomersException $e){
            $this->Smarty->assign('customer',[]);
        }
    }
    /**
     * assign URI
     *
     * @param string $pageName
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     */
    private function assignURI($actionName = '', $pageName = 'show')
    {
        $this->Smarty->assign('base_uri', "{$_SESSION['bnd']}/{$_SESSION['prg']}/{$_SESSION['oprname']}/{$pageName}/0/{$_SESSION['lang']}/{$actionName}");
    }
}