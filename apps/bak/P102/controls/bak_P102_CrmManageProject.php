<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON>
 * Date: 7/4/17
 * Time: 12:28 PM
 */

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

class bak_P102_CrmManageProject extends Controller
{
    const S_CRM_MANAGE_PROJECT_ID = 's_CrmManageProject_id';

    const S_CRM_MANAGE_PROJECT_TOKEN = 's_CrmManageProject_token';

    const S_CRM_MANAE_PROJECT_TASK_ID = 's_CrmManageProject_task_id';

    public function show($parm, $post)
    {
        $actrow = null;
        if (isset($_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ])) {
            try {
                $actrow = CRMProjects::readID((int)$_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ]);
            } catch (CRMProjectsException $e) {
            }
        }
        switch ($parm[0]) {
            case 'save_activity_id':
                $_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ] = $post[ 'activity_id'];
                break;
            case 'activity':
                switch ($parm[1]) {
                    case 'insert':
                        if ($parm[2] === $_SESSION[ self::S_CRM_MANAGE_PROJECT_TOKEN ]) {
                            $check_date = $this->Date->get_date($_SESSION['user']->def_datetype, (new DateTime
                            ($actrow->start_date))->sub(new DateInterval("P1D"))->format('Y-m-d'));

                            // Check if the validation passes
                            $this->insertTask($post, $parm);
                        }
                        break;
                    case 'update':
                        if ($parm[2] === $_SESSION[ self::S_CRM_MANAGE_PROJECT_TOKEN ]) {
                            $check_date = $this->Date->get_date($_SESSION['user']->def_datetype, (new DateTime
                            ($actrow->start_date))->sub(new DateInterval("P1D"))->format('Y-m-d'));
                            $this->updateTask($parm, $post);
                        }
                        break;
                    case 'delete':
                        if ($parm[2] === $_SESSION[ self::S_CRM_MANAGE_PROJECT_TOKEN ]) {
                            $this->deleteTask($parm);
                        }
                        break;
                }
                $_SESSION[ self::S_CRM_MANAGE_PROJECT_TOKEN ] = md5(rand(0, 9999));
                $_SESSION['s_prepare_tab'] = 3;
                break;
            case 'approve':
                try {
                    $task = CRMTasks::readID((int)$parm[1]);
                    $task->proved = $parm[2] ?? 0;
                    $task->save();
                } catch (CRMTasksException $e) {
                }
                break;
            case 'lockorunlock':
                try {
                    $task = CRMTasks::readID((int)$parm[1]);
                    $task->locked = $parm[2] ?? 0;
                    $task->save();
                } catch (CRMTasksException $e) {
                }
                break;
        }
        try {
            $this->Smarty->assign('activities_list', CRMProjects::userProjects());
        } catch (CRMProjectsException $e) {
        }

        if (isset($_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ])) {
            try {
                $actrow = CRMProjects::readID((int)$_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ]);
            } catch (CRMProjectsException $e) {
                $actrow = [];
            }
            /**
             * All the below fields are dynamically created
             * Not in original CRMTasks resource
             * One time use
             */
            try {
                $indicators = CRMIndicator::read([
                    CRMIndicator::PROJECT_ID => (int) $actrow->id
                ]);
                foreach ($indicators as $indicator) {
                    $excuted = CRMTasks::executedTasks($_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ], [CRMTasks::ACTSETTINGS_ID => (int) $indicator->activity_id]);
                    $indicator->precent_of_executed =  $excuted / $indicator->count;
                    $indicator->executed_no = $excuted;
                }
            } catch (CRMIndicatorException $e) {
                $indicators = [];
            }
            $actrow->indicators = $indicators;
            $actrow->executed_tasks = CRMTasks::executedTasks($_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ]);
            $actrow->not_executed_tasks = CRMTasks::notExecutedTasks($_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ]);
            $actrow->pending_tasks = CRMTasks::pendingTasks($_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ]);
            $actrow->benefic_sum = CRMTasks::beneficSum($_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ]);
            $actrow->opened_tasks = CRMTasks::openedTasks($_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ]);
            $actrow->closed_tasks = CRMTasks::closedTasks($_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ]);
            try {
                $actrow->sum_of_indicator_times = CRMIndicator::sumOfExecutionTimes($actrow);
                $actrow->sum_of_indicator_times_for_user = CRMIndicator::sumOfExecutionTimesForUser();
                $actrow->sum_of_indicator_count = CRMIndicator::sumOfBeneficsCount($actrow);
            } catch (CRMIndicatorException $e) {}

            $this->Smarty->assign('actrow', $actrow);

            try {
                $this->Smarty->assign('user_prvs', explode(',', CRMTeam::read([
                                                                                  CRMTeam::PROJECT_ID => $_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ],
                                                                                  CRMTeam::USER_ID => $_SESSION['user']->id
                                                                              ])[0]->prvs));
            } catch (CRMTeamException $e) {
            }
            try {
                $t = CRMTeam::read([
                    CRMTeam::PROJECT_ID => (int) $_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ],
                    CRMTeam::USER_ID => $_SESSION['user']->id
                ])[0];
                $this->Smarty->assign('user_team', $t);
            } catch (CRMTeamException $e) {
            }

            $this->Smarty->assign('priority_list', ProjectPriority::getPriorityList());
            $this->Smarty->assign('exec_list', Setting::read([
                                                                 Setting::OPR_ID => 92
                                                             ]));
            $this->Smarty->assign('stts_list', Setting::read([
                                                                 Setting::OPR_ID => 96
                                                             ]));

        }

        $_SESSION[ self::S_CRM_MANAGE_PROJECT_TOKEN ] = md5(rand(0, 9999));
    }

    public function activityadd($parm, $post)
    {
        $actrow = null;
        try {
            $actrow = CRMProjects::readID((int) $parm[0]);
        } catch (CRMProjectsException $e) {
        }
        $this->Smarty->assign('actrow', $actrow);
        try {
            $team = CRMTeam::read([
                                                                 CRMTeam::PROJECT_ID => $_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ],
                                                                 CRMTeam::USER_ID => $_SESSION['user']->id
                                                             ]);
        } catch (CRMTeamException $e) {
            $team = [];
        }
        $this->Smarty->assign('user_team', $team[0]);
        $this->Smarty->assign('priority_list', ProjectPriority::getPriorityList());
        $this->Smarty->assign('connection_list', Setting::read([
                                                                   Setting::OPR_ID => 180
                                                               ]));
        $this->Smarty->assign('gender_list', Setting::read([
                                                               Setting::OPR_ID => 27
                                                           ]));

        try {
            $activity_options = CRMActivities::read([CRMActivities::ACTIVATION => Setting::ACTIVE_CASE]);
        } catch (CRMActivitiesException $e) {
            $activity_options = [];
        }
        $this->Smarty->assign('activity_options', $activity_options);
        try {
            $customerStaff = CRMCustomerStaff::read([
                CRMCustomerStaff::CUST => $actrow->cust
            ]);
        } catch (CRMCustomerStaffException $e) {
            $customerStaff = [];
        }
        $this->Smarty->assign('customer_staff', $customerStaff);
    }

    public function activityedit($parm, $post)
    {
        $actrow = null;
        try {
            $actrow = CRMProjects::readID((int)$_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ]);
        } catch (CRMProjectsException $e) {
        }
        $this->Smarty->assign('actrow', $actrow);
        try {
            $this->Smarty->assign('user_team', CRMTeam::read([
                                                                 CRMTeam::PROJECT_ID => $_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ],
                                                                 CRMTeam::USER_ID => $_SESSION['user']->id
                                                             ])[0]);
        } catch (CRMTeamException $e) {
        }
        $this->Smarty->assign('priority_list', ProjectPriority::getPriorityList());
        $this->Smarty->assign('connection_list', Setting::read([
                                                                   Setting::OPR_ID => 180
                                                               ]));
        $this->Smarty->assign('gender_list', Setting::read([
                                                               Setting::OPR_ID => 27
                                                           ]));

        try {
            $this->Smarty->assign('taskRow', CRMTasks::readID((int)$parm[0]));
        } catch (CRMTasksException $e) {
        }

        try {
            $activity_options = CRMActivities::read([CRMActivities::ACTIVATION => Setting::ACTIVE_CASE]);
        } catch (CRMActivitiesException $e) {
            $activity_options = [];
        }
        $this->Smarty->assign('activity_options', $activity_options);
        try {
            $customerStaff = CRMCustomerStaff::read([
                CRMCustomerStaff::CUST => $actrow->cust
            ]);
        } catch (CRMCustomerStaffException $e) {
            $customerStaff = [];
        }
        $this->Smarty->assign('customer_staff', $customerStaff);
    }

    public function activityconfirm($parm, $post)
    {
        try {
            $this->Smarty->assign('row', CRMTasks::readID((int)$parm[0]));
        } catch (CRMTasksException $e) {
        }
    }

    public function browse($parm, $post)
    {
        if (isset($parm[0]) && ((int)$parm[0]) > 0) {
            $_SESSION[self::S_CRM_MANAE_PROJECT_TASK_ID] = (int)$parm[0];
            if ($parm[1] == 'update_notification') {
                $table = 'discuss_crm_tasks';
                $type = 1;
                if ($parm[3]) {
                    $table = 'crm_tasks';
                    $type = 2;
                }
                $_SESSION[self::S_CRM_MANAE_PROJECT_TASK_ID] = Helper::getIdFromNotif($parm[2], $table, $type);
                try {
                    $_SESSION[self::S_CRM_MANAGE_PROJECT_ID] = CRMTasks::readID((int)$_SESSION[self::S_CRM_MANAE_PROJECT_TASK_ID])
                        ->project_id;
                } catch (CRMTasksException $e) {
                }
            }
        }

        switch ($parm[0]){

            case 'updateTask':

                if ($parm[1] === $_SESSION[ self::S_CRM_MANAGE_PROJECT_TOKEN ]) {
                    try {
                        $task = CRMTasks::readID((int)$_SESSION[ self::S_CRM_MANAE_PROJECT_TASK_ID ]);
                        $task->bindProperties($post);
                        $task->save();
                        Notification::updatedAlert();
                    } catch (CRMTasksException $e) {
                    }
                }

                break;
            case 'updateStaff':
                try {
                    $customerStaff = CRMCustomerStaff::readID((int) $parm[2]);
                    $customerStaff->targeted = ((int) $customerStaff->targeted === CRMCustomerStaff::IS_TARGETED) ? CRMCustomerStaff::IS_NOT_TARGETED : CRMCustomerStaff::IS_TARGETED;
                    $customerStaff->save();
                } catch (CRMCustomerStaffException $e) {

                }
                break;
        }
        $utask = null;
        try {
            $utask = CRMTasks::readID((int)$_SESSION[ self::S_CRM_MANAE_PROJECT_TASK_ID ]);
        } catch (CRMTasksException $e) {
            $utask = [];
        }
        $this->Smarty->assign('task', $utask);
        try {
            $this->Smarty->assign('teamList', CRMTeam::read([
                                                                CRMTeam::PROJECT_ID => $_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ],
                                                                CRMTeam::USER_ID => $_SESSION['user']->id
                                                            ]));
        } catch (CRMTeamException $e) {
        }
        try {
            $staff = CRMCustomerStaff::staffByProjectId((int)$_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ]);
        } catch (CRMCustomerStaffException $e) {
            $staff = [];
        }
        $this->Smarty->assign('customer_staff', $staff);

        try {
            $this->Smarty->assign('exec_list', Setting::read([
                Setting::OPR_ID => 92
            ]));
        } catch (SettingException $e) {
            $this->Smarty->assign('exec_list', []);
        }
        try {
            $this->Smarty->assign('cc_list', CRMTeam::usersIds($utask));
        } catch (CRMTeamException $e){
            $this->Smarty->assign('cc_list', []);
        }
        $_SESSION[ self::S_CRM_MANAGE_PROJECT_TOKEN ] = Helper::generateToken();
    }

    public function taskapprove($parm, $post)
    {
        try {
            $this->Smarty->assign('row', CRMTasks::readID((int)$parm[0]));
        } catch (CRMTasksException $e) {
        }
    }

    /**
     * @param $post
     * @return void
     */
    public function insertTask($post, $parm)
    {
        try {
            $actrow = CRMProjects::readID((int) $parm[3]);
        } catch (CRMProjectsException $e) {
            $actrow = [];
        }
        CRMTasks::createNewTask($_SESSION['organization'], $_SESSION['user'], $actrow->id, $post);
//        $task = new CRMTasks();
//        $task->bindProperties($post);
//        $task->name = $actrow->name;
//        if ($post['connect_status'] == 818) {
//            try {
//                $relatedTask = CRMTasks::readID((int)$post['connected_to']);
//            } catch (CRMTasksException $e) {
//                $relatedTask = [];
//            }
//
//            $task->start_date = (new DateTime($relatedTask->end_date))->add(new DateInterval('P1D'))->format('Y-m-d');
//        } else {
//            $task->start_date = $this->Date->get_date('ad', (new DateTime($post['start_date']))
//                ->format('Y-m-d'));
//        }
//        $task->end_date = CRMTasks::taskEndDate($actrow, $task->start_date, $task->duration);
//        $task->org_id = $_SESSION['organization']->id;
//        $task->project_id = $actrow->id;
//        $task->locked = CRMTasks::TASK_OPENED;
//        $task->proved = $actrow->newtask_prov;
//        $task->status = CRMTasks::TASK_NOT_EXECUTED;
//        $task->assigner_id = $_SESSION['user']->id;
//        $task->place = $post['place'];
//        $task->is_activity = 0;
//        $task->created_by = $_SESSION['user']->id;
//        $task->created_date = date('Y-m-d', time());
//        $task->targeted_employees = implode(",", $task->targeted_employees);
//        // Update activity's end date
//        CRMProjects::activityEndDate($actrow);
//
//        if ((new DateTime($task->start_date) < new DateTime($actrow->start_date)) || (new DateTime($task->start_date) >
//                new DateTime($actrow->end_date))
//        ) {
//            Notification::alertMessage(Notification::ERROR, 'TaskStartDateBeforProjectStartDate');
//        } else {
//            $task->save();
//            // Update task's interval end date
//            CRMInterval::intervalEndDate($task->div_id);
//
//            Notification::sendNotification(
//                $_SESSION['operation']->id,
//                0,
//                crm_tasks::class,
//                $task->id,
//                $_SESSION['user']->id,
//                $task->assignee_id,
//                1106, []);
//
//            Notification::createdAlert();
//
//        }

    }

    /**
     * @param $parm
     * @param $post
     */
    public function updateTask($parm, $post)
    {
        try {
            $actrow = CRMProjects::readID((int)$_SESSION[ self::S_CRM_MANAGE_PROJECT_ID ]);
        } catch (CRMProjectsException $e) {
            $actrow = [];
        }
        try {
            $task = CRMTasks::readID((int)$parm[3]);
            // Check if the assignee is a new user (To notice him)
            $is_new_assignee = ($task->assignee_id == $post['assignee_id']) ? false : true;
            $task->bindProperties($post);
            $task->name = $actrow->name;
            $task->start_date = $this->Date->get_date('ad', (new DateTime($post['start_date']))
                ->format('Y-m-d'));
            $task->end_date = CRMTasks::taskEndDate($actrow, $task->start_date, $task->duration);
            CRMProjects::activityEndDate($actrow);
            CRMInterval::intervalEndDate($task->div_id);
            if ((new DateTime($task->start_date) < new DateTime($actrow->start_date)) || (new DateTime($task->start_date) >
                    new DateTime($actrow->end_date))
            ) {
                $this->Smarty->assign('message', 'TaskStartDateBeforProjectStartDate');
            } else {
                $task->save();
                if ($is_new_assignee) {
                    Notification::sendNotification($_SESSION['operation']->id, 0, crm_tasks::class, $task->id,
                                                   $_SESSION['user']->id, $task->assignee_id, 1106, []);
                }

                Notification::updatedAlert();

            }

        } catch (CRMTasksException $e) {
        }
    }

    /**
     * @param $parm
     */
    public function deleteTask($parm)
    {
        try {
            $task = CRMTasks::readID((int)$parm[3]);
            $task->delete();
            $this->Smarty->assign('message', 'delete');
        } catch (CRMTasksException $e) {
        }
    }
}
