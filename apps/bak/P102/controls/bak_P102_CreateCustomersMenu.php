<?php
/**
 * Deny Direct Script Access
 *
 * @category Controller
 * <AUTHOR> <<EMAIL>>
 * @SuppressWarnings(PHPMD.CamelCaseClassName)
 */
class bak_P102_CreateCustomersMenu extends Controller
{
    const SESSION_TOKEN = 's_CreateCustomersMenu_token';
    const CRUDS = ['insert', 'update', 'delete'];
    const OPR_CODE = 'CreateCustomersMenu';
    /**
     * show created customers
     *
     * @param Request $parm fsfdgd
     * @param mixed $post
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    public function show($parm, $post)
    {
        if (is_callable([$this, $parm[0]]) && in_array($parm[0], self::CRUDS)) {
            call_user_func_array([$this, $parm[0]], [$parm, $post, new CsrfManager()]);
        }

        try{
            $this->Smarty->assign('rows', CRMCustomers::read([
                CRMCustomers::ORG_ID => $_SESSION['organization']->id
            ]));
        }catch (CRMCustomersException $e){
            $this->Smarty->assign('rows',[]);
        }
        $this->Smarty->assign('opr_code', self::OPR_CODE);
        $linkedProjects = [];
        try {
            foreach (CRMProjects::read() as $project) {
                array_push($linkedProjects, (int) $project->cust);
            }
        } catch (CRMProjectsException $e) {}
        $this->Smarty->assign('linked_projects', $linkedProjects);
    }
    /**
     * Create a Customer profile
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.UnusedLocalParameters)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function insert($parm, $post, $csrf)
    {
        try{
            $csrf->check('token', $post['csrf_input']);
            $customerProfile =new CRMCustomers();
            $customerProfile->bindProperties($post);
            $customerProfile->org_id = $_SESSION['organization']->id;
            $customerProfile->created_by = $_SESSION['user']->id;
            // إجعله غير فعال في حالة عدم تفعيله إفتراضيا
            $customerProfile->activation = $post['activation']?? 653;
            $customerProfile->save();
            Notification::createdAlert();
        }catch (CRMCustomersException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// csrf exceptions
        }
    }
    /**
     * Update Customer Profile
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function update($parm, $post, $csrf)
    {
        try{
            $csrf->check('update_token', $post['csrf_input']);
            $customerProfile = CRMCustomers::readID((int) $parm[1] ?? 0);
            $customerProfile->bindProperties($post);
            $customerProfile->update();
            Notification::updatedAlert();
        }catch (CRMJobNamesException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle csrf exceptions
        }
    }
    /**
     * Delete Customer Profile
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    private function delete($parm, $post, $csrf)
    {
        try{
            $csrf->check('delete_token', $post['csrf_input']);
            $customerProfile = CRMCustomers::readID((int) $parm[1] ?? 0);
            $customerProfile->delete();
            Notification::deletedAlert();
        }catch (CRMCustomersException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle some csrf exceptions
        }
    }
    /**
     * Add
     * @param mixed $parm , $post
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    public function add()
    {
        try{
            $this->Smarty->assign('customer_types', CRMCustomersTypes::read([
                CRMCustomersTypes::ORG_ID => $_SESSION['organization']->id,
                CRMCustomersTypes::ACTIVATION => 652
            ]));
        }catch (CRMCustomersTypesException $e){
            $this->Smarty->assign('customer_types',[]);
        }
        try{
            $this->Smarty->assign('customer_accesses', CRMCustomerAccessablity::read([
                CRMCustomerAccessablity::ORG_ID => $_SESSION['organization']->id,
                CRMCustomerAccessablity::ACTIVATION => 652
            ]));
        }catch (CRMCustomerAccessablityException $e){
            $this->Smarty->assign('customer_accesses',[]);
        }
        $this->assignURI('insert');
    }

    /**
     * Edit
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function edit($parm)
    {
        try{
            $this->Smarty->assign('customer_types', CRMCustomersTypes::read([
                CRMCustomersTypes::ORG_ID => $_SESSION['organization']->id,
                CRMCustomersTypes::ACTIVATION => 652
            ]));
        }catch (CRMCustomersTypesException $e){
            $this->Smarty->assign('customer_types',[]);
        }
        try{
            $this->Smarty->assign('customer_accesses', CRMCustomerAccessablity::read([
                CRMCustomerAccessablity::ORG_ID => $_SESSION['organization']->id,
                CRMCustomerAccessablity::ACTIVATION => 652
            ]));
        }catch (CRMCustomerAccessablityException $e){
            $this->Smarty->assign('customer_accesses',[]);
        }
        try{
            $this->Smarty->assign('row', CRMCustomers::readID((int) $parm[0] ?? 0));
        }catch (CRMCustomersException $e){
            $this->Smarty->assign('row',[]);
        }
	    $linkedProjects = [];
	    try {
		    foreach (CRMProjects::read() as $project) {
			    array_push($linkedProjects, (int) $project->cust);
		    }
	    } catch (CRMProjectsException $e) {}
	    $this->Smarty->assign('linked_projects', $linkedProjects);
        $this->assignURI('update');
    }
    /**
     * confirm deletion
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function confirm($parm)
    {
        try{
            $this->Smarty->assign('raw', CRMCustomers::readID((int) $parm[0] ?? 0));
        }catch (CRMCustomersException $e){
            $this->Smarty->assign('raw',[]);
        }
        $this->assignURI('delete');
    }
    /**
     * assign URI
     *
     * @param string $pageName
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     */
    private function assignURI($actionName, $pageName = 'show')
    {
        $this->Smarty->assign('base_uri', "{$_SESSION['bnd']}/{$_SESSION['prg']}/{$_SESSION['oprname']}/{$pageName}/0/{$_SESSION['lang']}/{$actionName}");
    }
}