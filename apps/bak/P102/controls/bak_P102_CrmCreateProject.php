<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>
 * Date: 7/4/17 bak_P102_CrmCreateProject
 * Time: 12:28 PM
 */
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');
class bak_P102_CrmCreateProject extends Controller
{
    const OPR_CODE = 's_CrmCreateProject_token';
    const S_TAB = 's_product_tab';

    public function show($parm, $post)
    {
        // Set the active tab
        $_SESSION[ self::S_TAB ] = CRMProducts::first()->id;

        switch ($parm[0]) {
            case 'active_tab':
                $_SESSION[ self::S_TAB ] = (int) $parm[1];
            case 'insert':
                if($parm[1] === $_SESSION[ self::OPR_CODE ]) {
                    Validation::rules($post, [
                        'name'              => 'required|max:100',
                        'unit_id'           => 'required|numeric',
                    ]);

                    if(Validation::check()) {
                        try {
                            $products_start_date = CRMProducts::readID((int)$parm[2])->start_date;
                        } catch (CRMProductsException $e) {
                            $products_start_date = date('Y-m-d', time());
                        }

                        $projects = new CRMProjects();
                        $projects->bindProperties($post);
                        $projects->product = (int) $post['product'];
                        $projects->org_id = $_SESSION['organization']->id;
                        $projects->created_by = $_SESSION['user']->id;
                        $projects->created_date = (new DateTime())->format('Y-m-d');
                        $projects->start_date = $products_start_date;
                        $projects->end_date = $products_start_date;
                        $projects->request_success = 0;
                        $projects->save();


                        // Add one interval (First interval)
                        $interval = new CRMInterval();
                        $interval->org_id = $_SESSION['organization']->id;
                        $interval->name = 'المرحلة الأولى';
                        $interval->desc = 'المرحلة الأولى';
                        $interval->project_id = $projects->id;
                        $interval->start_date = $projects->start_date;
                        $interval->end_date = $projects->start_date;
                        $interval->created_by = $_SESSION['user']->id;
                        $interval->created_date = date('Y-m-d', time());
                        $interval->save();

                        // Set the active tab
                        $_SESSION[ self::S_TAB ] = (int) $post['product'];
                        Notification::createdAlert();
                    }

                }
                $_SESSION[ self::S_TAB ] = (int) $post['product'];
                $_SESSION[ self::OPR_CODE ] = md5(rand(0, 9999));
                break;
            case 'update':
                if($parm[1] === $_SESSION[ self::OPR_CODE ]) {
                    Validation::rules($post, [
                        'name'     => 'required|max:200',
                        'unit_id'           => 'required|numeric',
//                        'program_id'           => 'required|numeric',
                    ]);

                    if(Validation::check()) {
                        try {
                            $projects = CRMProjects::readID((int) $parm[2]);
                            $projects->bindProperties($post);
                            $projects->save();
                        } catch (CRMProjectsException $e) {
                        }
                    }
                }
                $_SESSION[ self::S_TAB ] = (int) $post['product'];
                $_SESSION[ self::OPR_CODE ] = md5(rand(0, 9999));
                break;
            case 'delete':
                if($parm[1] === $_SESSION[ self::OPR_CODE ]) {
                    try {
                        $projects = CRMProjects::readID((int) $parm[2]);
                        $projects->delete();
                        Notification::deletedAlert();
                    } catch (CRMProjectsException $e) {
                    }
                }
                $_SESSION[ self::OPR_CODE ] = md5(rand(0, 9999));
                break;
        }

        try {
            $tmplProducts = CRMProducts::read([CRMProducts::ACTIVATION => CRMProducts::ACTIVATED]);
        } catch (CRMProductsException $e) {
            $tmplProducts = [];
        }
        $this->Smarty->assign('products', $tmplProducts);
        $_SESSION[ self::OPR_CODE ] = md5(rand(0, 9999));
    }

    public function add($parm, $post)
    {
        try {
            $products = CRMProducts::readID((int)$parm[ 0 ] ?? 0);
        } catch (CRMProductsException $e) {
            $products = [];
        }
        try {
            $unit_list = Unit::read([ Unit::ORG_ID => $_SESSION['organization']->id ]);
        } catch (UnitException $e) {
            $unit_list = [];
        }
        try {
            $customers_list = CRMCustomers::read([ CRMCustomers::ACTIVATION => Setting::ACTIVE_CASE ]);
        } catch (CRMCustomersException $e) {
            $customers_list = [];
        }
        $this->Smarty->assign('product', $products);
        $this->Smarty->assign('units_list', $unit_list);
        $this->Smarty->assign('customers_list', $customers_list);
    }

    public function edit($parm, $post)
    {
        try {
            $this->Smarty->assign('product', CRMProducts::readID((int) $parm[1] ?? 0));
        } catch(CRMProductsException $e) {
            $this->Smarty->assign('product', []);
        }

        try {
            $this->Smarty->assign('units_list', Unit::read([
                                                               Unit::ORG_ID => $_SESSION['organization']->id
                                                           ]));
        } catch (ModelException $e) {
        }

        try {
            $this->Smarty->assign('actrow', CRMProjects::readID((int)$parm[0]));
        } catch (CRMProjectsException $e) {
            $this->Smarty->assign('actrow', []);
        }
        try {
            $this->Smarty->assign('customers_list', CRMCustomers::read([ CRMCustomers::ACTIVATION => Setting::ACTIVE_CASE ]));
        } catch (CRMCustomersException $e) {
            $this->Smarty->assign('customers_list', []);
        }
    }

    public function confirm($parm, $post)
    {
        try {
            $this->Smarty->assign('actrow', CRMProjects::readID((int)$parm[0]));
        } catch (CRMProjectsException $e) {
        }
    }
    public function browse($parm, $post)
    {
        $_SESSION['s_prepare_tab'] = 1;
        
        $this->Smarty->assign('evalist', pm_preva::simpleReadByProperty([]));
        $this->Smarty->assign('provlist', Setting::read([
            Setting::OPR_ID => 95
        ], [
            0 => [
                'property' => Setting::ORDER,
                'sort' => 'ASC'
            ]
        ]));
        $this->Smarty->assign('week_days', Setting::read([
            Setting::OPR_ID => 25
        ], [
            0 => [
                'property' => Setting::ORDER,
                'sort' => 'ASC'
            ]
        ]));
        
        try {
            $actrow = CRMProjects::readID((int)$parm[0]);
        } catch (CRMProjectsException $e) {
            $actrow = null;
        }
        $this->Smarty->assign('actrow', $actrow);
        try {
            $this->Smarty->assign('support_unit_list', Unit::supportUnitsExceptCurrent($actrow->unit_id));
        } catch (UnitException $e) {
        }
    }
}
//        try {
//            $this->Smarty->assign('activities', ClientList::read([
//                                                                   ClientList::CLIENT_ID => $_SESSION['organization']->id,
//                                                                   ClientList::TYPE => ClientList::CRM_ACTIVITY,
//                                                                   ClientList::ACTIVE => ClientList::ACTIVATED_SETTING
//                                                               ]));
//        } catch (ClientListException $e) {
//        }
