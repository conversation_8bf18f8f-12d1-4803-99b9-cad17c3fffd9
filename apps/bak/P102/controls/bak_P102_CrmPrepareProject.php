<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON>
 * Date: 7/4/17
 * Time: 12:28 PM
 */

defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');

// TODO Documentation Missing
class bak_P102_CrmPrepareProject extends Controller
{
    const S_PRODUCT_TAB = 's_product_tab';
    
    const S_TOKEN = 's_CrmPrepareProject_token';
    
    public function show($parm, $post)
    {
        $_SESSION[ self::S_PRODUCT_TAB ] = CRMProducts::first()->id;
        switch ($parm[ 0 ]) {
            case 'redirect':
                if ($parm[ 1 ] === $_SESSION[ self::S_TOKEN ]) {
                    try {
                        $project = CRMProjects::readID((int)$parm[ 2 ] ?? 0);
                        $project->bindProperties($post);
                        $project->save();
                        if (CRMTeam::count(
                                [
                                    CRMTeam::ORG_ID => $_SESSION[ 'organization' ]->id,
                                    CRMTeam::PROJECT_ID => $project->id,
                                    CRMTeam::USER_ID => $project->manager_id,
                                ]
                            ) == 0) {
                            // Add the manager with the full privileges
                            $member = new CRMTeam();
                            $member->org_id = $_SESSION[ 'organization' ]->id;
                            $member->project_id = $project->id;
                            $member->user_id = $project->manager_id;
                            $member->task_id = 1059;
                            $member->prvs = '392,394,402,403,1114';
                            $member->created_by = $_SESSION[ 'user' ]->id;
                            $member->created_date = date('Y-m-d', time());
                            $member->save();
                        } else {
                            try {
                                // Delete the old task
                                $team_member = CRMTeam::read(
                                    [
                                        CRMTeam::ORG_ID => $_SESSION[ 'organization' ]->id,
                                        CRMTeam::PROJECT_ID => $project->id,
                                        CRMTeam::USER_ID => $project->manager_id,
                                    ]
                                )[ 0 ];
                                $team_member->delete();
                                
                                // Add  new one with full privileges
                                $member = new CRMTeam();
                                $member->org_id = $_SESSION[ 'organization' ]->id;
                                $member->project_id = $project->id;
                                $member->user_id = $project->manager_id;
                                $member->task_id = 1059;
                                $member->prvs = '392,394,402,403,1114';
                                $member->created_by = $_SESSION[ 'user' ]->id;
                                $member->created_date = date('Y-m-d', time());
                                $member->save();
                            } catch (CRMTeamException $e) {
                            }
                        }
                        $_SESSION[ self::S_PRODUCT_TAB ] = $project->product;
                    } catch (CRMProjectsException $e) {
                        $project = [];
                    }
                }
                break;
            case 'select_tab_and_project':
//                $_SESSION[ self::S_PRODUCT_TAB ] = $parm[ 1 ];
                $_SESSION[ self::S_PRODUCT_TAB ] = (int) $parm[2];
                break;
        }
        try {
            $products = CRMProducts::read(
                [
                    CRMProducts::ORG_ID => $_SESSION[ 'organization' ]->id,
                    CRMProducts::ACTIVATION => Setting::ACTIVE_CASE,
                ]
            );
        } catch (CRMProductsException $e) {
            $products = [];
        }
        $this->Smarty->assign('products', $products);
        $_SESSION[ self::S_TOKEN ] = Helper::generateToken();
        try {
            $project = CRMProjects::readID((int)$parm[ 2 ] ?? 0);
        } catch (CRMProjectsException $e) {
            $project = [];
        }
    }
    
    public function redirect($parm, $post)
    {
        try {
            $row = CRMProjects::readID((int)$parm[ 0 ]);
        } catch (CRMProjectsException $e) {
            $row = [];
        }
        try {
            $users_list = Vacant::getEmployeeList($_SESSION['organization'],'all',(int) $row->unit_id);
            $employees_list = Vacant::getEmployeeList($_SESSION['organization']);
        } catch (VacantException $e) {
            $users_list = [];
            $employees_list = [];
        }
        $this->Smarty->assign('users_list', $users_list);
        $this->Smarty->assign('employees_list', $employees_list);
        $this->Smarty->assign('row', $row);
    }
    
    public function actmanager($parm, $post)
    {
        try {
            $users_list = Vacant::getEmployeeList($_SESSION[ 'organization' ]);
        } catch (VacantException $e) {
            $users_list = [];
        }
        
        try {
            $row = CRMProjects::readID((int)$parm[ 0 ]);
        } catch (CRMProjectsException $e) {
            $row = [];
        }
        $this->Smarty->assign('users_list', $users_list);
        $this->Smarty->assign('row', $row);
    }
}
