<?php
/**
 * <AUTHOR>
 **/
defined('ENVIRONMENT') OR exit('No Direct Access Allowed!');
class bak_P102_actPreparation extends Controller
{
    const SESSION_TOKEN = 's_crmProjects_token';
    const CRUDS = ['insert', 'update', 'delete', 'updateProduct'];
    const OPR_CODE = 'CreateProject'; 
    /**
     * Show Created Projects 
     * 
     * @param Request $parm
     * @param mixed $post
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    public function show($parm, $post)
    {
        if (is_callable([$this, $parm[0]]) && in_array($parm[0], self::CRUDS)) {
            call_user_func_array([$this, $parm[0]], [$parm, $post, new CsrfManager()]);
        }

        try{
            $this->Smarty->assign('raws', CRMProjects::read([]));
        }catch (CRMProjectsException $e){
            $this->Smarty->assign('raws',[]);
        }
        $this->Smarty->assign('opr_code', self::OPR_CODE);
    }
    /**
     * Add Project
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.UnusedLocalParameters)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function insert($parm, $post, $csrf)
    {
        try{
            $csrf->check('token', $post['csrf_input']);
            $crmProject = new CRMProjects();
            $crmProject->bindProperties($post);
            $crmProject->org_id = $_SESSION['organization']->id;
            $crmProject->created_by = $_SESSION['user']->id;
            $crmProject->created_at = date('Y-m-d', time());
            // إجعله غير فعال في حالة عدم تفعيله إفتراضيا
            $crmProject->activation = $post['activation'] ?? 653;
            $crmProject->save();
        }catch (CRMProjectsException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// csrf exceptions
        }
    }
    /**
     * Update JobType
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    private function update($parm, $post, $csrf)
    {
        try{
            $csrf->check('update_token', $post['csrf_input']);
            $crmProduct = CRMProjects::readID((int) $parm[1] ?? 0);
            $crmProduct->bindProperties($post);
            $crmProduct->update();
        }catch (CRMProjectsException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle csrf exceptions
        }
    }
    /**
     * Update Product Object
     *
     * @param array $parm
     * @param array $post
     * @param CsrfManager $csrf
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    private function updateProduct($parm, $post, $csrf)
    {
        try{
            $csrf->check('update_product_token', $post['csrf_input']);
            $post['activation'] = ($post['activation'] == 652 ) ? '652': '653';
            $post['objectivePurchaseable'] = isset($post['objectivePurchaseable']) ? CRMProjects::OBJECTIVE_PURCHASEABLE :  0;
            $post['objectiveForSale'] = isset($post['objectiveForSale']) ? CRMProjects::OBJECTIVE_FOR_SALE :  0;

            unset($post['csrf_input']);
            $productObject = json_encode($post, true);
            $crmProduct = CRMProjects::readID((int) $parm[1] ?? 0);
            $crmProduct->obj = $productObject;
            $crmProduct->update();
        }catch (CRMProjectsException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle csrf exceptions
        }
    }
    /**
     * Delete Product
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.UnusedPrivateMethod)
     * @SuppressWarnings(PHPMD.StaticAccess)
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    private function delete($parm, $post, $csrf)
    {
        try{
            $csrf->check('delete_token', $post['csrf_input']);
            $crmProduct = CRMProjects::readID((int) $parm[1] ?? 0);
            $crmProduct->delete();
        }catch (CRMProjectsException $e){
            Notification::alertMessage(Notification::ERROR, $e->getMessage());
        } catch (Exception $e) {
            //// handle some csrf exceptions
        }
    }
    /**
     * update scope with product object
     *
     * @param array $parm
     * @param array $post
     * @param CsrfManager $csrf
     * @return void
     * @SuppressWarnings(PHPMD)
     */
    public function productsView($parm, $post, $csrf)
    {
        try{
            $crmProduct = CRMProjects::readID((int) $parm[0] ?? 0);
            $productObject = json_decode($crmProduct->obj, true);
            $this->Smarty->assign('raw', $crmProduct);
            $this->Smarty->assign('raw_obj', $productObject);
        }catch (CRMProjectsException $e){
            $this->Smarty->assign('raw',[]);
        }
        $this->assignURI('updateProduct');
    }
    /**
     * Add Product
     * @param mixed $parm , $post
     * @return void
     */
    public function add()
    {
        $this->assignCustomers();
        $this->assignProducts();
        $this->assignUnits();

        $this->assignURI('insert');
    }

    /**
     * Edit Job Type
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function edit($parm)
    {
        try{
            $this->Smarty->assign('raw', CRMProjects::readID((int) $parm[0] ?? 0));
        }catch (CRMProjectsException $e){
            $this->Smarty->assign('raw',[]);
        }
        $this->assignURI('update');
    }
    /**
     * confirm deletion
     *
     * @param array $parm
     * @param array $post
     * @return void
     * @SuppressWarnings(PHPMD.StaticAccess)
     */
    public function confirm($parm)
    {
        try{
            $this->Smarty->assign('raw', CRMProjects::readID((int) $parm[0] ?? 0));
        }catch (CRMProjectsException $e){
            $this->Smarty->assign('raw',[]);
        }
        $this->assignURI('delete');
    }
    /**
     * assign URI
     *
     * @param string $pageName
     * @return void
     * @SuppressWarnings(PHPMD.Superglobals)
     */
    private function assignURI($actionName, $pageName = 'show')
    {
        $this->Smarty->assign('base_uri', "{$_SESSION['bnd']}/{$_SESSION['prg']}/{$_SESSION['oprname']}/{$pageName}/0/{$_SESSION['lang']}/{$actionName}");
    }
    private function assignCustomers()
    {
        try{
            $this->Smarty->assign('customers_list', CRMCustomers::read([
                CRMCustomers::ORG_ID => $_SESSION['organization']->id,
                CRMCustomers::ACTIVATION=> 652
            ]));
        }catch (CRMCustomersException $e){
            $this->Smarty->assign('customers_list',[]);
        }
    }
    private function assignProducts()
    {
        try {
            $this->Smarty->assign('products_list', CRMProducts::read([
                CRMProducts::ORG_ID=> $_SESSION['organization']->id,
                CRMProducts::ACTIVATION=> 652
            ]));
        } catch (CRMProductsException $e) {
            $this->Smarty->assign('products_list',[]);
        }
    }
    private function assignUnits()
    {
        try {
            $this->Smarty->assign('units_list', Unit::read([
                Unit::ORG_ID=> $_SESSION['organization']->id
            ]));
        } catch (UnitException $e) {
            var_dump($e);
            $this->Smarty->assign('units_list',[]);
        }
    }
}