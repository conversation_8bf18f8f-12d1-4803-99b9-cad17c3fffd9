{assign var='rtl' value='rtl'}
{assign var='ltr' value='ltr'}
{capture name="direction"}{if $smarty.config.direction eq $rtl}right{else}left{/if}{/capture}
{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}
	<div class="btn-group">
		<a class="btn btn-success " href="javascript:void(0);">
			{if !empty($smarty.session.s_ActivitiesLogMenu_token)}
				{$customer->name}
			{else}
				<span>{#p_choose_menu_from_list_above#} </span>
			{/if}
		</a>
		<a class="btn btn-success  dropdown-toggle" data-toggle="dropdown" href="javascript:void(0);" aria-expanded="false"><i class="fa fa-angle-down"></i></a>
		<ul class="dropdown-menu dropdown-success">
			{foreach $customers as $customerObject}
				<li>
					<a href="{url urltype="path" url_string="{$base_uri}/{$customerObject->id}"}">
						{$customerObject->name}
					</a>
				</li>
			{/foreach}
		</ul>
	</div>

	<div class="horizontal-space"></div>

	<div class="table-responsive" data-pattern="priority-columns">
		<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
			<thead>
				<tr>
					<th width="5%"></th>
					<th width="20%" data-priority="1">{#p_project#}</th>
					<th width="35%" data-priority="1">{#p_activity_or_task#}</th>
					<th width="25%" data-priority="1">{#p_exec#}</th>
					<th width="10%" data-priority="1">{#p_start_date#}</th>
					<th width="5%" data-priority="1">{#p_exec_duration#}</th>
				</tr>
			</thead>
			<tbody>
			{$i=1}
				{foreach $projects as $project}
					{foreach $project->Tasks as $task}
						{if $task->Team->task_id neq CRMTeam::MARKETING_EXPERT}
							<tr>
								<td align="center">
									{url urltype="button" url_string="bak/P102/CrmManageProject/browse/0/ar/{$task->id}" snsosize="snsosize" text_value="<i class='fa fa-file-o'></i>" style="btn btn-default shiny"}
								</td>
								<td align="{$smarty.capture.direction}" style="padding-{$smarty.capture.direction}: 2% !important"">{$project->name}</td>
								<td align="{$smarty.capture.direction}" style="padding-{$smarty.capture.direction}: 2% !important">
                                    {if $task->locked eq 395}<font color="#66CCCC"><i class="fa fa-unlock"></i></font>{/if}
                                    {if $task->locked eq 396}<font color="#996600"><i class="fa fa-lock"></i></font>{/if}
                                    {if $task->status eq 397} <font color="gray" ><i class="fa fa-times-circle-o"></i></font> {/if}
                                    {if $task->status eq 398} <font color="orange" ><i class="fa fa-dot-circle-o"></i></font> {/if}
                                    {if $task->status eq 399} <font color="green" ><i class="fa fa-check-circle-o"></i></font> {/if}
                                    {if $task->is_activity eq 1} <font color="#daa520" ><i class="fa fa-star"></i></font> {/if}
                                    {getname table=crm_activities id=$task->actsettings_id}
									»
                                    {$task->title}
								</td>
								<td align="{$smarty.capture.direction}" style="padding-{$smarty.capture.direction}: 2% !important">{getname table="sh_user" id=$task->assignee_id}</td>
								<td align="center">{$task->start_date}</td>
								<td align="center">{$task->duration}</td>
							</tr>
                        {elseif $task->assignee_id eq $smarty.session.user->id}
							<tr>
								<td align="center">{$i++}</td>
								<td align="center">{$project->name}</td>
								<td align="center">
                                    {getname table=crm_activities id=$task->actsettings_id}
									»
                                    {$task->title}
								</td>
								<td align="center">{getname table="sh_user" id=$task->assignee_id}</td>
								<td align="center">{$task->start_date}</td>
								<td align="center">{$task->duration}</td>
							</tr>
                        {/if}
					{/foreach}
				{/foreach}
			</tbody>
		</table>
	</div>
{/block}
