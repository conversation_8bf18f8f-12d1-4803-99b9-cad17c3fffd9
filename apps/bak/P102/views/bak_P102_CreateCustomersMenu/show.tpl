{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" /><style>{literal}th {background-color: #A0D468 !important;}{/literal}</style>{/block}
{block name=page_body}
    <div class="row">
        <div class="col-lg-12">
                <table class="table table-bordered table-hover dataTable no-footer sortable-table">
                    <thead>
                    <tr>
                        <th width="5%">{url check=1 urltype="madd" opr_code=$opr_code url_string="{$smarty.session.bnd}/{$smarty.session.prg}/{$opr_code}/add/0/{$smarty.session.lang}"}</th>
                        <th width="30%" data-priority="1">{#p_customer_name#}</th>
                        <th width="20%" data-priority="1">{#p_customer_type#}</th>
                        <th width="20%" data-priority="1">{#p_customer_access#}</th>
                        <th width="10%">{#gnr_activation#}</th>
                        <th width="10%" data-priority="3">{#gnr_settings#}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {$i=1}
                    {foreach $rows as $row}
                        <tr>
                            <td align="center">{$i++}</td>
                            <td>{$row->name}</td>
                            <td>{getname table="crm_customerstypes" id=$row->type}</td>
                            <td>{getname table="crm_custacc" id=$row->access}</td>
                            <td align="center">{getname table=st_setting id=$row->activation}</td>
                            <td align="right" style="padding-right: 2%">
                                {url check=1 urltype="medit" opr_code=$opr_code url_string="{$smarty.session.bnd}/{$smarty.session.prg}/{$opr_code}/edit/0/{$smarty.session.lang}/{$row->id}"}
                                {if not in_array($row->id, $linked_projects)}
                                    {url check=1 urltype="mdelete" opr_code=$opr_code url_string="{$smarty.session.bnd}/{$smarty.session.prg}/{$opr_code}/confirm/0/{$smarty.session.lang}/{$row->id}"}
                                {/if}
                            </td>
                        </tr>
                    {/foreach}
                    </tbody>
                </table>
        </div>
    </div>
{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
        function exportTo(ID, type) {
            $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
        }
        {/literal}
    </script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}