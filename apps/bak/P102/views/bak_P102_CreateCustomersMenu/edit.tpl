
{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
	<script type="text/javascript">

	</script>
{/block}

{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>

		<h4 class="modal-title">{#gnr_edit#}</h4>
	</div>
	<div class="modal-body">
		<div class="row">
			<form method="post" action='{url urltype="path" url_string=$base_uri}/{$row->id}'>
				{csrf_field token_key="update_token"}
				<div class="col-lg-12"> 
					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_name#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" id="name" name="name" placeholder="{#p_customer_name#}" value="{$row->name}" required></div>

					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_type#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						<select name="type" placeholder="{#p_customer_type#}" value="{$row->type}">
							{foreach $customer_types as $type}
								<option value={$type->id}
								{if $type->id eq $row->type}
									selected
								{/if}
								>{$type->name}</option>
							{/foreach}
						</select>
					</div>
					
					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_access#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						<select name="access" placeholder="{#p_customer_access#}" value="{$row->access}">
							{foreach $customer_accesses as $access}
								<option value={$access->id}
								{if $access->id eq $row->access}
									selected
								{/if}
								>{$access->name}</option>
							{/foreach}
						</select>
					</div>
                    {if not in_array($row->id, $linked_projects)}
						{gnr_activation edit_value=$row->activation}
					{else}
                    	{gnr_activation edit_value=$row->activation is_disabled='disabled'}
					{/if}
					
					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
				</div>
			</form>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#p_close#}</button>
	</div>
{/block}