
{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
	<script type="text/javascript">

	</script>
{/block}

{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>

		<h4 class="modal-title">{#gnr_add#}</h4>
	</div>
	<div class="modal-body">
		<div class="row">
			<form method="post" action='{url urltype="path" url_string=$base_uri}'>
				{csrf_field}
				<div class="col-lg-12"> 
					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_name#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><input type="text" class="form-control" id="name" name="name" placeholder="{#p_customer_name#}" required></div>

					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_type#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						<select name="type" placeholder="{#p_customer_type#}">
							{foreach $customer_types as $row}
								<option value={$row->id}>{$row->name}</option>
							{/foreach}
						</select>
					</div>
					
					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#p_customer_access#}</div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						<select name="access" placeholder="{#p_customer_access#}">
							{foreach $customer_accesses as $row}
								<option value={$row->id}>{$row->name}</option>
							{/foreach}
						</select>
					</div>
					{gnr_activation edit_value="{Setting::ACTIVE_CASE}"}
					
					<div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
					<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp">{#gnr_add#}</button></div>
				</div>
			</form>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#p_close#}</button>
	</div>
{/block}