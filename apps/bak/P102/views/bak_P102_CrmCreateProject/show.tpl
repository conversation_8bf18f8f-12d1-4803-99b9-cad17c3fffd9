{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=head_style}<link href="/templates/assets/css/dataTables.bootstrap.css" rel="stylesheet" />{/block}
{block name=page_body}
    <div class="row">
        <div class="col-lg-12">
            <div class="tabbable tabs-left">
                <ul class="nav nav-tabs" id="myTab3">
                    {$i=1}
                    {foreach $products as $product}
                        <li class="tab-sky {if $smarty.session.s_product_tab eq $product->id}active{/if}">
                            <a data-toggle="tab" href="#{$product->id}">
                                <span class="badge badge-sky badge-square">{$i++}</span>
                                {$product->scope}
                            </a>
                        </li>
                    {/foreach}
                </ul>
                <div class="tab-content">
                    {$j=1}
                    {foreach $products as $product}
                        <div id="{$product->id}" class="tab-pane {if $smarty.session.s_product_tab eq $product->id}active{/if}">
                            <span class="hidden">{$j++}</span>
                            <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
                                <thead>
                                <tr>
                                    <th width="5%">{url urltype="madd" url_string="bak/P102/CrmCreateProject/add/0/{$smarty.session.lang}/{$product->id}"}</th>
                                    <th width="30%">{#p_project_name#}</th>
                                    <th width="30%">{#p_customer#}</th>
                                    <th width="20%">{#p_unit#}</th>
                                    <th width="15%">{#gnr_settings#}</th>
                                </tr>
                                </thead>
                                <tbody>
                                {$i=1}
                                {foreach $product->Projects as $project}
                                    <tr>
                                        <td align="center">{$i++}</td>
                                        <td>{$project->name}</td>
                                        {*<td>{getname table=sh_clientlist id=$project->activity}</td>*}
                                        <td>{getname table=crm_customers id=$project->cust}</td>
                                        <td>{getname table=sh_unt id=$project->unit_id}</td>
                                        <td align="center">
                                            {if $project->request_success eq 0}
                                                {url urltype="medit" url_string="bak/P102/CrmCreateProject/edit/0/{$smarty.session.lang}/{$project->id}/{$product->id}"}
                                                {url urltype="mdelete" url_string="bak/P102/CrmCreateProject/confirm/0/{$smarty.session.lang}/{$project->id}"}
                                            {/if}
                                        </td>
                                    </tr>
                                {/foreach}
                                </tbody>
                            </table>
                        </div>
                    {/foreach}
                </div>
            </div>
        </div>
    </div>
{/block}
{block name=page_header}
    <script src="/templates/assets/js/datatable/jquery.dataTables.min.js"></script>
    <script src="/templates/assets/js/datatable/ZeroClipboard.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.tableTools.min.js"></script>
    <script src="/templates/assets/js/datatable/dataTables.bootstrap.min.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/tableExport.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jquery.base64.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/html2canvas.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/sprintf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/jspdf.js"></script>
    <script type="text/javascript" src="/templates/assets/plugins/tableExport/jspdf/libs/base64.js"></script>
    <script>
        {literal}
        function exportTo(ID, type) {
            $('#table' + ID).css('display','').tableExport({type:type,escape:'false'});$('#table' + ID).css('display','none');
        }
        {/literal}
    </script>
    <script>
        var InitiateSimpleDataTable = function() {
            return {
                init: function() {
                    //Datatable Initiating
                    var oTable = $('.sortable-table').dataTable({
                        "sDom": "Tflt<'row DTTTFooter'<'col-sm-6'i><'col-sm-6'p>>",
                        "iDisplayLength": 50,
                        "oTableTools": {
                            "aButtons": [
//                                "copy", "csv", "xls", "pdf", "print"
                            ],
                            "sSwfPath": "assets/swf/copy_csv_xls_pdf.swf"
                        },
                        "language": {
                            "search": "",
                            "sLengthMenu": "_MENU_",
                            "oPaginate": {
                                "sPrevious": "{#gnr_previous#}",
                                "sNext": "{#gnr_next#}"
                            }
                        }
                    });
                }

            };

        }();

        InitiateSimpleDataTable.init();
    </script>
{/block}