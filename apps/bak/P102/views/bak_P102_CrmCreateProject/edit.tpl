{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <script>
        resize_modal('lg');
    </script>
{/block}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#p_edit#}</h4>
	</div>
	<div class="modal-body">
        <form  method="post" action='{url urltype="path" url_string="bak/P102/CrmCreateProject/show/0/{$smarty.session.lang}/update/{$smarty.session.s_CrmCreateProject_token}/{$actrow->id}/{$season_id}"}'>
            <div class="row snsowraper">
                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_name#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="text" name="name" class="form-control" placeholder="{#p_name#}" value="{$actrow->name}" required>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="cust" style="width:100%;" required title="{#p_customer#}">
                        {*<option value="" selected>{#gnr_unspecified#}</option>*}
                        {foreach $customers_list as $customer}
                            <option value="{$customer->id}" {if $customer->id eq $actrow->cust}selected{/if}>{$customer->name}</option>
                        {/foreach}
                    </select>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_scope#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <input type="hidden" name="product" value="{$product->id}">
                    {$product->scope}
                </div>

                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_unit#}</div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <select name="unit_id" style="width:100%;" required>
                        {foreach $units_list as $row}
                            <option value="{$row->id}" {if $actrow->unit_id eq $row->id}selected{/if}>{getname table=sh_unt id=$row->id} >> {getname table=sh_user id=$row->manager_id}</option>
                        {/foreach}
                    </select>
                </div>


                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                    <button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button>
                </div>
            </div>
        </form>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}