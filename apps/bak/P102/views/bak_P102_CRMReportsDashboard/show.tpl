{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}
{block name=page_body}

    <div class="tabbable tabs-left">
        <ul class="nav nav-tabs" id="myTab3">

            {$i=1}
            {foreach $reports as $report}
                <li class="active tab-sky">
                    <a data-toggle="tab" href="#report{$report->id}">
                        <span class="badge badge-sky badge-square">{$i++}</span>
                        {$report->translatedName}
                    </a>
                </li>
            {/foreach}

        </ul>

        <div class="tab-content">
            {foreach $reports as $report}
                <div id="report{$report->id}" class="tab-pane in active">

                    <table class="table table-striped table-bordered table-hover dataTable no-footer sortable-table">
                        <thead>
                        <tr>
                            <th width="5%" style="background-color: #A0D468 !important;">&nbsp;</th>
                            <th width="75%" style="background-color: #A0D468 !important;">{#gnr_report#}</th>
                            <th width="20%" style="background-color: #A0D468 !important;">{#gnr_details#}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {$i=1}
                        {foreach $report->reportDataList as $data}
                            <tr>
                                <td align="center">{$i++}</td>
                                <td align="center">{$data->translatedName}</td>
                                <td align="center">
                                    {url check=0 urltype="alinkn" url_string="gnr/X000/reportView/centerPoint/0/{$smarty.session.lang}/{$report->code}/{$data->code}" text_value="{#gnr_view#}"}
                                </td>
                            </tr>
                        {/foreach}
                        </tbody>
                    </table>

                </div>
            {/foreach}
        </div>
    </div>

{/block}