{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#p_edit_indicator#}</h4>
	</div>
	<div class="modal-body">
		<div class="row">
			<div class="col-lg-12">
				<form  method="post" action='{url urltype="path" url_string="bak/P102/CrmDocumentProject/show/0/{$smarty.session.lang}/indicator/update/{$smarty.session.s_project_token}/{$indicator->id}"}'>
					<div class="row snsowraper">
						<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_task_or_activity#}</div>
						<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
							<select name="activity_id" required>
								<option value="">{#gnr_unspecified#}</option>
								{foreach $activities as $activity}
									<option value="{$activity->id}" {if $activity->id eq $indicator->activity_id}selected{/if}>{$activity->name}</option>
								{/foreach}
							</select>
						</div>
						<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_exec_times#}</div>
						<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
							<input type="number" min="0" name="times" value="{$indicator->times}" placeholder="{#p_exec_times#}" required>
						</div>

						<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_benefics_count#}</div>
						<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <input type="number" min="0" name="count" value="{$indicator->count}" placeholder="{#p_benefics_count#}" required>
						</div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <button type="submit" class="btn btn-warning">{#gnr_update#}</button>
                        </div>
					</div>
				</form>
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}