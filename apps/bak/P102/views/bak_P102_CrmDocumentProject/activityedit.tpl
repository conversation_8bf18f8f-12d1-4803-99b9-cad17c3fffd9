{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=header}
    <style>
        ul {
            list-style: none;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function () {

            $("#activityConnected").css("display", "none");
//			$("#activityNotConnected").css("display", "none");

            $("#818").change(function () {
                if ($("#818").is(":checked")) {
                    $("#activityConnected").show();
                    $("#activityNotConnected").hide();
                }
            });

            $("#819").change(function () {
                if ($("#819").is(":checked")) {
                    $("#activityNotConnected").show();
                    $("#activityConnected").hide();
                }
            });
        });
        $('#form').parsley();
    </script>
{/block}

{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_add#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <form id="form" method="post"
                  action='{url urltype="path" url_string="bak/P102/CrmDocumentProject/show/0/{$smarty.session.lang}/activity/update/{$smarty.session.s_project_token}/{$taskRow->id}"}'>
                <div class="col-lg-12">
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_project#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getname table=crm_projects id=$smarty.session.s_project_id}</div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_stage#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <select name="div_id" required>
                            {foreach $actrow->Intervals as $divrow}
                                <option value="{$divrow->id}"
                                        {if $taskRow->div_id eq $divrow->id}selected{/if}>{$divrow->name}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_activity_name#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <select name="actsettings_id" required>
                            <option value="">{#gnr_unspecified#}</option>
                            {foreach $activity_options as $activity}
                                <option value="{$activity->id}"
                                        {if $activity->id eq $taskRow->actsettings_id}selected{/if}>{$activity->name}</option>
                            {/foreach}
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_activity_title#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <input type="text" name="title" class="form-control" placeholder="{#p_activity_title#}"
                               value="{$taskRow->title}" required>
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_activity_desc#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><textarea class="form-control"
                                                                                           name="about"
                                                                                           placeholder="{#p_activity_desc#}"
                                                                                           minlength="5"
                                                                                           required>{$taskRow->about}</textarea>
                    </div>

                    <div class="row snsowraper">
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_relation#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            <div class="control-group">
                                {foreach $connection_list as $conrow}
                                    <div class="radio">
                                        <label>
                                            <input name="connect_status" value="{$conrow->id}"
                                                   id="{$conrow->id}" {if $conrow->id eq $taskRow->connect_status or true} checked {/if}
                                                   type="radio">
                                            <span class="text">{$conrow->translatedName}</span>
                                        </label>
                                    </div>
                                {/foreach}
                            </div>
                        </div>

                        <div id="activityConnected">
                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_relation_with#}</div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                                <select name="connected_to">
                                    {foreach $actrow->Tasks as $taskrow}
                                        <option value="{$taskrow->id}"
                                                {if $taskrow->project_id eq $actrow->id}selected{/if}>{$taskrow->name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div id="activityNotConnected">
                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_date_start#} <span
                                        class="text"><a class="tooltip-maroon maroon" data-toggle="tooltip"
                                                        data-placement="top"
                                                        data-original-title="{#p_daawa_activity_start_date#}"><span
                                                class="badge badge-warning"><i
                                                    class="fa fa-info"></i></span> </a></span></div>
                            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">{getdate type=edit col=start_date row=$taskRow id="crm_start_project_date"}
                                <span class="text-danger">{#p_daawa_activity_start_date#}
                                    => {getdate type=show row=$taskRow col=start_date }</span></div>
                        </div>

                    </div>
                    <input type="hidden" name="id" value="{$taskRow->id}">
                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_duration#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><input type="number" min="0"
                                                                                        name="duration"
                                                                                        placeholder="{#p_activity_duration#}"
                                                                                        required
                                                                                        value="{$taskRow->duration}">&nbsp;{#gnr_day#}
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_assigned_to#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <select name="assignee_id" required>
                            {foreach $actrow->Team as $memrow}
                                {if $memrow->task_id eq CRMTeam::MARKETING_EXPERT}
                                    <option value="{$memrow->user_id}"
                                            {if $smarty.session.post.assignee_id eq $memrow->user_id}selected{/if}>{getname table=sh_user id=$memrow->user_id}
                                        - {getname table=st_setting id=$memrow->task_id}</option>
                                {/if}
                            {/foreach}
                        </select>
                        {if $actrow->MarketingMems|@count gt 0}
                            <div class="alert alert-warning">{#p_must_be_marketing_spec#}</div>
                        {else}
                            <div class="alert alert-warning">{#p_there_is_no_marketing_expert#}</div>
                        {/if}
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_priority#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <select name="priority" required>
                            <option value="">{#gnr_unspecified#}</option>
                            {foreach $priority_list as $priorow}
                                <option value="{$priorow->id}"
                                        style='color: {$priorow->color};' {if $taskRow->priority eq $priorow->id}selected{/if}>{$priorow->translatedName}</option>
                            {/foreach}
                        </select>
                        {if not $priority_list|@count gt 0}
                            <div class="alert alert-warning">{#p_there_is_no_priorty_selected_spec#}</div>
                        {/if}
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_benefic_count#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <input type="number" min="0" name="benefic_count" class="form-control"
                               placeholder="{#p_benefic_count#}" value="{$taskRow->benefic_count}">
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_task_place#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <input type="text" name="place" class="form-control" placeholder="{#p_task_place#}"
                               value="{$taskRow->place}">
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_targeted_employees#}</div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        {foreach $customer_staff as $staffEmp}
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="targeted_employees[]"
                                           value="{$staffEmp->id}" {if in_array($staffEmp->id,','|explode:$taskRow->targeted_employees)} checked {/if}>
                                    <span class="text">{$staffEmp->name}</span>
                                </label>
                            </div>
                        {/foreach}
                    </div>

                    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                        <button type="submit" class="btn btn-warning sharp">{#gnr_edit#}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}
