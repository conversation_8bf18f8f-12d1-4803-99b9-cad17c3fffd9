{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}x.tpl"}
{block name=head_style}
    <style>
        .table-bordered>tbody>tr>td {
            padding: 10px;
        }
    </style>
{/block}
{block name=page_body}
    <div class="widget flat radius-bordered">
        <div class="row">
            <div class="col-lg-12">
                <div class="widget-header bg-themeprimary">
                <span class="widget-caption">
                    {$actrow->name}
                </span>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="widget-body">
                    <div class="tabbable tabs-left">
                        <ul class="nav nav-tabs" id="myTab3">
                            <li class="{if $smarty.session.s_prepare_tab eq 1}active{/if} tab-sky">
                                <a data-toggle="tab" href="#ProjectDoc">
                                    <span class="badge badge-sky badge-square">1</span>
                                    {#p_document#}
                                </a>
                            </li>

                            <li class="{if $smarty.session.s_prepare_tab eq 2}active{/if} tab-red">
                                <a data-toggle="tab" href="#ProjectTeam">
                                    <span class="badge badge-sky badge-square">2</span>
                                    {#p_team#}
                                </a>
                            </li>

                            <li class="{if $smarty.session.s_prepare_tab eq 3}active{/if} tab-orange">
                                <a data-toggle="tab" href="#ProjectTimeTable">
                                    <span class="badge badge-sky badge-square">3</span>
                                    {#p_tabulation#}
                                </a>
                            </li>

                            <li class="{if $smarty.session.s_prepare_tab eq 4}active{/if} tab-orange">
                                <a data-toggle="tab" href="#ProjectIndicators">
                                    <span class="badge badge-sky badge-square">4</span>
                                    {#p_indicators#}
                                </a>
                            </li>

                            <li class="{if $smarty.session.s_prepare_tab eq 5}active{/if} tab-orange">
                                <a data-toggle="tab" href="#ProjectRisks">
                                    <span class="badge badge-sky badge-square">5</span>
                                    {#p_risks#}
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            <div id="ProjectDoc" class="tab-pane {if $smarty.session.s_prepare_tab eq 1}in active{/if}">
                                <!-- ************************************************************-->
                                <form method="post"
                                      action='{url urltype="path" url_string="bak/P102/CrmDocumentProject/show/0/{$smarty.session.lang}/document/{$smarty.session.s_project_token}"}'>
                                    <div class="row snsowraper">
                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_unit#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=sh_unt id=$actrow->unit_id}</div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_scope#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                            {$product->scope}
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_project#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{$actrow->name}</div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_customer#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">{getname table=crm_customers id=$actrow->cust}</div>
                                    </div>
                                    <hr>
                                    <div class="row snsowraper">

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_project_def#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput"><textarea
                                                    class="form-control" name="desc"
                                                    placeholder="{#p_project_def#}">{$actrow->desc}</textarea></div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_products#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                            {foreach $product->ProductRow as $prow}
                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" name="product_ids[]"
                                                               value="{$prow->id}" {if in_array($prow->id,','|explode:$actrow->product_ids)} checked {/if}>
                                                        <span class="text">{$prow->name}</span>
                                                    </label>
                                                </div>
                                            {/foreach}
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_assumptions#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <textarea class="form-control"
                                          name="assumptions" placeholder="{#p_assumptions#}">{$actrow->assumptions}</textarea>
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_include#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <textarea class="form-control" name="included"
                                          placeholder="{#p_include#}">{$actrow->included}</textarea>
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_exclude#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                <textarea class="form-control" name="excluded"
                                          placeholder="{#p_exclude#}">{$actrow->excluded}</textarea>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row snsowraper">

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_units#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                            {foreach $support_unit_list as $surow}
                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" name="supply_units[]"
                                                               value="{$surow->id}" {if in_array($surow->id,','|explode:$actrow->supply_unit)} checked {/if}>
                                                        <span class="text">{getname table=sh_unt id=$surow->id}</span>
                                                    </label>
                                                </div>
                                            {/foreach}
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_date_start#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                            {if $actrow->requestEntity->wf_request_send_status neq Request::REQUEST_IS_NOT_SEND}
                                                {getdate type=show col=start_date row=$actrow}
                                            {else}
                                                {getdate type=edit col=start_date row=$actrow}
                                            {/if}
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#gnr_date_end#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                            <div class="row">
                                                {*<div class="col-lg-2">{getdate col=end_date type=show row=$actrow}</div>*}
                                                <div class="col-lg-10 padding-10" style="margin-right: 5px;margin-left: 5px;"><span class="text-danger">{#p_season_end_date_message#}</span></div>
                                            </div>
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_eval#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                            {if $actrow->requestEntity->wf_request_send_status neq Request::REQUEST_IS_NOT_SEND}
                                                {getname table=pm_preva id=$actrow->eva_list_id}
                                            {else}
                                                {foreach $evalist as $evarow}
                                                    <div class="radio">
                                                        <label>
                                                            <input type="radio" name="eva_list_id"
                                                                   value="{$evarow->pm_preva_id}" {if $actrow->eva_list_id eq $evarow->pm_preva_id} checked {/if}
                                                                   required>
                                                            <span class="text">&nbsp;{$evarow->pm_preva_name}</span>
                                                        </label>
                                                    </div>
                                                {/foreach}
                                            {/if}
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_new_tasks#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                            {foreach $provlist as $prvrow}
                                                <div class="radio">
                                                    <label>
                                                        <input type="radio" name="newtask_prov"
                                                               value="{$prvrow->id}" {if $actrow->newtask_prov eq $prvrow->id} checked {/if}
                                                               required>
                                                        <span class="text">&nbsp;{$prvrow->translatedName}</span>
                                                    </label>
                                                </div>
                                            {/foreach}
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel">{#p_weekends#}</div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                            <div class="control-group">
                                                <div class="checkbox">
                                                    {foreach $week_days as $crow}
                                                        <label>
                                                            <input type="checkbox" class="snso-checkbox"
                                                                   name="weekends_list[]" value="{$crow->id}"
                                                                    {if in_array($crow->id,','|explode:$actrow->weekends_list)} checked {/if} >
                                                            <span class="text">{$crow->translatedName}</span>
                                                        </label>
                                                        <br>
                                                    {/foreach}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row snsowraper">
                                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 snsolabel"></div>
                                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 snsoinput">
                                            <button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button>
                                        </div>
                                    </div>
                                </form>
                                <!-- ************************************************************-->
                            </div>

                            <div id="ProjectTeam"
                                 class="tab-pane {if $smarty.session.s_prepare_tab eq 2}in active{/if}">
                                <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%">{url check=0 urltype="madd" url_string="bak/P102/CrmDocumentProject/teamadd/0/{$smarty.session.lang}"}</th>
                                        <th width="50%">{#gnr_member#}</th>
                                        <th width="30%">{#gnr_task#}</th>
                                        <th width="15%">{#gnr_settings#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    <tr>
                                        <td align="center">{$i++}</td>
                                        <td style="padding-left: 15px;padding-right: 15px;">{getname table=sh_user id=$actrow->manager_id}</td>
                                        <td style="padding-left: 15px;padding-right: 15px;">{#p_manager#}</td>
                                        <td align="center"></td>
                                    </tr>
                                    {foreach $members_list as $mrow}
                                        {if $mrow->user_id ne $actrow->manager_id}
                                            <tr>
                                                <td align="center">{$i++}</td>
                                                <td>{getname table=sh_user id=$mrow->user_id}</td>
                                                <td>{getname table=st_setting id=$mrow->task_id}</td>
                                                <td align="center">
                                                    {if $smarty.session.user->id eq $actrow->manager_id}
                                                        {if $actrow->manager_id eq $mrow->user_id}
                                                            {url check=0 urltype="medit" url_string="bak/P102/CrmDocumentProject/projectmanagerprivileges/0/{$smarty.session.lang}/{$mrow->id}"}
                                                        {else}
                                                            {url check=0 urltype="medit" url_string="bak/P102/CrmDocumentProject/teamedit/0/{$smarty.session.lang}/{$mrow->id}"}
                                                            {url check=0 urltype="mdelete" url_string="bak/P102/CrmDocumentProject/teamconfirm/0/{$smarty.session.lang}/{$mrow->id}"}
                                                        {/if}
                                                    {/if}
                                                </td>
                                            </tr>
                                        {/if}
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>

                            <div id="ProjectTimeTable"
                                 class="tab-pane {if $smarty.session.s_prepare_tab eq 3}in active{/if}">
                                {if $actrow->requestEntity->wf_request_send_status eq Request::REQUEST_IS_NOT_SEND}
                                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                        {url check=0 urltype="mbutton" url_string="bak/P102/CrmDocumentProject/intervaladd/0/{$smarty.session.lang}" text_value="{#p_add_interval#}"}
                                        {url check=0 urltype="mbutton" url_string="bak/P102/CrmDocumentProject/activityadd/0/{$smarty.session.lang}" text_value="{#p_add_activity#}"}
                                    </div>
                                {/if}
                                {assign 'i' 1}
                                {foreach $actrow->Intervals as $introw}
                                    <div class="row" style="padding-right: 10px;padding-left: 10px;">
                                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 snsoinput" style="background-color: #9CA6B4; color: #ffffff;">[ {$i} ] {$introw->name}</div>
                                        <div class="col-lg-5 col-md-4 col-sm-12 col-xs-12 snsoinput" style="background-color: #9CA6B4; color: #ffffff;">
                                            {$actrow->start_date}
                                            &nbsp;&raquo;&nbsp;
                                            {if $introw->Tasks|@count gt 0}
                                                {getdate table=crm_div col=end_date type=show row=$introw}
                                                &raquo;&nbsp;
                                                {$introw->duration} {#gnr_day#}
                                            {else}
                                                {$actrow->start_date}
                                            {/if}
                                        </div>
                                        <div class="col-lg-1 col-md-1 col-sm-12 col-xs-12 snsoinput" style="background-color: #9CA6B4; color: #ffffff;">
                                            {if $actrow->requestEntity->wf_request_send_status eq Request::REQUEST_IS_NOT_SEND}
                                                {url check=0 urltype="medit" url_string="bak/P102/CrmDocumentProject/intervaledit/0/{$smarty.session.lang}/{$introw->id}"}
                                                {if $i++ neq 1}
                                                    {url check=0 urltype="mdelete" url_string="bak/P102/CrmDocumentProject/intervalconfirm/0/{$smarty.session.lang}/{$introw->id}"}
                                                {/if}
                                            {/if}
                                        </div>
                                    </div>
                                    {$j=1}
                                    {foreach $introw->Tasks as $activityrow}
                                        <div class="row" style="padding-right: 10px;padding-left: 10px;">
                                            <div class="col-lg-1 col-md-1 col-sm-12 col-xs-12">&nbsp;</div>
                                            <div class="col-lg-5 col-md-5 col-sm-12 col-xs-12 snsoinput">[ {$j++} ] {getname table=crm_activities id=$activityrow->actsettings_id} &nbsp;&raquo;&nbsp;{$activityrow->title}</div>
                                            <div class="col-lg-5 col-md-5 col-sm-12 col-xs-12 snsoinput">
                                                {getdate col=start_date type=show row=$activityrow}
                                                &nbsp;&raquo;&nbsp;
                                                {getdate col=end_date type=show row=$activityrow}
                                                &nbsp;&raquo;&nbsp;
                                                {$activityrow->duration} {#gnr_day#}
                                            </div>
                                            <div class="col-lg-1 col-md-1 col-sm-12 col-xs-12 snsoinput">
                                                {if $actrow->requestEntity->wf_request_send_status eq Request::REQUEST_IS_NOT_SEND}
                                                    {url check=0 urltype="medit" url_string="bak/P102/CrmDocumentProject/activityedit/0/{$smarty.session.lang}/{$activityrow->id}"}
                                                    {url check=0 urltype="mdelete" url_string="bak/P102/CrmDocumentProject/activityconfirm/0/{$smarty.session.lang}/{$activityrow->id}"}
                                                {/if}
                                            </div>
                                        </div>
                                    {/foreach}
                                {/foreach}
                            </div>

                            <div id="ProjectIndicators"
                                 class="tab-pane {if $smarty.session.s_prepare_tab eq 4}in active{/if}">

                                <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%">{if $actrow->requestEntity->wf_request_send_status eq Request::REQUEST_IS_NOT_SEND}
                                                {url check=0 urltype="madd" url_string="bak/P102/CrmDocumentProject/indicatorAdd/0/{$smarty.session.lang}"}

                                                {/if}</th>
                                        <th width="40%">{#p_activity#}</th>
                                        <th width="15%">{#p_exec_times#}</th>
                                        <th width="15%">{#p_benefics_count#}</th>
                                        <th width="25%">{#gnr_settings#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {assign var="times_sum" value="0"}
                                    {assign var="count_sum" value="0"}
                                    {foreach $indicators as $indicator}
                                        <tr>
                                            <td align="center">{$i++}</td>
                                            <td>{getname table=crm_activities id=$indicator->activity_id}</td>
                                            <td>{$indicator->times}</td>
                                            <td>{$indicator->count}</td>
                                            <td align="center">
                                                {if $smarty.session.user->id eq $actrow->manager_id && $actrow->requestEntity->wf_request_send_status eq Request::REQUEST_IS_NOT_SEND}
                                                        {url check=0 urltype="medit" url_string="bak/P102/CrmDocumentProject/indicatorEdit/0/{$smarty.session.lang}/{$indicator->id}"}
                                                        {url check=0 urltype="mdelete" url_string="bak/P102/CrmDocumentProject/indicatorConfirm/0/{$smarty.session.lang}/{$indicator->id}"}
                                                {/if}
                                            </td>
                                        </tr>
                                        {assign var="times_sum" value=($times_sum+$indicator->times)}
                                        {assign var="count_sum" value=($count_sum+$indicator->count)}
                                    {/foreach}
                                    </tbody>
                                    <tfoot>
                                    <tr>
                                        <td style="background-color: #ccc"></td>
                                        <td style="background-color: #ccc">{#gnr_total#}</td>
                                        <td style="background-color: #ccc">{$times_sum}</td>
                                        <td style="background-color: #ccc">{$count_sum}</td>
                                        <td style="background-color: #ccc"></td>
                                    </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <div id="ProjectRisks"
                                 class="tab-pane {if $smarty.session.s_prepare_tab eq 5}in active{/if}">
                                <table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
                                    <thead>
                                    <tr>
                                        <th width="5%">{url check=0 urltype="madd" url_string="bak/P102/CrmDocumentProject/riskadd/0/{$smarty.session.lang}"}</th>
                                        <th width="10%">{#gnr_date#}</th>
                                        <th width="15%">{#p_risk#}</th>
                                        <th width="40%">{#p_strategy#}</th>
                                        <th width="10%">{#gnr_settings#}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {$i=1}
                                    {foreach $risks_list as $rskrow}
                                        <tr>
                                            <td align="center">{$i++}</td>

                                            <td>{getdate type=show row=$rskrow col=created_date}</td>
                                            <td>{$rskrow->risk}</td>
                                            <td>{$rskrow->mitigation_strategy|nl2br}</td>
                                            <td align="center">
                                                {url check=0 urltype="medit" url_string="bak/P102/CrmDocumentProject/riskedit/0/{$smarty.session.lang}/{$rskrow->id}"}
                                                {url check=0 urltype="mdelete" url_string="bak/P102/CrmDocumentProject/riskconfirm/0/{$smarty.session.lang}/{$rskrow->id}"}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}

{block name=back}{url urltype="path" url_string="{$smarty.session.s_back_path}"}{/block}
