{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{#p_edit_team_member#}</h4>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-lg-12">
                <form  method="post" action='{url urltype="path" url_string="bak/P102/CrmDocumentProject/show/0/{$smarty.session.lang}/team/update/{$smarty.session.s_project_token}/{$memrow->id}"}'>
                    <div class="row snsowraper">
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_user#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            {getname table=sh_user id=$memrow->user_id}
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_task#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            {getname table=st_setting id=$memrow->task_id}
                        </div>

                        <hr>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_permissions#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            {foreach $prvs_list as $prvrow}
                                {if $prvrow->id ne 393}
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="prvs[]" value="{$prvrow->id}" {if in_array($prvrow->id, ','|explode:$memrow->prvs)}checked{/if}>
                                            <span class="text">{$prvrow->translatedName}</span>
                                        </label>
                                    </div>
                                {/if}
                            {/foreach}
                        </div>
                        {*<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_reports_prvs#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            {foreach $report_prvlist as $prvrow}
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="pm_prmem_report_prvs[]" value="{$prvrow->id}">
                                        <span class="text">{getname table=st_setting id=$prvrow->id}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>*}

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp" >{#gnr_update#}</button></div>

                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
    </div>
{/block}