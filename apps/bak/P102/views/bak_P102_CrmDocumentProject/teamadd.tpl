{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#p_add_team_member#}</h4>
	</div>
	<div class="modal-body">
		<div class="row">
			<div class="col-lg-12">
				<form  method="post" action='{url urltype="path" url_string="bak/P102/CrmDocumentProject/show/0/{$smarty.session.lang}/team/insert/{$smarty.session.s_project_token}"}'>
					<div class="row snsowraper">
						<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_user#}</div>
						<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
							<input type="hidden" name="project_id" value="{$actrow->id}">
							<select name="user_id" required>
                                {foreach $users_list as $urow}
                                    {if $urow->user_id neq $actrow->manager_id}
										<option value="{$urow->user_id}">{getname table=sh_user id=$urow->user_id}</option>
                                    {/if}
                                {/foreach}
							</select>
						</div>

						<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_task#}</div>
						<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
							<select name="task_id" required>
                                {foreach $tasks_types as $trow}
									<option value="{$trow->id}">{$trow->translatedName}</option>
                                {/foreach}
							</select>
						</div>

						<hr>

						<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#gnr_permissions#}</div>
						<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            {foreach $prvs_list as $prvrow}
                                {if $prvrow->id ne 393}
									<div class="checkbox">
										<label>
											<input type="checkbox" name="prvs[]" value="{$prvrow->id}">
											<span class="text">{$prvrow->translatedName}</span>
										</label>
									</div>
                                {/if}
                            {/foreach}
						</div>
                        {*<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel">{#p_reports_prvs#}</div>
                        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput">
                            {foreach $report_prvlist as $prvrow}
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="pm_prmem_report_prvs[]" value="{$prvrow->id}">
                                        <span class="text">{getname table=st_setting id=$prvrow->id}</span>
                                    </label>
                                </div>
                            {/foreach}
                        </div>*}

						<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 snsolabel"></div>
						<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-success sharp" >{#gnr_add#}</button></div>

					</div>
				</form>
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}