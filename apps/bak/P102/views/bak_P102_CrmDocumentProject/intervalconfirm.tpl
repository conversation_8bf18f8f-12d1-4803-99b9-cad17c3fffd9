{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#p_delete_interval#}</h4>
	</div>
	<div class="modal-body">
		<div class="row">

				<div class="snsowraper danger text-center">
                    {if $interval->Tasks|count eq 0}
						{#p_delete_interval_confirm#}
						<br>
						<br>
						{$interval->name}
						<br>
						<br>
						{url check=0 urltype="delete" url_string="bak/P102/CrmDocumentProject/show/0/{$smarty.session.lang}/interval/delete/{$smarty.session.s_project_token}/{$interval->id}"}
                    {else}
						<div class="alert alert-warning">{#p_delete_interval_error#}</div>
                    {/if}
				</div>

			{*{if $conncectedTasksNum gte 1}
				<div class="snsowraper danger centered">
                    {#p_delete_interval_error#}
					<br><br>
					{$i=1}
					{foreach $conncectedTasks as $task}
						{$i++}&nbsp;-&nbsp;{$task.pm_prtasks_name}<br>
					{/foreach}
				</div>
			{/if}*}

		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}