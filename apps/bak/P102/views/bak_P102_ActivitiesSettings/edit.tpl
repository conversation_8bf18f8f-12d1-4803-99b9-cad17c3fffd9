
{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}modal.tpl"}
{block name=content}

	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">{#gnr_edit#}</h4>
	</div>
	<div class="modal-body">
		<div class="row">
            <form method="post" action='{url urltype="path" url_string="{$base_uri}/{$raw->id}"}'>
                <div class="col-lg-12">
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel">{#activity_name#}</div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput">
						{if $raw->activity_type eq CRMActivities::ACT_TYPE}
                        	{$raw->name}
						{else}
							<input type="text" name="name" class="form-control" value="{$raw->name}" placeholder="{#p_scope#}" required>
                        {/if}
						{*<input type="text" name="name" class="form-control" value="" placeholder={#activity_name#} required>*}
					</div>
					{csrf_field token_key="update_token"}
					{gnr_activation edit_value=$raw->activation}

                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 snsolabel"></div>
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 snsoinput"><button type="submit" class="btn btn-warning sharp">{#gnr_update#}</button></div>
                </div>
            </form>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default" data-dismiss="modal">{#gnr_close#}</button>
	</div>
{/block}