{extends file="{$smarty.const.LAYOUTS_DIR}{$smarty.const.DS}app.tpl"}

{block name=page_body}
	<div data-pattern="priority-columns">
		<table id="snsotable-1" class="table table-hover table-striped table-bordered table-condensed">
			<thead>
				<tr>
					<th width="5%">{url check=1 urltype="madd" opr_code=$opr_code url_string="{$smarty.session.bnd}/{$smarty.session.prg}/{$opr_code}/add/0/{$smarty.session.lang}"}</th>
					<th width="60%" data-priority="1">{#activity_name#}</th>
					<th width="15%">{#gnr_activation#}</th>
					<th width="10%" data-priority="3">{#gnr_settings#}</th>
					{* <th widtn="70"><a href="{$smarty.session.bnd}/{$smarty.session.prg}/{$opr_code}/add/0/{$smarty.session.lang}"></a></th> *}
				</tr>
			</thead>
			<tbody>
			{$i=1}
				{foreach $raws as $raw}
					<tr>
					<td align="center">{$i++}</td>
					<td>{$raw->name}</td>
					<td align="center">{getname table=st_setting id=$raw->activation}</td>
					<td align="right">
						{url check=1 urltype="medit" opr_code=$opr_code url_string="{$smarty.session.bnd}/{$smarty.session.prg}/{$opr_code}/edit/0/{$smarty.session.lang}/{$raw->id}"}
						
						{if $i gt 6}
                        	{if not in_array($raw->id, $linked_types)}
								{url check=1 urltype="mdelete" opr_code=$opr_code url_string="{$smarty.session.bnd}/{$smarty.session.prg}/{$opr_code}/confirm/0/{$smarty.session.lang}/{$raw->id}"}
							{/if}
						{/if}
					</td>
					</tr>
				{/foreach}
			</tbody>
		</table>
	</div>
{/block}
