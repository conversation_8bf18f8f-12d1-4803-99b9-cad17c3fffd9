
[Setting]
direction = "ltr"
alignment = "left"
valignment = "right"

[Language]
code = "en"

[Program]
name = " Customer Management"
about = "Data management and customer services program"

[Menus]
Customers = "Customers"
CRMSettings = "Settings"
CRMProjects = " Marketing projects"
CRMReports = "Reports"

[Operations]
CreateCustomersMenu = " Add a client"
CustomersDataMenu = "Customer data"
ActivitiesLogMenu = "Record activities"
CustomersTypesSettings = "Customers Types Settings"
JobsTypesSettings = "Jobs Types Settings"
CustomersAccessibilitySettings = "Customers Accessibility Settings"
ProductsSettings = "Products Settings"
ActivitiesSettings = " Activities Settings"
CrmCreateProject = "Creation of marketing projects"
CrmPrepareProject = "Preparation of marketing projects"
CrmManageProject = " Marketing Project Management"
CrmDocumentProject = " Documentation of marketing projects"
CRMReportsDashboard = "Customer Management Reports"

[CreateCustomersMenu]
p_customer_name = " customer name"
p_customer_type = " Customer Type"
p_customer_access = "Customer access method"
p_create_new_customer_title = "Add a new client"
p_close = "close"

[CustomersDataMenu]
p_customer_name = " customer name"
p_customer_type = "Client type"
p_customer_chances = " Opportunities"
p_customer_challenges = " Challenges"
p_customer_panel = " Client panel"
p_contact_info = "Contact Data"
p_website = "website"
p_phone_line_one = "Fixed telephone 1"
p_phone_line_tow = " Fixed telephone 2"
p_fax = "fax"
p_mobile = "mobile"
p_email_one = " Email 1"
p_emailTow = "Email 2"
p_update = "update"
p_customer_staff_title = "customer staff"
p_name = "name"
p_job = "job"
p_analysis_customer_environment_title = "Analysis of client"
p_analysis = " Analysis"
p_type = "Type of analysis"
p_priority = "priority level"
p_customer_environment_analysis_title = "Analysis of client environment"
p_analysis_title = " Analysis"
p_analysis_desc = "Description of analysis"
p_priority_degree = " priority level"
p_rec = "rec"
p_customer_staff_edit_title = "customer staff edit title"
p_employee_name = "Employee Name"
p_employee_desc = " General description of the employee"
p_employee_phone = " Employee Phone"
p_employee_email = "Employee Mail"
p_customer_staff_add_title = " Add client level"

[ActivitiesLogMenu]
p_choose_menu_from_list_above = "Select from the list above"
p_project = "project"
p_activity_or_task = " Activity / task"
p_exec = "exec"
p_start_date = "starting date"
p_exec_duration = " Duration of implementation"

[JobsTypesSettings]
p_name = "Job Title"

[CustomersAccessibilitySettings]
p_customer_acc = "Customer access method"
add_customer_accessablity_title = "add customer accessibility title"
p_name = "Access method"
p_cust_acc_edit = " Modify the client access method"
p_close = "close"

[ProductsSettings]
p_scope = "product field"
p_products = "products"
p_product_name = "product name"
p_edit_product_title = "editproduct"
p_objective = " Marketing goal"
p_objective_purchaseable = "Purchasable"
p_objective_for_sale = " For Sale"
p_close = "close"
p_add_product_row_title = "add product row title"
p_add_product_title = " Add a product by domain"

[ActivitiesSettings]
activity_name = "activity name"
p_add_activity_settings_title = " add activity"
p_edit_activity_settings_title = "Edit activity"

[CrmCreateProject]
p_project_name = "Project Marketing"
p_customer = "customer"
p_scope = " Products area"
p_unit = "Unit name"
p_add = "add"
p_name = "Marketing projects"
p_product = "product"
p_indicator = "indicator"
p_edit = "edit"
p_delete = "delete"
p_delete_confirm = "delete confirm"

[CrmPrepareProject]
p_project_name = "project name"
p_unit_manager = "unit manager"
p_manager = "Project manager"
p_select_manager = "Choose the manager"
p_name = "name"
p_reviewer = "reviewer"
p_prover = "prover"

[CrmManageProject]
TaskStartDateBeforProjectStartDate = "Task Start Date Before Project Start Date"
g_executed_tasks = "executed tasks"
g_under_execution_tasks = "under execution tasks"
g_not_executed = "not executed"
g_open = "open"
g_closed = "closed"
g_my_tasks = "my tasks"
g_others_tasks = "others task"
g_all_tasks = "all task"
browse_project_intervals = " browse project intervals "
p_team = "team"
p_doing = "doing"
p_not_done = "not done"
p_pending = "pending"
p_done = "done"
p_task_classification = "task classification"
p_opened = "opened"
p_closed = "closed"
p_targeted = "targeted"
p_executed = "executed"
p_percent = "percent"
p_benefics = "beneficiary"
p_benefic = "beneficiary"
p_assigned_to = "assigned to"
p_proving = "proving"
g_tasks_cases = "tasks cases"
g_done_tasks = "done task"
p_doing_status = "doing status"
g_piriority_cases = "priority cases"
g_assign_cases = "assign cases"
g_revision_cases = "revision cases"
p_must_choose_project = "must choose project"
p_add = "add"
p_project = "project"
p_stage = "stage"
p_activity_name = "activity name"
p_activity_desc = "activity desc"
p_relation = "relation"
p_relation_with = "relation with"
p_daawa_activity_start_date = "daawa activity start date"
p_activity_duration = "activity duration"
p_must_be_daiya = "must be daiyya"
p_priority = "priority"
p_task_lang = "task"
p_benefic_gender = "beneficiary gender"
p_benefic_count = "beneficiary account"
p_task_place = "task place"
p_edit = "edit"
p_delete = "delete"
p_delete_confirm = "delete confirm"
p_created_at = "created at"
p_reference = "reference"
g_task_not_approved = " task not approved"
p_category = "category"
p_name = "name"
p_desc = "decription"
p_assigned = "assigned"
p_proved = "proved"
p_not_proved = "not proved"
p_transfer_to = "transfer to"
p_unapprove = "unapproved"
p_approve = "approved"

[CrmDocumentProject]
p_document = " Project Document"
p_team = "team staff"
p_tabulation = "project schedule"
p_indicators = "indicators"
p_risks = "risks"
p_unit = "Administrative unit"
p_scope = "field"
p_project = "project"
p_customer = "customer"
p_project_def = "Definition of the project"
p_products = "products"
p_objectives = "objectives"
p_assumptions = " Project Assumptions"
p_include = " Within the scope of the project"
p_exclude = " Outside the scope of the project"
p_units = "Support Units"
p_season_start_date = "date end"
p_season_end_date = "Expiry date"
p_hint = "hint"
p_eval = " Evaluation Options"
p_new_tasks = "new task"
p_weekends = "weekends"
p_add_interval = "add interval"
p_add_activity = "add activity"
p_activity = "activity"
p_exec_times = "Number of executions"
p_benefics_count = "number of beneficiaries"
p_risk = "risk"
p_strategy = "Strategy to Overcome Risk"
p_add_team_member = "add team member"
p_reports_prvs = "reports prvs"
p_edit_team_member = "edit team member"
p_delete_team_member = "delete team member"
p_delete_team_member_confirm = "delete team member confirm"
p_interval_name = " Stage Name"
division_name = "division name"
p_interval_desc = "Stage description"
p_edit_interval = "edit interval"
p_delete_interval = "delete interval"
p_delete_interval_confirm = "delete interval confirm"
p_delete_interval_error = "delete interval error"
p_add = "add"
p_stage = "stage"
p_activity_name = " Activity description"
p_activity_desc = ""
p_relation = "relation"
p_relation_with = "Linked to"
p_daawa_activity_start_date = "daawa activity start date"
p_activity_duration = "activity duration"
p_assigned_to = " Assigned to"
p_must_be_marketing_spec = " Must be a marketing specialist"
p_priority = "priority"
p_benefic_gender = " Type of beneficiaries"
p_benefic_count = "number of beneficiaries"
p_task_place = "task place"
p_delete = "delete"
p_delete_confirm = "delete confirm"
p_add_risk = "add risk"
p_edit_risk = "edit risk"
p_delete_risk = "delete risk"
p_delete_risk_confirm = "delete risk confirm"
p_add_indicator = "add indicator"
p_task_or_activity = "task or activity"
p_edit_indicator = "edit indicator"
p_delete_indicator = "delete indicator"
p_delete_indicator_confirm = "delete indicator confirm"
