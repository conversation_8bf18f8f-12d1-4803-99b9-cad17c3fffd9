# SNSO ERP Docker - Quick Start Guide

## 🚀 One-Command Setup

```bash
./docker-setup.sh
```

This will:
1. ✅ Create necessary directories and permissions
2. ✅ Build Docker containers with legacy tech stack
3. ✅ Start all services (app, database, redis, phpmyadmin)
4. ✅ Optionally initialize the database schema

## 📋 Step-by-Step Setup

### 1. Initial Setup
```bash
# Make setup script executable
chmod +x docker-setup.sh

# Run setup
./docker-setup.sh
```

### 2. Initialize Database Schema
**Important**: SNSO ERP requires its own schema initialization system.

```bash
# Option A: Automated (recommended)
make init-schema

# Option B: Manual
make schema-manual
# Then visit: http://localhost:8080/framework/SchemaAndDataRefactor.php
```

### 3. Access Application
- **Application**: http://localhost:8080
- **PhpMyAdmin**: http://localhost:8081
- **Database**: localhost:3306 (user: `snso_user`, pass: `snso_password`)

## 🔧 Common Commands

```bash
# View all available commands
make help

# Container management
make up          # Start all services
make down        # Stop all services
make restart     # Restart all services
make status      # Show container status

# Development
make logs        # View all logs
make shell       # Access app container
make db-shell    # Access database

# Database operations
make backup      # Backup database
make restore FILE=backup.sql  # Restore database

# Health check
make health      # Check all services
```

## 🗄️ Database Schema Initialization

SNSO ERP uses `framework/SchemaAndDataRefactor` for database setup. This system:

- Creates all database tables
- Seeds core data and entities
- Runs migrations and data refactoring
- Sets up organization structure
- Updates privileges and translations

**⏱️ Time**: 5-15 minutes depending on system performance
**📋 Logs**: Saved to `docker/logs/schema-init-*.log`

## 🔍 Troubleshooting

### Container Issues
```bash
# Check container status
docker-compose ps

# View logs
docker-compose logs -f

# Restart specific service
docker-compose restart snso-app
```

### Database Issues
```bash
# Check database connection
docker-compose exec snso-db mysql -u snso_user -p -e "SELECT 1;"

# View database logs
docker-compose logs snso-db
```

### Permission Issues
```bash
# Fix permissions
make permissions

# Or manually:
sudo chown -R $USER:$USER .
chmod -R 777 cache/ logs/ uploads/
```

### Schema Initialization Issues
```bash
# Check initialization logs
ls -la docker/logs/schema-init-*.log
tail -f docker/logs/schema-init-*.log

# Manual schema initialization
cp framework/SchemaAndDataRefactor framework/SchemaAndDataRefactor.php
curl -v http://localhost:8080/framework/SchemaAndDataRefactor.php
```

## 📊 Service Details

| Service | Port | Purpose |
|---------|------|---------|
| snso-app | 8080 | Main application (PHP 7.0 + Apache + Node.js 10) |
| snso-db | 3306 | MySQL 5.5 database |
| snso-redis | 6379 | Redis 5.0 cache/sessions |
| snso-phpmyadmin | 8081 | Database management |

## 🔒 Default Credentials

- **Database User**: `snso_user` / `snso_password`
- **Database Root**: `root` / `snso_root_password`
- **Redis**: `snso_redis_password`
- **App Admin**: `admin` / `admin123` (after schema init)

## 📝 Important Notes

1. **Legacy Stack**: Uses PHP 7.0, MySQL 5.5, Node.js 10 for compatibility
2. **Arabic Support**: Full UTF-8MB4 character set support
3. **Development Mode**: Debug enabled, not for production
4. **Schema Required**: Must run schema initialization for proper setup
5. **One-time Init**: Only initialize schema once per database

## 🆘 Need Help?

1. Check the full documentation: `DOCKER_README.md`
2. View container logs: `make logs`
3. Check service health: `make health`
4. Access container shell: `make shell`

---

**Quick Commands Summary:**
```bash
./docker-setup.sh     # Complete setup
make init-schema       # Initialize database
make logs             # View logs
make shell            # Access container
make help             # Show all commands
```
